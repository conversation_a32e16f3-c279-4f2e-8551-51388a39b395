<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;eefd1cbb-8464-4dcb-8313-3c2168da9006&quot;,&quot;conversations&quot;:{&quot;eefd1cbb-8464-4dcb-8313-3c2168da9006&quot;:{&quot;id&quot;:&quot;eefd1cbb-8464-4dcb-8313-3c2168da9006&quot;,&quot;createdAtIso&quot;:&quot;2025-07-25T14:14:09.990Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-01T02:00:38.607Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;3260d57d-368b-444f-b610-8a4661a3824c&quot;,&quot;request_message&quot;:&quot;我这个文件的内容梳理一下&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;我这个文件的内容梳理一下&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*\n\n---\n\n*Your access expires in 0 days. [Purchase a subscription](https://app.augmentcode.com/account)*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;我这个文件的内容梳理一下&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/usr/local/coding/workspace/zhihao&quot;,&quot;folderRoot&quot;:&quot;/usr/local/coding/workspace/zhihao&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-01T02:00:38.628Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*\n\n---\n\n*Your access expires in 0 days. [Purchase a subscription](https://app.augmentcode.com/account)*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-384af220-bbf9-4400-87e5-3009363c7535&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;c3dc849a-a6f7-4fbd-b237-31a223abd5b1&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;isAgentEditsCollapsed&quot;:true,&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>