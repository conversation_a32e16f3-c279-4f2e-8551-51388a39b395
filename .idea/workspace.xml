<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="GROUP_BY_SEVERITY" value="true" />
    <option name="ANALYZE_TEST_SOURCES" value="false" />
    <option name="SHOW_STRUCTURE" value="true" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ea99aca9-8ecf-4c7b-87f7-a3bb70d5fb86" name="更改" comment="fix:修改导入">
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/converter/BaseCallbackData.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/converter/CallbackDataConverter.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/converter/org/Org.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/converter/org/OrgCallbackData.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/converter/user/User.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/converter/user/UserCallbackData.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/dto/PersonDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/entity/Dept.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/entity/Person.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/entity/PersonDept.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/model/Tree.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/model/TreeNode.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/bean/vo/PersonVo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/meta/RedisKeys$Cache.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/meta/RedisKeys.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/meta/biz/CallbackDef$ChangeType.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/meta/biz/CallbackDef$ChangeTypeGroup.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/meta/biz/CallbackDef$Event.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/meta/biz/CallbackDef$InfoType.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/meta/biz/CallbackDef$Status.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/meta/biz/CallbackDef.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/meta/biz/WxwDef$isFetchChild.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-oa-domain/target/classes/com/zhihaoscm/domain/meta/biz/WxwDef.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/application.yaml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/bootstrap.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/ZhihaoscmOrgServiceBoot.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/config/MybatisPlusConfiguration.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/config/RedisConfiguration.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/config/WxwProperties.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/mapper/DeptMapper.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/mapper/PersonDeptMapper.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/mapper/PersonMapper.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/service/DeptService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/service/OrganizationalStructureSyncService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/service/PersonDeptService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/service/PersonService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/service/WxwCallbackService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/service/WxwService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/service/impl/DeptServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/service/impl/PersonDeptServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/core/service/impl/PersonServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/processor/Processor.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/processor/ProcessorFactory.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/processor/wxw/callback/OrgCallbackProcessor.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/processor/wxw/callback/UserCallbackProcessor.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/processor/wxw/callback/WxwCallbackProcessor.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/resource/DeptResource.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/resource/OrganizationalStructureSyncResource.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/resource/PersonResource.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/resource/WxwResource.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/utils/aes/AesException.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/utils/aes/ByteGroup.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/utils/aes/Pkcs7Encoder.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/utils/aes/Sha1Utils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/utils/aes/WxBizMsgCrypt.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/utils/aes/XmlParse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/com/zhihaoscm/org/service/utils/aes/XmlUtils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/config/dev/logback.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/config/prd/logback.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/config/staging/logback.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/logback.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/mybatis/mapper/DeptMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/mybatis/mapper/PersonDeptMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/zhihao-oa/zhihaoscm-org-service/target/classes/mybatis/mapper/PersonMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$/zhihao-hot">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$/zhihao-hot">
          <activation>
            <after_sync>
              <task name="setupDependencies" />
            </after_sync>
          </activation>
        </task>
        <projects_view>
          <tree_state>
            <expand />
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Action" />
        <option value="Interface" />
        <option value="Dockerfile" />
        <option value="AnnotationType" />
        <option value="JUnit5 Test Class" />
        <option value="Class" />
        <option value="HTTP Request" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="huangchanglong &lt;<EMAIL>&gt;" />
      </list>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/HotswapAgent" value="master" />
        <entry key="$PROJECT_DIR$/app" value="dev-1.0.5" />
        <entry key="$PROJECT_DIR$/secret" value="staging-2.24.0-fix" />
        <entry key="$PROJECT_DIR$/supplier-service" value="dev-1.3.0" />
        <entry key="$PROJECT_DIR$/system-design" value="staging-2.24.0-fix" />
        <entry key="$PROJECT_DIR$/technical-specifications" value="staging-2.24.0-fix" />
        <entry key="$PROJECT_DIR$/zhihao-gateway" value="nacos" />
        <entry key="$PROJECT_DIR$/zhihao-oa" value="staging-2.24.0-fix" />
        <entry key="$PROJECT_DIR$/zhihaoscm-server" value="main" />
        <entry key="$PROJECT_DIR$/zhihaoscm-starter" value="1.0.0_file" />
        <entry key="$PROJECT_DIR$/zhihaoscm-transport" value="staging-2.10.1" />
        <entry key="$PROJECT_DIR$/zhihaoscm-user-center" value="dev-2.8.1" />
        <entry key="$PROJECT_DIR$/zhihaoscm-web" value="dev-2.25.0" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/supplier-service" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="GitToolBoxStore">
    <option name="branchesCleanupHistory">
      <BranchesCleanupHistory>
        <option name="history">
          <list>
            <BranchCleanupEntry>
              <option name="deletions">
                <list>
                  <BranchDeletion>
                    <option name="hash" value="8aca682f2ccbe1b2595e047b6a2839f57941675d" />
                    <option name="name" value="staging-2.21.0_usercenter" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="51db771df13694ffc7723c24c2621cc1d4614447" />
                    <option name="name" value="dev-2.22.0" />
                  </BranchDeletion>
                </list>
              </option>
              <option name="timestamp" value="1743149596974" />
            </BranchCleanupEntry>
            <BranchCleanupEntry>
              <option name="deletions">
                <list>
                  <BranchDeletion>
                    <option name="hash" value="f4de540458a28c43dbeaf05e62dc2b40ada5bc75" />
                    <option name="name" value="staging-2.24.0-fix" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="3bbb6f83a0d1d8b3d204f76c14e2abcd99f13c74" />
                    <option name="name" value="staging-2.24.0-fix" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="853d8c7867fb353a8cf34693ca93c0a7c906fc00" />
                    <option name="name" value="staging-2.24.0-fix" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="4edc37ebba93500bf294fa10d3e22c37cbe75463" />
                    <option name="name" value="staging-2.24.0-fix" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="11a7856a98960a8c4a054a6df8b582a70eb19aa8" />
                    <option name="name" value="staging-2.24.0-fix" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="1ddc449220e320655ba1bb6eb14b1107bf1c7fcd" />
                    <option name="name" value="staging-2.24.0-fix" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="45631c943ff0936e06e2cbec99511145e320c501" />
                    <option name="name" value="dev-1.0.1" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="74b4833816119d38b87e2586e6a1fb1c5f101464" />
                    <option name="name" value="staging-2.24.0-fix" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="63850df2813d9311bfe61b7c799a0df9dcc5dbe5" />
                    <option name="name" value="dev-1.1.0" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="15889a666c87b001070eabc94d82ddf66537f12f" />
                    <option name="name" value="dev-1.0.3" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="ee7380f46c891f88865b0da6b8a3a4157de8f272" />
                    <option name="name" value="dev-1.0.2" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="7e07e2d872632f67f7c6d12efcbb2ce974441032" />
                    <option name="name" value="dev-1.0.4" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="e4bf497406309ef8706073dc5be961d420a6b2fe" />
                    <option name="name" value="staging-1.0.1" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="50d78c39424b672c0a15c80811ae382e482728b3" />
                    <option name="name" value="dev-1.0.1" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="eb8365aa7c97f1d553b676d6754536dd5905d7eb" />
                    <option name="name" value="staging-2.24.0-fix" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="33b860e2a8d5da369a2fd4c4513399552d038748" />
                    <option name="name" value="staging-2.24.0-fix" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="eda75ea01b53078656c0d3fcf1498dae8594a4fd" />
                    <option name="name" value="staging-2.24.0-fix" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="e6bd2ad69adb9775abc0cb0c83e03d1cddaf720e" />
                    <option name="name" value="dev-2.25.0" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="5f641833079ab6f61c7b3874519f918c673e436a" />
                    <option name="name" value="staging-2.24.0" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="22dbc7712592aa5e300e6c76e7ff394de1374f88" />
                    <option name="name" value="staging-2.23.0" />
                  </BranchDeletion>
                  <BranchDeletion>
                    <option name="hash" value="cdbf8e7226520185dd6365e616c8fc2e6fd5b82c" />
                    <option name="name" value="dev-2.23.0" />
                  </BranchDeletion>
                </list>
              </option>
              <option name="timestamp" value="1744872702036" />
            </BranchCleanupEntry>
          </list>
        </option>
      </BranchesCleanupHistory>
    </option>
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1726212928" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/HotswapAgent" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1726325529" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1726325528" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/HotSeconds" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1728544194" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/skywalking-java" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev-1.0.5" />
                    <option name="lastUsedInstant" value="1743486886" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="staging-2.24.0-fix" />
                    <option name="lastUsedInstant" value="1743486824" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-1.1.0" />
                    <option name="lastUsedInstant" value="1739415661" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-1.0.1" />
                    <option name="lastUsedInstant" value="1736472019" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1735018954" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/app" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1743486908" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="staging-2.24.0-fix" />
                    <option name="lastUsedInstant" value="1743486824" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/secret" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev-1.0.0" />
                    <option name="lastUsedInstant" value="1743486995" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="staging-2.24.0-fix" />
                    <option name="lastUsedInstant" value="1743486824" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1723451498" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/zhihao-oa" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1743487035" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="staging-2.24.0-fix" />
                    <option name="lastUsedInstant" value="1743486824" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/system-design" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1743487052" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="staging-2.24.0-fix" />
                    <option name="lastUsedInstant" value="1743486823" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/technical-specifications" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1744247008" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="nacos" />
                    <option name="lastUsedInstant" value="1743487018" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="staging-2.24.0-fix" />
                    <option name="lastUsedInstant" value="1743486824" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="supplier" />
                    <option name="lastUsedInstant" value="1740377509" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/zhihao-gateway" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.10.1" />
                    <option name="lastUsedInstant" value="1750670319" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.8.1" />
                    <option name="lastUsedInstant" value="1747014353" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.7.0" />
                    <option name="lastUsedInstant" value="1743486950" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.6.0" />
                    <option name="lastUsedInstant" value="1740732934" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.5.0" />
                    <option name="lastUsedInstant" value="1736993554" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/zhihaoscm-user-center" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.28.1" />
                    <option name="lastUsedInstant" value="1750905469" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.25.0" />
                    <option name="lastUsedInstant" value="1743554499" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1743554498" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/zhihaoscm-web" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev-1.3.1" />
                    <option name="lastUsedInstant" value="1751506638" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="staging-1.2.2" />
                    <option name="lastUsedInstant" value="1750837842" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-1.2.1" />
                    <option name="lastUsedInstant" value="1750834815" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="prod" />
                    <option name="lastUsedInstant" value="1750249669" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1750247206" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/supplier-service" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="1.0.0" />
                    <option name="lastUsedInstant" value="1751939924" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="1.0.0_file" />
                    <option name="lastUsedInstant" value="1751939744" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1743486969" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/zhihaoscm-starter" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.11.0" />
                    <option name="lastUsedInstant" value="1751946266" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="staging-2.10.1" />
                    <option name="lastUsedInstant" value="1751946141" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.10.1" />
                    <option name="lastUsedInstant" value="1750665237" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.9.0" />
                    <option name="lastUsedInstant" value="1748238167" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.8.0" />
                    <option name="lastUsedInstant" value="1744939275" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/zhihaoscm-transport" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="staging-2.28.0" />
                    <option name="lastUsedInstant" value="1752585471" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1752136728" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.29.0" />
                    <option name="lastUsedInstant" value="1750987949" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="staging-2.26.0" />
                    <option name="lastUsedInstant" value="1747723049" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev-2.26.0" />
                    <option name="lastUsedInstant" value="1746666204" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/zhihaoscm-server" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="true" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/zhihaoscm-user-center/src/main/resources/bootstrap.yml" root0="SKIP_INSPECTION" />
  </component>
  <component name="HttpClientSelectedEnvironments">
    <file url="jar://$APPLICATION_HOME_DIR$/plugins/restClient/lib/restClient.jar!/com/intellij/ws/rest/client/requests/collection/post-requests.http" environment="test" />
    <file url="file://$PROJECT_DIR$/测试.http" />
  </component>
  <component name="KotlinScriptingSettings">
    <scriptDefinition className="org.jetbrains.kotlin.scripting.resolve.KotlinScriptDefinitionFromAnnotatedTemplate" definitionName="KotlinBuildScript">
      <order>2147483647</order>
      <autoReloadConfigurations>true</autoReloadConfigurations>
    </scriptDefinition>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="$PROJECT_DIR$/../../repo" />
        <option name="userSettingsFile" value="$PROJECT_DIR$/../../apache-maven-3.9.8/conf/settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="PerforceDirect.Settings">
    <option name="CHARSET" value="none" />
  </component>
  <component name="ProblemsViewState">
    <option name="proportion" value="0.25792506" />
    <option name="selectedTabId" value="CurrentFile" />
    <option name="showPreview" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2k3Q7DpRCkEs4OR5k8qdGIT2py3" />
  <component name="ProjectViewState">
    <option name="abbreviatePackageNames" value="true" />
    <option name="compactDirectories" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showMembers" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Docker.Docker 镜像.executor&quot;: &quot;Run&quot;,
    &quot;Docker.Dockerfile (1).executor&quot;: &quot;Run&quot;,
    &quot;Docker.Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Downloaded.Files.Path.Enabled&quot;: &quot;false&quot;,
    &quot;Flutter Test.tests in widget_test.dart.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Run Plugin.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.zhihao-hot [build].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.zhihao-hot [classes].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.zhihao-hot [clean].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.zhihao-hot [jar].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.zhihao-hot [runIde].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.zhihao-hot [setupDependencies].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.zhihao-hot [testClasses].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.加载 Gradle 依赖项.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.构建 zhihao-hot.executor&quot;: &quot;Run&quot;,
    &quot;HTTP 请求.测试 | #1.executor&quot;: &quot;Run&quot;,
    &quot;HTTP 请求.测试 | #2.executor&quot;: &quot;Run&quot;,
    &quot;JAR 应用程序.zhihaoscm-service-1.0.0-dev.jar.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AccessTokenClientTest.getAccessToken.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.AisTest.geocoder.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AisTest.test.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AliyunOcrSdkApplicationTests.recognizeBusinessLicense.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.AliyunSmsSdkApplicationTests.sendNoticeSms.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AliyunSmsSdkApplicationTests.sendVerifyCode.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ContractClientTest.companyAuthH5PageWithLicense.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ContractClientTest.companyAuthUrl.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ContractClientTest.companyDetail.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ContractClientTest.createEmployee.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ContractClientTest.detail.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ContractClientTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ContractClientTest.invalid.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ContractClientTest.pcUrl.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.ContractClientTest.personalAuthPcPage.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ContractClientTest.privilegeUrl.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ContractClientTest.removeEmployee.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.HmgClientTest.testQueryChargesByNo.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.HmgClientTest.testQueryOrderByNo.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.HmgClientTest.testQueryWaybillByNo (1).executor&quot;: &quot;Run&quot;,
    &quot;JUnit.HmgClientTest.testQueryWaybillByNo.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.HmgClientTest.testSendSmsCode.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ImportCustomerTest.dsda.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.InfoTest.download.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.LoginClientTest.code2Session.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.MpGenerator.getAccessToken.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.MpGenerator.readResource.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.MpGenerator.test.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.Test.Test.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.Test.testCreateOrder.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.TestMessage.dsadas.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.TestMessage.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.UserCenterTest.getData.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.UserCenterTest.testService.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.VehicleInfoTest.recognizeVin.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.VisionLLMTest.testVehicleVin.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.WxBotSdkApplicationTests.chatroomMember.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.zhihao-hot中的所有.executor&quot;: &quot;Run&quot;,
    &quot;JavaMethodFindUsagesOptions.isImplementingMethods&quot;: &quot;true&quot;,
    &quot;JavaMethodFindUsagesOptions.isSearchForBaseMethod&quot;: &quot;false&quot;,
    &quot;Maven.apm-vertx-core-3.x-plugin [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.hotswap-agent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.hotswap-agent [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.hotswap-agent-aggregator [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.hotswap-agent-aggregator [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.hotswap-agent-aggregator [verify].executor&quot;: &quot;Run&quot;,
    &quot;Maven.hotswap-core [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.java-agent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.java-agent [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.java-agent-network [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.java-agent-network [kr.motd.maven:os-maven-plugin:1.6.2:detect].executor&quot;: &quot;Run&quot;,
    &quot;Maven.java-agent-network [org.xolstice.maven.plugins:protobuf-maven-plugin:0.6.1:compile-javanano].executor&quot;: &quot;Run&quot;,
    &quot;Maven.java-agent-network [org.xolstice.maven.plugins:protobuf-maven-plugin:0.6.1:compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.java-agent-network [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.java-agent-protocol [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.java-agent-sniffer [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.starter [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.starter [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.starter [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.startup [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.startup [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.supplier-server [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-admin-api [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-custom-api [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-domain [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-domain [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-notice [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-notice [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-server [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-server [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-server [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-server [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-service [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-service [com.spotify:docker-maven-plugin:0.4.10:build].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-service [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.zhihaoscm-user-center [clean].executor&quot;: &quot;Run&quot;,
    &quot;Notification.DisplayName-DoNotAsk-Database detector&quot;: &quot;数据库检测器&quot;,
    &quot;Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;找到数据库连接形参&quot;,
    &quot;Notification.DoNotAsk-Database detector&quot;: &quot;true&quot;,
    &quot;Notification.DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;true&quot;,
    &quot;Repository.Attach.Annotations&quot;: &quot;false&quot;,
    &quot;Repository.Attach.JavaDocs&quot;: &quot;false&quot;,
    &quot;Repository.Attach.Sources&quot;: &quot;false&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;Spring Boot.CustomApiApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.GatewayBoot.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.MessageApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.NoticeApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SupplierServiceBoot.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.UserCenterApplication (1).executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.UserCenterApplication (2).executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.UserCenterApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.WsApplication (1).executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.WsApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ZhihaoAdminApiBoot.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ZhihaoscmOrgServiceBoot.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ZhihaoscmServiceBoot.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ZhihaoscmTransportBoot.executor&quot;: &quot;Run&quot;,
    &quot;android.gradle.sync.needed&quot;: &quot;true&quot;,
    &quot;com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrary&quot;: &quot;JUnit5&quot;,
    &quot;com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrarySuperClass.JUnit5&quot;: &quot;&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev-1.4.0&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;jbr-17&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;/usr/local/coding/workspace/zhihao/zhihaoscm-starter/zhihao-sdk/bank-sdk/src/test/java/com/zhihaoscm/bank/api&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs.protractor.protractor_package&quot;: &quot;&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.********&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;settings.editor.splitter.proportion&quot;: &quot;0.********&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;,
    &quot;ts.external.directory.path&quot;: &quot;/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/plugins/javascript-plugin/jsLanguageServicesImpl/external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.APICustomizer.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.AdvertResource.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.ArticleForm.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.AsyncConfig.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileTransJavaDemo.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.HistoryInterceptor.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.HwRedisJwtTokenService.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.InputDialog.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.JsonSerializer.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.LoginValidator.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Otms3ApiManager.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.PcWxAuthenticationSuccessHandler.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.PurchaseContractDef.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.RedisBitmap.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.RedisJwtTokenService.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.RefreshMetadataEventListener.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.ShipWeightInference.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.ShipWeightPrediction.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.SpelConfig.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Test.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Test2.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Test3.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.TransportOrderShipDef.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.ViTImageClassificationWithDJL.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.WatermarkDemo.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.WebSecurityConfiguration.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.com.zhihaoscm.admin.api.JsonSerializer.executor&quot;: &quot;Run&quot;,
    &quot;远程 JVM 调试.admin-api.executor&quot;: &quot;Debug&quot;,
    &quot;远程 JVM 调试.custom-api.executor&quot;: &quot;Debug&quot;,
    &quot;远程 JVM 调试.gateway.executor&quot;: &quot;Debug&quot;,
    &quot;远程 JVM 调试.servce.executor&quot;: &quot;Debug&quot;,
    &quot;远程 JVM 调试.service.executor&quot;: &quot;Debug&quot;,
    &quot;远程 JVM 调试.user_center.executor&quot;: &quot;Debug&quot;,
    &quot;远程 JVM 调试.user—center.executor&quot;: &quot;Debug&quot;,
    &quot;远程 JVM 调试.ws.executor&quot;: &quot;Debug&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/zhihaoscm-starter/zhihao-sdk/bank-sdk/src/test/java/com/zhihaoscm/bank/api" />
      <recent name="$PROJECT_DIR$/zhihaoscm-starter/zhihao-sdk/bank-sdk/src" />
      <recent name="$PROJECT_DIR$/zhihaoscm-starter/zhihao-sdk/hmg-sdk/src/main/resources/META-INF" />
      <recent name="$PROJECT_DIR$/zhihaoscm-starter/zhihao-sdk/hmg-sdk/src/test" />
      <recent name="$PROJECT_DIR$/zhihaoscm-server/zhihaoscm-user-center/src/main/java/com/zhihaoscm" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
      <recent name="$PROJECT_DIR$/build" />
      <recent name="$PROJECT_DIR$/zhihaoscm-server/zhihaoscm-service/src/main/java/com/zhihaoscm" />
      <recent name="$PROJECT_DIR$/zhihaoscm-transport/src/main/java/com/zhihaoscm/config" />
      <recent name="$PROJECT_DIR$/zhihao-hot/src/main/java/cxs/idea/plugin" />
    </key>
    <key name="CreateTestDialog.Recents.Supers">
      <recent name="" />
    </key>
    <key name="ExtractSuperBase.RECENT_KEYS">
      <recent name="com.zhihaoscm.service.core.service.impl" />
    </key>
    <key name="CreateTestDialog.RecentsKey">
      <recent name="com.zhihaoscm.service.core.service.impl" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.zhihaoscm.hmg.sdk.api" />
      <recent name="com.zhihaoscm.hmg.sdk" />
      <recent name="com.zhihaoscm" />
      <recent name="com.zhihaoscm.usercenter.config.configuration" />
      <recent name="com.zhihaoscm.service.config.security" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
        <option value="docker-deploy" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.SupplierServiceBoot">
    <configuration name="测试 | #1" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/测试.http" executionIdentifier="#1" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="测试 | #2" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/测试.http" executionIdentifier="#2" index="2" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="HmgClientTest.testQueryOrderByNo" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="hmg-sdk" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhihaoscm.hmg.sdk.api.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.zhihaoscm.hmg.sdk.api" />
      <option name="MAIN_CLASS_NAME" value="com.zhihaoscm.hmg.sdk.api.HmgClientTest" />
      <option name="METHOD_NAME" value="testQueryOrderByNo" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HmgClientTest.testQueryWaybillByNo (1)" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="hmg-sdk" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhihaoscm.hmg.sdk.api.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.zhihaoscm.hmg.sdk.api" />
      <option name="MAIN_CLASS_NAME" value="com.zhihaoscm.hmg.sdk.api.HmgClientTest" />
      <option name="METHOD_NAME" value="testQueryWaybillByNo" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HmgClientTest.testSendSmsCode" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="hmg-sdk" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhihaoscm.hmg.sdk.api.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.zhihaoscm.hmg.sdk.api" />
      <option name="MAIN_CLASS_NAME" value="com.zhihaoscm.hmg.sdk.api.HmgClientTest" />
      <option name="METHOD_NAME" value="testSendSmsCode" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="custom-api" type="Remote">
      <module name="zhihaoscm-custom-api" />
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="9082" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="9082" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="gateway" type="Remote">
      <module name="zhihaoscm-gateway" />
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="9012" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="9012" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="service" type="Remote">
      <module name="supplier-user-center" />
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="9082" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="9082" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="service" type="Remote">
      <module name="zhihaoscm-service" />
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="9080" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="9080" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="user_center" type="Remote">
      <module name="zhihaoscm-user-center (1)" />
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="9082" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="9082" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="ws" type="Remote">
      <module name="supplier-ws" />
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="8084" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="8084" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="ws" type="Remote">
      <module name="zhihaoscm-ws" />
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="9084" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="9084" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="AppApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-application" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.application.AppApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayBoot" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.gateway.GatewayBoot" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayBoot" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.gateway.GatewayBoot" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="NoticeApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="supplier-notice" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.notice.NoticeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="NoticeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-notice" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.notice.NoticeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SupplierServiceBoot" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="supplier-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.service.SupplierServiceBoot" />
      <option name="VM_PARAMETERS" value="-javaagent:$USER_HOME$/Downloads/skywalking-agent/skywalking-agent.jar -Dskywalking.agent.service_name=supplier-prod::supplier-service -Dskywalking.collector.backend_service=8.149.136.2:11800" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TimeScheduleApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="supplier-time-schedule" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.time.schedule.TimeScheduleApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TimeScheduleApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-time-schedule" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.time.schedule.TimeScheduleApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TimeScheduleApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-time-schedule" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.time.schedule.TimeScheduleApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserCenterApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="supplier-user-center" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.usercenter.UserCenterApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserCenterApplication (2)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="supplier-user-center" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.usercenter.UserCenterApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserCenterApplication (3)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="supplier-user-center" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.usercenter.UserCenterApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserCenterApplication (4)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-user-center (2)" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.usercenter.UserCenterApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserCenterApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-user-center (1)" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.usercenter.UserCenterApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WsApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="supplier-ws" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.ws.WsApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WsApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-ws" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.ws.WsApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WsApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-ws" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.ws.WsApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ZhihaoscmServiceBoot" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.service.ZhihaoscmServiceBoot" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ZhihaoscmServiceBoot" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.service.ZhihaoscmServiceBoot" />
      <option name="VM_PARAMETERS" value="-XX:+AllowEnhancedClassRedefinition -javaagent:$PROJECT_DIR$/HotswapAgent/hotswap-agent/target/hotswap-agent.jar=autoHotswap=true --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/jdk.internal.loader=ALL-UNNAMED --add-opens java.base/sun.nio.ch=ALL-UNNAMED" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ZhihaoscmTransportBoot" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhihaoscm-transport" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhihaoscm.ZhihaoscmTransportBoot" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-image" temporary="true">
      <deployment type="docker-image">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="HTTP 请求.测试 | #1" />
      <item itemvalue="HTTP 请求.测试 | #2" />
      <item itemvalue="JUnit.HmgClientTest.testQueryOrderByNo" />
      <item itemvalue="JUnit.HmgClientTest.testQueryWaybillByNo (1)" />
      <item itemvalue="JUnit.HmgClientTest.testSendSmsCode" />
      <item itemvalue="Spring Boot.UserCenterApplication (4)" />
      <item itemvalue="Spring Boot.AppApplication (1)" />
      <item itemvalue="Spring Boot.UserCenterApplication (3)" />
      <item itemvalue="Spring Boot.UserCenterApplication (1)" />
      <item itemvalue="Spring Boot.ZhihaoscmTransportBoot" />
      <item itemvalue="Spring Boot.NoticeApplication (1)" />
      <item itemvalue="Spring Boot.SupplierServiceBoot" />
      <item itemvalue="Spring Boot.TimeScheduleApplication (1)" />
      <item itemvalue="Spring Boot.UserCenterApplication (2)" />
      <item itemvalue="Spring Boot.WsApplication (1)" />
      <item itemvalue="Spring Boot.UserCenterApplication" />
      <item itemvalue="Spring Boot.ZhihaoscmServiceBoot" />
      <item itemvalue="Spring Boot.GatewayBoot" />
      <item itemvalue="Spring Boot.NoticeApplication" />
      <item itemvalue="Spring Boot.TimeScheduleApplication" />
      <item itemvalue="Spring Boot.WsApplication" />
      <item itemvalue="远程 JVM 调试.user_center" />
      <item itemvalue="远程 JVM 调试.custom-api" />
      <item itemvalue="远程 JVM 调试.gateway" />
      <item itemvalue="远程 JVM 调试.service" />
      <item itemvalue="远程 JVM 调试.ws" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP 请求.测试 | #1" />
        <item itemvalue="HTTP 请求.测试 | #2" />
        <item itemvalue="JUnit.HmgClientTest.testSendSmsCode" />
        <item itemvalue="JUnit.HmgClientTest.testQueryWaybillByNo (1)" />
        <item itemvalue="JUnit.HmgClientTest.testQueryOrderByNo" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="实体" />
  </component>
  <component name="SvnConfiguration">
    <configuration>$USER_HOME$/.subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="ea99aca9-8ecf-4c7b-87f7-a3bb70d5fb86" name="更改" comment="" />
      <created>1722509610456</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1722509610456</updated>
      <workItem from="1722509611468" duration="2399000" />
      <workItem from="1722517000823" duration="458000" />
      <workItem from="1722560763517" duration="5552000" />
      <workItem from="1722566344839" duration="785000" />
      <workItem from="1722567135229" duration="1000" />
      <workItem from="1722567145849" duration="1608000" />
      <workItem from="1722568957768" duration="274000" />
      <workItem from="1722569239527" duration="325000" />
      <workItem from="1722569581660" duration="6503000" />
      <workItem from="1722584679801" duration="81000" />
      <workItem from="1722584766337" duration="692000" />
      <workItem from="1722585465892" duration="9677000" />
      <workItem from="1722600132330" duration="462000" />
      <workItem from="1722606993593" duration="133000" />
      <workItem from="1722607178831" duration="82000" />
      <workItem from="1722607281209" duration="42000" />
      <workItem from="1722647267657" duration="24555000" />
      <workItem from="1722827349513" duration="930000" />
      <workItem from="1722828290326" duration="21431000" />
      <workItem from="1722904462612" duration="18795000" />
      <workItem from="1722993134663" duration="871000" />
      <workItem from="1723001328101" duration="3791000" />
      <workItem from="1723077535999" duration="80000" />
      <workItem from="1723077623747" duration="7928000" />
      <workItem from="1723099543135" duration="7959000" />
      <workItem from="1723164442798" duration="20574000" />
      <workItem from="1723253032541" duration="284000" />
      <workItem from="1723276360548" duration="346000" />
      <workItem from="1723276716093" duration="210000" />
      <workItem from="1723425177202" duration="20347000" />
      <workItem from="1723510442166" duration="44000" />
      <workItem from="1723510494989" duration="18884000" />
      <workItem from="1723598490171" duration="193000" />
      <workItem from="1723598693307" duration="15019000" />
      <workItem from="1723640786677" duration="695000" />
      <workItem from="1723682835970" duration="3949000" />
      <workItem from="1723700839631" duration="6327000" />
      <workItem from="1723729340449" duration="9000" />
      <workItem from="1723773117586" duration="8876000" />
      <workItem from="1723865692019" duration="266000" />
      <workItem from="1723897496832" duration="813000" />
      <workItem from="1724028304951" duration="6681000" />
      <workItem from="1724067969381" duration="1893000" />
      <workItem from="1724135549381" duration="1761000" />
      <workItem from="1724200376799" duration="14685000" />
      <workItem from="1724287447414" duration="17799000" />
      <workItem from="1724373760852" duration="4957000" />
      <workItem from="1724379712791" duration="20454000" />
      <workItem from="1724495838680" duration="19000" />
      <workItem from="1724574543826" duration="852000" />
      <workItem from="1724639032778" duration="11172000" />
      <workItem from="1724677496530" duration="11000" />
      <workItem from="1724719099200" duration="12658000" />
      <workItem from="1724814570843" duration="6515000" />
      <workItem from="1724894308753" duration="18103000" />
      <workItem from="1725003474347" duration="3885000" />
      <workItem from="1725057945723" duration="164000" />
      <workItem from="1725094548672" duration="4590000" />
      <workItem from="1725181614282" duration="64000" />
      <workItem from="1725181683491" duration="490000" />
      <workItem from="1725182186146" duration="5927000" />
      <workItem from="1725205415220" duration="1058000" />
      <workItem from="1725268111600" duration="3427000" />
      <workItem from="1725323848212" duration="14054000" />
      <workItem from="1725373021346" duration="7000" />
      <workItem from="1725429917363" duration="11000" />
      <workItem from="1725429943033" duration="4319000" />
      <workItem from="1725445364495" duration="622000" />
      <workItem from="1725457205976" duration="20000" />
      <workItem from="1725504630184" duration="12361000" />
      <workItem from="1725583520392" duration="3088000" />
      <workItem from="1725885524064" duration="3237000" />
      <workItem from="1725938027202" duration="4286000" />
      <workItem from="1725951026448" duration="9895000" />
      <workItem from="1726017927426" duration="4265000" />
      <workItem from="1726023703588" duration="14582000" />
      <workItem from="1726056876807" duration="528000" />
      <workItem from="1726101664252" duration="16408000" />
      <workItem from="1726187777006" duration="642000" />
      <workItem from="1726190157415" duration="14721000" />
      <workItem from="1726238199886" duration="631000" />
      <workItem from="1726275426525" duration="30826000" />
      <workItem from="1726448189679" duration="1101000" />
      <workItem from="1726498261716" duration="1742000" />
      <workItem from="1726557707394" duration="3084000" />
      <workItem from="1726621952724" duration="30634000" />
      <workItem from="1726706526125" duration="15658000" />
      <workItem from="1726731860826" duration="12294000" />
      <workItem from="1726792663978" duration="13093000" />
      <workItem from="1726822804931" duration="13479000" />
      <workItem from="1727053209388" duration="5443000" />
      <workItem from="1727074562836" duration="5870000" />
      <workItem from="1727084307200" duration="5904000" />
      <workItem from="1727138743047" duration="4242000" />
      <workItem from="1727148999490" duration="19833000" />
      <workItem from="1727224608100" duration="33214000" />
      <workItem from="1727321081172" duration="5952000" />
      <workItem from="1727336522495" duration="6615000" />
      <workItem from="1727397071930" duration="35203000" />
      <workItem from="1727570423633" duration="761000" />
      <workItem from="1727573114839" duration="21781000" />
      <workItem from="1727657637835" duration="450000" />
      <workItem from="1727658163530" duration="6957000" />
      <workItem from="1727667421329" duration="4000" />
      <workItem from="1727667444028" duration="12820000" />
      <workItem from="1727690273058" duration="196000" />
      <workItem from="1727748033130" duration="2086000" />
      <workItem from="1727837009486" duration="1565000" />
      <workItem from="1727838596319" duration="11706000" />
      <workItem from="1727858782211" duration="13568000" />
      <workItem from="1727912198222" duration="3373000" />
      <workItem from="1728005391810" duration="23057000" />
      <workItem from="1728036186212" duration="10023000" />
      <workItem from="1728085829767" duration="12960000" />
      <workItem from="1728119434904" duration="1269000" />
      <workItem from="1728186886755" duration="2919000" />
      <workItem from="1728205762560" duration="11876000" />
      <workItem from="1728269680014" duration="519000" />
      <workItem from="1728270289190" duration="385000" />
      <workItem from="1728270680493" duration="864000" />
      <workItem from="1728278300675" duration="971000" />
      <workItem from="1728285228536" duration="60000" />
      <workItem from="1728285302304" duration="234000" />
      <workItem from="1728285671720" duration="60000" />
      <workItem from="1728285764704" duration="968000" />
      <workItem from="1728286757031" duration="127000" />
      <workItem from="1728286889419" duration="73000" />
      <workItem from="1728287332351" duration="1478000" />
      <workItem from="1728288839568" duration="210000" />
      <workItem from="1728289054355" duration="3982000" />
      <workItem from="1728301930707" duration="2193000" />
      <workItem from="1728349551623" duration="29171000" />
      <workItem from="1728434476545" duration="1133000" />
      <workItem from="1728435746825" duration="9000" />
      <workItem from="1728435823526" duration="2863000" />
      <workItem from="1728438791437" duration="24497000" />
      <workItem from="1728473718430" duration="7286000" />
      <workItem from="1728520638050" duration="326000" />
      <workItem from="1728521081605" duration="18900000" />
      <workItem from="1728543514542" duration="11626000" />
      <workItem from="1728607404724" duration="38000" />
      <workItem from="1728607623453" duration="9839000" />
      <workItem from="1728619065251" duration="17198000" />
      <workItem from="1728693719784" duration="27000" />
      <workItem from="1728693816192" duration="22447000" />
      <workItem from="1728780610558" duration="15000" />
      <workItem from="1728780683261" duration="14000" />
      <workItem from="1728780776722" duration="147000" />
      <workItem from="1728781283100" duration="17042000" />
      <workItem from="1728872719843" duration="8272000" />
      <workItem from="1728952765296" duration="4000" />
      <workItem from="1728963434338" duration="14382000" />
      <workItem from="1729040111032" duration="22941000" />
      <workItem from="1729127739018" duration="14428000" />
      <workItem from="1729156288111" duration="2834000" />
      <workItem from="1729213033653" duration="16408000" />
      <workItem from="1729319594486" duration="696000" />
      <workItem from="1729473925260" duration="2307000" />
      <workItem from="1729476532111" duration="3066000" />
      <workItem from="1729481242619" duration="12427000" />
      <workItem from="1729565461999" duration="12978000" />
      <workItem from="1729643709241" duration="2015000" />
      <workItem from="1729645806030" duration="93000" />
      <workItem from="1729645927705" duration="22190000" />
      <workItem from="1729734925004" duration="23454000" />
      <workItem from="1729817277785" duration="74000" />
      <workItem from="1729819469119" duration="31000" />
      <workItem from="1729823971905" duration="3703000" />
      <workItem from="1729844913083" duration="2601000" />
      <workItem from="1729863512324" duration="537000" />
      <workItem from="1729864087368" duration="90000" />
      <workItem from="1729944157722" duration="165000" />
      <workItem from="1730000189898" duration="16000" />
      <workItem from="1730093514876" duration="13645000" />
      <workItem from="1730123423981" duration="469000" />
      <workItem from="1730123900061" duration="433000" />
      <workItem from="1730124357409" duration="143000" />
      <workItem from="1730162773137" duration="285000" />
      <workItem from="1730163394430" duration="4670000" />
      <workItem from="1730169300815" duration="11648000" />
      <workItem from="1730190931820" duration="11519000" />
      <workItem from="1730249563629" duration="30000" />
      <workItem from="1730250837286" duration="13686000" />
      <workItem from="1730277394863" duration="4616000" />
      <workItem from="1730337750372" duration="15834000" />
      <workItem from="1730372559746" duration="12000" />
      <workItem from="1730421711897" duration="5268000" />
      <workItem from="1730431810229" duration="3466000" />
      <workItem from="1730437983688" duration="7534000" />
      <workItem from="1730552753531" duration="312000" />
      <workItem from="1730691337640" duration="2200000" />
      <workItem from="1730709671199" duration="3838000" />
      <workItem from="1730721945679" duration="2134000" />
      <workItem from="1730767257211" duration="64000" />
      <workItem from="1730767975021" duration="4403000" />
      <workItem from="1730774165962" duration="17495000" />
      <workItem from="1730856191610" duration="10003000" />
      <workItem from="1730880714390" duration="3874000" />
      <workItem from="1730941481400" duration="18519000" />
      <workItem from="1730987238143" duration="11000" />
      <workItem from="1731027422978" duration="15786000" />
      <workItem from="1731286808250" duration="399000" />
      <workItem from="1731288594699" duration="4797000" />
      <workItem from="1731297044205" duration="15048000" />
      <workItem from="1731321556593" duration="32000" />
      <workItem from="1731328378983" duration="371000" />
      <workItem from="1731328906620" duration="144000" />
      <workItem from="1731374360980" duration="12000" />
      <workItem from="1731394803860" duration="4301000" />
      <workItem from="1731462056779" duration="10511000" />
      <workItem from="1731483136843" duration="5965000" />
      <workItem from="1731494233402" duration="3000" />
      <workItem from="1731503480871" duration="834000" />
      <workItem from="1731545863115" duration="21000" />
      <workItem from="1731546373861" duration="592000" />
      <workItem from="1731548067692" duration="16453000" />
      <workItem from="1731632259060" duration="6867000" />
      <workItem from="1731644127020" duration="9456000" />
      <workItem from="1731661349359" duration="936000" />
      <workItem from="1731898822553" duration="11434000" />
      <workItem from="1732064155420" duration="5298000" />
      <workItem from="1732153605884" duration="42000" />
      <workItem from="1732171823343" duration="2026000" />
      <workItem from="1732178107206" duration="1330000" />
      <workItem from="1732181389431" duration="21342000" />
      <workItem from="1732496165650" duration="4043000" />
      <workItem from="1732506656619" duration="16112000" />
      <workItem from="1732588942955" duration="121000" />
      <workItem from="1732591403972" duration="3022000" />
      <workItem from="1732605948102" duration="22575000" />
      <workItem from="1732777094599" duration="8041000" />
      <workItem from="1732842455086" duration="256000" />
      <workItem from="1732842715595" duration="12506000" />
      <workItem from="1733190636577" duration="3072000" />
      <workItem from="1733193786587" duration="6235000" />
      <workItem from="1733364106479" duration="1341000" />
      <workItem from="1733376737221" duration="38154000" />
      <workItem from="1733492007692" duration="594000" />
      <workItem from="1733723581912" duration="35278000" />
      <workItem from="1733826179812" duration="3269000" />
      <workItem from="1733879021509" duration="18125000" />
      <workItem from="1733965432704" duration="32276000" />
      <workItem from="1734312940067" duration="14175000" />
      <workItem from="1734340587801" duration="16373000" />
      <workItem from="1734489207471" duration="14378000" />
      <workItem from="1734571599194" duration="1907000" />
      <workItem from="1734574994816" duration="1102000" />
      <workItem from="1734576158548" duration="116000" />
      <workItem from="1734576288005" duration="10914000" />
      <workItem from="1734749748801" duration="9178000" />
      <workItem from="1734916592842" duration="10511000" />
      <workItem from="1734951254527" duration="2834000" />
      <workItem from="1735002395399" duration="1575000" />
      <workItem from="1735004055387" duration="1058000" />
      <workItem from="1735005114980" duration="491000" />
      <workItem from="1735005673472" duration="17037000" />
      <workItem from="1735098943652" duration="23081000" />
      <workItem from="1735261666273" duration="13539000" />
      <workItem from="1735531953501" duration="16060000" />
      <workItem from="1735781774523" duration="12470000" />
      <workItem from="1735868126301" duration="405000" />
      <workItem from="1735869136488" duration="5457000" />
      <workItem from="1735875363516" duration="6000" />
      <workItem from="1735875747959" duration="6000" />
      <workItem from="1735875989886" duration="6000" />
      <workItem from="1735876168111" duration="650000" />
      <workItem from="1735889058682" duration="7794000" />
      <workItem from="1736133112250" duration="705000" />
      <workItem from="1736148340853" duration="9157000" />
      <workItem from="1736211765433" duration="4323000" />
      <workItem from="1736228691933" duration="5856000" />
      <workItem from="1736321599752" duration="5241000" />
      <workItem from="1736392435978" duration="7082000" />
      <workItem from="1736471981632" duration="22005000" />
      <workItem from="1736746173795" duration="9260000" />
      <workItem from="1736769408316" duration="341000" />
      <workItem from="1736841344704" duration="2520000" />
      <workItem from="1736990742085" duration="9445000" />
      <workItem from="1737083201580" duration="4594000" />
      <workItem from="1737182932285" duration="5439000" />
      <workItem from="1737508457593" duration="11655000" />
      <workItem from="1737593188912" duration="16328000" />
      <workItem from="1737681447031" duration="6937000" />
      <workItem from="1737941506155" duration="1308000" />
      <workItem from="1738654246200" duration="89000" />
      <workItem from="1738654357544" duration="5000" />
      <workItem from="1738722278986" duration="2902000" />
      <workItem from="1738737659118" duration="9000" />
      <workItem from="1738739516836" duration="3613000" />
      <workItem from="1738822144662" duration="643000" />
      <workItem from="1738897649419" duration="13713000" />
      <workItem from="1738978895572" duration="5271000" />
      <workItem from="1739004460309" duration="2230000" />
      <workItem from="1739152012420" duration="11353000" />
      <workItem from="1739237631603" duration="22254000" />
      <workItem from="1739322180747" duration="15747000" />
      <workItem from="1739413129670" duration="13321000" />
      <workItem from="1739496634188" duration="30196000" />
      <workItem from="1739848318876" duration="5310000" />
      <workItem from="1739927958502" duration="12015000" />
      <workItem from="1740017665459" duration="5345000" />
      <workItem from="1740105777817" duration="2930000" />
      <workItem from="1740110116403" duration="6873000" />
      <workItem from="1740186531416" duration="22267000" />
      <workItem from="1740463546831" duration="27000" />
      <workItem from="1740465343337" duration="2378000" />
      <workItem from="1740535659113" duration="9669000" />
      <workItem from="1740625811453" duration="4060000" />
      <workItem from="1740636183951" duration="7876000" />
      <workItem from="1740722001248" duration="6421000" />
      <workItem from="1740829975187" duration="18399000" />
      <workItem from="1741067629367" duration="13943000" />
      <workItem from="1741134931635" duration="76000" />
      <workItem from="1741135028390" duration="23091000" />
      <workItem from="1741244833541" duration="7127000" />
      <workItem from="1741312925627" duration="1182000" />
      <workItem from="1741314125404" duration="10147000" />
      <workItem from="1741425567210" duration="2528000" />
      <workItem from="1741568703188" duration="306000" />
      <workItem from="1741569150123" duration="8308000" />
      <workItem from="1741748709746" duration="1079000" />
      <workItem from="1741750134125" duration="5597000" />
      <workItem from="1741827646092" duration="3607000" />
      <workItem from="1741837861019" duration="5312000" />
      <workItem from="1741853275322" duration="5481000" />
      <workItem from="1741919864312" duration="459000" />
      <workItem from="1741946474247" duration="932000" />
      <workItem from="1742002164036" duration="3364000" />
      <workItem from="1742022515313" duration="7305000" />
      <workItem from="1742283205786" duration="3755000" />
      <workItem from="1742458426619" duration="3431000" />
      <workItem from="1742473507070" duration="4447000" />
      <workItem from="1742518638825" duration="7481000" />
      <workItem from="1742627882356" duration="2126000" />
      <workItem from="1742950937668" duration="21206000" />
      <workItem from="1743054323699" duration="477000" />
      <workItem from="1743054829037" duration="2304000" />
      <workItem from="1743125596619" duration="5139000" />
      <workItem from="1743130966190" duration="20020000" />
      <workItem from="1743164126358" duration="157000" />
      <workItem from="1743399728431" duration="3216000" />
      <workItem from="1743485675437" duration="3365000" />
      <workItem from="1743495395583" duration="4758000" />
      <workItem from="1743555831950" duration="10943000" />
      <workItem from="1743647963746" duration="1523000" />
      <workItem from="1743656659712" duration="20000" />
      <workItem from="1743657030173" duration="17067000" />
      <workItem from="1743992249617" duration="316000" />
      <workItem from="1744103773497" duration="18615000" />
      <workItem from="1744246949081" duration="23299000" />
      <workItem from="1744336095318" duration="3640000" />
      <workItem from="1744361883791" duration="765000" />
      <workItem from="1744681485945" duration="41310000" />
      <workItem from="1744856875064" duration="4806000" />
      <workItem from="1744870207759" duration="7199000" />
      <workItem from="1744939243861" duration="9489000" />
      <workItem from="1744985630504" duration="1411000" />
      <workItem from="1745070436073" duration="206000" />
      <workItem from="1745070657225" duration="81000" />
      <workItem from="1745286651355" duration="442000" />
      <workItem from="1745287302521" duration="6115000" />
      <workItem from="1745371339334" duration="3628000" />
      <workItem from="1745415844361" duration="444000" />
      <workItem from="1745416309393" duration="4022000" />
      <workItem from="1745466343305" duration="14360000" />
      <workItem from="1745751749330" duration="1418000" />
      <workItem from="1745826657334" duration="759000" />
      <workItem from="1745830631467" duration="598000" />
      <workItem from="1745910378112" duration="3331000" />
      <workItem from="1745914658762" duration="10005000" />
      <workItem from="1746445829533" duration="651000" />
      <workItem from="1746492485128" duration="11495000" />
      <workItem from="1746580520361" duration="16074000" />
      <workItem from="1746696775190" duration="4986000" />
      <workItem from="1747010418891" duration="9438000" />
      <workItem from="1747103516723" duration="3202000" />
      <workItem from="1747108557671" duration="639000" />
      <workItem from="1747190293440" duration="7725000" />
      <workItem from="1747279157749" duration="13366000" />
      <workItem from="1747620782133" duration="1792000" />
      <workItem from="1747722658435" duration="1883000" />
      <workItem from="1747728911516" duration="6000" />
      <workItem from="1747987014608" duration="5148000" />
      <workItem from="1748227434761" duration="291000" />
      <workItem from="1748227745521" duration="16382000" />
      <workItem from="1748310013451" duration="19025000" />
      <workItem from="1748338432390" duration="651000" />
      <workItem from="1748418917539" duration="4000" />
      <workItem from="1748425244851" duration="1546000" />
      <workItem from="1748432171778" duration="53000" />
      <workItem from="1748488664626" duration="99000" />
      <workItem from="1748921307199" duration="54000" />
      <workItem from="1748930671787" duration="74000" />
      <workItem from="1748930758797" duration="1563000" />
      <workItem from="1748935379649" duration="2355000" />
      <workItem from="1748949851960" duration="171000" />
      <workItem from="1749181873715" duration="2831000" />
      <workItem from="1749434229645" duration="774000" />
      <workItem from="1750129225184" duration="17000" />
      <workItem from="1750129295088" duration="236000" />
      <workItem from="1750247002705" duration="440000" />
      <workItem from="1750249608288" duration="574000" />
      <workItem from="1750665187264" duration="12469000" />
      <workItem from="1750826521935" duration="4067000" />
      <workItem from="1750842440870" duration="659000" />
      <workItem from="1750903442861" duration="7782000" />
      <workItem from="1750924136106" duration="3925000" />
      <workItem from="1750940509834" duration="5090000" />
      <workItem from="1750996592191" duration="2444000" />
      <workItem from="1751010554606" duration="1916000" />
      <workItem from="1751013248402" duration="6018000" />
      <workItem from="1751262949081" duration="1000" />
      <workItem from="1751263006878" duration="10553000" />
      <workItem from="1751291153108" duration="851000" />
      <workItem from="1751353250973" duration="1476000" />
      <workItem from="1751355500481" duration="114000" />
      <workItem from="1751355640075" duration="406000" />
      <workItem from="1751356141900" duration="16000" />
      <workItem from="1751356198756" duration="78000" />
      <workItem from="1751356433790" duration="12000" />
      <workItem from="1751356485220" duration="14000" />
      <workItem from="1751356544426" duration="23000" />
      <workItem from="1751357702673" duration="9000" />
      <workItem from="1751357766299" duration="21256000" />
      <workItem from="1751546958722" duration="13724000" />
      <workItem from="1751622392261" duration="1339000" />
      <workItem from="1751895430447" duration="24446000" />
      <workItem from="1752478962633" duration="1047000" />
      <workItem from="1752583892184" duration="849000" />
      <workItem from="1752626844409" duration="259000" />
      <workItem from="1753346774784" duration="145000" />
      <workItem from="1753346934823" duration="900000" />
      <workItem from="1753347881201" duration="4606000" />
      <workItem from="1753424182806" duration="217000" />
      <workItem from="1753452729857" duration="157000" />
      <workItem from="1753752612637" duration="3717000" />
      <workItem from="1753771953565" duration="16000" />
      <workItem from="1753772380264" duration="15000" />
      <workItem from="1753773860877" duration="3000" />
      <workItem from="1753778724689" duration="241000" />
      <workItem from="1753856258818" duration="1928000" />
      <workItem from="1753945549755" duration="53000" />
      <workItem from="1753946711187" duration="2000" />
      <workItem from="1754013182826" duration="594000" />
    </task>
    <task id="LOCAL-00066" summary="update:删除失效子账号的token">
      <option name="closed" value="true" />
      <created>1728716900686</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1728716900686</updated>
    </task>
    <task id="LOCAL-00067" summary="update:优化会员兑换刷新token逻辑">
      <option name="closed" value="true" />
      <created>1728734955662</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1728734955662</updated>
    </task>
    <task id="LOCAL-00068" summary="update:优化会员兑换刷新token逻辑">
      <option name="closed" value="true" />
      <created>1728736023522</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1728736023522</updated>
    </task>
    <task id="LOCAL-00069" summary="update:优化会员兑换刷新token逻辑">
      <option name="closed" value="true" />
      <created>1728736245808</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1728736245808</updated>
    </task>
    <task id="LOCAL-00070" summary="update:除异味">
      <option name="closed" value="true" />
      <created>1728787637523</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1728787637523</updated>
    </task>
    <task id="LOCAL-00071" summary="update:文件优化">
      <option name="closed" value="true" />
      <created>1728980269426</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1728980269426</updated>
    </task>
    <task id="LOCAL-00072" summary="update:文件优化">
      <option name="closed" value="true" />
      <created>1728983946397</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1728983946397</updated>
    </task>
    <task id="LOCAL-00073" summary="update:文件优化">
      <option name="closed" value="true" />
      <created>1729046089776</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1729046089776</updated>
    </task>
    <task id="LOCAL-00074" summary="update:测试热部署">
      <option name="closed" value="true" />
      <created>1729048787857</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1729048787857</updated>
    </task>
    <task id="LOCAL-00075" summary="update:测试热部署">
      <option name="closed" value="true" />
      <created>1729058247633</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1729058247633</updated>
    </task>
    <task id="LOCAL-00076" summary="update:测试热部署">
      <option name="closed" value="true" />
      <created>1729058760029</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1729058760029</updated>
    </task>
    <task id="LOCAL-00077" summary="update:测试热部署">
      <option name="closed" value="true" />
      <created>1729059037626</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1729059037626</updated>
    </task>
    <task id="LOCAL-00078" summary="update:测试热部署">
      <option name="closed" value="true" />
      <created>1729064845568</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1729064845568</updated>
    </task>
    <task id="LOCAL-00079" summary="update:测试热部署">
      <option name="closed" value="true" />
      <created>1729064960255</created>
      <option name="number" value="00079" />
      <option name="presentableId" value="LOCAL-00079" />
      <option name="project" value="LOCAL" />
      <updated>1729064960255</updated>
    </task>
    <task id="LOCAL-00080" summary="update:服务合并">
      <option name="closed" value="true" />
      <created>1729654145392</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1729654145393</updated>
    </task>
    <task id="LOCAL-00081" summary="update:去除错误import">
      <option name="closed" value="true" />
      <created>1730435456191</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1730435456191</updated>
    </task>
    <task id="LOCAL-00082" summary="update:删除错误文件">
      <option name="closed" value="true" />
      <created>1730435829530</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1730435829530</updated>
    </task>
    <task id="LOCAL-00083" summary="update:暂时去除灰度配置">
      <option name="closed" value="true" />
      <created>1730436694109</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1730436694109</updated>
    </task>
    <task id="LOCAL-00084" summary="update:数据变更标准aop处理封装">
      <option name="closed" value="true" />
      <created>1730792163787</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1730792163787</updated>
    </task>
    <task id="LOCAL-00085" summary="update:除异味">
      <option name="closed" value="true" />
      <created>1731029698791</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1731029698791</updated>
    </task>
    <task id="LOCAL-00086" summary="update:除异味">
      <option name="closed" value="true" />
      <created>1731029711445</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1731029711445</updated>
    </task>
    <task id="LOCAL-00087" summary="update:去除自定义包扫描路径">
      <option name="closed" value="true" />
      <created>1731031338271</created>
      <option name="number" value="00087" />
      <option name="presentableId" value="LOCAL-00087" />
      <option name="project" value="LOCAL" />
      <updated>1731031338271</updated>
    </task>
    <task id="LOCAL-00088" summary="update:去除自定义包扫描路径">
      <option name="closed" value="true" />
      <created>1731031480668</created>
      <option name="number" value="00088" />
      <option name="presentableId" value="LOCAL-00088" />
      <option name="project" value="LOCAL" />
      <updated>1731031480668</updated>
    </task>
    <task id="LOCAL-00089" summary="update:除异味,历史记录标准实现封装">
      <option name="closed" value="true" />
      <created>1731310167352</created>
      <option name="number" value="00089" />
      <option name="presentableId" value="LOCAL-00089" />
      <option name="project" value="LOCAL" />
      <updated>1731310167352</updated>
    </task>
    <task id="LOCAL-00090" summary="update:测试热部署">
      <option name="closed" value="true" />
      <created>1731483952219</created>
      <option name="number" value="00090" />
      <option name="presentableId" value="LOCAL-00090" />
      <option name="project" value="LOCAL" />
      <updated>1731483952219</updated>
    </task>
    <task id="LOCAL-00091" summary="update:测试热部署">
      <option name="closed" value="true" />
      <created>1731484344826</created>
      <option name="number" value="00091" />
      <option name="presentableId" value="LOCAL-00091" />
      <option name="project" value="LOCAL" />
      <updated>1731484344826</updated>
    </task>
    <task id="LOCAL-00092" summary="update:生产环境增加灰度依赖">
      <option name="closed" value="true" />
      <created>1731574479080</created>
      <option name="number" value="00092" />
      <option name="presentableId" value="LOCAL-00092" />
      <option name="project" value="LOCAL" />
      <updated>1731574479080</updated>
    </task>
    <task id="LOCAL-00093" summary="update:消费模式改为广播模式">
      <option name="closed" value="true" />
      <created>1731584443781</created>
      <option name="number" value="00093" />
      <option name="presentableId" value="LOCAL-00093" />
      <option name="project" value="LOCAL" />
      <updated>1731584443781</updated>
    </task>
    <task id="LOCAL-00094" summary="update:除异味">
      <option name="closed" value="true" />
      <created>1732173841296</created>
      <option name="number" value="00094" />
      <option name="presentableId" value="LOCAL-00094" />
      <option name="project" value="LOCAL" />
      <updated>1732173841296</updated>
    </task>
    <task id="LOCAL-00095" summary="fix:修复导包问题">
      <option name="closed" value="true" />
      <created>1732782041257</created>
      <option name="number" value="00095" />
      <option name="presentableId" value="LOCAL-00095" />
      <option name="project" value="LOCAL" />
      <updated>1732782041257</updated>
    </task>
    <task id="LOCAL-00096" summary="fix:除异味">
      <option name="closed" value="true" />
      <created>1733380553831</created>
      <option name="number" value="00096" />
      <option name="presentableId" value="LOCAL-00096" />
      <option name="project" value="LOCAL" />
      <updated>1733380553831</updated>
    </task>
    <task id="LOCAL-00097" summary="fix:除异味">
      <option name="closed" value="true" />
      <created>1733809742753</created>
      <option name="number" value="00097" />
      <option name="presentableId" value="LOCAL-00097" />
      <option name="project" value="LOCAL" />
      <updated>1733809742754</updated>
    </task>
    <task id="LOCAL-00098" summary="fix:用户中心日志配置添加">
      <option name="closed" value="true" />
      <created>1733829274765</created>
      <option name="number" value="00098" />
      <option name="presentableId" value="LOCAL-00098" />
      <option name="project" value="LOCAL" />
      <updated>1733829274765</updated>
    </task>
    <task id="LOCAL-00099" summary="fix:去除角色传参">
      <option name="closed" value="true" />
      <created>1733829407524</created>
      <option name="number" value="00099" />
      <option name="presentableId" value="LOCAL-00099" />
      <option name="project" value="LOCAL" />
      <updated>1733829407524</updated>
    </task>
    <task id="LOCAL-00100" summary="fix:修复导包">
      <option name="closed" value="true" />
      <created>1734082008286</created>
      <option name="number" value="00100" />
      <option name="presentableId" value="LOCAL-00100" />
      <option name="project" value="LOCAL" />
      <updated>1734082008286</updated>
    </task>
    <task id="LOCAL-00101" summary="fix:修复校验问题">
      <option name="closed" value="true" />
      <created>1734512649796</created>
      <option name="number" value="00101" />
      <option name="presentableId" value="LOCAL-00101" />
      <option name="project" value="LOCAL" />
      <updated>1734512649797</updated>
    </task>
    <task id="LOCAL-00102" summary="feat：新增开发注意事项">
      <option name="closed" value="true" />
      <created>1735554806260</created>
      <option name="number" value="00102" />
      <option name="presentableId" value="LOCAL-00102" />
      <option name="project" value="LOCAL" />
      <updated>1735554806260</updated>
    </task>
    <task id="LOCAL-00103" summary="update：优化app登陆处理">
      <option name="closed" value="true" />
      <created>1739859165477</created>
      <option name="number" value="00103" />
      <option name="presentableId" value="LOCAL-00103" />
      <option name="project" value="LOCAL" />
      <updated>1739859165477</updated>
    </task>
    <task id="LOCAL-00104" summary="update：跳过测试">
      <option name="closed" value="true" />
      <created>1740726705476</created>
      <option name="number" value="00104" />
      <option name="presentableId" value="LOCAL-00104" />
      <option name="project" value="LOCAL" />
      <updated>1740726705476</updated>
    </task>
    <task id="LOCAL-00105" summary="update：修正跳转">
      <option name="closed" value="true" />
      <created>1741435646841</created>
      <option name="number" value="00105" />
      <option name="presentableId" value="LOCAL-00105" />
      <option name="project" value="LOCAL" />
      <updated>1741435646842</updated>
    </task>
    <task id="LOCAL-00106" summary="update：找回代码">
      <option name="closed" value="true" />
      <created>1742466133466</created>
      <option name="number" value="00106" />
      <option name="presentableId" value="LOCAL-00106" />
      <option name="project" value="LOCAL" />
      <updated>1742466133466</updated>
    </task>
    <task id="LOCAL-00107" summary="update：去除校验">
      <option name="closed" value="true" />
      <created>1742474406663</created>
      <option name="number" value="00107" />
      <option name="presentableId" value="LOCAL-00107" />
      <option name="project" value="LOCAL" />
      <updated>1742474406664</updated>
    </task>
    <task id="LOCAL-00108" summary="update：修改校验">
      <option name="closed" value="true" />
      <created>1742476141067</created>
      <option name="number" value="00108" />
      <option name="presentableId" value="LOCAL-00108" />
      <option name="project" value="LOCAL" />
      <updated>1742476141067</updated>
    </task>
    <task id="LOCAL-00109" summary="feat：黄码港sdk封装">
      <option name="closed" value="true" />
      <created>1748334997213</created>
      <option name="number" value="00109" />
      <option name="presentableId" value="LOCAL-00109" />
      <option name="project" value="LOCAL" />
      <updated>1748334997213</updated>
    </task>
    <task id="LOCAL-00110" summary="feat：黄码港sdk封装">
      <option name="closed" value="true" />
      <created>1748337117720</created>
      <option name="number" value="00110" />
      <option name="presentableId" value="LOCAL-00110" />
      <option name="project" value="LOCAL" />
      <updated>1748337117720</updated>
    </task>
    <task id="LOCAL-00111" summary="fix:除异味">
      <option name="closed" value="true" />
      <created>1748936126962</created>
      <option name="number" value="00111" />
      <option name="presentableId" value="LOCAL-00111" />
      <option name="project" value="LOCAL" />
      <updated>1748936126962</updated>
    </task>
    <task id="LOCAL-00112" summary="fix:除异味">
      <option name="closed" value="true" />
      <created>1748937875259</created>
      <option name="number" value="00112" />
      <option name="presentableId" value="LOCAL-00112" />
      <option name="project" value="LOCAL" />
      <updated>1748937875259</updated>
    </task>
    <task id="LOCAL-00113" summary="fix:文件表名称长度修改">
      <option name="closed" value="true" />
      <created>1749434498119</created>
      <option name="number" value="00113" />
      <option name="presentableId" value="LOCAL-00113" />
      <option name="project" value="LOCAL" />
      <updated>1749434498119</updated>
    </task>
    <task id="LOCAL-00114" summary="fix:修改导入">
      <option name="closed" value="true" />
      <created>1752136992255</created>
      <option name="number" value="00114" />
      <option name="presentableId" value="LOCAL-00114" />
      <option name="project" value="LOCAL" />
      <updated>1752136992256</updated>
    </task>
    <option name="localTasksCounter" value="115" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.enterprise:jakarta.enterprise.cdi-api" />
    <option featureType="dependencySupport" implementationName="java:org.jboss.weld:weld-core-impl" />
    <option featureType="dependencySupport" implementationName="java:org.apache.dubbo:dubbo" />
    <option featureType="dependencySupport" implementationName="java:javax.ws.rs:javax.ws.rs-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:io.micronaut:micronaut-core" />
    <option featureType="dependencySupport" implementationName="java:javax.validation:validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="java:jakarta.persistence:jakarta.persistence-api" />
    <option featureType="dependencySupport" implementationName="java:io.grpc:grpc-api" />
    <option featureType="dependencySupport" implementationName="java:com.vaadin:flow-server" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.security:spring-security-core" />
    <option featureType="dependencySupport" implementationName="java:org.jboss.resteasy:resteasy-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:javax.persistence:javax.persistence-api" />
    <option featureType="dependencySupport" implementationName="java:javax.enterprise:cdi-api" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:io.reactivex.rxjava2:rxjava" />
    <option featureType="dependencySupport" implementationName="java:com.github.tomakehurst:wiremock" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/staging-2.28.0" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/dev-2.11.0" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/1.0.0" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/dev-1.2.0" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/dev-2.28.0" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/staging-2.8.0" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/main" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/staging-2.26.0" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/dev-2.26.0_app" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/dev-2.26.0" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="Paths">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:/usr/local/coding/workspace/zhihao/zhihaoscm-server" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:/usr/local/coding/workspace/zhihao/zhihaoscm-starter" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:/usr/local/coding/workspace/zhihao/supplier-service" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:/usr/local/coding/workspace/zhihao/app" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:/usr/local/coding/workspace/zhihao/zhihao-gateway" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:/usr/local/coding/workspace/zhihao/zhihaoscm-transport" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="wuhan" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="wzh" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="weizixuan" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="xujing" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="weizhipeng" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="pengpai" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="huangchanglong" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="zouxin" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="homejim" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/staging-2.28.0" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:/usr/local/coding/workspace/zhihao/zhihaoscm-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="update:删除错误文件" />
    <MESSAGE value="update:暂时去除灰度配置" />
    <MESSAGE value="update:数据变更标准aop处理封装" />
    <MESSAGE value="update:去除自定义包扫描路径" />
    <MESSAGE value="update:除异味,历史记录标准实现封装" />
    <MESSAGE value="update:测试热部署" />
    <MESSAGE value="update:生产环境增加灰度依赖" />
    <MESSAGE value="update:消费模式改为广播模式" />
    <MESSAGE value="update:除异味" />
    <MESSAGE value="fix:修复导包问题" />
    <MESSAGE value="fix:用户中心日志配置添加" />
    <MESSAGE value="fix:去除角色传参" />
    <MESSAGE value="fix:修复导包" />
    <MESSAGE value="fix:修复校验问题" />
    <MESSAGE value="feat：新增开发注意事项" />
    <MESSAGE value="update：优化app登陆处理" />
    <MESSAGE value="update：跳过测试" />
    <MESSAGE value="update：修正跳转" />
    <MESSAGE value="update：找回代码" />
    <MESSAGE value="update：去除校验" />
    <MESSAGE value="update：修改校验" />
    <MESSAGE value="feat：黄码港sdk封装" />
    <MESSAGE value="fix:除异味" />
    <MESSAGE value="fix:文件表名称长度修改" />
    <MESSAGE value="fix:修改导入" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:修改导入" />
  </component>
  <component name="VgoProject">
    <integration-enabled>false</integration-enabled>
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/zhihaoscm-starter/zhihao-sdk/wx-miniapp-sdk/src/test/java/com/zhihaoscm/common/sdk/wx/miniapp/api/api/AccessTokenClientTest.java</url>
          <line>45</line>
          <option name="timeStamp" value="66" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/HotswapAgent/plugin/hotswap-agent-spring-plugin/src/main/java/org/hotswap/agent/plugin/spring/files/XmlBeanDefinitionScannerAgent.java</url>
          <line>88</line>
          <option name="timeStamp" value="86" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jrt:///Library/Java/JavaVirtualMachines/jbr_jcef-17.0.12-osx-aarch64-b1000.54/Contents/Home!/java.xml/com/sun/org/apache/xerces/internal/impl/dv/util/Base64.class</url>
          <line>109</line>
          <option name="timeStamp" value="94" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/zhihaoscm-server/zhihaoscm-service/src/main/java/com/zhihaoscm/service/core/service/impl/ShipRouteServiceImpl.java</url>
          <line>196</line>
          <option name="timeStamp" value="227" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/HotswapAgent/plugin/hotswap-agent-tomcat-plugin/src/main/java/org/hotswap/agent/plugin/tomcat/TomcatPlugin.java</url>
          <line>170</line>
          <option name="timeStamp" value="242" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$PROJECT_DIR$/../../repo/org/springframework/spring-beans/6.0.2/spring-beans-6.0.2-sources.jar!/org/springframework/beans/factory/support/AbstractAutowireCapableBeanFactory.java</url>
          <line>1299</line>
          <option name="timeStamp" value="246" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/zhihaoscm-server/zhihaoscm-custom-api/src/main/java/com/zhihaoscm/custom/api/config/security/wx/WxAuthenticationProvider.java</url>
          <line>83</line>
          <option name="timeStamp" value="263" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$PROJECT_DIR$/../../repo/net/bytebuddy/byte-buddy/1.12.13/byte-buddy-1.12.13-sources.jar!/net/bytebuddy/agent/builder/AgentBuilder.java</url>
          <line>12317</line>
          <option name="timeStamp" value="272" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$PROJECT_DIR$/../../repo/net/bytebuddy/byte-buddy/1.12.13/byte-buddy-1.12.13-sources.jar!/net/bytebuddy/agent/builder/AgentBuilder.java</url>
          <line>11835</line>
          <option name="timeStamp" value="273" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <condition expression="metadata.getClassName().equals(&quot;com.zhihaoscm.common.gray.GrayscaleGlobalAutoConfiguration&quot;)" language="JAVA" />
          <url>jar://$PROJECT_DIR$/../../repo/org/springframework/boot/spring-boot/3.0.2/spring-boot-3.0.2-sources.jar!/org/springframework/boot/context/properties/EnableConfigurationPropertiesRegistrar.java</url>
          <line>48</line>
          <option name="timeStamp" value="325" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/zhihaoscm-server/zhihaoscm-domain/src/main/java/com/zhihaoscm/domain/meta/biz/TransportOrderShipDef.java</url>
          <line>473</line>
          <option name="timeStamp" value="326" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>jar://$PROJECT_DIR$/../../repo/org/springframework/cloud/spring-cloud-gateway-server/4.0.0/spring-cloud-gateway-server-4.0.0-sources.jar!/org/springframework/cloud/gateway/filter/NettyRoutingFilter.java</url>
          <line>188</line>
          <option name="timeStamp" value="336" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/zhihaoscm-server/zhihaoscm-service/src/main/java/com/zhihaoscm/service/core/service/impl/DataBoardServiceImpl.java</url>
          <line>420</line>
          <option name="timeStamp" value="339" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$PROJECT_DIR$/../../repo/org/springframework/spring-websocket/6.0.4/spring-websocket-6.0.4-sources.jar!/org/springframework/web/socket/client/WebSocketConnectionManager.java</url>
          <line>55</line>
          <option name="timeStamp" value="346" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/zhihaoscm-user-center/src/main/java/com/zhihaoscm/usercenter/config/security/admin/wxw/WxwAuthenticationProvider.java</url>
          <line>31</line>
          <option name="timeStamp" value="347" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>jar://$PROJECT_DIR$/../../repo/org/apache/rocketmq/rocketmq-spring-boot/2.2.3/rocketmq-spring-boot-2.2.3-sources.jar!/org/apache/rocketmq/spring/support/DefaultRocketMQListenerContainer.java</url>
          <line>443</line>
          <option name="timeStamp" value="353" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/supplier-service/supplier-ws/src/main/java/com/zhihaoscm/ws/mq/consumer/CustomUserChangeConsumer.java</url>
          <line>28</line>
          <option name="timeStamp" value="354" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/zhihaoscm-server/zhihaoscm-service/src/main/java/com/zhihaoscm/service/core/processor/shipping/ShippingReceiptProcessorImpl.java</url>
          <line>583</line>
          <option name="timeStamp" value="360" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>jar://$PROJECT_DIR$/../../repo/dev/langchain4j/langchain4j-community-dashscope/1.0.0-alpha1/langchain4j-community-dashscope-1.0.0-alpha1.jar!/dev/langchain4j/community/model/dashscope/QwenChatModel.class</url>
          <line>137</line>
          <option name="timeStamp" value="363" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>jar://$PROJECT_DIR$/../../repo/com/alibaba/dashscope-sdk-java/2.14.5/dashscope-sdk-java-2.14.5-sources.jar!/com/alibaba/dashscope/aigc/multimodalconversation/MultiModalConversationResult.java</url>
          <line>18</line>
          <option name="timeStamp" value="364" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>jar://$PROJECT_DIR$/../../repo/com/alibaba/dashscope-sdk-java/2.14.5/dashscope-sdk-java-2.14.5-sources.jar!/com/alibaba/dashscope/protocol/okhttp/OkHttpHttpClient.java</url>
          <line>224</line>
          <option name="timeStamp" value="365" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/zhihaoscm-starter/zhihao-sdk/coze-sdk/src/main/java/com/zhihaoscm/coze/core/CozeSDK.java</url>
          <line>119</line>
          <option name="timeStamp" value="366" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/supplier-service/supplier-service/src/main/java/com/zhihaoscm/service/config/security/encrypt/EncryptFilter.java</url>
          <line>35</line>
          <option name="timeStamp" value="370" />
        </line-breakpoint>
        <breakpoint type="java-exception">
          <properties class="java.lang.ExceptionInInitializerError" package="java.lang" />
          <option name="timeStamp" value="109" />
        </breakpoint>
      </breakpoints>
      <breakpoints-defaults>
        <breakpoint suspend="THREAD" type="java-line" />
      </breakpoints-defaults>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor" memberName="requiredParameterName" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>