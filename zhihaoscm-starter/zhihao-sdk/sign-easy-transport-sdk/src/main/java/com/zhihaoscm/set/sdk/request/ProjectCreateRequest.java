package com.zhihaoscm.set.sdk.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectCreateRequest {

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 凭证要求：
     * RECEIPT 回单
     * WEIGHT_LIST 装卸货磅单
     * PHOTO_RECEIPT 装卸货照片+回单
     */
    private String certificateType;

}
