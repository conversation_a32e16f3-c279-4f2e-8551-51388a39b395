package com.zhihaoscm.set.sdk.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CarBossUnbindResult {

    /**
     * 车老板用户 ID
     */
    private String carBossUserId;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 结果状态码
     */
    private String resultCode;

    /**
     * 结果状态描述
     */
    private String resultMsg;

}
