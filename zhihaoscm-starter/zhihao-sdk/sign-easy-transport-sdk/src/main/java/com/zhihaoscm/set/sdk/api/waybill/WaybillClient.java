package com.zhihaoscm.set.sdk.api.waybill;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.zhihaoscm.common.feign.anno.FeignLog;
import com.zhihaoscm.set.sdk.config.FeignSupportConfig;
import com.zhihaoscm.set.sdk.config.SignSupportConfig;
import com.zhihaoscm.set.sdk.request.*;
import com.zhihaoscm.set.sdk.response.*;

@FeignClient(name = "waybillClient", url = "${sign-easy-transport.url}", configuration = {
		FeignSupportConfig.class, SignSupportConfig.class })
public interface WaybillClient {

	/**
	 * 新增汽运单
	 */
	@FeignLog(response = false)
	@PostMapping(value = "api-server/openapi/v3/waybill/car/create", consumes = MediaType.APPLICATION_JSON_VALUE)
	WaybillCarCreateResponse waybillCarCreate(
			@RequestBody WaybillCarCreateRequest request);

	/**
	 * 汽运单新增-经纪人
	 */
	@FeignLog(response = false)
	@PostMapping(value = "api-server/openapi/v3/waybill/car/carrier/create", consumes = MediaType.APPLICATION_JSON_VALUE)
	WaybillCarCreateResponse waybillCarCreateCarrier(
			@RequestBody WaybillCarCarrierCreateRequest request);

	/**
	 * 运单信息修改
	 */
	@FeignLog(response = false)
	@PostMapping(value = "api-server/openapi/v3/waybill/edit", consumes = MediaType.APPLICATION_JSON_VALUE)
	WaybillCarCreateResponse waybillEdit(
			@RequestBody WaybillEditRequest request);

	/**
	 * 运单信息修改-经纪人
	 */
	@FeignLog(response = false)
	@PostMapping(value = "api-server/openapi/v3/waybill/carrier/edit", consumes = MediaType.APPLICATION_JSON_VALUE)
	WaybillCarCreateResponse waybillCarrierEdit(
			@RequestBody WaybillCarrierEditRequest request);

	/**
	 * 运单信息查询
	 */
	@FeignLog(response = false)
	@PostMapping(value = "api-server/openapi/v3/waybill/detail", consumes = MediaType.APPLICATION_JSON_VALUE)
	WaybillDetailResponse waybillDetail(
			@RequestBody WaybillDetailRequest request);

	/**
	 * 运单作废
	 */
	@FeignLog(response = false)
	@PostMapping(value = "api-server/openapi/v3/waybill/cancel", consumes = MediaType.APPLICATION_JSON_VALUE)
	BaseResponse waybillCancel(@RequestBody WaybillDetailRequest request);

	/**
	 * 接单合同获取
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/waybill/contract/sign/create", consumes = MediaType.APPLICATION_JSON_VALUE)
	ProjectCreateResponse waybillContractSignCreate(
			@RequestBody WaybillDetailRequest request);

	/**
	 * 运单合同状态查询
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/waybill/contract/detail", consumes = MediaType.APPLICATION_JSON_VALUE)
	WaybillContractDetailResponse waybillContractDetail(
			@RequestBody WaybillDetailRequest request);

	/**
	 * 运单起运
	 */
	@FeignLog(response = false)
	@PostMapping(value = "api-server/openapi/v3/waybill/pickgoods", consumes = MediaType.APPLICATION_JSON_VALUE)
	WaybillPickGoodsResponse waybillPickGoods(
			@RequestBody WaybillPickGoodsRequest request);

	/**
	 * 运单运抵
	 */
	@FeignLog(response = false)
	@PostMapping(value = "api-server/openapi/v3/waybill/arrive", consumes = MediaType.APPLICATION_JSON_VALUE)
	WaybillPickGoodsResponse waybillArrive(
			@RequestBody WaybillPickGoodsRequest request);

	/**
	 * 运单凭证上传
	 */
	@FeignLog(response = false)
	@PostMapping(value = "api-server/openapi/v3/waybill/receipts/upload", consumes = MediaType.APPLICATION_JSON_VALUE)
	BaseResponse waybillReceiptsUpload(
			@RequestBody WaybillReceiptsUploadRequest request);

	/**
	 * 轨迹查询
	 */
	@FeignLog(response = false)
	@PostMapping(value = "api-server/openapi/v3/waybill/track/detail", consumes = MediaType.APPLICATION_JSON_VALUE)
	WaybillTrackDetailResponse waybillTrackDetail(
			@RequestBody WaybillDetailRequest request);

}
