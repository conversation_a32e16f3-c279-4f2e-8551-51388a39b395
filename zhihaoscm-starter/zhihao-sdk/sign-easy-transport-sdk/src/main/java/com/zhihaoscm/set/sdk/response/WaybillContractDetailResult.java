package com.zhihaoscm.set.sdk.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WaybillContractDetailResult {

    /** 合同乙方名称 (必填) 示例值: 张三公司 */
    public String contractSecondPartyName;

    /** 合同甲方名称 (必填) 示例值: 湖北我家物流服务有限公司 */
    public String contractFirstPartyName;

    /** 是否完成签署 (必填) 类型: boolean 示例值: false */
    public boolean isFinishSign;

    /** 合同链接 (必填) */
    public String contractUrl;

}
