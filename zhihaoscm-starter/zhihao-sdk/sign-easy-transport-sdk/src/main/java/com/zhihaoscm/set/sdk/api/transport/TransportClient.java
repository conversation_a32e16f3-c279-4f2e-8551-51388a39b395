package com.zhihaoscm.set.sdk.api.transport;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.zhihaoscm.common.feign.anno.FeignLog;
import com.zhihaoscm.set.sdk.config.FeignSupportConfig;
import com.zhihaoscm.set.sdk.config.SignSupportConfig;
import com.zhihaoscm.set.sdk.request.*;
import com.zhihaoscm.set.sdk.response.*;

@FeignClient(name = "transportClient", url = "${sign-easy-transport.url}", configuration = {
		FeignSupportConfig.class, SignSupportConfig.class })
public interface TransportClient {

	/**
	 * 车老板新增
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/carboss/create", consumes = MediaType.APPLICATION_JSON_VALUE)
	CarBossCreateResponse carBossCreate(
			@RequestBody CarBossCreateRequest request);

	/**
	 * 车老板绑定车辆
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/carboss/bind", consumes = MediaType.APPLICATION_JSON_VALUE)
	CarBossBindResponse carBossBind(@RequestBody CarBossBindRequest request);

	/**
	 * 车老板解绑车辆
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/carboss/unbind", consumes = MediaType.APPLICATION_JSON_VALUE)
	CarBossUnbindResponse carBossUnbind(
			@RequestBody CarBossUnbindRequest request);

	/**
	 * 车老板查询
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/carboss/detail", consumes = MediaType.APPLICATION_JSON_VALUE)
	CarBossDetailResponse carBossDetail(
			@RequestBody CarBossDetailRequest request);

	/**
	 * 汽运运力注册
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/vehicle/car/create", consumes = MediaType.APPLICATION_JSON_VALUE)
	CarCreateResponse carCreate(@RequestBody CarCreateRequest request);

	/**
	 * 汽运运力认证
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/vehicle/car/auth", consumes = MediaType.APPLICATION_JSON_VALUE)
	BaseResponse carAuth(@RequestBody CarAuthRequest request);

	/**
	 * 汽运运力认证状态查询
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/vehicle/car/authstatusdetail", consumes = MediaType.APPLICATION_JSON_VALUE)
	VehicleAuthStatusDetailResponse carAuthStatusDetail(
			@RequestBody VehicleAuthStatusDetailRequest request);

}
