package com.zhihaoscm.set.sdk.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zhihaoscm.set.sdk.item.CarDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CarBossDetailResult {

    /**
     * 车老板姓名
     */
    private String bossName;

    /**
     * 车老板身份证号码
     */
    private String idCardNo;

    /**
     * 车老板绑定的车辆列表
     */
    private List<CarDetail> carList;

}
