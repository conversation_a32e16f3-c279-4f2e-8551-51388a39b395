package com.zhihaoscm.set.sdk.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zhihaoscm.set.sdk.item.UploadFileInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WaybillReceiptsUploadRequest {

    /**
     * 运单 code
     */
    private String waybillCode;

    /**
     * 文件对象集
     */
    private List<UploadFileInfo> uploadFileInfoDtoList;

}
