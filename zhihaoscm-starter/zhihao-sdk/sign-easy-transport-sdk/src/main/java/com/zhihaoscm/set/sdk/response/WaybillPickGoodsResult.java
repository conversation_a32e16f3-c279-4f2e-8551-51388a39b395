package com.zhihaoscm.set.sdk.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WaybillPickGoodsResult {

    /**
     * 轨迹类型 参考 2.3.2
     * (不影响起运)
     */
    private String trackType;

    /**
     * 轨迹类型名称
     */
    private String trackTypeText;

}
