package com.zhihaoscm.set.sdk.api.project;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.zhihaoscm.common.feign.anno.FeignLog;
import com.zhihaoscm.set.sdk.config.FeignSupportConfig;
import com.zhihaoscm.set.sdk.config.SignSupportConfig;
import com.zhihaoscm.set.sdk.request.ProjectCreateRequest;
import com.zhihaoscm.set.sdk.request.ProjectGetCodeRequest;
import com.zhihaoscm.set.sdk.request.ProjectListRequest;
import com.zhihaoscm.set.sdk.response.ProjectCreateResponse;
import com.zhihaoscm.set.sdk.response.ProjectGetCodeResponse;
import com.zhihaoscm.set.sdk.response.ProjectListResponse;

@FeignClient(name = "projectClient", url = "${sign-easy-transport.url}", configuration = {
		FeignSupportConfig.class, SignSupportConfig.class })
public interface ProjectClient {

	/**
	 * 项目新增
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/project/create", consumes = MediaType.APPLICATION_JSON_VALUE)
	ProjectCreateResponse create(@RequestBody ProjectCreateRequest request);

	/**
	 * 项目信息查询
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/project/list", consumes = MediaType.APPLICATION_JSON_VALUE)
	ProjectListResponse list(@RequestBody ProjectListRequest request);

	/**
	 * 项目编码查询
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/project/getcode", consumes = MediaType.APPLICATION_JSON_VALUE)
	ProjectGetCodeResponse getCode(@RequestBody ProjectGetCodeRequest request);

}
