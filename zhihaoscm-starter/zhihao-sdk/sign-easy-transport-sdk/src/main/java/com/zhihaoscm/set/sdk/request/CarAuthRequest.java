package com.zhihaoscm.set.sdk.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CarAuthRequest {

    /**
     * 司机姓名（必填）
     */
    private String driverName;

    /**
     * 司机手机号（必填）
     */
    private String phone;

    /**
     * 身份证号（必填）
     */
    private String idCardNo;

    /**
     * 身份证国徽面图片 URL（必填）
     */
    private String idCardFrontImg;

    /**
     * 身份证人像面图片 URL（必填）
     */
    private String idCardBackImg;

    /**
     * 驾驶证图片 URL（必填）
     */
    private String drivingImg;

    /**
     * 驾驶证副本图片 URL（必填）
     */
    private String drivingCopyImg;

    /**
     * 从业资格证图片 URL
     * 条件必填：当车型包含“重型”或“中型”字样时，必须上传
     */
    private String qualificationCardImg;

    /**
     * 车牌号（必填）
     */
    private String plateNo;

    /**
     * 车辆类型（必填），参考数据字典
     */
    private Integer carTypesOf;

    /**
     * 挂车牌照号
     * 条件必填：当车型包含“挂”字时，必须填写
     */
    private String trailerNo;

    /**
     * 车牌颜色（必填）
     * 枚举值：BLUE(蓝色), YELLOW(黄色), GRADIENT_GREEN(渐变绿色), CHARTREUSE(黄绿色)
     */
    private String plateColor;

    /**
     * 行驶证主页图片 URL（必填）
     */
    private String carDrivingFrontImg;

    /**
     * 行驶证副页图片 URL（必填）
     */
    private String carDrivingCopyImg;

    /**
     * 行驶证过期补充照片 URL（可选）
     */
    private String carDrivingCopyAddImg;

    /**
     * 挂车行驶证主页图片 URL
     * 条件必填：当车型包含“挂”字时，必须上传
     */
    private String trailerCarDrivingFrontImg;

    /**
     * 挂车行驶证副页图片 URL
     * 条件必填：当车型包含“挂”字时，必须上传
     */
    private String trailerCarDrivingCopyImg;

    /**
     * 道路运输证图片 URL
     * 条件必填：当车型包含“重型”或“中型”字样时，必须上传
     */
    private String roadTransportPermImg;

}
