package com.zhihaoscm.set.sdk.item;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CarDetail {

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 审核备注(不通过原因)
     */
    private String auditRemark;

}
