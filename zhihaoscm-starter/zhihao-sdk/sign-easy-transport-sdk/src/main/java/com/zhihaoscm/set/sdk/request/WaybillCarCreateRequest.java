package com.zhihaoscm.set.sdk.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WaybillCarCreateRequest {

    /** 项目编码 (必填) 示例值: PJ2024112100001 */
    public String projectCode;

    /** 客户运单号 (必填) 示例值: 2411272224000001 */
    public String channelWaybillCode;

    /** 上游单号 (非必填) 示例值: HS2024112100001 */
    public String upstreamCode;

    /** 三方创建时间 (必填) 格式: yyyy-MM-dd HH:mm:ss 示例值: 2024-12-13 13:00:00 */
    public String threeCreateTime;

    /** 运力 ID (必填) 示例值: 1000 */
    public Long vehicleId;

    /** 驾驶人手机号 (必填) 示例值: 15966661234 */
    public String driverPhone;

    /** 发货省 (必填) 示例值: 湖北省 */
    public String sendProvince;

    /** 发货市 (必填) 示例值: 武汉市 */
    public String sendCity;

    /** 发货区 (必填) 示例值: 武昌区 */
    public String sendArea;

    /** 发货详细地址 (必填) 示例值: 徐东大街 100 号 */
    public String sendAddress;

    /** 发货经度 (非必填) 类型: BigDecimal 示例值: 116.4523 */
    public BigDecimal sendLng;

    /** 发货纬度 (非必填) 类型: BigDecimal 示例值: 39.8824 */
    public BigDecimal sendLat;

    /** 收货省 (必填) 示例值: 上海市 */
    public String receiveProvince;

    /** 收货市 (必填) 示例值: 上海市 */
    public String receiveCity;

    /** 收货区 (必填) 示例值: 浦东新区 */
    public String receiveArea;

    /** 收货详细地址 (必填) 示例值: 川沙新镇上海迪士尼度假区 */
    public String receiveAddress;

    /** 收货经度 (非必填) 类型: BigDecimal 示例值: 106.4523 */
    public BigDecimal receiveLng;

    /** 收货纬度 (非必填) 类型: BigDecimal 示例值: 35.8824 */
    public BigDecimal receiveLat;

    /** 货物名称 (必填) 示例值: 钢材 */
    public String goodsName;

    /** 货物品类 (必填) 示例值: 参考：2.1.7 */
    public Integer goodsCategory;

    /** 货物重量(吨/方/件/车)(三位小数) 示例值: 100 */
    public BigDecimal goodsWeight;

    /** 货物计量单位 (必填) 示例值: TON (参考：2.1.8) */
    public String goodsUnit;

    /** 运输金额(元)(两位小数) 示例值: 2000 */
    public BigDecimal amount;

    /** 回单押金(元)(两位小数) 非必填 示例值: 500 */
    public BigDecimal returnDepositAmount;

    /** 预付款金额(元)(两位小数) 非必填 示例值: 400 */
    public BigDecimal advancePayProportion;

    /** 备注（长度 500） 非必填 示例值: 备注明细 */
    public String remark;

    /** 司机备注（长度 200） 非必填 示例值: 备注明细 */
    public String driverRemark;

}
