package com.zhihaoscm.set.sdk.api.organ;

import com.zhihaoscm.common.feign.anno.FeignLog;
import com.zhihaoscm.set.sdk.config.FeignSupportConfig;
import com.zhihaoscm.set.sdk.config.SignSupportConfig;
import com.zhihaoscm.set.sdk.request.*;
import com.zhihaoscm.set.sdk.response.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "companyClient", url = "${sign-easy-transport.url}", configuration = {
		FeignSupportConfig.class, SignSupportConfig.class })
public interface CompanyClient {

	/**
	 * 企业创建
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/company/create", consumes = MediaType.APPLICATION_JSON_VALUE)
	BaseResponse create(@RequestBody CompanyCreateRequest request);

	/**
	 * 网货合同获取
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/contract/create", consumes = MediaType.APPLICATION_JSON_VALUE)
	ContractCreateResponse contractCreate(
			@RequestBody ContractCreateRequest request);

	/**
	 * 企业信息查询
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/company/detail", consumes = MediaType.APPLICATION_JSON_VALUE)
	CompanyDetailResponse detail(@RequestBody CompanyDetailRequest request);

	/**
	 * 企业信息修改
	 */
	@FeignLog(response = false)
	@PostMapping(value = "/api-server/openapi/v3/company/edit", consumes = MediaType.APPLICATION_JSON_VALUE)
	BaseResponse edit(@RequestBody CompanyEditRequest request);

}
