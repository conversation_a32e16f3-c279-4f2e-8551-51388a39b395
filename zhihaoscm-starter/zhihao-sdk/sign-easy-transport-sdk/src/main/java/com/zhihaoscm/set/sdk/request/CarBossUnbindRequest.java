package com.zhihaoscm.set.sdk.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CarBossUnbindRequest {

    /**
     * 车老板用户id
     */
    private String userId;

    /**
     * 车牌号集合
     */
    private List<String> plateNoList;

}
