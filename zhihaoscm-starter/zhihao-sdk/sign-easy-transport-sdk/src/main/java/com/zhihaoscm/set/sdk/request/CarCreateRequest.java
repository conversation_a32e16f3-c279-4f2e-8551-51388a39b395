package com.zhihaoscm.set.sdk.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CarCreateRequest {

    /**
     * 司机姓名（必填）
     * 仅支持中文、英文字母
     * 示例值：张三
     */
    private String driverName;

    /**
     * 司机手机号（必填）
     * 示例值：19011112222
     */
    private String phone;

    /**
     * 身份证号（必填）
     * 示例值：******************
     */
    private String idCardNo;

    /**
     * 车牌号（必填）
     * 示例值：鄂 A52J6P
     */
    private String plateNo;

}
