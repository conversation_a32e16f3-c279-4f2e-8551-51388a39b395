package com.zhihaoscm.set.sdk.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zhihaoscm.set.sdk.item.Track;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WaybillTrackDetailResult {

    /**
     * 总条数
     */
    private String total;

    /**
     * 轨迹集
     */
    private List<Track> trackList;

}
