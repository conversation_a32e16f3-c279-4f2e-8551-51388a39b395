package com.zhihaoscm.set.sdk.api.organ;

import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.set.sdk.config.SignEasyTransportProperties;
import com.zhihaoscm.set.sdk.request.*;
import com.zhihaoscm.set.sdk.response.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;

@SpringBootTest
public class CompanyTest {

	private static final String TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJvcGVuVXNlcklkIjoxMjcsImdyYW50VHlwZSI6ImNsaWVudF9jcmVkZW50aWFscyIsInRpbWVzdGFtcCI6MTc1MzI1ODU3MjA1N30.XgRw3stTF2TYLAHqHLBuXw8mCmRwV1ShJ-B3h_uBbIo";

	@Autowired
	private CompanyClient companyClient;

	@Test
	@DisplayName(value = "企业创建")
	public void create() {
		SignEasyTransportProperties.AppInfoHolder.setAppInfo(TOKEN, null);
		CompanyCreateRequest request = new CompanyCreateRequest();
		request.setCompanyName("上海大风技术有限公司");
		request.setUnifiedCreditCode("91310110MA1G8YJ86T");
		request.setBusinessLicenseUrl(
				"https://img.56yzm.com/dev/image/2025/7/961193bd1738171fcbf4ac39fa767fca.jpg");
		request.setAdminName("刘赛赛");
		request.setAdminPhone("***********");
		request.setBusinessContact("余晓峰");
		request.setBusinessContactTel("***********");
		request.setFinanceContact("余晓峰");
		request.setFinanceContactTel("***********");
		request.setRegisterAddress("上海");
		request.setRegisterTelephone("***********");
		request.setBankName("中国工商银行");
		request.setBankAccount("*********");
		request.setSendAddress("上海");
		request.setReceiveName("余晓峰");
		request.setReceivePhone("***********");
		request.setEmail("<EMAIL>");
		System.out.println(JsonUtils.objectToJson(request));
		BaseResponse baseResponse = companyClient.create(request);
		System.out.println(baseResponse);
	}

	@Test
	@DisplayName(value = "网货合同获取")
	public void contractCreate() {
		SignEasyTransportProperties.AppInfoHolder.setAppInfo(TOKEN, "YZM_cL2IZrMQwM");
		ContractCreateRequest request = new ContractCreateRequest();
		request.setSettlementNo("CPF000003");
		request.setTransportType("CAR");
		request.setContractRate(new BigDecimal("6.50"));
		request.setContractStartDate("2025-07-01");
		request.setContractEndDate("2026-07-31");
		System.out.println(JsonUtils.objectToJson(request));
		ContractCreateResponse baseResponse = companyClient
				.contractCreate(request);
		System.out.println(baseResponse);
	}

	@Test
	@DisplayName(value = "企业详情")
	public void detail() {
		SignEasyTransportProperties.AppInfoHolder.setAppInfo(TOKEN, "YZM_cL2IZrMQwM");
		CompanyDetailRequest request = new CompanyDetailRequest();
		request.setCompanyName("湖北省志豪合顺船务有限公司");
		CompanyDetailResponse baseResponse = companyClient.detail(request);
		System.out.println(baseResponse);
	}

	@Test
	@DisplayName(value = "企业信息修改")
	public void edit() {
		SignEasyTransportProperties.AppInfoHolder.setAppInfo(TOKEN, "YZM_cL2IZrMQwM");
		CompanyEditRequest request = new CompanyEditRequest();
		request.setCompanyName("湖北省志豪合顺船务有限公司");
		request.setBusinessContact("徐昊鸿");
		request.setBusinessContactTel("***********");
		request.setFinanceContact("徐昊鸿");
		request.setFinanceContactTel("***********");
		request.setReceiveName("徐昊鸿");
		request.setReceivePhone("***********");
		BaseResponse edit = companyClient.edit(request);
		System.out.println(edit);
	}

}
