package com.zhihaoscm.set.sdk.api.file;

import lombok.SneakyThrows;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;

@SpringBootTest
public class FileTest {

	@Autowired
	private SetFileClient setFileClient;

	@Test
	@DisplayName(value = "上传文件")
	public void uploadFile() {
		String filePath = "D:/dev/test_picture/transport_set/刘得雄_司机身份证人像面.jpg";
		String result = setFileClient.uploadFile(convert(filePath));
		System.out.println(result);
	}

	@SneakyThrows
	private MultipartFile convert(String filePath) {
		File file = new File(filePath);
		FileInputStream fileInputStream = new FileInputStream(file);
		return new MockMultipartFile("file", file.getName(), "image/jpeg",
				fileInputStream);
	}

}
