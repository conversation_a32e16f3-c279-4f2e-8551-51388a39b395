package com.zhihaoscm.set.sdk.api.transport;

import com.zhihaoscm.set.sdk.config.SignEasyTransportProperties;
import com.zhihaoscm.set.sdk.request.CarAuthRequest;
import com.zhihaoscm.set.sdk.request.CarBossCreateRequest;
import com.zhihaoscm.set.sdk.request.CarBossDetailRequest;
import com.zhihaoscm.set.sdk.request.CarCreateRequest;
import com.zhihaoscm.set.sdk.response.BaseResponse;
import com.zhihaoscm.set.sdk.response.CarBossCreateResponse;
import com.zhihaoscm.set.sdk.response.CarBossDetailResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class TransportTest {

    private static final String TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJvcGVuVXNlcklkIjoxMjcsImdyYW50VHlwZSI6ImNsaWVudF9jcmVkZW50aWFscyIsInRpbWVzdGFtcCI6MTc1Mzg2MzUwMjcyNX0.HNMeR5rXUSqO8rSiDDB9lC-c7A_Ll_D3WziqUOZtBVA";

    @Autowired
    private TransportClient transportClient;

    @Test
    @DisplayName(value = "车老板新增")
    public void carBossCreate() {
        SignEasyTransportProperties.AppInfoHolder.setAppInfo(TOKEN, "YZM_cL2IZrMQwM");
        CarBossCreateRequest request = new CarBossCreateRequest();
        request.setBossName("徐昊鸿");
        request.setIdCardNo("330123199012123456");
        request.setPhone("17786652678");
        request.setIdCardFrontImg("https://img.56yzm.com/dev/image/2025/7/31c890a86857682065da5d3ddbf97aa5.jpg");
        request.setIdCardBackImg("https://img.56yzm.com/dev/image/2025/7/65d2ab9c7e7aba7c6f5a2286166c7f88.jpg");
        CarBossCreateResponse response = transportClient.carBossCreate(request);
        System.out.println(response);
    }

    @Test
    @DisplayName(value = "车老板查询")
    public void carBossDetail() {
        SignEasyTransportProperties.AppInfoHolder.setAppInfo(TOKEN, "YZM_cL2IZrMQwM");
        CarBossDetailRequest request = new CarBossDetailRequest();
        request.setUserId("b4b86ad54146f31e28d733c2fe91e75c");
        CarBossDetailResponse response = transportClient.carBossDetail(request);
        System.out.println(response);
    }

    @Test
    @DisplayName(value = "汽运运力注册")
    public void carCreate() {
        SignEasyTransportProperties.AppInfoHolder.setAppInfo(TOKEN, "YZM_cL2IZrMQwM");
        CarCreateRequest request = new CarCreateRequest();
        request.setDriverName("朱泽豪");
        request.setPhone("18907113097");
        request.setIdCardNo("******************");
        request.setPlateNo("川A12345");
        System.out.println(transportClient.carCreate(request));
    }

    @Test
    @DisplayName(value = "汽运运力认证")
    public void carAuth() {
        SignEasyTransportProperties.AppInfoHolder.setAppInfo(TOKEN, "YZM_cL2IZrMQwM");
        CarAuthRequest request = new CarAuthRequest();
        request.setDriverName("刘得雄");
        request.setPhone("15072601498");
        request.setIdCardNo("42098419711118441X");
        request.setIdCardFrontImg("https://img.56yzm.com/dev/image/2025/7/7c99736b44554791159b1abc70ecaf4e.jpg");
        request.setIdCardBackImg("https://img.56yzm.com/dev/image/2025/7/550adda0789f7cd69c701a721c3b5946.jpg");
        request.setDrivingImg("https://img.56yzm.com/dev/image/2025/7/be13882d20632b7a6e1b2bb4f6645fc5.jpg");
        request.setDrivingCopyImg("https://img.56yzm.com/dev/image/2025/7/be13882d20632b7a6e1b2bb4f6645fc5.jpg");
        request.setQualificationCardImg(null);
        request.setPlateNo("鄂J12699");
        request.setCarTypesOf(4);
        request.setTrailerNo("鄂J7218挂");
        request.setPlateColor("YELLOW");
        request.setCarDrivingFrontImg("https://img.56yzm.com/dev/image/2025/7/8c8a922838d4fe2759f883b48472b389.jpg");
        request.setCarDrivingCopyImg("https://img.56yzm.com/dev/image/2025/7/8c8a922838d4fe2759f883b48472b389.jpg");
        request.setCarDrivingCopyAddImg(null);
        request.setTrailerCarDrivingFrontImg("https://img.56yzm.com/dev/image/2025/7/135648fbd27a76415780ef340c5b77f2.jpg");
        request.setTrailerCarDrivingCopyImg("https://img.56yzm.com/dev/image/2025/7/135648fbd27a76415780ef340c5b77f2.jpg");
        request.setRoadTransportPermImg("https://img.56yzm.com/dev/image/2025/7/673876916700c6e548719c8f98ebf96a.jpg");
        BaseResponse baseResponse = transportClient.carAuth(request);
        System.out.println(baseResponse);
    }

}
