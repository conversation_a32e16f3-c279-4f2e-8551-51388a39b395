package com.zhihaoscm.ws.handler;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.adapter.standard.StandardWebSocketSession;

import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.meta.RedisKeys;

public class QrTokenAuthenticationIntercept
		implements TokenAuthenticationIntercept {

	@Autowired
	private StringRedisClient redisClient;

	@Override
	public Boolean checkToken(WebSocketSession session) {
		List<String> tokens = session.getHandshakeHeaders().get("loginCode");
		StandardWebSocketSession standardWebSocketSession = (StandardWebSocketSession) session;
		List<String> urlTokens = standardWebSocketSession.getNativeSession()
				.getRequestParameterMap().get("loginCode");
		if (CollectionUtils.isEmpty(tokens)
				&& CollectionUtils.isEmpty(urlTokens)) {
			return Boolean.FALSE;
		}
		String token = CollectionUtils.isEmpty(tokens) ? urlTokens.get(0)
				: tokens.get(0);
		if (StringUtils.isBlank(token)) {
			return Boolean.FALSE;
		}
		String loginCode = redisClient.hGet(RedisKeys.Cache.QR_LOGIN + token,
				"loginCode");
		if (StringUtils.isBlank(loginCode)) {
			return Boolean.FALSE;
		}
		QrLoginContextHolder.setLoginCode(loginCode);
		return Boolean.TRUE;
	}
}
