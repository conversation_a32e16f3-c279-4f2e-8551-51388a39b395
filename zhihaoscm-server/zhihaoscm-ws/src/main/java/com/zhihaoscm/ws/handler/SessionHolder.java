package com.zhihaoscm.ws.handler;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.web.socket.WebSocketSession;

public class SessionHolder {

	/**
	 * 管理后台用户的WebSocketSession
	 */
	public static final Map<Long, List<WebSocketSession>> ADMIN_SESSIONS = new ConcurrentHashMap<>();
	public static final Map<WebSocketSession, Long> SESSION_USER = new ConcurrentHashMap<>();

	/**
	 * 客户端用户的webSocketSession
	 */
	public static final Map<Long, List<WebSocketSession>> CUSTOM_SESSIONS = new ConcurrentHashMap<>();
	public static final Map<WebSocketSession, Long> CUSTOM_SESSION_USER = new ConcurrentHashMap<>();

	/**
	 * app扫码登录pc的webSocketSession
	 */
	public static final Map<String, WebSocketSession> QR_LOGIN_SESSION = new ConcurrentHashMap<>();
	public static final Map<WebSocketSession, String> SESSION_QR_LOGIN = new ConcurrentHashMap<>();

}
