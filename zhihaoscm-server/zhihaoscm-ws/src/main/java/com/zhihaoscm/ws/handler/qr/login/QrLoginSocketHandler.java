package com.zhihaoscm.ws.handler.qr.login;

import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;

import com.zhihaoscm.ws.handler.AbstractWebSocketHandler;
import com.zhihaoscm.ws.handler.QrLoginContextHolder;
import com.zhihaoscm.ws.handler.SessionHolder;
import com.zhihaoscm.ws.handler.TokenAuthenticationIntercept;

public class QrLoginSocketHandler extends AbstractWebSocketHandler {

	public QrLoginSocketHandler(TokenAuthenticationIntercept intercept) {
		super(intercept);
	}

	@Override
	protected void handleConnectionEstablished(WebSocketSession session) {
		String loginCode = QrLoginContextHolder.getLoginCode();
		// 维护用户和session的关系
		SessionHolder.QR_LOGIN_SESSION.put(loginCode, session);
		SessionHolder.SESSION_QR_LOGIN.put(session, loginCode);
	}

	@Override
	protected void handleMessageReceived(WebSocketSession session,
			WebSocketMessage<?> message) {
	}

	@Override
	protected void handleConnectionClosed(WebSocketSession session,
			CloseStatus closeStatus) {
		String loginCode = SessionHolder.SESSION_QR_LOGIN.get(session);
		if (loginCode != null) {
			SessionHolder.QR_LOGIN_SESSION.remove(loginCode, session);
			SessionHolder.SESSION_QR_LOGIN.remove(session);
		}
	}
}
