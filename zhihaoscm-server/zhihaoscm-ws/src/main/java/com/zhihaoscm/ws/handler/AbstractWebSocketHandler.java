package com.zhihaoscm.ws.handler;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.socket.*;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.zhihaoscm.common.gray.GrayscaleProperties;
import com.zhihaoscm.common.gray.GrayscaleVersionHolder;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.ws.meta.CommonDef;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractWebSocketHandler implements WebSocketHandler {

	@Autowired(required = false)
	private GrayscaleProperties grayscaleProperties;

	private final TokenAuthenticationIntercept tokenAuthenticationIntercept;

	public AbstractWebSocketHandler(TokenAuthenticationIntercept intercept) {
		this.tokenAuthenticationIntercept = intercept;
	}

	@Override
	public void afterConnectionEstablished(@NotNull WebSocketSession session) {
		try {
			WebSocketHeartbeatMonitor.SESSION_LAST_HEARTBEAT.put(session,
					LocalDateTime.now());
			if (checkTokenAndSendErrorMessageIfInvalid(session)) {
				handleConnectionEstablished(session);
			}
		} finally {
			CustomerContextHolder.clear();
			UserInfoContextHolder.clear();
			QrLoginContextHolder.clear();
		}

	}

	@Override
	public void handleMessage(@NotNull WebSocketSession session,
			@NotNull WebSocketMessage<?> message) {
		// 从 WebSocket 会话中恢复版本信息
		if (Objects.nonNull(grayscaleProperties)) {
			String version = (String) session.getAttributes()
					.get(grayscaleProperties.getVersionKey());
			// 设置到 GrayscaleVersionHolder
			GrayscaleVersionHolder.setVersion(version);
			GrayscaleVersionHolder
					.setHeader(grayscaleProperties.getVersionKey());
		}
		try {
			if (!isHeartbeat(message, session)
					&& checkTokenAndSendErrorMessageIfInvalid(session)) {
				handleMessageReceived(session, message);
			}
		} finally {
			CustomerContextHolder.clear();
			UserInfoContextHolder.clear();
			GrayscaleVersionHolder.clear();
		}

	}

	private boolean isHeartbeat(WebSocketMessage<?> message,
			WebSocketSession session) {
		JsonObject jsonObject = JsonParser
				.parseString((String) message.getPayload()).getAsJsonObject();
		JsonElement heartBeat = jsonObject.get("heartBeat");
		if (Objects.nonNull(heartBeat)
				&& CommonDef.HEART_BEAT.equals(heartBeat.getAsString())) {
			WebSocketHeartbeatMonitor.SESSION_LAST_HEARTBEAT.put(session,
					LocalDateTime.now());
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	private boolean checkTokenAndSendErrorMessageIfInvalid(
			WebSocketSession session) {
		if (!tokenAuthenticationIntercept.checkToken(session)) {
			sendErrorMessage(session,
					CommonDef.ErrorType.INVALID_TOKEN.getCode());
			return false;
		}
		return true;
	}

	@SneakyThrows
	public void sendErrorMessage(WebSocketSession session, Integer errorType) {
		session.sendMessage(new TextMessage(JsonUtils
				.objectToJson(Map.of(CommonDef.ERROR_TYPE, errorType))));
	}

	@Override
	public void handleTransportError(@NotNull WebSocketSession session,
			@NotNull Throwable exception) {
		handleError(session, exception);
		if (session.isOpen()) {
			try {
				session.close(CloseStatus.SERVER_ERROR);
			} catch (IOException e) {
				log.error("AbstractWebSocketHandler close error occurred : {}",
						e.getMessage());
			}
		}
	}

	@Override
	public void afterConnectionClosed(@NotNull WebSocketSession session,
			@NotNull CloseStatus closeStatus) {
		handleConnectionClosed(session, closeStatus);
		WebSocketHeartbeatMonitor.SESSION_LAST_HEARTBEAT.remove(session);
	}

	@Override
	public boolean supportsPartialMessages() {
		return false;
	}

	protected abstract void handleConnectionEstablished(
			WebSocketSession session);

	protected abstract void handleMessageReceived(WebSocketSession session,
			WebSocketMessage<?> message);

	protected void handleError(WebSocketSession session, Throwable exception) {

	}

	protected void handleConnectionClosed(WebSocketSession session,
			CloseStatus closeStatus) {

	}

}
