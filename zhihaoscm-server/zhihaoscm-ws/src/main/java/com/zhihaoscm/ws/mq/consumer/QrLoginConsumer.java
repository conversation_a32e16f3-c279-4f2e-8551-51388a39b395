package com.zhihaoscm.ws.mq.consumer;

import java.util.Objects;

import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.domain.bean.dto.QrLoginDto;
import com.zhihaoscm.domain.meta.biz.TopicDef;
import com.zhihaoscm.ws.handler.SessionHolder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RocketMQMessageListener(topic = TopicDef.QR_LOGIN, consumerGroup = TopicDef.QR_LOGIN_GROUP, messageModel = MessageModel.BROADCASTING)
public class QrLoginConsumer implements RocketMQListener<QrLoginDto> {

	@Override
	public void onMessage(QrLoginDto dto) {
		if (Objects.nonNull(dto)) {
			log.info("收到消息：{}", dto);
			WebSocketSession webSocketSession = SessionHolder.QR_LOGIN_SESSION
					.get(dto.getLoginCode());
			if (Objects.isNull(webSocketSession)) {
				return;
			}
			if (webSocketSession.isOpen()) {
				try {
					webSocketSession.sendMessage(
							new TextMessage(JsonUtils.objectToJson(dto)));
				} catch (Exception e) {
					log.error("qr-login send message error: {}",
							e.getMessage());
				}
			}
		}
	}
}