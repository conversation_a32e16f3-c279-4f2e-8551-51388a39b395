package com.zhihaoscm.ws.handler;

import com.alibaba.ttl.TransmittableThreadLocal;

public class QrLoginContextHolder {

	private static final ThreadLocal<String> LOGIN_CODE_HOLDER = new TransmittableThreadLocal<>();

	public static void setLoginCode(String loginCode) {
		LOGIN_CODE_HOLDER.set(loginCode);
	}

	public static String getLoginCode() {
		return LOGIN_CODE_HOLDER.get();
	}

	public static void clear() {
		LOGIN_CODE_HOLDER.remove();
	}
}
