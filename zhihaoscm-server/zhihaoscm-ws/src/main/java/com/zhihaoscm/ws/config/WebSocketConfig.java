package com.zhihaoscm.ws.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

import com.zhihaoscm.common.gray.GrayscaleProperties;
import com.zhihaoscm.common.redis.cache.CacheConfigure;
import com.zhihaoscm.ws.handler.*;
import com.zhihaoscm.ws.handler.admin.AdminSocketHandler;
import com.zhihaoscm.ws.handler.ai.AiSocketHandler;
import com.zhihaoscm.ws.handler.custom.CustomSocketHandler;
import com.zhihaoscm.ws.handler.qr.login.QrLoginSocketHandler;

@EnableConfigurationProperties
@Configuration
@Import({ CacheConfigure.class })
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

	@Autowired(required = false)
	private GrayscaleProperties grayscaleProperties;

	@Override
	public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
		registry.addHandler(aiSocketHandler(), "/ai")
				.addInterceptors(
						new GrayscaleHandshakeInterceptor(grayscaleProperties))
				.setAllowedOrigins("*")
				.addHandler(adminSocketHandler(), "/admin")
				.setAllowedOrigins("*")
				.addHandler(customSocketHandler(), "/custom")
				.setAllowedOrigins("*")
				.addHandler(qrLoginSocketHandler(), "/qr-login")
				.setAllowedOrigins("*");
	}

	@Bean
	public TokenAuthenticationIntercept customTokenAuthenticationIntercept() {
		return new CustomTokenAuthenticationIntercept();
	}

	@Bean
	public TokenAuthenticationIntercept adminTokenAuthenticationIntercept() {
		return new AdminTokenAuthenticationIntercept();
	}

	@Bean
	public LimitIntercept limitIntercept() {
		return new LimitIntercept();
	}

	@Bean
	public WebSocketHandler aiSocketHandler() {
		return new AiSocketHandler(customTokenAuthenticationIntercept());
	}

	@Bean
	public WebSocketHandler adminSocketHandler() {
		return new AdminSocketHandler(adminTokenAuthenticationIntercept());
	}

	@Bean
	public ServerEndpointExporter serverEndpointExporter() {
		return new ServerEndpointExporter();
	}

	@Bean
	public CustomSocketHandler customSocketHandler() {
		return new CustomSocketHandler(customTokenAuthenticationIntercept());
	}

	@Bean
	public QrTokenAuthenticationIntercept qrTokenAuthenticationIntercept() {
		return new QrTokenAuthenticationIntercept();
	}

	@Bean
	public QrLoginSocketHandler qrLoginSocketHandler() {
		return new QrLoginSocketHandler(qrTokenAuthenticationIntercept());
	}

}