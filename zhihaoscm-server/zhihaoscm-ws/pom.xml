<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zhihaoscm</groupId>
        <artifactId>zhihaoscm-server</artifactId>
        <version>1.0.0-dev</version>
    </parent>

    <artifactId>zhihaoscm-ws</artifactId>
    <version>1.0.0-dev</version>


    <properties>
        <skipTests>true</skipTests>
        <docker.image.name>zhihaoscm-ws</docker.image.name>
        <docker.image.tag>1.0.0</docker.image.tag>
        <profile>default</profile>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-rocketmq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>zhihaoscm-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>openai-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

        <!-- 通义千问 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.14.5</version>
        </dependency>

        <!-- 阿里云 智能语音交互 -->
        <dependency>
            <groupId>com.alibaba.nls</groupId>
            <artifactId>nls-sdk-tts</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-gray</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>coze-sdk</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <!-- here the phase you need -->
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${basedir}/target/dockerfile</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/docker</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <!--用于指定镜像名称-->
                    <imageName>${docker.image.name}:${docker.image.tag}</imageName>
                    <!--Dockerfile文件位置-->
                    <dockerDirectory>${basedir}/target/dockerfile</dockerDirectory>
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <!--用于指定需要复制的根目录，${project.build.directory}表示target目录-->
                            <directory>${project.build.directory}</directory>
                            <!--用于指定需要复制的文件。${project.build.finalName}.jar指的是打包后的jar包文件-->
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                        <resource>
                            <targetPath>/arthas</targetPath>
                            <!-- 本地 Arthas JAR 包的路径 -->
                            <directory>${project.parent.basedir}/arthas</directory>
                            <!-- 如果需要指定特定的 Arthas JAR 包文件名，可以在这里指定 -->
                            <include>arthas.jar</include>
                        </resource>
                    </resources>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
