package com.zhihaoscm.domain.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 收藏商机
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Getter
@Setter
@TableName("t_business_opportunity_collect")
public class BusinessOpportunityCollect extends BaseEntityWithLongId {

	/**
	 * 用户id
	 */
	private Long customId;

	/**
	 * 商机id
	 */
	private Long businessId;
}
