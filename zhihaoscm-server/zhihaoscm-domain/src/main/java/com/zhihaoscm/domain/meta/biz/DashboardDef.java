package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

/**
 * 大屏枚举定义
 *
 * <AUTHOR>
 */
public interface DashboardDef {

	/**
	 * 日期范围
	 */
	@Getter
	enum Scope {

		TOTAL(1, "全部"),

		<PERSON><PERSON><PERSON>(2, "近一月"),

		QUARTER(3, "近一季度"),

		YEAR(4, "近一年");

		private final Integer code;

		private final String desc;

		private static final Map<Integer, Scope> MAPPING;

		static {
			{
				Map<Integer, Scope> mapping = new HashMap<>();
				for (Scope value : Scope.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		Scope(Integer code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public static Scope from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}
	}

}
