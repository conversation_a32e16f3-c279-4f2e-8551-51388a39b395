package com.zhihaoscm.domain.meta.biz;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.util.Assert;

import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;

import lombok.Getter;

public interface AutoCodeDef {

	/**
	 * 生成自动编码
	 */
	AutoCodeFunction<StringRedisClient, String, Integer, DATE_TYPE> CREATE_AUTO_CODE = (
			stringRedisClient, prefix, prefixKey, suffix, quantity,
			dateType) -> {
		LocalDateTime now = LocalDateTime.now();
		String datetime = getFormattedDate(dateType, now);
		String key = MessageFormat.format("{0}{1}", prefixKey, datetime);
		// 查询 key 是否存在， 不存在返回 1 ，存在的话则自增加1
		Long autoID = stringRedisClient.getRedisTemplate().opsForValue()
				.increment(key, 1);
		// 设置key过期时间,保证每月的流水号从1开始
		if (Objects.equals(autoID, 1L)) {
			expire(dateType, stringRedisClient, key, now);
		}
		// 补零
		String value = StringUtils.leftPad(String.valueOf(autoID), quantity,
				"0");
		// 然后把 时间戳和优化后的 ID 拼接
		return MessageFormat.format("{0}{1}{2}{3}", prefix, datetime, suffix,
				value);
	};

	@Getter
	enum BusinessCode {
		REQUIREMENT_PLAT_PREFIX("10", "平台船运需求 业务编号前缀"),
		TRANSPORT_ORDER_SHIP_PREFIX("11", "船运单 业务编号前缀"),
		ORDER_PREFIX("20", "订单 业务编号前缀"),
		PROJECT_PREFIX("21", "项目 业务编号前缀"),
		RECEIVE_PAYMENT_PREFIX("22", "付款 业务编号前缀"),
		PURCHASE_PAYMENT_PREFIX("23", "采购付款 业务编号前缀"),
		PURCHASE_GOODS_PREFIX("24", "采购提货 业务编号前缀"),
		PURCHASE_GOODS_RECEIPT_PREFIX("25", "采购签收 业务编号前缀"),
		PURCHASE_RECONCILIATION_PREFIX("26", "采购对账 业务编号前缀"),
		PURCHASE_INVOICE_PREFIX("27", "采购开票 业务编号前缀"),
		ADVANCE_PAYMENT_PREFIX("30", "预付款记录 业务编号前缀"),
		SERVICE_COST_PREFIX("31", "服务费账单 业务编号前缀"),
		MEMBER_OPEN_PREFIX("32", "会员开通记录 业务编号前缀"),
		PROMOTION_PREFIX("33", "推广 业务编号前缀"),
		PLATFORM_ADVANCE_PAYMENT("34", "平台预收款"),
		PRODUCT_PREFIX("40", "寄售商品 业务编号前缀"),
		PRODUCT_GROUP_PURCHASE_PREFIX("41", "团购商品 业务编号前缀"),
		PRODUCT_BARGAIN_PREFIX("42", "捡漏商品 业务编号前缀"),
		INSTALL_PROMOTION("12", "安装推广记录 业务编号前缀"),
		OIL_ORDER("13", "油品订单 业务编号前缀"),
		SHIPPING_DEPOSIT("35", "船运定金 业务编号前缀"),
		SHIP_INFO_SERVICE_FEE("36", "船务信息服务费 业务编号前缀"),
		INSTALL_PROMOTION_COMMISSION("37", "安装推广佣金 业务编号前缀"),
		OIL_FEE("38", "油品费用 业务编号前缀"),
		;

		private final String code;

		private final String desc;

		private static final Map<String, BusinessCode> MAPPING;

		static {
			{
				Map<String, BusinessCode> mapping = new HashMap<>();
				for (BusinessCode value : BusinessCode.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		BusinessCode(String code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public static BusinessCode from(final String code) {
			BusinessCode type = MAPPING.get(code);
			Assert.notNull(type, "wrong business code: " + code);
			return type;
		}

		public boolean match(final String code) {
			return this.code.equals(code);
		}
	}

	/**
	 * 设置过期时间
	 *
	 * @param dateType
	 * @param stringRedisClient
	 * @param key
	 * @param now
	 */
    static void expire(DATE_TYPE dateType,
			StringRedisClient stringRedisClient, String key,
			LocalDateTime now) {
		LocalDateTime endOfPeriod;
		switch (dateType) {
			case yyMM:
				endOfPeriod = LocalDateTime.of(now
						.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate(),
						LocalTime.MAX);
				break;
			case yyMMdd:
				endOfPeriod = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
				break;
			default:
				return;
		}
		long secondsTillEndOfPeriod = getSecondsBetween(now, endOfPeriod);
		stringRedisClient.expire(key, secondsTillEndOfPeriod, TimeUnit.SECONDS);
	}

	private static long getSecondsBetween(LocalDateTime start,
			LocalDateTime end) {
		return ChronoUnit.SECONDS.between(start, end);
	}

	/**
	 * 获取格式化日期
	 *
	 * @param dateType
	 * @param now
	 * @return
	 */
    static String getFormattedDate(DATE_TYPE dateType,
			LocalDateTime now) {
		return DateTimeFormatter.ofPattern(dateType.getName())
				.format(now);
	}

	@FunctionalInterface
	interface AutoCodeFunction<T, R, I, Q> {
		R apply(T t, R r1, R r2, R r3, I i, Q q);

	}

	@Getter
	enum DATE_TYPE {

		NONE(1, ""), yyMM(2, "yyMM"), yyMMdd(3, "yyMMdd");

		private final Integer code;

		private final String name;

		private static final Map<Integer, AutoCodeDef.DATE_TYPE> MAPPING;

		static {
			{
				Map<Integer, AutoCodeDef.DATE_TYPE> mapping = new HashMap<>();
				for (AutoCodeDef.DATE_TYPE value : AutoCodeDef.DATE_TYPE
						.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		DATE_TYPE(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

		public static AutoCodeDef.DATE_TYPE from(final Integer code) {
			AutoCodeDef.DATE_TYPE dateType = MAPPING.get(code);
			Assert.notNull(dateType, "wrong dateType status " + code);
			return dateType;
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}

}
