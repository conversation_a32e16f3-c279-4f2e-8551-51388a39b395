package com.zhihaoscm.domain.bean.vo;

import java.time.LocalDateTime;
import java.util.List;

import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.ShippingPriceIndex;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementCustomerDef;

import lombok.Data;

@Data
public class ShippingRequirementCustomerVo {

	/**
	 * 平台船运需求
	 */
	private ShippingRequirementPlat plat;

	/**
	 * 运价指数
	 */
	private ShippingPriceIndex priceIndex;

	/**
	 * 货主
	 */
	private Customer owener;

	/**
	 * 关联的船运单详情
	 */
	private List<TransportOrderShip> orderShips;

	/**
	 * 关联的船运单详情
	 */
	private List<TransportOrderShipVo> orderShipVos;

	/**
	 * 编号--用于货主运单唯一搜索
	 */
	private String title;

	/**
	 * 始发地码头名称--用于货主运单模糊搜索
	 */
	private String sourcePortName;

	/**
	 * 目的地码头名称--用于货主运单模糊搜索
	 */
	private String destinationPortName;

	/**
	 * 货主运单状态{@link ShippingRequirementCustomerDef.TransportOrderState}--用于货主运单搜索
	 */
	private Integer tarnsPortOrderState;

	/**
	 * 发布时间(除了平台创建的船运单取船运单的创建时间，其他情况取货主找船需求的创建时间)--用于货主运单
	 */
	private LocalDateTime publishTime;

	/**
	 * 船舶名称 (中文/英文)
	 */
	private String shipName;

}
