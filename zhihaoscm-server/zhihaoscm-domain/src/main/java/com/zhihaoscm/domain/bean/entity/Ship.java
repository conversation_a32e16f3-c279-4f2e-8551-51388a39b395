package com.zhihaoscm.domain.bean.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.domain.annotation.DiffField;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.json.CertificateFiles;
import com.zhihaoscm.domain.bean.json.IdCardInfo;
import com.zhihaoscm.domain.meta.biz.ShipDef;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 船舶
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Getter
@Setter
@TableName("t_ship")
public class Ship extends BaseEntityWithStringId {

	/**
	 * imo号
	 */
	private String imo;

	/**
	 * 呼号
	 */
	private String callSign;

	/**
	 * 船舶名称
	 */
	@DiffField(alias = "英文船名")
	private String name;

	/**
	 * 船舶名称中文
	 */
	@DiffField(alias = "中文船名")
	private String cnname;

	/**
	 * 承运方
	 */
	private Long carrier;

	/**
	 * 类型 {@link ShipDef.Type}
	 */
	@DiffField(alias = "类型", type = DiffField.Type.ENUM, clazz = ShipDef.Type.class)
	private Integer type;

	/**
	 * 散货船类型 {@link ShipDef.BulkCargoShipType}
	 */
	private Integer bulkCargoShipType;

	/**
	 * 长度
	 */
	@DiffField(alias = "船长")
	private BigDecimal length;

	/**
	 * 宽度
	 */
	@DiffField(alias = "船宽")
	private BigDecimal width;

	/**
	 * 型深
	 */
	@DiffField(alias = "型深")
	private BigDecimal deep;

	/**
	 * 营业运输证
	 */
	private String certificate;

	/**
	 * 船只负责人
	 */
	@DiffField(alias = "负责人")
	private String captain;

	/**
	 * 手机号
	 */
	@DiffField(alias = "联系方式")
	private String mobile;

	/**
	 * 吃水
	 */
	private String draft;

	/**
	 * 联系方式维护人id
	 */
	private Long contactMaintainerId;

	/**
	 * 联系方式维护人
	 */
	private String contactMaintainer;

	/**
	 * 总吨数
	 */
	@DiffField(alias = "总吨")
	private Long tonTotal;

	/**
	 * 载重吨数
	 */
	@DiffField(alias = "载重吨")
	private Long tonCapacity;

	/**
	 * 净吨数
	 */
	@DiffField(alias = "净吨")
	private Long tonPure;

	/**
	 * 营运证截止时间
	 */
	private LocalDateTime operatingLicenseExpirationDate;

	/**
	 * 船舶经营人名称
	 */
	private String shipOperatorName;

	/**
	 * 国籍截止时间
	 */
	private LocalDateTime nationalityExpirationDate;

	/**
	 * 船检证截止时间
	 */
	private LocalDateTime shipInspectionExpirationDate;

	/**
	 * 建造日期
	 */
	private LocalDateTime constructionDate;

	/**
	 * 船籍港-城市编码
	 */
	private String homePortCityCode;

	/**
	 * 船舶营运证号
	 */
	private String operatingLicenseNumber;

	/**
	 * 船舶照片文件id
	 */
	@FileId
	private ArrayLong shipPhotoFiles;

	/**
	 * 船舶视频文件id
	 */
	@FileId
	private ArrayLong shipVideoFiles;

	/**
	 * 船只证件文件id
	 */
	private CertificateFiles certificateFiles;

	/**
	 * 职务资格-适任证书识别的
	 */
	private String jobQualification;

	/**
	 * 船舶身份证图片识别的字段
	 */
	private IdCardInfo idCardInfo;

	/**
	 * 资料完善进度
	 */
	private Integer dataProgress;

	/**
	 * 备注
	 */
	@DiffField(alias = "备注")
	private String remark;

	/**
	 * 状态 {@link ShipDef.State}
	 */
	private Integer state;

	/**
	 * 货仓状态
	 * {@link com.zhihaoscm.domain.meta.biz.DeviceCapturePicDef.RecognizeResult}
	 */
	private Integer warehouseState;

	/**
	 * 剩余装/卸货时间
	 */
	private String remainingLayTime;

	/**
	 * 是否存在摄像头
	 */
	private Integer existDevice;

	/**
	 * 是否存在联系方式
	 */
	private Integer existMobile;

	/**
	 * 是否存在联系方式
	 */
	private Integer existWaterGauge;

	/**
	 * 认证时间
	 */
	private LocalDateTime authenticationTime;

	/**
	 * 黄码港审核状态 {@link ShipDef.HmgCheckState}
	 */
	private Integer hmgCheckState;

}
