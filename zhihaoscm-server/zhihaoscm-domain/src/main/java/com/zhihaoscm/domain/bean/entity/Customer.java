package com.zhihaoscm.domain.bean.entity;

import java.io.Serial;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.MembershipLevelDef;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 客户
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@Getter
@Setter
@TableName("t_customer")
public class Customer extends BaseEntityWithLongId {

	@Serial
	private static final long serialVersionUID = -7520465385159037475L;

	/**
	 * 客户编码
	 */
	private String code;

	/**
	 * 微信Openid
	 */
	private String wxOpenId;

	/**
	 * 微信Unionid
	 */
	private String unionId;

	/**
	 * 客户昵称
	 */
	private String nickName;

	/**
	 * 拥有的身份 1111 二进制表示 从右往左分别是 采购商 供应商 联合购销商 承运商
	 */
	private Integer identity;

	/**
	 * 真实姓名
	 */
	private String realName;

	/**
	 * 真实姓名拼音
	 */
	private String realNamePinyin;

	/**
	 * 身份证号
	 */
	private String idNo;

	/**
	 * 组织机构名称
	 */
	private String institutionName;

	/**
	 * 统一社会信用代码
	 */
	private String unifiedSocialCreditCode;

	/**
	 * 法人代表
	 */
	private String legalRepresentative;

	/**
	 * 企业认证是否已过期
	 */
	private Integer hasExpired;

	/**
	 * 手机号码
	 */
	private String mobile;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 密码
	 */
	private String password;

	/**
	 * 推广码
	 */
	private String promotionCode;

	/**
	 * 推广人编码
	 */
	private String promoterCode;

	/**
	 * 推广人id
	 */
	private Long promoterId;

	/**
	 * 本身是否有推广资格
	 */
	private Integer hasPromotion;

	/**
	 * 机构认证状态 {@link CommonDef.Symbol}
	 */
	private Integer applyState;

	/**
	 * 状态
	 */
	private Integer state;

	/**
	 * 系统头像地址
	 */
	@FileId
	private Long avatarFileId;

	/**
	 * 会员等级 默认为非会员 {@link MembershipLevelDef.Level}
	 */
	private Integer memberLevel;

	/**
	 * 会员激活时间
	 */
	private LocalDateTime memberActiveTime;

	/**
	 * 会员到期日期
	 */
	private LocalDate memberExpiryDate;

	/**
	 * 会员状态 {@link CustomerDef.MemberState}
	 */
	private Integer memberState;

	/**
	 * 注销时间
	 */
	private LocalDateTime logOffTime;

	/**
	 * 创建途径
	 */
	private Integer createdType;

	/**
	 * 更新途径
	 */
	private Integer updatedType;

	/**
	 * 是否接收短信
	 */
	private Integer receiptSms = CommonDef.Symbol.YES.getCode();

	/**
	 * 密码修改时间
	 */
	private LocalDateTime passwordUpdateTime;

	/**
	 * 苹果返回的客户唯一id
	 */
	private String subId;

	/**
	 * 是否签署黄码港代开协议 {@link CommonDef.Symbol}
	 */
	private Integer isSignHmgAgreement;

	/**
	 * 应用类型（注册来源）
	 */
	private Integer appType;
}
