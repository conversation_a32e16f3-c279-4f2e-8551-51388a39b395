package com.zhihaoscm.domain.bean.json;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 商机的审核信息
 */
@Data
public class AuditInfo {

	/**
	 * 审核人id
	 */
	private Long auditBy;

	/**
	 * 审核人名称
	 */
	private String auditByName;

	/**
	 * 审核时间
	 */
	private LocalDateTime auditTime;

	/**
	 * 未通过原因（不通过）
	 */
	private Integer failedReason;

	/**
	 * 备注（不通过）
	 */
	private String remark;

	/**
	 * 联系方式(通过)
	 */
	private String contactMobile;

	/**
	 * 审核结果：1通过、0不通过
	 */
	private Integer state;
}
