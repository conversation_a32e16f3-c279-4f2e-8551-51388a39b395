package com.zhihaoscm.domain.bean.json;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 船舶身份证识别字段
 */
@Data
public class IdCardInfo implements Serializable {
	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 身份证号
	 */
	private String idNumber;

	/**
	 * 性别:男 女
	 */
	private String sex;

	/**
	 * 住址
	 */
	private String address;

	/**
	 * 出生日期
	 */
	private LocalDateTime birthDate;

	/**
	 * 身份证有效期-开始时间
	 */
	private LocalDateTime idCardStartDate;

	/**
	 * 身份证有效期-到期时间
	 */
	private LocalDateTime idCardEndDate;
}
