package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

/**
 * 平台银行账号相关
 */
public interface PlatformBankAccountDef {

	/**
	 * 适用终端
	 */
	@Getter
	enum UseType {
		SHIP_WAYBILL(1L, "船运单"), OIL_ORDER(2L, "油品订单");

		private final Long code;
		private final String name;

		private static final Map<Long, PlatformBankAccountDef.UseType> MAPPING;

		static {
			Map<Long, PlatformBankAccountDef.UseType> mapping = new HashMap<>();
			for (PlatformBankAccountDef.UseType value : PlatformBankAccountDef.UseType
					.values()) {
				mapping.put(value.getCode(), value);
			}
			MAPPING = mapping;
		}

		public static PlatformBankAccountDef.UseType from(final Long code) {
			return MAPPING.get(code);
		}

		public boolean match(final Long code) {
			return this.code.equals(code);
		}

		UseType(Long code, String name) {
			this.code = code;
			this.name = name;
		}
	}
}
