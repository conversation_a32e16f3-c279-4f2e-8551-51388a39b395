package com.zhihaoscm.domain.bean.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.domain.bean.json.AddressInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.ProductInfo;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Getter
@Setter
@TableName("t_order")
public class Order extends BaseEntityWithStringId {

	/**
	 * 订单类型(1寄售,2团购,3捡漏)
	 * {@link com.zhihaoscm.domain.meta.biz.OrderDef.OrderType}
	 */
	private Integer orderType;

	/**
	 * 商品id
	 */
	private String productId;

	/**
	 * 商品快照信息
	 */
	private ProductInfo productInfo;

	/**
	 * 客户id
	 */
	private Long customerId;

	/**
	 * 客户企业信息
	 */
	private Enterprise customerEnterprise;

	/**
	 * 地址id
	 */
	private Long addressId;

	/**
	 * 地址信息
	 */
	private AddressInfo addressInfo;

	/**
	 * 采购数量(吨数)
	 */
	private Long ton;

	/**
	 * 单价
	 */
	private BigDecimal unitPrice;

	/**
	 * 预估金额
	 */
	private BigDecimal estimatedAmount;

	/**
	 * 付款单金额
	 */
	private BigDecimal payAmount;

	/**
	 * 退款单金额
	 */
	private BigDecimal refundAmount;

	/**
	 * 锁货时间
	 */
	private LocalDateTime lockingTime;

	/**
	 * 处理人ID
	 */
	private Long handlerId;

	/**
	 * 处理人名称
	 */
	private String handlerName;

	/**
	 * 采购说明
	 */
	private String remark;

	/**
	 * 状态 {@link com.zhihaoscm.domain.meta.biz.OrderDef.State}
	 */
	private Integer state;

	/**
	 * 客户删除状态 {@link com.zhihaoscm.common.bean.constants.CommonDef.Symbol}
	 */
	private Integer customerDel;

	/**
	 * 实际收款金额
	 */
	private BigDecimal actualReceivedAmount;

	/**
	 * 未开票金额
	 */
	private BigDecimal uninvoicedAmount;

	/**
	 * 已开票金额
	 */
	private BigDecimal invoicedAmount;
}
