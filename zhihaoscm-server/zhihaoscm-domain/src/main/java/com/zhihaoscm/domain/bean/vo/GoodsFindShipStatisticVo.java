package com.zhihaoscm.domain.bean.vo;

import lombok.Data;

/**
 * 货找船页面统计vo
 */
@Data
public class GoodsFindShipStatisticVo {

	/**
	 * 累计船舶数量(个)-船舶信息列表的所有数量
	 */
	private Long totalShipNum;

	/**
	 * 认证船舶数量(个)-船舶信息列表，状态为已认证的所有数量
	 */
	private Long totalShipApplyNum;

	/**
	 * 累计船主数量(个)
	 */
	private Long totalRegisterCarrierNum;

	/**
	 * 累计货船匹配运单(个)-平台船运需求列表的所有数量
	 */
	private Long totalShippingRequirementPlatNum;

}
