package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import lombok.Getter;

/**
 * 阅读量枚举定义
 */
public interface ReadQuantityDef {


	/**
	 * 文章类型
	 */
	@Getter
	enum Type {
		SAND_INFO(1, "砂石资讯"),
		SAND_ACADEMY(2, "砂石学院"),
		SAND_HOT(3, "砂石热点");

		private final Integer code;
		private final String name;

		private static final Map<Integer, ReadQuantityDef.Type> MAPPING;

		static {
			{
				Map<Integer, ReadQuantityDef.Type> mapping = new HashMap<>();
				for (ReadQuantityDef.Type value : ReadQuantityDef.Type
						.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static ReadQuantityDef.Type from(final Integer code) {
			ReadQuantityDef.Type result = MAPPING.get(code);

			if (Objects.nonNull(result)) {
				return result;
			} else {
				throw new IllegalArgumentException("wrong Type code " + code);
			}
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Type(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}
}
