package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

/**
 * 订单定义
 */
public interface OrderDef {

	/**
	 * 状态
	 */
	@Getter
	enum OrderType {

		CONSIGNMENT_PRODUCTS(1, "寄售商品"), GROUP_BUYING_PRODUCTS(2,
				"团购商品"), PICKING_UP_MISSING_PRODUCTS(3, "捡漏商品");

		private final Integer code;
		private final String name;

		private static final Map<Integer, OrderDef.OrderType> MAPPING;

		static {
			{
				Map<Integer, OrderDef.OrderType> mapping = new HashMap<>();
				for (OrderDef.OrderType value : OrderDef.OrderType.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static OrderDef.OrderType from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		OrderType(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 状态
	 */
	@Getter
	enum State {

		OBLIGATION(1, "待付款"), TO_BE_PICKED_UP(2,
				"待提货"), IN_THE_PROCESS_OF_PICKING_UP_THE_GOODS(3,
						"提货中"), COMPLETED(4, "已完成"), CANCELLED(5, "已取消");

		private final Integer code;
		private final String name;

		private static final Map<Integer, OrderDef.State> MAPPING;

		static {
			{
				Map<Integer, OrderDef.State> mapping = new HashMap<>();
				for (OrderDef.State value : OrderDef.State.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static OrderDef.State from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		State(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}
}
