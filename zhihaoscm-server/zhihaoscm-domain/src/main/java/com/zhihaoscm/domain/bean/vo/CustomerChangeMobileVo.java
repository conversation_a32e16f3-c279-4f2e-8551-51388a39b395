package com.zhihaoscm.domain.bean.vo;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.CustomerChangeMobile;
import com.zhihaoscm.domain.bean.entity.CustomerPersonalCertification;
import com.zhihaoscm.domain.bean.entity.InstitutionApply;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerChangeMobileVo {

	/**
	 * 客户信息
	 */
	private CustomerChangeMobile customerChangeMobile;

	/**
	 * 客户个人认证信息
	 */
	private CustomerPersonalCertification personalCertification;
	/**
	 * 机构认证状态 {@link CommonDef.Symbol}
	 */
	private Integer applyState;

	/**
	 * 机构认证信息
	 */
	private InstitutionApply institutionApply;

	/**
	 * 组织机构名称
	 */
	private String institutionName;

}
