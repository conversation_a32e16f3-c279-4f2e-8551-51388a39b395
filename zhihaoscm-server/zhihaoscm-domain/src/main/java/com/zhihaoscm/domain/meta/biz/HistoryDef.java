package com.zhihaoscm.domain.meta.biz;

public interface HistoryDef {

	int ADMIN = 0;
	int CUSTOM = 1;

	// 小模块
	// 物流管理 - 船舶管理 - 船舶信息
	String LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO = "1";
	// 物流管理 - 船运需求 - 货主找船需求
	String LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_SHIPPER_DEMAND = "2";
	// 物流管理 - 船运需求 - 船运需求发布
	String LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_PUBLISH = "3";
	// 物流管理 - 船运需求 - 船运单
	String LOGISTICS_MANAGEMENT_SHIPPING_ORDER = "4";
	// 物流管理 - 油品订单
	String LOGISTICS_MANAGEMENT_OIL_ORDER = "5";
	// 船运单据操作人
	String SHIPPING_DOCUMENT_OPERATOR = "11";

	// 操作动作
	// 船舶管理-船舶信息 相关
	String SHIP_ADD = " 新增";
	String SHIP_MODIFY = "修改";
	String SHIP_UNBIND = "解绑";
	String SHIP_BIND = "绑定";
	String SHIP_IMPORT_DRAFT = "导入船舶水尺";
	String SHIP_ADD_ONE_DRAFT = "新增船舶水尺";
	String SHIP_DELETE_ONE_DRAFT = "删除船舶水尺";
	// 船运需求-货主找船需求 相关
	String SHIPPING_DEMAND_SHIPPER_DEMAND_ASSIGN = "指派给 #userName#";
	String SHIPPING_DEMAND_SHIPPER_DEMAND_START = "开始";
	String SHIPPING_DEMAND_SHIPPER_DEMAND_CHECK = "核对";
	String SHIPPING_DEMAND_SHIPPER_DEMAND_CONFIRM = "确认找船";
	// 船运需求-船运需求发布 相关
	String SHIPPING_DEMAND_PUBLISH_ADD = "新增";
	String SHIPPING_DEMAND_PUBLISH_UPDATE = "修改";
	String SHIPPING_DEMAND_PUBLISH_CHANGE_ASSIGN = "变更指派给 #userName#";
	String SHIPPING_DEMAND_PUBLISH_END = "结束";
	String SHIPPING_DEMAND_PUBLISH_CHECK_ORDER = "核对";
	String SHIPPING_DEMAND_PUBLISH_REJECT_ORDER = "拒绝";
	// 船运需求-船运单 相关
	String SHIPPING_ORDER_ADD = "新增";
	String SHIPPING_ORDER_ASSIGN = "指派给上游专员 #upHandlerName#；指派给下游专员 #downHandlerName#";
	String SHIPPING_ORDER_ASSIGN1 = "指派给上游专员 #upHandlerName#";
	String SHIPPING_ORDER_ASSIGN2 = "指派给下游专员 #downHandlerName#";
	String SHIPPING_ORDER_START_LOADING = "开始装货";
	String SHIPPING_ORDER_APPLY_SAILING = "发航申请";
	String SHIPPING_ORDER_COMPLETE_UNLOADING = "完成卸货";
	String SHIPPING_ORDER_COMPLETE = "完成";
	String SHIPPING_ORDER_CONFIRM_DEPARTURE = "帮货主发航确认";
	String SHIPPING_ORDER_AGREE_UNLOAD = "帮货主同意卸货";
	String SHIPPING_ORDER_CONFIRM_ARRIVAL_PORT = "帮船主到港确认";
	String SHIPPING_ORDER_UPLOAD_DRAINAGE_VIDEO = "帮船主上传排水视频";
	String HELP_THE_OWNER_PAY = "帮货主支付#type#";
	String HELP_THE_BOAT_OWNER_CONFIRM_RECEIPT = "帮船主确认收到#type#";
	String HELP_PAY_THE_INFORMATION_SERVICE_FEE = "帮#type#支付信息服务费";
	String MODIFY_PAYMENT = "修改支付#type#";
	String CANCEL_THE_PAYMENT = "取消支付#type#";
	String HELP_THE_SHIPOWNER_UPLOAD_THE_SHIP_INFORMATION = "帮船主上传发船信息";
	String HELP_THE_SHIPOWNER_UPLOAD_THE_UNLOADING_INFORMATION = "帮船主上传卸货信息";

	// 油品订单
	String OIL_ORDER_ADD = "新增";
	String OIL_ORDER_CHECK = "核对";
	String OIL_ORDER_PLAN = "报计划";
	String OIL_ORDER_CONFIRM = "确认加油";
	String OIL_ORDER_COMPLETE = "完成加油";
	String OIL_ORDER_CANCEL = "取消";

	// 确认找船
	String CONFIRM_FINDING_SHIP = "确认找船操作人：";
	// 支付船务信息服务费
	String PAY_SHIPPING_INFORMATION_SERVICE_FEE = "支付船务信息服务费操作人：";
	// 发航确认
	String FLIGHT_CONFIRMATION = "发航确认操作人：";
	// 同意卸货
	String AGREE_TO_UNLOAD = "同意卸货操作人：";

}
