package com.zhihaoscm.domain.bean.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.json.Enterprise;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 收款信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Getter
@Setter
@TableName("t_receive_payment")
public class ReceivePayment extends BaseEntityWithStringId {

	/**
	 * 支付类型 {@link com.zhihaoscm.domain.meta.biz.ReceivePaymentDef.PaymentType}
	 */
	private Integer paymentType;

	/**
	 * 排期id
	 */
	private String scheduleId;

	/**
	 * 订单id
	 */
	private String orderId;

	/**
	 * 客户id
	 */
	private Long customerId;

	/**
	 * 客户信息
	 */
	private Enterprise customerEnterprise;

	/**
	 * 吨数
	 */
	private BigDecimal ton;

	/**
	 * 吨位证明文件ID
	 */
	@FileId
	private Long tonProveFileId;

	/**
	 * 金额
	 */
	private BigDecimal amount;

	/**
	 * 支付时间
	 */
	private LocalDateTime payTime;

	/**
	 * 支付结束时间
	 */
	private LocalDateTime payEndTime;

	/**
	 * 费用说明
	 */
	private String remark;

	/**
	 * 状态(1待支付,2已支付,3未支付)
	 * {@link com.zhihaoscm.domain.meta.biz.ReceivePaymentDef.State}
	 */
	private Integer state;

	/**
	 * 未开票金额
	 */
	private BigDecimal uninvoicedAmount;

	/**
	 * 已完成开票金额
	 */
	private BigDecimal invoicedAmount;
}
