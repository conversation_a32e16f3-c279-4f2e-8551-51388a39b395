package com.zhihaoscm.domain.bean.vo;

import java.time.LocalDateTime;

import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.json.MemberPermission;

import lombok.Data;

@Data
public class MeVo {

	/**
	 * 代理账号信息
	 */
	private Customer proxyAccount;

	/**
	 * 实际账号信息
	 */
	private Customer actualAccount;

	/**
	 * 代理账号会员权限
	 */
	private MemberPermission proxyMemberPermission;

	/**
	 * 实际账号会员权限
	 */
	private MemberPermission actualMemberPermission;

	/**
	 * 微信昵称
	 */
	private String nickName;

	/**
	 * 绑定时间
	 */
	private LocalDateTime bindTime;

}
