package com.zhihaoscm.domain.meta.error;

import java.util.function.Function;

import lombok.Getter;

@Getter
public enum ErrorCodeDef {
	CODE_001(ErrorCode.CODE_001, "业务错误"),
	CODE_002(ErrorCode.CODE_002, "签署错误"),
	CODE_003(ErrorCode.CODE_003, "选择的链云用户不能是同一个账号"),
	CODE_400(ErrorCode.CODE_400, "BadRequest"),
	CODE_401(ErrorCode.CODE_401, "没有权限"),
	CODE_403(ErrorCode.CODE_403, "权限错误"),
	CODE_404(ErrorCode.CODE_404, "没有资源"),
	CODE_900(ErrorCode.CODE_900, "sdk异常"),
	CODE_911(ErrorCode.CODE_911, "车辆服务接口异常"),
	CODE_912(ErrorCode.CODE_912, "车辆服务查询结果为空"),
	CODE_913(ErrorCode.CODE_913, "参数不正确（参数为空、检索时间范围不正确、参数数量不正确）"),

	//用户不存在
	CODE_20001003(ErrorCode.CODE_20001003, "用户不存在!"),

	// 合同相关
	CODE_30001001(ErrorCode.CODE_30001001, "合同不存在!"),
	CODE_30001002(ErrorCode.CODE_30001002, "当前用户不存在!"),
	CODE_30001003(ErrorCode.CODE_30001003, "调用契约锁撤销合同接口异常"),
	CODE_30001004(ErrorCode.CODE_30001004, "调用契约锁撤销合同接口失败"),
	CODE_30001005(ErrorCode.CODE_30001005, "调用契约锁下载合同接口失败"),
	CODE_30001006(ErrorCode.CODE_30001006, "调用契约锁生成合同草稿接口异常"),
	CODE_30001007(ErrorCode.CODE_30001007, "调用契约锁生成合同草稿接口失败"),
	CODE_30001008(ErrorCode.CODE_30001008, "下载合同文件异常!"),
	CODE_30001009(ErrorCode.CODE_30001009, "调用契约锁通过文件添加合同文档接口异常"),
	CODE_30001010(ErrorCode.CODE_30001010, "调用契约锁通过文件添加合同文档接口失败"),
	CODE_30001011(ErrorCode.CODE_30001011, "调用契约锁发起合同接口异常"),
	CODE_30001012(ErrorCode.CODE_30001012, "调用契约锁发起合同接口失败"),
	CODE_30001013(ErrorCode.CODE_30001013, "调用契约锁获取签署链接接口异常"),
	CODE_30001014(ErrorCode.CODE_30001014, "调用契约锁生成签署令牌接口异常"),
	CODE_30001015(ErrorCode.CODE_30001015, "暂无可用印章，请先创建关联该业务的印章"),
	CODE_30001016(ErrorCode.CODE_30001016, "您尚未为该业务指定关联印章,请前往修改印章页面勾选相关业务后再试"),
	CODE_30001017(ErrorCode.CODE_30001017, "无权签署该合同"),
	CODE_30001018(ErrorCode.CODE_30001018, "已发起签署，请等待专人进行签署"),
	CODE_30001019(ErrorCode.CODE_30001019, "调用契约锁查看合同详情异常"),
	CODE_30001020(ErrorCode.CODE_30001020, "发起签署失败，请修改印章使用者"),
	CODE_30001021(ErrorCode.CODE_30001021, "合同已存在，请返回列表查看"),
	CODE_30001022(ErrorCode.CODE_30001022, "获取token失败"),
	CODE_30001023(ErrorCode.CODE_30001023, "企业信息变更中，无法签署"),

	// 船舶相关
	CODE_30002001(ErrorCode.CODE_30002001, "抓拍船舶照片失败"),
	CODE_30002002(ErrorCode.CODE_30002002, "设备不在线"),
	CODE_30002003(ErrorCode.CODE_30002003, "设备未开启直播功能"),
	CODE_30002004(ErrorCode.CODE_30002004, "调用宇视云获取视频播放链接接口异常"),
	CODE_30002005(ErrorCode.CODE_30002005, "设备推流错误"),
	CODE_30002006(ErrorCode.CODE_30002006, "调用宇视云开启播放功能接口异常"),
	CODE_30002007(ErrorCode.CODE_30002007, "调用宇视云关闭播放功能接口异常"),
	CODE_30002008(ErrorCode.CODE_30002008, "获取视频播放链接失败"),
	CODE_30002009(ErrorCode.CODE_30002009, "抓拍船舶照片异常"),
	CODE_30002010(ErrorCode.CODE_30002010, "开启播放功能失败"),
	CODE_30002011(ErrorCode.CODE_30002011, "调用宇视云关闭播放功能接口失败"),
	CODE_30002012(ErrorCode.CODE_30002012, "调用宇视云查询设备接口异常"),
	CODE_30002013(ErrorCode.CODE_30002013, "调用宇视云接口查询设备失败"),
	CODE_30002014(ErrorCode.CODE_30002014, "船舶已存在"),
	CODE_30002015(ErrorCode.CODE_30002015, "船舶不存在"),
	CODE_30002016(ErrorCode.CODE_30002016, "调用船顺网查询船舶信息接口异常"),
	CODE_30002017(ErrorCode.CODE_30002017, "调用船顺网区域查询船舶接口异常"),
	CODE_30002018(ErrorCode.CODE_30002018, "调用船顺网关键字查询船舶接口异常"),
	CODE_30002019(ErrorCode.CODE_30002019, "识别内河船舶检验报告文件不存在"),


	// 航线相关
	CODE_30003001(ErrorCode.CODE_30003001, "航线已存在，不可重复维护"),

	// 预付款相关
	CODE_30004001(ErrorCode.CODE_30004001, "用户账户不存在,无法撤销"),

	// 服务费账单相关
	CODE_30005001(ErrorCode.CODE_30005001, "用户账户不存在,无法核销"),
	CODE_30005002(ErrorCode.CODE_30005002, "用户不存在,无法结算"),
	// 设备相关
	CODE_30006001(ErrorCode.CODE_30006001, "调用海康威视平台新增接口异常"),
	CODE_30006002(ErrorCode.CODE_30006002, "调用海康威视平台修改接口异常"),
	CODE_30006003(ErrorCode.CODE_30006003, "调用海康威视平台删除接口异常"),
	CODE_30006004(ErrorCode.CODE_30006004, "验证码错误"),
	CODE_30006005(ErrorCode.CODE_30006005, "设备不存在"),
	CODE_30006006(ErrorCode.CODE_30006006, "设备不在线"),
	CODE_30006007(ErrorCode.CODE_30006007, "设备序列号无效"),
	CODE_30006008(ErrorCode.CODE_30006008, "远程抓图异常"),
	CODE_30006009(ErrorCode.CODE_30006009, "获取标准流播放地址异常"),
	CODE_30006010(ErrorCode.CODE_30006010, "失效标准流播放地址异常"),
	CODE_30006011(ErrorCode.CODE_30006011, "参数错误"),

	// 企业认证相关
	CODE_30007001(ErrorCode.CODE_30007001, "当前客户尚未进行过个人认证操作"),
	CODE_30007002(ErrorCode.CODE_30007002, "当前客户未进行过企业认证操作,请核实后再试!"),
	CODE_30007003(ErrorCode.CODE_30007003, "保存企业认证结果异常"),
	CODE_30007004(ErrorCode.CODE_30007004, "当前企业未进行授权操作,请核实后再试!"),
	CODE_30007005(ErrorCode.CODE_30007005, "调用获取契约锁企业认证结果接口异常,请稍后再试"),
	CODE_30007006(ErrorCode.CODE_30007006, "该企业尚未通过认证,请等待企业审核完成后再进行授权操作"),
	CODE_30007007(ErrorCode.CODE_30007007, "获取企业授权链接异常"),
	CODE_30007008(ErrorCode.CODE_30007008, "获取企业授权链接失败"),
	CODE_30007009(ErrorCode.CODE_30007009, "给当前操作人添加印章管理员角色异常!"),
	CODE_30007010(ErrorCode.CODE_30007010, "给当前操作人添加印章管理员角色失败!"),
	CODE_30007011(ErrorCode.CODE_30007011, "未找到相关企业秘钥"),
	CODE_30007012(ErrorCode.CODE_30007012, "添加员工异常"),
	CODE_30007013(ErrorCode.CODE_30007013, "添加员工失败"),
	CODE_30007014(ErrorCode.CODE_30007014, "未找到相关企业认证信息"),
	CODE_30007015(ErrorCode.CODE_30007015, "获取企业认证令牌异常"),
	CODE_30007016(ErrorCode.CODE_30007016, "获取企业认证令牌失败"),
	CODE_30007017(ErrorCode.CODE_30007017, "调用第三方接口查询公司详情异常"),
	CODE_30007018(ErrorCode.CODE_30007018, "调用第三方接口查询公司详情失败"),
	CODE_30007019(ErrorCode.CODE_30007019, "企业正在认证中,请等待认证结果"),
	CODE_30007020(ErrorCode.CODE_30007020, "企业名称不能为空!"),


	// openAI相关
	CODE_30008001(ErrorCode.CODE_30008001, "上传文件失败"),
	CODE_30008002(ErrorCode.CODE_30008002, "删除文件失败"),
	CODE_30008003(ErrorCode.CODE_30008003, "创建向量存储失败"),
	CODE_30008004(ErrorCode.CODE_30008004, "修改向量存储失败"),
	CODE_30008005(ErrorCode.CODE_30008005, "删除向量存储失败"),
	CODE_30008006(ErrorCode.CODE_30008006, "向量存储添加文件失败"),
	CODE_30008007(ErrorCode.CODE_30008007, "向量存储不存在"),
	CODE_30008008(ErrorCode.CODE_30008008, "向量存储关联文件失败"),
	CODE_30008009(ErrorCode.CODE_30008009, "删除向量存储文件失败"),
	CODE_30008010(ErrorCode.CODE_30008010, "文件不存在"),
	CODE_30008011(ErrorCode.CODE_30008011, "智能助理创建失败"),
	CODE_30008012(ErrorCode.CODE_30008012, "更新智能助理失败"),
	CODE_30008013(ErrorCode.CODE_30008013, "删除智能助理失败"),
	CODE_30008014(ErrorCode.CODE_30008014, "清空会话失败"),
	CODE_30008015(ErrorCode.CODE_30008015, "读取 functions.json 文件失败"),

	// 支付相关
	CODE_30009001(ErrorCode.CODE_30009001, "订单已关闭"),
	CODE_30009002(ErrorCode.CODE_30009002, "订单已支付"),
	CODE_30009003(ErrorCode.CODE_30009003, "订单不存在"),
	CODE_30009004(ErrorCode.CODE_30009004, "获取支付二维码失败"),
	CODE_30009005(ErrorCode.CODE_30009005, "调用支付接口异常"),
	CODE_30009006(ErrorCode.CODE_30009006, "订单号重复"),
	CODE_30009007(ErrorCode.CODE_30009007, "金额不能为空且必须大于0"),
	CODE_30009008(ErrorCode.CODE_30009008, "json解析异常"),
	CODE_30009009(ErrorCode.CODE_30009009, "非会员不需要购买"),
	CODE_30009010(ErrorCode.CODE_30009010, "会员等级不存在"),
	CODE_30009011(ErrorCode.CODE_30009011, "购买类型不存在"),
	CODE_30009012(ErrorCode.CODE_30009012, "船运单id不能为空"),
	CODE_30009013(ErrorCode.CODE_30009013, "船运单不能为空"),
	CODE_30009014(ErrorCode.CODE_30009014, "微信openid不能为空"),
	CODE_30009015(ErrorCode.CODE_30009015, "船运单非待支付船务信息服务费状态不可进行支付"),
	CODE_30009016(ErrorCode.CODE_30009016, "船务信息服务费id不能为空"),
	CODE_30009017(ErrorCode.CODE_30009017, "船务信息服务费不存在"),

	// 会员等级
	CODE_30010001(ErrorCode.CODE_30010001, "查询不到用户当前的会员信息"),
	CODE_30010002(ErrorCode.CODE_30010002, "当前会员已不能购买"),
	CODE_30010003(ErrorCode.CODE_30010003, "错误的购买类型"),
	CODE_30010004(ErrorCode.CODE_30010004, "购买的会员等级不能小于用户当前的会员等级"),
	CODE_30010005(ErrorCode.CODE_30010005, "当前会员已不能兑换"),

	// 验证码相关
	CODE_30011001(ErrorCode.CODE_30011001, "验证码校验未通过，请重新获取验证码"),

	// 项目相关
	CODE_30012001(ErrorCode.CODE_30012001, "买方信息不存在"),
	CODE_30012002(ErrorCode.CODE_30012002, "项目不存在"),

	// 微信相关
	CODE_30013001(ErrorCode.CODE_30013001, "微信公众号生成签名异常"),

	// 印章管理相关
	CODE_30014001(ErrorCode.CODE_30014001, "主账号不存在"),
	CODE_30014002(ErrorCode.CODE_30014002, "seal image not found"),
	CODE_30014003(ErrorCode.CODE_30014003, "从第三方平台下载印章图片异常"),
	CODE_30014004(ErrorCode.CODE_30014004, "调用第三方接口自动创建印章失败"),
	CODE_30014005(ErrorCode.CODE_30014005, "调用第三方接口自动创建印章异常"),
	CODE_30014006(ErrorCode.CODE_30014006, "调用第三方接口查询印章详情异常"),
	CODE_30014007(ErrorCode.CODE_30014007, "调用第三方接口查询印章详情失败"),
	CODE_30014008(ErrorCode.CODE_30014008, "调用三方接口编辑印章异常"),
	CODE_30014009(ErrorCode.CODE_30014009, "调用三方接口编辑印章失败"),

	// 子账号管理相关
	CODE_30015001(ErrorCode.CODE_30015001, "调用第三方接口移除子账号异常"),
	CODE_30015002(ErrorCode.CODE_30015002, "调用第三方接口移除子账号失败"),
	CODE_30015003(ErrorCode.CODE_30015003, "调用第三方接口邀请子账号异常"),
	CODE_30015004(ErrorCode.CODE_30015004, "调用第三方接口邀请子账号失败"),
	CODE_30015005(ErrorCode.CODE_30015005, "用户未登录,请登录后再试"),

	// 运价综合指数配置版本相关
	CODE_30016001(ErrorCode.CODE_30016001, "版本不存在"),
	CODE_30016002(ErrorCode.CODE_30016002, "版本已生效不能进行启用"),
	CODE_30016003(ErrorCode.CODE_30016003, "版本已失效不能进行修改"),

	// 砂石综合指数配置版本相关
	CODE_30017001(ErrorCode.CODE_30017001, "版本不存在"),
	CODE_30017002(ErrorCode.CODE_30017002, "版本已生效不能进行启用"),
	CODE_30017003(ErrorCode.CODE_30017003, "版本已失效不能进行修改"),

	// 船舶群组相关
	CODE_30018001(ErrorCode.CODE_30018001, "群组不存在"),
	CODE_30018002(ErrorCode.CODE_30018002, "群组名不能为空"),
	CODE_30018003(ErrorCode.CODE_30018003, "群组名不能超过25个字符"),
	CODE_30018004(ErrorCode.CODE_30018004, "群组船舶颜色不能为空"),
	CODE_30018005(ErrorCode.CODE_30018005, "该群组名已经存在"),
	CODE_30018006(ErrorCode.CODE_30018006, "最多可新增20个群组"),
	CODE_30018007(ErrorCode.CODE_30018007, "固定群组不能进行操作"),
	CODE_30018008(ErrorCode.CODE_30018008, "颜色不存在"),
	CODE_30018009(ErrorCode.CODE_30018009, "每个群组最多只能添加200条数据"),

	// 船舶关注相关
	CODE_30019001(ErrorCode.CODE_30019001, "群组id不能为空"),
	CODE_30019002(ErrorCode.CODE_30019002, "船舶状态不能为空"),
	CODE_30019003(ErrorCode.CODE_30019003, "船舶状态不存在"),
	CODE_30019004(ErrorCode.CODE_30019004, "备注字符长度不能超过100个字符"),
	CODE_30019005(ErrorCode.CODE_30019005, "船舶id不能为空"),
	CODE_30019006(ErrorCode.CODE_30019006, "船舶关注信息不存在"),
	CODE_30019007(ErrorCode.CODE_30019007, "该群组你无权限添加"),
	CODE_30019008(ErrorCode.CODE_30019008, "船舶已经被关注"),
	CODE_30019009(ErrorCode.CODE_30019009, "无权操作他人数据"),

	//通知管理相关
	CODE_30020001(ErrorCode.CODE_30020001, "通知信息不存在"),
	CODE_30020002(ErrorCode.CODE_30020002, "标题不能为空，且最大为50个字符"),
	CODE_30020003(ErrorCode.CODE_30020003, "分类不能为空"),
	CODE_30020004(ErrorCode.CODE_30020004, "分类不存在"),
	CODE_30020005(ErrorCode.CODE_30020005, "内容不能为空"),
	CODE_30020006(ErrorCode.CODE_30020006, "定时发布时间只能选择未来时间"),
	CODE_30020007(ErrorCode.CODE_30020007, "草稿状态才能进行编辑"),
	CODE_30020008(ErrorCode.CODE_30020008, "待发布状态才能进行取消发布"),
	CODE_30020009(ErrorCode.CODE_30020009, "待发布状态不能进行删除"),
	CODE_30020010(ErrorCode.CODE_30020010, "已发布状态才能进行撤回"),
	CODE_30020011(ErrorCode.CODE_30020011, "撤回失败，发布后7天内才可撤回"),
	CODE_30020012(ErrorCode.CODE_30020012, "草稿状态才能进行发布"),
	CODE_30020013(ErrorCode.CODE_30020013, "参数错误"),

	//砂石学院相关
	CODE_30021001(ErrorCode.CODE_30021001, "砂石学院数据不存在"),
	CODE_30021002(ErrorCode.CODE_30021002, "标题不能为空，且最大为50个字符"),
	CODE_30021003(ErrorCode.CODE_30021003, "收费类型不能为空"),
	CODE_30021004(ErrorCode.CODE_30021004, "收费类型不存在"),
	CODE_30021005(ErrorCode.CODE_30021005, "展示位置不存在"),
	CODE_30021006(ErrorCode.CODE_30021006, "标签数量超过10个"),
	CODE_30021007(ErrorCode.CODE_30021007, "来源长度不超过10个字符"),
	CODE_30021008(ErrorCode.CODE_30021008, "发布日期不能为空"),
	CODE_30021009(ErrorCode.CODE_30021009, "略缩图不能为空"),
	CODE_30021010(ErrorCode.CODE_30021010, "摘要长度不超过200个字符"),
	CODE_30021011(ErrorCode.CODE_30021011, "内容不能为空"),
	CODE_30021012(ErrorCode.CODE_30021012, "免责声明长度不超过300个字符"),
	CODE_30021013(ErrorCode.CODE_30021013, "下架状态才能编辑"),
	CODE_30021014(ErrorCode.CODE_30021014, "下架状态才能删除"),
	CODE_30021015(ErrorCode.CODE_30021015, "存在重复标签"),
	CODE_30021016(ErrorCode.CODE_30021016, "参数错误"),

	// 机构认证相关
	CODE_30022001(ErrorCode.CODE_30022001, "机构认证不存在"),
	CODE_30022002(ErrorCode.CODE_30022002, "状态不能为空"),
	CODE_30022003(ErrorCode.CODE_30022003, "状态不存在"),
	CODE_30022004(ErrorCode.CODE_30022004, "备注不能超过100个字符"),
	CODE_30022005(ErrorCode.CODE_30022005, "营业执照不能为空"),
	CODE_30022006(ErrorCode.CODE_30022006, "企业名称不能为空"),
	CODE_30022007(ErrorCode.CODE_30022007, "企业名称不能超过40个字符"),
	CODE_30022008(ErrorCode.CODE_30022008, "统一社会信用代码不能为空"),
	CODE_30022009(ErrorCode.CODE_30022009, "统一社会信用代码必须为18位"),
	CODE_30022010(ErrorCode.CODE_30022010, "法定代表人不能为空"),
	CODE_30022011(ErrorCode.CODE_30022011, "法定代表人不能超过10个字符"),
	CODE_30022012(ErrorCode.CODE_30022012, "该企业已被认证"),
	CODE_30022013(ErrorCode.CODE_30022013, "待审核状态才能进行审核"),
	CODE_30022014(ErrorCode.CODE_30022014, "无权限"),
	CODE_30022015(ErrorCode.CODE_30022015, "已驳回状态才能进行修改"),
	CODE_30022016(ErrorCode.CODE_30022016, "已认证"),
	CODE_30022017(ErrorCode.CODE_30022017, "已解除机构认证，或未认证才能进行新增"),
	CODE_30022018(ErrorCode.CODE_30022018, "备注不能为空"),
	CODE_30022019(ErrorCode.CODE_30022019, "未完成个人认证"),
	CODE_30022020(ErrorCode.CODE_30022020, "您还未完成本系统的组织机构认证"),
	CODE_30022021(ErrorCode.CODE_30022021, "营业执照不合法"),
	CODE_30022022(ErrorCode.CODE_30022022, "授权委托书不能为空"),

	// 客户船舶关注相关
	CODE_30023001(ErrorCode.CODE_30023001, "超出关注数量"),
	CODE_30023002(ErrorCode.CODE_30023002, "船舶关注不存在"),
	CODE_30023003(ErrorCode.CODE_30023003, "该船不是您关注的"),


	// 搜索发现相关
	CODE_30024001(ErrorCode.CODE_30024001, "搜索发现不存在"),
	CODE_30024002(ErrorCode.CODE_30024002, "关键词不能为空"),
	CODE_30024003(ErrorCode.CODE_30024003, "关键词不能超过10个字符"),
	CODE_30024004(ErrorCode.CODE_30024004, "关键词不能重复"),
	CODE_30024005(ErrorCode.CODE_30024005, "类型不能为空"),
	CODE_30024006(ErrorCode.CODE_30024006, "类型不存在"),
	CODE_30024007(ErrorCode.CODE_30024007, "业务id不能为空"),
	CODE_30024008(ErrorCode.CODE_30024008, "置顶数已达上限"),
	CODE_30024009(ErrorCode.CODE_30024009, "未置顶才能进行编辑"),
	CODE_30024010(ErrorCode.CODE_30024010, "未置顶才能进行删除"),
	CODE_30024011(ErrorCode.CODE_30024011, "置顶的关键词才能增加点击"),

	// 留言反馈相关
	CODE_30025001(ErrorCode.CODE_30025001, "留言反馈数据不存在"),
	CODE_30025002(ErrorCode.CODE_30025002, "留言类型不能为空"),
	CODE_30025003(ErrorCode.CODE_30025003, "留言类型不存在"),
	CODE_30025004(ErrorCode.CODE_30025004, "留言内容不能为空，且最大为150字符"),
	CODE_30025005(ErrorCode.CODE_30025005, "最大上传6张图片"),
	CODE_30025006(ErrorCode.CODE_30025006, "待回复状态才能进行回复"),
	CODE_30025007(ErrorCode.CODE_30025007, "回复内容不能为空，且最大为200字符"),

	// 功能反馈相关
	CODE_30026001(ErrorCode.CODE_30026001, "类型不存在"),
	CODE_30026002(ErrorCode.CODE_30026002, "补充说明字数不能超过50"),
	CODE_30026003(ErrorCode.CODE_30026003, "反馈原因不存在"),
	CODE_30026004(ErrorCode.CODE_30026004, "类型不能为空"),
	CODE_30026005(ErrorCode.CODE_30026005, "反馈原因不能为空"),
	CODE_30026006(ErrorCode.CODE_30026006, "反馈原因不能多选"),
	CODE_30026007(ErrorCode.CODE_30026007, "该数据不存在"),
	CODE_30026008(ErrorCode.CODE_30026008, "该数据状态为已处理"),
	CODE_30026009(ErrorCode.CODE_30026009, "备注不能为空"),
	CODE_30026010(ErrorCode.CODE_30026010, "备注不能超过200个字符"),
	CODE_30026011(ErrorCode.CODE_30026011, "状态码错误"),
	CODE_30026012(ErrorCode.CODE_30026012, "油品订单ID不能为空"),

	// 帮助中心相关
	CODE_30027001(ErrorCode.CODE_30027001, "帮助中心数据不存在"),
	CODE_30027002(ErrorCode.CODE_30027002, "标题不能为空，且最大为50个字符"),
	CODE_30027003(ErrorCode.CODE_30027003, "内容不能为空"),
	CODE_30027004(ErrorCode.CODE_30027004, "下架状态才能编辑"),
	CODE_30027005(ErrorCode.CODE_30027005, "下架状态才能删除"),
	CODE_30027006(ErrorCode.CODE_30027006, "参数错误"),

	// 平台船运需求相关
	// 平台船运需求ID不能为空
	CODE_30028001(ErrorCode.CODE_30028001, "平台船运需求ID不能为空"),
	CODE_30028002(ErrorCode.CODE_30028002, "抢单后不能进行资源反馈"),
	CODE_30028003(ErrorCode.CODE_30028003, "船主接单ID不能为空"),

	// 货主船运需求相关
	// 货主船运需求ID不能为空
	CODE_30029001(ErrorCode.CODE_30029001, "货主船运需求ID不能为空"),

	// AI热词相关
	// AI热词不能为空
	CODE_30030001(ErrorCode.CODE_30030001, "AI热词不能为空"),
	// AI热词长度应为1-10个字符
	CODE_30030002(ErrorCode.CODE_30030002, "AI热词长度应为1-10个字符"),
	// 权重值不能为空
	CODE_30030003(ErrorCode.CODE_30030003, "权重值不能为空"),
	// 热词数据不存在
	CODE_30030004(ErrorCode.CODE_30030004, "热词数据不存在"),
	// 该热词数据已存在
	CODE_30030005(ErrorCode.CODE_30030005, "该热词已存在"),

	// 向量存储器
	CODE_30031001(ErrorCode.CODE_30031001, "向量存储名称不能为空且不能超过16个字符"),
	CODE_30031002(ErrorCode.CODE_30031002, "向量存储器名称不能为空"),
	CODE_30031003(ErrorCode.CODE_30031003, "向量存储器不存在"),
	CODE_30031004(ErrorCode.CODE_30031004, "文件不能为空"),
	CODE_30031005(ErrorCode.CODE_30031005, "助手名称不能为空 长度为1~20个字符"),
	CODE_30031006(ErrorCode.CODE_30031006, "指令不能为空 长度为1~500个字符"),
	CODE_30031007(ErrorCode.CODE_30031007, "模型不能为空"),
	CODE_30031008(ErrorCode.CODE_30031008, "fileSearch参数不能为空 只能为0和1"),
	CODE_30031009(ErrorCode.CODE_30031009, "miniProgram参数不能为空 只能为0和1"),
	CODE_30031010(ErrorCode.CODE_30031010, "向量存储不能为空"),
	CODE_30031011(ErrorCode.CODE_30031011, "智能助理不存在"),

	// 轮播图相关
	CODE_30032001(ErrorCode.CODE_30032001, "轮播图位置不能为空"),
	CODE_30032002(ErrorCode.CODE_30032002, "轮播图位置不存在"),
	CODE_30032003(ErrorCode.CODE_30032003, "轮播图文件id不能为空"),
	CODE_30032004(ErrorCode.CODE_30032004, "排序值不能为空"),
	CODE_30032005(ErrorCode.CODE_30032005, "跳转类型不能为空"),
	CODE_30032006(ErrorCode.CODE_30032006, "跳转类型只能为1到6"),
	CODE_30032007(ErrorCode.CODE_30032007, "内容不能为空"),
	CODE_30032008(ErrorCode.CODE_30032008, "轮播图不存在"),
	CODE_30032009(ErrorCode.CODE_30032009, "承运商跳转类型不能为商品"),

	// 广告位相关
	CODE_30033001(ErrorCode.CODE_30033001, "广告位不存在"),
	CODE_30033002(ErrorCode.CODE_30033002, "广告名称不能为空"),
	CODE_30033003(ErrorCode.CODE_30033003, "展现形式不能为空"),
	CODE_30033004(ErrorCode.CODE_30033004, "轮播间隔不能为空"),
	CODE_30033005(ErrorCode.CODE_30033005, "广告周期不能为空"),
	CODE_30033006(ErrorCode.CODE_30033006, "广告图片不能为空"),
	CODE_30033007(ErrorCode.CODE_30033007, "排序不能为空"),
	CODE_30033008(ErrorCode.CODE_30033008, "广告位已存在"),
	CODE_30033009(ErrorCode.CODE_30033009, "广告不能为空"),
	CODE_30033010(ErrorCode.CODE_30033010, "排序只能范围1-5"),
	CODE_30033011(ErrorCode.CODE_30033011, "排序字段存在重复值"),
	CODE_30033012(ErrorCode.CODE_30033012, "展现形式类型不存在"),
	CODE_30033013(ErrorCode.CODE_30033013, "轮播间隔类型不存在"),
	CODE_30033014(ErrorCode.CODE_30033014, "标题不能为空 长度为1-50个字符"),
	CODE_30033015(ErrorCode.CODE_30033015, "分类不能为空"),
	CODE_30033016(ErrorCode.CODE_30033016, "广告类型不能为空"),
	CODE_30033017(ErrorCode.CODE_30033017, "上传图片不能为空"),
	CODE_30033018(ErrorCode.CODE_30033018, "按钮值不能为空并且长度不能超过15个字符"),
	CODE_30033019(ErrorCode.CODE_30033019, "广告信息不存在"),
	CODE_30033020(ErrorCode.CODE_30033020, "链接类型不存在"),
	CODE_30033021(ErrorCode.CODE_30033021, "广告页不能为空"),
	CODE_30033022(ErrorCode.CODE_30033022, "链接不能为空"),
	CODE_30033023(ErrorCode.CODE_30033023, "图片不能为空"),
	CODE_30033024(ErrorCode.CODE_30033024, "是否需要按钮不能为空"),
	CODE_30033025(ErrorCode.CODE_30033025, "按钮类型不能为空"),
	CODE_30033026(ErrorCode.CODE_30033026, "按钮名称不能为空并且长度不能超过15个字符"),
	CODE_30033027(ErrorCode.CODE_30033027, "分类不存在"),
	CODE_30033028(ErrorCode.CODE_30033028, "广告类型不存在"),
	CODE_30033029(ErrorCode.CODE_30033029, "按钮类型不存在"),
	CODE_30033030(ErrorCode.CODE_30033030, "广告位位置类型不能为空"),
	CODE_30033031(ErrorCode.CODE_30033031, "广告位位置类型不存在"),
	CODE_30033032(ErrorCode.CODE_30033032, "广告位号数不存在"),
	CODE_30033033(ErrorCode.CODE_30033033, "该广告位无法设置多图轮播"),
	CODE_30033034(ErrorCode.CODE_30033034, "无法设置3号广告位"),

	// 设备申领相关
	CODE_30034001(ErrorCode.CODE_30034001, "该认领数据已绑定设备,请核实后再试!"),
	CODE_30034002(ErrorCode.CODE_30034002, "该设备已操作过申领绑定,请核实后再试!"),
	CODE_30034003(ErrorCode.CODE_30034003, "该认领数据不存在!"),
	CODE_30034004(ErrorCode.CODE_30034004, "申领id不能为空"),
	CODE_30034005(ErrorCode.CODE_30034005, "设备id不能为空"),
	CODE_30034006(ErrorCode.CODE_30034006, "安装位置不能为空"),
	CODE_30034007(ErrorCode.CODE_30034007, "安装位置长度不能超过10个字符"),
	CODE_30034008(ErrorCode.CODE_30034008, "推介人和船主不能是同一个人"),
	CODE_30034009(ErrorCode.CODE_30034009, "船舶未认证不能申领设备!"),
	CODE_30034010(ErrorCode.CODE_30034010, "船舶已绑定设备无需再次进行申领操作!"),
	CODE_30034011(ErrorCode.CODE_30034011, "船舶已申请过申领,请勿重复操作!"),
	CODE_30034012(ErrorCode.CODE_30034012, "推荐人不能填写本人手机号"),

	// 预付款相关
	CODE_30035001(ErrorCode.CODE_30035001, "金额不能为空 取值范围为0.01~10000000000"),
	CODE_30035002(ErrorCode.CODE_30035002, "日期不能为空"),
	CODE_30035003(ErrorCode.CODE_30035003, "平台银行账号不能为空"),
	CODE_30035004(ErrorCode.CODE_30035004, "平台银行账号不存在"),
	CODE_30035005(ErrorCode.CODE_30035005, "预付款账号信息不能为空"),
	CODE_30035006(ErrorCode.CODE_30035006, "开户名称不能为空 最大25个字符"),
	CODE_30035007(ErrorCode.CODE_30035007, "银行账号不能为空 最多8到30个数字"),
	CODE_30035008(ErrorCode.CODE_30035008, "开户行不能为空 最大128个字符"),
	CODE_30035009(ErrorCode.CODE_30035009, "用户账户id不能为空"),
	CODE_30035010(ErrorCode.CODE_30035010, "用户账户不存在"),
	CODE_30035011(ErrorCode.CODE_30035011, "预付款id不能为空"),
	CODE_30035012(ErrorCode.CODE_30035012, "撤销备注不能为空,最多一百个字符"),
	CODE_30035013(ErrorCode.CODE_30035013, "预付款信息不存在"),

	// 印章管理相关
	CODE_30036001(ErrorCode.CODE_30036001, "主账号未通过企业认证,请先通过认证页面进行企业认证后再创建印章") ,
	CODE_30036002(ErrorCode.CODE_30036002, "印章使用者不存在或不属于当前主账号的子账号") ,
	CODE_30036003(ErrorCode.CODE_30036003, "子账号不存在") ,
	CODE_30036004(ErrorCode.CODE_30036004, "子账号未通过个人实名认证,请子账号用户先通过实名认证页面进行个人实名认证后再将其指定为印章使用者") ,
	CODE_30036005(ErrorCode.CODE_30036005, "关联业务必须选择对应的印章使用者") ,
	CODE_30036006(ErrorCode.CODE_30036006, "当前业务已经被其他印章关联") ,
	CODE_30036007(ErrorCode.CODE_30036007, "当前主账号未关联任何子账号,请先关联子账号后再指定印章使用者") ,
	CODE_30036008(ErrorCode.CODE_30036008, "印章名称不能为空") ,
	CODE_30036009(ErrorCode.CODE_30036009, "印章名称长度必须在1-50个字符之间") ,
	CODE_30036010(ErrorCode.CODE_30036010, "印章样式不能为空") ,
	CODE_30036011(ErrorCode.CODE_30036011, "下方横排文字长度必须在1-50个字符之间") ,
	CODE_30036012(ErrorCode.CODE_30036012, "信息编码长度必须在1-50个字符之间") ,
	CODE_30036013(ErrorCode.CODE_30036013, "印章不存在") ,
	CODE_30036014(ErrorCode.CODE_30036014, "存在签署中的单据，暂不支持修改") ,
	CODE_30036015(ErrorCode.CODE_30036015, "该印章存在签署中的业务单据，不可删除") ,
	CODE_30036016(ErrorCode.CODE_30036016, "发起签署失败，请修改印章使用者") ,
	CODE_30036017(ErrorCode.CODE_30036017, "失效印章不能进行修改") ,

	// 设备相关
	CODE_30037001(ErrorCode.CODE_30037001, "设备名称不能为空"),
	CODE_30037002(ErrorCode.CODE_30037002, "设备名称不超过40个字"),
	CODE_30037003(ErrorCode.CODE_30037003, "设备类型不能为空"),
	CODE_30037004(ErrorCode.CODE_30037004, "状态不能为空"),
	CODE_30037005(ErrorCode.CODE_30037005, "SMI卡号不能为空"),
	CODE_30037006(ErrorCode.CODE_30037006, "SMI卡号格式错误"),
	CODE_30037007(ErrorCode.CODE_30037007, "序列号不能为空"),
	CODE_30037008(ErrorCode.CODE_30037008, "序列号默认9个字符限制"),
	CODE_30037009(ErrorCode.CODE_30037009, "验证码不能为空"),
	CODE_30037010(ErrorCode.CODE_30037010, "设备不存在"),
	CODE_30037011(ErrorCode.CODE_30037011, "只有设备状态为非运行状态才能删除"),
	CODE_30037012(ErrorCode.CODE_30037012, "SMI卡号已存在"),
	CODE_30037013(ErrorCode.CODE_30037013, "序列号已存在"),
	CODE_30037014(ErrorCode.CODE_30037014, "状态为禁用才能启用"),
	CODE_30037015(ErrorCode.CODE_30037015, "状态为启用才能禁用"),
	CODE_30037016(ErrorCode.CODE_30037016, "运输工具id不能为空"),
	CODE_30037017(ErrorCode.CODE_30037017, "运输工具类型不能为空"),
	CODE_30037018(ErrorCode.CODE_30037018, "位置不能为空"),
	CODE_30037019(ErrorCode.CODE_30037019, "设备已经被禁用"),
	CODE_30037020(ErrorCode.CODE_30037020, "设备已经被绑定"),
	CODE_30037021(ErrorCode.CODE_30037021, "注册码不存在"),
	CODE_30037022(ErrorCode.CODE_30037022, "宇视平台内部服务器错误"),
	CODE_30037023(ErrorCode.CODE_30037023, "宇视平台接口调用失败"),
	CODE_30037024(ErrorCode.CODE_30037024, "该设备已绑定船舶，请先解绑船舶"),
	CODE_30037025(ErrorCode.CODE_30037025, "设备类型不存在"),
	CODE_30037026(ErrorCode.CODE_30037026, "设备不在线"),
	CODE_30037027(ErrorCode.CODE_30037027, "验证码格式错误"),
	CODE_30037028(ErrorCode.CODE_30037028, "位置不能超过20个字符"),
	CODE_30037029(ErrorCode.CODE_30037029, "船舶未认证"),
	CODE_30037030(ErrorCode.CODE_30037030, "安装人最多10个字符长度"),
	CODE_30037031(ErrorCode.CODE_30037031, "安装地点最多32个字符长度"),
	CODE_30037032(ErrorCode.CODE_30037032, "图片向量不能为空"),
	CODE_30037033(ErrorCode.CODE_30037033, "图片名称不能为空"),
	CODE_30037034(ErrorCode.CODE_30037034, "抓拍图片不能为空"),
	CODE_30037035(ErrorCode.CODE_30037035, "图片标记不能为空"),
	CODE_30037036(ErrorCode.CODE_30037036, "模型数据id不能为空"),
	CODE_30037037(ErrorCode.CODE_30037037, "模型数据标签不能为空"),
	CODE_30037038(ErrorCode.CODE_30037038, "模型数据不存在"),
	CODE_30037039(ErrorCode.CODE_30037039, "识别记录不存在"),
	CODE_30037040(ErrorCode.CODE_30037040, "识别记录已加入模型"),
	CODE_30037041(ErrorCode.CODE_30037041, "已进入契约锁认证不支持取消"),
	CODE_30037042(ErrorCode.CODE_30037042, "机构认证还未通过或未进行认证"),
	CODE_30037043(ErrorCode.CODE_30037043, "数据不能为空"),
	CODE_30037044(ErrorCode.CODE_30037044, "已注销账号才能进行恢复"),
	CODE_30037045(ErrorCode.CODE_30037045, "此船舶已绑定其他设备"),
	CODE_30037046(ErrorCode.CODE_30037046, "该船舶没有绑定此设备，无法解绑"),
	CODE_30037047(ErrorCode.CODE_30037047, "设备ID不能为空"),
	CODE_30037048(ErrorCode.CODE_30037048, "标志不能为空"),
	CODE_30037049(ErrorCode.CODE_30037049, "设备没有被绑定，无须进行解绑"),

	// 定制管理相关
	CODE_30038001(ErrorCode.CODE_30038001, "平台用户id不能为空"),
	CODE_30038002(ErrorCode.CODE_30038002, "后台id不能为空") ,
	CODE_30038003(ErrorCode.CODE_30038003, "系统名称不能为空") ,
	CODE_30038004(ErrorCode.CODE_30038004, "系统名称长度最大为20") ,
	CODE_30038005(ErrorCode.CODE_30038005, "系统简称长度最大为10") ,
	CODE_30038006(ErrorCode.CODE_30038006, "系统头部slogan不能为空") ,
	CODE_30038007(ErrorCode.CODE_30038007, "系统头部slogan长度最大为20") ,
	CODE_30038008(ErrorCode.CODE_30038008, "AI助理名称长度最大为20") ,
	CODE_30038009(ErrorCode.CODE_30038009, "服务热线不能为空") ,
	CODE_30038010(ErrorCode.CODE_30038010, "服务热线长度最大为20") ,
	CODE_30038011(ErrorCode.CODE_30038011, "版权信息不能为空") ,
	CODE_30038012(ErrorCode.CODE_30038012, "版权信息长度最大为50") ,
	CODE_30038013(ErrorCode.CODE_30038013, "access_key长度最大为50") ,
	CODE_30038014(ErrorCode.CODE_30038014, "secret_key长度最大为50") ,
	CODE_30038015(ErrorCode.CODE_30038015, "登录短信模板ID长度最大为20") ,
	CODE_30038016(ErrorCode.CODE_30038016, "私有域名长度最大为20") ,
	CODE_30038017(ErrorCode.CODE_30038017, "custom信息不能为空") ,
	CODE_30038018(ErrorCode.CODE_30038018, "user信息不能为空") ,
	CODE_30038019(ErrorCode.CODE_30038019, "用户pc端登录页logo不能为空") ,
	CODE_30038020(ErrorCode.CODE_30038020, "用户pc端登录页登录图片不能为空") ,
	CODE_30038021(ErrorCode.CODE_30038021, "用户pc端公众页头部LOGO不能为空") ,
	CODE_30038022(ErrorCode.CODE_30038022, "用户pc端公众页首页背景图不能为空") ,
	CODE_30038023(ErrorCode.CODE_30038023, "用户pc端公众页AI形象不能为空") ,
	CODE_30038024(ErrorCode.CODE_30038024, "用户pc端公众页底部LOGO不能为空") ,
	CODE_30038025(ErrorCode.CODE_30038025, "用户pc端控制台头部LOGO不能为空") ,
	CODE_30038026(ErrorCode.CODE_30038026, "用户pc端控制台账号头像不能为空") ,
	CODE_30038027(ErrorCode.CODE_30038027, "管理后台登录页登录页LOGO不能为空") ,
	CODE_30038028(ErrorCode.CODE_30038028, "管理后台登录页登录图片不能为空") ,
	CODE_30038029(ErrorCode.CODE_30038029, "管理后台登录页头部导航栏LOGO不能为空") ,
	CODE_30038030(ErrorCode.CODE_30038030, "企业定制表id不能为空") ,
	CODE_30038031(ErrorCode.CODE_30038031, "ICP备案信息不能为空") ,
	CODE_30038032(ErrorCode.CODE_30038032, "ICP备案信息长度最大为20") ,
	CODE_30038033(ErrorCode.CODE_30038033, "联网备案信息不能为空") ,
	CODE_30038034(ErrorCode.CODE_30038034, "联网备案信息长度最大为20") ,
	CODE_30038035(ErrorCode.CODE_30038035, "该用户已被关联，请勿重复操作") ,
	CODE_30038036(ErrorCode.CODE_30038036, "短信签名长度最大为12") ,
	CODE_30038037(ErrorCode.CODE_30038037, "用户pc端公众页AI头像不能为空") ,
	CODE_30038038(ErrorCode.CODE_30038038, "企业样式信息不存在") ,
	CODE_30038039(ErrorCode.CODE_30038039, "企业样式信息已存在") ,
	CODE_30038040(ErrorCode.CODE_30038040, "企业定制表不存在") ,
	CODE_30038041(ErrorCode.CODE_30038041, "成员数量最大为10") ,
	CODE_30038042(ErrorCode.CODE_30038042, "成员已存在") ,
	CODE_30038043(ErrorCode.CODE_30038043, "成员不存在") ,

	// 关于我们相关
	CODE_30039001(ErrorCode.CODE_30039001, "文章id不能为空"),
	CODE_30039002(ErrorCode.CODE_30039002, "内容富文本不能为空"),
	CODE_30039003(ErrorCode.CODE_30039003, "属于哪个app不能为空"),
	CODE_30039004(ErrorCode.CODE_30039004, "文章类型不能为空"),
	CODE_30039005(ErrorCode.CODE_30039005, "同一账号下同一类型不能重复添加"),

	// 文件相关
	CODE_30040001(ErrorCode.CODE_30040001, "文件最大为100M"),
	CODE_30040002(ErrorCode.CODE_30040002, "文件不存在"),
	CODE_30040003(ErrorCode.CODE_30040003, "该导入模板不存在"),

	//常跑航线相关
	CODE_30041001(ErrorCode.CODE_30041001, "该常跑航线不存在"),
	CODE_30041002(ErrorCode.CODE_30041002, "常跑航线最多只能传入3条"),

	// 船舶已经存在生效推广
	CODE_30042001(ErrorCode.CODE_30042001, "船舶已经存在生效推广"),
	CODE_30042002(ErrorCode.CODE_30042002, "推广记录状态为失效,不允许撤销激活"),
	CODE_30042003(ErrorCode.CODE_30042003, "撤销理由不能超过200个字符"),
	CODE_30042004(ErrorCode.CODE_30042004, "推广记录不存在"),

	// 监控预警记录相关
	CODE_30043001(ErrorCode.CODE_30043001, "监控预警记录不存在"),
	CODE_30043002(ErrorCode.CODE_30043002, "已处理状态不可再指派"),
	CODE_30043003(ErrorCode.CODE_30043003, "已处理状态不可二次处理"),
	CODE_30043004(ErrorCode.CODE_30043004, "监控预警记录id不能为空"),
	CODE_30043005(ErrorCode.CODE_30043005, "备注不能超过200个字符"),
	CODE_30043006(ErrorCode.CODE_30043006, "请确认是否误报"),
	CODE_30043007(ErrorCode.CODE_30043007, "未处理状态不可修改"),
	CODE_30043008(ErrorCode.CODE_30043008, "备注不能为空"),
	CODE_30043009(ErrorCode.CODE_30043009, "当前用户不是处理人，无修改权限"),
	CODE_30043010(ErrorCode.CODE_30043010, "不是监控预警记录的处理人，不能处理"),

	// 船运定金
	CODE_30044001(ErrorCode.CODE_30044001, "船运定金不存在"),
	CODE_30044002(ErrorCode.CODE_30044002, "船运单id不能为空"),
	CODE_30044003(ErrorCode.CODE_30044003, "类型不能为空"),
	CODE_30044004(ErrorCode.CODE_30044004, "结算凭证不能为空"),
	CODE_30044005(ErrorCode.CODE_30044005, "类型不存在"),
	CODE_30044006(ErrorCode.CODE_30044006, "金额不能为空"),
	CODE_30044007(ErrorCode.CODE_30044007, "定金已经存在"),
	CODE_30044008(ErrorCode.CODE_30044008, "待支付状态才能取消"),
	CODE_30044009(ErrorCode.CODE_30044009, "必须先支付定金1"),
	CODE_30044010(ErrorCode.CODE_30044010, "待对账状态才能重新支付"),
	CODE_30044011(ErrorCode.CODE_30044011, "支付成功才能进行确认"),
	CODE_30044012(ErrorCode.CODE_30044012, "必须先支付定金"),
	CODE_30044013(ErrorCode.CODE_30044013, "支付类型错误"),
	CODE_30044014(ErrorCode.CODE_30044014, "船主已经确认"),
	CODE_30044015(ErrorCode.CODE_30044015, "此承运商未上传银行账户信息"),
	CODE_30044016(ErrorCode.CODE_30044016, "黄码港船舶还未通过审核"),
	CODE_30044017(ErrorCode.CODE_30044017, "黄码港船运单未通过审核"),
	CODE_30044018(ErrorCode.CODE_30044018, "待卸货、卸货中、已清仓、已完成状态才能支付"),
	CODE_30044019(ErrorCode.CODE_30044019, "待支付定金状态才能支付"),
	CODE_30044020(ErrorCode.CODE_30044020, "待支付信息服务费状态到运输中状态才能支付"),
	CODE_30044021(ErrorCode.CODE_30044021, "船主没有签署代开协议不能进行支付"),
	CODE_30044022(ErrorCode.CODE_30044022, "传入的船运单id不是本平台推送的船运单"),
	CODE_30044023(ErrorCode.CODE_30044023, "当前船运单的船运费用支付方式不是自行支付"),

	// 订单流水详情
	CODE_30045001(ErrorCode.CODE_30045001, "支付类型不能为空"),
	CODE_30045002(ErrorCode.CODE_30045002, "业务类型不能为空"),
	CODE_30045003(ErrorCode.CODE_30045003, "支付金额不能为空"),
	CODE_30045004(ErrorCode.CODE_30045004, "当前订单不是未支付状态"),
	CODE_30045005(ErrorCode.CODE_30045005, "支付明细字符长度1-255个字符"),
	CODE_30045006(ErrorCode.CODE_30045006, "描述不能为空"),
	CODE_30045007(ErrorCode.CODE_30045007, "描述字符长度为1-255个字符"),
	CODE_30045008(ErrorCode.CODE_30045008, "明细不能为空"),
	CODE_30045009(ErrorCode.CODE_30045009, "订单不存在"),
	CODE_30045010(ErrorCode.CODE_30045010, "支付类型不存在"),
	CODE_30045011(ErrorCode.CODE_30045011, "业务类型不存在"),
	CODE_30045012(ErrorCode.CODE_30045012, "来源不能为空"),
	CODE_30045013(ErrorCode.CODE_30045013, "来源不存在"),
	CODE_30045014(ErrorCode.CODE_30045014, "类型不存在"),

	// 码头相关
	CODE_30046001(ErrorCode.CODE_30046001, "码头不存在"),
	CODE_30046002(ErrorCode.CODE_30046002, "码头名称不能为空 长度为1-40个字符"),
	CODE_30046003(ErrorCode.CODE_30046003, "码头简称不能为空 长度为1-20个字符"),
	CODE_30046004(ErrorCode.CODE_30046004, "码头面积长度最多为16位"),
	CODE_30046005(ErrorCode.CODE_30046005, "联系人姓名长度最大为10位"),
	CODE_30046006(ErrorCode.CODE_30046006, "联系人手机号长度最大为16位"),
	CODE_30046007(ErrorCode.CODE_30046007, "省编码不能为空"),
	CODE_30046008(ErrorCode.CODE_30046008, "城市编码不能为空"),
	CODE_30046009(ErrorCode.CODE_30046009, "区域编码不能为空"),
	CODE_30046010(ErrorCode.CODE_30046010, "详细地址不能为空"),
	CODE_30046011(ErrorCode.CODE_30046011, "经纬度不能为空"),
	CODE_30046012(ErrorCode.CODE_30046012, "简介长度最大为256位"),
	CODE_30046013(ErrorCode.CODE_30046013, "地址全称不能为空"),
	CODE_30046014(ErrorCode.CODE_30046014, "码头id不能为空"),

	// 砂石资讯相关
	CODE_30047001(ErrorCode.CODE_30047001, "数据不存在"),
	CODE_30047002(ErrorCode.CODE_30047002, "标题不能为空 长度为1-50个字符"),
	CODE_30047003(ErrorCode.CODE_30047003, "发布日期不能为空"),
	CODE_30047004(ErrorCode.CODE_30047004, "品类不能为空"),
	CODE_30047005(ErrorCode.CODE_30047005, "来源长度最大为10个字符"),
	CODE_30047006(ErrorCode.CODE_30047006, "类型不能为空"),
	CODE_30047007(ErrorCode.CODE_30047007, "封面不能为空"),
	CODE_30047008(ErrorCode.CODE_30047008, "删除时 主键id不能为空"),
	CODE_30047009(ErrorCode.CODE_30047009, "删除时 要取消激活的文件id不能为空"),
	CODE_30047010(ErrorCode.CODE_30047010, "分类不能为空"),
	CODE_30047011(ErrorCode.CODE_30047011, "收费类型不能为空"),
	CODE_30047012(ErrorCode.CODE_30047012, "标签数量超过10个"),
	CODE_30047013(ErrorCode.CODE_30047013, "品类不存在"),
	CODE_30047014(ErrorCode.CODE_30047014, "类别不能为空"),
	CODE_30047015(ErrorCode.CODE_30047015, "该资讯被广告占用，暂不支持下架"),
	CODE_30047016(ErrorCode.CODE_30047016, "分类不存在"),
	CODE_30047017(ErrorCode.CODE_30047017, "移动端展示位置不存在"),
	CODE_30047018(ErrorCode.CODE_30047018, "PC端展示位置不存在"),

	//部门相关
	CODE_30048001(ErrorCode.CODE_30048001, "部门名称重复"),
	CODE_30048002(ErrorCode.CODE_30048002, "部门名称不能为空"),
	CODE_30048003(ErrorCode.CODE_30048003, "上级部门id不能为空"),
	CODE_30048004(ErrorCode.CODE_30048004, "部门名称长度最大为64"),
	CODE_30048005(ErrorCode.CODE_30048005, "排序不能为空"),
	CODE_30048006(ErrorCode.CODE_30048006, "部门不存在"),
	CODE_30048007(ErrorCode.CODE_30048007, "部门下有子部门不能删除"),
	CODE_30048008(ErrorCode.CODE_30048008, "部门下有员工不能删除"),
	CODE_30048009(ErrorCode.CODE_30048009, "被拖动部门id不能为空"),
	CODE_30048010(ErrorCode.CODE_30048010, "目标部门id不能为空"),
	CODE_30048011(ErrorCode.CODE_30048011, "拖动类型不能为空"),
	CODE_30048012(ErrorCode.CODE_30048012, "拖动类型只能为0,1,2"),

	CODE_30049001(ErrorCode.CODE_30049001, "客户状态不能为空"),
	CODE_30049002(ErrorCode.CODE_30049002, "客户id不能为空"),
	CODE_30049003(ErrorCode.CODE_30049003, "客户不存在"),

	//客户修改头像
	CODE_30050001(ErrorCode.CODE_30050001, "文件Id不能为空"),

	// 客户登录
	CODE_30051001(ErrorCode.CODE_30051001, "客户微信昵称不能为空"),
	CODE_30051002(ErrorCode.CODE_30051002, "客户微信openid不能为空"),
	CODE_30051003(ErrorCode.CODE_30051003, "客户微信unionid不能为空"),
	CODE_30051004(ErrorCode.CODE_30051004, "客户身份编码不能为空"),
	CODE_30051005(ErrorCode.CODE_30051005, "客户微信昵称长度最大为64"),
	CODE_30051006(ErrorCode.CODE_30051006, "客户微信openid长度最大为64"),
	CODE_30051007(ErrorCode.CODE_30051007, "客户微信unionid长度最大为64"),
	CODE_30051008(ErrorCode.CODE_30051008, "客户手机号码格式不正确"),
	CODE_30051009(ErrorCode.CODE_30051009, "客户身份编码不正确"),
	CODE_30051010(ErrorCode.CODE_30051010, "客户在系统中已存在相同身份的账号"),
	CODE_30051011(ErrorCode.CODE_30051011, "客户信息不存在"),
	CODE_30051012(ErrorCode.CODE_30051012, "客户该身份不可用"),

	// 客户银行账户
	CODE_30052001(ErrorCode.CODE_30052001, "账户类型不能为空"),
	CODE_30052002(ErrorCode.CODE_30052002, "账户类型只能为1或2"),
	CODE_30052003(ErrorCode.CODE_30052003, "开户名称不能为空"),
	CODE_30052004(ErrorCode.CODE_30052004, "开户名称长度最大为255"),
	CODE_30052005(ErrorCode.CODE_30052005, "银行账户/银行卡号不能为空"),
	CODE_30052006(ErrorCode.CODE_30052006, "银行账户/银行卡号规则不正确"),
	CODE_30052007(ErrorCode.CODE_30052007, "开户行不能为空"),
	CODE_30052008(ErrorCode.CODE_30052008, "开户行长度最大为128"),
	CODE_30052009(ErrorCode.CODE_30052009, "银行账户不能为空"),

	// 客户认证
	CODE_30053001(ErrorCode.CODE_30053001, "当前客户已进行过个人实名认证操作,请核实后重新再试!"),
	CODE_30053002(ErrorCode.CODE_30053002, "未查询到客户实名认证信息"),
	CODE_30053003(ErrorCode.CODE_30053003, "请求查询客户实名认证信息失败"),
	CODE_30053004(ErrorCode.CODE_30053004, "当前客户未进行过个人实名认证操作,请首先进行个人实名认证的操作!"),
	CODE_30053005(ErrorCode.CODE_30053005, "当前客户已进行过企业实名认证操作并且还在有效期内,请核实后重新再试!"),
	CODE_30053006(ErrorCode.CODE_30053006, "当前客户未进行过企业实名认证操作,请首先进行企业实名认证的操作!"),
	CODE_30053007(ErrorCode.CODE_30053007, "请求查询客户企业实名认证信息失败"),
	CODE_30053008(ErrorCode.CODE_30053008, "机构名称长度不能超过255位"),

	// 客户地址
	CODE_30053009(ErrorCode.CODE_30053009, "联系人不能为空"),
	CODE_30053010(ErrorCode.CODE_30053010, "联系人长度不能超过25位"),
	CODE_30053011(ErrorCode.CODE_30053011, "手机号不能为空"),
	CODE_30053012(ErrorCode.CODE_30053012, "手机号格式不正确"),
	CODE_30053013(ErrorCode.CODE_30053013, "区号长度不能超过4位不能短于3位"),
	CODE_30053014(ErrorCode.CODE_30053014, "区号格式不正确"),
	CODE_30053015(ErrorCode.CODE_30053015, "座机号码长度不能超过8位不能短于7位"),
	CODE_30053016(ErrorCode.CODE_30053016, "座机号码格式不正确"),
	CODE_30053017(ErrorCode.CODE_30053017, "座机分机号码长度不能超过4位不能短于3位"),
	CODE_30053018(ErrorCode.CODE_30053018, "座机分机号码格式不正确"),
	CODE_30053019(ErrorCode.CODE_30053019, "省份不能为空"),
	CODE_30053020(ErrorCode.CODE_30053020, "省份长度不能超过10位"),
	CODE_30053021(ErrorCode.CODE_30053021, "市不能为空"),
	CODE_30053022(ErrorCode.CODE_30053022, "市长度不能超过25位"),
	CODE_30053023(ErrorCode.CODE_30053023, "区不能为空"),
	CODE_30053024(ErrorCode.CODE_30053024, "区长度不能超过25位"),
	CODE_30053025(ErrorCode.CODE_30053025, "详细地址不能为空"),
	CODE_30053026(ErrorCode.CODE_30053026, "详细地址长度不能超过128位"),
	CODE_30053027(ErrorCode.CODE_30053027, "邮政编码长度不能超过6位"),
	CODE_30053028(ErrorCode.CODE_30053028, "邮政编码格式不正确"),
	CODE_30053029(ErrorCode.CODE_30053029, "是否默认不能为空"),
	CODE_30053030(ErrorCode.CODE_30053030, "默认地址标识不正确, 只能为是或否"),
	CODE_30053031(ErrorCode.CODE_30053031, "已存在默认地址"),
	CODE_30053032(ErrorCode.CODE_30053032, "当前客户未进行企业认证"),
	CODE_30053033(ErrorCode.CODE_30053033, "当前所添加的机构名称必须与组织机构认证主体名称保持一致"),
	CODE_30053034(ErrorCode.CODE_30053034, "地址不存在"),
	CODE_30053035(ErrorCode.CODE_30053035, "客户id不能为空"),
	CODE_30053036(ErrorCode.CODE_30053036, "手机号长度不能超过11位"),

	// 客户发票
	CODE_30054001(ErrorCode.CODE_30054001, "抬头类型不能为空"),
	CODE_30054002(ErrorCode.CODE_30054002, "抬头类型只能为1或2"),
	CODE_30054003(ErrorCode.CODE_30054003, "抬头类型为企业时发票类型不能为空"),
	CODE_30054004(ErrorCode.CODE_30054004, "发票类型只能为1或2"),
	CODE_30054005(ErrorCode.CODE_30054005, "发票抬头不能为空"),
	CODE_30054006(ErrorCode.CODE_30054006, "发票抬头长度最大为55"),
	CODE_30054007(ErrorCode.CODE_30054007, "纳税人识别号规则不正确"),
	CODE_30054008(ErrorCode.CODE_30054008, "地址长度最大为128"),
	CODE_30054009(ErrorCode.CODE_30054009, "开户行长度最大为128"),
	CODE_30054010(ErrorCode.CODE_30054010, "对公账户银行账户不正确"),
	CODE_30054011(ErrorCode.CODE_30054011, "纳税人识别号不能为空"),
	CODE_30054012(ErrorCode.CODE_30054012, "地址不能为空"),
	CODE_30054013(ErrorCode.CODE_30054013, "手机号不能为空"),
	CODE_30054014(ErrorCode.CODE_30054014, "开户行不能为空"),
	CODE_30054015(ErrorCode.CODE_30054015, "对公账户不能为空"),
	CODE_30054016(ErrorCode.CODE_30054016, "是否默认抬头只能为0或1"),
	CODE_30054017(ErrorCode.CODE_30054017, "发票不存在"),

	// 子账号管理相关
	CODE_30055001(ErrorCode.CODE_30055001, "受邀人手机号不能为空"),
	CODE_30055002(ErrorCode.CODE_30055002, "受邀人可用身份不能为空"),
	CODE_30055003(ErrorCode.CODE_30055003, "请先前往企业认证页面完成企业认证后再试"),
	CODE_30055004(ErrorCode.CODE_30055004, "请勿邀请自己作为子账号加入企业"),
	CODE_30055005(ErrorCode.CODE_30055005, "该子账号已加入10个企业无法继续加入,请核实后再试"),
	CODE_30055006(ErrorCode.CODE_30055006, "您已经邀请过该用户，请勿重复邀请"),
	CODE_30055007(ErrorCode.CODE_30055007, "子账号不存在"),
	CODE_30055008(ErrorCode.CODE_30055008, "您加入的企业数量已达上限,无法继续加入,请核实后再试"),
	CODE_30055009(ErrorCode.CODE_30055009, "子账号可用身份中不包含承运商"),
	CODE_30055010(ErrorCode.CODE_30055010, "已确认的邀请记录不允许删除"),
	CODE_30055011(ErrorCode.CODE_30055011, "您不是该企业的管理员,无权进行此操作"),
	CODE_30055012(ErrorCode.CODE_30055012, "只能移除已确认的子账号"),
	CODE_30055013(ErrorCode.CODE_30055013, "企业不存在"),
	CODE_30055014(ErrorCode.CODE_30055014, "您尚未加入到任何企业,请先加入企业后再切换"),
	CODE_30055015(ErrorCode.CODE_30055015, "您尚未加入到该企业或已被该企业移除,请核实后再试"),
	CODE_30055016(ErrorCode.CODE_30055016, "您没有该身份的权限,请核实后再试"),
	CODE_30055017(ErrorCode.CODE_30055017, "暂不支持以承运商的角色切换企业"),
	CODE_30055018(ErrorCode.CODE_30055018, "当前主账号已拥有100个子账号，无法再创建"),
	CODE_30055019(ErrorCode.CODE_30055019, "当前会员等级子账号数量已达上限"),
	CODE_30055020(ErrorCode.CODE_30055020, "开通或升级链云会员，继续使用子账号"),
	CODE_30055021(ErrorCode.CODE_30055021, "当前会员等级子账号数量已达上限，无法激活子账号"),
	CODE_30055022(ErrorCode.CODE_30055022, "该企业子账号已失效"),
	CODE_30055023(ErrorCode.CODE_30055023, "受邀人可用权限不能为空"),
	CODE_30055024(ErrorCode.CODE_30055024, "当前账号不是代理账号"),

	// 认证相关
	CODE_30056001(ErrorCode.CODE_30056001, "当前存在已认证或认证中的企业,请勿重复操作!") ,
	CODE_30056002(ErrorCode.CODE_30056002, "该企业已经被认证或已在认证过程中") ,
	CODE_30056003(ErrorCode.CODE_30056003, "用户尚未完成个人实名认证,请先完成个人认证后再次重试!") ,
	CODE_30056004(ErrorCode.CODE_30056004, "用户已完成企业认证,请勿重复认证!") ,
	CODE_30056005(ErrorCode.CODE_30056005, "换绑手机号时企业认证需是同一企业认证才能换绑!") ,
	CODE_30056006(ErrorCode.CODE_30056006, "用户不在换绑手机号流程中!") ,
	CODE_30056007(ErrorCode.CODE_30056007, "此手机号已被使用!") ,


	// 个人认证
	CODE_30057001(ErrorCode.CODE_30057001, "真实姓名不能为空"),
	CODE_30057002(ErrorCode.CODE_30057002, "真实姓名长度不能超过25位"),
	CODE_30057003(ErrorCode.CODE_30057003, "身份证号码不能为空"),
	CODE_30057004(ErrorCode.CODE_30057004, "身份证号长度不能超过18位"),
	CODE_30057005(ErrorCode.CODE_30057005, "身份证号格式不正确"),
	CODE_30057006(ErrorCode.CODE_30057006, "认证完成跳转页面链接不能为空"),

	// 企业管理
	CODE_30058001(ErrorCode.CODE_30058001, "企业名称不能为空"),
	CODE_30058002(ErrorCode.CODE_30058002, "企业名称长度最大为255"),
	CODE_30058003(ErrorCode.CODE_30058003, "法定代表人不能为空"),
	CODE_30058004(ErrorCode.CODE_30058004, "法定代表人长度不能超过25位"),
	CODE_30058005(ErrorCode.CODE_30058005, "统一社会信用代码不能为空"),
	CODE_30058006(ErrorCode.CODE_30058006, "统一社会信用代码不正确"),
	CODE_30058007(ErrorCode.CODE_30058007, "统一社会信用代码长度不能超过18位"),
	CODE_30058008(ErrorCode.CODE_30058008, "组织机构类型不能为空"),

	// 登录相关
	CODE_30059001(ErrorCode.CODE_30059001, "获取微信小程序接口异常"),
	CODE_30059002(ErrorCode.CODE_30059002, "无效的code或者code已过期"),
	CODE_30059003(ErrorCode.CODE_30059003, "用户不存在"),
	CODE_30059004(ErrorCode.CODE_30059004, "此账号不存在"),
	CODE_30059005(ErrorCode.CODE_30059005, "账号或密码错误"),
	CODE_30059006(ErrorCode.CODE_30059006, "验证码校验未通过，请重新获取验证码"),
	CODE_30059007(ErrorCode.CODE_30059007, "用户身份不能为空"),
	CODE_30059008(ErrorCode.CODE_30059008, "您的账户已被禁用"),
	CODE_30059009(ErrorCode.CODE_30059009, "身份不可用"),
	CODE_30059010(ErrorCode.CODE_30059010, "未绑定微信"),
	CODE_30059011(ErrorCode.CODE_30059011, "该手机号已绑定其他账号！"),
	CODE_30059012(ErrorCode.CODE_30059012, "该手机号正用于换绑手机号流程中，暂不能使用"),
	CODE_30059013(ErrorCode.CODE_30059013, "您的账号已被管理员禁用，暂时无法登录"),
	CODE_30059014(ErrorCode.CODE_30059014, "此手机号不存在"),
	CODE_30059015(ErrorCode.CODE_30059015, "您的手机号已被管理员禁用，暂时无法登录"),
	CODE_30059016(ErrorCode.CODE_30059016, "不允许登录"),
	CODE_30059017(ErrorCode.CODE_30059017, "账号或密码错误"),
	CODE_30059018(ErrorCode.CODE_30059018, "账号不能为空"),
	CODE_30059019(ErrorCode.CODE_30059019, "key不能为空"),
	CODE_30059020(ErrorCode.CODE_30059020, "新密码不能为空"),
	CODE_30059021(ErrorCode.CODE_30059021, "两次输入的密码不相同"),
	CODE_30059022(ErrorCode.CODE_30059022, "新密码不可和旧密码一致"),
	CODE_30059023(ErrorCode.CODE_30059023, "请设置6-16位包含字母、数字的密码"),
	CODE_30059024(ErrorCode.CODE_30059024, "该账号已经注销，请联系平台处理"),
	CODE_30059025(ErrorCode.CODE_30059025, "无效的苹果identityToken"),
	CODE_30059026(ErrorCode.CODE_30059026, "未获取到apple subId或解析apple subId失败！"),
	CODE_30059027(ErrorCode.CODE_30059027, "未绑定苹果账号！"),
	CODE_30059028(ErrorCode.CODE_30059028, "该手机号已绑定其他账号！"),
    CODE_30059029(ErrorCode.CODE_30059029, "token无效,获取手机号失败"),

	//微信绑定相关
	CODE_30060001(ErrorCode.CODE_30060001, "该用户已绑定微信"),
	CODE_30060002(ErrorCode.CODE_30060002, "该微信已被绑定"),
	CODE_30060003(ErrorCode.CODE_30060003, "该用户未绑定微信"),
	CODE_30060004(ErrorCode.CODE_30060004, "该用户已绑定AppleId"),
	CODE_30060005(ErrorCode.CODE_30060005, "该AppleId已被绑定"),
	CODE_30060006(ErrorCode.CODE_30060006, "该用户未绑定AppleId"),
	CODE_30060007(ErrorCode.CODE_30060007, "Apple类型不能为空"),

	// 邮箱绑定相关
	CODE_30061001(ErrorCode.CODE_30061001, "邮箱不能为空"),
	CODE_30061002(ErrorCode.CODE_30061002, "该邮箱已被绑定"),








	// 品类指数相关
	CODE_30090001(ErrorCode.CODE_30090001, "版本id不能为空"),
	CODE_30090002(ErrorCode.CODE_30090002, "记录id不能为空"),
	CODE_30090003(ErrorCode.CODE_30090003, "品类不能为空"),
	CODE_30090004(ErrorCode.CODE_30090004, "价格信息不能为空，最多500条数据"),
	CODE_30090005(ErrorCode.CODE_30090005, "产地价格类型不能为空"),
	CODE_30090006(ErrorCode.CODE_30090006, "价格类型不能为空"),
	CODE_30090007(ErrorCode.CODE_30090007, "出货量的取值范围为0~99999999"),
	CODE_30090008(ErrorCode.CODE_30090008, "成交均价不能为空"),
	CODE_30090009(ErrorCode.CODE_30090009, "品类不存在"),
	CODE_30090010(ErrorCode.CODE_30090010, "价格类型已存在 不可重复维护"),
	CODE_30090011(ErrorCode.CODE_30090011, "待提交、已驳回状态才能进行提交、修改、删除操作"),
	CODE_30090012(ErrorCode.CODE_30090012, "没有权限"),
	CODE_30090013(ErrorCode.CODE_30090013, "旧数据品类id不能为空"),
	CODE_30090014(ErrorCode.CODE_30090014, "上传的数据不能为空"),
	CODE_30090015(ErrorCode.CODE_30090015, "上传的数据量超过最大限制 最多为500条"),
	CODE_30090016(ErrorCode.CODE_30090016, "有数据存在上船价数据为空的数据"),
	CODE_30090017(ErrorCode.CODE_30090017, "上船价的成交均价应大于0小于等于1000"),
	CODE_30090018(ErrorCode.CODE_30090018, "靠港价的成交均价应大于0小于等于1000"),
	CODE_30090019(ErrorCode.CODE_30090019, "上船价的成交均价不能为空"),

	// 砂石指数版本相关
	CODE_30091001(ErrorCode.CODE_30091001, "版本日期不能为空"),
	CODE_30091002(ErrorCode.CODE_30091002, "日期不能在今天之后"),
	CODE_30091003(ErrorCode.CODE_30091003, "版本日期已存在"),
	CODE_30091004(ErrorCode.CODE_30091004, "存在指数数据，不能修改"),
	CODE_30091005(ErrorCode.CODE_30091005, "版本不存在"),
	CODE_30091006(ErrorCode.CODE_30091006, "存在指数数据，不能删除"),

	// 砂石指数版本记录相关
	CODE_30092001(ErrorCode.CODE_30092001, "版本id不能为空"),
	CODE_30092002(ErrorCode.CODE_30092002, "砂石指数版本记录不存在"),
	CODE_30092003(ErrorCode.CODE_30092003, "指数数据为空，不能进行提交"),
	CODE_30092004(ErrorCode.CODE_30092004, "待发布、已撤回状态才能进行发布"),
	CODE_30092005(ErrorCode.CODE_30092005, "待发布、已撤回状态才能进行驳回"),
	CODE_30092006(ErrorCode.CODE_30092006, "已发布状态且是最新发布数据才能进行撤回"),
	CODE_30092007(ErrorCode.CODE_30092007, "当前已经是最初发布版本，无法进行撤回！"),
	CODE_30092008(ErrorCode.CODE_30092008, "待提交、已驳回状态才能进行提交、修改、删除操作"),
	CODE_30092009(ErrorCode.CODE_30092009, "待提交、已驳回状态才能进行保存"),
	CODE_30092010(ErrorCode.CODE_30092010, "没有权限"),

	// 砂石资讯相关
	CODE_30093001(ErrorCode.CODE_30093001, "删除时 主键id不能为空"),
	CODE_30093002(ErrorCode.CODE_30093002, "删除时 要取消激活的文件id不能为空"),

	// 商品相关
	CODE_30094001(ErrorCode.CODE_30094001, "商品id不能为空"),
	CODE_30094002(ErrorCode.CODE_30094002, "商品名称不能为空"),
	CODE_30094003(ErrorCode.CODE_30094003, "权重不能为空 取值范围为0~10000"),
	CODE_30094004(ErrorCode.CODE_30094004, "主图文件不能为空"),
	CODE_30094005(ErrorCode.CODE_30094005, "其他图片最多只能传五张"),
	CODE_30094006(ErrorCode.CODE_30094006, "视频文件必须上传"),
	CODE_30094007(ErrorCode.CODE_30094007, "品类不能为空"),
	CODE_30094008(ErrorCode.CODE_30094008, "规格不能为空"),
	CODE_30094009(ErrorCode.CODE_30094009, "最小细度模数不能为空 取值范围位0.001~99.999"),
	CODE_30094010(ErrorCode.CODE_30094010, "最大细度模数取值范围位0.001~99.999"),
	CODE_30094011(ErrorCode.CODE_30094011, "含泥量取值范围为1~99"),
	CODE_30094012(ErrorCode.CODE_30094012, "压碎值取值范围为1~99"),
	CODE_30094013(ErrorCode.CODE_30094013, "石粉MB值取值范围为1~99"),
	CODE_30094014(ErrorCode.CODE_30094014, "含水率取值范围为1~99"),
	CODE_30094015(ErrorCode.CODE_30094015, "细粉含量取值范围为1~99"),
	CODE_30094016(ErrorCode.CODE_30094016, "供货商不能为空"),
	CODE_30094017(ErrorCode.CODE_30094017, "起订条件不能为空"),
	CODE_30094018(ErrorCode.CODE_30094018, "开票说明 最大为32个字符"),
	CODE_30094019(ErrorCode.CODE_30094019, "提货周期 最大为12个字符"),
	CODE_30094023(ErrorCode.CODE_30094023, "详细地址不能为空"),
	CODE_30094024(ErrorCode.CODE_30094024, "经纬度不能为空"),
	CODE_30094025(ErrorCode.CODE_30094025, "商品详情介绍不能为空"),
	CODE_30094026(ErrorCode.CODE_30094026, "备注最大为100个字符"),
	CODE_30094027(ErrorCode.CODE_30094027, "供应商类型不存在"),
	CODE_30094028(ErrorCode.CODE_30094028, "价格类型不能为空"),
	CODE_30094029(ErrorCode.CODE_30094029, "价格类型不存在"),
	CODE_30094030(ErrorCode.CODE_30094030, "履约保证金额不能为空 取值范围为0.01~10000000000"),
	CODE_30094031(ErrorCode.CODE_30094031, "履约金百分比不能为空 取值范围为1~99"),
	CODE_30094032(ErrorCode.CODE_30094032, "单价不能为空 取值范围为0.01~10000"),
	CODE_30094033(ErrorCode.CODE_30094033, "起订金额不能为空 取值范围为0.01~10000000000"),
	CODE_30094034(ErrorCode.CODE_30094034, "起订吨数不能为空 取值范围为0.01~99999999.99"),
	CODE_30094035(ErrorCode.CODE_30094035, "供应商不存在"),
	CODE_30094036(ErrorCode.CODE_30094036, "当前状态不允许编辑"),
	CODE_30094037(ErrorCode.CODE_30094037, "当前状态不允许审核"),
	CODE_30094038(ErrorCode.CODE_30094038, "当前状态不允许下架"),
	CODE_30094039(ErrorCode.CODE_30094039, "当前状态不允许关闭"),
	CODE_30094040(ErrorCode.CODE_30094040, "未关闭的商品才支持绑定设备"),
	CODE_30094041(ErrorCode.CODE_30094041, "商品不存在"),
	CODE_30094042(ErrorCode.CODE_30094042, "该商品被广告占用，暂不支持下架"),




	// 品类相关
	CODE_30095001(ErrorCode.CODE_30095001, "品类代码不能为空，长度为6-10个字符"),
	CODE_30095002(ErrorCode.CODE_30095002, "品类名称不能为空, 长度为1-10个字符"),
	CODE_30095003(ErrorCode.CODE_30095003, "品类类型不能为空"),
	CODE_30095004(ErrorCode.CODE_30095004, "品类区域不能为空"),
	CODE_30095005(ErrorCode.CODE_30095005, "产地价格类型不能为空"),
	CODE_30095006(ErrorCode.CODE_30095006, "采区/矿厂全称不能为空 长度为1-40个字符"),
	CODE_30095007(ErrorCode.CODE_30095007, "采区/矿厂简称不能为空 长度为1-10个字符"),
	CODE_30095008(ErrorCode.CODE_30095008, "规格/细度模数不能为空 长度为1-15个字符"),
	CODE_30095009(ErrorCode.CODE_30095009, "品类介绍不能为空 最大为256个字符"),
	CODE_30095010(ErrorCode.CODE_30095010, "该品类代码已存在"),
	CODE_30095011(ErrorCode.CODE_30095011, "产地价格类型必须有默认"),
	CODE_30095012(ErrorCode.CODE_30095012, "产地价格类型只能有一个默认"),
	CODE_30095013(ErrorCode.CODE_30095013, "产地价格类型名称不能重复"),
	CODE_30095014(ErrorCode.CODE_30095014, "品类类型不存在"),
	CODE_30095015(ErrorCode.CODE_30095015, "品类区域不存在"),
	CODE_30095016(ErrorCode.CODE_30095016, "该品类关联未关闭的商品 无法删除"),
	CODE_30095017(ErrorCode.CODE_30095017, "存在重复标签"),
	CODE_30095018(ErrorCode.CODE_30095018, "品类不存在"),

	// 项目相关
	CODE_30096001(ErrorCode.CODE_30096001, "项目类型不能为空 只能为1和2"),
	CODE_30096002(ErrorCode.CODE_30096002, "采购类型不能为空"),
	CODE_30096003(ErrorCode.CODE_30096003, "其他表的主键id不能为空"),
	CODE_30096004(ErrorCode.CODE_30096004, "项目名称不能为空 最大为1~30个字符"),
	CODE_30096005(ErrorCode.CODE_30096005, "项目介绍不能为空 最大为1~500个字符"),
	CODE_30096006(ErrorCode.CODE_30096006, "买方id不能为空"),
	CODE_30096007(ErrorCode.CODE_30096007, "意向id不能为空"),
	CODE_30096008(ErrorCode.CODE_30096008, "项目id不能为空"),
	CODE_30096009(ErrorCode.CODE_30096009, "用户不存在"),
	CODE_30096010(ErrorCode.CODE_30096010, "用户身份不能为空"),
	CODE_30096011(ErrorCode.CODE_30096011, "服务费不能为空"),
	CODE_30096012(ErrorCode.CODE_30096012, "项目不存在"),
	CODE_30096013(ErrorCode.CODE_30096013, "当前状态不允许指派"),
	CODE_30096014(ErrorCode.CODE_30096014, "当前状态不允许开始"),
	CODE_30096015(ErrorCode.CODE_30096015, "当前状态不允许点完成"),
	CODE_30096016(ErrorCode.CODE_30096016, "结算未完成,无法完成项目"),
	CODE_30096017(ErrorCode.CODE_30096017, "当前状态不允许关闭"),
	CODE_30096018(ErrorCode.CODE_30096018, "预估单价不能为空 取值范围 0.01-100亿 保留两位小数"),
	CODE_30096019(ErrorCode.CODE_30096019, "冻结金额不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096020(ErrorCode.CODE_30096020, "供应链预估单价不能为空 取值范围 0.01-100亿 保留两位小数"),
	CODE_30096021(ErrorCode.CODE_30096021, "供应链冻结金额不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096022(ErrorCode.CODE_30096022, "供应链合同金额不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096023(ErrorCode.CODE_30096023, "供应链最低付款额度不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096024(ErrorCode.CODE_30096024, "项目名称不支持重名"),
	CODE_30096025(ErrorCode.CODE_30096025, "购买意向不存在"),
	CODE_30096026(ErrorCode.CODE_30096026, "意向采购吨数不能为空 且必须大于起订吨数"),
	CODE_30096027(ErrorCode.CODE_30096027, "买方预估单价不能为空 取值范围 0.01-100亿 保留两位小数"),
	CODE_30096028(ErrorCode.CODE_30096028, "买方冻结金额不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096029(ErrorCode.CODE_30096029, "买方合同金额不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096030(ErrorCode.CODE_30096030, "买方最低付款额度不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096031(ErrorCode.CODE_30096031, "服务费索引参数非法"),
	CODE_30096032(ErrorCode.CODE_30096032, "服务费信息缺少,请填写完整"),
	CODE_30096033(ErrorCode.CODE_30096033, "只支持线上项目进行导出"),
	CODE_30096034(ErrorCode.CODE_30096034, "只支持状态为交付中和已完成的项目进行导出"),
	CODE_30096035(ErrorCode.CODE_30096035, "采购商id不能为空"),
	CODE_30096036(ErrorCode.CODE_30096036, "租户id不能为空"),
	CODE_30096037(ErrorCode.CODE_30096037, "存在作废中的数据，请先处理"),
	CODE_30096038(ErrorCode.CODE_30096038, "项目已完成 不允许新增"),
	CODE_30096039(ErrorCode.CODE_30096039, "项目已完成才能新增"),
	CODE_30096040(ErrorCode.CODE_30096040, "已完成状态才能进行删除"),
	CODE_30096041(ErrorCode.CODE_30096041, "线下作废请联系平台处理"),
	CODE_30096042(ErrorCode.CODE_30096042, "供应商需完成电子签章授权"),
	CODE_30096043(ErrorCode.CODE_30096043, "供应商、联合购销商需完成电子签章授权"),

	// 签收单相关
	CODE_30097001(ErrorCode.CODE_30097001, "签收单不存在"),
	CODE_30097002(ErrorCode.CODE_30097002, "文章类型不能为空"),
	CODE_30097003(ErrorCode.CODE_30097003, "签收单吨数不能为空"),
	CODE_30097004(ErrorCode.CODE_30097004, "买方认证信息不存在"),
	CODE_30097005(ErrorCode.CODE_30097005, "签收确认日期不能为空"),
	CODE_30097006(ErrorCode.CODE_30097006, "非草稿状态下不能修改"),
	CODE_30097007(ErrorCode.CODE_30097007, "非草稿状态不能删除"),
	CODE_30097008(ErrorCode.CODE_30097008, "类型不能为空"),
	CODE_30097009(ErrorCode.CODE_30097009, "只有线下已完成的项目才允许撤回"),
	CODE_30097010(ErrorCode.CODE_30097010, "买方客户不存在"),
	CODE_30097011(ErrorCode.CODE_30097011, "项目状态为已完成或者已关闭,不能加入"),
	CODE_30097012(ErrorCode.CODE_30097012, "关联签收单id不能为空"),
	CODE_30097013(ErrorCode.CODE_30097013, "关联提货单不能为空"),
	CODE_30097014(ErrorCode.CODE_30097014, "采购商已在项目中,加入失败"),
	CODE_30097015(ErrorCode.CODE_30097015, "对账状态为对账中或已对账的状态无法关闭"),
	CODE_30097016(ErrorCode.CODE_30097016, "签收日期不能为空"),
	CODE_30097017(ErrorCode.CODE_30097017, "预估单价不能为空"),
	CODE_30097018(ErrorCode.CODE_30097018, "已完成的状态才能删除"),
	CODE_30097019(ErrorCode.CODE_30097019, "先删除关联的对账单才能进行删除"),
	CODE_30097020(ErrorCode.CODE_30097020, "先删除被关联的签收单才能进行删除"),
	CODE_30097021(ErrorCode.CODE_30097021, "签收吨数不能为空"),
	CODE_30097022(ErrorCode.CODE_30097022, "签收id不能为空"),
	CODE_30097023(ErrorCode.CODE_30097023, "驳回原因长度最大为100"),
	CODE_30097024(ErrorCode.CODE_30097024, "待作废的签收单不允许关联对账单并且下游的签收单也不允许关联对账单"),

	// 合同相关
	CODE_30098001(ErrorCode.CODE_30098001, "合同名称不能为空"),
	CODE_30098002(ErrorCode.CODE_30098002, "合同名称长度不能超过32个字符"),
	CODE_30098003(ErrorCode.CODE_30098003, "甲方账号不能为空"),
	CODE_30098004(ErrorCode.CODE_30098004, "甲方公司不能为空"),
	CODE_30098005(ErrorCode.CODE_30098005, "乙方账号不能为空"),
	CODE_30098006(ErrorCode.CODE_30098006, "乙方公司不能为空"),
	CODE_30098007(ErrorCode.CODE_30098007, "签署模式不能为空"),
	CODE_30098008(ErrorCode.CODE_30098008, "合同类型不能为空"),
	CODE_30098009(ErrorCode.CODE_30098009, "合同文件id不能为空"),
	CODE_30098010(ErrorCode.CODE_30098010, "提交方式不能为空"),
	CODE_30098011(ErrorCode.CODE_30098011, "合同编号或合同名称搜索不得超过32个字符!"),
	CODE_30098012(ErrorCode.CODE_30098012, "项目[%s]不存在"),
	CODE_30098013(ErrorCode.CODE_30098013, "只有线上签署的合同才能进行修改!"),
	CODE_30098014(ErrorCode.CODE_30098014, "只有草稿状态的合同才能进行修改!"),
	CODE_30098015(ErrorCode.CODE_30098015, "只有签署中状态的合同才能进行撤回!"),
	CODE_30098016(ErrorCode.CODE_30098016, "只有线上签署的合同才能进行撤回!"),
	CODE_30098017(ErrorCode.CODE_30098017, "只有草稿状态的合同才能进行删除!"),
	CODE_30098018(ErrorCode.CODE_30098018, "只有已完成状态的合同才能进行关闭!"),
	CODE_30098019(ErrorCode.CODE_30098019, "甲方客户已被禁用,不能创建合同,请核实后再试!"),
	CODE_30098020(ErrorCode.CODE_30098020, "乙方客户已被禁用,不能创建合同,请核实后再试!"),
	CODE_30098021(ErrorCode.CODE_30098021, "运价取值范围为0.01~1000"),
	CODE_30098022(ErrorCode.CODE_30098022, "乙方客户未进行企业认证,不能创建合同,请核实后再试!"),
	CODE_30098023(ErrorCode.CODE_30098023, "运价指数不存在"),
	CODE_30098024(ErrorCode.CODE_30098024, "乙方客户认证企业与传入企业不匹配!"),
	CODE_30098033(ErrorCode.CODE_30098033, "起始日期不能为空"),
	CODE_30098034(ErrorCode.CODE_30098034, "截止日期不能为空"),
	CODE_30098035(ErrorCode.CODE_30098035, "当前操作用户不是项目专员,不可新增"),
	CODE_30098036(ErrorCode.CODE_30098036, "当前操作用户不是项目专员,不可撤回"),
	CODE_30098037(ErrorCode.CODE_30098037, "甲方必须实名"),

	// 购买意向相关
	CODE_30099001(ErrorCode.CODE_30099001, "购买意向id不能为空"),
	CODE_30099002(ErrorCode.CODE_30099002, "指派服务专员id不能为空"),
	CODE_30099003(ErrorCode.CODE_30099003, "只有待确认状态可以确认"),
	CODE_30099004(ErrorCode.CODE_30099004, "只有待处理,已驳回,处理中,待确认状态的购买意向才能搁浅"),
	CODE_30099005(ErrorCode.CODE_30099005, "搁浅原因不能为空"),
	CODE_30099006(ErrorCode.CODE_30099006, "搁浅原因长度最大为64"),
	CODE_30099007(ErrorCode.CODE_30099007, "只有待指派,已指派,搁浅状态的购买意向才能指派"),
	CODE_30099008(ErrorCode.CODE_30099008, "联合采购类型 可出资比例和下游出资金额不能为空"),
	CODE_30099009(ErrorCode.CODE_30099009, "项目在交付状态才可以生成签收单"),
	CODE_30099010(ErrorCode.CODE_30099010, "采购类型只有1或2"),
	CODE_30099011(ErrorCode.CODE_30099011, "未通过企业认证不能进行操作"),
	CODE_30099012(ErrorCode.CODE_30099012, "采购数量不能为空"),
	CODE_30099013(ErrorCode.CODE_30099013, "总价不能为空"),
	CODE_30099014(ErrorCode.CODE_30099014, "请确认存在关联下游采购商签收单已驳回"),
	CODE_30099015(ErrorCode.CODE_30099015, "草稿状态下不存在联合-采购商的签收单才能删除"),
	CODE_30099016(ErrorCode.CODE_30099016, "勾选的对账单中包含已开票的数据,请重新勾选"),
	CODE_30099017(ErrorCode.CODE_30099017, "您没有查看监控的权限,如需查看监控,请购买或升级会员"),
	CODE_30099019(ErrorCode.CODE_30099019, "采购说明长度最大为128"),
	CODE_30099020(ErrorCode.CODE_30099020, "省编码长度最大为16"),
	CODE_30099021(ErrorCode.CODE_30099021, "市编码长度最大为16"),
	CODE_30099022(ErrorCode.CODE_30099022, "区域编码长度最大为16"),
	CODE_30099023(ErrorCode.CODE_30099023, "联系人长度最大为64"),
	CODE_30099024(ErrorCode.CODE_30099024, "手机号长度最大为64"),
	CODE_30099025(ErrorCode.CODE_30099025, "提货时间不能为空"),
	CODE_30099026(ErrorCode.CODE_30099026, "电话号10-19位"),
	CODE_30099028(ErrorCode.CODE_30099028, "供应链id不能为空"),
	CODE_30099029(ErrorCode.CODE_30099029, "只有已确认状态可以生成项目"),
	CODE_30099030(ErrorCode.CODE_30099030, "只有已搁浅状态下可以关闭"),
	CODE_30099031(ErrorCode.CODE_30099031, "只有待处理状态可以开始"),
	CODE_30099032(ErrorCode.CODE_30099032, "只有处理中状态可以提交审核"),
	CODE_30099033(ErrorCode.CODE_30099033, "只有待审核状态可以审核"),
	CODE_30099034(ErrorCode.CODE_30099034, "审核意见长度最大为255"),
	CODE_30099035(ErrorCode.CODE_30099035, "审核类型不能为空"),
	CODE_30099036(ErrorCode.CODE_30099036, "审核类型只有1或2"),
	CODE_30099037(ErrorCode.CODE_30099037, "驳回时审核意见必填"),
	CODE_30099038(ErrorCode.CODE_30099038, "只有已确认,已搁浅,已完成可以撤销"),
	CODE_30099039(ErrorCode.CODE_30099039, "意向的服务专员不是当前操作人"),
	CODE_30099040(ErrorCode.CODE_30099040, "买方预估单价不能为空"),
	CODE_30099041(ErrorCode.CODE_30099041, "买方合同金额不能为空"),
	CODE_30099042(ErrorCode.CODE_30099042, "买方被冻结金额不能为空"),
	CODE_30099043(ErrorCode.CODE_30099043, "买方最低付款额度不能为空"),
	CODE_30099045(ErrorCode.CODE_30099045, "供应链合同金额不能为空"),
	CODE_30099046(ErrorCode.CODE_30099046, "供应链被冻结金额不能为空"),
	CODE_30099047(ErrorCode.CODE_30099047, "供应链最低付款额度不能为空"),
	CODE_30099050(ErrorCode.CODE_30099050, "服务费类型错误"),
	CODE_30099051(ErrorCode.CODE_30099051, "服务费金额不能为空 取值范围 0~10000000000"),
	CODE_30099052(ErrorCode.CODE_30099052, "总价数据错误"),

	// 提货单相关
	CODE_30100001(ErrorCode.CODE_30100001, "关联项目不能为空"),
	CODE_30100002(ErrorCode.CODE_30100002, "关联签收单不能为空"),
	CODE_30100003(ErrorCode.CODE_30100003, "关联的提货单不能为空"),
	CODE_30100004(ErrorCode.CODE_30100004, "运输方式为船运时船舶id不能为空"),
	CODE_30100005(ErrorCode.CODE_30100005, "确认中或者作废中状态才能进行签署"),
	CODE_30100006(ErrorCode.CODE_30100006, "提货单不存在"),
	CODE_30100007(ErrorCode.CODE_30100007, "草稿状态的提货单才允许修改"),
	CODE_30100008(ErrorCode.CODE_30100008, "只有确认中的状态才能进行驳回"),
	CODE_30100009(ErrorCode.CODE_30100009, "草稿状态的提货单才允许删除"),
	CODE_30100010(ErrorCode.CODE_30100010, "只有已完成的提货单可以被关联"),
	CODE_30100011(ErrorCode.CODE_30100011, "项目在交付状态才可以生成提货单"),
	CODE_30100012(ErrorCode.CODE_30100012, "只有签署中或者作废中状态可以签署"),
	CODE_30100013(ErrorCode.CODE_30100013, "数量不能为空"),
	CODE_30100014(ErrorCode.CODE_30100014, "只有未关联签收单的提货单才能发起作废"),
	CODE_30100015(ErrorCode.CODE_30100015, "只有联合购销商未转发的提货单才能发起作废"),
	CODE_30100016(ErrorCode.CODE_30100016, "备注长度不能超过20"),
	CODE_30100017(ErrorCode.CODE_30100017, "吨数不能为空"),
	CODE_30100018(ErrorCode.CODE_30100018, "提货申请日期不能为空"),
	CODE_30100019(ErrorCode.CODE_30100019, "凭证文件不能为空"),
	CODE_30100020(ErrorCode.CODE_30100020, "已完成状态才能删除"),
	CODE_30100021(ErrorCode.CODE_30100021, "需要先删除关联的签收单才能删除"),
	CODE_30100022(ErrorCode.CODE_30100022, "需要先删除被关联的提货单才能删除"),
	CODE_30100023(ErrorCode.CODE_30100023, "预估单价不能为空"),
	CODE_30100024(ErrorCode.CODE_30100024, "提货数量超过可提货预估吨数"),
	CODE_30100025(ErrorCode.CODE_30100025, "只有未关联签收单的提货单才能发起作废"),
	CODE_30100026(ErrorCode.CODE_30100026, "只有联合购销商未转发的提货单才能发起作废"),
	CODE_30100028(ErrorCode.CODE_30100028, "已完成状态才能进行新增"),
	CODE_30100029(ErrorCode.CODE_30100029, "只有提货状态是完成并且签收状态是未签署的数据可以关闭"),
	CODE_30100030(ErrorCode.CODE_30100030, "运货吨数不能为空"),

	// 对账相关
	CODE_30101001(ErrorCode.CODE_30101001, "已发布状态不能进行修改"),
	CODE_30101002(ErrorCode.CODE_30101002, "综合指数 应大于0小于1000 保留两位小数"),
	CODE_30101003(ErrorCode.CODE_30101003, "计算总金额不能为空"),
	CODE_30101004(ErrorCode.CODE_30101004, "实际总金额不能为空"),
	CODE_30101005(ErrorCode.CODE_30101005, "备注长度不能超过200"),
	CODE_30101006(ErrorCode.CODE_30101006, "签收单id集合不能为空"),
	CODE_30101007(ErrorCode.CODE_30101007, "综合指数不能全为空"),
	CODE_30101008(ErrorCode.CODE_30101008, "对账id不存在"),
	CODE_30101009(ErrorCode.CODE_30101009, "只有草稿状态和驳回状态的对账单才能进行修改操作"),
	CODE_30101010(ErrorCode.CODE_30101010, "只有草稿和签署中状态的对账单才能进行驳回操作"),
	CODE_30101011(ErrorCode.CODE_30101011, "只有草稿状态和驳回状态的对账单才能进行删除操作"),
	CODE_30101012(ErrorCode.CODE_30101012, "驳回原因不能为空"),
	CODE_30101013(ErrorCode.CODE_30101013, "类型只能为1-3"),
	CODE_30101014(ErrorCode.CODE_30101014, "类型不能为空"),
	CODE_30101015(ErrorCode.CODE_30101015, "项目在交付状态才可以生成对账单"),
	CODE_30101016(ErrorCode.CODE_30101016, "只有签署中状态可以签署"),
	CODE_30101017(ErrorCode.CODE_30101017, "只有已完成状态并且未绑定的签收单可以进行对账"),
	CODE_30101018(ErrorCode.CODE_30101018, "签署单id不能为空"),
	CODE_30101019(ErrorCode.CODE_30101019, "存在重复日期数据"),
	CODE_30101020(ErrorCode.CODE_30101020, "草稿和驳回状态才能进行发起签署"),
	CODE_30101021(ErrorCode.CODE_30101021, "对账日期不能为空"),
	CODE_30101022(ErrorCode.CODE_30101022, "提货单id集合不能为空"),
	CODE_30101023(ErrorCode.CODE_30101023, "提货单id不能为空"),
	CODE_30101024(ErrorCode.CODE_30101024, "船舶信息不能为空"),
	CODE_30101025(ErrorCode.CODE_30101025, "单价不能为空"),
	CODE_30101026(ErrorCode.CODE_30101026, "采购账号id不能为空"),
	CODE_30101027(ErrorCode.CODE_30101027, "综合指数页应为3种不同类型数据"),
	CODE_30101028(ErrorCode.CODE_30101028, "只有未关联开票的且已完成状态下的对账单才能发起作废"),
	CODE_30101029(ErrorCode.CODE_30101029, "签收单不能为空"),
	CODE_30101030(ErrorCode.CODE_30101030, "总吨数不能为空"),
	CODE_30101031(ErrorCode.CODE_30101031, "核算金额不能为空"),
	CODE_30101032(ErrorCode.CODE_30101032, "折扣金额不能为空"),
	CODE_30101033(ErrorCode.CODE_30101033, "对账单不存在"),
	CODE_30101034(ErrorCode.CODE_30101034, "只有未开票的对账单才能进行关闭操作"),
	CODE_30101035(ErrorCode.CODE_30101035, "已完成的项目才能进行新增"),

	// 开票相关
	CODE_30102001(ErrorCode.CODE_30102001, "对账单不能为空 最少勾选一个"),

	// 付款相关
	CODE_30103001(ErrorCode.CODE_30103001, "已发布状态不能进行删除"),
	CODE_30103002(ErrorCode.CODE_30103002, "非完成状态下不能关闭"),
	CODE_30103003(ErrorCode.CODE_30103003, "项目id不能为空"),
	CODE_30103004(ErrorCode.CODE_30103004, "类型不能为空"),
	CODE_30103005(ErrorCode.CODE_30103005, "综合指数页需校验天然砂、机制砂、碎石都不能为空"),
	CODE_30103006(ErrorCode.CODE_30103006, "金额不能为空"),
	CODE_30103007(ErrorCode.CODE_30103007, "付款日期不能为空"),
	CODE_30103008(ErrorCode.CODE_30103008, "凭证文件不能为空"),
	CODE_30103009(ErrorCode.CODE_30103009, "综合指数页需校验天然砂、机制砂、碎石的权重和等于100%"),
	CODE_30103010(ErrorCode.CODE_30103010, "付款方id不能为空"),
	CODE_30103011(ErrorCode.CODE_30103011, "当前付款金额小于可提货预估金额，不支持作废"),
	CODE_30103012(ErrorCode.CODE_30103012, "方式不能为空"),
	CODE_30103013(ErrorCode.CODE_30103013, "凭证不能为空"),
	CODE_30103014(ErrorCode.CODE_30103014, "金额不能大于99999999999999.99"),
	CODE_30103015(ErrorCode.CODE_30103015, "金额不能超过（收款单位与付款单位交易金额的总和-已付款总额）"),
	CODE_30103016(ErrorCode.CODE_30103016, "非驳回状态下不能删除"),
	CODE_30103017(ErrorCode.CODE_30103017, "非驳回状态不能更新"),
	CODE_30103018(ErrorCode.CODE_30103018, "驳回原因不能为空"),
	CODE_30103019(ErrorCode.CODE_30103019, "驳回原因长度最大为100"),
	CODE_30103020(ErrorCode.CODE_30103020, "项目在交付状态才可以生成付款单"),
	CODE_30103021(ErrorCode.CODE_30103021, "当前付款金额小于可提货预估金额，不支持作废"),

	// 服务费相关
	CODE_30104001(ErrorCode.CODE_30104001, "服务费id不能为空"),
	CODE_30104002(ErrorCode.CODE_30104002, "核销日期不能为空"),
	CODE_30104003(ErrorCode.CODE_30104003, "核销金额不能为空"),
	CODE_30104004(ErrorCode.CODE_30104004, "核销金额不能大于账单金额"),
	CODE_30104005(ErrorCode.CODE_30104005, "服务费账单不存在"),
	CODE_30104006(ErrorCode.CODE_30104006, "价格日期不能为空"),
	CODE_30104007(ErrorCode.CODE_30104007, "项目已完成才能进行新增"),
	CODE_30104008(ErrorCode.CODE_30104008, "服务费类型不能为空"),
	CODE_30104009(ErrorCode.CODE_30104009, "当前发布状态不是未发布不能进行发布操作"),
	CODE_30104010(ErrorCode.CODE_30104010, "吨数不能为空"),
	CODE_30104011(ErrorCode.CODE_30104011, "当前选择的日期已存在指数"),
	CODE_30104012(ErrorCode.CODE_30104012, "金额不能为空"),
	CODE_30104013(ErrorCode.CODE_30104013, "用户id不能为空"),
	CODE_30104014(ErrorCode.CODE_30104014, "当前综合指数不存在"),
	CODE_30104015(ErrorCode.CODE_30104015, "服务费类型不存在"),

	// 平台银行账号相关
	CODE_30105001(ErrorCode.CODE_30105001, "开户名称不能为空 长度为1~25个字符"),
	CODE_30105002(ErrorCode.CODE_30105002, "银行账户不能为空 8~30位的正整数"),
	CODE_30105003(ErrorCode.CODE_30105003, "开户银行不能为空 长度为1~128个字符"),
	CODE_30105004(ErrorCode.CODE_30105004, "平台银行账号不存在"),
	CODE_30105005(ErrorCode.CODE_30105005, "禁用状态下不能设置默认"),
	CODE_30105006(ErrorCode.CODE_30105006, "该账户已经是默认账户，不能进行禁用"),
	CODE_30105007(ErrorCode.CODE_30105007, "批量上传失败"),
	CODE_30105008(ErrorCode.CODE_30105008, "无效code"),
	CODE_30105009(ErrorCode.CODE_30105009, "当前选用的使用场景已被其他银行账号使用"),

	// 砂石综合指数配置相关
	CODE_30106001(ErrorCode.CODE_30106001, "数据不存在"),
	CODE_30106002(ErrorCode.CODE_30106002, "商品不存在"),
	CODE_30106003(ErrorCode.CODE_30106003, "只有草稿和驳回状态才能删除"),
	CODE_30106004(ErrorCode.CODE_30106004, "草稿和驳回状态才能进行更新"),
	CODE_30106005(ErrorCode.CODE_30106005, "综合指数页类型不能为空"),
	CODE_30106006(ErrorCode.CODE_30106006, "综合指数页存在重复类型"),
	CODE_30106007(ErrorCode.CODE_30106007, "天然砂、碎石、机制砂配置不能为空"),
	CODE_30106008(ErrorCode.CODE_30106008, "天然砂、碎石、机制砂页中区域不能为空"),
	CODE_30106009(ErrorCode.CODE_30106009, "天然砂、碎石、机制砂页中选择区域不存在"),
	CODE_30106010(ErrorCode.CODE_30106010, "天然砂、碎石、机制砂页中区域不能重复"),
	CODE_30106011(ErrorCode.CODE_30106011, "天然砂、碎石、机制砂页中品类不能为空"),
	CODE_30106012(ErrorCode.CODE_30106012, "请确认所有关联下游采购商签收单已签署完成"),
	CODE_30106013(ErrorCode.CODE_30106013, "天然砂、碎石、机制砂页中品类不能重复"),
	CODE_30106014(ErrorCode.CODE_30106014, "天然砂、碎石、机制砂页中区域权重不能为空"),
	CODE_30106015(ErrorCode.CODE_30106015, "天然砂、碎石、机制砂页中区域权重之和必须等于100%"),
	CODE_30106016(ErrorCode.CODE_30106016, "天然砂、碎石、机制砂页中价格类型不能为空"),
	CODE_30106017(ErrorCode.CODE_30106017, "天然砂、碎石、机制砂页中价格类型不存在"),
	CODE_30106018(ErrorCode.CODE_30106018, "天然砂、碎石、机制砂页中价格类型不能重复"),
	CODE_30106019(ErrorCode.CODE_30106019, "天然砂、碎石、机制砂页中价格权重不能为空"),
	CODE_30106020(ErrorCode.CODE_30106020, "天然砂、碎石、机制砂页中价格权重之和必须等于100%"),
	CODE_30106021(ErrorCode.CODE_30106021, "参数错误"),
	CODE_30106022(ErrorCode.CODE_30106022, "品类不存在"),
	CODE_30106023(ErrorCode.CODE_30106023, "请检查品类类型及所属区域"),
	CODE_30106024(ErrorCode.CODE_30106024, "天然砂、碎石、机制砂页中区域不能全为空"),
	CODE_30106025(ErrorCode.CODE_30106025, "类型不能为空"),
	CODE_30106026(ErrorCode.CODE_30106026, "类型不存在"),

	// 船舶相关
	CODE_30120001(ErrorCode.CODE_30120001, "MMSI编号不能为空"),
	CODE_30120002(ErrorCode.CODE_30120002, "MMSI格式错误"),
	CODE_30120003(ErrorCode.CODE_30120003, "船只名称不能为空"),
	CODE_30120004(ErrorCode.CODE_30120004, "船只名称长度不能超过64位"),
	CODE_30120005(ErrorCode.CODE_30120005, "船只类型不能为空"),
	CODE_30120006(ErrorCode.CODE_30120006, "船只长度不能为空"),
	CODE_30120007(ErrorCode.CODE_30120007, "船只宽度不能为空"),
	CODE_30120008(ErrorCode.CODE_30120008, "船只编号不能为空"),
	CODE_30120009(ErrorCode.CODE_30120009, "船只载重不能为空"),
	CODE_30120010(ErrorCode.CODE_30120010, "船只负责人长度不能超过10位"),
	CODE_30120011(ErrorCode.CODE_30120011, "联系方式格式不正确"),
	CODE_30120012(ErrorCode.CODE_30120012, "船舶营业运输证不能为空"),
	CODE_30120013(ErrorCode.CODE_30120013, "船舶营业运输证长度不能超过20位"),
	CODE_30120014(ErrorCode.CODE_30120014, "船舶营业运输证图片链接不能为空"),
	CODE_30120015(ErrorCode.CODE_30120015, "船只照片不能为空"),
	CODE_30120016(ErrorCode.CODE_30120016, "船舶id不能为空"),
	CODE_30120017(ErrorCode.CODE_30120017, "船舶编号长度不能超过20位"),
	CODE_30120018(ErrorCode.CODE_30120018, "MMSI编号已存在"),
	CODE_30120019(ErrorCode.CODE_30120019, "船舶编号已存在"),
	CODE_30130121(ErrorCode.CODE_30130121, "船只照片最多上传3张"),
	CODE_30120021(ErrorCode.CODE_30120021, "审核状态不合法,只能为通过或不通过"),
	CODE_30120022(ErrorCode.CODE_30120022, "审核备注长度不能超过100位"),
	CODE_30120023(ErrorCode.CODE_30120023, "只有待审核状态的船舶才能进行审核操作"),
	CODE_30120024(ErrorCode.CODE_30120024, "船舶不存在"),
	CODE_30120025(ErrorCode.CODE_30120025, "只有已通过状态的船舶才能进行修改操作"),
	CODE_30120026(ErrorCode.CODE_30120026, "船舶编号已存在"),
	CODE_30120027(ErrorCode.CODE_30120027, "只有已禁用状态的船舶才能进行启用操作"),
	CODE_30120028(ErrorCode.CODE_30120028, "只有已通过状态的船舶才能进行禁用操作"),
	CODE_30120122(ErrorCode.CODE_30120122, "只有未通过/已禁用状态的船舶才能进行关闭操作"),
	CODE_30120030(ErrorCode.CODE_30120030, "船舶下存在设备,不能进行关闭操作"),
	CODE_30120031(ErrorCode.CODE_30120031, "文件名称不能为空"),
	CODE_30130127(ErrorCode.CODE_30130127, "船只照片最多上传3张"),
	CODE_30120033(ErrorCode.CODE_30120033, "船只照片最多上传1张"),
	CODE_30120034(ErrorCode.CODE_30120034, "船舶下未绑定设备,不能进行直播操作"),
	CODE_30120035(ErrorCode.CODE_30120035, "只有已通过状态的船舶才能进行直播操作"),
	CODE_30120036(ErrorCode.CODE_30120036, "船舶下未绑定该设备,不能进行直播或抓拍图片操作"),
	CODE_30120037(ErrorCode.CODE_30120037, "文件不存在"),
	CODE_30120038(ErrorCode.CODE_30120038, "承运商id不能为空"),
	CODE_30120039(ErrorCode.CODE_30120039, "坐标点存在空值"),
	CODE_30120040(ErrorCode.CODE_30120040, "经度差值应小于等于两度"),
	CODE_30120041(ErrorCode.CODE_30120041, "纬度差值应小于等于两度"),
	CODE_30120042(ErrorCode.CODE_30120042, "坐标点的经度或纬度存在空值"),
	CODE_30120043(ErrorCode.CODE_30120043, "船只照片不能为空"),
	CODE_30120044(ErrorCode.CODE_30120044, "船只照片最少1张，最多10张"),
	CODE_30120045(ErrorCode.CODE_30120045, "船只视频不能为空"),
	CODE_30120046(ErrorCode.CODE_30120046, "船只视频最少1个，最多5个"),
	CODE_30120047(ErrorCode.CODE_30120047, "船舶未认证才能进行绑定"),
	CODE_30120048(ErrorCode.CODE_30120048, "船舶已认证才能进行解绑"),
	CODE_30120049(ErrorCode.CODE_30120049, "范围超过限制，暂不支持查询"),
	CODE_30120050(ErrorCode.CODE_30120050, "坐标点数量小于3"),
	CODE_30120051(ErrorCode.CODE_30120051, "散货船细分不存在"),
	CODE_30120052(ErrorCode.CODE_30120052, "船舶类型不存在"),
	CODE_30120053(ErrorCode.CODE_30120053, "备注长度不能超过200"),
	CODE_30120054(ErrorCode.CODE_30120054, "当前船舶有在承运的运单，不能解绑"),
	CODE_30120055(ErrorCode.CODE_30120055, "营运证截止时间不能为空"),
	CODE_30120056(ErrorCode.CODE_30120056, "船舶经营人名称不能为空"),
	CODE_30120057(ErrorCode.CODE_30120057, "船舶经营人名称不超过32个字符"),
	CODE_30120058(ErrorCode.CODE_30120058, "船舶营运证号不超过32个字符"),
	CODE_30120059(ErrorCode.CODE_30120059, "身份证已过期"),
	CODE_30120060(ErrorCode.CODE_30120060, "身份证识别失败"),
	CODE_30120061(ErrorCode.CODE_30120061, "适任职务资格不能为空"),
	CODE_30120062(ErrorCode.CODE_30120062, "身份证正面识别失败"),
	CODE_30120063(ErrorCode.CODE_30120063, "身份证反面识别失败"),

	// 船舶认证相关
	CODE_30121001(ErrorCode.CODE_30121001, "船舶id不能为空"),
	CODE_30121002(ErrorCode.CODE_30121002, "手持身份证的照片不能为空"),
	CODE_30121003(ErrorCode.CODE_30121003, "船舶证书照片不能为空"),
	CODE_30121004(ErrorCode.CODE_30121004, "船舶认证申请不存在"),
	CODE_30121005(ErrorCode.CODE_30121005, "船舶未认证才能进行认证申请"),
	CODE_30121006(ErrorCode.CODE_30121006, "手持身份证的照片不能为空"),
	CODE_30121007(ErrorCode.CODE_30121007, "船舶证书照片不能为空"),
	CODE_30121008(ErrorCode.CODE_30121008, "已驳回的状态才能进行删除"),
	CODE_30121009(ErrorCode.CODE_30121009, "该船舶正在认证中，不能重复认证"),
	CODE_30121010(ErrorCode.CODE_30121010, "审核备注不能为空"),
	CODE_30121011(ErrorCode.CODE_30121011, "审核备注不能超过100个字符"),
	CODE_30121012(ErrorCode.CODE_30121012, "非待审核状态下不能进行审核"),

	// 船务信息服务费相关
	CODE_30122001(ErrorCode.CODE_30122001, "支付凭证不能为空"),
	CODE_30122002(ErrorCode.CODE_30122002, "船运单id不能为空"),
	CODE_30122003(ErrorCode.CODE_30122003, "类型不能为空"),
	CODE_30122004(ErrorCode.CODE_30122004, "类型不能存在"),
	CODE_30122005(ErrorCode.CODE_30122005, "只有待支付状态才能进行支付"),
	CODE_30122006(ErrorCode.CODE_30122006, "待支付信息服务费状态才能进行支付"),
	CODE_30122007(ErrorCode.CODE_30122007, "支付方式为线下才能进行线下支付"),
	CODE_30122008(ErrorCode.CODE_30122008, "船务信息服务费不存在"),
	CODE_30122009(ErrorCode.CODE_30122009, "当前状态不允许确认"),
	CODE_30122010(ErrorCode.CODE_30122010, "当前支付类型不允许确认"),
	CODE_30122011(ErrorCode.CODE_30122011, "支付时间不能为空"),

	// 航线相关
	CODE_30123001(ErrorCode.CODE_30123001, "始发地不能为空"),
	CODE_30123002(ErrorCode.CODE_30123002, "目的地不能为空"),
	CODE_30123003(ErrorCode.CODE_30123003, "始发地省份不能为空"),
	CODE_30123004(ErrorCode.CODE_30123004, "始发地城市不能为空"),
	CODE_30123005(ErrorCode.CODE_30123005, "目的地省份不能为空"),
	CODE_30123006(ErrorCode.CODE_30123006, "目的地城市不能为空"),
	CODE_30123007(ErrorCode.CODE_30123007, "航线id不能为空"),
	CODE_30123008(ErrorCode.CODE_30123008, "航线信息不存在"),
	CODE_30123009(ErrorCode.CODE_30123009, "权重不能为空 取值范围为0~10000"),

	// 船舶水尺相关
	CODE_30124001(ErrorCode.CODE_30124001, "吃水取值范围 0.01-100 保留两位小数"),
	CODE_30124002(ErrorCode.CODE_30124002, "干舷取值范围 0.01-100 保留两位小数"),
	CODE_30124003(ErrorCode.CODE_30124003, "载货量取值范围 1-100000 需为整数"),
	CODE_30124004(ErrorCode.CODE_30124004, "上传数据量最大不能超过300"),
	CODE_30124005(ErrorCode.CODE_30124005, "船舶信息不存在"),
	CODE_30124006(ErrorCode.CODE_30124006, "船舶水尺信息不存在"),
	CODE_30124007(ErrorCode.CODE_30124007, "该船舶水尺 吃水或干舷已存在"),
	CODE_30124008(ErrorCode.CODE_30124008, "吃水不能为空"),
	CODE_30124009(ErrorCode.CODE_30124009, "干舷不能为空 "),
	CODE_30124010(ErrorCode.CODE_30124010, "载货量不能为空 "),
	CODE_30124011(ErrorCode.CODE_30124011, "收集人信息不能为空 "),

	//船舶监控分享相关
	CODE_30125001(ErrorCode.CODE_30125001, "船舶监控分享的好友最多只能添加5位"),
	CODE_30125002(ErrorCode.CODE_30125002, "自己的设备不能分享给自己"),

	// 标签相关
	CODE_30126001(ErrorCode.CODE_30126001, "标签不存在"),
	CODE_30126002(ErrorCode.CODE_30126002, "标签名称不能为空"),
	CODE_30126003(ErrorCode.CODE_30126003, "标签名称长度不能超过10个字符"),
	CODE_30126004(ErrorCode.CODE_30126004, "此标签被引用不支持删除"),
	CODE_30126005(ErrorCode.CODE_30126005, "标签名称已存在"),

	// 置顶相关
	CODE_30127001(ErrorCode.CODE_30127001, "业务ID不能为空"),
	CODE_30127002(ErrorCode.CODE_30127002, "类型不能为空"),
	CODE_30127003(ErrorCode.CODE_30127003, "类型不存在"),

	// 承运商船运需求相关
	CODE_30128001(ErrorCode.CODE_30128001, "当前状态不是确认中,不能进行接单操作"),
	CODE_30128002(ErrorCode.CODE_30128002, "定金金额只能是大于0小于100亿的数字"),
	CODE_30128003(ErrorCode.CODE_30128003, "定金金额必须精确到小数点后2位"),
	CODE_30128004(ErrorCode.CODE_30128004, "不存在的承运商船运需求"),
	CODE_30128005(ErrorCode.CODE_30128005, "当前状态不是确认中,不能进行接单操作"),
	CODE_30128006(ErrorCode.CODE_30128006, "拒绝备注不能为空"),
	CODE_30128007(ErrorCode.CODE_30128007, "拒绝备注不能超过200个字符"),
	CODE_30128008(ErrorCode.CODE_30128008, "当前状态不是已接单,不能进行收到定金操作"),
	CODE_30128009(ErrorCode.CODE_30128009, "货品类型不能为空"),
	CODE_30128010(ErrorCode.CODE_30128010, "货品类型长度不能超过32位"),
	CODE_30128011(ErrorCode.CODE_30128011, "船型不能为空"),
	CODE_30128012(ErrorCode.CODE_30128012, "船型长度不能超过32位"),
	CODE_30128013(ErrorCode.CODE_30128013, "始发地码头id不能为空"),
	CODE_30128014(ErrorCode.CODE_30128014, "始发地码头名称不能为空"),
	CODE_30128015(ErrorCode.CODE_30128015, "目的地码头id不能为空"),
	CODE_30128016(ErrorCode.CODE_30128016, "目的地码头名称不能为空"),
	CODE_30128017(ErrorCode.CODE_30128017, "目的地码头名称不能为空"),
	CODE_30128018(ErrorCode.CODE_30128018, "航线id不能为空"),
	CODE_30128019(ErrorCode.CODE_30128019, "航线始发地不能为空"),
	CODE_30128020(ErrorCode.CODE_30128020, "航线目的地不能为空"),
	CODE_30128021(ErrorCode.CODE_30128021, "意向单价不能为空"),
	CODE_30128022(ErrorCode.CODE_30128022, "意向单价必须大于0小于100亿且最多保留两位小数"),
	CODE_30128023(ErrorCode.CODE_30128023, "意向吨位不能为空"),
	CODE_30128024(ErrorCode.CODE_30128024, "意向吨位必须大于0且为正整数"),
	CODE_30128025(ErrorCode.CODE_30128025, "装载日期不能为空"),
	CODE_30128026(ErrorCode.CODE_30128026, "宽限天数不能为空"),
	CODE_30128027(ErrorCode.CODE_30128027, "宽限天数必须大于0且为正整数"),
	CODE_30128028(ErrorCode.CODE_30128028, "装卸天数不能为空"),
	CODE_30128029(ErrorCode.CODE_30128029, "装卸天数必须大于0且为正整数"),
	CODE_30128030(ErrorCode.CODE_30128030, "滞期费必须大于0小于100亿且最多保留两位小数"),
	CODE_30128031(ErrorCode.CODE_30128031, "海事费用不能为空"),
	CODE_30128032(ErrorCode.CODE_30128032, "吨位随船不能为空"),
	CODE_30128033(ErrorCode.CODE_30128033, "吨位不随船时最大吨位数必须大于0且为正整数"),
	CODE_30128034(ErrorCode.CODE_30128034, "吨位不随船时最大吨位数必须大于0且为正整数"),
	CODE_30128035(ErrorCode.CODE_30128035, "联系人不能为空"),
	CODE_30128036(ErrorCode.CODE_30128036, "联系人长度不能超过32位"),
	CODE_30128037(ErrorCode.CODE_30128037, "联系电话不能为空"),
	CODE_30128038(ErrorCode.CODE_30128038, "联系电话格式不正确"),
	CODE_30128039(ErrorCode.CODE_30128039, "补充约定长度不能超过200位"),
	CODE_30128040(ErrorCode.CODE_30128040, "货主船运需求不存在"),
	CODE_30128041(ErrorCode.CODE_30128041, "当前状态不合法,不能进行指派操作"),
	CODE_30128042(ErrorCode.CODE_30128042, "当前状态不合法,不能进行开始操作"),
	CODE_30128043(ErrorCode.CODE_30128043, "当前状态不合法,不能进行修改操作"),
	CODE_30128044(ErrorCode.CODE_30128044, "吨位不随船时吨位要求不能为空"),
	CODE_30128045(ErrorCode.CODE_30128045, "吨位不随船时最大吨位必须大于等于最小吨位"),
	CODE_30128046(ErrorCode.CODE_30128046, "当前状态不合法,不能进行完成操作"),
	CODE_30128047(ErrorCode.CODE_30128047, "当前货主需求下没有关联平台船运单,不能进行完成操作"),
	CODE_30128048(ErrorCode.CODE_30128048, "船务信息服务费不能大于定金"),
	CODE_30128049(ErrorCode.CODE_30128049, "船务信息服务费必须精确到小数点后2位"),
	CODE_30128050(ErrorCode.CODE_30128050, "船运定金不能为空"),
	CODE_30128051(ErrorCode.CODE_30128051, "船务信息服务费不能为空"),
	CODE_30128052(ErrorCode.CODE_30128052, "支付类型不存在"),
	CODE_30128053(ErrorCode.CODE_30128053, "船务信息服务费不能为空"),
	CODE_30128054(ErrorCode.CODE_30128054, "是否包含排水槽字段不能为空"),
	CODE_30128055(ErrorCode.CODE_30128055, "是否提交不能为空"),
	CODE_30128056(ErrorCode.CODE_30128056, "当前货主找船需求下没有关联船运单,不能进行完成操作"),
	CODE_30128057(ErrorCode.CODE_30128057, "当前登录用户不是该船运需求的船务专员，不可操作"),
	CODE_30128058(ErrorCode.CODE_30128058, "当前状态不是处理中,不能进行提交操作"),
	CODE_30128059(ErrorCode.CODE_30128059, "运价不能为空"),
	CODE_30128060(ErrorCode.CODE_30128060, "吨位不能为空"),
	CODE_30128061(ErrorCode.CODE_30128061, "船运定金不能为空"),
	CODE_30128062(ErrorCode.CODE_30128062, "船务信息服务费不能为空"),
	CODE_30128063(ErrorCode.CODE_30128063, "运费结算确认节点只能为待发航或待卸货"),
	CODE_30128064(ErrorCode.CODE_30128064, "货主id不能为空"),
	CODE_30128065(ErrorCode.CODE_30128065, "货主公司信息不能为空"),
	CODE_30128066(ErrorCode.CODE_30128066, "货品类型不能为空"),
	CODE_30128067(ErrorCode.CODE_30128067, "始发地码头id不能为空"),
	CODE_30128068(ErrorCode.CODE_30128068, "始发地码头名称不能为空"),
	CODE_30128069(ErrorCode.CODE_30128069, "目的地码头id不能为空"),
	CODE_30128070(ErrorCode.CODE_30128070, "目的地码头名称不能为空"),
	CODE_30128071(ErrorCode.CODE_30128071, "意向单价不能为空"),
	CODE_30128072(ErrorCode.CODE_30128072, "运输最大吨数不能为空"),
	CODE_30128073(ErrorCode.CODE_30128073, "运输最小吨数不能为空"),
	CODE_30128074(ErrorCode.CODE_30128074, "装载日期不能为空"),
	CODE_30128075(ErrorCode.CODE_30128075, "装载日期的宽限天数不能为空"),
	CODE_30128076(ErrorCode.CODE_30128076, "装卸天数不能为空"),
	CODE_30128077(ErrorCode.CODE_30128077, "装卸天数必须在1-127之间"),
	CODE_30128078(ErrorCode.CODE_30128078, "滞期费不能为空"),
	CODE_30128079(ErrorCode.CODE_30128079, "特殊说明不能超过200个字符"),
	CODE_30128080(ErrorCode.CODE_30128080, "定金支付方式不能为空"),
	CODE_30128081(ErrorCode.CODE_30128081, "船务信息服务费不能为空"),
	CODE_30128082(ErrorCode.CODE_30128082, "船运定金的取值范围为大于0的正整数,最多10位"),
	CODE_30128083(ErrorCode.CODE_30128083, "平台船运需求ID不能为空"),
	CODE_30128084(ErrorCode.CODE_30128084, "承运船相关信息不能为空"),
	CODE_30128085(ErrorCode.CODE_30128085, "找船需求已经关联船运单"),
	CODE_30128086(ErrorCode.CODE_30128086, "只有状态为已发布和待发布的平台船运需求才允许指派专员操作"),
	CODE_30128087(ErrorCode.CODE_30128087, "只有状态为已发布的平台船运需求才允许关闭"),
	CODE_30128088(ErrorCode.CODE_30128088, "不存在的平台船运需求"),
	CODE_30128089(ErrorCode.CODE_30128089, "该平台船运需求已经关联船运单,不能关闭"),
	CODE_30128090(ErrorCode.CODE_30128090, "货主船运需求已经被关联"),
	CODE_30128091(ErrorCode.CODE_30128091, "已确认的状态才能进行绑定"),
	CODE_30128092(ErrorCode.CODE_30128092, "运货吨数不能为空"),
	CODE_30128093(ErrorCode.CODE_30128093, "船运定金不能为空"),
	CODE_30128094(ErrorCode.CODE_30128094, "需要智能推荐船舶的船运需求id不能为空"),
	CODE_30128095(ErrorCode.CODE_30128095, "邀请抢单的船舶id不能为空"),
	CODE_30128096(ErrorCode.CODE_30128096, "船运需求已发布状态才有智能推荐船舶"),
	CODE_30128097(ErrorCode.CODE_30128097, "没有可以邀请的船舶"),
	CODE_30128098(ErrorCode.CODE_30128098, "电联号码类型不能为空"),
	CODE_30128099(ErrorCode.CODE_30128099, "电联号码类型不存在"),
	CODE_30128100(ErrorCode.CODE_30128100, "电联号码不能为空"),
	CODE_30128101(ErrorCode.CODE_30128101, "电联号码格式错误"),
	CODE_30128102(ErrorCode.CODE_30128102, "只有状态为待发布的平台船运需求才允许发布"),
	CODE_30128103(ErrorCode.CODE_30128103, "指派专员后才可以发布"),
	CODE_30128104(ErrorCode.CODE_30128104, "传入的船运需求id不是本平台推送的船运需求"),
	CODE_30128105(ErrorCode.CODE_30128105, "需求等级不能为空"),
	CODE_30128106(ErrorCode.CODE_30128106, "需求等级不存在"),
	CODE_30128107(ErrorCode.CODE_30128107, "需要修改需求等级的船运需求id不能为空"),
	CODE_30128108(ErrorCode.CODE_30128108, "没有可以修改需求等级的船运需求"),
	CODE_30128109(ErrorCode.CODE_30128109, "该平台船运需求无法修改"),
	CODE_30128110(ErrorCode.CODE_30128110, "不是当前船运需求的船务专员，不能修改"),
	CODE_30128111(ErrorCode.CODE_30128111, "已帮您成功匹配到船主，不能取消"),
	CODE_30128112(ErrorCode.CODE_30128112, "当前状态不是处理中、找船中状态,不允许取消"),
	CODE_30128113(ErrorCode.CODE_30128113, "吨位不随船时最大吨位必须大于等于最小吨位"),
	CODE_30128114(ErrorCode.CODE_30128114, "装载日期宽限天数不能为负数"),
	CODE_30128115(ErrorCode.CODE_30128115, "装卸天数不能为负数"),
	CODE_30128116(ErrorCode.CODE_30128116, "船运定金的取值范围为大于0的正整数,最多10位"),
	CODE_30128117(ErrorCode.CODE_30128117, "选择链云垫付需完成个人实名"),


	// 承运商船运需求
	CODE_30129001(ErrorCode.CODE_30129001, "承运商接单信息不存在"),
	CODE_30129002(ErrorCode.CODE_30129002, "承运商修改接单信息状态不合法"),
	CODE_30129003(ErrorCode.CODE_30129003, "平台船运需求不存在"),
	CODE_30129004(ErrorCode.CODE_30129004, "平台船运需求已关闭"),
	CODE_30129005(ErrorCode.CODE_30129005, "承运商取消接单信息状态不合法"),
	CODE_30129006(ErrorCode.CODE_30129006, "承运商确认接单信息状态不合法"),
	CODE_30129007(ErrorCode.CODE_30129007, "承运商只能选择一条船舶"),
	CODE_30129008(ErrorCode.CODE_30129008, "只有处于卸货中、已卸货或已完成状态的运单才允许抢单"),
	CODE_30129009(ErrorCode.CODE_30129009, "晚了一步，已被其他船主抢单"),
	CODE_30129010(ErrorCode.CODE_30129010, "不可重复接单"),
	CODE_30129011(ErrorCode.CODE_30129011, "船运需求不存在"),
	CODE_30129012(ErrorCode.CODE_30129012, "已发布的船运需求才能接单"),
	CODE_30129013(ErrorCode.CODE_30129013, "无法对链云船运需求进行操作"),
	CODE_30129014(ErrorCode.CODE_30129014, "无法对链云接单信息进行操作"),
	CODE_30129015(ErrorCode.CODE_30129015, "该接单信息与船运需求不匹配"),
	CODE_30129016(ErrorCode.CODE_30129016, "当前船运需求已结束"),

	// 船主线索相关
	CODE_30130001(ErrorCode.CODE_30130001, "船主线索信息不存在"),
	CODE_30130002(ErrorCode.CODE_30130002, "当前状态是已处理,不能进行提交操作"),
	CODE_30130003(ErrorCode.CODE_30130003, "备注不能为空"),
	CODE_30130004(ErrorCode.CODE_30130004, "备注长度应为1-200位"),

	// 运价综合指数
	CODE_30131001(ErrorCode.CODE_30131001, "运价综合指数不存在"),
	CODE_30131002(ErrorCode.CODE_30131002, "日期不能为空"),
	CODE_30131003(ErrorCode.CODE_30131003, "综合指数只能0-1000保留两位小数"),
	CODE_30131004(ErrorCode.CODE_30131004, "当前日期已存在指数"),
	CODE_30131005(ErrorCode.CODE_30131005, "综合指数不能为空"),
	CODE_30131006(ErrorCode.CODE_30131006, "日期不能重复"),
	CODE_30131007(ErrorCode.CODE_30131007, "未发布状态才能进行修改"),
	CODE_30131008(ErrorCode.CODE_30131008, "未发布状态才能进行删除"),
	CODE_30131009(ErrorCode.CODE_30131009, "未发布状态才能进行发布"),
	CODE_30131010(ErrorCode.CODE_30131010, "已发布状态才能进行撤回"),
	CODE_30131011(ErrorCode.CODE_30131011, "不能发布今日之后指数数据"),

	// 运价综合指数配置相关
	CODE_30132001(ErrorCode.CODE_30132001, "运价综合指数配置不存在"),
	CODE_30132002(ErrorCode.CODE_30132002, "始发地列表不能为空"),
	CODE_30132003(ErrorCode.CODE_30132003, "始发地省份编码"),
	CODE_30132004(ErrorCode.CODE_30132004, "始发地城市编码"),
	CODE_30132005(ErrorCode.CODE_30132005, "始发地权重不能为空"),
	CODE_30132006(ErrorCode.CODE_30132006, "始发地存在重复数据"),
	CODE_30132007(ErrorCode.CODE_30132007, "航线不能为空"),
	CODE_30132008(ErrorCode.CODE_30132008, "航线id不能为空"),
	CODE_30132009(ErrorCode.CODE_30132009, "航线不存在"),
	CODE_30132010(ErrorCode.CODE_30132010, "航线权重不能为空"),
	CODE_30132011(ErrorCode.CODE_30132011, "航线吨位不能为空"),
	CODE_30132012(ErrorCode.CODE_30132012, "存在重复航线"),
	CODE_30132013(ErrorCode.CODE_30132013, "吨位类型不能为空"),
	CODE_30132014(ErrorCode.CODE_30132014, "吨位类型不存在"),
	CODE_30132015(ErrorCode.CODE_30132015, "吨位权重不能为空"),
	CODE_30132016(ErrorCode.CODE_30132016, "存在重复吨位"),
	CODE_30132017(ErrorCode.CODE_30132017, "吨位权重之和必须为100%"),
	CODE_30132018(ErrorCode.CODE_30132018, "航线权重之和必须为100%"),
	CODE_30132019(ErrorCode.CODE_30132019, "始发地权重之和必须为100%"),

	//消息相关
	CODE_30133001(ErrorCode.CODE_30133001, "流水号不能为空"),
	CODE_30133002(ErrorCode.CODE_30133002, "场景ID不能为空"),
	CODE_30133003(ErrorCode.CODE_30133003, "验证码校验参数不能为空"),
	CODE_30133004(ErrorCode.CODE_30133004, "手机号不存在"),
	CODE_30133005(ErrorCode.CODE_30133005, "手机号被禁用"),


	// 货主船运需求
	CODE_30134001(ErrorCode.CODE_30134001, "当前状态不是处理中，待确认,找船中状态,不允许取消"),
	CODE_30134002(ErrorCode.CODE_30134002, "当前状态不是待货主确认,不允许进行确认操作"),
	CODE_30134003(ErrorCode.CODE_30134003, "吨位不随船时最大吨位必须大于等于最小吨位"),
	CODE_30134004(ErrorCode.CODE_30134004, "装载日期宽限天数不能为负数"),
	CODE_30134005(ErrorCode.CODE_30134005, "装卸天数不能为负数"),
	CODE_30134006(ErrorCode.CODE_30134006, "船运定金的取值范围为大于0的正整数,最多10位"),
	CODE_30134007(ErrorCode.CODE_30134007, "选择链云垫付需完成个人实名"),
	CODE_30134008(ErrorCode.CODE_30134008, "船运需求不存在"),
	CODE_30134009(ErrorCode.CODE_30134009, "船运定金不能为空"),
	CODE_30134010(ErrorCode.CODE_30134010, "已帮您成功匹配到船主，不能取消"),
	CODE_30134011(ErrorCode.CODE_30134011, "货品类型不能为空"),
	CODE_30134012(ErrorCode.CODE_30134012, "货品类型长度不能超过32位"),
	CODE_30134013(ErrorCode.CODE_30134013, "船型不能为空"),
	CODE_30134014(ErrorCode.CODE_30134014, "排水槽字段不能为空"),
	CODE_30134015(ErrorCode.CODE_30134015, "始发地码头id不能为空"),
	CODE_30134016(ErrorCode.CODE_30134016, "始发地码头名称不能为空"),
	CODE_30134017(ErrorCode.CODE_30134017, "目的地码头id不能为空"),
	CODE_30134018(ErrorCode.CODE_30134018, "目的地码头名称不能为空"),
	CODE_30134019(ErrorCode.CODE_30134019, "意向单价不能为空"),
	CODE_30134020(ErrorCode.CODE_30134020, "意向单价必须大于0小于100亿且最多保留两位小数"),
	CODE_30134021(ErrorCode.CODE_30134021, "意向吨位不能为空"),
	CODE_30134022(ErrorCode.CODE_30134022, "意向吨位必须大于0且为正整数"),
	CODE_30134023(ErrorCode.CODE_30134023, "装载日期不能为空"),
	CODE_30134024(ErrorCode.CODE_30134024, "宽限天数不能为空"),
	CODE_30134025(ErrorCode.CODE_30134025, "装卸天数不能为空"),
	CODE_30134026(ErrorCode.CODE_30134026, "滞期费必须大于0小于100亿且最多保留两位小数"),
	CODE_30134027(ErrorCode.CODE_30134027, "海事费用不能为空"),
	CODE_30134028(ErrorCode.CODE_30134028, "吨位不随船时最大吨位数必须大于0且为正整数"),
	CODE_30134029(ErrorCode.CODE_30134029, "吨位不随船时最小吨位数必须大于0且为正整数"),
	CODE_30134030(ErrorCode.CODE_30134030, "联系人长度不能超过32位"),
	CODE_30134031(ErrorCode.CODE_30134031, "联系电话不能为空"),
	CODE_30134032(ErrorCode.CODE_30134032, "联系电话格式不正确"),
	CODE_30134033(ErrorCode.CODE_30134033, "补充约定长度不能超过200位"),
	CODE_30134034(ErrorCode.CODE_30134034, "船舶类型不存在"),
	CODE_30134035(ErrorCode.CODE_30134035, "货主已确认找船，请勿重复动作"),
	CODE_30134036(ErrorCode.CODE_30134036, "下单类型不能为空"),
	CODE_30134037(ErrorCode.CODE_30134037, "下单类型不存在"),
	CODE_30134038(ErrorCode.CODE_30134038, "参数错误"),


	// 运价指数版本相关
	CODE_30135001(ErrorCode.CODE_30135001, "版本日期不能为空"),
	CODE_30135002(ErrorCode.CODE_30135002, "版本日期已存在"),
	CODE_30135004(ErrorCode.CODE_30135004, "存在指数数据，不能修改"),
	CODE_30135005(ErrorCode.CODE_30135005, "存在指数数据，不能删除"),
	CODE_30135006(ErrorCode.CODE_30135006, "版本不存在"),
	CODE_30135007(ErrorCode.CODE_30135007, "版本日期不能选择今天之后的日期"),

	// 运价指数版本记录相关
	CODE_30136001(ErrorCode.CODE_30136001, "运价指数版本记录不存在"),
	CODE_30136002(ErrorCode.CODE_30136002, "版本id不能为空"),
	CODE_30136003(ErrorCode.CODE_30136003, "待提交、已驳回状态才能进行提交、修改、删除操作"),
	CODE_30136004(ErrorCode.CODE_30136004, "待发布、已撤回状态才能进行发布"),
	CODE_30136005(ErrorCode.CODE_30136005, "待发布、已撤回状态才能进行驳回"),
	CODE_30136006(ErrorCode.CODE_30136006, "已发布状态且是最新发布数据才能进行撤回"),
	CODE_30136007(ErrorCode.CODE_30136007, "当前已经是最初发布版本，无法进行撤回"),
	CODE_30136008(ErrorCode.CODE_30136008, "备注不能超过100个字符"),
	CODE_30136009(ErrorCode.CODE_30136009, "待提交、已驳回状态才能进行保存"),
	CODE_30136010(ErrorCode.CODE_30136010, "指数数据为空，不能进行提交"),

	// 船运单相关
	CODE_30137001(ErrorCode.CODE_30137001, "船运单不存在"),
	CODE_30137002(ErrorCode.CODE_30137002, "货名不能为空"),
	CODE_30137003(ErrorCode.CODE_30137003, "货名不能超过32个字符"),
	CODE_30137004(ErrorCode.CODE_30137004, "货主不能为空"),
	CODE_30137005(ErrorCode.CODE_30137005, "联系人不能为空"),
	CODE_30137006(ErrorCode.CODE_30137006, "手机号不能为空"),
	CODE_30137007(ErrorCode.CODE_30137007, "运价不能为空"),
	CODE_30137008(ErrorCode.CODE_30137008, "运价必须大于0小于100亿"),
	CODE_30137009(ErrorCode.CODE_30137009, "运货量不能为空"),
	CODE_30137010(ErrorCode.CODE_30137010, "装载日期不能为空"),
	CODE_30137011(ErrorCode.CODE_30137011, "宽限天数不能为空"),
	CODE_30137012(ErrorCode.CODE_30137012, "装卸天数不能为空"),
	CODE_30137013(ErrorCode.CODE_30137013, "船舶不能为空"),
	CODE_30137014(ErrorCode.CODE_30137014, "联系人不能超过32个字符"),
	CODE_30137015(ErrorCode.CODE_30137015, "手机号格式错误"),
	CODE_30137016(ErrorCode.CODE_30137016, "当前用户无操作权限"),
	CODE_30137017(ErrorCode.CODE_30137017, "未指派上游专员"),
	CODE_30137018(ErrorCode.CODE_30137018, "未指派下游专员"),
	CODE_30137019(ErrorCode.CODE_30137019, "始发港不能为空"),
	CODE_30137020(ErrorCode.CODE_30137020, "目的港不能为空"),
	CODE_30137021(ErrorCode.CODE_30137021, "待发起合同状态才能进行修改"),
	CODE_30137022(ErrorCode.CODE_30137022, "待发航前状态才能进行关闭"),
	CODE_30137023(ErrorCode.CODE_30137023, "待装货才能进行开始装货"),
	CODE_30137024(ErrorCode.CODE_30137024, "装货中才能进行发航申请"),
	CODE_30137025(ErrorCode.CODE_30137025, "卸货中才能进行确认卸货"),
	CODE_30137026(ErrorCode.CODE_30137026, "金额不能为空"),
	CODE_30137027(ErrorCode.CODE_30137027, "船务信息服务费不能为空"),
	CODE_30137028(ErrorCode.CODE_30137028, "定金金额必须大于0小于100000000.00"),
	CODE_30137029(ErrorCode.CODE_30137029, "船务信息服务费必须大于0小于100000000.00"),
	CODE_30137030(ErrorCode.CODE_30137030, "船务信息服务费不能大于定金金额"),
	CODE_30137031(ErrorCode.CODE_30137031, "合同类型不能为空"),
	CODE_30137032(ErrorCode.CODE_30137032, "合同类型不存在"),
	CODE_30137033(ErrorCode.CODE_30137033, "船务信息服务费要大于平台设置的安装推广佣金"),
	CODE_30137034(ErrorCode.CODE_30137034, "货主联系地址不能为空"),
	CODE_30137035(ErrorCode.CODE_30137035, "船主联系地址不能为空"),
	CODE_30137036(ErrorCode.CODE_30137036, "货主联系地址不能超过128个字符"),
	CODE_30137037(ErrorCode.CODE_30137037, "船主联系地址不能超过128个字符"),
	CODE_30137038(ErrorCode.CODE_30137038, "只有待发起合同状态才能发起合同"),
	CODE_30137039(ErrorCode.CODE_30137039, "已卸货状态才能完成"),
	CODE_30137040(ErrorCode.CODE_30137040, "支付类型不能为空"),
	CODE_30137041(ErrorCode.CODE_30137041, "支付类型不存在"),
	CODE_30137042(ErrorCode.CODE_30137042, "合同文件不能为空"),
	CODE_30137043(ErrorCode.CODE_30137043, "运费结算吨位确认节点不能为空"),
	CODE_30137044(ErrorCode.CODE_30137044, "运费结算吨位确认节点不存在"),
	CODE_30137045(ErrorCode.CODE_30137045, "货主还未确认运费结算吨位"),
	CODE_30137046(ErrorCode.CODE_30137046, "船主还未确认运费结算吨位"),
	CODE_30137047(ErrorCode.CODE_30137047, "货主不存在"),
	CODE_30137048(ErrorCode.CODE_30137048, "船主不存在"),
	CODE_30137049(ErrorCode.CODE_30137049, "货主未进行个人认证或者企业认证"),
	CODE_30137050(ErrorCode.CODE_30137050, "船主未进行个人认证或者企业认证"),
	CODE_30137051(ErrorCode.CODE_30137051, "货主船务信息服务费不能为空"),
	CODE_30137052(ErrorCode.CODE_30137052, "船主船务信息服务费不能为空"),
	CODE_30137053(ErrorCode.CODE_30137053, "海事费用不存在"),
	CODE_30137054(ErrorCode.CODE_30137054, "货主和船主不能是同一个账号"),
	CODE_30137055(ErrorCode.CODE_30137055, "船运单已经被绑定"),
	CODE_30137056(ErrorCode.CODE_30137056, "平台船运需求已经被关联"),
	CODE_30137057(ErrorCode.CODE_30137057, "船运单已关闭"),
	CODE_30137058(ErrorCode.CODE_30137058, "已经发航确认"),
	CODE_30137059(ErrorCode.CODE_30137059, "已经同意卸货"),
	CODE_30137060(ErrorCode.CODE_30137060, "装货时间必须大于创建时间且小于等于当前时间"),
	CODE_30137061(ErrorCode.CODE_30137061, "发航时间不能为空"),
	CODE_30137062(ErrorCode.CODE_30137062, "发航时间必须大于装货时间且小于等于当前时间且小于到港时间"),
	CODE_30137063(ErrorCode.CODE_30137063, "卸货完成时间不能为空"),
	CODE_30137064(ErrorCode.CODE_30137064, "卸货完成时间必须大于到到港时间且小于等于当前时间"),
	CODE_30137065(ErrorCode.CODE_30137065, "卸货完成时间必须大于卸货开始时间"),
	CODE_30137066(ErrorCode.CODE_30137066, "请填写装载吨位"),
	CODE_30137067(ErrorCode.CODE_30137067, "请填写发航时间、卸货完成时间"),
	CODE_30137068(ErrorCode.CODE_30137068, "请填写卸货信息"),

	// 船运单相关
	CODE_30138001(ErrorCode.CODE_30138001, "船运单不存在"),
	CODE_30138002(ErrorCode.CODE_30138002, "合同签署状态才能进行签署"),
	CODE_30138003(ErrorCode.CODE_30138003, "货主未签署状态才能进行签署"),
	CODE_30138004(ErrorCode.CODE_30138004, "船主未签署状态才能进行签署"),
	CODE_30138005(ErrorCode.CODE_30138005, "货主签署完成之后才能进行签署"),
	CODE_30138006(ErrorCode.CODE_30138006, "当前用户无权限进行确认"),
	CODE_30138007(ErrorCode.CODE_30138007, "付款凭证不能为空"),
	CODE_30138008(ErrorCode.CODE_30138008, "自行付款才能进行支付"),
	CODE_30138009(ErrorCode.CODE_30138009, "未支付状态才能进行支付"),
	CODE_30138010(ErrorCode.CODE_30138010, "船主未确认状态才能进行确认"),
	CODE_30138011(ErrorCode.CODE_30138011, "待支付定金状态才能进行确认"),
	CODE_30138012(ErrorCode.CODE_30138012, "待支付定金状态才能进行支付"),
	CODE_30138013(ErrorCode.CODE_30138013, "船运定金不存在"),
	CODE_30138014(ErrorCode.CODE_30138014, "船运定金未结算或未支付"),
	CODE_30138015(ErrorCode.CODE_30138015, "您已经确吨位，不可重复操作！"),
	CODE_30138016(ErrorCode.CODE_30138016, "货主确认完之后才能进行确认"),
	CODE_30138017(ErrorCode.CODE_30138017, "待发航状态才能进行确认"),
	CODE_30138018(ErrorCode.CODE_30138018, "待卸货状态才能进行确认"),
	CODE_30138019(ErrorCode.CODE_30138019, "船运单已关闭"),
	CODE_30138020(ErrorCode.CODE_30138020, "银行账户不存在"),
	CODE_30138021(ErrorCode.CODE_30138021, "无法对链云船运单支付定金"),
	CODE_30138022(ErrorCode.CODE_30138022, "待支付定金状态才能进行修改银行账户"),
	CODE_30138023(ErrorCode.CODE_30138023, "银行信息已存在"),
	CODE_30138024(ErrorCode.CODE_30138024, "开户人和开户名称非承运商实名"),
	CODE_30138025(ErrorCode.CODE_30138025, "当前船运单已被三方所关联，您无法操作"),

	// 船运单明细相关
	CODE_30139001(ErrorCode.CODE_30139001, "装货日期不能为空"),
	CODE_30139002(ErrorCode.CODE_30139002, "集港货物吨位不能为空"),
	CODE_30139003(ErrorCode.CODE_30139003, "集港货物吨位应大于0"),
	CODE_30139004(ErrorCode.CODE_30139004, "备注不能超过200个字符"),
	CODE_30139005(ErrorCode.CODE_30139005, "装载吨位不能为空"),
	CODE_30139006(ErrorCode.CODE_30139006, "装载吨位必须大于0小于100亿"),
	CODE_30139007(ErrorCode.CODE_30139007, "量方吨位必须大于0"),
	CODE_30139008(ErrorCode.CODE_30139008, "运费结算吨位必须大于0"),
	CODE_30139009(ErrorCode.CODE_30139009, "六处水尺-1必须大于0"),
	CODE_30139010(ErrorCode.CODE_30139010, "六处水尺-2必须大于0"),
	CODE_30139011(ErrorCode.CODE_30139011, "六处水尺-3必须大于0"),
	CODE_30139012(ErrorCode.CODE_30139012, "六处水尺-4必须大于0"),
	CODE_30139013(ErrorCode.CODE_30139013, "六处水尺-5必须大于0"),
	CODE_30139014(ErrorCode.CODE_30139014, "六处水尺-6必须大于0"),
	CODE_30139015(ErrorCode.CODE_30139015, "滞期天数必须大于等于0，小于1000的数字"),
	CODE_30139016(ErrorCode.CODE_30139016, "卸货吨位必须大于0"),
	CODE_30139017(ErrorCode.CODE_30139017, "卸货量方吨位必须大于0"),
	CODE_30139018(ErrorCode.CODE_30139018, "运费结算吨位必须大于0"),
	CODE_30139019(ErrorCode.CODE_30139019, "船运单明细不存在"),
	CODE_30139020(ErrorCode.CODE_30139020, "已完成状态不能进行修改"),
	CODE_30139021(ErrorCode.CODE_30139021, "运输中状态不能进行修改"),
	CODE_30139022(ErrorCode.CODE_30139022, "运费结算吨位不能为空"),
	CODE_30139023(ErrorCode.CODE_30139023, "待卸货节点才能进行确认吨位"),
	CODE_30139024(ErrorCode.CODE_30139024, "运费结算吨位不能为空"),
	CODE_30139025(ErrorCode.CODE_30139025, "实际运费不能为空"),
	CODE_30139026(ErrorCode.CODE_30139026, "实际运费必须大于0"),
	CODE_30139027(ErrorCode.CODE_30139027, "接档记录信息不能为空"),
	CODE_30139028(ErrorCode.CODE_30139028, "船舶到港时间应早于装货上档时间"),
	CODE_30139029(ErrorCode.CODE_30139029, "吨位证明不能为空"),

	// 船运单明细相关
	CODE_30140001(ErrorCode.CODE_30140001, "船运单明细相关不存在"),
	CODE_30140002(ErrorCode.CODE_30140002, "待发航才能进行发航确认"),
	CODE_30140003(ErrorCode.CODE_30140003, "运输中才能进行到港确认"),
	CODE_30140004(ErrorCode.CODE_30140004, "待卸货才能进行同意卸货"),
	CODE_30140005(ErrorCode.CODE_30140005, "您已经进行发航申请并确认吨位，不可重复操作！"),
	CODE_30140006(ErrorCode.CODE_30140006, "您已经进行同意卸货并确认吨位，不可重复操作！"),
	CODE_30140007(ErrorCode.CODE_30140007, "是否排水不能为空"),
	CODE_30140008(ErrorCode.CODE_30140008, "实际结算吨位不能为空"),
	CODE_30140009(ErrorCode.CODE_30140009, "实际结算吨位必须大于0"),
	CODE_30140010(ErrorCode.CODE_30140010, "船主未确认运费结算吨位才能进行修改"),
	CODE_30140011(ErrorCode.CODE_30140011, "待发航的状态才能进行修改运费结算吨位"),
	CODE_30140012(ErrorCode.CODE_30140012, "待卸货的状态才能进行修改运费结算吨位"),
	CODE_30140013(ErrorCode.CODE_30140013, "无法对链云船运单进行操作"),
	CODE_30140014(ErrorCode.CODE_30140014, "appid不能为空"),
	CODE_30140015(ErrorCode.CODE_30140015, "运输中、待卸货、卸货中才能上传排水视频"),
	CODE_30140016(ErrorCode.CODE_30140016, "排水选择了是才能上传排水视频"),
	CODE_30140017(ErrorCode.CODE_30140017, "货主已发航确认，请勿重复动作"),
	CODE_30140018(ErrorCode.CODE_30140018, "货主已同意卸货，请勿重复动作"),
	CODE_30140019(ErrorCode.CODE_30140019, "船主已到港确认，请勿重复动作"),
	CODE_30140020(ErrorCode.CODE_30140020, "待发航状态才能进行上传发船信息"),
	CODE_30140021(ErrorCode.CODE_30140021, "待卸货状态才能进行上传卸货信息"),
	CODE_30140022(ErrorCode.CODE_30140022, "满载图片或视频不能为空"),
	CODE_30140023(ErrorCode.CODE_30140023, "卸货图片或视频不能为空"),
	CODE_30140024(ErrorCode.CODE_30140024, "排水视频不能为空"),





	// 导入相关
	CODE_30141001(ErrorCode.CODE_30141001, "上传的数据不能为空"),
	CODE_30141002(ErrorCode.CODE_30141002, "上传数据量超出了限制"),

	// 激活码相关
	CODE_30142001(ErrorCode.CODE_30142001, "等级不能为空"),
	CODE_30142002(ErrorCode.CODE_30142002, "时长必须在1-10000之间"),
	CODE_30142003(ErrorCode.CODE_30142003, "兑换日期只能选择今日之后的日期"),
	CODE_30142004(ErrorCode.CODE_30142004, "生成数量必须大于0"),
	CODE_30142005(ErrorCode.CODE_30142005, "关联了账号的激活码只能生成单条激活码"),
	CODE_30142006(ErrorCode.CODE_30142006, "生成激活码数量不能超过1000条"),
	CODE_30142007(ErrorCode.CODE_30142007, "兑换有效时长应为大于0小于100的整数"),
	CODE_30142008(ErrorCode.CODE_30142008, "赠送时长应为大于0小于1000的整数"),

	// 业务设置相关
	CODE_30143001(ErrorCode.CODE_30143001, "业务设置数据不存在"),
	CODE_30143002(ErrorCode.CODE_30143002, "类型不能为空"),
	CODE_30143003(ErrorCode.CODE_30143003, "内容不能为空"),
	CODE_30143004(ErrorCode.CODE_30143004, "类型不存在"),
	CODE_30143005(ErrorCode.CODE_30143005, "参数解析错误"),
	CODE_30143006(ErrorCode.CODE_30143006, "推介人结佣金额比例不能为空"),
	CODE_30143007(ErrorCode.CODE_30143007, "推介人分佣有效期不能为空"),
	CODE_30143008(ErrorCode.CODE_30143008, "推介人结佣金额比例不能超过100"),
	CODE_30143009(ErrorCode.CODE_30143009, "有效期最低不能少于一个月，不能超过120个月"),

	// 会员等级相关
	CODE_30144001(ErrorCode.CODE_30144001, "会员等级id不能为空"),
	CODE_30144002(ErrorCode.CODE_30144002, "会员等级不存在"),
	CODE_30144003(ErrorCode.CODE_30144003, "包月费用不能为空"),
	CODE_30144004(ErrorCode.CODE_30144004, "包年费用不能为空"),
	CODE_30144005(ErrorCode.CODE_30144005, "会员权限不能为空"),
	CODE_30144006(ErrorCode.CODE_30144006, "包月费用不能为空 取值范围 0.01~10000000000"),
	CODE_30144007(ErrorCode.CODE_30144007, "包年费用不能为空 取值范围 0.01~10000000000"),
	CODE_30144008(ErrorCode.CODE_30144008, "包年费用必须大于包月费用"),
	CODE_30144009(ErrorCode.CODE_30144009, "包月费用必须大于上一个等级的包月费用"),
	CODE_30144010(ErrorCode.CODE_30144010, "包年费用必须大于上一个等级的包年费用"),
	CODE_30144011(ErrorCode.CODE_30144011, "包月费用必须小于下一个等级的包月费用"),
	CODE_30144012(ErrorCode.CODE_30144012, "包年费用必须小于下一个等级的包年费用"),
	CODE_30144013(ErrorCode.CODE_30144013, "注册送会员配置信息不存在"),
	CODE_30144014(ErrorCode.CODE_30144014, "无法关闭，请检查注册送会员配置"),
	CODE_30144015(ErrorCode.CODE_30144015, "无法关闭，请检查会员等级列表"),
	CODE_30144016(ErrorCode.CODE_30144016, "无法开放，请检查会员等级列表"),

	//员工相关
	CODE_30145001(ErrorCode.CODE_30145001, "手机和邮箱不可同时为空"),
	CODE_30145002(ErrorCode.CODE_30145002, "手机号重复"),
	CODE_30145003(ErrorCode.CODE_30145003, "员工主责部门只允许有一个"),
	CODE_30145004(ErrorCode.CODE_30145004, "员工部门不能为空"),
	CODE_30145005(ErrorCode.CODE_30145005, "组织不存在"),
	CODE_30145007(ErrorCode.CODE_30145007, "邮箱账号重复"),
	CODE_30145008(ErrorCode.CODE_30145008, "员工工号重复"),
	CODE_30145009(ErrorCode.CODE_30145009, "员工姓名不能为空"),
	CODE_30145010(ErrorCode.CODE_30145010, "员工工号不能为空"),
	CODE_30145011(ErrorCode.CODE_30145011, "员工手机号不能为空"),
	CODE_30145012(ErrorCode.CODE_30145012, "手机号格式不正确"),
	CODE_30145013(ErrorCode.CODE_30145013, "邮箱格式不正确"),
	CODE_30145014(ErrorCode.CODE_30145014, "员工姓名长度最大为64"),
	CODE_30145015(ErrorCode.CODE_30145015, "员工工号长度最大为10"),
	CODE_30145016(ErrorCode.CODE_30145016, "员工手机号长度最大为64"),
	CODE_30145017(ErrorCode.CODE_30145017, "员工邮箱长度最大为64"),
	CODE_30145018(ErrorCode.CODE_30145018, "员工职位描述长度最大为128"),
	CODE_30145019(ErrorCode.CODE_30145019, "员工部门不能为空"),
	CODE_30145020(ErrorCode.CODE_30145020, "员工部门id不能为空"),
	CODE_30145021(ErrorCode.CODE_30145021, "主责部门的值只能为0或1"),
	CODE_30145022(ErrorCode.CODE_30145022, "员工不存在"),

	// 推广码相关
	CODE_30146001(ErrorCode.CODE_30146001, "标题不能为空"),
	CODE_30146002(ErrorCode.CODE_30146002, "标题长度必须在1-15个字符之间"),
	CODE_30146003(ErrorCode.CODE_30146003, "关联账号信息不能为空"),
	CODE_30146004(ErrorCode.CODE_30146004, "是否赠送会员不能为空"),
	CODE_30146005(ErrorCode.CODE_30146005, "会员赠送时长必须在1-10000之间"),
	CODE_30146006(ErrorCode.CODE_30146006, "会员兑换有效期必须是未来的日期"),
	CODE_30146007(ErrorCode.CODE_30146007, "推广有效期不能为空"),
	CODE_30146008(ErrorCode.CODE_30146008, "推广有效期必须是未来的日期"),
	CODE_30146009(ErrorCode.CODE_30146009, "推广头图不能为空"),
	CODE_30146010(ErrorCode.CODE_30146010, "页面不能为空"),
	CODE_30146011(ErrorCode.CODE_30146011, "赠送会员为'是'时，会员等级不能为空"),
	CODE_30146012(ErrorCode.CODE_30146012, "赠送会员为'是'时，赠送时长不能为空"),
	CODE_30146013(ErrorCode.CODE_30146013, "赠送会员为'是'时，兑换有效期不能为空"),
	CODE_30146014(ErrorCode.CODE_30146014, "推广已关闭，无法延期"),
	CODE_30146015(ErrorCode.CODE_30146015, "延期时间只能是当前日期之后的时间"),
	CODE_30146016(ErrorCode.CODE_30146016, "推广不存在"),
	CODE_30146017(ErrorCode.CODE_30146017, "客户已绑定有效推广"),

	//角色相关
	CODE_30147001(ErrorCode.CODE_30147001, "角色名称重复"),
	CODE_30147002(ErrorCode.CODE_30147002, "角色名称不能为空"),
	CODE_30147003(ErrorCode.CODE_30147003, "权限列表不能为空"),
	CODE_30147004(ErrorCode.CODE_30147004, "角色描述长度最大为128"),
	CODE_30147005(ErrorCode.CODE_30147005, "角色名称长度最大为64"),
	CODE_30147006(ErrorCode.CODE_30147006, "角色被使用不能删除"),
	CODE_30147007(ErrorCode.CODE_30147007, "角色不存在"),
	CODE_30147008(ErrorCode.CODE_30147008, "权限不存在"),

	// 账号相关
	CODE_30148001(ErrorCode.CODE_30148001, "员工id不能为空"),
	CODE_30148002(ErrorCode.CODE_30148002, "角色列表不能为空"),
	CODE_30148003(ErrorCode.CODE_30148003, "状态不能为空"),
	CODE_30148004(ErrorCode.CODE_30148004, "状态只有0或1"),
	CODE_30148005(ErrorCode.CODE_30148005, "账号不存在"),
	CODE_30148006(ErrorCode.CODE_30148006, "账号已存在"),
	CODE_30148007(ErrorCode.CODE_30148007, "用户类型不能为空"),
	CODE_30148008(ErrorCode.CODE_30148008, "员工姓名不能为空"),
	CODE_30148009(ErrorCode.CODE_30148009, "员工号码不能为空"),
	CODE_30148010(ErrorCode.CODE_30148010, "号码不规范"),
	CODE_30148011(ErrorCode.CODE_30148011, "号码重复"),
	CODE_30148012(ErrorCode.CODE_30148012, "手机号最多维护10个"),

	// 天地图相关
	CODE_30149001(ErrorCode.CODE_30149001, "关键字不能为空"),
	CODE_30149002(ErrorCode.CODE_30149002, "行政区域国标码不能为空"),
	CODE_30149003(ErrorCode.CODE_30149003, "服务查询类型参数"),
	CODE_30149004(ErrorCode.CODE_30149004, "返回结果起始位不能为空"),
	CODE_30149005(ErrorCode.CODE_30149005, "返回结果数量不能为空"),

	// 应用管理
	CODE_30150001(ErrorCode.CODE_30150001, "该数据不存在"),
	CODE_30150002(ErrorCode.CODE_30150002, "应用名称不能为空"),
	CODE_30150003(ErrorCode.CODE_30150003, "应用名称长度最大为25"),
	CODE_30150004(ErrorCode.CODE_30150004, "回调地址不能超过200个字符"),
	CODE_30150005(ErrorCode.CODE_30150005, "银行卡识别失败，请手动填写"),
	CODE_30150006(ErrorCode.CODE_30150006, "参数错误"),

	// app版本相关
	CODE_30151001(ErrorCode.CODE_30151001, "版本号不能为空"),
	CODE_30151002(ErrorCode.CODE_30151002, "发布日期不能为空"),
	CODE_30151003(ErrorCode.CODE_30151003, "发布日期不能超过今天"),
	CODE_30151004(ErrorCode.CODE_30151004, "版本号不能超过10个字符"),
	CODE_30151005(ErrorCode.CODE_30151005, "安装包不能为空"),
	CODE_30151006(ErrorCode.CODE_30151006, "类型不能为空"),
	CODE_30151007(ErrorCode.CODE_30151007, "类型不存在"),
	CODE_30151008(ErrorCode.CODE_30151008, "app版本信息不存在"),

	// 安全设置设备相关
	CODE_30152001(ErrorCode.CODE_30152001, "新设备登录需要校验验证码"),

	// 回调记录管理
	CODE_30153001(ErrorCode.CODE_30153001, "该数据不存在"),
	CODE_30153002(ErrorCode.CODE_30153002, "失败状态才有重新回调操作"),

	// 油品站点管理
	CODE_30154001(ErrorCode.CODE_30154001, "该数据不存在"),
	CODE_30154002(ErrorCode.CODE_30154002, "站点名称不能为空"),
	CODE_30154003(ErrorCode.CODE_30154003, "站点名称不超过32位字符"),
	CODE_30154004(ErrorCode.CODE_30154004, "该站点名称已存在"),
	CODE_30154005(ErrorCode.CODE_30154005, "站点品牌不能为空"),
	CODE_30154006(ErrorCode.CODE_30154006, "站点品牌不超过10个字符"),
	CODE_30154007(ErrorCode.CODE_30154007, "维护人联系方式不能为空"),
	CODE_30154008(ErrorCode.CODE_30154008, "维护人联系方式为11位数字"),
	CODE_30154009(ErrorCode.CODE_30154009, "价格*数量不能为空"),
	CODE_30154010(ErrorCode.CODE_30154010, "价格*数量不存在"),
	CODE_30154011(ErrorCode.CODE_30154011, "省编码不能为空"),
	CODE_30154012(ErrorCode.CODE_30154012, "城市编码不能为空"),
	CODE_30154013(ErrorCode.CODE_30154013, "区域编码不能为空"),
	CODE_30154014(ErrorCode.CODE_30154014, "详细地址不能为空"),
	CODE_30154015(ErrorCode.CODE_30154015, "经纬度不能为空"),
	CODE_30154016(ErrorCode.CODE_30154016, "该状态无法删除"),
	CODE_30154017(ErrorCode.CODE_30154017, "参数错误"),
	CODE_30154018(ErrorCode.CODE_30154018, "站点联系方式不能为空"),
	CODE_30154019(ErrorCode.CODE_30154019, "站点联系方式为11位数字"),
	CODE_30154020(ErrorCode.CODE_30154020, "省名称不能为空"),
	CODE_30154021(ErrorCode.CODE_30154021, "城市名称不能为空"),
	CODE_30154022(ErrorCode.CODE_30154022, "区域名称不能为空"),

	// 油品费用管理
	CODE_30155001(ErrorCode.CODE_30155001, "该数据不存在"),
	CODE_30155002(ErrorCode.CODE_30155002, "当前状态不允许确认"),
	CODE_30155003(ErrorCode.CODE_30155003, "当前支付类型不允许确认"),
	CODE_30155004(ErrorCode.CODE_30155004, "确认时间不能为空"),
	CODE_30155005(ErrorCode.CODE_30155005, "该订单状态无法支付油费"),
	CODE_30155006(ErrorCode.CODE_30155006, "油费金额不能为空"),
	CODE_30155007(ErrorCode.CODE_30155007, "油品订单id不能为空"),
	CODE_30155008(ErrorCode.CODE_30155008, "支付凭证不能为空"),
	CODE_30155009(ErrorCode.CODE_30155009, "最多上传5张图片"),

	// 油品订单管理
	CODE_30156001(ErrorCode.CODE_30156001, "该数据不存在"),
	CODE_30156002(ErrorCode.CODE_30156002, "此油品订单未支付油费，无法确认加油"),
	CODE_30156003(ErrorCode.CODE_30156003, "此油品订单油费待确认，无法确认加油"),
	CODE_30156004(ErrorCode.CODE_30156004, "此油品订单无法确认加油"),
	CODE_30156005(ErrorCode.CODE_30156005, "此油品订单无法取消订单"),
	CODE_30156006(ErrorCode.CODE_30156006, "当前状态无法核对油品订单"),
	CODE_30156007(ErrorCode.CODE_30156007, "此油品订单无法报计划"),
	CODE_30156008(ErrorCode.CODE_30156008, "此油品订单油费待确认，请确认财税收到此油款"),
	CODE_30156009(ErrorCode.CODE_30156009, "此油品订单无法完成加油"),
	CODE_30156010(ErrorCode.CODE_30156010, "实际加油时间不能为空"),
	CODE_30156011(ErrorCode.CODE_30156011, "此油品订单无法删除"),
	CODE_30156012(ErrorCode.CODE_30156012, "订单方式不能为空"),
	CODE_30156013(ErrorCode.CODE_30156013, "订单方式不存在"),
	CODE_30156014(ErrorCode.CODE_30156014, "油品站点不能为空"),
	CODE_30156015(ErrorCode.CODE_30156015, "联系人不能为空"),
	CODE_30156016(ErrorCode.CODE_30156016, "手机号不能为空"),
	CODE_30156017(ErrorCode.CODE_30156017, "船舶MMSI不能为空"),
	CODE_30156018(ErrorCode.CODE_30156018, "油品类型不能为空"),
	CODE_30156019(ErrorCode.CODE_30156019, "油品类型不存在"),
	CODE_30156020(ErrorCode.CODE_30156020, "加油量不能为空"),
	CODE_30156021(ErrorCode.CODE_30156021, "预约加油日期不能为空"),
	CODE_30156022(ErrorCode.CODE_30156022, "预约加油时间段不能为空"),
	CODE_30156023(ErrorCode.CODE_30156023, "预约加油时间段不存在"),
	CODE_30156024(ErrorCode.CODE_30156024, "油价不能为空"),
	CODE_30156025(ErrorCode.CODE_30156025, "备注不超过200个字符"),
	CODE_30156026(ErrorCode.CODE_30156026, "油价必须大于0小于1000000"),
	CODE_30156027(ErrorCode.CODE_30156027, "加油量必须大于0小于10000"),
	CODE_30156028(ErrorCode.CODE_30156028, "核对方式错误"),
	CODE_30156029(ErrorCode.CODE_30156029, "计划信息不能为空"),
	CODE_30156030(ErrorCode.CODE_30156030, "计划加油日期不能为空"),
	CODE_30156031(ErrorCode.CODE_30156031, "计划加油时间段不能为空"),
	CODE_30156032(ErrorCode.CODE_30156032, "计划加油时间段不存在"),
	CODE_30156033(ErrorCode.CODE_30156033, "承运人不能为空"),
	CODE_30156034(ErrorCode.CODE_30156034, "船主未确认该油品订单，无法进行核对"),
	CODE_30156035(ErrorCode.CODE_30156035, "无法确认此油品订单"),
	CODE_30156036(ErrorCode.CODE_30156036, "此油品订单已确认，无法重复确认"),



	// 弹窗广告管理
	CODE_30158001(ErrorCode.CODE_30158001, "该数据不存在"),
	CODE_30158002(ErrorCode.CODE_30158002, "弹窗名称不能为空"),
	CODE_30158003(ErrorCode.CODE_30158003, "弹窗名称不超过50个字符"),
	CODE_30158004(ErrorCode.CODE_30158004, "适用终端不能为空"),
	CODE_30158005(ErrorCode.CODE_30158005, "适用终端不存在"),
	CODE_30158006(ErrorCode.CODE_30158006, "弹窗图片不能为空"),
	CODE_30158007(ErrorCode.CODE_30158007, "显示页面不能为空"),
	CODE_30158008(ErrorCode.CODE_30158008, "显示页面不存在"),
	CODE_30158009(ErrorCode.CODE_30158009, "显示频率不能为空"),
	CODE_30158010(ErrorCode.CODE_30158010, "显示频率不存在"),
	CODE_30158011(ErrorCode.CODE_30158011, "跳转类型不能为空"),
	CODE_30158012(ErrorCode.CODE_30158012, "跳转类型不存在"),
	CODE_30158013(ErrorCode.CODE_30158013, "值不能为空"),
	CODE_30158014(ErrorCode.CODE_30158014, "广告页不存在"),
	CODE_30158015(ErrorCode.CODE_30158015, "该状态无法编辑"),
	CODE_30158016(ErrorCode.CODE_30158016, "该状态无法删除"),
	CODE_30158017(ErrorCode.CODE_30158017, "参数错误"),
	CODE_30158018(ErrorCode.CODE_30158018, "该状态无法禁用"),
	CODE_30158019(ErrorCode.CODE_30158019, "该状态无法启用"),
	CODE_30158020(ErrorCode.CODE_30158020, "该页面已有弹窗"),
	CODE_30158021(ErrorCode.CODE_30158021, "终端不能重复"),

	// AI语料
	CODE_30160001(ErrorCode.CODE_30160001, "语料发布信息不存在"),
	CODE_30160002(ErrorCode.CODE_30160002, "语料库信息不存在"),
	CODE_30160003(ErrorCode.CODE_30160003, "识别语料不能为空"),
	CODE_30160004(ErrorCode.CODE_30160004, "录音内容不能为空"),
	CODE_30160005(ErrorCode.CODE_30160005, "语料库信息不存在"),
	CODE_30160006(ErrorCode.CODE_30160006, "语料内容不能为空"),
	CODE_30160007(ErrorCode.CODE_30160007, "AI语料导出异常"),
	CODE_30160008(ErrorCode.CODE_30160008, "当天已发布过语料，请勿重复发布"),
	CODE_30160009(ErrorCode.CODE_30160009, "是否发布不能为空"),

	// 油品指数版本
	CODE_30161001(ErrorCode.CODE_30161001, "版本日期不能为空"),
	CODE_30161002(ErrorCode.CODE_30161002, "版本名称最多32个字符"),
	CODE_30161003(ErrorCode.CODE_30161003, "版本日期已存在"),
	CODE_30161004(ErrorCode.CODE_30161004, "存在指数数据，不能修改"),
	CODE_30161005(ErrorCode.CODE_30161005, "存在指数数据，不能删除"),
	CODE_30161006(ErrorCode.CODE_30161006, "版本不存在"),
	CODE_30161007(ErrorCode.CODE_30161007, "日期不能在今天之后"),

	// 油品指数版本记录
	CODE_30162001(ErrorCode.CODE_30162001, "油品指数版本记录不存在"),
	CODE_30162002(ErrorCode.CODE_30162002, "版本id不能为空"),
	CODE_30162003(ErrorCode.CODE_30162003, "待提交、已驳回状态才能进行提交、修改、删除操作"),
	CODE_30162004(ErrorCode.CODE_30162004, "待发布、已撤回状态才能进行发布"),
	CODE_30162005(ErrorCode.CODE_30162005, "待发布、已撤回状态才能进行驳回"),
	CODE_30162006(ErrorCode.CODE_30162006, "已发布状态且是最新发布数据才能进行撤回"),
	CODE_30162007(ErrorCode.CODE_30162007, "当前已经是最初发布版本，无法进行撤回！"),
	CODE_30162008(ErrorCode.CODE_30162008, "备注不能超过100个字符"),
	CODE_30162009(ErrorCode.CODE_30162009, " 待提交、已驳回状态才能进行保存"),
	CODE_30162010(ErrorCode.CODE_30162010, "指数数据为空，不能进行提交"),

	// 油品指数版本记录快照相关
	CODE_30163001(ErrorCode.CODE_30163001, "站点id不能为空"),
	CODE_30163002(ErrorCode.CODE_30163002, "版本id不能为空"),
	CODE_30163003(ErrorCode.CODE_30163003, "版本记录id不能为空"),
	CODE_30163004(ErrorCode.CODE_30163004, "价格应大于0小于100000的整数"),
	CODE_30163005(ErrorCode.CODE_30163005, "请至少填写一个价格"),
	CODE_30163006(ErrorCode.CODE_30163006, "该站点已经存在油品指数"),
	CODE_30163007(ErrorCode.CODE_30163007, "指数不存在"),


	CODE_30170001(ErrorCode.CODE_30170001, "您今天的查车次数已用完"),

	// 车辆管理
	CODE_30180001(ErrorCode.CODE_30180001, "该车辆未绑定设备或设备不在线"),
	CODE_30180002(ErrorCode.CODE_30180002, "未查询到车辆信息或车辆已离线"),

	// 链小云找船
	CODE_30190001(ErrorCode.CODE_30190001, "我可以帮您快速匹配到合适的船舶，请告诉我完整的始发港、目的港、运货吨数、货物类型。\n您可以这样说：\n我需要在3月30号从黄冈到芜湖运输1万吨天然砂，意向运价参考市场运价"),
	CODE_30190002(ErrorCode.CODE_30190002, "船运需求id不能为空"),
	CODE_30190003(ErrorCode.CODE_30190003, "会话ID不能为空"),
	CODE_30190004(ErrorCode.CODE_30190004, "始发港为空"),
	CODE_30190005(ErrorCode.CODE_30190005, "目的港为空"),
	CODE_30190006(ErrorCode.CODE_30190006, "始发港、目的港为空"),
	CODE_30190007(ErrorCode.CODE_30190007, "期望装载日期不能早于今天，建议您调整为今天或之后的日期。"),

	// 黄码港对接相关
	CODE_30191001(ErrorCode.CODE_30191001, "手机号码不能为空"),
	CODE_30191002(ErrorCode.CODE_30191002, "短信验证码不能为空"),
	CODE_30191003(ErrorCode.CODE_30191003, "eidToken不能为空"),
	CODE_30191004(ErrorCode.CODE_30191004, "黄码港新增或变更承运人和船舶失败:{0}"),
	CODE_30191005(ErrorCode.CODE_30191005, "黄码港新增或变更货源是失败:{0}"),
	CODE_30191006(ErrorCode.CODE_30191006, "黄码港新增运单信息失败:{0}"),
	CODE_30191007(ErrorCode.CODE_30191007, "黄码港新增对账失败:{0}"),
	CODE_30191008(ErrorCode.CODE_30191008, "黄码港新增轨迹失败:{0}"),
	CODE_30191009(ErrorCode.CODE_30191009, "黄码港更新运单信息失败:{0}"),
	CODE_30191010(ErrorCode.CODE_30191010, "船舶信息不存在"),
	CODE_30191011(ErrorCode.CODE_30191011, "船主信息不存在"),
	CODE_30191012(ErrorCode.CODE_30191012, "船主手机号不存在"),
	CODE_30191013(ErrorCode.CODE_30191013, "船主身份证号码不能为空"),
	CODE_30191014(ErrorCode.CODE_30191014, "身份证正面不能为空"),
	CODE_30191015(ErrorCode.CODE_30191015, "身份证反面不能为空"),
	CODE_30191016(ErrorCode.CODE_30191016, "身份证正面识别失败"),
	CODE_30191017(ErrorCode.CODE_30191017, "身份证反面识别失败"),
	CODE_30191018(ErrorCode.CODE_30191018, "船主实名不能为空"),
	CODE_30191019(ErrorCode.CODE_30191019, "请补充船舶信息"),
	CODE_30191020(ErrorCode.CODE_30191020, "船员适任证书识别失败"),
	CODE_30191021(ErrorCode.CODE_30191021, "认证失败"),
	CODE_30191022(ErrorCode.CODE_30191022, "发送短信验证码失败"),
	CODE_30191023(ErrorCode.CODE_30191023, "获得人脸识别token失败"),
	CODE_30191024(ErrorCode.CODE_30191024, "获得H5人脸识别链接失败"),
	CODE_30191025(ErrorCode.CODE_30191025, "该承运人已授权签署，无需重复授权签署"),
	CODE_30191026(ErrorCode.CODE_30191026, "职务资格不能为空"),
	CODE_30191027(ErrorCode.CODE_30191027, "请上传船舶营运证所有人页"),
	CODE_30191028(ErrorCode.CODE_30191028, "请上传船舶国籍证书"),
	CODE_30191029(ErrorCode.CODE_30191029, "请上传船舶营运证吨位页"),
	CODE_30191030(ErrorCode.CODE_30191030, "请上传船员适任证书"),
	CODE_30191031(ErrorCode.CODE_30191031, "请上传内河船舶检验报告"),
	CODE_30191032(ErrorCode.CODE_30191032, "请上传船舶自动识别系统AIS标识码证书"),
	CODE_30191033(ErrorCode.CODE_30191033, "身份证信息不能为空"),
	CODE_30191034(ErrorCode.CODE_30191034, "身份证有效期开始时间不能为空"),
	CODE_30191035(ErrorCode.CODE_30191035, "身份证有效期结束时间不能为空"),
	CODE_30191036(ErrorCode.CODE_30191036, "身份证号码不能为空"),
	CODE_30191037(ErrorCode.CODE_30191037, "身份证住址不能为空"),
	CODE_30191038(ErrorCode.CODE_30191038, "身份证性别不能为空"),
	CODE_30191039(ErrorCode.CODE_30191039, "身份证出生日期不能为空"),

	//砂石热点相关
	CODE_30192001(ErrorCode.CODE_30192001, "砂石热点数据不存在"),
	CODE_30192002(ErrorCode.CODE_30192002, "标题不能为空，且最大为50个字符"),
	CODE_30192003(ErrorCode.CODE_30192003, "分类不能为空"),
	CODE_30192004(ErrorCode.CODE_30192004, "分类不存在"),
	CODE_30192006(ErrorCode.CODE_30192006, "标签数量超过10个"),
	CODE_30192007(ErrorCode.CODE_30192007, "来源长度不超过10个字符"),
	CODE_30192008(ErrorCode.CODE_30192008, "发布日期不能为空"),
	CODE_30192009(ErrorCode.CODE_30192009, "图片不能为空"),
	CODE_30192011(ErrorCode.CODE_30192011, "内容不能为空"),
	CODE_30192012(ErrorCode.CODE_30192012, "免责声明长度不超过300个字符"),
	CODE_30192013(ErrorCode.CODE_30192013, "下架状态才能编辑"),
	CODE_30192014(ErrorCode.CODE_30192014, "下架状态才能删除"),
	CODE_30192015(ErrorCode.CODE_30192015, "存在重复标签"),
	CODE_30192016(ErrorCode.CODE_30192016, "参数错误"),

	// 扫码登录相关
	CODE_30193001(ErrorCode.CODE_30193001, "唯一标识不能为空"),
	CODE_30193002(ErrorCode.CODE_30193002, "二维码已失效"),

	// 商机相关
	CODE_30194001(ErrorCode.CODE_30194001, "商机数据不存在"),
	CODE_30194002(ErrorCode.CODE_30194002, "标题不能为空"),
	CODE_30194003(ErrorCode.CODE_30194003, "标题最大为32个字符"),
	CODE_30194004(ErrorCode.CODE_30194004, "类型不能为空"),
	CODE_30194005(ErrorCode.CODE_30194005, "类型不存在"),
	CODE_30194006(ErrorCode.CODE_30194006, "省编码不能为空"),
	CODE_30194007(ErrorCode.CODE_30194007, "城市编码不能为空"),
	CODE_30194008(ErrorCode.CODE_30194008, "原因不能为空"),
	CODE_30194009(ErrorCode.CODE_30194009, "原因不存在"),
	CODE_30194010(ErrorCode.CODE_30194010, "备注最大为32个字符"),
	CODE_30194011(ErrorCode.CODE_30194011, "联系方式不能为空"),
	CODE_30194012(ErrorCode.CODE_30194012, "发布人不能为空"),
	CODE_30194013(ErrorCode.CODE_30194013, "联系电话不能为空"),
	CODE_30194014(ErrorCode.CODE_30194014, "详细说明最大为500个字符"),
	CODE_30194015(ErrorCode.CODE_30194015, "关联用户不能为空"),
	CODE_30194016(ErrorCode.CODE_30194016, "审核结果不能为空"),
	CODE_30194017(ErrorCode.CODE_30194017, "审核意见最大为50个字符"),
	CODE_30194018(ErrorCode.CODE_30194018, "当前状态无法审核"),
	CODE_30194019(ErrorCode.CODE_30194019, "当前状态无法编辑"),
	CODE_30194020(ErrorCode.CODE_30194020, "当前状态无法删除"),
	CODE_30194021(ErrorCode.CODE_30194021, "当前状态无法下架"),
	CODE_30194022(ErrorCode.CODE_30194022, "该商机已被收藏，无法重复收藏"),
	CODE_30194023(ErrorCode.CODE_30194023, "该商机未被收藏，无法取消收藏"),
	CODE_30194024(ErrorCode.CODE_30194024, "是否关联用户不能为空"),


	// 拼团商品相关
	CODE_30195001(ErrorCode.CODE_30195001, "吨数不能为空"),
	CODE_30195002(ErrorCode.CODE_30195002, "吨数必须在1-10000之间"),
	CODE_30195003(ErrorCode.CODE_30195003, "您已经参与拼团"),
	CODE_30195004(ErrorCode.CODE_30195004, "商品不存在"),

	// 寄售商品、团购商品、捡漏商品相关
	CODE_30196001(ErrorCode.CODE_30196001, "数据不存在"),

	// 申请寄样相关
	CODE_30197001(ErrorCode.CODE_30197001, "寄样申请不存在"),
	CODE_30197002(ErrorCode.CODE_30197002, "商品ID不能为空"),
	CODE_30197003(ErrorCode.CODE_30197003, "地址ID不能为空"),
	CODE_30197004(ErrorCode.CODE_30197004, "备注最大不能超过32个字符"),
	CODE_30197005(ErrorCode.CODE_30197005, "状态不能为空"),
	CODE_30197006(ErrorCode.CODE_30197006, "状态错误"),
	CODE_30197007(ErrorCode.CODE_30197007, "物流单号最大不能超过100个字符"),
	CODE_30197008(ErrorCode.CODE_30197008, "备注最大不能超过300个字符"),
	CODE_30197009(ErrorCode.CODE_30197009, "未认证"),
	CODE_30197010(ErrorCode.CODE_30197010, "商品已经申请过"),
	CODE_30197011(ErrorCode.CODE_30197011, "地址不存在"),
	CODE_30197012(ErrorCode.CODE_30197012, "待处理状态才能进行处理"),
	CODE_30197013(ErrorCode.CODE_30197013, "请填写快递单号"),


    CODE_30198001(ErrorCode.CODE_30198001, "供应商名称不能为空"),
    CODE_30198002(ErrorCode.CODE_30198002, "集团类型只能为1~5"),
    CODE_30198003(ErrorCode.CODE_30198003, "企业标签最多6个"),
    CODE_30198004(ErrorCode.CODE_30198004, "省份不能为空"),
    CODE_30198005(ErrorCode.CODE_30198005, "地级市不能为空"),
    CODE_30198006(ErrorCode.CODE_30198006, "经纬度不能为空"),
    CODE_30198007(ErrorCode.CODE_30198007, "主营品类不能为空"),
    CODE_30198008(ErrorCode.CODE_30198008, "企业logo不能为空"),
    CODE_30198009(ErrorCode.CODE_30198009, "统一社会信用代码不能为空"),
    CODE_30198010(ErrorCode.CODE_30198010, "统一社会信用代码重复"),
    CODE_30198011(ErrorCode.CODE_30198011, "法定代表人不能为空"),
    CODE_30198012(ErrorCode.CODE_30198012, "描述介绍超过300个字符限制"),
    CODE_30198013(ErrorCode.CODE_30198013, "展示位置（合作矿厂位置）只能1~8"),




    ;

	/**
	 * 国际化前缀
	 */
	private static final String PREFIX = "com.zhihaoscm.service.meta.";
	/**
	 * 获取i18n
	 */
	public static final Function<String, String> I18N = code -> ErrorCodeDef.PREFIX
			+ code;
	/**
	 * 编号
	 */
	private final String code;
	/**
	 * 编号信息
	 */
	@Getter
	private final String message;

	/**
	 * 构造器
	 */
	ErrorCodeDef(String code, String message) {
		this.code = code;
		this.message = message;
	}

	/**
	 * 通过编号信息获取枚举对象
	 */
	public static ErrorCodeDef from(String code) {
		for (ErrorCodeDef item : ErrorCodeDef.values()) {
			if (item.getCode().equals(code)) {
				return item;
			}
		}
		return ErrorCodeDef.CODE_001;
	}
}
