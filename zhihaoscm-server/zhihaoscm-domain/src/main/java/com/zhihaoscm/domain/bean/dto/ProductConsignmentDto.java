package com.zhihaoscm.domain.bean.dto;

import java.util.List;

import com.zhihaoscm.domain.bean.entity.ProductConsignment;
import com.zhihaoscm.domain.bean.entity.ProductGroupPurchase;

import lombok.Data;

@Data
public class ProductConsignmentDto {

	/**
	 * 寄售商品
	 */
	private ProductConsignment productConsignment;

	/**
	 * 团购商品
	 */
	private ProductGroupPurchase productGroupPurchase;

	/**
	 * 要激活的文件id
	 */
	private List<Long> activeFileIds;

	/**
	 * 要取消激活的文件id
	 */
	private List<Long> unActiveFileIds;
}
