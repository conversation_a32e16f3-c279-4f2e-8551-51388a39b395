package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

/**
 * 广告位定义
 */
public interface AdvertisementPositionDef {

	/**
	 * 广告位位置类型
	 */
	@Getter
	enum PositionType {

		HOMEPAGE_PLACEMENT(1, "首页广告位"),

		FIND_SAND_PRICE_PLACEMENT(2, "查砂价广告位"),

		FIND_SHIPPING_PRICE_PLACEMENT(3, "查运价广告位"),

		GOODS_FIND_SHIP_PLACEMENT(4, "货找船广告位"),

		FIND_INFORMATION_PLACEMENT(5, "看资讯广告位"),

		FIND_GOODS_SOURCE_PLACEMENT(6, "找货源广告位"),

		FIND_BUSINESS_OPPORTUNITY_PLACEMENT(7, "找商机广告位");

		private final Integer code;

		private final String name;

		private static final Map<Integer, PositionType> MAPPING;

		static {
			{
				Map<Integer, PositionType> mapping = new HashMap<>();
				for (PositionType value : PositionType.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static PositionType from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		PositionType(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 广告位
	 */
	@Getter
	enum Position {

		ONE(1, "1号广告位"),

		TWO(2, "2号广告位"),

		THREE(3, "3号广告位");

		private final Integer code;

		private final String name;

		private static final Map<Integer, Position> MAPPING;

		static {
			{
				Map<Integer, Position> mapping = new HashMap<>();
				for (Position value : Position.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static Position from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Position(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 展现形式
	 */
	@Getter
	enum DisplayForm {

		FIXED_SINGLE_IMAGE(1, "固定单图"),

		MULTI_IMAGE_ROTATION(2, "多图轮播");

		private final Integer code;

		private final String name;

		private static final Map<Integer, AdvertisementPositionDef.DisplayForm> MAPPING;

		static {
			{
				Map<Integer, AdvertisementPositionDef.DisplayForm> mapping = new HashMap<>();
				for (AdvertisementPositionDef.DisplayForm value : AdvertisementPositionDef.DisplayForm
						.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static AdvertisementPositionDef.DisplayForm from(
				final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		DisplayForm(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 轮播间隔
	 */
	@Getter
	enum RotationInterval {

		THREE_SECONDS(1, "3秒"), FIVE_SECONDS(2, "5秒"),;

		private final Integer code;

		private final String name;

		private static final Map<Integer, AdvertisementPositionDef.RotationInterval> MAPPING;

		static {
			{
				Map<Integer, AdvertisementPositionDef.RotationInterval> mapping = new HashMap<>();
				for (AdvertisementPositionDef.RotationInterval value : AdvertisementPositionDef.RotationInterval
						.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static AdvertisementPositionDef.RotationInterval from(
				final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		RotationInterval(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 跳转类型
	 */
	@Getter
	enum LinkType {

		ADVERT(1, "广告页"), LINK(2, "链接");

		private final Integer code;

		private final String name;

		private static final Map<Integer, AdvertisementPositionDef.LinkType> MAPPING;

		static {
			{
				Map<Integer, AdvertisementPositionDef.LinkType> mapping = new HashMap<>();
				for (AdvertisementPositionDef.LinkType value : AdvertisementPositionDef.LinkType
						.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static AdvertisementPositionDef.LinkType from(
				final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		LinkType(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}
}
