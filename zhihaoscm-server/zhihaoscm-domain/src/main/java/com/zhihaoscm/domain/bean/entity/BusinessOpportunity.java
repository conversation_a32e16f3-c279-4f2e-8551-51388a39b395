package com.zhihaoscm.domain.bean.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.common.bean.json.ArrayInteger;
import com.zhihaoscm.domain.bean.json.AuditInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.TakedownInfo;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 商机
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Getter
@Setter
@TableName("t_business_opportunity")
public class BusinessOpportunity extends BaseEntityWithLongId {

	/**
	 * 是否关联用户(1是 0否)
	 */
	private Integer isRelatedOwner;

	/**
	 * 关联用户id
	 */
	private Long ownerId;

	/**
	 * 用户账号信息
	 */
	private Enterprise ownerEnterprise;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 类型{@link com.zhihaoscm.domain.meta.biz.BusinessOpportunityDef.Type}
	 */
	private Integer type;

	/**
	 * 省编码
	 */
	private String provinceCode;

	/**
	 * 城市编码
	 */
	private String cityCode;

	/**
	 * 联系人(发布人)
	 */
	private String contactName;

	/**
	 * 联系电话
	 */
	private String contactPhone;

	/**
	 * 详细说明
	 */
	private String detailedDescription;

	/**
	 * 审核信息
	 */
	private AuditInfo auditInfo;

	/**
	 * 下架信息
	 */
	private TakedownInfo takedownInfo;

	/**
	 * 是否置顶(1是 0否)
	 */
	private Integer isTop;

	/**
	 * 置顶时间
	 */
	private LocalDateTime topTime;

	/**
	 * 商机标签{@link com.zhihaoscm.domain.meta.biz.BusinessOpportunityDef.OpportunityLabel}
	 */
	private ArrayInteger opportunityLabel;

	/**
	 * 状态：1待审核、2上架中、3未通过、4已下架{@link com.zhihaoscm.domain.meta.biz.BusinessOpportunityDef.State}
	 */
	private Integer state;
}
