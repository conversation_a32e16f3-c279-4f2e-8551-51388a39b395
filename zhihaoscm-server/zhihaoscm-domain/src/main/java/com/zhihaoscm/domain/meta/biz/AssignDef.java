package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

public interface AssignDef {

	/**
	 * 分派类型
	 */
	@Getter
	enum Type {

		PURCHASE_DEMAND(1, "意向"),

		PROJECT(2, "项目"),

		REQUIREMENT_CUSTOMER(3, "货主船运"),

		REQUIREMENT_PLAT(4, "平台船运"),

		TRANSPORT_ORDER_SHIP_UPSTREAM(5, "船运单-上游"),

		TRANSPORT_ORDER_SHIP_DOWNSTREAM(6, "船运单-下游"),

		MONITOR_EARLY_WARN_RECORD(7, "监控预警记录"),

		PRODUCT_CONSIGNMENT(8, "寄售商品"),

		PRODUCT_GROUP_PURCHASE(9, "团购商品"),

		PRODUCT_BARGAIN(10, "捡漏商品"),

        ORDER(11,"订单"),
        ;

		private final Integer code;

		private final String name;

		private static final Map<Integer, AssignDef.Type> MAPPING;

		static {
			{
				Map<Integer, AssignDef.Type> mapping = new HashMap<>();
				for (AssignDef.Type value : AssignDef.Type.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static AssignDef.Type from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Type(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}
}
