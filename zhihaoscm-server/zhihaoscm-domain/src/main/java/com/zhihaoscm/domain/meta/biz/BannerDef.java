package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

public interface BannerDef {

	@Getter
	enum Type {

		PRODUCT(1, "商品"), SAND_PRICE(2, "沙价"), FREIGHT_RATES(3,
				"运价"), ADVERT_PAGE(4, "广告页"), NEWS(5, "资讯"), FEATURE(6, "功能页");

		private final Integer code;

		private final String name;

		private static final Map<Integer, Type> MAPPING;

		static {
			{
				Map<Integer, Type> mapping = new HashMap<>();
				for (Type value : Type.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static Type from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Type(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	@Getter
	enum Pos {

		PURCHASER(1, "链云砂石"),

		CARRIER(4, "链云船务");

		private final Integer code;

		private final String name;

		private static final Map<Integer, Pos> MAPPING;

		static {
			{
				Map<Integer, Pos> mapping = new HashMap<>();
				for (Pos value : Pos.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static Pos from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Pos(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

}
