package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

/**
 * 标签业务定义
 */
public interface TagBusinessDef {

    /**
     * 类型
     */
    @Getter
    enum Type {

        PRODUCT_TYPE(1, "品类"),
        Information(2, "资讯"),
        SAND_ACADEMY(3, "砂石学院"),
        SAND_HOT(4, "砂石热点");

        private final Integer code;

        private final String name;

        private static final Map<Integer, TagBusinessDef.Type> MAPPING;

        static {
            {
                Map<Integer, TagBusinessDef.Type> mapping = new HashMap<>();
                for (TagBusinessDef.Type value : TagBusinessDef.Type
                        .values()) {
                    mapping.put(value.getCode(), value);
                }
                MAPPING = mapping;
            }
        }

        Type(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public static TagBusinessDef.Type from(final Integer code) {
            return MAPPING.get(code);
        }

        public boolean match(final Integer code) {
            return this.code.equals(code);
        }
    }
}
