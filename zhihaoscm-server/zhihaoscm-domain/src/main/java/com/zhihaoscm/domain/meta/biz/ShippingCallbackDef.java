package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import org.springframework.util.Assert;

import lombok.Getter;

/**
 * 船运回调相关枚举
 */
public interface ShippingCallbackDef {

	/**
	 * 推送的操作类型
	 */
	@Getter
	enum PushType {
		SHIP_ACCEPT(1, "链云船主接单信息"),
		UPDATE_SHIP_ACCEPT(2, "管理后台修改船主接单信息"),
		UPDATE_SHIP_ACCEPT_CREATE_ORDER(3, "修改船主接单信息并生成船运单"),
		CONFIRM_RECEIVE_DEPOSIT(4, "链云船主确认收到定金"),
		REJECT_SHIP_ACCEPT(5, "链云管理后台拒绝船主接单"),
		START_LOADING(6, "链云平台点开始装货"),
		APPLY_SAILING(7, "发航申请"),
		CONFIRM_ARRIVAL_PORT(9, "到港确认"),
		CONFIRM_UNLOAD(10, "确认卸货"),
		COMPLETE_ORDER(11, "完成船运单"),
		UPDATE_DELIVERY(12, "修改船运单明细的发货信息"),
		UPDATE_GEAR_SHIFT(13, "修改船运单明细的接档信息"),
		UPDATE_DEPARTURE_PREPARATION(14, "修改船运单明细的离港准备信息、发船信息"),
		UPDATE_SET_SAIL(15, "修改船运单明细的发航信息"),
		UPDATE_UNLOADING(16, "修改船运单明细的卸货信息"),
		UPLOAD_DRAINAGE_VIDEO(17, "上传排水视频"),
		CANCEL_SHIP_ACCEPT(18, "船主取消接单"),
		PAY_OR_CONFIRM_SERVICE(19, "船主线上支付信息服务费或者确认船主线下支付的船务信息服务费"),
		UPLOAD_BANK_INFO(20, "船主上传银行信息"),
		COMPLETE_PLAT(21, "管理后台结束船运需求"),
		CLOSE_PLAT(22, "管理后台关闭船运需求"),
		UPDATE_ACCEPT(23, "承运商修改船主接单信息"),
		CLOSE_ORDER(24, "关闭船运单"),
		CONFIRM_DEPARTURE(25, "管理后台代替货主发航确认"),
		AGREE_UNLOAD(26, "管理后台代替货主同意卸货"),
		HMG_PAY_SUCCESS(28, "服务商垫付时黄码港支付成功"),
		UPLOAD_THE_DEPARTURE_INFORMATION(29, "上传发船信息"),
		UPLOAD_UNLOADING_INFORMATION(30, "上传卸货信息"),
		HMG_PUSH_PAY_FILE(31, "黄码港推送结算凭证"),
		PAY_OWNER_SHIPPING_DEPOSIT(32, "管理后台代替货主支付船运费用"),
		UPDATE_SHIP_PLAT(33, "管理后台修改船运需求"),


		;
		private final Integer code;
		private final String name;

		private static final Map<Integer, PushType> MAPPING;
		static {
			Map<Integer, PushType> mapping = new HashMap<>();
			for (PushType value : PushType.values()) {
				mapping.put(value.getCode(), value);
			}
			MAPPING = mapping;
		}

		PushType(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

		public static PushType from(final Integer code) {
			ShippingCallbackDef.PushType type = MAPPING.get(code);
			Assert.notNull(type, "wrong push type: " + type);
			return type;
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}

}
