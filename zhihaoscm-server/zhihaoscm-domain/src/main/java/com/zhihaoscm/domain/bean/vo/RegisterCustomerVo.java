package com.zhihaoscm.domain.bean.vo;

import java.util.List;

import lombok.Data;

/**
 * 注册用户统计vo
 */
@Data
public class RegisterCustomerVo {

    /**
     * 累计注册用户
     */
    private Long totalRegisterNum;

    /**
     * 累计注册采购商(链云)
     */
    private Long totalRegisterCustomerNum;

    /**
     * 累计注册承运商(船务)
     */
    private Long totalRegisterCarrierNum;

    /**
     * 累计自行注册
     */
    private Long totalRegisterSelfNum;

    /**
     * 累计推广注册
     */
    private Long totalRegisterExtensionNum;

    /**
     * 累计个人认证
     */
    private Long totalPersonalCertificationNum;

    /**
     * 累计组织机构认证
     */
    private Long totalOrganizationCertificationNum;


    /**
     * 累计注册采购商(链云)
     */
    private List<Long> addRegisterCustomerNums;

    /**
     * 累计注册承运商(船务)
     */
    private List<Long> addRegisterCarrierNums;

}
