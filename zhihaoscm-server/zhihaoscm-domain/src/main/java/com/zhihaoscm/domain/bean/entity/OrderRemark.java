package com.zhihaoscm.domain.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.domain.annotation.FileId;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单备注信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@Setter
@TableName("t_order_remark")
public class OrderRemark extends BaseEntityWithLongId {

	/**
	 * 订单ID
	 */
	private String orderId;

	/**
	 * 附件ID
	 */
	@FileId
	private Long fileId;

	/**
	 * 备注
	 */
	private String remark;
}
