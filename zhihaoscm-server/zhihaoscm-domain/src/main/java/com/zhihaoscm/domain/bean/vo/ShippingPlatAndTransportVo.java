package com.zhihaoscm.domain.bean.vo;

import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.entity.TransportOrderDetailsShip;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;

import lombok.Data;

@Data
public class ShippingPlatAndTransportVo {

	/**
	 * 船运需求
	 */
	private ShippingRequirementPlat shippingRequirementPlat;

	/**
	 * 关联的船运单详情
	 */
	private TransportOrderShip transportOrderShip;

	/**
	 * 关联的船运单明细详情
	 */
	private TransportOrderDetailsShip transportOrderDetailsShip;

}
