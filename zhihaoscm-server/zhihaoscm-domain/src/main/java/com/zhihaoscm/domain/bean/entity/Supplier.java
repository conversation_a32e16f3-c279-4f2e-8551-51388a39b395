package com.zhihaoscm.domain.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.common.bean.json.ArrayString;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 供应商管理
 * @author: xiaYZ 2025/7/31
 * @version: 1.0
 */
@Data
@TableName("t_supplier")
public class Supplier extends BaseEntityWithLongId {

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 关联集团id
     */
    private String groupCompanyId;

    /**
     * 关联集团名称
     */
    private String groupCompanyName;

    /**
     * 集团类型 1 矿山，2 砂场，3矿山加工厂，4贸易商，5其他
     */
    private Integer type;

    /**
     * 企业标签
     */
    private ArrayString labels;

    /**
     * 省份
     */
    private String province;

    /**
     * 地级市
     */
    private String city;

    /**
     * 县区
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private BigDecimal lng;

    /**
     * 纬度
     */
    private BigDecimal lat;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 主营品类
     *
     * <p>供应商的主营品类信息，用于分类筛选。</p>
     */
    private String mainCategories;

    /**
     * 企业logo文件id
     */
    private Long logoFileId;

    /**
     * 统一社会信用代码

     */
    private String socialCreditCode;

    /**
     * 法定代表人
     */
    private String legalRepresentative;

    /**
     * 移动端图片文件id
     */
    private Long mobileSideFileId;

    /**
     * PC端图片文件id
     */
    private Long pcSideFileId;

    /**
     * 描述介绍
     */
    private String description;

    /**
     * 展示位置（合作矿厂位置）
     */
    private Integer showLocation;


}
