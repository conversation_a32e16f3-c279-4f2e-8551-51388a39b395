package com.zhihaoscm.domain.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 油品站点
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Getter
@Setter
@TableName("t_oil_site")
public class OilSite extends BaseEntityWithLongId {

	/**
	 * 站点名称
	 */
	private String name;

	/**
	 * 品牌
	 */
	private String brand;

	/**
	 * 站点联系方式
	 */
	private String sitePhone;

	/**
	 * 维护人联系方式
	 */
	private String phone;

	/**
	 * 价格*数量
	 */
	private Integer quantity;

	/**
	 * 省编码
	 */
	private String provinceCode;

	/**
	 * 城市编码
	 */
	private String cityCode;

	/**
	 * 区域编码
	 */
	private String regionCode;

	/**
	 * 省名称
	 */
	private String provinceName;

	/**
	 * 城市名称
	 */
	private String cityName;

	/**
	 * 区域名称
	 */
	private String regionName;

	/**
	 * 地址全称
	 */
	private String address;

	/**
	 * 经纬度
	 */
	private String latLon;

	/**
	 * 状态：1 启用 0 禁用
	 */
	private Integer state;
}
