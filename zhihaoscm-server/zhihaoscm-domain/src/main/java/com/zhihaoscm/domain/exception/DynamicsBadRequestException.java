package com.zhihaoscm.domain.exception;

import java.util.Map;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DynamicsBadRequestException extends RuntimeException {

	/**
	 * 错误消息中的变量参数
	 */
	private Map<String, String> params;

	/**
	 * 扩展信息
	 */
	private String[] ext;

	public DynamicsBadRequestException() {
		super();
	}

	public DynamicsBadRequestException(String code) {
		super(code);
	}

	public DynamicsBadRequestException(String code, String... ext) {
		super(code);
		this.ext = ext;
	}

	public DynamicsBadRequestException(String code,
			Map<String, String> params) {
		super(code);
		this.params = params;
	}

	public DynamicsBadRequestException(String code, Throwable cause) {
		super(code, cause);
	}

	public DynamicsBadRequestException(Throwable cause) {
		super(cause);
	}

}
