package com.zhihaoscm.domain.bean.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.json.CustomerBankInfo;
import com.zhihaoscm.domain.bean.json.CustomerJsonInfo;
import com.zhihaoscm.domain.meta.biz.OwnerShippingDepositDef;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货主船运定金
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@TableName("t_owner_shipping_deposit")
public class OwnerShippingDeposit extends BaseEntityWithStringId {

	/**
	 * 货主id
	 */
	private Long ownerId;

	/**
	 * 船主id
	 */
	private Long captainId;

	/**
	 * 货主账号信息
	 */
	private CustomerJsonInfo ownerEnterprise;

	/**
	 * 船主账号信息
	 */
	private CustomerJsonInfo captainEnterprise;

	/**
	 * 关联船运单
	 */
	private String relationCode;

	/**
	 * 关联的船运需求
	 */
	private String shippingRequirementId;

	/**
	 * 金额
	 */
	private BigDecimal amount;

	/**
	 * 船舶的MMSI号
	 */
	private String shipId;

	/**
	 * 船舶名称
	 */
	private String shipName;

	/**
	 * 船舶中文名
	 */
	private String shipCnName;

	/**
	 * 支付方式
	 * {@link com.zhihaoscm.domain.meta.biz.TransportOrderShipDef.DepositPayType}
	 */
	private Integer paymentMethod;

	/**
	 * 结算凭证(自行支付) 电子结算单(黄码港)
	 */
	@FileId
	private Long paymentFileId;

	/**
	 * 船主收款银行账户
	 */
	private Long captainBankId;

	/**
	 * 船主收款银行账户信息
	 */
	private CustomerBankInfo captainBankInfo;

	/**
	 * 船主确认状态 {@link com.zhihaoscm.common.bean.constants.CommonDef.Symbol}
	 */
	private Integer captainConfirmState;

	/**
	 * 审核结果
	 */
	private String remark;

	/**
	 * 审核时间
	 */
	private LocalDateTime approveTime;

	/**
	 * 费用类型 {@link OwnerShippingDepositDef.Type}
	 */
	private Integer type;

	/**
	 * 状态 {@link OwnerShippingDepositDef.State}
	 */
	private Integer state;

	/**
	 * 支付交易单号 黄码港返回
	 */
	private String tradeNo;

	/**
	 * 银行电子回单(黄码港)
	 */
	@FileId
	private Long receiptFileId;

}
