package com.zhihaoscm.domain.bean.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.domain.bean.json.CustomerBankInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.meta.biz.ShipInfoServiceFeeDef;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementCustomerDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderShipDef;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 船运单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Getter
@Setter
@TableName("t_transport_order_ship")
public class TransportOrderShip extends BaseEntityWithStringId {

	/**
	 * 货品类型
	 */
	private String goodsType;

	/**
	 * 平台船运需求单ID
	 */
	private String srpId;

	/**
	 * 承运商船运需求单ID
	 */
	private Long sraId;

	/**
	 * 货主id
	 */
	private Long ownerId;

	/**
	 * 货主账号信息
	 */
	private Enterprise ownerEnterprise;

	/**
	 * 货主信息联系人
	 */
	private String ownerName;
	/**
	 * 货主信息手机号
	 */
	private String ownerMobile;

	/**
	 * 船主账号id
	 */
	private Long captainId;

	/**
	 * 船主账号信息
	 */
	private Enterprise captainEnterprise;

	/**
	 * 船主信息联系人
	 */
	private String captainName;

	/**
	 * 船主信息手机号
	 */
	private String captainMobile;

	/**
	 * 船主收款银行账户id--收定金使用
	 */
	private Long captainBankId;

	/**
	 * 船主收款银行账户信息--收定金使用
	 */
	private CustomerBankInfo captainBankInfo;

	/**
	 * 船舶id
	 */
	private String shipId;

	/**
	 * 船舶类型
	 */
	private Integer shipType;

	/**
	 * 船舶名称
	 */
	private String shipName;

	/**
	 * 始发地码头id
	 */
	private Long sourcePortId;

	/**
	 * 始发地码头名称
	 */
	private String sourcePortName;

	/**
	 * 目的地码头id
	 */
	private Long destinationPortId;

	/**
	 * 目的地码头名称
	 */
	private String destinationPortName;

	/**
	 * 航线id
	 */
	private Long shipRouteId;

	/**
	 * 航线始发地
	 */
	private String shipRouteSource;

	/**
	 * 航线目的地
	 */
	private String shipRouteDestination;

	/**
	 * 上游处理专员id
	 */
	private Long upstreamHandlerId;

	/**
	 * 上游处理专员姓名
	 */
	private String upstreamHandlerName;

	/**
	 * 下游处理专员id
	 */
	private Long downstreamHandlerId;

	/**
	 * 下游处理专员姓名
	 */
	private String downstreamHandlerName;

	/**
	 * 意向单价
	 */
	private BigDecimal unitPrice;

	/**
	 * 吨数
	 */
	private Integer ton;

	/**
	 * 实际运费
	 */
	private BigDecimal actualFreightCost;

	/**
	 * 装载日期
	 */
	private LocalDate loadDate;

	/**
	 * 装载日期宽限的天数
	 */
	private Integer loadDays;

	/**
	 * 装卸天数
	 */
	private Integer loadUnloadDays;

	/**
	 * 滞期费
	 */
	private BigDecimal demurrageFee;

	/**
	 * 海事费用 {@link ShippingRequirementCustomerDef.MaritimeAffairsFee}
	 */
	private Integer maritimeAffairsFee;

	/**
	 * 定金
	 */
	private BigDecimal deposit;

	/**
	 * 类型
	 */
	private Integer type;

	/**
	 * 定金支付类型 {@link TransportOrderShipDef.DepositPayType}
	 */
	private Integer payType;

	/**
	 * 状态 {@link TransportOrderShipDef.State}
	 */
	private Integer state;

	/**
	 * 货主船务信息服务费
	 */
	private BigDecimal ownerShipInfoServiceFee;

	/**
	 * 货主船务信息服务费支付状态 {@link ShipInfoServiceFeeDef.State}
	 */
	private Integer ownerShipInfoServiceFeeState;

	/**
	 * 船主船务信息服务费
	 */
	private BigDecimal captainShipInfoServiceFee;

	/**
	 * 船主船务信息服务费支付状态 {@link ShipInfoServiceFeeDef.State}
	 */
	private Integer captainShipInfoServiceFeeState;

	/**
	 * 货主服务费支付类型 {@link ShipInfoServiceFeeDef.PayType}
	 */
	private Integer ownerShipInfoServiceFeePayType;

	/**
	 * 船主服务费支付类型 {@link ShipInfoServiceFeeDef.PayType}
	 */
	private Integer captainShipInfoServiceFeePayType;

	/**
	 * 开始装货时间
	 */
	private LocalDateTime startLoadingTime;

	/**
	 * 是否发航 {@link com.zhihaoscm.common.bean.constants.CommonDef.Symbol}
	 */
	private Integer isSailConfirm;

	/**
	 * 是否同意卸货 {@link com.zhihaoscm.common.bean.constants.CommonDef.Symbol}
	 */
	private Integer isAgreeUnloading;

	/**
	 * 黄码港审核状态 {@link TransportOrderShipDef.HmgCheckState}
	 */
	private Integer hmgCheckState;

	/**
	 * 黄码港运单号
	 */
	private String hmgWaybillNo;

	/**
	 * 应用id
	 */
	private Long appId;
}
