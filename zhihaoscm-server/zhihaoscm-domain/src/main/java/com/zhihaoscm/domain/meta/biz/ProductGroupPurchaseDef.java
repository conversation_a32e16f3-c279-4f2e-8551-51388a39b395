package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

/**
 * 团购商品管理定义
 */
public interface ProductGroupPurchaseDef {
	/**
	 * 税率
	 */
	@Getter
	enum TaxRate {
		THREE(1, "3%", 0.03),

		SIX(2, "6%", 0.06),

		NINE(3, "9%", 0.09),

		THIRTEEN(4, "13%", 0.13);

		private final Integer code;

		private final String name;

		private final double rateValue;

		private static final Map<Integer, TaxRate> MAPPING;

		static {
			{
				Map<Integer, TaxRate> mapping = new HashMap<>();
				for (TaxRate value : TaxRate.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static TaxRate from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		TaxRate(Integer code, String name, double rateValue) {
			this.code = code;
			this.name = name;
			this.rateValue = rateValue;
		}

	}

	/**
	 * 浮动方式
	 */
	@Getter
	enum FloatType {
		FLOAT_RATIO(1, "浮动比例"), FLOAT_TON(2, "浮动固定吨数");

		private final Integer code;

		private final String name;

		private static final Map<Integer, FloatType> MAPPING;

		static {
			{
				Map<Integer, FloatType> mapping = new HashMap<>();
				for (FloatType value : FloatType.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static FloatType from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		FloatType(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 结算方式
	 */
	@Getter
	enum SettlementMethod {
		FULL_PAYMENT(1, "全部货款"), PAY_DEPOSIT(2, "支付定金");

		private final Integer code;

		private final String name;

		private static final Map<Integer, SettlementMethod> MAPPING;

		static {
			{
				Map<Integer, SettlementMethod> mapping = new HashMap<>();
				for (SettlementMethod value : SettlementMethod.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static SettlementMethod from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		SettlementMethod(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

	}

	/**
	 * 支付定金方式
	 */
	@Getter
	enum DepositPaymentMethod {
		PAYMENT_RATIO(1, "货款比例"), FIXED_AMOUNT(2, "固定金额");

		private final Integer code;

		private final String name;

		private static final Map<Integer, DepositPaymentMethod> MAPPING;

		static {
			{
				Map<Integer, DepositPaymentMethod> mapping = new HashMap<>();
				for (DepositPaymentMethod value : DepositPaymentMethod
						.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static DepositPaymentMethod from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		DepositPaymentMethod(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

	}

	/**
	 * 商品状态
	 */
	@Getter
	enum State {
		UP_SHELF(1, "上架"), DOWN_SHELF(2, "下架");

		private final Integer code;

		private final String name;

		private static final Map<Integer, State> MAPPING;

		static {
			{
				Map<Integer, State> mapping = new HashMap<>();
				for (State value : State.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static State from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		State(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

	}

	/**
	 * 发布状态
	 */
	@Getter
	enum PublishState {
		NOT_AUDIT(1, "待审核"), FAIL(2, "未通过"), PASS(3, "已发布"), DOWN_SHELF(4,
				"已下架"), CLOSE(5, "已关闭");

		private final Integer code;

		private final String name;

		private static final Map<Integer, PublishState> MAPPING;

		static {
			{
				Map<Integer, PublishState> mapping = new HashMap<>();
				for (PublishState value : PublishState.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static PublishState from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		PublishState(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 团购状态
	 */
	@Getter
	enum GroupPurchaseState {
		GROUPING(1, "拼团中"), SUCCESS(2, "拼团成功"), FAIL(3, "拼团失败");

		private final Integer code;

		private final String name;

		private static final Map<Integer, GroupPurchaseState> MAPPING;

		static {
			{
				Map<Integer, GroupPurchaseState> mapping = new HashMap<>();
				for (GroupPurchaseState value : GroupPurchaseState.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static GroupPurchaseState from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		GroupPurchaseState(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}
}
