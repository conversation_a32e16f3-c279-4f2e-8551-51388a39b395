package com.zhihaoscm.domain.bean.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.domain.annotation.DiffField;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.PlanInfo;
import com.zhihaoscm.domain.meta.biz.OilOrderDef;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 油品订单快照
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@Getter
@Setter
@TableName("t_oil_order_snapshot")
public class OilOrderSnapshot extends BaseEntityWithStringId {

    /**
     * 订单方式:1.自行下单/2.平台代下{@link OilOrderDef.OrderType}
     */
    private Integer orderType;

    /**
     * 油品站点id
     */
    @DiffField(alias = "预约站点", type = DiffField.Type.ID, service = "oilSiteServiceImpl")
    private Long oilSiteId;

    /**
     * 船主id
     */
    private Long captainId;

    /**
     * 船主信息
     */
    private Enterprise captainInfo;

    /**
     * 船主信息联系人
     */
    @DiffField(alias = "联系人")
    private String captainName;

    /**
     * 船主信息手机号
     */
    @DiffField(alias = "联系方式")
    private String captainMobile;

    /**
     * 船舶的MMSI
     */
    private String shipId;

    /**
     * 船舶名称
     */
    @DiffField(alias = "预约船舶")
    private String shipName;

    /**
     * 船舶中文名
     */
    private String shipCnName;

    /**
     * 油品类型:1.0号柴油/2.轻质燃油{@link OilOrderDef.OilType}
     */
    @DiffField(alias = "油品类型", type = DiffField.Type.ENUM, clazz = OilOrderDef.OilType.class)
    private Integer oilType;

    /**
     * 预估加油量
     */
    @DiffField(alias = "预估加油量")
    private BigDecimal bookingRefuelingVolume;

    /**
     * 预约加油日期
     */
	@DiffField(alias = "预约加油时间", type = DiffField.Type.DATE, fields = {
			"bookingRefuelingTime", "bookingRefuelingTimePeriod" }, join = " ")
    private LocalDateTime bookingRefuelingTime;

    /**
     * 预约加油时间段{@link OilOrderDef.TimePeriod}
     */
    @DiffField(alias = "预约加油时间", type = DiffField.Type.ENUM, clazz = OilOrderDef.TimePeriod.class, fields = {
            "bookingRefuelingTime", "bookingRefuelingTimePeriod" })
    private Integer bookingRefuelingTimePeriod;

    /**
     * 预估油价
     */
	@DiffField(alias = "油价")
    private BigDecimal bookingRefuelingPrice;

    /**
     * 预估油费
     */
    private BigDecimal bookingRefuelingCost;

    /**
     * 备注
     */
    private String remark;

    /**
     * 计划信息
     */
    private PlanInfo planInfo;

    /**
     * 实际加油时间
     */
    private LocalDateTime actualRefuelingTime;

    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 下单时间
     */
    private LocalDateTime bookingTime;

    /**
     * 付款金额
     */
    private BigDecimal payAmount;

    /**
     * 支付状态:1.待确认/2.已支付{@link OilOrderDef.PayState}
     */
    private Integer payState;

	/**
	 * 付款凭证ids-最多5张
	 */
	@FileId
	private ArrayLong payFileIds;

	/**
	 * 用户是否同意加油协议{@link CommonDef.Symbol}
	 */
	private Integer isAgreeProtocol;

    /**
     * 业务状态：1.预约中、2.进行中、3.已完成、4.已取消{@link OilOrderDef.State}
     */
    private Integer state;

    /**
     * 进行中状态：1.待报计划、2.待确认加油、3.待完成加油、4.已完成{@link OilOrderDef.ProgressState}
     */
    private Integer progressState;

    /**
     * 取消之前的业务状态
     */
    private Integer beforeCancelState;

    /**
     * 取消之前的进行中状态
     */
    private Integer beforeCancelProgressState;
}
