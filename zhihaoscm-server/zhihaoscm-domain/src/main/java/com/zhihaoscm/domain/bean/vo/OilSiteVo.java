package com.zhihaoscm.domain.bean.vo;

import java.math.BigDecimal;

import com.zhihaoscm.domain.bean.entity.OilIndex;
import com.zhihaoscm.domain.bean.entity.OilSite;

import lombok.Data;

@Data
public class OilSiteVo {

	/**
	 * 油品站点
	 */
	private OilSite oilSite;
	/**
	 * 与该站点的距离（米）
	 */
	private BigDecimal distance;
	/**
	 * 油品指数
	 */
	private OilIndex oilIndex;
	/**
	 * 油品指数中最低的实际售卖价
	 */
	private Long lowestListingPrice;
	/**
	 * 油品指数中最低的实际售卖价的油品类型的数量
	 */
	private Long lowestListingPriceCount;
}
