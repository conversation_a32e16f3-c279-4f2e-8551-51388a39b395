package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import org.springframework.util.Assert;

import lombok.Getter;

/**
 * 客户定义
 */
public interface AppTypeDef {




	@Getter
	enum AppType {

		LIAN_YUN(1,"lianyun"),

		CHUAN_WU(2,"chuanwu"),
		;

		private final Integer code;

		private final String name;

		private static final Map<Integer, AppType> MAPPING;

		static {
			{
				Map<Integer, AppType> mapping = new HashMap<>();
				for (AppType value : AppType.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		AppType(Integer code,String name) {
			this.code = code;
			this.name = name;
		}

		public static AppType from(final Integer code) {
			AppType role = MAPPING.get(code);
			Assert.notNull(role, "wrong code " + code);
			return role;
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}


}
