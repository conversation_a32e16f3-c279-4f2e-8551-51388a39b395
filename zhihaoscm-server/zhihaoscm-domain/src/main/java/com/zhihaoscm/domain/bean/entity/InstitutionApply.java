package com.zhihaoscm.domain.bean.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.domain.annotation.FileId;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 组织机构认证
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@Getter
@Setter
@TableName("t_institution_apply")
public class InstitutionApply extends BaseEntityWithLongId {

	/**
	 * 用户id
	 */
	private Long customerId;

	/**
	 * 组织机构名称
	 */
	private String institutionName;

	/**
	 * 统一社会信用代码
	 */
	private String unifiedSocialCreditCode;

	/**
	 * 营业执照文件id
	 */
	@FileId
	private Long businessLicenseFileId;

	/**
	 * 授权委托书文件id
	 */
	@FileId
	private Long powerAttorneyFileId;

	/**
	 * 法定代表人
	 */
	private String legalRepresentative;

	/**
	 * 审核人id
	 */
	private Long approveBy;

	/**
	 * 审核人名称
	 */
	private String approveName;

	/**
	 * 审核时间
	 */
	private LocalDateTime approveTime;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 是否取消
	 */
	private Integer isCancel;

	/**
	 * 状态 1：待审核，2：已驳回，3：已通过
	 */
	private Integer state;
}
