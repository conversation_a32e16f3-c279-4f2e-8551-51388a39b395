package com.zhihaoscm.domain.bean.json;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

public class ArrayParameterInfo extends ArrayList<ParameterInfo> {
	public ArrayParameterInfo() {
	}

	public ArrayParameterInfo(List<ParameterInfo> lists) {
		if (CollectionUtils.isNotEmpty(lists)) {
			super.addAll(lists);
		}
	}

	public ArrayParameterInfo(ParameterInfo... item) {
		super.addAll(List.of(item));
	}
}
