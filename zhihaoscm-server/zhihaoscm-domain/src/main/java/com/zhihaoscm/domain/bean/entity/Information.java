package com.zhihaoscm.domain.bean.entity;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.meta.biz.InformationDef;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 砂石资讯
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_information")
public class Information extends BaseEntityWithLongId {

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 资讯类型 {@link InformationDef.Type}
	 */
	private Integer type;

	/**
	 * 来源
	 */
	private String source;

	/**
	 * 类别(1:公告;2:资讯) {@link InformationDef.Category}
	 */
	private Integer category;

	/**
	 * 发布日期
	 */
	private LocalDate publishDate;

	/**
	 * 内容富文本
	 */
	private String content;

	/**
	 * 免责声明
	 */
	private String disclaimers;

	/**
	 * 封面
	 */
	@FileId
	private Long coverFileId;

	/**
	 * 视频
	 */
	@FileId
	private Long videoFileId;

	/**
	 * 简要描述
	 */
	private String description;

	/**
	 * 是否上架
	 */
	private Integer state;

	/**
	 * 分类：1:行业动态、2:分析报告、3:每日评述。{@link InformationDef.Classify}
	 */
	private Integer classify;

	/**
	 * 收费类型：1:免费内容、2:会员专享。 {@link InformationDef.FeeType}
	 */
	private Integer feeType;

	/**
	 * 移动端展示位置
	 */
	private Integer showLocation;

	/**
	 * PC端展示位置
	 */
	private Integer pcShowLocation;

}
