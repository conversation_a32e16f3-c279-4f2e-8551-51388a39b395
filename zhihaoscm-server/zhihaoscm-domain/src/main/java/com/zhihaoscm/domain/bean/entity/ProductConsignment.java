package com.zhihaoscm.domain.bean.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.common.bean.json.GeoPoint;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.json.ArrayBankAccountInfo;
import com.zhihaoscm.domain.bean.json.ArrayParameterInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.meta.biz.ProductConsignmentDef;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 寄售商品
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Getter
@Setter
@TableName("t_product_consignment")
public class ProductConsignment extends BaseEntityWithStringId {

	/**
	 * 商品标题
	 */
	private String title;

	/**
	 * 权重 默认值0
	 */
	private Integer weight;

	/**
	 * 销售单价
	 */
	private BigDecimal unitPrice;

	/**
	 * 是否含税：1是 0否{@link com.zhihaoscm.common.bean.constants.CommonDef.Symbol}
	 */
	private Integer isIncludeTax;

	/**
	 * 税率{@link ProductConsignmentDef.TaxRate}
	 */
	private Integer taxRate;

	/**
	 * 主图文件编号
	 */
	@FileId
	private Long imgMainId;

	/**
	 * 其他图片文件编号集合
	 */
	@FileId
	private ArrayLong imgIds;

	/**
	 * 视频编号
	 */
	@FileId
	private Long videoId;

	/**
	 * 供货商编号
	 */
	private Long supplierId;

	/**
	 * 供应商信息
	 */
	private Enterprise supplierEnterprise;

	/**
	 * 品类编号
	 */
	private String productTypeId;

	/**
	 * 品类名称
	 */
	private String productTypeName;

	/**
	 * 品类规格
	 */
	private String productTypeSpec;

	/**
	 * 采区
	 */
	private String area;

	/**
	 * 参数信息
	 */
	private ArrayParameterInfo parameterInfo;

	/**
	 * 商品标签
	 */
	private ArrayString productLabel;

	/**
	 * 商品详情介绍
	 */
	private String introduce;

	/**
	 * 提货开始日期
	 */
	private LocalDateTime deliveryStartTime;

	/**
	 * 提货周期
	 */
	private Integer deliveryCycle;

	/**
	 * 省编码
	 */
	private String provinceCode;

	/**
	 * 城市编码
	 */
	private String cityCode;

	/**
	 * 区域编码
	 */
	private String regionCode;

	/**
	 * 详细地址
	 */
	private String address;

	/**
	 * 经纬度
	 */
	private GeoPoint geo;

	/**
	 * 已订吨数（吨）
	 */
	private Integer orderedTon;

	/**
	 * 库存量（吨）
	 */
	private Integer inventoryQuantity;

	/**
	 * 起订吨数（吨）
	 */
	private Integer minOrderTon;

	/**
	 * 最小调整吨数（吨）
	 */
	private Integer minAdjustmentTon;

	/**
	 * 提货说明
	 */
	private String deliveryDescn;

	/**
	 * 结算方式：1全部货款 2支付定金{@link ProductConsignmentDef.SettlementMethod}
	 */
	private Integer settlementMethod;

	/**
	 * 支付定金方式：1货款比例 2固定金额{@link ProductConsignmentDef.DepositPaymentMethod}
	 */
	private Integer depositPaymentMethod;

	/**
	 * 货款比例
	 */
	private BigDecimal paymentRatio;

	/**
	 * 固定金额
	 */
	private BigDecimal fixedAmount;

	/**
	 * 结算说明
	 */
	private String settlementDescn;

	/**
	 * 服务保障
	 */
	private String serviceGuarantee;

	/**
	 * 规则
	 */
	private String regulation;

	/**
	 * 商品专员id
	 */
	private Long handlerId;

	/**
	 * 商品专员姓名
	 */
	private String handlerName;

	/**
	 * 商品专员联系电话
	 */
	private ArrayString handlerMobile;

	/**
	 * 收款账号
	 */
	private ArrayBankAccountInfo receivingAccount;

	/**
	 * 审核意见
	 */
	private String auditDescn;

	/**
	 * 发布状态 1.待审核 2.未通过 3.已发布 4.已下架
	 * 5.已关闭{@link ProductConsignmentDef.PublishState}
	 */
	private Integer publishState;

	/**
	 * 审核时间
	 */
	private LocalDateTime auditTime;

	/**
	 * 商品状态：1上架 2下架{@link ProductConsignmentDef.State}
	 */
	private Integer state;
}
