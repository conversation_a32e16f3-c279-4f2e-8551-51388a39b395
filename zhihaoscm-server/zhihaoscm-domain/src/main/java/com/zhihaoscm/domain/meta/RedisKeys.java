package com.zhihaoscm.domain.meta;

/**
 * redis相关常量定义
 */
public interface RedisKeys {

	/**
	 * 所有redis key的前缀
	 */
	String COMMON_PREFIX = "SCM:";

	interface Cache {
		/**
		 * login info 前缀
		 */
		String LOGIN_INFO_PREFIX = RedisKeys.COMMON_PREFIX + "LOGIN:";

		/**
		 * H5 login info 前缀
		 */
		String HW_LOGIN_INFO_PREFIX = RedisKeys.COMMON_PREFIX + "HW_LOGIN:";

		/**
		 * login短信验证
		 */
		String LOGIN_MSG_PREFIX = RedisKeys.COMMON_PREFIX + "LOGIN_MSG:";

		/**
		 * 客户PC端登录短信验证
		 */
		String PC_LOGIN_MSG_PREFIX = RedisKeys.COMMON_PREFIX + "PC_LOGIN_MSG:";

		/**
		 * 我有意向验证码
		 */
		String DEMAND_MSG_PREFIX = RedisKeys.COMMON_PREFIX + "DEMAND_MSG:";
		/**
		 * 换绑手机号验证码
		 */
		String CHANGE_MOBILE_MSG_PREFIX = RedisKeys.COMMON_PREFIX
				+ "CHANGE_MOBILE_MSG:";

		/**
		 * 找回/修改密码手机号验证码
		 */
		String CAPTCHA_MOBILE_MSG_PREFIX = RedisKeys.COMMON_PREFIX
				+ "CAPTCHA_MOBILE_MSG:";

		/**
		 * 注销账号验证码
		 */
		String LOG_OFF_MSG_PREFIX = RedisKeys.COMMON_PREFIX
				+ "LOG_OFF_MSG_PREFIX:";

		/**
		 * 更新邮箱验证码
		 */
		String CAPTCHA_EMAIL_MSG_PREFIX = RedisKeys.COMMON_PREFIX
				+ "CAPTCHA_EMAIL_MSG:";

		/**
		 * 验证码
		 */
		String CAPTCHA_PREFIX = RedisKeys.COMMON_PREFIX + "CAPTCHA:";

		/**
		 * 客户端PC图形拖动验证码
		 */
		String PC_CUSTOM_CAPTCHA_PREFIX = RedisKeys.COMMON_PREFIX
				+ "PC_CUSTOM_CAPTCHA:";

		/**
		 * 客户端PC图形文本验证码
		 */
		String PC_CUSTOM_CAPTCHA_TEXT_PREFIX = RedisKeys.COMMON_PREFIX
				+ "PC_CUSTOM_CAPTCHA_TEXT:";

		/**
		 * 部门缓存
		 */
		String DEPT = RedisKeys.COMMON_PREFIX + "DEPT";
		/**
		 * 阅读量缓存
		 */
		String READ_QUANTITY_PREFIX = RedisKeys.COMMON_PREFIX
				+ "READ_QUANTITY:";
		/**
		 * 小程序 login info 前缀
		 */
		String MINI_LOGIN_INFO_PREFIX = RedisKeys.COMMON_PREFIX + "MINI_LOGIN:";
		/**
		 * 小程序 注册前缀
		 */
		String MINI_REGISTER_PREFIX = RedisKeys.COMMON_PREFIX
				+ "MINI_REGISTER:";
		/**
		 * 小程序 注册前缀
		 */
		String PC_REGISTER_PREFIX = RedisKeys.COMMON_PREFIX + "PC_REGISTER:";
		/**
		 * 小程序 最后一次登录的身份
		 */
		String MINI_LAST_LOGIN_PREFIX = RedisKeys.COMMON_PREFIX
				+ "MINI_LAST_LOGIN:";

		/**
		 * 文件缓存前缀
		 */
		String FILE_CACHE_PREFIX = RedisKeys.COMMON_PREFIX + "FILE:";
		/**
		 * 客户id生成器的key
		 */
		String CUSTOMER_ID_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "CUSTOMER_ID_GENERATOR:";

		/**
		 * 微信获取token稳定版
		 */
		String WX_STABLE_TOKEN = RedisKeys.COMMON_PREFIX + "WX_STABLE_TOKEN:";

		/**
		 * 微信公众号ticket
		 */
		String WX_OFFICIAL_TICKET = RedisKeys.COMMON_PREFIX
				+ "WX_OFFICIAL_TICKET:";

		/**
		 * 微信公众号token
		 */
		String WX_OFFICIAL_TOKEN = RedisKeys.COMMON_PREFIX
				+ "WX_OFFICIAL_TOKEN:";

		/**
		 * 企业微信token的key
		 */
		String WXW_TOKEN = RedisKeys.COMMON_PREFIX + "WXW_TOKEN:";

		/**
		 * 购买意向单号生成器的key
		 */
		String PURCHASE_DEMAND_ID_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PURCHASE_DEMAND_ID_GENERATOR:";
		/**
		 * 项目编号生成器的key
		 */
		String PROJECT_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PROJECT_CODE_GENERATOR:";
		/**
		 * 商品编号生成器的key
		 */
		String PRODUCT_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PRODUCT_CODE_GENERATOR:";

		/**
		 * 寄售商品编号生成器的key
		 */
		String PRODUCT_CONSIGNMENT_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PRODUCT_CONSIGNMENT_CODE_GENERATOR:";

		/**
		 * 团购商品编号生成器的key
		 */
		String PRODUCT_GROUP_PURCHASE_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PRODUCT_GROUP_PURCHASE_CODE_GENERATOR:";

		/**
		 * 捡漏商品编号生成器的key
		 */
		String PRODUCT_BARGAIN_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PRODUCT_BARGAIN_CODE_GENERATOR:";

		/**
		 * 付款编号生成器key
		 */
		String PURCHASE_PAYMENT_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PURCHASE_PAYMENT_CODE_GENERATOR:";

		/**
		 * 采购合同编号生成器key
		 */
		String PURCHASE_CONTRACT_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PURCHASE_CONTRACT_CODE_GENERATOR:";

		/**
		 * 采购对账单编号生成器key
		 */
		String PURCHASE_RECONCILIATION_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PURCHASE_RECONCILIATION_CODE_GENERATOR:";

		/**
		 * 签收编号生成器key
		 */
		String PURCHASE_GOODS_RECEIPT_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PURCHASE_GOODS_RECEIPT_CODE_GENERATOR:";

		/**
		 * 采购提货单单编号生成器key
		 */
		String PURCHASE_GOODS_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PURCHASE_GOODS_CODE_GENERATOR:";

		/**
		 * 开票编号生成器key
		 */
		String PURCHASE_INVOICE_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PURCHASE_INVOICE_CODE_GENERATOR:";

		/**
		 * 平台船运编号生成器key
		 */
		String SHIPPING_REQUIREMENT_PLAT_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "SHIPPING_REQUIREMENT_PLAT_CODE_GENERATOR:";

		/**
		 * 船运单编号生成器key
		 *
		 */
		String TRANSPORT_ORDER_SHIP_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "TRANSPORT_ORDER_SHIP_CODE_GENERATOR:";

		/**
		 * 预付款编号生成器key
		 *
		 */
		String ADVANCE_PAYMENT_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "ADVANCE_PAYMENT_CODE_GENERATOR:";

		/**
		 * 服务费账单编号生成器key
		 *
		 */
		String SERVICE_COST_BILL_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "SERVICE_COST_BILL_CODE_GENERATOR:";

		/**
		 * 船舶查询历史
		 *
		 */
		String SHIP_HISTORY = RedisKeys.COMMON_PREFIX + "SHIP_HISTORY:";

		/**
		 * 船舶/码头查询历史
		 *
		 */
		String SHIP_PORT_HISTORY = RedisKeys.COMMON_PREFIX
				+ "SHIP_PORT_HISTORY:";

		/**
		 * 车辆查询历史
		 *
		 */
		String VEHICLE_HISTORY = RedisKeys.COMMON_PREFIX + "VEHICLE_HISTORY:";

		/**
		 * AI调用
		 *
		 */
		String AI_CALL = RedisKeys.COMMON_PREFIX + "AI_CALL:";

		/**
		 * 会员开通
		 */
		String MEMBER_OPEN = RedisKeys.COMMON_PREFIX + "MEMBER_OPEN:";

		/**
		 * 商品编号生成器的key
		 */
		String PROMOTION_CODE_GENERATOR = RedisKeys.COMMON_PREFIX
				+ "PROMOTION_CODE_GENERATOR:";

		/**
		 * 平台预收款
		 */
		String PLATFORM_ADVANCE_PAYMENT = RedisKeys.COMMON_PREFIX
				+ "PLATFORM_ADVANCE_PAYMENT:";

		/**
		 * 安装推广
		 */
		String INSTALL_PROMOTION = RedisKeys.COMMON_PREFIX
				+ "INSTALL_PROMOTION:";

		/**
		 * 油品订单
		 */
		String OIL_ORDER = RedisKeys.COMMON_PREFIX + "OIL_ORDER:";

		/**
		 * 船运定金
		 */
		String SHIPPING_DEPOSIT = RedisKeys.COMMON_PREFIX + "SHIPPING_DEPOSIT:";

		/**
		 * 船务信息服务费
		 */
		String SHIP_INFO_SERVICE_FEE = RedisKeys.COMMON_PREFIX
				+ "SHIP_INFO_SERVICE_FEE:";

		/**
		 * 油品费用
		 */
		String OIL_FEE = RedisKeys.COMMON_PREFIX + "OIL_FEE:";

		/**
		 * 用户token
		 */
		String CUSTOM_TOKEN = RedisKeys.COMMON_PREFIX + "CUSTOM_TOKEN:";

		/**
		 * 一句话token
		 */
		String NLS_TOKEN = RedisKeys.COMMON_PREFIX + "NLS_TOKEN";

		/**
		 * 船舶评分
		 */
		String SHIP_SCOPE = RedisKeys.COMMON_PREFIX + "SHIP_SCOPE";

		/**
		 * 区域查船
		 */
		String SHIP_EXTENT = RedisKeys.COMMON_PREFIX + "SHIP_EXTENT";

		/**
		 * asi查询详情
		 */
		String SHIP_ASI = RedisKeys.COMMON_PREFIX + "SHIP_ASI";

		/**
		 * asi查询详情批量
		 */
		String SHIP_ASI_BATCH = RedisKeys.COMMON_PREFIX + "SHIP_ASI_BATCH";

		/**
		 * 查询船舶轨迹
		 */
		String SHIP_ASI_TRACK = RedisKeys.COMMON_PREFIX + "SHIP_ASI_TRACK";

		/**
		 * 码头查询历史记录
		 */
		String PORT_HISTORY = RedisKeys.COMMON_PREFIX + "PORT_HISTORY:";

		/**
		 * 码头、城市查询历史记录
		 */
		String PORT_ALL_HISTORY = RedisKeys.COMMON_PREFIX + "PORT_HISTORY:";

		// 用户token
		String LICENSE = RedisKeys.COMMON_PREFIX + "LICENSE:";

		// 通知
		String NOTIFICATION = RedisKeys.COMMON_PREFIX + "NOTIFICATION:";

		// 语料zset
		String CORPUS_ZSET = RedisKeys.COMMON_PREFIX + "CORPUS_ZSET:";

		/**
		 * 弹窗广告
		 */
		String POP_UP_ADVERT_ZSET = RedisKeys.COMMON_PREFIX
				+ "POP_UP_ADVERT_ZSET:";

		/**
		 * app扫码登录
		 */
		String QR_LOGIN = RedisKeys.COMMON_PREFIX + "QR_LOGIN:";

		/**
		 * 商品拼团
		 */
		String GROUP_PRODUCT = RedisKeys.COMMON_PREFIX + "GROUP_PRODUCT:";

		/**
		 * 订单编码生成
		 */
		String ORDER_GENERATOR = RedisKeys.COMMON_PREFIX + "ORDER_GENERATOR:";

		/**
		 * 收款管理
		 */
		String RECEIVE_PAYMENT_GENERATOR = RedisKeys.COMMON_PREFIX + "RECEIVE_PAYMENT_GENERATOR:";

		/**
		 * 集团管理
		 */
		String GROUP_COMPANY = RedisKeys.COMMON_PREFIX + "GROUP_COMPANY:";
	}

}
