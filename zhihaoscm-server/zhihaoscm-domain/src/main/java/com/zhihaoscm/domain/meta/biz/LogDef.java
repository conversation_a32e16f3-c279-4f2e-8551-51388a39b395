package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

public interface LogDef {

	// operatorType
	// 外部用户
	String OUTER = "1";
	// 内部用户
	String INNER = "2";


	// subType
	// 创建
	String CREATE = "1";
	String UPDATE = "2";
	String DELETE = "3";
	// 撤回
	String REVOKE = "4";

	// 指派
	String ASSIGN = "7";
	// 变更状态
	String UPDATE_STATUS = "8";
	// 绑定操作
	String BIND = "9";

	// 提交
	String SUBMIT = "16";
	// 发布
	String PUBLISH = "17";
	// 驳回
	String REJECT = "18";

	// 全局 - 登录系统
	String USER_ENABLE = " 启用了 用户 #highlight#";
	String USER_DISABLE = " 禁用了 用户 #highlight#";
	// 用户管理 - 充值列表 - 兑换会员
	String USER_MANAGEMENT_RECHARGE_LIST_REDEEM_MEMBERSHIP = " 兑换了 会员 #highlight# #vip_level# #vip_duration#";
	// 用户管理 - 激活码 - 删除激活码
	String USER_MANAGEMENT_ACTIVATION_CODE_DELETE = " 删除了 激活码 #highlight# #vip_level# #vip_duration#";
	// 用户管理 - 激活码 - 新增激活码
	String USER_MANAGEMENT_ACTIVATION_CODE_ADD = " 新增了 激活码 #highlight# #vip_level# #vip_duration#";
	// 用户管理 - 推广管理 - 新增推广
	String USER_MANAGEMENT_PROMOTION_MANAGEMENT_ADD = " 新增了 推广 #highlight# #title#";
	// 用户管理 - 推广管理 - 延期推广
	String USER_MANAGEMENT_PROMOTION_MANAGEMENT_EXTEND = " 延期了 推广 #highlight# #title#";
	// 用户管理 - 推广管理 - 关闭推广
	String USER_MANAGEMENT_PROMOTION_MANAGEMENT_CLOSE = " 关闭了 推广 #highlight# #title#";
	// 用户管理 - 会员等级 - 修改会员等级
	String USER_MANAGEMENT_MEMBERSHIP_LEVEL_MODIFY = " 修改了 会员等级 #highlight#";
	// 用户管理 - 会员等级 - 关闭会员等级
	String USER_MANAGEMENT_MEMBERSHIP_LEVEL_STATE_CLOSE = " 关闭了 会员等级 #highlight#";
	String USER_MANAGEMENT_MEMBERSHIP_LEVEL_STATE_OPEN = " 开放了 会员等级 #highlight#";
	// 用户管理 - 定制管理 - 新增企业定制
	String USER_MANAGEMENT_CUSTOMIZATION_MANAGEMENT_ADD = " 新增了 企业定制 #highlight#";
	// 用户管理 - 定制管理 - 修改企业定制
	String USER_MANAGEMENT_CUSTOMIZATION_MANAGEMENT_MODIFY = " 修改了 企业定制 #highlight#";
	// 用户管理 - 机构认证管理
	String USER_MANAGEMENT_ORGANIZATION_CERTIFICATION_MANAGEMENT_APPROVE = "审核同意 组织机构认证 #highlight#";
	String USER_MANAGEMENT_ORGANIZATION_CERTIFICATION_MANAGEMENT_APPROVE_REJECT = "审核驳回了 组织机构认证 #highlight#";
	// 用户管理 - 链云用户
	String USER_MANAGEMENT_CHAIN_CLOUD_USERS_RELIEVE = "解除了 用户 #highlight# 组织机构认证";
	// 用户管理 - 恢复用户
	String USER_MANAGEMENT_CHAIN_CLOUD_USERS_RESTORE = "恢复了 用户 #highlight#";
	// 指数管理 - 砂石指数 - 新增砂石指数版本
	String INDEX_MANAGEMENT_SAND_INDEX_ADD_VERSION = " 新增了 砂石指数版本 #highlight#";
	// 指数管理 - 砂石指数 - 修改砂石指数版本
	String INDEX_MANAGEMENT_SAND_INDEX_MODIFY_VERSION = " 修改了 砂石指数版本 #highlight#";
	// 指数管理 - 砂石指数 - 删除砂石指数版本
	String INDEX_MANAGEMENT_SAND_INDEX_DELETE_VERSION = " 删除了 砂石指数版本 #highlight#";
	// 指数管理 - 砂石指数 - 新增砂石指数版本提交记录
	String INDEX_MANAGEMENT_SAND_INDEX_ADD_SUBMISSION_RECORD = " 新增了 砂石指数版本提交记录 #highlight#";
	// 指数管理 - 砂石指数 - 提交砂石指数版本提交记录
	String INDEX_MANAGEMENT_SAND_INDEX_SUBMIT_SUBMISSION_RECORD = " 提交了 砂石指数版本提交记录 #highlight#";
	// 指数管理 - 砂石指数 - 删除砂石指数版本提交记录
	String INDEX_MANAGEMENT_SAND_INDEX_DELETE_SUBMISSION_RECORD = " 删除了 砂石指数版本提交记录 #highlight#";
	// 指数管理 - 砂石指数 - 发布砂石指数版本提交记录
	String INDEX_MANAGEMENT_SAND_INDEX_PUBLISH_SUBMISSION_RECORD = " 发布了 砂石指数版本提交记录 #highlight#";
	// 指数管理 - 砂石指数 - 驳回砂石指数版本提交记录
	String INDEX_MANAGEMENT_SAND_INDEX_REJECT_SUBMISSION_RECORD = " 驳回了 砂石指数版本提交记录 #highlight#";
	// 指数管理 - 砂石指数 - 撤回砂石指数版本提交记录
	String INDEX_MANAGEMENT_SAND_INDEX_WITHDRAW_SUBMISSION_RECORD = " 撤回了 砂石指数版本提交记录 #highlight#";
	// 指数管理 - 砂石综合指数 - 新增砂石综合指数
	String INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX_ADD = " 新增了 砂石综合指数 #highlight#";
	// 指数管理 - 砂石综合指数 - 发布砂石综合指数
	String INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX_PUBLISH = " 发布了 砂石综合指数 #highlight#";
	// 指数管理 - 砂石综合指数 - 修改砂石综合指数
	String INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX_MODIFY = " 修改了 砂石综合指数 #highlight#";
	// 指数管理 - 砂石综合指数 - 删除砂石综合指数
	String INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX_DELETE = " 删除了 砂石综合指数 #highlight#";
	// 指数管理 - 砂石综合指数 - 撤回砂石综合指数
	String INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX_WITHDRAW = " 撤回了 砂石综合指数 #highlight#";
	// 指数管理 - 砂石综合指数配置 - 修改砂石综合指数配置
	String INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX_CONFIG_MODIFY = " 修改了 砂石综合指数配置 #highlight#";
	String INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX_CONFIG_ADD = " 新增了 砂石综合指数配置 #highlight#";
	String INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX_CONFIG_ENABLE = " 启用了 砂石综合指数配置 #highlight#";
	// 指数管理 - 运价指数 - 新增运价指数版本
	String INDEX_MANAGEMENT_FREIGHT_INDEX_ADD_VERSION = " 新增了 运价指数版本 #highlight#";
	// 指数管理 - 运价指数 - 修改运价指数版本
	String INDEX_MANAGEMENT_FREIGHT_INDEX_MODIFY_VERSION = " 修改了 运价指数版本 #highlight#";
	// 指数管理 - 运价指数 - 删除运价指数版本
	String INDEX_MANAGEMENT_FREIGHT_INDEX_DELETE_VERSION = " 删除了 运价指数版本 #highlight#";
	// 指数管理 - 运价指数 - 新增运价指数版本提交记录
	String INDEX_MANAGEMENT_FREIGHT_INDEX_ADD_SUBMISSION_RECORD = " 新增了 运价指数版本提交记录 #highlight#";
	// 指数管理 - 运价指数 - 提交运价指数版本提交记录
	String INDEX_MANAGEMENT_FREIGHT_INDEX_SUBMIT_SUBMISSION_RECORD = " 提交了 运价指数版本提交记录 #highlight#";
	// 指数管理 - 运价指数 - 删除运价指数版本提交记录
	String INDEX_MANAGEMENT_FREIGHT_INDEX_DELETE_SUBMISSION_RECORD = " 删除了 运价指数版本提交记录 #highlight#";
	// 指数管理 - 运价指数 - 发布运价指数版本提交记录
	String INDEX_MANAGEMENT_FREIGHT_INDEX_PUBLISH_SUBMISSION_RECORD = " 发布了 运价指数版本提交记录 #highlight#";
	// 指数管理 - 运价指数 - 驳回运价指数版本提交记录
	String INDEX_MANAGEMENT_FREIGHT_INDEX_REJECT_SUBMISSION_RECORD = " 驳回了 运价指数版本提交记录 #highlight#";
	// 指数管理 - 运价指数 - 撤回运价指数版本提交记录
	String INDEX_MANAGEMENT_FREIGHT_INDEX_WITHDRAW_SUBMISSION_RECORD = " 撤回了 运价指数版本提交记录 #highlight#";
	// 指数管理 - 运价综合指数 - 新增运价综合指数
	String INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX_ADD = " 新增了 运价综合指数 #highlight#";
	// 指数管理 - 运价综合指数 - 发布运价综合指数
	String INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX_PUBLISH = " 发布了 运价综合指数 #highlight#";
	// 指数管理 - 运价综合指数 - 修改运价综合指数
	String INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX_MODIFY = " 修改了 运价综合指数 #highlight#";
	// 指数管理 - 运价综合指数 - 删除运价综合指数
	String INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX_DELETE = " 删除了 运价综合指数 #highlight#";
	// 指数管理 - 运价综合指数 - 撤回运价综合指数
	String INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX_WITHDRAW = " 撤回了 运价综合指数 #highlight#";
	// 指数管理 - 运价综合指数配置 - 修改运价综合指数配置
	String INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX_CONFIG_MODIFY = " 修改了 运价综合指数配置 #highlight#";
	String INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX_CONFIG_ADD = " 新增了 运价综合指数配置 #highlight#";
	String INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX_CONFIG_ENABLE = " 启用了 运价综合指数配置 #highlight#";
	// 指数管理 - 油品指数 - 新增油品指数版本
	String INDEX_MANAGEMENT_OIL_INDEX_ADD_VERSION = " 新增了 油品指数版本 #highlight#";
	// 指数管理 - 油品指数 - 修改油品指数版本
	String INDEX_MANAGEMENT_OIL_INDEX_MODIFY_VERSION = " 修改了 油品指数版本 #highlight#";
	// 指数管理 - 油品指数 - 删除油品指数版本
	String INDEX_MANAGEMENT_OIL_INDEX_DELETE_VERSION = " 删除了 油品指数版本 #highlight#";
	// 指数管理 - 油品指数 - 新增油品指数版本提交记录
	String INDEX_MANAGEMENT_OIL_INDEX_ADD_SUBMISSION_RECORD = " 新增了 油品指数版本提交记录 #highlight#";
	// 指数管理 - 油品指数 - 提交油品指数版本提交记录
	String INDEX_MANAGEMENT_OIL_INDEX_SUBMIT_SUBMISSION_RECORD = " 提交了 油品指数版本提交记录 #highlight#";
	// 指数管理 - 油品指数 - 删除油品指数版本提交记录
	String INDEX_MANAGEMENT_OIL_INDEX_DELETE_SUBMISSION_RECORD = " 删除了 油品指数版本提交记录 #highlight#";
	// 指数管理 - 油品指数 - 发布油品指数版本提交记录
	String INDEX_MANAGEMENT_OIL_INDEX_PUBLISH_SUBMISSION_RECORD = " 发布了 油品指数版本提交记录 #highlight#";
	// 指数管理 - 油品指数 - 驳回油品指数版本提交记录
	String INDEX_MANAGEMENT_OIL_INDEX_REJECT_SUBMISSION_RECORD = " 驳回了 油品指数版本提交记录 #highlight#";
	// 指数管理 - 油品指数 - 撤回油品指数版本提交记录
	String INDEX_MANAGEMENT_OIL_INDEX_WITHDRAW_SUBMISSION_RECORD = " 撤回了 油品指数版本提交记录 #highlight#";
	// 指数管理 - 砂石资讯 - 新增砂石资讯
	String INDEX_MANAGEMENT_SAND_NEWS_ADD = " 新增了 砂石资讯 #highlight#";
	// 指数管理 - 砂石资讯 - 编辑砂石资讯
	String INDEX_MANAGEMENT_SAND_NEWS_EDIT = " 编辑了 砂石资讯 #highlight#";
	// 指数管理 - 砂石资讯 - 上架砂石资讯
	String INDEX_MANAGEMENT_SAND_NEWS_PUT_ON_SHELF = " 上架了 砂石资讯 #highlight#";
	String INDEX_MANAGEMENT_SAND_NEWS_PUT_OFF_SHELF = " 下架了 砂石资讯 #highlight#";
	// 指数管理 - 砂石资讯 - 删除砂石资讯
	String INDEX_MANAGEMENT_SAND_NEWS_DELETE = " 删除了 砂石资讯 #highlight#";
	// 指数管理 - 砂石学院 - 新增砂石学院
	String INDEX_MANAGEMENT_SAND_ACADEMY_ADD = " 新增了 砂石学院 #highlight#";
	// 指数管理 - 砂石学院 - 编辑砂石学院
	String INDEX_MANAGEMENT_SAND_ACADEMY_EDIT = " 编辑了 砂石学院 #highlight#";
	// 指数管理 - 砂石学院 - 上架砂石学院
	String INDEX_MANAGEMENT_SAND_ACADEMY_PUT_ON_SHELF = " 上架了 砂石学院 #highlight#";
	String INDEX_MANAGEMENT_SAND_ACADEMY_PUT_OFF_SHELF = " 下架了 砂石学院 #highlight#";
	// 指数管理 - 砂石学院 - 删除砂石学院
	String INDEX_MANAGEMENT_SAND_ACADEMY_DELETE = " 删除了 砂石学院 #highlight#";
	// 指数管理 - 砂石热点 - 新增砂石热点
	String INDEX_MANAGEMENT_SAND_HOT_ADD = " 新增了 砂石热点 #highlight#";
	// 指数管理 - 砂石热点 - 编辑砂石热点
	String INDEX_MANAGEMENT_SAND_HOT_EDIT = " 编辑了 砂石热点 #highlight#";
	// 指数管理 - 砂石热点 - 上架砂石热点
	String INDEX_MANAGEMENT_SAND_HOT_PUT_ON_SHELF = " 上架了 砂石热点 #highlight#";
	String INDEX_MANAGEMENT_SAND_HOT_PUT_OFF_SHELF = " 下架了 砂石热点 #highlight#";
	// 指数管理 - 砂石热点 - 删除砂石热点
	String INDEX_MANAGEMENT_SAND_HOT_DELETE = " 删除了 砂石热点 #highlight#";
	// 指数管理 - 航线管理 - 新增航线
	String INDEX_MANAGEMENT_SHIPPING_LANE_MANAGEMENT_ADD = " 新增了 航线 #highlight#";
	// 指数管理 - 航线管理 - 修改航线
	String INDEX_MANAGEMENT_SHIPPING_LANE_MANAGEMENT_MODIFY = " 修改了 航线 #highlight#";
	// 指数管理 - 航线管理 - 删除航线
	String INDEX_MANAGEMENT_SHIPPING_LANE_MANAGEMENT_DELETE = " 删除了 航线 #highlight#";
	// 商品管理 - 品类管理 - 新增品类
	String GOODS_MANAGEMENT_CATEGORY_MANAGEMENT_ADD = " 新增了 品类 #prd_type_name# #highlight#";
	// 商品管理 - 品类管理 - 编辑品类
	String GOODS_MANAGEMENT_CATEGORY_MANAGEMENT_EDIT = " 编辑了 品类 #prd_type_name# #highlight#";
	// 商品管理 - 品类管理 - 禁用品类
	String GOODS_MANAGEMENT_CATEGORY_MANAGEMENT_DISABLE = " 禁用了 品类 #prd_type_name# #highlight#";
	String GOODS_MANAGEMENT_CATEGORY_MANAGEMENT_ENABLE = " 启用了 品类 #prd_type_name# #highlight#";
	// 商品管理 - 品类管理 - 删除品类
	String GOODS_MANAGEMENT_CATEGORY_MANAGEMENT_DELETE = " 删除了 品类 #prd_type_name# #highlight#";
	// 商品管理 - 商品管理 - 新增商品
	String GOODS_MANAGEMENT_GOODS_MANAGEMENT_ADD = " 新增了 商品 #prd_type_name# #highlight#";
	// 商品管理 - 商品管理 - 编辑商品
	String GOODS_MANAGEMENT_GOODS_MANAGEMENT_EDIT = " 编辑了 商品 #prd_type_name# #highlight#";
	// 商品管理 - 商品管理 - 下架商品
	String GOODS_MANAGEMENT_GOODS_MANAGEMENT_TAKE_OFF_SHELF = " 下架了 商品 #prd_type_name# #highlight#";
	// 商品管理 - 商品管理 - 审核商品
	String GOODS_MANAGEMENT_GOODS_MANAGEMENT_REVIEW = " 审核了 商品 #prd_type_name# #highlight#";
	// 商品管理 - 商品管理 - 关闭商品
	String GOODS_MANAGEMENT_GOODS_MANAGEMENT_CLOSE = " 关闭了 商品 #prd_type_name# #highlight#";
	// 商品管理 - 商品管理 - 变更指派
	String GOODS_MANAGEMENT_GOODS_MANAGEMENT_CHANGE_ASSIGN = " 变更指派了 商品 #prd_type_name# #highlight#";
	// 商机管理 - 商机管理 - 新增商机
	String BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT_ADD = " 新增了 商机 #prd_type_name# #highlight#";
	// 商机管理 - 商机管理 - 编辑商机
	String BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT_EDIT = " 编辑了 商机 #prd_type_name# #highlight#";
	// 商机管理 - 商机管理 - 下架商机
	String BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT_TAKE_OFF_SHELF = " 下架了 商机 #prd_type_name# #highlight#";
	// 商机管理 - 商机管理 - 审核商机
	String BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT_AUDIT = " 审核了 商机 #prd_type_name# #highlight#";
	// 商机管理 - 商机管理 - 删除商机
	String BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT_DEL = " 删除了 商机 #prd_type_name# #highlight#";
	// 码头管理相关
	String DOCK_ADD = " 新增了 码头 #highlight#";
	String DOCK_MODIFY = " 修改了 码头 #highlight#";
	String DOCK_DELETE = " 删除了 码头 #highlight#";
	// 设备管理相关
	String EQUIPMENT_ADD = " 新增了 设备 #highlight#";
	String EQUIPMENT_EDIT = " 编辑了 设备 #highlight#";
	String EQUIPMENT_DISABLE = " 禁用了 设备 #highlight#";
	String EQUIPMENT_ENABLE = " 启用了 设备 #highlight#";
	String EQUIPMENT_DELETE = " 删除了 设备 #highlight#";
	// 设备申领相关
	String EQUIPMENT_REQUEST_PROCESS = " 处理了 设备申领 #highlight#";
	// 船主线索相关
	String SHIPOWNER_LEAD_PROCESS = " 处理了 船主线索 #highlight#";
	// 船舶管理相关
	String SHIP_ADD = " 新增了 船舶 #highlight#";
	String SHIP_MODIFY = " 修改了 船舶 #highlight#";
	String SHIP_UNBIND = " 解绑了 船舶 #highlight#";
	String SHIP_BIND = " 绑定了 船舶 #highlight#";
	String SHIP_IMPORT_DRAFT = " 导入了 船舶水尺 #highlight#";
	String SHIP_ADD_ONE_DRAFT = " 新增了 1 条 船舶水尺 #highlight#";
	String SHIP_DELETE_ONE_DRAFT = " 删除了 1 条 船舶水尺 #highlight#";
	String SHIP_IMPORT_MOBILE = " 导入 1条 手机号";
	String SHIP_ADD_DEVICE = " 新增了 船舶 #highlight#  设备 #name#";
	String SHIP_DELETE_DEVICE = " 删除了 船舶 #highlight#  设备 #name#";
	String SHIP_APPROVE_CERTIFICATION = " 审核同意 船舶认证 #highlight#";
	String SHIP_REJECT_CERTIFICATION = " 审核驳回 船舶认证 #highlight#";

	// 船运需求发布相关
	String SHIPPING_DEMAND_PUBLISH_ADD = " 新增了 船运需求发布 #highlight#";
	String SHIPPING_DEMAND_PUBLISH_CHANGE_ASSIGN = " 变更指派了 船运需求发布 #highlight#";
	String SHIPPING_DEMAND_PUBLISH_UPDATE = " 修改了 船运需求发布 #highlight#";
	String SHIPPING_DEMAND_PUBLISH_END = " 结束了 船运需求发布 #highlight#";
	String SHIPPING_DEMAND_PUBLISH_CLOSE = " 关闭了 船运需求发布 #highlight#";
	String SHIPPING_DEMAND_PUBLISH_CHECK_ORDER = " 核对（抢单）了 船运需求发布 #highlight#";
	String SHIPPING_DEMAND_PUBLISH_REJECT_ORDER = " 拒绝（抢单）了 船运需求发布 #highlight#";
	// 船运单相关
	String SHIPPING_ORDER_ADD = " 新增了 船运单 #highlight# #type#";
	String SHIPPING_ORDER_ASSIGN = " 指派了 船运单 #highlight# #type#";
	String SHIPPING_ORDER_CLOSE = " 关闭了 船运单 #highlight#";
	String SHIPPING_ORDER_START_LOADING = " 开始装货了 船运单 #highlight#";
	String SHIPPING_ORDER_APPLY_SAILING = " 发航申请了 船运单 #highlight#";
	String SHIPPING_ORDER_COMPLETE_UNLOADING = " 完成卸货了 船运单 #highlight#";
	String SHIPPING_ORDER_COMPLETE = " 完成了 船运单 #highlight#";
	String SHIPPING_ORDER_AGREE_UNLOAD = " 同意卸货 船运单 #highlight# #type#";
	String SHIPPING_ORDER_CONFIRM_DEPARTURE = " 发航确认 船运单 #highlight# #type#";
	String SHIPPING_ORDER_CONFIRM_ARRIVAL_PORT = " 到港确认 船运单 #highlight# #type#";
	String SHIPPING_ORDER_UPLOAD_DRAINAGE_VIDEO = " 上传排水视频 船运单 #highlight# #type#";
	String UPLOAD_THE_DEPARTURE_INFORMATION = " 上传发船信息 船运单 #highlight# #type#";
	String UPLOAD_UNLOADING_INFORMATION = " 上传卸货信息 船运单 #highlight# #type#";
	String PAY_THE_INFORMATION_SERVICE_FEE = " 支付#userType#信息服务费 船运单 #highlight# #type#";
	String PAY_THE_DEPOSIT = "  支付#payType# 船运单 #highlight# #type#";
	String UPDATE_THE_DEPOSIT = "  修改#payType# 船运单 #highlight# #type#";
	String CANCEL_THE_DEPOSIT = "  取消支付#payType# 船运单 #highlight# #type#";
	String CONFIRM_THE_DEPOSIT = "  确认#payType# 船运单 #highlight# #type#";




	// 监控预警相关
	String MONITORING_ALERT_ASSIGN = " 指派了 监控预警记录 #highlight#";
	String MONITORING_ALERT_PROCESS = " 处理了 监控预警记录 #highlight#";
	// 模型数据列表相关
	String MODEL_DATA_MARK = " 标记了 模型数据 #highlight#";
	String MODEL_DATA_DELETE = " 删除了 模型数据 #highlight#";
	String MODEL_DATA_PUBLISH = " 发布了 模型数据";
	// 识别记录列表相关
	String RECOGNITION_RECORD_ADD = " 加入了 模型数据 #highlight#";
	// 平台银行账号相关
	String PLATFORM_BANK_ACCOUNT_ADD = " 新增了 平台银行账号 #highlight#";
	String PLATFORM_BANK_ACCOUNT_EDIT = " 编辑了 平台银行账号 #highlight#";
	String PLATFORM_BANK_ACCOUNT_ENABLE = " 启用了 平台银行账号 #highlight#";
	String PLATFORM_BANK_ACCOUNT_DISABLE = " 禁用了 平台银行账号 #highlight#";
	// 船务信息服务费相关
	String SHIPPING_SERVICE_FEE_CONFIRM = "确认了船务信息服务费 #highlight#";
	// 组织架构相关
	String ORGANIZATION_EDIT = "编辑了 组织架构 #name# #highlight#";
	// 后台用户相关
	String BACKEND_USER_ADD = "新增了 后台用户 #name# #highlight#";
	String BACKEND_USER_EDIT = "编辑了 后台用户 #name# #highlight#";
	String BACKEND_USER_DELETE = "删除了 后台用户 #name# #highlight#";
	// 角色管理相关
	String ROLE_ADD = "新增了 角色 #highlight#";
	String ROLE_EDIT = "编辑了 角色 #highlight#";
	String ROLE_DELETE = "删除了 角色 #highlight#";
	// 小程序广告相关
	String APP_AD_MODIFY = "修改了 移动端广告 #highlight#";
	String APP_AD_DELETE = "删除了 移动端广告 #highlight#";
	// PC 端广告相关
	String PC_AD_MODIFY = "修改了 PC 端广告 #highlight#";
	String PC_AD_RESET = "重置了 PC 端广告 #highlight#";
	// 广告页相关
	String AD_PAGE_ADD = " 新增了 广告页 #highlight#";
	String AD_PAGE_MODIFY = " 修改了 广告页 #highlight#";
	String AD_PAGE_DELETE = " 删除了 广告页 #highlight#";
	// 标签管理相关
	String TAG_ADD = " 新增了 标签 #highlight#";
	String TAG_MODIFY = " 修改了 标签 #highlight#";
	String TAG_DELETE = " 删除了 标签 #highlight#";
	// 配置管理相关
	String CONFIG_MODIFY = "修改了 配置 #highlight#";
	// 关于我们相关
	String ABOUT_US_MODIFY = "修改了 关于我们 #highlight#";
	// 用户协议相关
	String USER_AGREEMENT_MODIFY = "修改了 用户协议 #highlight#";
	// 隐私政策相关
	String PRIVACY_POLICY_MODIFY = "修改了 隐私政策 #highlight#";
	// 会员服务协议相关
	String MEMBER_SERVICE_AGREEMENT_MODIFY = "修改了 会员服务协议 #highlight#";
	// 设备申领协议相关
	String EQUIPMENT_APPLICATION_AGREEMENT_MODIFY = "修改了 设备申领协议 #highlight#";
	// 加油协议相关
	String REFUELING_AGREEMENT_MODIFY = "修改了 加油协议 #highlight#";
	// 船运合同服务协议相关
	String SHIPPING_CONTRACT_SERVICE_AGREEMENT_MODIFY = "修改了 船运合同服务协议 #highlight#";
	// 平台管理 - 留言反馈 - 留言回复
	String PLATFORM_MANAGEMENT_MESSAGE_FEEDBACK_REPLY = "回复了 留言反馈 #highlight#";

	// 平台管理 - 搜索发现
	String  PLATFORM_MANAGEMENT_SEARCH_ADD = " 新增了 关键词 #highlight#";
	String PLATFORM_MANAGEMENT_SEARCH_MODIFY = " 修改了 关键词 #highlight#";
	String PLATFORM_MANAGEMENT_SEARCH_DELETE = " 删除了 关键词 #highlight#";

	// 平台管理 - 帮助中心 - 新增帮助中心
	String PLATFORM_MANAGEMENT_HELP_CENTER_ADD = " 新增了 帮助中心 #highlight#";
	// 平台管理 - 帮助中心 - 编辑帮助中心
	String PLATFORM_MANAGEMENT_HELP_CENTER_EDIT = " 编辑了 帮助中心 #highlight#";
	// 平台管理 - 帮助中心 - 上架帮助中心
	String PLATFORM_MANAGEMENT_HELP_CENTER_PUT_ON_SHELF = " 上架了 帮助中心 #highlight#";
	String PLATFORM_MANAGEMENT_HELP_CENTER_PUT_OFF_SHELF = " 下架了 帮助中心 #highlight#";
	// 平台管理 - 帮助中心 - 删除帮助中心
	String PLATFORM_MANAGEMENT_HELP_CENTER_DELETE = " 删除了 帮助中心 #highlight#";

	// 平台管理 - 功能反馈 - 处理
	String PLATFORM_MANAGEMENT_FEATURES_FEEDBACK_PROCESS = " 处理了 功能反馈 #highlight#";

	// 智能助理 - AI热词管理 - 新增AI热词
	String ASSISTANTS_HOT_WORD_ADD = " 新增了 AI热词";
	String ASSISTANTS_HOT_WORD_DELETE = "  删除了 AI热词 #highlight#";
	String ASSISTANTS_HOT_WORD_SYNCHRONIZE = "  同步了 AI热词";

	// 应用管理 - 新增应用管理
	String APPLICATION_MANAGEMENT_ADD = " 新增了 应用管理 #highlight#";
	// 应用管理 - 编辑应用管理
	String APPLICATION_MANAGEMENT_EDIT = " 编辑了 应用管理 #highlight#";
	// 应用管理 - 删除应用管理
	String APPLICATION_MANAGEMENT_DELETE = " 删除了 应用管理 #highlight#";
	// AI语料
	String AI_CORPUS_ADD = " 新增了 AI语料发布";
	String AI_CORPUS_REVERT = " 撤回了 AI语料发布";
	String AI_CORPUS_CORRECT = " 修正了 AI语料库";

	// app版本管理
	String APP_VERSION_LIAN_YUN_ADD = " 新增了 志豪链云 APP版本 #highlight#";
	String APP_VERSION_LIAN_YUN_EDIT = " 修改了 志豪链云 APP版本 #highlight#";
	String APP_VERSION_LIAN_YUN_DELETE = " 删除了 志豪链云 APP版本 #highlight#";

	String APP_VERSION_SHIP_ADD = " 新增了 链云船务 APP版本 #highlight#";
	String APP_VERSION_SHIP_EDIT = " 修改了 链云船务 APP版本 #highlight#";
	String APP_VERSION_SHIP_DELETE = " 删除了 链云船务 APP版本 #highlight#";

	// 应用管理 - 回调记录 - 重新回调
	String APPLICATION_CALLBACK_RECORD_RE = " 重新回调了 应用名称 #highlight#";

	// 平台管理-广告页管理-弹窗管理
	String PLATFORM_MANAGEMENT_POPUP_ADD = " 新增了 弹窗 #highlight#";
	String PLATFORM_MANAGEMENT_POPUP_UPDATE = " 修改了 弹窗 #highlight#";
	String PLATFORM_MANAGEMENT_POPUP_ENABLE = " 启用了 弹窗 #highlight#";
	String PLATFORM_MANAGEMENT_POPUP_DISABLE = " 禁用了 弹窗 #highlight#";
	String PLATFORM_MANAGEMENT_POPUP_DELETE = " 删除了 弹窗 #highlight#";

	// 油品站点相关
	String OIL_SITE_ADD = " 新增了 站点 #highlight#";
	String OIL_SITE_EDIT = " 修改了 站点 #highlight#";
	String OIL_SITE_DELETE = " 删除了 站点 #highlight#";
	String OIL_SITE_DISABLE = " 禁用了 站点 #highlight#";
	String OIL_SITE_ENABLE = " 启用了 站点 #highlight#";

	// 油品费用相关
	String OIL_FEE_CONFIRM = "确认了 油品费用 #highlight#";

	// 油品订单相关
	String OIL_ORDER_ADD = " 新增了 油品订单 #highlight#";
	String OIL_ORDER_CHECK = " 核对了 油品订单 #highlight#";
	String OIL_ORDER_PLAN = " 报计划 油品订单 #highlight#";
	String OIL_ORDER_CONFIRM = " 确认加油 油品订单 #highlight#";
	String OIL_ORDER_COMPLETE = " 完成加油 油品订单 #highlight#";
	String OIL_ORDER_DELETE = " 删除了 油品订单 #highlight#";
	String OIL_ORDER_CANCEL = " 取消了 油品订单 #highlight#";


	/**
	 * 功能模块
	 */
	// 用户管理 - 链云用户
	String USER_MANAGEMENT_CHAIN_CLOUD_USERS = "1";
	// 用户管理 - 充值列表
	String USER_MANAGEMENT_RECHARGE_LIST = "2";
	// 用户管理 - 激活码
	String USER_MANAGEMENT_ACTIVATION_CODE = "3";
	// 用户管理 - 推广管理
	String USER_MANAGEMENT_PROMOTION_MANAGEMENT = "4";
	// 用户管理 - 会员等级
	String USER_MANAGEMENT_MEMBERSHIP_LEVEL = "5";
	// 用户管理 - 定制管理
	String USER_MANAGEMENT_CUSTOMIZATION_MANAGEMENT = "6";
	// 指数管理 - 砂石指数
	String INDEX_MANAGEMENT_SAND_INDEX = "7";
	// 指数管理 - 砂石综合指数
	String INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX = "8";
	// 指数管理 - 砂石综合指数配置
	String INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX_CONFIG = "9";
	// 指数管理 - 运价指数
	String INDEX_MANAGEMENT_FREIGHT_INDEX = "10";
	// 指数管理 - 运价综合指数
	String INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX = "11";
	// 指数管理 - 运价综合指数配置
	String INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX_CONFIG = "12";
	// 指数管理 - 砂石资讯
	String INDEX_MANAGEMENT_SAND_NEWS = "13";
	// 指数管理 - 航线管理
	String INDEX_MANAGEMENT_SHIPPING_LANE_MANAGEMENT = "14";
	// 商品管理 - 品类管理
	String GOODS_MANAGEMENT_CATEGORY_MANAGEMENT = "15";
	// 商品管理 - 商品管理
	String GOODS_MANAGEMENT_GOODS_MANAGEMENT = "16";
	// 物流管理 - 码头管理
	String LOGISTICS_MANAGEMENT_DOCK_MANAGEMENT = "20";
	// 物流管理 - 设备管理 - 设备管理
	String LOGISTICS_MANAGEMENT_EQUIPMENT_MANAGEMENT = "21";
	// 物流管理 - 设备管理 - 设备申领
	String LOGISTICS_MANAGEMENT_EQUIPMENT_REQUEST = "22";
	// 物流管理 - 船主线索
	String LOGISTICS_MANAGEMENT_SHIPOWNER_LEAD = "23";
	// 物流管理 - 船舶管理 - 船舶信息
	String LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO = "24";
	// 物流管理 - 船舶管理 - 认证审核
	String LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_CERTIFICATION = "25";
	// 物流管理 - 船运需求 - 船运需求发布
	String LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_PUBLISH = "27";
	// 物流管理 - 船运需求 - 船运单
	String LOGISTICS_MANAGEMENT_SHIPPING_ORDER = "28";
	// 物流管理 - 监控 - 监控预警
	String LOGISTICS_MANAGEMENT_MONITORING_ALERT = "29";
	// 物流管理 - 监控 - 模型数据列表
	String LOGISTICS_MANAGEMENT_MODEL_DATA_LIST = "30";
	// 物流管理 - 监控 - 识别记录列表
	String LOGISTICS_MANAGEMENT_RECOGNITION_RECORD_LIST = "31";
	// 财税管理 - 平台银行账号
	String FINANCE_MANAGEMENT_PLATFORM_BANK_ACCOUNT = "32";
	// 财税管理 - 船务交易 - 船务信息服务费
	String FINANCE_MANAGEMENT_SHIPPING_TRANSACTION_SERVICE_FEE = "36";
	// 平台管理 - 组织用户 - 组织架构
	String PLATFORM_MANAGEMENT_ORGANIZATION_USER_ORGANIZATION = "37";
	// 平台管理 - 组织用户 - 后台用户
	String PLATFORM_MANAGEMENT_ORGANIZATION_USER_BACKEND_USER = "38";
	// 平台管理 - 组织用户 - 角色管理
	String PLATFORM_MANAGEMENT_ORGANIZATION_USER_ROLE_MANAGEMENT = "39";
	// 平台管理 - 广告管理 - 小程序
	String PLATFORM_MANAGEMENT_ADVERTISING_MANAGEMENT_APP = "40";
	// 平台管理 - 广告管理 - PC 端
	String PLATFORM_MANAGEMENT_ADVERTISING_MANAGEMENT_PC = "41";
	// 平台管理 - 广告页管理 - 广告页列表
	String PLATFORM_MANAGEMENT_ADVERTISING_PAGE_MANAGEMENT = "42";
	// 平台管理 - 标签管理
	String PLATFORM_MANAGEMENT_TAG_MANAGEMENT = "43";
	// 平台管理 - 配置管理
	String PLATFORM_MANAGEMENT_CONFIGURATION_MANAGEMENT = "44";
	// 平台管理 - 关于链云 - 关于我们
	String PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_ABOUT_US = "45";
	// 平台管理 - 关于链云 - 用户协议
	String PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_USER_AGREEMENT = "46";
	// 平台管理 - 关于链云 - 隐私政策
	String PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_PRIVACY_POLICY = "47";
	// 平台管理 - 关于链云 - 会员服务协议
	String PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_MEMBER_SERVICE_AGREEMENT = "48";
	// 平台管理 - 关于链云 - 设备申领协议
	String PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_EQUIPMENT_APPLICATION_AGREEMENT = "49";
	// 指数管理 - 运价指数版本提交记录
	String INDEX_MANAGEMENT_FREIGHT_INDEX_RECORD = "55";
	// 指数管理 - 砂石指数版本提交记录
	String INDEX_MANAGEMENT_SAND_INDEX_RECORD = "56";
	// 指数管理 - 砂石学院
	String INDEX_MANAGEMENT_SAND_ACADEMY = "57";
	// 用户管理 - 机构认证管理
	String USER_MANAGEMENT_ORGANIZATION_CERTIFICATION_MANAGEMENT = "58";
	// 平台管理 - 留言反馈
	String PLATFORM_MANAGEMENT_MESSAGE_FEEDBACK = "59";
	// 平台管理 - 搜索发现
	String PLATFORM_MANAGEMENT_SEARCH = "60";
	// 平台管理 - 帮助中心
	String PLATFORM_MANAGEMENT_HELP_CENTER = "61";
	// 平台管理 - 功能反馈
	String PLATFORM_MANAGEMENT_FEATURES_FEEDBACK = "62";
	// 智能助理 - AI热词管理
	String ASSISTANTS_HOT_WORD = "63";
	// 应用管理
	String APPLICATION_MANAGEMENT = "64";
	// 智能助理-AI语料库-AI语料发布
	String ASSISTANTS_AI_CORPUS_PUBLISH = "65";
	// 智能助理-AI语料库-AI语料库
	String ASSISTANTS_AI_CORPUS_LIBRARY = "66";
	// 平台管理-app版本管理
	String PLATFORM_MANAGEMENT_APP_VERSION = "67";
	// 应用管理-回调记录
	String APPLICATION_CALLBACK_RECORD = "68";
	// 平台管理-广告页管理-弹窗管理
	String PLATFORM_MANAGEMENT_POPUP = "69";
	// 物流管理-站点管理
	String OIL_SITE_MANAGEMENT = "70";
	// 财税管理 - 船务交易 - 油品费用
	String FINANCE_MANAGEMENT_SHIPPING_TRANSACTION_OIL_FEE = "71";
	// 平台管理 - 关于链云 - 加油协议
	String PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_REFUELING_AGREEMENT = "72";
	// 物流管理 - 油品订单
	String LOGISTICS_MANAGEMENT_OIL_ORDER = "73";
	// 指数管理 - 油品指数版本提交记录
	String INDEX_MANAGEMENT_OIL_INDEX_RECORD = "74";
	// 指数管理 - 油品指数
	String INDEX_MANAGEMENT_OIL_INDEX = "75";
	// 平台管理 - 关于链云 - 船运合同服务协议
	String PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_SHIPPING_CONTRACT_SERVICE_AGREEMENT = "76";
	// 指数管理 - 砂石热点
	String INDEX_MANAGEMENT_SAND_HOT = "77";
	// 商机管理-商机管理
	String BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT = "78";
	// 商品管理 - 寄售商品管理
	String GOODS_MANAGEMENT_PRODUCT_CONSIGNMENT_MANAGEMENT = "79";



	@Getter
	enum ServiceType {
		// 用户管理 - 链云用户
		USER_MANAGEMENT_CHAIN_CLOUD_USERS("1", "CustomerServiceImpl"),
		// 用户管理 - 充值列表
		USER_MANAGEMENT_RECHARGE_LIST("2", "ActivationCodeServiceImpl"),
		// 用户管理 - 激活码
		USER_MANAGEMENT_ACTIVATION_CODE("3", "ActivationCodeServiceImpl"),
		// 用户管理 - 推广管理
		USER_MANAGEMENT_PROMOTION_MANAGEMENT("4", "PromotionServiceImpl"),
		// 用户管理 - 会员等级
		USER_MANAGEMENT_MEMBERSHIP_LEVEL("5", "MembershipLevelServiceImpl"),
		// 用户管理 - 定制管理
		USER_MANAGEMENT_CUSTOMIZATION_MANAGEMENT("6", "EnterpriseCustomServiceImpl"),
		// 指数管理 - 砂石指数
		INDEX_MANAGEMENT_SAND_INDEX("7", "ProductTypeIndexVersionServiceImpl"),
		// 指数管理 - 砂石综合指数
		INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX("8", "CompositeIndexServiceImpl"),
		// 指数管理 - 砂石综合指数配置
		INDEX_MANAGEMENT_SAND_COMPREHENSIVE_INDEX_CONFIG("9", "CompositeIndexConfigServiceImpl,CompositeIndexConfigVersionServiceImpl"),
		// 指数管理 - 运价指数
		INDEX_MANAGEMENT_FREIGHT_INDEX("10", "ShippingPriceIndexVersionServiceImpl"),
		// 指数管理 - 运价综合指数
		INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX("11", "ShippingCompositeIndexServiceImpl"),
		// 指数管理 - 运价综合指数配置
		INDEX_MANAGEMENT_FREIGHT_COMPREHENSIVE_INDEX_CONFIG("11", "ShippingCompositeIndexConfigServiceImpl,ShippingCompositeIndexConfigVersionServiceImpl"),
		// 指数管理 - 砂石资讯
		INDEX_MANAGEMENT_SAND_NEWS("13", "InformationServiceImpl"),
		// 指数管理 - 航线管理
		INDEX_MANAGEMENT_SHIPPING_LANE_MANAGEMENT("14", "ShipRouteServiceImpl"),
		// 商品管理 - 品类管理
		GOODS_MANAGEMENT_CATEGORY_MANAGEMENT("15", "ProductTypeServiceImpl"),
		// 商品管理 - 商品管理
		GOODS_MANAGEMENT_GOODS_MANAGEMENT("16", "ProductServiceImpl"),

		// 物流管理 - 码头管理
		LOGISTICS_MANAGEMENT_DOCK_MANAGEMENT("20", "PortServiceImpl"),
		// 物流管理 - 设备管理 - 设备管理
		LOGISTICS_MANAGEMENT_EQUIPMENT_MANAGEMENT("21", "DeviceServiceImpl"),
		// 物流管理 - 设备管理 - 设备申领
		LOGISTICS_MANAGEMENT_EQUIPMENT_REQUEST("22", "ClaimServiceImpl"),
		// 物流管理 - 船主线索
		LOGISTICS_MANAGEMENT_SHIPOWNER_LEAD("23", "ShippingOwnerCluesServiceImpl"),
		// 物流管理 - 船舶管理 - 船舶信息
		LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO("24", "ShipServiceImpl"),
		// 物流管理 - 船舶管理 - 认证审核
		LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_CERTIFICATION("25", "ShipApplyServiceImpl"),
		// 物流管理 - 船运需求 - 船运需求发布
		LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_PUBLISH("27", "ShippingRequirementAcceptServiceImpl,ShippingRequirementPlatServiceImpl"),
		// 物流管理 - 船运需求 - 船运单
		LOGISTICS_MANAGEMENT_SHIPPING_ORDER("28", "TransportOrderShipServiceImpl"),
		// 物流管理 - 监控 - 监控预警
		LOGISTICS_MANAGEMENT_MONITORING_ALERT("29", "MonitorEarlyWarnRecordServiceImpl"),
		// 物流管理 - 监控 - 模型数据列表
		LOGISTICS_MANAGEMENT_MODEL_DATA_LIST("30", "DeviceCapturePicDatasetServiceImpl"),
		// 物流管理 - 监控 - 识别记录列表
		LOGISTICS_MANAGEMENT_RECOGNITION_RECORD_LIST("31", "DeviceCapturePicRecServiceImpl"),
		// 财税管理 - 平台银行账号
		FINANCE_MANAGEMENT_PLATFORM_BANK_ACCOUNT("32", "PlatformBankAccountServiceImpl"),

		// 财税管理 - 船务交易 - 船务信息服务费
		FINANCE_MANAGEMENT_SHIPPING_TRANSACTION_SERVICE_FEE("36", "ShipInfoServiceFeeServiceImpl"),
		// 平台管理 - 组织用户 - 组织架构
		PLATFORM_MANAGEMENT_ORGANIZATION_USER_ORGANIZATION("37", "PersonServiceImpl"),
		// 平台管理 - 组织用户 - 后台用户
		PLATFORM_MANAGEMENT_ORGANIZATION_USER_BACKEND_USER("38", "UserServiceImpl"),
		// 平台管理 - 组织用户 - 角色管理
		PLATFORM_MANAGEMENT_ORGANIZATION_USER_ROLE_MANAGEMENT("39", "RoleServiceImpl"),
		// 平台管理 - 广告管理 - 小程序
		PLATFORM_MANAGEMENT_ADVERTISING_MANAGEMENT_APP("40", "BannerServiceImpl"),
		// 平台管理 - 广告管理 - PC 端
		PLATFORM_MANAGEMENT_ADVERTISING_MANAGEMENT_PC("41", "AdvertisementPositionServiceImpl"),
		// 平台管理 - 广告页管理 - 广告页列表
		PLATFORM_MANAGEMENT_ADVERTISING_PAGE_MANAGEMENT("42", "AdvertServiceImpl"),
		// 平台管理 - 标签管理
		PLATFORM_MANAGEMENT_TAG_MANAGEMENT("43", "TagServiceImpl"),
		// 平台管理 - 配置管理
		PLATFORM_MANAGEMENT_CONFIGURATION_MANAGEMENT("44", "BusinessConfigServiceImpl"),
		// 平台管理 - 关于链云 - 关于我们
		PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_ABOUT_US("45", "ArticleServiceImpl"),
		// 平台管理 - 关于链云 - 用户协议
		PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_USER_AGREEMENT("46", "ArticleServiceImpl"),
		// 平台管理 - 关于链云 - 隐私政策
		PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_PRIVACY_POLICY("47", "ArticleServiceImpl"),
		// 平台管理 - 关于链云 - 会员服务协议
		PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_MEMBER_SERVICE_AGREEMENT("48", "ArticleServiceImpl"),
		// 平台管理 - 关于链云 - 设备申领协议
		PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_EQUIPMENT_APPLICATION_AGREEMENT("49", "ArticleServiceImpl"),

		// 指数管理 - 运价指数版本提交记录
		INDEX_MANAGEMENT_FREIGHT_INDEX_RECORD("55", "ShippingPriceIndexVersionRecordServiceImpl"),
		// 指数管理 - 砂石指数版本提交记录
		INDEX_MANAGEMENT_SAND_INDEX_RECORD("56", "ProductTypeIndexVersionRecordServiceImpl"),
		// 指数管理 - 砂石学院
		INDEX_MANAGEMENT_SAND_ACADEMY("57", "SandAcademyServiceImpl"),
		// 用户管理 - 机构认证管理
		USER_MANAGEMENT_ORGANIZATION_CERTIFICATION_MANAGEMENT("58", "InstitutionApplyServiceImpl"),
		// 平台管理 - 留言反馈
		PLATFORM_MANAGEMENT_MESSAGE_FEEDBACK("59","MessageFeedbackServiceImpl"),
		// 平台管理 - 搜索发现
		PLATFORM_MANAGEMENT_SEARCH("60","SearchServiceImpl"),
		// 平台管理 - 帮助中心
		PLATFORM_MANAGEMENT_HELP_CENTER("61","HelpCenterServiceImpl"),
		// 平台管理 - 功能反馈
		PLATFORM_MANAGEMENT_FEATURES_FEEDBACK("62","FeaturesFeedbackServiceImpl"),
		// 智能助理 - AI热词管理
		ASSISTANTS_HOT_WORD("63","HotWordServiceImpl"),
		// 应用管理
		APPLICATION_MANAGEMENT("64","ApplicationServiceImpl"),
		// 智能助理-AI语料库-AI语料发布
		ASSISTANTS_AI_CORPUS_PUBLISH("65","CorpusPublishServiceImpl"),
		// 智能助理-AI语料库-AI语料库
		ASSISTANTS_AI_CORPUS_LIBRARY("66","CorpusLibraryServiceImpl"),
		// 平台管理 - App版本管理
		PLATFORM_MANAGEMENT_APP_VERSION("67","AppVersionServiceImpl"),
		// 应用管理-回调记录
		APPLICATION_CALLBACK_RECORD("68","ApplicationPushRecordServiceImpl"),
		// 平台管理-广告页管理-弹窗广告
		PLATFORM_MANAGEMENT_POPUP("69","PopUpAdvertServiceImpl"),
		// 物流管理-站点管理
		OIL_SITE_MANAGEMENT("70","OilSiteServiceImpl"),
		// 财税管理-船务交易-油品费用
		FINANCE_MANAGEMENT_SHIPPING_TRANSACTION_OIL_FEE("71","OilFeeServiceImpl"),
		// 平台管理 - 关于链云 - 加油协议
		PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_REFUELING_AGREEMENT("72", "ArticleServiceImpl"),
		// 物流管理 - 油品订单
		LOGISTICS_MANAGEMENT_OIL_ORDER("73", "OilOrderSnapshotServiceImpl"),
		// 指数管理 - 油品指数版本提交记录
		INDEX_MANAGEMENT_OIL_INDEX_RECORD("74", "OilIndexVersionRecordServiceImpl"),
		// 指数管理 - 油品指数
		INDEX_MANAGEMENT_OIL_INDEX("75", "OilIndexVersionServiceImpl"),
		// 平台管理 - 关于链云 - 船运合同服务协议
		PLATFORM_MANAGEMENT_ABOUT_CHAIN_CLOUD_SHIPPING_CONTRACT_SERVICE_AGREEMENT("76", "ArticleServiceImpl"),
		// 指数管理 - 砂石热点
		INDEX_MANAGEMENT_SAND_HOT("77", "SandHotServiceImpl"),
		// 商机管理-商机管理
		BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT("78", "BusinessOpportunityServiceImpl"),
		// 商品管理 - 寄售商品管理
		GOODS_MANAGEMENT_PRODUCT_CONSIGNMENT_MANAGEMENT("79", "ProductConsignmentServiceImpl"),
		;

		private final String key;
		private final String value;

		private static final Map<String, ServiceType> MAPPING;

		static {
			{
				Map<String, ServiceType> mapping = new HashMap<>();
				for (ServiceType value : ServiceType.values()) {
					mapping.put(value.getKey(), value);
				}
				MAPPING = mapping;
			}
		}

		ServiceType(String key, String value) {
			this.key = key;
			this.value = value;
		}

		public static ServiceType from(final String key) {
			return MAPPING.get(key);
		}

		public boolean match(final String key) {
			return this.key.equals(key);
		}
	}
}
