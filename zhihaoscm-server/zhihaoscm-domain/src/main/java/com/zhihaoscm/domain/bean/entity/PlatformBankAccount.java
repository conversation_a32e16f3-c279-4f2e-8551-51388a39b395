package com.zhihaoscm.domain.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.common.bean.json.ArrayLong;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 平台银行账户
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Getter
@Setter
@TableName("t_platform_bank_account")
public class PlatformBankAccount extends BaseEntityWithLongId {

	/**
	 * 开户名称
	 */
	private String name;

	/**
	 * 银行账户
	 */
	private String account;

	/**
	 * 开户行
	 */
	private String openingBank;

	/**
	 * 使用类型 {@link com.zhihaoscm.domain.meta.biz.PlatformBankAccountDef.UseType}
	 */
	private ArrayLong useType;

	/**
	 * 状态 1 启用 0 禁用 {@link com.zhihaoscm.common.bean.constants.CommonDef.Symbol}
	 */
	private Integer state;
}
