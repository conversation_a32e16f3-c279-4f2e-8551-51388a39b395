package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

public interface BusinessOpportunityDef {

	/**
	 * 类型
	 */
	@Getter
	enum Type {

		BUYING_INFORMATION(1, "求购信息"),

		FIND_MARKET(2, "找销路"),

		SEEK_COOPERATION(3, "寻求合作"),

		OTHER(4, "其他信息");

		private final Integer code;

		private final String name;

		private static final Map<Integer, Type> MAPPING;

		static {
			{
				Map<Integer, Type> mapping = new HashMap<>();
				for (Type value : Type.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static Type from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Type(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 原因
	 */
	@Getter
	enum Reason {

		DUPLICATE_RELEASES(1, "重复发布"),

		DISINFORMATION(2, "虚假信息"),

		VIOLATING_CONTENT(3, "违规内容"),

		UNRELATED_INFORMATION_RELEASE(4, "无关信息发布"),

		OTHER(5, "其他情况"),

		USER_ACTIONS(6, "用户操作");

		private final Integer code;

		private final String name;

		private static final Map<Integer, Reason> MAPPING;

		static {
			{
				Map<Integer, Reason> mapping = new HashMap<>();
				for (Reason value : Reason.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static Reason from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Reason(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 商机标签
	 */
	@Getter
	enum OpportunityLabel {

		SELECTIVE(1, "精选"),

		POPULAR(2, "热门"),

		EMERGENCY(3, "紧急");

		private final Integer code;

		private final String name;

		private static final Map<Integer, OpportunityLabel> MAPPING;

		static {
			{
				Map<Integer, OpportunityLabel> mapping = new HashMap<>();
				for (OpportunityLabel value : OpportunityLabel.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static OpportunityLabel from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		OpportunityLabel(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 状态
	 */
	@Getter
	enum State {

		TO_BE_REVIEWED(1, "待审核"),

		ON_SHELF(2, "上架中"),

		FAILED(3, "未通过"),

		OFF_SHELF(4, "已下架");

		private final Integer code;

		private final String name;

		private static final Map<Integer, State> MAPPING;

		static {
			{
				Map<Integer, State> mapping = new HashMap<>();
				for (State value : State.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static State from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		State(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}
}
