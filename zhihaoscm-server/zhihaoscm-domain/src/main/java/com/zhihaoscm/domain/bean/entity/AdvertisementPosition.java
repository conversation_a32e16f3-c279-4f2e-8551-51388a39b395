package com.zhihaoscm.domain.bean.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.domain.bean.json.ArrayAdvertisement;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 广告位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@Getter
@Setter
@TableName("t_advertisement_position")
public class AdvertisementPosition extends BaseEntityWithLongId {

	/**
	 * 广告位位置类型{@link com.zhihaoscm.domain.meta.biz.AdvertisementPositionDef.PositionType}
	 */
	private Integer positionType;

	/**
	 * 广告位(1,2,3){@link com.zhihaoscm.domain.meta.biz.AdvertisementPositionDef.Position}
	 */
	private Integer position;

	/**
	 * 长
	 */
	private Integer length;

	/**
	 * 宽
	 */
	private Integer width;

	/**
	 * 展现形式
	 * {@link com.zhihaoscm.domain.meta.biz.AdvertisementPositionDef.DisplayForm}
	 */
	private Integer displayForm;

	/**
	 * 轮播间隔
	 * {@link com.zhihaoscm.domain.meta.biz.AdvertisementPositionDef.RotationInterval}
	 */
	private Integer timeInterval;

	/**
	 * 开始时间
	 */
	private LocalDateTime beginTime;

	/**
	 * 结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 广告
	 */
	private ArrayAdvertisement content;
}
