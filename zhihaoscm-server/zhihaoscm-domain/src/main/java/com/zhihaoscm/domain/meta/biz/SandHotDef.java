package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

/**
 * 砂石学院定义
 */
public interface SandHotDef {

	/**
	 * 分类
	 */
	@Getter
	enum Type {

		INDUSTRY_NEWS(1, "行业快讯"),

		BAI_LU(2, "白露专题"),

		AUCTION_OF_TENEMENTS(3, "矿权拍卖"),

		POLICY_DIRECT(4, "政策直达"),

		RECENT_EXHIBITION(5, "近期展会"),

		;

		private final Integer code;

		private final String name;

		private static final Map<Integer, Type> MAPPING;

		static {
			{
				Map<Integer, Type> mapping = new HashMap<>();
				for (Type value : Type.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static Type from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Type(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}
}
