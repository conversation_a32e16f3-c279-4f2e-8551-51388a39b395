package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import lombok.Getter;

/**
 * 砂石资讯定义
 */
public interface InformationDef {

	String SOURCE = "志豪链云";

	String CJHDJ = "长江航道局";
	String DISCLAIMERS = "志豪链云发布的原创及转载内容，仅供客户参考，不作为决策建议。原创内容版权归志豪链云所有，转载须取得志豪链云书面授权，且志豪链云保留对任何侵权行为和有悖原创内容原意的引用行为进行追究的权利。志豪链云转载内容来源于公共网络，目的在于传递更多行业相关信息，方便学习与交流，并不代表志豪链云赞同其观点及对其真实性、完整性负责。申请授权及投诉，请联系志豪链云（400-0707-798）处理。";

	String DAILY_REVIEW_PREFIX = "链云砂石日评：";

	/**
	 * 资讯类型
	 */
	@Getter
	enum Type {

		IMG(1, "图片"), VIDEO(2, "视频"),;

		private final Integer code;

		private final String name;

		private static final Map<Integer, Type> MAPPING;

		static {
			{
				Map<Integer, Type> mapping = new HashMap<>();
				for (Type value : Type.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static Type from(final Integer code) {
			Type result = MAPPING.get(code);

			if (Objects.nonNull(result)) {
				return result;
			} else {
				throw new IllegalArgumentException(
						"wrong InfoType code " + code);
			}
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Type(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

	/**
	 * 资讯类别
	 */
	@Getter
	enum Category {

		ANNOUNCEMENT(1, "公告"), INFORMATION(2, "资讯");

		private final Integer code;

		private final String name;

		private static final Map<Integer, Category> MAPPING;

		static {
			{
				Map<Integer, Category> mapping = new HashMap<>();
				for (Category value : Category.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static Category from(final Integer code) {
			Category result = MAPPING.get(code);

			if (Objects.nonNull(result)) {
				return result;
			} else {
				throw new IllegalArgumentException(
						"wrong Info category code " + code);
			}
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Category(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}



	/**
	 * 分类
	 */
	@Getter
	enum Classify {

		INDUSTRY_TRENDS(1, "行业动态"),
		ANALYSIS_REPORT(2, "分析报告"),
		DAILY_REVIEW(3, "每日评述"),
		WATER_LEVEL(4, "水位信息"),
		WATERWAY_NOTICE(5, "航道通告"),
		BAI_LU(6, "白露专区");

		private final Integer code;

		private final String name;

		private static final Map<Integer, Classify> MAPPING;

		static {
			{
				Map<Integer, Classify> mapping = new HashMap<>();
				for (Classify value : Classify.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static Classify from(final Integer code) {
			Classify result = MAPPING.get(code);

			if (Objects.nonNull(result)) {
				return result;
			} else {
				throw new IllegalArgumentException(
						"wrong Info category code " + code);
			}
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		Classify(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}


	/**
	 * 资讯类别
	 */
	@Getter
	enum FeeType {

		FREE_CONTENT(1, "免费内容"), VIP_EXCLUSIVE(2, "会员专享");

		private final Integer code;

		private final String name;

		private static final Map<Integer, FeeType> MAPPING;

		static {
			{
				Map<Integer, FeeType> mapping = new HashMap<>();
				for (FeeType value : FeeType.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static FeeType from(final Integer code) {
			FeeType result = MAPPING.get(code);

			if (Objects.nonNull(result)) {
				return result;
			} else {
				throw new IllegalArgumentException(
						"wrong Info FeeType code " + code);
			}
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

		FeeType(Integer code, String name) {
			this.code = code;
			this.name = name;
		}
	}

}
