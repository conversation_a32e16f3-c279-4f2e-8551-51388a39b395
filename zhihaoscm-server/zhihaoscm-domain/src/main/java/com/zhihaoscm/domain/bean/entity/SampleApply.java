package com.zhihaoscm.domain.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.domain.bean.json.AddressInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.ProductInfo;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 申请寄样信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Getter
@Setter
@TableName("t_sample_apply")
public class SampleApply extends BaseEntityWithLongId {

	/**
	 * 商品id
	 */
	private String productId;

	/**
	 * 商品信息
	 */
	private ProductInfo productInfo;

	/**
	 * 客户id
	 */
	private Long customerId;

	/**
	 * 客户信息
	 */
	private Enterprise customerEnterprise;

	/**
	 * 地址id
	 */
	private Long addressId;

	/**
	 * 地址信息
	 */
	private AddressInfo addressInfo;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 状态(1待处理,2已寄出,3无效信息)
	 * {@link com.zhihaoscm.domain.meta.biz.SampleApplyDef.State}
	 */
	private Integer state;

	/**
	 * 物流单号
	 */
	private String logisticsNumber;

	/**
	 * 处理备注
	 */
	private String handleRemark;

	/**
	 * 处理人ID
	 */
	private Long handleId;

	/**
	 * 处理人名称
	 */
	private String handleName;

}
