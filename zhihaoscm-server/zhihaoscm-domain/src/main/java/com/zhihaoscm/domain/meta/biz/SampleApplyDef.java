package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

/**
 * 寄样定义
 */
public interface SampleApplyDef {

    /**
     * 状态
     */
    @Getter
    enum State {

        PENDING(1, "待处理"), SHIPPED(2, "已寄出"),INVALID_INFORMATION(3,"无效信息");

        private final Integer code;

        private final String name;

        private static final Map<Integer, SampleApplyDef.State> MAPPING;

        static {
            {
                Map<Integer, SampleApplyDef.State> mapping = new HashMap<>();
                for (SampleApplyDef.State value : SampleApplyDef.State.values()) {
                    mapping.put(value.getCode(), value);
                }
                MAPPING = mapping;
            }
        }

        public static SampleApplyDef.State from(final Integer code) {
            return MAPPING.get(code);
        }

        public boolean match(final Integer code) {
            return this.code.equals(code);
        }

        State(Integer code, String name) {
            this.code = code;
            this.name = name;
        }
    }

}
