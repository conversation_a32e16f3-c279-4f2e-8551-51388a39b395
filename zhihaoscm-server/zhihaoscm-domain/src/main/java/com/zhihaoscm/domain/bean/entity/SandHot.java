package com.zhihaoscm.domain.bean.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.domain.annotation.FileId;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 砂石热点
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Getter
@Setter
@TableName("t_sand_hot")
public class SandHot extends BaseEntityWithLongId {

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 分类{@link com.zhihaoscm.domain.meta.biz.SandHotDef.Type}
	 */
	private Integer type;

	/**
	 * 来源
	 */
	private String source;

	/**
	 * 发布日期
	 */
	private LocalDateTime publishDate;

	/**
	 * 图片id-只有近期展会才传
	 */
	@FileId
	private Long fileId;

	/**
	 * 内容富文本
	 */
	private String content;

	/**
	 * 免责声明
	 */
	private String disclaimers;

	/**
	 * 状态：1.上架、0.下架
	 */
	private Integer state;
}
