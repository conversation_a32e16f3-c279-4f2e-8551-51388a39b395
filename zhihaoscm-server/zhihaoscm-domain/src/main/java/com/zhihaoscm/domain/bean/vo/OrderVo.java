package com.zhihaoscm.domain.bean.vo;

import java.util.List;

import com.zhihaoscm.domain.bean.entity.Order;
import com.zhihaoscm.domain.bean.entity.OrderRemark;
import com.zhihaoscm.domain.bean.entity.ReceivePayment;

import lombok.Data;

@Data
public class OrderVo {

	/**
	 * 订单信息
	 */
	private Order order;

	/**
	 * 订单备注信息
	 */
	private List<OrderRemark> remarks;

	/**
	 * 收款信息
	 */
	private List<ReceivePayment> receivePayments;
}
