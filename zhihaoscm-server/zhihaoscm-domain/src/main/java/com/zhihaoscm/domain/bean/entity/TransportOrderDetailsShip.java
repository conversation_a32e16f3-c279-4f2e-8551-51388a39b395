package com.zhihaoscm.domain.bean.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.json.*;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 船运单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Getter
@Setter
@TableName("t_transport_order_details_ship")
public class TransportOrderDetailsShip extends BaseEntityWithStringId {

	/**
	 * 发货信息
	 */
	private DeliveryInfo deliveryInfo;

	/**
	 * 接档信息
	 */
	private ArrayGearShift jdInfo;

	/**
	 * 离岸准备信息
	 */
	private DeparturePreparationInfo lazbInfo;

	/**
	 * 是否排水
	 */
	private Integer drainage;

	/**
	 * 排水确认时间
	 */
	private LocalDateTime drainageTime;

	/**
	 * 排水视频ID
	 */
	@FileId
	private Long drainageVideoId;

	/**
	 * 货主确认发航时间
	 */
	private LocalDateTime ownerSetSailTime;

	/**
	 * 发航信息
	 */
	private SetSailInfo setSailInfo;

	/**
	 * 船主确认到港时间
	 */
	private LocalDateTime captainArrivalTime;

	/**
	 * 到港视频ID
	 */
	@FileId
	private Long arrivalVideoId;

	/**
	 * 货主确认卸货时间
	 */
	private LocalDateTime ownerUnloadingTime;

	/**
	 * 卸货信息
	 */
	private UnloadingInfo unloadingInfo;
}
