package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import org.springframework.util.Assert;

import lombok.Getter;

public interface ShippingRequirementPlatDef {

	@Getter
	enum State {
		TO_BE_PUBLISH(0, "待发布"),

		TO_BE_ALLOCATED(1, "已发布"),

		ENDED(2, "已结束"),

		CLOSED(3, "已关闭");

		private final Integer code;

		private final String desc;

		private static final Map<Integer, State> MAPPING;

		static {
			{
				Map<Integer, State> mapping = new HashMap<>();
				for (State value : State.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		State(Integer code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public static State from(final Integer code) {
			State type = MAPPING.get(code);
			Assert.notNull(type, "wrong plat state: " + code);
			return type;
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}

	@Getter
	enum OrderAcceptanceState {

		PENDING_PROCESSING(0, "待处理"),

		CONFIRMED(1, "--");

		private final Integer code;

		private final String desc;

		private static final Map<Integer, OrderAcceptanceState> MAPPING;

		static {
			{
				Map<Integer, OrderAcceptanceState> mapping = new HashMap<>();
				for (OrderAcceptanceState value : OrderAcceptanceState
						.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		OrderAcceptanceState(Integer code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public static OrderAcceptanceState from(final Integer code) {
			OrderAcceptanceState type = MAPPING.get(code);
			Assert.notNull(type, "wrong order acceptance state: " + code);
			return type;
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}

	@Getter
	enum DrainageChannel {

		NO_DRAINAGE_CHANNEL(1, "无水槽"),

		DRAINAGE_CHANNEL_FAIR(2, "水槽一般"),

		DRAINAGE_CHANNEL_GOOD(3, "水槽好");

		private final Integer code;

		private final String name;

		private static final Map<Integer, DrainageChannel> MAPPING;

		static {
			{
				Map<Integer, DrainageChannel> mapping = new HashMap<>();
				for (DrainageChannel value : DrainageChannel.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		DrainageChannel(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

		public static DrainageChannel from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}

	/**
	 * 船务小程序船运需求标签
	 */
	@Getter
	enum RouteTag {

		SMART_MATCH(1, "智能匹配"),

		FREQUENT_ROUTE(2, "常跑航线"),

		HISTORICAL_ROUTE(3, "历史航线");

		private final Integer code;

		private final String desc;

		private static final Map<Integer, RouteTag> MAPPING;

		static {
			{
				Map<Integer, RouteTag> mapping = new HashMap<>();
				for (RouteTag value : RouteTag.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		RouteTag(Integer code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public static RouteTag from(final Integer code) {
			RouteTag routeTag = MAPPING.get(code);
			Assert.notNull(routeTag, "wrong plat RouteTag: " + code);
			return routeTag;
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}

	/**
	 * 电联号码类型
	 */
	@Getter
	enum ElectricContactNumberType {

		PHONE(1, "手机号"),

		LANDLINE(2, "座机");

		private final Integer code;

		private final String name;

		private static final Map<Integer, ElectricContactNumberType> MAPPING;

		static {
			{
				Map<Integer, ElectricContactNumberType> mapping = new HashMap<>();
				for (ElectricContactNumberType value : ElectricContactNumberType
						.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		ElectricContactNumberType(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

		public static ElectricContactNumberType from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}

	/**
	 * 数据来源
	 */
	@Getter
	enum DataSource {
		INNER(1, "平台船运需求"), OUTER(2, "三方需求");

		private final Integer code;

		private final String desc;

		private static final Map<Integer, DataSource> MAPPING;

		static {
			{
				Map<Integer, DataSource> mapping = new HashMap<>();
				for (DataSource value : DataSource.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		DataSource(Integer code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public static DataSource from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}

	/**
	 * 需求等级
	 */
	@Getter
	enum DemandLevel {
		ONE(1, "1级"), TWO(2, "2级"), THREE(3, "3级"), FOUR(4, "4级"), FIVE(5,
				"5级");

		private final Integer code;

		private final String name;

		private static final Map<Integer, DemandLevel> MAPPING;

		static {
			{
				Map<Integer, DemandLevel> mapping = new HashMap<>();
				for (DemandLevel value : DemandLevel.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		DemandLevel(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

		public static DemandLevel from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}

	/**
	 * 操作平台
	 */
	@Getter
	enum OperatingPlatform {
		PC(1, "PC端"), APP(2, "移动端（小程序或APP）");

		private final Integer code;

		private final String name;

		private static final Map<Integer, OperatingPlatform> MAPPING;

		static {
			{
				Map<Integer, OperatingPlatform> mapping = new HashMap<>();
				for (OperatingPlatform value : OperatingPlatform.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		OperatingPlatform(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

		public static OperatingPlatform from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}

	}
}
