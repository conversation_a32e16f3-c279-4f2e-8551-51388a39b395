package com.zhihaoscm.domain.bean.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.json.CustomerJsonInfo;
import com.zhihaoscm.domain.meta.biz.OilFeeDef;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 油品费用
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Getter
@Setter
@TableName("t_oil_fee")
public class OilFee extends BaseEntityWithStringId {

	/**
	 * 账户id
	 */
	private Long customerId;

	/**
	 * 账户信息
	 */
	private CustomerJsonInfo customerInfo;

	/**
	 * 支付方式:1.线上/2.线下{@link OilFeeDef.PayType}
	 */
	private Integer payType;

	/**
	 * 付款凭证ids-最多5张
	 */
	@FileId
	private ArrayLong payFileIds;

	/**
	 * 支付时间
	 */
	private LocalDateTime payTime;

	/**
	 * 船舶的MMSI
	 */
	private String shipId;

	/**
	 * 船舶名称
	 */
	private String shipName;

	/**
	 * 船舶中文名
	 */
	private String shipCnName;

	/**
	 * 关联的油品订单
	 */
	private String oilOrderId;

	/**
	 * 金额
	 */
	private BigDecimal amount;

	/**
	 * 确认人id
	 */
	private Long confirmBy;

	/**
	 * 确认人名称
	 */
	private String confirmByName;

	/**
	 * 确认时间
	 */
	private LocalDateTime confirmTime;

	/**
	 * 状态:1.待确认/2.已支付{@link OilFeeDef.State}
	 */
	private Integer state;
}
