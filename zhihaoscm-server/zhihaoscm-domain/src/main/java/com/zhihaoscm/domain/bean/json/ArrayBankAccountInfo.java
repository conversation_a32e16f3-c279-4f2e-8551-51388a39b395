package com.zhihaoscm.domain.bean.json;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

public class ArrayBankAccountInfo extends ArrayList<BankAccountInfo> {
	public ArrayBankAccountInfo() {
	}

	public ArrayBankAccountInfo(List<BankAccountInfo> lists) {
		if (CollectionUtils.isNotEmpty(lists)) {
			super.addAll(lists);
		}
	}

	public ArrayBankAccountInfo(BankAccountInfo... item) {
		super.addAll(List.of(item));
	}
}
