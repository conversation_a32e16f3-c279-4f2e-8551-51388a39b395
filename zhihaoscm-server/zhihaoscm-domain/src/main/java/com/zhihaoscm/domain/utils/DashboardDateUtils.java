package com.zhihaoscm.domain.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.BeanUtils;

import com.zhihaoscm.domain.bean.dto.DashboardScopeDto;
import com.zhihaoscm.domain.meta.biz.DashboardDef;

/**
 * 大屏计算日期工具类
 */
public class DashboardDateUtils {

	/**
	 * 计算开始日期
	 *
	 * @param scope
	 * @return
	 */
	public static LocalDateTime calcBeginDate(Integer scope) {
		return switch (DashboardDef.Scope.from(scope)) {
			case TOTAL -> null;
			case MONTH -> LocalDateTime.now().with(LocalTime.MIN).minusMonths(1)
					.plusDays(1);
			case QUARTER -> LocalDateTime.now().with(LocalTime.MIN)
					.minusMonths(3).plusDays(1);
			case YEAR -> LocalDateTime.now().with(LocalTime.MIN).minusYears(1)
					.plusDays(1);
		};
	}

	/**
	 * 根据枚举返回对应的时间范围
	 *
	 * @param scope
	 * @param month
	 *            传入的月份
	 * @param year
	 *            传入的年份
	 * @return
	 */
	public static List<DashboardScopeDto> getDateScope(Integer scope,
			LocalDateTime month, LocalDateTime year) {
		if (Objects.nonNull(scope)) {
			return switch (DashboardDef.Scope.from(scope)) {
				case TOTAL -> List.of();
				case MONTH -> getDateByMonth();
				case QUARTER -> getDateByQuarter();
				case YEAR -> getDateByYear();
			};
		} else {
			if (Objects.nonNull(month)) {
				return getDateByMonth(month);
			} else if (Objects.nonNull(year)) {
				return getYearMonthRanges(year);
			}
			return List.of();
		}

	}

	/**
	 * 获取近一年的每个月的第一天和最后一天
	 *
	 * @return
	 */
	private static List<DashboardScopeDto> getDateByYear() {
		List<DashboardScopeDto> list = new ArrayList<>();
		LocalDate today = LocalDate.now();

		for (int i = 0; i < 12; i++) {
			YearMonth yearMonth = YearMonth.from(today.minusMonths(i));
			LocalDateTime firstDay = yearMonth.atDay(1).atStartOfDay();
			LocalDateTime lastDay = yearMonth.atEndOfMonth().atTime(23, 59, 59);

			list.add(new DashboardScopeDto(firstDay, lastDay));
		}
		Collections.reverse(list);
		return list;
	}

	/**
	 * 获取近一季度每个月的第一天和最后一天
	 *
	 * @return
	 */
	private static List<DashboardScopeDto> getDateByQuarter() {
		List<DashboardScopeDto> list = new ArrayList<>();
		LocalDate today = LocalDate.now();
		for (int i = 0; i < 3; i++) {
			DashboardScopeDto dto = new DashboardScopeDto();
			YearMonth yearMonth = YearMonth.from(today.minusMonths(i));
			LocalDateTime firstDay = yearMonth.atDay(1).atTime(LocalTime.MIN);
			LocalDateTime lastDay = yearMonth.atEndOfMonth()
					.atTime(LocalTime.MAX).withNano(0);
			dto.setBeginTime(firstDay);
			dto.setEndTime(lastDay);
			list.add(dto);
		}
		Collections.reverse(list);
		return list;
	}

	/**
	 * 获取近一月的查询时间
	 *
	 * @return
	 */
	private static List<DashboardScopeDto> getDateByMonth() {
		List<DashboardScopeDto> list = new ArrayList<>();
		DashboardScopeDto dashboardScopeDto = getDataTime();
		DashboardScopeDto dto = new DashboardScopeDto();
		dashboardScopeDto.setBeginTime(dashboardScopeDto.getEndTime()
				.minusDays(6).toLocalDate().atStartOfDay());
		dashboardScopeDto.setEndTime(dashboardScopeDto.getEndTime());
		BeanUtils.copyProperties(dashboardScopeDto, dto);
		list.add(dto);
		for (int i = 0; i < 3; i++) {
			dto = new DashboardScopeDto();
			dashboardScopeDto.setEndTime(dashboardScopeDto.getBeginTime()
					.minusDays(1).with(LocalTime.MAX).withNano(0));
			dashboardScopeDto.setBeginTime(dashboardScopeDto.getBeginTime()
					.minusDays(7).toLocalDate().atStartOfDay());
			BeanUtils.copyProperties(dashboardScopeDto, dto);
			list.add(dto);
		}
		// 当月只有28天
		if (getDataTime().getBeginTime()
				.isEqual(dashboardScopeDto.getBeginTime())) {
			Collections.reverse(list);
			return list;
		}
		dto = new DashboardScopeDto();
		dashboardScopeDto.setEndTime(dashboardScopeDto.getBeginTime()
				.minusDays(1).with(LocalTime.MAX).withNano(0));
		dashboardScopeDto.setBeginTime(getDataTime().getBeginTime());
		BeanUtils.copyProperties(dashboardScopeDto, dto);
		list.add(dto);
		Collections.reverse(list);
		return list;
	}

	/**
	 * 计算近一月日期
	 *
	 * @return
	 */
	private static DashboardScopeDto getDataTime() {
		DashboardScopeDto dto = new DashboardScopeDto();
		dto.setBeginTime(LocalDateTime.now().with(LocalTime.MIN).minusMonths(1)
				.plusDays(1));
		dto.setEndTime(LocalDateTime.now().with(LocalTime.MAX).withNano(0));
		return dto;
	}

	/**
	 * 获取选择月的查询时间
	 *
	 * @return
	 */
	private static List<DashboardScopeDto> getDateByMonth(
			LocalDateTime analysisMonth) {
		List<DashboardScopeDto> list = new ArrayList<>();
		DashboardScopeDto dashboardScopeDto = new DashboardScopeDto();
		DashboardScopeDto dto = new DashboardScopeDto();
		LocalDateTime monthFirstDay = LocalDate
				.of(analysisMonth.getYear(), analysisMonth.getMonth(), 1)
				.atStartOfDay();
		dashboardScopeDto.setBeginTime(monthFirstDay);
		dashboardScopeDto.setEndTime(
				monthFirstDay.plusDays(6).with(LocalTime.MAX).withNano(0));
		BeanUtils.copyProperties(dashboardScopeDto, dto);
		list.add(dto);
		for (int i = 0; i < 3; i++) {
			dto = new DashboardScopeDto();
			dashboardScopeDto.setBeginTime(dashboardScopeDto.getBeginTime()
					.plusDays(7).toLocalDate().atStartOfDay());
			dashboardScopeDto.setEndTime(dashboardScopeDto.getEndTime()
					.plusDays(7).with(LocalTime.MAX).withNano(0));
			BeanUtils.copyProperties(dashboardScopeDto, dto);
			list.add(dto);
		}
		// 当月只有28天
		if (YearMonth.of(analysisMonth.getYear(), analysisMonth.getMonth())
				.lengthOfMonth() == 28) {
			return list;
		}
		dto = new DashboardScopeDto();
		dashboardScopeDto.setBeginTime(dashboardScopeDto.getBeginTime()
				.plusDays(7).toLocalDate().atStartOfDay());
		dashboardScopeDto.setEndTime(
				YearMonth.of(analysisMonth.getYear(), analysisMonth.getMonth())
						.atEndOfMonth().atTime(LocalTime.MAX).withNano(0));
		BeanUtils.copyProperties(dashboardScopeDto, dto);
		list.add(dto);
		return list;
	}

	/**
	 * 获取选择年份的12个月的第一天和最后一天
	 *
	 * @param analysisDate
	 * @return
	 */
	public static List<DashboardScopeDto> getYearMonthRanges(
			LocalDateTime analysisDate) {
		int year = analysisDate.getYear();
		List<DashboardScopeDto> result = new ArrayList<>();

		for (int month = 1; month <= 12; month++) {
			YearMonth yearMonth = YearMonth.of(year, month);
			LocalDateTime beginTime = yearMonth.atDay(1).atStartOfDay();
			LocalDateTime endTime = yearMonth.atEndOfMonth()
					.atTime(LocalTime.MAX).withNano(0);

			DashboardScopeDto dto = new DashboardScopeDto();
			dto.setBeginTime(beginTime);
			dto.setEndTime(endTime);
			result.add(dto);
		}
		return result;
	}
}
