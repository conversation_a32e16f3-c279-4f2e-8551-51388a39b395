package com.zhihaoscm.domain.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.domain.annotation.FileId;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 集团管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@Setter
@TableName("t_group_company")
public class GroupCompany extends BaseEntityWithStringId {

	/**
	 * 集团名称
	 */
	private String name;

	/**
	 * 联系人
	 */
	private String contactName;

	/**
	 * 联系电话
	 */
	private String contactPhone;

	/**
	 * 集团logo文件编号
	 */
	@FileId
	private Long imgMainId;

	/**
	 * 展示位置
	 */
	private Integer showLocation;

	/**
	 * 是否关联供应商:1是 0否
	 */
	private Integer used;
}
