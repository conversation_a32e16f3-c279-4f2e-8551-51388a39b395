package com.zhihaoscm.domain.bean.vo;

import java.util.List;

import com.zhihaoscm.domain.bean.entity.*;

import lombok.Data;

/**
 * 寄售商品vo
 */
@Data
public class ProductConsignmentVo {
	/**
	 * 寄售商品
	 */
	private ProductConsignment productConsignment;

	/**
	 * 团购商品
	 */
	private ProductGroupPurchase productGroupPurchase;

	/**
	 * 品类
	 */
	private ProductType productType;

	/**
	 * 品类信息
	 */
	private ProductTypeVo productTypeVo;

	/**
	 * 主图文件
	 */
	private File mainFile;

	/**
	 * 视频文件
	 */
	private File videoFile;

	/**
	 * 非主图文件
	 */
	private List<File> otherImgFile;

	/**
	 * 申请寄样（最新一条待处理或已寄出状态的）（寄售商品）
	 */
	private SampleApply sampleApply;
}
