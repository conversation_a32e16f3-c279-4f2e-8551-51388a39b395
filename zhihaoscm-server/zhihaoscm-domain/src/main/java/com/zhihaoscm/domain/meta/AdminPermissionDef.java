package com.zhihaoscm.domain.meta;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import com.zhihaoscm.common.util.utils.JsonUtils;

import lombok.Getter;

public interface AdminPermissionDef {

	/**
	 * 权限前缀
	 */
	String PERMISSION_PREFIX = "ROLE_";

	/**
	 * 全局操作权限 操作按钮（禁用）
	 */
	String BUTTON_BAN = PERMISSION_PREFIX + "BUTTON_BAN";

	/**
	 * 全局操作权限 操作按钮（点击提示：操作成功）
	 */
	String BUTTON_TIP = PERMISSION_PREFIX + "BUTTON_TIP";

	/**
	 * 全局操作权限 操作按钮（点击无触发事件）
	 */
	String BUTTON_NULL = PERMISSION_PREFIX + "BUTTON_NULL";

	/**
	 * 用户管理 定制管理 查看
	 */
	String CUSTOMIZED_R = PERMISSION_PREFIX + "CUSTOMIZED_R";

	/**
	 * 用户管理 定制管理 管理
	 */
	String CUSTOMIZED_W = PERMISSION_PREFIX + "CUSTOMIZED_W";

	/**
	 * 供应商管理 查看： 查看
	 */
	String SUPPLIER_R = PERMISSION_PREFIX + "SUPPLIER_R";

	/**
	 * 供应商管理 处理： 新增、编辑、删除
	 */
	String SUPPLIER_DEAL = PERMISSION_PREFIX + "SUPPLIER_DEAL";

	/**
	 * 后台用户管理
	 **/
	String USER_W = PERMISSION_PREFIX + "USER_W";

	/**
	 * 后台用户查看
	 */
	String USER_R = PERMISSION_PREFIX + "USER_R";

	/**
	 * 员工管理
	 **/
	String EMPLOYEE_W = PERMISSION_PREFIX + "EMPLOYEE_W";

	/**
	 * 部门管理
	 **/
	String DEPT_W = PERMISSION_PREFIX + "DEPT_W";

	/**
	 * 组织架构查看
	 **/
	String ORG_R = PERMISSION_PREFIX + "ORG_R";

	/**
	 * 角色查看
	 **/
	String ROLE_R = PERMISSION_PREFIX + "ROLE_R";

	/**
	 * 角色管理
	 **/
	String ROLE_W = PERMISSION_PREFIX + "ROLE_W";

	/**
	 * 品类管理
	 */
	String PRODUCT_TYPE_W = PERMISSION_PREFIX + "PRODUCT_TYPE_W";

	/**
	 * 品类查看
	 */
	String PRODUCT_TYPE_R = PERMISSION_PREFIX + "PRODUCT_TYPE_R";

	/**
	 * 码头管理
	 */
	String DOCK_W = PERMISSION_PREFIX + "DOCK_W";

	/**
	 * 码头管理查看
	 */
	String DOCK_R = PERMISSION_PREFIX + "DOCK_R";

	/**
	 * 砂石资讯管理
	 */
	String INFO_W = PERMISSION_PREFIX + "INFO_W";
	/**
	 * 砂石资讯查看
	 */
	String INFO_R = PERMISSION_PREFIX + "INFO_R";

	/**
	 * 砂石学院查看：查找单个砂石学院数据、分页查询砂石学院
	 */
	String SAND_ACADEMY_R = PERMISSION_PREFIX + "SAND_ACADEMY_R";

	/**
	 * 砂石学院管理：查找单个砂石学院数据、分页查询砂石学院、修改、新增、更新状态、删除
	 */
	String SAND_ACADEMY_W = PERMISSION_PREFIX + "SAND_ACADEMY_W";

	/**
	 * 砂石热点查看：查看
	 */
	String HPTSPOT_R = PERMISSION_PREFIX + "HPTSPOT_R";

	/**
	 * 砂石热点管理：查看、新增、修改、提交、下架
	 */
	String HPTSPOT_W = PERMISSION_PREFIX + "HPTSPOT_W";

	/**
	 * 品类指数管理
	 */
	String CATEGORY_INDEX_MANAGE = PERMISSION_PREFIX + "CATEGORY_INDEX_MANAGE";

	/**
	 * 品类指数查看
	 */
	String CATEGORY_INDEX_R = PERMISSION_PREFIX + "CATEGORY_INDEX_R";

	/**
	 * 品类指数处理
	 */
	String CATEGORY_INDEX_DEAL = PERMISSION_PREFIX + "CATEGORY_INDEX_DEAL";

	/**
	 * 商品管理 --查询商品详情、分页查询商品列表、监控
	 * 查询商品详情、分页查询商品列表
	 */
	String PRODUCT_R = PERMISSION_PREFIX + "PRODUCT_R";

	/**
	 * 商品管理 --查询商品详情、分页查询商品列表、编辑商品、下架商品、新增商品、预览、绑定、解绑、设备
	 * 查询商品详情、分页查询商品列表、审核、变更指派
	 */
	String PRODUCT_MANAGE = PERMISSION_PREFIX + "PRODUCT_MANAGE";

	/**
	 * 商品管理：查询商品详情、分页查询商品列表、编辑商品、下架商品、新增商品、新增排期、修改排期、删除排期
	 */
	String PRODUCT_DEAL = PERMISSION_PREFIX + "PRODUCT_DEAL";

	/**
	 * 商品管理 审核
	 */
	String PRODUCT_REVIEW = PERMISSION_PREFIX + "PRODUCT_REVIEW";

	/**
	 * 商品管理 关闭
	 */
	String PRODUCT_CLOSE = PERMISSION_PREFIX + "PRODUCT_CLOSE";

	/**
	 * 商机管理-查看-查看
	 */
	String BUSINESS_R = PERMISSION_PREFIX + "BUSINESS_R";

	/**
	 * 商机管理-管理-新增、编辑、删除、下架
	 */
	String BUSINESS_W = PERMISSION_PREFIX + "BUSINESS_W";

	/**
	 * 商机管理-审核-审核
	 */
	String BUSINESS_REVIEW = PERMISSION_PREFIX + "BUSINESS_REVIEW";

	/**
	 * 信息管理 查看
	 */
	String CUSTOMER_R = PERMISSION_PREFIX + "CUSTOMER_R";

	/**
	 * 信息管理 管理
	 */
	String CUSTOMER_W = PERMISSION_PREFIX + "CUSTOMER_W";

	/**
	 * 机构认证管理 审核
	 */
	String ORG_REVIEW = PERMISSION_PREFIX + "ORG_REVIEW";

	/**
	 * 设备管理 查看
	 */
	String DEVICE_R = PERMISSION_PREFIX + "DEVICE_R";

	/**
	 * 设备管理 管理
	 */
	String DEVICE_W = PERMISSION_PREFIX + "DEVICE_W";

	/**
	 * 设备申领查看
	 */
	String DEVICE_BIND_R = PERMISSION_PREFIX + "DEVICE_BIND_R";

	/**
	 * 设备申领处理
	 */
	String DEVICE_BIND_W = PERMISSION_PREFIX + "DEVICE_BIND_W";

	/**
	 * 设备申领查看
	 */
	String DEVICE_RECORD_R = PERMISSION_PREFIX + "DEVICE_RECORD_R";

	/**
	 * 设备申领处理
	 */
	String DEVICE_RECORD_W = PERMISSION_PREFIX + "DEVICE_RECORD_W";

	/**
	 * 船舶管理 查看、监控
	 */
	String SHIP_R = PERMISSION_PREFIX + "SHIP_R";

	/**
	 * 船舶管理管理：新增、修改、绑定、解绑、启用、禁用、设备、关闭；
	 */
	String SHIP_W = PERMISSION_PREFIX + "SHIP_W";

	/**
	 * 船舶信息管理 水尺表：导入（水尺数据表）、新增（水尺数据）、删除（水尺数据）、修改（水尺数据）
	 */
	String SHIP_M = PERMISSION_PREFIX + "SHIP_M";

	/**
	 * 船舶信息管理 导入：导入联系方式
	 */
	String SHIP_I = PERMISSION_PREFIX + "SHIP_I";

	/**
	 * 货主船运需求管理 平台船运需求管理 查看
	 */
	String SHIP_DEMAND_R = PERMISSION_PREFIX + "SHIP_DEMAND_R";

	/**
	 * 货主船运需求管理 平台船运需求管理 处理
	 */
	String SHIP_DEMAND_DEAL = PERMISSION_PREFIX + "SHIP_DEMAND_DEAL";

	/**
	 * 船运需求发布-邀请-一键发短信、平台邀请
	 */
	String SHIP_INVITE = PERMISSION_PREFIX + "SHIP_INVITE";

	/**
	 * 货主船运需求管理 平台船运需求管理 管理
	 */
	String SHIP_DEMAND_MANAGE = PERMISSION_PREFIX + "SHIP_DEMAND_MANAGE";

	/**
	 * 轮播图管理
	 */
	String SLIDER = PERMISSION_PREFIX + "SLIDER";

	/**
	 * 关于链云 管理
	 */
	String ABOUT_LY = PERMISSION_PREFIX + "ABOUT_LY";

	/**
	 * 帮助中心 查看：查看
	 */
	String HELP_R = PERMISSION_PREFIX + "HELP_R";

	/**
	 * 帮助中心 管理：新增、编辑、上架、下架、删除
	 */
	String HELP_W = PERMISSION_PREFIX + "HELP_W";

	/**
	 * 船舶认证管理
	 */
	String SHIP_REVIEW = PERMISSION_PREFIX + "SHIP_REVIEW";

	/**
	 * 查船
	 */
	String LF_SHIP = PERMISSION_PREFIX + "LF_SHIP";

	/**
	 * 自定义搜船
	 */
	String LF_SHIP_CUSTOMIZE = PERMISSION_PREFIX + "LF_SHIP_CUSTOMIZE";

	/**
	 * 区域查船
	 */
	String LF_SHIP_REGION = PERMISSION_PREFIX + "LF_SHIP_REGION";

	/**
	 * 航线管理：查看
	 */
	String SHIP_ROUTE_R = PERMISSION_PREFIX + "SHIP_ROUTE_R";

	/**
	 * 航线管理：新增、修改、删除
	 */
	String SHIP_ROUTE_W = PERMISSION_PREFIX + "SHIP_ROUTE_W";

	/**
	 * 运价指数：查看
	 */
	String FREIGHT_RATE_R = PERMISSION_PREFIX + "FREIGHT_RATE_R";

	/**
	 * 运价指数：新增、批量上传、修改、删除
	 */
	String FREIGHT_RATE_DEAL = PERMISSION_PREFIX + "FREIGHT_RATE_DEAL";

	/**
	 * 运价指数：发布、撤回
	 */
	String FREIGHT_RATE_MANAGE = PERMISSION_PREFIX + "FREIGHT_RATE_MANAGE";

	/**
	 * 平台银行账号管理: 查看、删除、新增、提交 用户账户管理：添加预付款、撤销、核销
	 */
	String ACCOUNT_MANAGE = PERMISSION_PREFIX + "ACCOUNT_MANAGE";

	/**
	 * 船运单管理:运输
	 */
	String SHIP_ORDER_DEAL = PERMISSION_PREFIX + "SHIP_ORDER_DEAL";

	/**
	 * 砂石综合指数:查看
	 */
	String COMPSITE_INDEX_R = PERMISSION_PREFIX + "COMPSITE_INDEX_R";

	/**
	 * 砂石综合指数:处理
	 */
	String COMPSITE_INDEX_DEAL = PERMISSION_PREFIX + "COMPSITE_INDEX_DEAL";

	/**
	 * 砂石综合指数:管理
	 */
	String COMPSITE_INDEX_MANAGE = PERMISSION_PREFIX + "COMPSITE_INDEX_MANAGE";

	/**
	 * 广告管理:查看
	 */
	String AD_R = PERMISSION_PREFIX + "AD_R";

	/**
	 * 广告管理:处理
	 */
	String AD_W = PERMISSION_PREFIX + "AD_W";

	/**
	 * 广告页查看
	 */
	String AD_LIST_R = PERMISSION_PREFIX + "AD_LIST_R";

	/**
	 * 广告页处理
	 */
	String AD_LIST_W = PERMISSION_PREFIX + "AD_LIST_W";

	/**
	 * 会员等级 修改（权益）、开放/关闭
	 */
	String VIP_LEVEL = PERMISSION_PREFIX + "VIP_LEVEL";

	/**
	 * 充值列表 查看
	 */
	String ORDER_R = PERMISSION_PREFIX + "ORDER_R";

	/**
	 * 充值列表 管理 兑换
	 */
	String ORDER_W = PERMISSION_PREFIX + "ORDER_W";

	/**
	 * 激活码查看
	 */
	String CDKEY_R = PERMISSION_PREFIX + "CDKEY_R";

	/**
	 * 激活码管理
	 */
	String CDKEY_W = PERMISSION_PREFIX + "CDKEY_W";

	/**
	 * 推广查看
	 */
	String PROMO_R = PERMISSION_PREFIX + "PROMO_R";

	/**
	 * 推广管理
	 */
	String PROMO_W = PERMISSION_PREFIX + "PROMO_W";

	/**
	 * 监控预警记录：查看
	 */
	String WARNING_R = PERMISSION_PREFIX + "WARNING_R";

	/**
	 * 监控预警记录：处理
	 */
	String WARNING_DEAL = PERMISSION_PREFIX + "WARNING_DEAL";

	/**
	 * 监控预警记录：指派
	 */
	String WARNING_MANAGE = PERMISSION_PREFIX + "WARNING_MANAGE";

	/**
	 * 砂石交易看板
	 */
	String DEAL_BI = PERMISSION_PREFIX + "DEAL_BI";

	/**
	 * 链云用户看板
	 */
	String USER_BI = PERMISSION_PREFIX + "USER_BI";

	/**
	 * 物流看板
	 */
	String SHIP_BI = PERMISSION_PREFIX + "SHIP_BI";

	/**
	 * 平台预收款记录
	 */
	String SYS_PRE_INCOME_R = PERMISSION_PREFIX + "SYS_PRE_INCOME_R";

	/**
	 * 安装推广佣金:查看
	 */
	String DEVICE_BONUS_R = PERMISSION_PREFIX + "DEVICE_BONUS_R";

	/**
	 * 安装推广佣金:管理 结算
	 */
	String DEVICE_BONUS_W = PERMISSION_PREFIX + "DEVICE_BONUS_W";

	/**
	 * 业务设置：查看、安装推广佣金设置
	 */
	String PROMO_SETTINGS = PERMISSION_PREFIX + "PROMO_SETTINGS";

	/**
	 * 船运定金 查看
	 */
	String SHIP_DOWNPAYMENT_R = PERMISSION_PREFIX + "SHIP_DOWNPAYMENT_R";

	/**
	 * 船运定金 重新发起
	 */
	String SHIP_DOWNPAYMENT_W = PERMISSION_PREFIX + "SHIP_DOWNPAYMENT_W";

	/**
	 * 船务信息服务费 查看
	 */
	String SHIP_ORDER_SERVICE_FEE_R = PERMISSION_PREFIX
			+ "SHIP_ORDER_SERVICE_FEE_R";

	/**
	 * 船务信息服务费 确认、撤销
	 */
	String SHIP_ORDER_SERVICE_FEE_W = PERMISSION_PREFIX
			+ "SHIP_ORDER_SERVICE_FEE_W";

	/**
	 * 模型数据： 查看
	 */
	String MODEL_DATA_R = PERMISSION_PREFIX + "MODEL_DATA_R";

	/**
	 * 模型数据：发布、标记、删除
	 */
	String MODEL_DATA_W = PERMISSION_PREFIX + "MODEL_DATA_W";

	/**
	 * 识别记录：查看
	 */
	String MODEL_DATA_LOG_R = PERMISSION_PREFIX + "MODEL_DATA_LOG_R";

	/**
	 * 识别记录：加入模型
	 */
	String MODEL_DATA_LOG_W = PERMISSION_PREFIX + "MODEL_DATA_LOG_W";

	/**
	 * 砂石综合指数自动配置管理:新增、修改、保存、启用
	 */
	String COMPSITE_INDEX_AUTO_W = PERMISSION_PREFIX + "COMPSITE_INDEX_AUTO_W";

	/**
	 * 砂石综合指数自动配置查看：查看版本记录、查看配置详情
	 */
	String COMPSITE_INDEX_AUTO_R = PERMISSION_PREFIX + "COMPSITE_INDEX_AUTO_R";

	/**
	 * 船主线索查看
	 */
	String SHIP_CLUE_R = PERMISSION_PREFIX + "SHIP_CLUE_R";

	/**
	 * 船主线索处理
	 */
	String SHIP_CLUE_W = PERMISSION_PREFIX + "SHIP_CLUE_W";

	/**
	 * 业务配置：查看、修改、保存
	 */
	String SETTINGS = PERMISSION_PREFIX + "SETTINGS";

	/**
	 * 标签管理：查看 查分页查询、根据名称查询
	 */
	String LABEL_R = PERMISSION_PREFIX + "LABEL_R";

	/**
	 * 标签管理：管理 查分页查询、根据名称查询，新增、修改、删除、移除
	 *
	 */
	String LABEL_W = PERMISSION_PREFIX + "LABEL_W";

	/**
	 * 通知管理：查看
	 */
	String NOTICE_R = PERMISSION_PREFIX + "NOTICE_R";

	/**
	 * 通知管理：新增、编辑、定时发布、发布、撤回、删除
	 */
	String NOTICE_W = PERMISSION_PREFIX + "NOTICE_W";

	/**
	 * 留言反馈查看：查看
	 */
	String FEEDBACK_R = PERMISSION_PREFIX + "FEEDBACK_R";

	/**
	 * 留言反馈管理：查看、回复
	 */
	String FEEDBACK_W = PERMISSION_PREFIX + "FEEDBACK_W";

	/**
	 * 查车
	 */
	String LF_CAR = PERMISSION_PREFIX + "LF_CAR";

	/**
	 * 船运单管理 --关闭
	 */
	String SHIP_ORDER_CLOSE = PERMISSION_PREFIX + "SHIP_ORDER_CLOSE";
	/**
	 * 运价综合指数:查看 分页查询运价综合指数、根据价格日期和状态查询列表数据
	 */
	String COMPSITE_FREIGHT_R = PERMISSION_PREFIX + "COMPSITE_FREIGHT_R";

	/**
	 * 运价综合指数:处理 分页查询运价综合指数、根据价格日期和状态查询列表数据、快捷新增、修改综合指数、删除
	 */
	String COMPSITE_FREIGHT_DEAL = PERMISSION_PREFIX + "COMPSITE_FREIGHT_DEAL";

	/**
	 * 运价综合指数:管理 发布、撤回
	 */
	String COMPSITE_FREIGHT_MANAGE = PERMISSION_PREFIX
			+ "COMPSITE_FREIGHT_MANAGE";

	/**
	 * 运价综合指数自动配置:管理 查看、修改、保存、启用
	 */
	String COMPSITE_FREIGHT_AUTO_W = PERMISSION_PREFIX
			+ "COMPSITE_FREIGHT_AUTO_W";

	/**
	 * 运价综合指数自动配置:查看
	 */
	String COMPSITE_FREIGHT_AUTO_R = PERMISSION_PREFIX
			+ "COMPSITE_FREIGHT_AUTO_R";

	/**
	 * 设备管理：导出
	 */
	String DEVICE_EXPORT = PERMISSION_PREFIX + "DEVICE_EXPORT";

	/**
	 * 船舶关注
	 */
	String LF_SHIP_LIKE = PERMISSION_PREFIX + "LF_SHIP_LIKE";

	/**
	 * 搜索发现-查看
	 */
	String SEARCH_R = PERMISSION_PREFIX + "SEARCH_R";

	/**
	 * 搜索发现-管理 新增、编辑、删除、置顶、取消置顶
	 */
	String SEARCH_W = PERMISSION_PREFIX + "SEARCH_W";

	/**
	 * 功能反馈-查看
	 */
	String FUNCTION_R = PERMISSION_PREFIX + "FUNCTION_R";

	/**
	 * 功能反馈-管理
	 */
	String FUNCTION_W = PERMISSION_PREFIX + "FUNCTION_W";

	/**
	 * AI历史对话管理权限
	 */
	String ASSISTANTS = PERMISSION_PREFIX + "ASSISTANTS";

	/**
	 * AI热词管理权限 查看、删除、新增、批量导入、一键同步
	 */
	String ASSISTANTS_WORDS = PERMISSION_PREFIX + "ASSISTANTS_WORDS";

	/**
	 * 应用管理
	 */
	String APPLICATION = PERMISSION_PREFIX + "APPLICATION";

	/**
	 * 智能助理-AI语料库-查看、新增、编辑、导出、修正、删除、撤回、准确
	 */
	String ASSISTANTS_CORPUS_W = PERMISSION_PREFIX + "ASSISTANTS_CORPUS_W";

	/**
	 * 平台管理 app版本管理
	 */
	String VERSION = PERMISSION_PREFIX + "VERSION";

	/**
	 * 自定义搜船导出
	 */
	String LF_SHIP_EXPORT = PERMISSION_PREFIX + "LF_SHIP_EXPORT";

	/**
	 * 回调记录：查看、重新回调
	 */
	String CALLBACK_RECORD = PERMISSION_PREFIX + "CALLBACK_RECORD";

	/**
	 * 弹窗管理 查看
	 */
	String POPUP_R = PERMISSION_PREFIX + "POPUP_R";

	/**
	 * 弹窗管理 启用、禁用、新增、编辑、删除
	 */
	String POPUP_W = PERMISSION_PREFIX + "POPUP_W";

	/**
	 * 站点管理：查看
	 */
	String GAS_STATION_R = PERMISSION_PREFIX + "GAS_STATION_R";

	/**
	 * 站点管理：管理：新增、修改、删除、禁用、启用
	 */
	String GAS_STATION_W = PERMISSION_PREFIX + "GAS_STATION_W";

	/**
	 * 油品费用：查看
	 */
	String REFUELING_FEE_R = PERMISSION_PREFIX + "REFUELING_FEE_R";

	/**
	 * 油品费用：管理：确认
	 */
	String REFUELING_FEE_W = PERMISSION_PREFIX + "REFUELING_FEE_W";

	/**
	 * 油品订单：查看
	 */
	String GAS_ORDER_R = PERMISSION_PREFIX + "GAS_ORDER_R";

	/**
	 * 油品订单：处理：新增、核对、确认加油、完成加油、报计划、取消
	 */
	String GAS_ORDER_DEAL = PERMISSION_PREFIX + "GAS_ORDER_DEAL";

	/**
	 * 油品订单：管理：删除
	 */
	String GAS_ORDER_MANAGE = PERMISSION_PREFIX + "GAS_ORDER_MANAGE";

	/**
	 * 油品指数：查看
	 */
	String PETROL_INDEX_R = PERMISSION_PREFIX + "PETROL_INDEX_R";

	/**
	 * 油品指数：处理：新增(版本)、修改(版本)、删除(版本)、新增(记录）、修改(记录）、删除(记录）、保存(记录）、提交(记录）、批量上传、新增(指数）、修改(指数）、删除(指数）、附件上传
	 */
	String PETROL_INDEX_DEAL = PERMISSION_PREFIX + "PETROL_INDEX_DEAL";

	/**
	 * 油品指数：发布、撤回、驳回
	 */
	String PETROL_INDEX_MANAGE = PERMISSION_PREFIX + "PETROL_INDEX_MANAGE";

	/**
	 * 管理后台代操作 货主找船需求：确认找船； 船运单：发航确认、到港确认、同意卸货、上传排水视频
	 */
	String SHIP_DEMAND_OPERATE = PERMISSION_PREFIX + "SHIP_DEMAND_OPERATE";

	/**
	 * 申请寄样列表：查询申请寄样详情、分页查询申请寄样列表
	 */
	String APPLY_SAMPLE_R = PERMISSION_PREFIX + "APPLY_SAMPLE_R";

	/**
	 * 申请寄样列表：查询申请寄样详情、分页查询申请寄样列表、处理
	 */
	String APPLY_SAMPLE_DEAL = PERMISSION_PREFIX + "APPLY_SAMPLE_DEAL";

	/**
	 * 订单管理：查询订单详情、分页查询订单列表
	 */
	String TRADE_ORDER_R = PERMISSION_PREFIX + "TRADE_ORDER_R";

	/**
	 * 订单管理：查询订单详情、分页查询订单列表、指派专员、变更指派、完成
	 */
	String TRADE_ORDER_MANAGE = PERMISSION_PREFIX + "TRADE_ORDER_MANAGE";

	/**
	 * 订单管理：删除
	 */
	String TRADE_ORDER_DELETE = PERMISSION_PREFIX + "TRADE_ORDER_DELETE";

	/**
	 * 订货单管理：查询订单详情、分页查询订单列表、订单备注、取消订单、
	 */
	String TRADE_ORDER_DEAL = PERMISSION_PREFIX + "TRADE_ORDER_DEAL";

	/**
	 * 退款管理：查询退款详情、分页查询退款列表
	 */
	String REFUND_R = PERMISSION_PREFIX + "REFUND_R";

	/**
	 * 退款管理：查询退款详情、分页查询退款列表、审核、取消
	 */
	String REFUND_MANAGE = PERMISSION_PREFIX + "REFUND_MANAGE";

	/**
	 * 退款管理：查询退款详情、分页查询退款列表、新增、编辑、处理退款
	 */
	String REFUND_DEAL = PERMISSION_PREFIX + "REFUND_DEAL";

	/**
	 * 发票管理：查询发票详情、分页查询发票列表、预览
	 */
	String INVOICE_R = PERMISSION_PREFIX + "INVOICE_R";

	/**
	 * 发票管理：查询发票详情、分页查询发票列表、预览、新增、作废
	 */
	String INVOICE_DEAL = PERMISSION_PREFIX + "INVOICE_DEAL";

	/**
	 * 资金线索：查看
	 */
	String FUNDS_R = PERMISSION_PREFIX + "FUNDS_R";

	/**
	 * 资金线索：处理
	 */
	String FUNDS_W = PERMISSION_PREFIX + "FUNDS_W";




	@Getter
	enum AdminPermission {
		USER_W(AdminPermissionDef.USER_W),

		USER_R(AdminPermissionDef.USER_R),

		EMPLOYEE_W(AdminPermissionDef.EMPLOYEE_W),

		ORG_R(AdminPermissionDef.ORG_R),

		ROLE_W(AdminPermissionDef.ROLE_W),

		ROLE_R(AdminPermissionDef.ROLE_R),

		PRODUCT_TYPE_W(AdminPermissionDef.PRODUCT_TYPE_W),

		PRODUCT_TYPE_R(AdminPermissionDef.PRODUCT_TYPE_R),

		DEPT_W(AdminPermissionDef.DEPT_W),

		DOCK_W(AdminPermissionDef.DOCK_W),

		DOCK_R(AdminPermissionDef.DOCK_R),

		INFO_W(AdminPermissionDef.INFO_W),

		INFO_R(AdminPermissionDef.INFO_R),

		SAND_ACADEMY_R(AdminPermissionDef.SAND_ACADEMY_R),

		SAND_ACADEMY_W(AdminPermissionDef.SAND_ACADEMY_W),

		HPTSPOT_R(AdminPermissionDef.HPTSPOT_R),

		HPTSPOT_W(AdminPermissionDef.HPTSPOT_W),

		CATEGORY_INDEX_MANAGE(AdminPermissionDef.CATEGORY_INDEX_MANAGE),

		CATEGORY_INDEX_R(AdminPermissionDef.CATEGORY_INDEX_R),

		CATEGORY_INDEX_DEAL(AdminPermissionDef.CATEGORY_INDEX_DEAL),

		PRODUCT_R(AdminPermissionDef.PRODUCT_R),

		PRODUCT_MANAGE(AdminPermissionDef.PRODUCT_MANAGE),

		PRODUCT_DEAL(AdminPermissionDef.PRODUCT_DEAL),

		PRODUCT_REVIEW(AdminPermissionDef.PRODUCT_REVIEW),

		PRODUCT_CLOSE(AdminPermissionDef.PRODUCT_CLOSE),

		BUSINESS_R(AdminPermissionDef.BUSINESS_R),

		BUSINESS_W(AdminPermissionDef.BUSINESS_W),

		BUSINESS_REVIEW(AdminPermissionDef.BUSINESS_REVIEW),

		CUSTOMER_R(AdminPermissionDef.CUSTOMER_R),

		CUSTOMER_W(AdminPermissionDef.CUSTOMER_W),

		ORG_REVIEW(AdminPermissionDef.ORG_REVIEW),

		DEVICE_R(AdminPermissionDef.DEVICE_R),

		DEVICE_W(AdminPermissionDef.DEVICE_W),

		DEVICE_BIND_R(AdminPermissionDef.DEVICE_BIND_R),

		DEVICE_BIND_W(AdminPermissionDef.DEVICE_BIND_W),

		DEVICE_RECORD_R(AdminPermissionDef.DEVICE_RECORD_R),

		DEVICE_RECORD_W(AdminPermissionDef.DEVICE_RECORD_W),

		SHIP_R(AdminPermissionDef.SHIP_R),

		SHIP_W(AdminPermissionDef.SHIP_W),

		SHIP_M(AdminPermissionDef.SHIP_M),

		SHIP_I(AdminPermissionDef.SHIP_I),

		SHIP_DEMAND_R(AdminPermissionDef.SHIP_DEMAND_R),

		SHIP_DEMAND_DEAL(AdminPermissionDef.SHIP_DEMAND_DEAL),

		SHIP_INVITE(AdminPermissionDef.SHIP_INVITE),

		SHIP_DEMAND_MANAGE(AdminPermissionDef.SHIP_DEMAND_MANAGE),

		SLIDER(AdminPermissionDef.SLIDER),

		ABOUT_LY(AdminPermissionDef.ABOUT_LY),

		HELP_R(AdminPermissionDef.HELP_R),

		HELP_W(AdminPermissionDef.HELP_W),

		SHIP_REVIEW(AdminPermissionDef.SHIP_REVIEW),

		LF_SHIP(AdminPermissionDef.LF_SHIP),

		LF_SHIP_CUSTOMIZE(AdminPermissionDef.LF_SHIP_CUSTOMIZE),

		LF_SHIP_REGION(AdminPermissionDef.LF_SHIP_REGION),

		SHIP_ROUTE_R(AdminPermissionDef.SHIP_ROUTE_R),

		SHIP_ROUTE_W(AdminPermissionDef.SHIP_ROUTE_W),

		FREIGHT_RATE_R(AdminPermissionDef.FREIGHT_RATE_R),

		FREIGHT_RATE_DEAL(AdminPermissionDef.FREIGHT_RATE_DEAL),

		FREIGHT_RATE_MANAGE(AdminPermissionDef.FREIGHT_RATE_MANAGE),

		ACCOUNT_MANAGE(AdminPermissionDef.ACCOUNT_MANAGE),

		SHIP_ORDER_DEAL(AdminPermissionDef.SHIP_ORDER_DEAL),

		COMPSITE_INDEX_R(AdminPermissionDef.COMPSITE_INDEX_R),

		COMPSITE_INDEX_DEAL(AdminPermissionDef.COMPSITE_INDEX_DEAL),

		COMPSITE_INDEX_MANAGE(AdminPermissionDef.COMPSITE_INDEX_MANAGE),

		AD_R(AdminPermissionDef.AD_R),

		AD_W(AdminPermissionDef.AD_W),

		AD_LIST_R(AdminPermissionDef.AD_LIST_R),

		AD_LIST_W(AdminPermissionDef.AD_LIST_W),

		VIP_LEVEL(AdminPermissionDef.VIP_LEVEL),

		ORDER_R(AdminPermissionDef.ORDER_R),

		ORDER_W(AdminPermissionDef.ORDER_W),

		CDKEY_R(AdminPermissionDef.CDKEY_R),

		CDKEY_W(AdminPermissionDef.CDKEY_W),

		PROMO_R(AdminPermissionDef.PROMO_R),

		PROMO_W(AdminPermissionDef.PROMO_W),

		WARNING_R(AdminPermissionDef.WARNING_R),

		WARING_DEAL(AdminPermissionDef.WARNING_DEAL),

		WARNING_MANAGE(AdminPermissionDef.WARNING_MANAGE),

		DEAL_BI(AdminPermissionDef.DEAL_BI),

		USER_BI(AdminPermissionDef.USER_BI),

		SHIP_BI(AdminPermissionDef.SHIP_BI),

		SYS_PRE_INCOME_R(AdminPermissionDef.SYS_PRE_INCOME_R),

		DEVICE_BONUS_R(AdminPermissionDef.DEVICE_BONUS_R),

		DEVICE_BONUS_W(AdminPermissionDef.DEVICE_BONUS_W),

		PROMO_SETTINGS(AdminPermissionDef.PROMO_SETTINGS),

		SHIP_DOWNPAYMENT_R(AdminPermissionDef.SHIP_DOWNPAYMENT_R),

		SHIP_DOWNPAYMENT_W(AdminPermissionDef.SHIP_DOWNPAYMENT_W),

		SHIP_ORDER_SERVICE_FEE_R(AdminPermissionDef.SHIP_ORDER_SERVICE_FEE_R),

		SHIP_ORDER_SERVICE_FEE_W(AdminPermissionDef.SHIP_ORDER_SERVICE_FEE_W),

		MODEL_DATA_R(AdminPermissionDef.MODEL_DATA_R),

		MODEL_DATA_W(AdminPermissionDef.MODEL_DATA_W),

		MODEL_DATA_LOG_R(AdminPermissionDef.MODEL_DATA_LOG_R),

		MODEL_DATA_LOG_W(AdminPermissionDef.MODEL_DATA_LOG_W),

		COMPSITE_INDEX_AUTO_W(AdminPermissionDef.COMPSITE_INDEX_AUTO_W),

		COMPSITE_INDEX_AUTO_R(AdminPermissionDef.COMPSITE_INDEX_AUTO_R),

		BUTTON_BAN(AdminPermissionDef.BUTTON_BAN),

		BUTTON_TIP(AdminPermissionDef.BUTTON_TIP),

		BUTTON_NULL(AdminPermissionDef.BUTTON_NULL),

		CUSTOMIZED_R(AdminPermissionDef.CUSTOMIZED_R),

		CUSTOMIZED_W(AdminPermissionDef.CUSTOMIZED_W),

		SUPPLIER_R(AdminPermissionDef.SUPPLIER_R),

		SUPPLIER_DEAL(AdminPermissionDef.SUPPLIER_DEAL),

		SHIP_CLUE_R(AdminPermissionDef.SHIP_CLUE_R),

		SHIP_CLUE_W(AdminPermissionDef.SHIP_CLUE_W),

		SETTINGS(AdminPermissionDef.SETTINGS),

		LABEL_R(AdminPermissionDef.LABEL_R),

		LABEL_W(AdminPermissionDef.LABEL_W),

		NOTICE_R(AdminPermissionDef.NOTICE_R),

		NOTICE_W(AdminPermissionDef.NOTICE_W),

		FEEDBACK_R(AdminPermissionDef.FEEDBACK_R),

		FEEDBACK_W(AdminPermissionDef.FEEDBACK_W),

		LF_CAR(AdminPermissionDef.LF_CAR),

		SHIP_ORDER_CLOSE(AdminPermissionDef.SHIP_ORDER_CLOSE),

		COMPSITE_FREIGHT_R(AdminPermissionDef.COMPSITE_FREIGHT_R),

		COMPSITE_FREIGHT_DEAL(AdminPermissionDef.COMPSITE_FREIGHT_DEAL),

		COMPSITE_FREIGHT_MANAGE(AdminPermissionDef.COMPSITE_FREIGHT_MANAGE),

		COMPSITE_FREIGHT_AUTO_W(AdminPermissionDef.COMPSITE_FREIGHT_AUTO_W),

		COMPSITE_FREIGHT_AUTO_R(AdminPermissionDef.COMPSITE_FREIGHT_AUTO_R),

		DEVICE_EXPORT(AdminPermissionDef.DEVICE_EXPORT),

		LF_SHIP_LIKE(AdminPermissionDef.LF_SHIP_LIKE),

		SEARCH_R(AdminPermissionDef.SEARCH_R),

		SEARCH_W(AdminPermissionDef.SEARCH_W),

		FUNCTION_R(AdminPermissionDef.FUNCTION_R),

		FUNCTION_W(AdminPermissionDef.FUNCTION_W),

		ASSISTANTS(AdminPermissionDef.ASSISTANTS),

		ASSISTANTS_WORDS(AdminPermissionDef.ASSISTANTS_WORDS),

		APPLICATION(AdminPermissionDef.APPLICATION),

		ASSISTANTS_CORPUS_W(AdminPermissionDef.ASSISTANTS_CORPUS_W),

		VERSION(AdminPermissionDef.VERSION),

		LF_SHIP_EXPORT(AdminPermissionDef.LF_SHIP_EXPORT),

		CALLBACK_RECORD(AdminPermissionDef.CALLBACK_RECORD),

		POPUP_R(AdminPermissionDef.POPUP_R),

		POPUP_W(AdminPermissionDef.POPUP_W),

		SHIP_DEMAND_OPERATE(AdminPermissionDef.SHIP_DEMAND_OPERATE),

		GAS_STATION_R(AdminPermissionDef.GAS_STATION_R),

		GAS_STATION_W(AdminPermissionDef.GAS_STATION_W),

		REFUELING_FEE_R(AdminPermissionDef.REFUELING_FEE_R),

		REFUELING_FEE_W(AdminPermissionDef.REFUELING_FEE_W),

		GAS_ORDER_R(AdminPermissionDef.GAS_ORDER_R),

		GAS_ORDER_DEAL(AdminPermissionDef.GAS_ORDER_DEAL),

		GAS_ORDER_MANAGE(AdminPermissionDef.GAS_ORDER_MANAGE),

		PETROL_INDEX_R(AdminPermissionDef.PETROL_INDEX_R),

		PETROL_INDEX_DEAL(AdminPermissionDef.PETROL_INDEX_DEAL),

		PETROL_INDEX_MANAGE(AdminPermissionDef.PETROL_INDEX_MANAGE),

		APPLY_SAMPLE_R(AdminPermissionDef.APPLY_SAMPLE_R),

		APPLY_SAMPLE_DEAL(AdminPermissionDef.APPLY_SAMPLE_DEAL),

		TRADE_ORDER_R(AdminPermissionDef.TRADE_ORDER_R),

		TRADE_ORDER_MANAGE(AdminPermissionDef.TRADE_ORDER_MANAGE),

		TRADE_ORDER_DELETE(AdminPermissionDef.TRADE_ORDER_DELETE),

		TRADE_ORDER_DEAL(AdminPermissionDef.TRADE_ORDER_DEAL),

		REFUND_R(AdminPermissionDef.REFUND_R),

		REFUND_MANAGE(AdminPermissionDef.REFUND_MANAGE),

		REFUND_DEAL(AdminPermissionDef.REFUND_DEAL),

		INVOICE_R(AdminPermissionDef.INVOICE_R),

		INVOICE_DEAL(AdminPermissionDef.INVOICE_DEAL),

		FUNDS_R(AdminPermissionDef.FUNDS_R),

		FUNDS_W(AdminPermissionDef.FUNDS_W),

		;

		private final String permission;

		private static final Map<String, AdminPermissionDef.AdminPermission> MAPPING;

		AdminPermission(String permission) {
			this.permission = permission;
		}

		static {
			{
				Map<String, AdminPermissionDef.AdminPermission> mapping = new HashMap<>();
				for (AdminPermissionDef.AdminPermission value : AdminPermissionDef.AdminPermission
						.values()) {
					mapping.put(value.getPermission(), value);
				}
				MAPPING = mapping;
			}
		}

		public static AdminPermissionDef.AdminPermission from(
				final String permission) {
			return MAPPING.get(permission);
		}

		public boolean match(final String permission) {
			return this.permission.equals(permission);
		}

		public static void main(String[] args) {
			// 打印所有permission的值
			System.out.println(JsonUtils
					.objectToJson(Arrays.stream(AdminPermission.values())
							.map(AdminPermission::getPermission).toList()));
		}

	}

}
