package com.zhihaoscm.domain.bean.entity;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.domain.annotation.DiffField;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementCustomerDef;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementPlatDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderShipDef;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 平台船运需求
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Getter
@Setter
@TableName("t_shipping_requirement_plat")
public class ShippingRequirementPlat extends BaseEntityWithStringId {

	/**
	 * 需求等级 {@link ShippingRequirementPlatDef.DemandLevel}
	 */
	@DiffField(alias = "需求等级", type = DiffField.Type.ENUM, clazz = ShippingRequirementPlatDef.DemandLevel.class)
	private Integer demandLevel;

	/**
	 * 货主id
	 */
	private Long ownerId;

	/**
	 * 货主公司信息
	 */
	private Enterprise ownerEnterprise;

	/**
	 * 联系人
	 */
	@DiffField(alias = "联系人")
	private String contact;

	/**
	 * 手机号
	 */
	@DiffField(alias = "手机号")
	private String mobile;

	/**
	 * 货品类型
	 */
	@DiffField(alias = "货品类型")
	private String goodsType;

	/**
	 * 船型 {@link ShippingRequirementCustomerDef.ShipType}
	 */
	@DiffField(alias = "船型", type = DiffField.Type.ENUM, clazz = ShippingRequirementCustomerDef.ShipType.class)
	private Integer shipType;

	/**
	 * 排水槽{@link ShippingRequirementPlatDef.DrainageChannel}
	 */
	@DiffField(alias = "排水槽", type = DiffField.Type.ENUM, clazz = ShippingRequirementPlatDef.DrainageChannel.class)
	private Integer drainageChannel;

	/**
	 * 始发地码头id
	 */
	private Long sourcePortId;

	/**
	 * 始发地码头名称
	 */
	@DiffField(alias = "始发地")
	private String sourcePortName;

	/**
	 * 目的地码头id
	 */
	private Long destinationPortId;

	/**
	 * 目的地码头名称
	 */
	@DiffField(alias = "目的地")
	private String destinationPortName;

	/**
	 * 航线id
	 */
	private Long shipRouteId;

	/**
	 * 航线始发地
	 */
	@DiffField(alias = "航线名称", fields = { "shipRouteSource",
			"shipRouteDestination" }, join = "→")
	private String shipRouteSource;

	/**
	 * 航线目的地
	 */
	@DiffField(alias = "航线名称", fields = { "shipRouteSource",
			"shipRouteDestination" })
	private String shipRouteDestination;

	/**
	 * 船运单id
	 */
	private ArrayString orderShipIds;

	/**
	 * 处理专员id
	 */
	private Long handlerId;

	/**
	 * 处理专员名称
	 */
	private String handlerName;

	/**
	 * 意向单价
	 */
	@DiffField(alias = "意向运价")
	private BigDecimal unitPrice;

	/**
	 * 运货吨数
	 */
	@DiffField(alias = "运货吨数")
	private Integer freightTons;

	/**
	 * 运输最大吨数
	 */
	@DiffField(alias = "船舶吨位要求", fields = { "tonMin", "tonMax" })
	private Integer tonMax;

	/**
	 * 运输最小吨数
	 */
	@DiffField(alias = "船舶吨位要求", fields = { "tonMin", "tonMax" }, join = "-")
	private Integer tonMin;

	/**
	 * 装载日期
	 */
	@DiffField(alias = "期望装载日期", type = DiffField.Type.DATE, fields = {
			"loadDate", "loadDays" }, join = " +")
	private LocalDate loadDate;

	/**
	 * 装载日期+的天数
	 */
	@DiffField(alias = "期望装载日期", type = DiffField.Type.DAY, fields = {
			"loadDate", "loadDays" })
	private Integer loadDays;

	/**
	 * 装卸天数
	 */
	@DiffField(alias = "装卸天数")
	private Integer loadUnloadDays;

	/**
	 * 滞期费
	 */
	@DiffField(alias = "滞期费")
	private BigDecimal demurrageFee;

	/**
	 * 海事费用 {@link ShippingRequirementCustomerDef.MaritimeAffairsFee}
	 */
	@DiffField(alias = "海事费用", type = DiffField.Type.ENUM, clazz = ShippingRequirementCustomerDef.MaritimeAffairsFee.class)
	private Integer maritimeAffairsFee;

	/**
	 * 补充约定
	 */
	@DiffField(alias = "补充约定")
	private String replenishAppoint;

	/**
	 * 特殊说明
	 */
	@DiffField(alias = "特殊说明")
	private String specialRemark;

	/**
	 * 接单状态
	 */
	private Integer orderAcceptanceState;

	/**
	 * 船运定金
	 */
	@DiffField(alias = "船运定金")
	private BigDecimal deposit;

	/**
	 * 船运费用支付方式
	 */
	@DiffField(alias = "船运费用支付方式", type = DiffField.Type.ENUM, clazz = TransportOrderShipDef.DepositPayType.class)
	private Integer payType;

	/**
	 * 船务信息服务费
	 */
	@DiffField(alias = "船务信息服务费")
	private BigDecimal shipInfoServiceFee;

	/**
	 * 电联号码类型 {@link ShippingRequirementPlatDef.ElectricContactNumberType}
	 */
	@DiffField(alias = "电联号码", type = DiffField.Type.ENUM, clazz = ShippingRequirementPlatDef.ElectricContactNumberType.class, fields = {
			"electricContactNumberType", "electricContactNumber" }, join = " ")
	private Integer electricContactNumberType;

	/**
	 * 电联号码
	 */
	@DiffField(alias = "电联号码", fields = { "electricContactNumberType",
			"electricContactNumber" })
	private String electricContactNumber;

	/**
	 * 状态
	 */
	private Integer state;

	/**
	 * 数据来源 {link AccountsDef.}
	 */
	private Integer dataSource;

	/**
	 * 来源应用id
	 */
	private Long sourceAppId;

}
