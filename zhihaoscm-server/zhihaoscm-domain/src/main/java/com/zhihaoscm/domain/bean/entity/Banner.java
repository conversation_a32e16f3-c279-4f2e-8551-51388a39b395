package com.zhihaoscm.domain.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.domain.annotation.FileId;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 横幅
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Getter
@Setter
@TableName("t_banner")
public class Banner extends BaseEntityWithLongId {

	/**
	 * 位置{@link com.zhihaoscm.domain.meta.biz.BannerDef.Pos}
	 */
	private Integer pos;

	/**
	 * 图片文件id
	 */
	@FileId
	private Long fileId;

	/**
	 * 排序
	 */
	private Integer sort;

	/**
	 * 类型
	 */
	private Integer type;

	/**
	 * 内容
	 */
	private String value;
}
