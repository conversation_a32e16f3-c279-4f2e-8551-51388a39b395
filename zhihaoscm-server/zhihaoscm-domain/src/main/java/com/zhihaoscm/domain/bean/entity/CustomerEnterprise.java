package com.zhihaoscm.domain.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 子账号中间表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Getter
@Setter
@TableName("t_customer_enterprise")
public class CustomerEnterprise extends BaseEntityWithLongId {

	/**
	 * 客户id
	 */
	private Long mainAccountId;

	/**
	 * 企业id
	 */
	private Long subAccountId;

	/**
	 * 子账号在第三方平台入驻企业的员工id
	 */
	private Long thirdSubAccountId;

	/**
	 * 状态
	 */
	private Integer state;

	/**
	 * 账号状态 0 已失效 1 已激活
	 */
	private Integer accountState = CommonDef.Symbol.YES.getCode();

	/**
	 * 是否接收短信
	 */
	private Integer receiptSms = CommonDef.Symbol.NO.getCode();

}
