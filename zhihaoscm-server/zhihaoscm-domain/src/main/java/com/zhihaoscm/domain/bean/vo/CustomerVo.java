package com.zhihaoscm.domain.bean.vo;

import java.util.List;

import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.MemberPermission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerVo {

	/**
	 * 客户信息
	 */
	private Customer customer;

	/**
	 * 用户的会员权限
	 */
	private MemberPermission memberPermission;

	/**
	 * 客户个人认证信息
	 */
	private CustomerPersonalCertification personalCertification;

	/**
	 * 客户发票抬头信息
	 */
	private List<CustomerInvoiceHeader> invoiceHeaders;

	/**
	 * 发票抬头条数，列表展示的字段
	 */
	private Integer invoiceHeaderCount;

	/**
	 * 客户银行信息
	 */
	private List<CustomerBank> banks;

	/**
	 * 银行卡条数, 列表展示的字段
	 */
	private Integer bankCount;

	/**
	 * 客户收货地址列表
	 */
	private List<CustomerReceivingAddress> addresses;

	/**
	 * 地址簿条数
	 */
	private Integer addressCount;

	/**
	 * 商品数量
	 */
	private Long productQuantity;

	/**
	 * 客户常跑航线信息
	 */
	private List<FrequentShipRoute> frequentShipRoutes;

	/**
	 * 微信昵称
	 */
	private String nickName;

	/**
	 * 机构认证信息
	 */
	private InstitutionApply institutionApply;

	/**
	 * 是否能解除组织机构认证 0否 1是
	 */
	private Integer isCancelInstitutionApply;
}
