package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

/**
 * 收款定义
 */
public interface ReceivePaymentDef {

	/**
	 * 状态
	 */
	@Getter
	enum State {

		PENDING_PAYMENT(1, "待支付"), PAID(2, "已支付"), UNPAID(3, "未支付"),;

		private final Integer code;

		private final String name;

		private static final Map<Integer, ReceivePaymentDef.State> MAPPING;

		State(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

		static {
			{
				Map<Integer, ReceivePaymentDef.State> mapping = new HashMap<>();
				for (ReceivePaymentDef.State value : ReceivePaymentDef.State
						.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static ReceivePaymentDef.State from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}
	}

	/**
	 * 支付类型
	 */
	@Getter
	enum PaymentType {

		ORDER_PAYMENT(5, "订单货款", 3),
		ORDER_DEPOSIT(1, "订单定金",3),
		ADVANCE_PAYMENT(2, "排期货款", 3),
		SHIPPING_PAYMENT(3, "发航货款",1),
		WAREHOUSE_OPENING_PAYMENT(4, "开仓货款", 1),;

		private final Integer code;

		private final String name;

		private final Integer hour;

		private static final Map<Integer, ReceivePaymentDef.PaymentType> MAPPING;

		PaymentType(Integer code, String name, Integer hour) {
			this.code = code;
			this.name = name;
			this.hour = hour;
		}

		static {
			{
				Map<Integer, ReceivePaymentDef.PaymentType> mapping = new HashMap<>();
				for (ReceivePaymentDef.PaymentType value : ReceivePaymentDef.PaymentType
						.values()) {
					mapping.put(value.getCode(), value);
				}
				MAPPING = mapping;
			}
		}

		public static ReceivePaymentDef.PaymentType from(final Integer code) {
			return MAPPING.get(code);
		}

		public boolean match(final Integer code) {
			return this.code.equals(code);
		}
	}
}
