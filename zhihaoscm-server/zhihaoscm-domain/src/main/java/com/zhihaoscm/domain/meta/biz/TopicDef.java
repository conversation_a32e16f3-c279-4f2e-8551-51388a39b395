package com.zhihaoscm.domain.meta.biz;

import java.util.HashMap;
import java.util.Map;

import org.springframework.util.Assert;

import lombok.Getter;

public interface TopicDef {

    /**
     * 用户发生变化的topic
     */
    String USER_CHANGE = "user_change";

    String USER_CHANGE_GROUP = "user_change_group";

    /**
     * 客户端用户发生变化的topic
     */
    String CUSTOM_USER_CHANGE = "custom_user_change";

    String CUSTOM_USER_CHANGE_GROUP = "custom_user_change_group";

    /**
     * 客户企业主体发生变更
     */
    String CUSTOMER_ENTERPRISE_CHANGE = "customer_enterprise_change";

    String CUSTOMER_ENTERPRISE_CHANGE_GROUP = "customer_enterprise_change_group";

    /**
     * 订单确认状态
     */
    String PAYMENT_CHECK_STATE = "payment_check_state";

    String PAYMENT_CHECK_STATE_GROUP = "payment_check_state_group";

    /**
     * 开会员topic
     */
    String MEMBER_OPEN = "member_open";

    String MEMBER_OPEN_GROUP = "member_open_group";

    /**
     * customer的相关topic
     */
    String CUSTOMER_ACCOUNT_MIND = "customer_account_mind";

    String CUSTOMER_ACCOUNT_MIND_GROUP = "customer_account_mind_group";

    /**
     * 发送通知
     */
    String SEND_NOTICE = "send_notice";

    String SEND_NOTICE_GROUP = "send_notice_group";

    /**
     * 应用管理消息组
     */
    String APPLICATION_CONNECTOR = "application_connector";

    String APPLICATION_CONNECTOR_GROUP = "application_connector_group";

    /**
     * 设备扫码上传文件消息
     */
    String DEVICE_UPLOAD_FILE_CONNECTOR = "device_upload_file_connector";

    String DEVICE_UPLOAD_FILE_CONNECTOR_GROUP = "device_upload_file_connector_group";

    /**
     * app扫码登录消息
     */
    String QR_LOGIN = "qr_login";

    String QR_LOGIN_GROUP = "qr_login_group";

    @Getter
    enum Type {

        REFRESH_STATE(1, "用户状态刷新"),

        CHOOSE_ENTERPRISE(2, "强制重新选择企业"),

        REFRESH_SESSION_LIST(3, "刷新AI会话历史记录列表"),

        @Deprecated
        CUSTOMER_ENTERPRISE_CHANGE(4, "客户企业主体发生变更"),

        DEVICE_UPLOAD_FILE_TIME_OUT(5, "二维码过期消息"),

        AFTER_DEVICE_UPLOAD_FILE(6, "设备扫码上传文件成功消息"),

        QR_LOGIN_SCAN(7, "app扫码登录通知已经被扫描"),

        QR_LOGIN_CONFIRM(8, "app扫码登录已经确认"),

        QR_LOGIN_EXPIRED(9, "app扫码登录通知已经过期"),

        QR_LOGIN_CANCEL(10, "app扫码登录通知取消登录")
        ;

        private final Integer code;

        private final String desc;

        private static final Map<Integer, Type> MAPPING;

        static {
            {
                Map<Integer, Type> mapping = new HashMap<>();
                for (Type value : Type.values()) {
                    mapping.put(value.getCode(), value);
                }
                MAPPING = mapping;
            }
        }

        Type(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static Type from(final Integer code) {
            Type type = MAPPING.get(code);
            Assert.notNull(type, "wrong topic type: " + code);
            return type;
        }

        public boolean match(final Integer code) {
            return this.code.equals(code);
        }

    }

}
