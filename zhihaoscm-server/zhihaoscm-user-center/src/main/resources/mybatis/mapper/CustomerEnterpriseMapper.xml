<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhihaoscm.usercenter.core.mapper.CustomerEnterpriseMapper">

    <select id="invitedPaging" resultType="com.zhihaoscm.domain.bean.entity.CustomerEnterprise">
        SELECT
        ce.id,
        ce.tenant_id tenantId,
        ce.main_account_id mainAccountId,
        ce.sub_account_id subAccountId,
        ce.third_sub_account_id thirdSubAccountId,
        ce.state,
        ce.del,
        ce.origin,
        ce.created_by createdBy,
        ce.created_time createdTime,
        ce.updated_by updatedBy,
        ce.updated_time updatedTime
        FROM t_customer_enterprise ce
        LEFT JOIN t_customer c ON c.id = ce.main_account_id
        WHERE ce.sub_account_id = #{actualAccountId}
        AND ce.del = 0
        AND ce.state != 3
        <if test="state != null">
            AND ce.state = #{state}
        </if>
        <if test="institutionName != null and institutionName != ''">
            AND c.institution_name LIKE CONCAT('%', #{institutionName}, '%')
        </if>
        ORDER BY ce.created_time DESC
    </select>

    <select id="paging" resultType="com.zhihaoscm.domain.bean.entity.CustomerEnterprise">
        SELECT
        ce.id,
        ce.tenant_id tenantId,
        ce.main_account_id mainAccountId,
        ce.sub_account_id subAccountId,
        ce.third_sub_account_id thirdSubAccountId,
        ce.state,
        ce.del,
        ce.origin,
        ce.created_by createdBy,
        ce.created_time createdTime,
        ce.updated_by updatedBy,
        ce.updated_time updatedTime,
        ce.account_state accountState,
        ce.receipt_sms receiptSms
        FROM t_customer_enterprise ce
        LEFT JOIN t_customer c ON c.id = ce.sub_account_id
        WHERE ce.main_account_id = #{mainAccountId}
        AND ce.del = 0
        <if test="state != null">
            AND ce.state = #{state}
        </if>
        <if test="accountState != null">
            AND ce.account_state = #{accountState}
        </if>
        <if test="searchParam != null and searchParam != ''">
            AND (c.mobile LIKE CONCAT('%', #{searchParam}, '%') or c.real_name LIKE CONCAT('%', #{searchParam}, '%'))
        </if>
        <choose>
            <!-- 如果 state 不为空，先按 account_state 倒序，再按 sortKey 和 sortDirection 排序 -->
            <when test="state != null">
                ORDER BY ce.account_state DESC, ${sortKey} ${sortDirection}
            </when>
            <!-- 如果 state 为空，仅按 sortKey 和 sortDirection 排序 -->
            <otherwise>
                ORDER BY ${sortKey} ${sortDirection}
            </otherwise>
        </choose>
    </select>

</mapper>
