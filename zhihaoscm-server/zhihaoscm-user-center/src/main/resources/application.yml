server:
    port: 8082
# Application Configuration
application:
    config:
        # jwt密钥
        jwt-secret: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9"
        url: https://www.zhihaoscm.cn/dev/zhihaoscm-custom/login/bindPhone
        admin:
            login-success-page: "https://www.zhihaoscm.cn/dev/zhihaoscm-admin/mytask/index"
            forbidden-page: "https://www.zhihaoscm.cn/dev/zhihaoscm-admin/403"
            hw-success-page: "https://dev.zhihaoscm.cn/ai-corpus"
# spring相关配置
spring:
    main:
        allow-bean-definition-overriding: true
        allow-circular-references: true
    cloud:
        nacos:
            discovery:
                # dev 开启服务发现
                register-enabled: false
                enabled: false
            config:
                username: hotfix
                enabled: false
        # Feign客户端配置
        openfeign:
            client:
                config:
                    zhihaoscm-service:
                        requestInterceptors:
                            - com.zhihaoscm.common.gray.GrayscaleFeignInterceptor

    # 数据库配置
    datasource:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: *****************************************************************************************************************************
        username: root
        password: 123456
redis:
    cache:
        host: ***************
        port: 6379
        password: 123456
        database: 0
        timeout: 10000
        ttl: 30d
        jedis:
            pool:
                max-active: 8
                max-idle: 8
                min-idle: 0
                max-wait: -1ms
# spring doc config
springdoc:
    swagger-ui:
        enabled: true
    # 指定包下面的接口生成文档
    packages-to-scan: com.zhihaoscm.usercenter.resource.custom,com.zhihaoscm.usercenter.resource.admin
    api-docs:
        path: /v4/api-docs
    group-configs:
        -   group: 'admin'
            paths-to-match: '/admin/**'
            packages-to-scan:
                # 配置接口文档扫描包路径
                - com.zhihaoscm.usercenter.resource.admin
        -   group: 'custom'
            paths-to-match: '/custom/**'
            packages-to-scan:
                # 配置接口文档扫描包路径
                - com.zhihaoscm.usercenter.resource.custom

rocketmq:
    name-server: ***************:9876
    # 生产者
    producer:
        group: testGroup
        # 消息发送超时时间
        send-message-timeout: 3000
        # 消息最大长度4M
        max-message-size: 4096
        # 消息发送失败重试次数
        retry-times-when-send-failed: 3
        # 异步消息发送失败重试次数
        retry-times-when-send-async-failed: 2
        # 消息发送超时时间
        sendMessageTimeout: 50000
    # 消费者
    consumer:
        group: testGroup
        # 每次提取的最大消息数
        pull-batch-size: 5
    enhance:
        enabledIsolation: true
        environment: ${spring.cloud.nacos.config.username}

wxw:
    url: https://qyapi.weixin.qq.com/cgi-bin
    corpId: ww8f15478974c3a925
    corpSecret: Pj3VZiV_9DoBdwlZbwysCh_fLliLETd4CH6Mw4LHmVQ
    sToken: b3M8J1QadPFB7OcQxKYS
    encodingAESKey: 1VPBQchko1n34FWidFTIS9sRCfG3h5L40InwBiqYGwP
    redirectUri: https://www.zhihaoscm.cn/dev/zhihaoscm-admin/login
    agentId: 1000002

wx-pc:
    appid: wx52bad7fb1d5b2565
    secret: 94108ac9a31154180e4bef585a41f105
    grant_type: authorization_code
lianyun-app:
    appid: wx4a3ec0e66f85ddcd
    secret: 5f61ad0ace1f5fd7c505f07ce1e06d30
    grant_type: authorization_code
chuanwu-app:
    appid: wxac3ce68b24e9044d
    secret: f42424bed456603110f9fc3dd3fd0211
    grant_type: authorization_code

# miniapp相关配置
wx-mini-app:
    url: https://api.weixin.qq.com
    appid: wx8c10c99edf9d8908
    secret: 7ed34dee4cf3666eb4f87f455bb19f1f
    carrier-appid: wxc4bf3cdba4aff288
    carrier-secret: a9349d147ad0fadcaac3a73a436dc399
    env-version: trial
    official-appid: wxd5f92dce453a98a1
    official-secret: 24e635b9056d3ea57a2e770f4d8309aa

# 契约锁相关配置
qiyuesuo:
    url: https://openapi.qiyuesuo.cn
    appToken: ZXE2kLBXKR
    appSecret: ******************************
    goodsCategoryId: 3210504295250256791
    reconciliationCategoryId: 3210483514868867600
    goodsReceiptCategoryId: 3210529196300337640
    creator-contact: 18607096269
    contract-category-id: 3208362134912209735
    secret-key: JvKkUNeyS6wqBnmP
    personal-callback-url: http://*************:10005/dev/api/custom/callback/personal/certification
    enterprise-callback-url: http://*************:10005/dev/api/custom/callback/enterprise/certification
    lyAppToken: SMb8m0jbd7
    lyAppSecret: ******************************

aliyun:
    captcha:
        accessKey: LTAI5tNkZziT4Un6GBEkwvKj
        secretKey: ******************************
        endpoint: captcha.cn-shanghai.aliyuncs.com
        app-code: 5e77fea8fe414f5c8c776e3c513fa17e
    oss:
        access-key: LTAI5tDxzE2RmUGyCAjvJYLR
        secret-key: ******************************
        bucket: zhihaoscm-dev
        endpoint: oss-cn-hangzhou.aliyuncs.com
        url-expire: 7200
        cache-expire: 5400
        custom-domain-name: devstatic.zhihaoscm.com
    sms:
        access-key: LTAI5tNn8xdUUdqidPD69UWL
        secret-key: ******************************
        endpoint: dysmsapi.aliyuncs.com
        connect-timeout: 5
        read-timeout: 10
        # 登录验证码
        loginVerifyCode: SMS_465660519
        # 我有意向验证码
        intentionVerifyCode: SMS_465926750
        # 换绑流程通过后 (修改)
        changeCustomerMobileCode: SMS_489380136
        # 会员续费提醒 模板Code （修改）
        memberRenewalReminder: SMS_489415125

        ####### 短信链接页面配置 #######
        intendDetailPage : /page/business/common/intend/intendDetail/intendDetail
        # 付款详情
        paymentDetailPage : /page/business/common/purchasePayment/purchasePaymentDetail/purchasePaymentDetail
        # 收款详情
        receiptDetailPage : /page/business/common/purchasePayment/purchasePaymentDetail/purchasePaymentDetail
        # 找船需求详情
        shipDetailPage : /page/shipDemand/myShipDemand/myShipDemandDetail/myShipDemandDetail
        # 船运单详情
        transportOrderPage : /page/business/customer/transportOrderShip/detail/detail
        # 承运商船运单详情
        transferTransportOrderPage : /pages/transportOrderShipDetail/detail
        # 承运商抢单详情
        transferShipRequirePage : /pages/shipRequire/order/detail/detail
        # 会员页
        membershipPage : /page/vip/membership/membership
        # 承运商会员页
        transferMembershipPage : /pages/vip/membership/membership
        # 受邀请记录详情
        invitationRecordPage : /page/setting/invitationRecord/detail/detail
        # 提货单详情
        purchaseGoodPage : /page/business/common/purchaseGoods/purchaseGoodsDetail/purchaseGoodsDetail
        # 项目详情
        projectPage: /page/business/common/project/projectDetail/projectDetail
        # 短信链接小程序 URL Link 失效类型，失效时间：0，失效间隔天数：1
        expireType: 1
        # 短信链接失效时间，默认最长时限30天
        expireNum: 30
        # app短信链接
        appLinkUrl: https://dev.zhihaoscm.cn/app-version/index

    auth:
        cloudAccessKey: LTAI5tQVNFbL7VgkpjwX74jx
        cloudAccessSecret: ******************************
        schemaName: FC220000008845065
        appName: 志豪链云
        endpoint: dypnsapi.aliyuncs.com

mybatis-plus:
    # 别名 bean包
    typeAliasesPackage: com.lianxin.tffm.core.bean.entity
    mapperLocations: classpath:mybatis/mapper/**/*.xml
    global-config:
        banner: false
        db-config:
            update-strategy: ignored
    configuration:
        mapUnderscoreToCamelCase: true
        lazyLoadingEnabled: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    type-handlers-package: com.zhihaoscm.common.mybatis.plus.json.handler,com.zhihaoscm.domain.bean.json.handler
# 支付宝支付
alipay:
    ali-pay-cert-path: ${user.dir}/zhihaoscm-server/zhihaoscm-service/src/main/resources/pay/alipay/sandbox/alipayPublicCert.crt
    ali-pay-root-cert-path: ${user.dir}/zhihaoscm-server/zhihaoscm-service/src/main/resources/pay/alipay/sandbox/alipayRootCert.crt
    app-cert-path: ${user.dir}/zhihaoscm-server/zhihaoscm-service/src/main/resources/pay/alipay/sandbox/appPublicCert.crt
    app-id: 9021000137681624
    gateway-url: https://openapi-sandbox.dl.alipaydev.com/gateway.do
    notify-url: http://fa46en.natappfree.cc/payment/alipay/callback
    private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCbJ2gHWfcoJnrzHuZ71z/Asxw4udxrZuPiQbIvtD/iZlpvaEdjUZXp+q1zYcOl0LTA13C0V9JJTqyT5tNILd6zC2HFnwsRWWMu6IyRP+u5H8XnRYzKOvQpg7I/e15JLNXht7Em0qHo6Z/UsRRl6NR1yVgwAZNXs/0olvwkebAQNUjDO7D4KcNWcGsDFZLUty3eUNP34HiIEyqIFFsDeMmHmHJ2hCLICjgwz9OvaW/tcJ48XmZN8QbrpbiCShzuFkXYFfnI+atN9BCWqbBcY+G3IGP2/svSYlpR8mSCIDVWFH8E5VpGWdBqsaytD3cR371Eavid63WKIK7+k7KFaeUTAgMBAAECggEAXVydEK9mNlbRbTKO5r5jsoUU2l2c8VP8lM2Ld4EzX/zPNnK9YlpZp24cqW2O2Vx5KAF75ElkVJBu8KDZSnmvpHWY494MPuhW8ock7tJT+hXQdJY5XgeTnNeOjvyhg87i9OwgQYhFvW23q7jS2W/9SNNbqaeJDiFUmPQhzaEb/OPimyO/CUqPsEKf7yBcAUAwIfzUDyzW2gZ4N+jCGcl5i2C+sEbEble8IODebL4rxASD8wyzLCpmMBQkL8SuiyxkuWHJQ5nJyDJHaUaq3pwwxiW1Hf68FMCS3HMh4nquishfA96WrHGcZ2dS60iyxlIGSG53T1SP0w2+wEdIcTogQQKBgQDZiWpowoNtmbd/UVR0V+KpdCiMRZoXwmmKWYWQZdk4vb1Ia3X+UoPUtRXfBJUJH6WqQgtQ7zkYEedH3ks3F7QxAiCs4CLvtsv3/C2/KUmx/FjAifu+ZfrbaHMvLKImFh19hOQUPewPOqwctqh5ePi8MMPcj6kM4qa8Hyf8LyfbawKBgQC2lksnJeDAmG7CmsW/J9DY16Z2Tji7jpsYmOifBy2oExd4WbJ6N81qdtaZ08zk4ZO/0JEaJGNsiVZxp2RBKUYd1aFcWbGXnU9pTx3xofvWBwa3AZdONlasFiVMaEnIe5BtkcgHGOpVp8DXRkbOIzf9Foe33eKmtzpEoH3bnvju+QKBgGHjr0mS9jd0q5kUTQ/JmIquZayxiWTVrE6AeFUBL92TrECRcthN41rtXfksg7BGWHMkIxl5fNSzAcNaabhR51kCXxfMCIycUm62QRO7jZkVOA70SJ9mHptrBiIdUJ4Y545bJZNiRf6YZWjKXUfEQxHUU97FmsncmDEhSAn4YlQfAoGAKp+NLbde7zbmbFF7JjOiQmydNHLwNYCF3kkbzLv9QYYrbXHrAzLxpg/V6xUReMDcH+QNa3/hId4xqth7w+ZhsudDAu8BUTZs6lWI1PaOdpkvTRt2+dAfjY6h6GNUDhQiVCG7A0UitxoxH1ANiWOZV2CiEB8hIPAXDxw0JUfILukCgYEAow8LcCK/uZPZaNH9jyqOs/xKjZ1iFvB7F47gu5nqQR0cVVE13/RJn+4Qfdp6ag5FiPaUUvx3yg1PQBzplWDSV1iRn7/5Z2XTr9HxQ8dvbLG2lrLmKA5sLR9VwKqLbIR2JigNb0TKm73oYPzFU7bXF55JZQYsNpCRSc4zww56Pfw=
    public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjgeLoN0PRQKIQb8GYvzhfCvee1x5pBkLF+dRpNhDAUBhUlMx/EQzxUdq2uf8rCAYV5QMWhgqtr84phuymzuRSQ/sr6NfS83AmTlsl7Hk4PkjOcDVyhbsT3KNpZU/Xyz5fw4mr1ibDZjcb/E9f7vcUuxfxwJQb0MgWQt4jtgFgFb4eppjgwmaxakyv8/3GiIvjt8NHjdK68dn3v/y4jRWTv9CVbuu3IbEb4rLLfmk6vjOdCTdVBG5j7I8DNcwQCRjgoYryzI83DM9Ub7pBwpAdIn2wmGNSxyae87ccXUVNA6JJSoeozXMPFFSpZQbPKYUWR9bFaTqgfvAsndAsBhAkQIDAQAB
    expire-time: 10
    qrcode-width: 138
