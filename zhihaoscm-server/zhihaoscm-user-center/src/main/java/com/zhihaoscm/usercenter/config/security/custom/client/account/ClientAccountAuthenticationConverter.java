package com.zhihaoscm.usercenter.config.security.custom.client.account;

import java.util.Objects;
import java.util.Optional;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationConverter;

import com.zhihaoscm.common.api.util.CommonWebUtils;
import com.zhihaoscm.common.util.utils.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

public class ClientAccountAuthenticationConverter
		implements AuthenticationConverter {

	@Override
	public Authentication convert(HttpServletRequest request) {
		Optional<ClientAccountLoginForm> login = CommonWebUtils
				.getRequestBody(request, ClientAccountLoginForm.class);

		if (login.isEmpty()) {
			throw new BadCredentialsException(
					"missing account or role or password or origin");
		}

		ClientAccountLoginForm loginForm = login.get();

		if (StringUtils.isBlank(loginForm.getAccount())) {
			throw new BadCredentialsException("missing account");
		}
		if (StringUtils.isBlank(loginForm.getPassword())) {
			throw new BadCredentialsException("missing password");
		}
		if (Objects.isNull(loginForm.getAppType())) {
			throw new BadCredentialsException("missing appType");
		}
		if (Objects.isNull(loginForm.getOrigin())) {
			throw new BadCredentialsException("missing origin");
		}
		return new ClientAccountAuthentication(login.get());
	}

}
