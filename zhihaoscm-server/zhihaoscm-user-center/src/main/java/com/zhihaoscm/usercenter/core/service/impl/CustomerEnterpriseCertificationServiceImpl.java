package com.zhihaoscm.usercenter.core.service.impl;

import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerChangeMobile;
import com.zhihaoscm.domain.bean.entity.CustomerPersonalCertification;
import com.zhihaoscm.domain.bean.entity.InstitutionApply;
import com.zhihaoscm.domain.bean.vo.CustomerChangeMobileVo;
import com.zhihaoscm.domain.bean.vo.CustomerPersonalAuthVo;
import com.zhihaoscm.domain.bean.vo.CustomerVo;
import com.zhihaoscm.domain.meta.biz.CertificationDef;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.InstitutionApplyDef;
import com.zhihaoscm.usercenter.core.service.*;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 客户企业认证记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Slf4j
@Service
public class CustomerEnterpriseCertificationServiceImpl
		implements CustomerEnterpriseCertificationService {

	@Autowired
	private CustomerService customerService;

	@Autowired
	private PersonalCertificationService personalCertificationService;

	@Autowired
	private InstitutionApplyService institutionApplyService;

	@Autowired
	private CustomerChangeMobileService customerChangeMobileService;

	@Override
	public Optional<CustomerVo> certDetail(Long customerId) {
		CustomerVo customerVo = new CustomerVo();
		// 个人认证详情
		Optional<CustomerPersonalCertification> personalOptional = personalCertificationService
				.findByCustomerIdLatest(customerId,
						CertificationDef.CertificationState.VALID.getCode());
		personalOptional.ifPresent(person -> {
			if (CertificationDef.CertificationStatus.PASS
					.match(person.getStatus())) {
				this.dataDesensitization(person);
			} else {
				CustomerPersonalAuthVo personalVo = personalCertificationService
						.personalAuthResult(customerId);
				person.setStatus(
						personalVo.getPersonalCertification().getStatus());
			}
			customerVo.setPersonalCertification(person);
		});

		// 用户信息
		customerService.findById(customerId).ifPresent(customerVo::setCustomer);

		return Optional.of(customerVo);
	}

	/**
	 * 数据脱敏
	 *
	 * @param certification
	 */
	private void dataDesensitization(
			CustomerPersonalCertification certification) {
		if (StringUtils.isNotBlank(certification.getName())) {
			certification.setName(
					CustomerDef.FILTER_LEGAL_REPRESENTATIVE_SENSITIVE_INFO
							.apply(certification.getName()));
		}
		if (StringUtils.isNotBlank(certification.getIdNo())) {
			certification.setIdNo(
					CustomerDef.FILTER_SOCIAL_CREDIT_CODE_SENSITIVE_INFO
							.apply(certification.getIdNo()));
		}
	}

	/**
	 * 换绑手机号-查询企业认证详情
	 *
	 * @param customerId
	 * @return
	 */
	@Override
	public Optional<CustomerChangeMobileVo> cmCertDetail(Long customerId) {
		CustomerChangeMobileVo customerChangeMobileVo = new CustomerChangeMobileVo();
		// 换绑手机号详情
		CustomerChangeMobile customerChangeMobile = customerChangeMobileService
				.findByCustomerId(customerId).orElse(null);
		// 用户信息
		Customer customer = customerService.findById(customerId).orElse(null);
		if (Objects.nonNull(customerChangeMobile)) {
			customerChangeMobileVo
					.setCustomerChangeMobile(customerChangeMobile);
		}
		// 个人认证详情
		Optional<CustomerPersonalCertification> personalOptional = personalCertificationService
				.findByCustomerIdLatest(customerId,
						CertificationDef.CertificationState.CHANGED.getCode());
		personalOptional.ifPresent(person -> {
			if (CertificationDef.CertificationStatus.PASS
					.match(person.getStatus())) {
				this.dataDesensitization(person);
			}
			customerChangeMobileVo.setPersonalCertification(person);
		});
		// 客户的组织机构认证信息
		InstitutionApply institutionApply = institutionApplyService
				.findByCustomerIdAndStateLatest(customerId, null).orElse(null);
		if (Objects.nonNull(customer)) {
			// 本系统已认证
			if (CommonDef.Symbol.YES.match(customer.getApplyState())) {
				// 当用户的组织机构名称为空时，取组织机构认证的名称认证
				if (StringUtils.isBlank(customer.getInstitutionName())
						&& Objects.nonNull(institutionApply)) {
					customerChangeMobileVo.setInstitutionName(
							institutionApply.getInstitutionName());
				}
				// 其他情况取用户的历史机构名称
				else {
					customerChangeMobileVo
							.setInstitutionName(customer.getInstitutionName());
				}
				// 组织机构认证状态设置为已认证
				customerChangeMobileVo
						.setApplyState(CommonDef.Symbol.YES.getCode());
			} else if (Objects.nonNull(institutionApply)
					&& InstitutionApplyDef.State.APPROVED
							.match(institutionApply.getState())) {
				// 组织机构认证状态设置为已认证
				customerChangeMobileVo
						.setApplyState(CommonDef.Symbol.YES.getCode());
			} else {
				customerChangeMobileVo
						.setApplyState(CommonDef.Symbol.NO.getCode());
			}
		}
		customerChangeMobileVo.setInstitutionApply(institutionApply);
		return Optional.of(customerChangeMobileVo);
	}
}
