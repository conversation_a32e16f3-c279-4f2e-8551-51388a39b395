package com.zhihaoscm.usercenter.config.security.custom.wx;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextHolderStrategy;
import org.springframework.security.web.context.RequestAttributeSecurityContextRepository;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.util.Assert;
import org.springframework.web.filter.OncePerRequestFilter;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.CommonWebUtils;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UUIDUtil;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.CustomerJsonInfo;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.meta.error.ErrorCodeDef;
import com.zhihaoscm.usercenter.config.security.custom.RedisJwtTokenService;
import com.zhihaoscm.usercenter.config.security.custom.TokenResponse;
import com.zhihaoscm.usercenter.core.service.*;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WxCreateFilter extends OncePerRequestFilter {

	private final SecurityContextHolderStrategy securityContextHolderStrategy = SecurityContextHolder
			.getContextHolderStrategy();

	private final SecurityContextRepository securityContextRepository = new RequestAttributeSecurityContextRepository();

	private final StringRedisClient redisClient;

	private final CustomerService customerService;

	private final RequestMatcher requestMatcher;

	private final RedisJwtTokenService tokenService;

	private final PromotionService promotionService;

	private final ActivationCodeService activationCodeService;

	private final PromotionDetailService promotionDetailService;

	private final BusinessConfigService businessConfigService;

	private final CustomerWxService customerWxService;

	private final CustomerChangeMobileService customerChangeMobileService;

	private final SecuritySettingDeviceService securitySettingDeviceService;

	public WxCreateFilter(StringRedisClient redisClient,
			CustomerService customerService, RequestMatcher requestMatcher,
			RedisJwtTokenService tokenService,
			PromotionService promotionService,
			ActivationCodeService activationCodeService,
			PromotionDetailService promotionDetailService,
			BusinessConfigService businessConfigService,
			CustomerWxService customerWxService,
			CustomerChangeMobileService customerChangeMobileService,
			SecuritySettingDeviceService securitySettingDeviceService) {
		this.redisClient = redisClient;
		this.tokenService = tokenService;
		this.customerService = customerService;
		this.requestMatcher = requestMatcher;
		this.promotionService = promotionService;
		this.activationCodeService = activationCodeService;
		this.promotionDetailService = promotionDetailService;
		this.businessConfigService = businessConfigService;
		this.customerWxService = customerWxService;
		this.customerChangeMobileService = customerChangeMobileService;
		this.securitySettingDeviceService = securitySettingDeviceService;
	}

	@Override
	protected void doFilterInternal(@NonNull HttpServletRequest request,
			@NonNull HttpServletResponse response,
			@NonNull FilterChain filterChain)
			throws ServletException, IOException {
		// 校验请求url是否匹配
		if (!this.requestMatcher.matches(request)) {
			if (logger.isTraceEnabled()) {
				logger.trace("Did not match request to " + this.requestMatcher);
			}
			filterChain.doFilter(request, response);
			return;
		}
		try {
			String tenantId = request.getHeader("TenantId");
			// 校验参数
			WxCreateForm createForm = this.validate(request);

			List<CustomerChangeMobile> customerChangeMobiles = customerChangeMobileService
					.findByMobile(createForm.getMobile());
			if (CollectionUtils.isNotEmpty((customerChangeMobiles))) {
				throw new BadRequestException(ErrorCode.CODE_30059012);
			}

			// 校验账号是否注销
			Customer customer1 = customerService
					.findByMobileOrEmail(createForm.getMobile()).orElse(null);
			if (Objects.nonNull(customer1)) {
				if (CustomerDef.State.CANCELLED.match(customer1.getState())) {
					throw new BadRequestException(ErrorCode.CODE_30059024);
				}
			}

			// 设置用户code
			Promotion promotion = null;
			String activationCode;
			if (StringUtils.isNotBlank(createForm.getPromotionCode())) {
				promotion = promotionService
						.findByCode(createForm.getPromotionCode()).orElse(null);
			}
			BusinessConfig businessConfig = businessConfigService.findByType(
					BusinessConfigDef.Type.REGISTER_GIVE_VIP_SETTING.getCode())
					.orElse(null);
			Customer convert = createForm.convert(promotion);
			convert.setCode(AutoCodeDef.CREATE_AUTO_CODE.apply(redisClient,
					String.valueOf(CustomerDef.Role.from(createForm.getAppType())
							.getCode()),
					RedisKeys.Cache.CUSTOMER_ID_GENERATOR, "", 7,
					AutoCodeDef.DATE_TYPE.NONE));

			convert.setAppType(createForm.getAppType());
			// 注册
			Customer customer = customerService.create(convert);

			// 绑定设备
			SecuritySettingDevice securitySettingDevice = new SecuritySettingDevice();
			securitySettingDevice.setDeviceCode(createForm.getDeviceCode());
			securitySettingDevice.setCustomerId(customer.getId());
			securitySettingDevice.setLoginTime(LocalDateTime.now());
			securitySettingDeviceService.create(securitySettingDevice);

			if (Objects.nonNull(createForm.getId())) {
				CustomerWx customerWx = customerWxService
						.findOne(createForm.getId()).orElse(null);

				if (Objects.isNull(customerWx)) {
					throw new BadRequestException(ErrorCode.CODE_30059003);
				}

				if (CommonDef.Symbol.NO.getCode()
						.equals(customerWx.getType())) {
					customer.setUnionId(customerWx.getUnionId());
				} else {
					customer.setSubId(customerWx.getUnionId());
				}
				customerService.updateAllProperties(customer);
				customerWx.setCustomerId(customer.getId());
				customerWx.setBindTime(LocalDateTime.now());
				customerWxService.updateAllProperties(customerWx);
			}
			// 组装代理信息
			CustomerLoginVo customerLoginVo = new CustomerLoginVo();
			customerLoginVo.setActualAccount(customer);
			customerLoginVo.setRole(createForm.getAppType());
			customerLoginVo.setProxyAccount(customer);
			// 生成token
			String token = tokenService.createAccessToken(new LoginUser<>(
					customerLoginVo, null,
					createForm.getMobile(),
					List.of()),
					createForm.getOrigin());
			// 刷新SecurityContext
			SecurityContext context = this.securityContextHolderStrategy
					.createEmptyContext();
			context.setAuthentication(new WxAuthentication(customer));
			this.securityContextHolderStrategy.setContext(context);
			this.securityContextRepository.saveContext(context, request,
					response);
			Customer customerWithCreatedTime = customerService
					.findById(customer.getId()).orElse(new Customer());
			// 若用户不是从推广码处进入注册也不是链云分身版用户且注册送会员配置开启了
			if (Objects.nonNull(businessConfig) && StringUtils.isBlank(tenantId)
					&& (Objects.isNull(promotion))) {
				activationCode = this.assembleActivationCode(
						customerWithCreatedTime, businessConfig);
			} else {
				// 若用户从推广码出进入注册且该推广码支持赠送会员则为该会员生成激活码
				activationCode = this.assembleActivationCode(
						customerWithCreatedTime, promotion);
				// 并且在推广详情中增加一条记录
				this.assemblePromotionDetail(customerWithCreatedTime, promotion,
						activationCode);
				// 为该推广码增加一名未付费用户
				if (Objects.nonNull(promotion)) {
					promotionService.addUnpaid(promotion.getId());
				}
			}
			// 若通过礼品卡进入则将用户信息绑定到激活码上
			if (StringUtils.isNotBlank(createForm.getActivationCode())) {
				this.bindCustomer(createForm.getActivationCode(),
						customerWithCreatedTime);
			}
			// 返回token
			CommonWebUtils.renderJson(response,
					new ApiResponse<>(
							new TokenResponse(token, null, activationCode)),
					HttpStatus.OK.value());
		} catch (BadRequestException e) {
			ErrorCodeDef def = ErrorCodeDef.from(e.getMessage());
			CommonWebUtils.renderJson(response,
					new ApiResponse<>(def.getCode(), def.getMessage()),
					HttpStatus.BAD_REQUEST.value());
		}
	}

	private void bindCustomer(String activationCode,
			Customer customerWithCreatedTime) {
		ActivationCode code = activationCodeService.findByCode(activationCode)
				.orElse(new ActivationCode());
		code.setCustomerId(customerWithCreatedTime.getId());
		CustomerJsonInfo customerInfo = new CustomerJsonInfo();
		customerInfo.setMobile(customerWithCreatedTime.getMobile());
		customerInfo.setId(customerWithCreatedTime.getId());
		customerInfo.setCreatedTime(customerWithCreatedTime.getCreatedTime());
		customerInfo.setCode(customerWithCreatedTime.getCode());
		code.setCustomerInfo(customerInfo);
		code.setHasBind(ActivationCodeDef.HasBind.YES.getCode());
		activationCodeService.update(code);
	}

	private void assemblePromotionDetail(Customer customer, Promotion promotion,
			String activationCode) {
		if (Objects.isNull(promotion)
				|| !PromotionDef.State.EFFECTIVE.match(promotion.getState())) {
			return;
		}

		PromotionDetail promotionDetail = new PromotionDetail();
		promotionDetail.setPromotionCode(promotion.getCode());
		promotionDetail.setActivationCode(activationCode);
		promotionDetail.setCustomerId(customer.getId());
		promotionDetail.setPromotedCreatedTime(customer.getCreatedTime());

		CustomerJsonInfo customerInfo = new CustomerJsonInfo();
		customerInfo.setMobile(customer.getMobile());
		customerInfo.setId(customer.getId());
		customerInfo.setCreatedTime(customer.getCreatedTime());
		customerInfo.setCode(customer.getCode());
		promotionDetail.setCustomerInfo(customerInfo);

		promotionDetailService.create(promotionDetail);
	}

	private String assembleActivationCode(Customer customer,
			Promotion promotion) {
		if (Objects.isNull(promotion)
				|| PromotionDef.HasGift.NO.match(promotion.getHasGift())
				|| !PromotionDef.State.EFFECTIVE.match(promotion.getState())
				|| promotion.getRedemptionValidity()
						.isBefore(LocalDate.now())) {
			return null;
		}
		CustomerJsonInfo customerInfo = new CustomerJsonInfo();
		customerInfo.setMobile(customer.getMobile());
		customerInfo.setId(customer.getId());
		customerInfo.setCreatedTime(customer.getCreatedTime());
		customerInfo.setCode(customer.getCode());

		ActivationCode activationCode = new ActivationCode();
		String generatedCode = this.generateActivationCode();
		activationCode.setCode(generatedCode);
		activationCode.setPromotionCode(promotion.getCode());
		activationCode.setCustomerId(customer.getId());
		activationCode.setCustomerInfo(customerInfo);
		activationCode.setLevel(promotion.getLevel());
		activationCode.setDuration(promotion.getGiftDuration());
		activationCode.setExpirationDate(promotion.getRedemptionValidity());
		activationCode.setHasBind(ActivationCodeDef.HasBind.YES.getCode());
		activationCode.setState(ActivationCodeDef.State.UNUSED.getCode());
		activationCodeService.create(activationCode);
		return generatedCode;
	}

	private String assembleActivationCode(Customer customer,
			BusinessConfig businessConfig) {
		if (Objects.isNull(businessConfig)
				|| CommonDef.Symbol.NO.match(businessConfig.getHasOpen())) {
			return null;
		}
		CustomerJsonInfo customerInfo = new CustomerJsonInfo();
		customerInfo.setMobile(customer.getMobile());
		customerInfo.setId(customer.getId());
		customerInfo.setCreatedTime(customer.getCreatedTime());
		customerInfo.setCode(customer.getCode());
		ActivationCode activationCode = new ActivationCode();
		String generatedCode = this.generateActivationCode();
		activationCode.setCode(generatedCode);
		activationCode.setCustomerId(customer.getId());
		activationCode.setCustomerInfo(customerInfo);
		activationCode.setLevel(businessConfig.getLevel());
		activationCode.setDuration(businessConfig.getDuration());
		activationCode.setExpirationDate(LocalDate.now()
				.plusDays(businessConfig.getEffectiveDuration()));
		activationCode.setHasBind(ActivationCodeDef.HasBind.YES.getCode());
		activationCode.setState(ActivationCodeDef.State.UNUSED.getCode());
		activationCodeService.create(activationCode);
		return generatedCode;
	}

	private WxCreateForm validate(HttpServletRequest request) {
		Optional<WxCreateForm> createOptinal = CommonWebUtils
				.getRequestBody(request, WxCreateForm.class);
		createOptinal.ifPresent(
				op -> log.info("mini program registration param => {}",
						JsonUtils.objectToJson(op)));
		createOptinal.ifPresentOrElse(form -> {
			String tempVoucherCode = form.getTempVoucherCode();
			Assert.state(StringUtils.isNotBlank(tempVoucherCode),
					"missing tempVoucherCode");

			String mobile = form.getMobile();
			Assert.state(StringUtils.isNotBlank(mobile), "missing mobile");

			String value = redisClient.get(UsernameSalt.encryptUsername(
					RedisKeys.Cache.MINI_REGISTER_PREFIX + mobile));
			if (StringUtils.isBlank(value)) {
				value = redisClient.get(UsernameSalt.encryptUsername(
						RedisKeys.Cache.PC_REGISTER_PREFIX + mobile));
			}
			Assert.state(
					StringUtils.isNotBlank(value)
							&& Objects.equals(value, tempVoucherCode),
					"temporary login credentials is expired");

			Customer existCustomer = customerService.findByMobile(mobile)
					.orElse(null);
			Assert.isNull(existCustomer, "user already exists");

			Assert.notNull(form.getOrigin(), "missing origin");
			CertificationDef.Origin.from(form.getOrigin());
		}, () -> {
			throw new BadCredentialsException(
					"missing registration information");
		});
		return createOptinal.orElse(null);
	}

	/**
	 * 生成20位激活码
	 *
	 * @return
	 */
	private String generateActivationCode() {
		return UUIDUtil.random().substring(0, 20);
	}

}
