package com.zhihaoscm.usercenter.core.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.vo.CustomerSmsSettingVo;
import com.zhihaoscm.domain.bean.vo.CustomerVo;

/**
 * <p>
 * 客户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
public interface CustomerService extends MpLongIdBaseService<Customer> {

	/**
	 * 分页查询客户列表
	 *
	 * @param page
	 * @param size
	 * @param sortKey
	 * @param sortOrder
	 * @param param
	 *            用户昵称、个人实名、企业名称
	 * @param state
	 * @param memberLevel
	 * @param activeBeginTime
	 * @param activeEndTime
	 * @param expiryBeginDate
	 * @param expiryEndDate
	 * @param memberState
	 * @return
	 */
	Page<CustomerVo> paging(Integer page, Integer size, String sortKey,
			String sortOrder, String param, List<Integer> state,
			List<Integer> memberLevel, LocalDateTime activeBeginTime,
			LocalDateTime activeEndTime, LocalDate expiryBeginDate,
			LocalDate expiryEndDate, Integer memberState, Integer appType);

	/**
	 * 客户下拉列表
	 *
	 * @param searchParam
	 * @param personalAuth
	 * @param enterpriseAuth
	 * @param applyState
	 *            return
	 */
	List<Customer> selector(String searchParam, Integer personalAuth,
			Integer enterpriseAuth, Integer applyState);

	/**
	 * 查询客户下拉列表-船运需求
	 *
	 * @param searchParam
	 * @param state
	 * @param isReal
	 *            是否实名
	 * @return
	 */
	List<CustomerVo> shippingSelector(String searchParam, Integer state,
			Boolean isReal);

	/**
	 * 根据客户id查询客户信息
	 *
	 * @param customerId
	 * @return
	 */
	Optional<Customer> findById(Long customerId);

	/**
	 * 根据ID查询客户信息(后台查看功能)
	 *
	 * @param id
	 * @return 客户信息
	 */
	Optional<CustomerVo> findVoById(Long id);

	/**
	 * 根据指定的openid查询最后登录的身份
	 *
	 * @param mobile
	 *            openid
	 * @return 最后登录的身份
	 */
	Optional<Customer> findLastLogin(String mobile);

	/**
	 * 根据手机号批量获取客户信息
	 *
	 * @param mobile
	 * @return
	 */
	Optional<Customer> findByMobile(String mobile);

	/**
	 * 根据unionId获取客户信息
	 *
	 * @param unionId
	 * @return
	 */
	Optional<Customer> findByUnionId(String unionId);

	/**
	 * 根据CustomerId查询企业信息
	 *
	 * @param customerId
	 * @return
	 */
	Optional<Enterprise> findEnterpriseByCustomerId(Long customerId);

	/**
	 * 获取 AI 对话次数限制
	 *
	 * @param id
	 * @return
	 */
	Optional<Integer> findAssistantSessionLimit(Long id);

	/**
	 * 根据code获取客户信息
	 *
	 * @param code
	 * @return
	 */
	Optional<Customer> findByCode(String code);

	/**
	 * 根据统一社会信用代码获取客户信息
	 *
	 * @param unifiedSocialCreditCode
	 * @return
	 */
	Optional<Customer> findByUnifiedSocialCreditCode(
			String unifiedSocialCreditCode);

	/**
	 * 查询全部
	 *
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<Customer> findAll(LocalDateTime beginTime, LocalDateTime endTime);

	/**
	 * 通过姓名、用户Id、手机号查找账号
	 *
	 * @param searchParam
	 * @return
	 */
	List<Customer> findByNameOrCodeOrMobile(String searchParam);

	/**
	 * 查询客户所处的认证状态
	 *
	 * @param id
	 *
	 * @return
	 */
	Optional<Integer> getCustomerState(Long id);

	/**
	 * 根据app类型查询客户数量
	 * 
	 * @param appType
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	Long countByAppType(Integer appType, LocalDateTime beginTime,
			LocalDateTime endTime);

	/**
	 * 根据会员等级查询会员数量
	 *
	 * @param memberLevel
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	Long countByMemberLevel(Integer memberLevel, LocalDateTime beginTime,
			LocalDateTime endTime);

	/**
	 * 更新已过期会员的会员状态
	 *
	 * @return
	 */
	void updateExpiredMemberState();

	/**
	 * 根据客户id和state查询客户信息
	 *
	 * @param id
	 * @param state
	 */
	void updateState(Long id, Integer state, Integer origin);

	/**
	 * 获取用户openid
	 *
	 * @param code
	 * @param customerId
	 * @param origin
	 * @return
	 */
	Optional<String> getOpenid(String code, Long customerId, Integer origin);

	/**
	 * 会员续费提醒
	 */
	void memberRenewalReminder();

	/**
	 * 根据会员等级id查询客户列表
	 *
	 * @param id
	 * @return
	 */
	List<Customer> findByMemberLevel(Long id);

	/**
	 * 删除redis中的缓存信息
	 *
	 * @param keys
	 */
	void deleteCache(String keys);

	/**
	 * 更新会员的真实姓名拼音
	 */
	void updateRealNamePinyin();

	/**
	 * 获取用户子账号短信设置
	 */
	CustomerSmsSettingVo getCustomerAndSubAccount(Long id, String searchParams);

	/**
	 * 保存短信设置
	 */
	void saveCustomerAndSubAccount(CustomerSmsSettingVo customerSmsSettingVo);

	/**
	 * 取消机构认证
	 *
	 * @param customer
	 */
	void cancelApply(Customer customer);

	/**
	 * 查询供应商下拉列表
	 *
	 * @param name
	 * @return
	 */
	List<Customer> supplierSelector(String name);

	/**
	 * 根据账号查询用户
	 *
	 * @param account
	 * @return
	 */
	Optional<Customer> findByMobileOrEmail(String account);

	/**
	 * 根据邮箱查询用户
	 *
	 * @param email
	 * @return
	 */
	Optional<Customer> findByEmail(String email);

	/**
	 * 注销账号
	 *
	 * @param customer
	 */
	void logOff(Customer customer);

	/**
	 * 恢复账号
	 *
	 * @param customer
	 */
	void restore(Customer customer);

	/**
	 * 修改当天时间大于等于注销时间七天之后的数据为已注销
	 *
	 */
	void changeLogOffState();

	/**
	 * 根据苹果的subid获取客户信息
	 *
	 * @param subId
	 * @return
	 */
	Optional<Customer> findBySubId(String subId);
}
