package com.zhihaoscm.usercenter.resource.common;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.mybatis.plus.resource.MpLongIdBaseResource;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.MembershipLevel;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.bean.vo.CustomerVo;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.MembershipLevelDef;
import com.zhihaoscm.usercenter.config.properties.ApplicationProperties;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.config.security.custom.RedisJwtTokenService;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.core.service.MembershipLevelService;

/**
 * <p>
 * 客户 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@RestController
@RequestMapping("/customer-rest")
public class CustomerResource
		extends MpLongIdBaseResource<Customer, CustomerService>
		implements InitializingBean {

	public CustomerResource(CustomerService service) {
		super(service);
	}

	@Autowired
	MembershipLevelService membershipLevelService;
	@Autowired
	private StringRedisClient stringRedisClient;

	@Autowired
	private ApplicationProperties applicationProperties;

	@Autowired
	private WxMiniProgramProperties properties;

	private RedisJwtTokenService tokenService;

	@Override
	@GetMapping("/{id}")
	public Customer findById(@PathVariable(value = "id") Long id) {
		return service.findOne(id).orElse(null);
	}

	@GetMapping("/findByIdsNoDeleted")
	public List<Customer> findByIdsNoDeleted(
			@RequestParam Collection<Long> ids) {
		return service.findByIdsNoDeleted(ids);
	}

	@GetMapping("/assistant-session-limit")
	public Integer findAssistantSessionLimit(@RequestParam Long id) {
		return service.findAssistantSessionLimit(id).orElse(null);
	}

	@PutMapping("/expired-member-state")
	public void updateExpiredMemberState() {
		service.updateExpiredMemberState();
	}

	/**
	 * 更新会员的真实姓名拼音
	 */
	@PutMapping("/update/pinyin")
	public void updateRealNamePinyin() {
		service.updateRealNamePinyin();
	}

	/**
	 * 会员续费提醒
	 */
	@PutMapping("/member-renewal-reminder")
	void memberRenewalReminder() {
		service.memberRenewalReminder();
	}

	/**
	 * 根据app类型查询客户数量
	 *
	 * @param role
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	@GetMapping("/count/app-type")
	Long countByAppType(@RequestParam(required = false) Integer role,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
		return service.countByAppType(role, beginTime, endTime);
	}

	/**
	 * 查询全部
	 *
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	@GetMapping("/find/all")
	List<Customer> findAll(
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
		return service.findAll(beginTime, endTime);
	}

	@GetMapping("/count/by/level")
	Long countByMemberLevel(@RequestParam Integer memberLevel,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
		return service.countByMemberLevel(memberLevel, beginTime, endTime);
	}

	@PostMapping("/get/membership/level")
	MembershipLevel getMembershipLevel(@RequestBody Customer customer) {
		boolean nonMember = Objects.isNull(customer.getMemberLevel())
				|| MembershipLevelDef.Level.NON_MEMBER
						.match(customer.getMemberLevel())
				|| CustomerDef.MemberState.EXPIRY
						.match(customer.getMemberState());
		MembershipLevel membershipLevel;
		if (nonMember) {
			membershipLevel = membershipLevelService
					.findOne(Long.valueOf(
							MembershipLevelDef.Level.NON_MEMBER.getCode()))
					.orElse(null);
		} else {
			membershipLevel = membershipLevelService
					.findOne(Long.valueOf(customer.getMemberLevel()))
					.orElse(null);
		}
		return membershipLevel;
	}

	@GetMapping("/find/mobile")
	Customer findByMobile(@RequestParam String mobile) {
		return service.findByMobile(mobile).orElse(null);
	}

	@GetMapping("/find/lastlogin")
	Customer findLastLogin(@RequestParam String mobile) {
		return service.findLastLogin(mobile).orElse(null);
	}

	/**
	 * 客户下拉列表
	 *
	 * @param searchParam
	 * @param personalAuth
	 * @param enterpriseAuth
	 *            return
	 */
	@GetMapping("/selector")
	List<Customer> selector(@RequestParam(required = false) String searchParam,
			@RequestParam(required = false) Integer personalAuth,
			@RequestParam(required = false) Integer enterpriseAuth,
			@RequestParam(required = false) Integer applyState) {
		return service.selector(searchParam, personalAuth, enterpriseAuth,
				applyState);
	}

	@GetMapping("/find/unionid")
	Customer findByUnionId(@RequestParam String unionId) {
		return service.findByUnionId(unionId).orElse(null);
	}

	@GetMapping("/find/enterprise")
	Enterprise findEnterpriseByCustomerId(@RequestParam Long customerId) {
		return service.findEnterpriseByCustomerId(customerId).orElse(null);
	}

	@PostMapping({ "/batch/update" })
	void batchUpdate(@RequestBody List<Customer> datas) {
		service.batchUpdate(datas);
	}

	@GetMapping("/find/vo/{id}")
	CustomerVo findVoById(@PathVariable Long id) {
		return service.findVoById(id).orElse(null);
	}

	@GetMapping("/find/unified/social")
	Customer findByUnifiedSocialCreditCode(
			@RequestParam String unifiedSocialCreditCode) {
		return service.findByUnifiedSocialCreditCode(unifiedSocialCreditCode)
				.orElse(null);
	}

	@Override
	@GetMapping("/find/customer/all")
	public List<Customer> findAll() {
		return service.findAll();
	}

	@GetMapping("/find/code")
	Customer findByCode(@RequestParam String code) {
		return service.findByCode(code).orElse(null);
	}

	@GetMapping("/supplier/selector")
	List<Customer> supplierSelector(
			@RequestParam(required = false) String name) {
		return service.supplierSelector(name);
	}

	@RequestMapping("/parse/token")
	@ResponseBody
	LoginUser<CustomerLoginVo> parseToken(@RequestParam String token) {
		return tokenService.readUser(token);
	}

	/**
	 * 修改当天时间大于等于注销时间七天之后的数据为已注销
	 *
	 */
	@PutMapping("/change-log-off-state")
	public void changeLogOffState() {
		service.changeLogOffState();
	}

	@Override
	public void afterPropertiesSet() {
		tokenService = new RedisJwtTokenService(stringRedisClient);
		tokenService.setTokenExpire(properties.getLoginTokenExpire());
		tokenService.setJwtSecret(applicationProperties.getJwtSecret());
	}
}
