package com.zhihaoscm.usercenter.resource.validator.login;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerEnterprise;
import com.zhihaoscm.domain.meta.biz.AppTypeDef;
import com.zhihaoscm.domain.meta.biz.CustomerEnterpriseDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.usercenter.core.service.CustomerEnterpriseService;
import com.zhihaoscm.usercenter.core.service.CustomerService;

@Component
public class LoginValidator {

	@Autowired
	private CustomerService customerService;

	@Autowired
	private CustomerEnterpriseService customerEnterpriseService;

	/**
	 * 校验切换
	 *
	 * @param proxyAccountId
	 *            代理账号id
	 * @param role
	 *            角色
	 */
	public void validateSwitch(Long proxyAccountId, Integer role) {
		// 校验代理账号是否存在
		Customer proxyAccount = customerService.findById(proxyAccountId)
				.orElse(null);
		Assert.notNull(proxyAccount, ErrorCode.CODE_30055013);
		// 校验当前实际账号是否是代理账号的子账号
		Long actualAccountId = CustomerContextHolder.getCustomerLoginVo()
				.getActualAccount().getId();
		// 切换到本人账号时不做校验
		if (!Objects.equals(proxyAccountId, actualAccountId)) {
			List<CustomerEnterprise> invitedList = customerEnterpriseService
					.findBySubId(actualAccountId,
							CustomerEnterpriseDef.State.CONFIRMED.getCode());
			if (CollectionUtils.isEmpty(invitedList)) {
				throw new BadRequestException(ErrorCode.CODE_30055014);
			}
			Optional<CustomerEnterprise> first = invitedList.stream()
					.filter(customerEnterprise -> Objects.equals(
							customerEnterprise.getMainAccountId(),
							proxyAccountId))
					.findFirst();
			if (first.isEmpty()) {
				throw new BadRequestException(ErrorCode.CODE_30055015);
			}
		}
		// 校验身份是否合法
		if (Objects.isNull(role)) {
			return;
		}
		Assert.state(!AppTypeDef.AppType.CHUAN_WU.match(role),
				ErrorCode.CODE_30055017);
	}

}
