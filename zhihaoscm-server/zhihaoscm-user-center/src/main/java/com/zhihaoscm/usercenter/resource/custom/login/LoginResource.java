package com.zhihaoscm.usercenter.resource.custom.login;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerWx;
import com.zhihaoscm.domain.bean.entity.InstitutionApply;
import com.zhihaoscm.domain.bean.entity.MembershipLevel;
import com.zhihaoscm.domain.bean.json.MemberPermission;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.bean.vo.MeVo;
import com.zhihaoscm.domain.meta.CustomerPermissionDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.usercenter.config.properties.ApplicationProperties;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.usercenter.config.security.custom.RedisJwtTokenService;
import com.zhihaoscm.usercenter.config.security.custom.TokenResponse;
import com.zhihaoscm.usercenter.core.service.*;
import com.zhihaoscm.usercenter.resource.validator.login.LoginValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "登录管理", description = "登录管理API")
@RestController
@RequestMapping(value = "/login")
public class LoginResource implements InitializingBean {

	@Autowired
	private LoginValidator validator;

	@Autowired
	private LoginService service;

	@Autowired
	private CustomerServiceExt customerService;
	@Autowired
	private CustomerService customService;

	@Autowired
	private CustomerWxService customerWxService;

	@Autowired
	private InstitutionApplyService institutionApplyService;

	@Autowired
	private StringRedisClient stringRedisClient;

	@Autowired
	private ApplicationProperties applicationProperties;

	@Autowired
	private WxMiniProgramProperties properties;

	private RedisJwtTokenService tokenService;

	@Operation(summary = "登录后获取客户信息")
	@GetMapping({ "/me" })
	public ApiResponse<MeVo> me() {
		CustomerLoginVo customerLoginVo = CustomerContextHolder
				.getCustomerLoginVo();
		MeVo meVo = new MeVo();
		this.populateAttributes(customerLoginVo::getProxyAccount,
				meVo::setProxyAccount, meVo::setProxyMemberPermission);
		this.populateAttributes(customerLoginVo::getActualAccount,
				meVo::setActualAccount, meVo::setActualMemberPermission);

		CustomerWx customerWx = customerWxService
				.findByCustomerId(customerLoginVo.getActualAccount().getId())
				.stream().filter(c -> Objects
						.equals(CommonDef.Symbol.NO.getCode(), c.getType()))
				.findFirst().orElse(null);
		if (Objects.nonNull(customerWx)) {
			meVo.setNickName(customerWx.getNickName());
			meVo.setBindTime(customerWx.getBindTime());
		}

		// 本系统已认证
		if (CommonDef.Symbol.YES
				.match(customerLoginVo.getActualAccount().getApplyState())) {

			if (StringUtils.isBlank(
					customerLoginVo.getActualAccount().getInstitutionName())) {
				InstitutionApply institutionApply = institutionApplyService
						.findByCustomerIdAndStateLatest(
								customerLoginVo.getActualAccount().getId(),
								null)
						.orElse(null);
				if (Objects.nonNull(institutionApply)) {
					customerLoginVo.getActualAccount().setInstitutionName(
							institutionApply.getInstitutionName());
				}
			}
		}
		return new ApiResponse<>(meVo);
	}

	@Operation(summary = "切换代理账号")
	@PutMapping({ "/switch/proxy" })
	public ApiResponse<Void> switchProxy(
			@RequestParam(value = "proxyAccountId") Long proxyAccountId,
			@RequestParam(value = "role", required = false) Integer role,
			@RequestParam(value = "origin") Integer origin) {
		validator.validateSwitch(proxyAccountId, role);
		service.switchProxy(proxyAccountId, CustomerContextHolder
				.getCustomerLoginVo().getActualAccount().getId(), role, origin);
		return new ApiResponse<>();
	}

	@Operation(summary = "客户端切换身份后登陆")
	@PutMapping({ "/app/switch" })
	public ApiResponse<TokenResponse> appSwitch(
			@RequestParam(value = "mobile") String mobile,
			@RequestParam(value = "role") Integer role,
			@RequestParam(value = "origin") Integer origin) {
		CustomerLoginVo customerLoginVo = CustomerContextHolder
				.getCustomerLoginVo();
		customerLoginVo.setRole(role);
		// 获取用户上一次登录的代理账号
		String lastLoginKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX + mobile;
		String encryptedKey = UsernameSalt.encryptMiniLogin(lastLoginKey);
		String proxyAccountId = stringRedisClient.get(encryptedKey);
		if (StringUtils.isNotBlank(proxyAccountId)) {
			Customer clientById = customService
					.findById(Long.valueOf(proxyAccountId)).orElse(null);
			if (Objects.nonNull(clientById)) {
				customerLoginVo.setProxyAccount(clientById);
			}
			stringRedisClient.setEx(encryptedKey, proxyAccountId,
					properties.getLoginTokenExpire(), TimeUnit.SECONDS);
		}
		LoginUser<CustomerLoginVo> user = new LoginUser<>(customerLoginVo,
				String.valueOf(role), mobile,
				List.of(CustomerPermissionDef.CustomerPermission.from(role)
						.getPermission()));
		String token = tokenService.createAccessToken(user, origin);
		return new ApiResponse<>(
				new TokenResponse(token, user.getPassword(), null));
	}

	/**
	 * 填充meVo属性
	 *
	 * @param supplier
	 *            从上下文获取账户基本信息
	 * @param customerConsumer
	 *            消费账户基础信息
	 * @param permissionConsumer
	 *            消费会员信息
	 */
	private void populateAttributes(Supplier<Customer> supplier,
			Consumer<Customer> customerConsumer,
			Consumer<MemberPermission> permissionConsumer) {
		Customer account = supplier.get();
		if (Objects.isNull(account)) {
			return;
		}
		CustomerDef.FILTER_CUSTOMER_SENSITIVE_INFO.accept(account);
		customerConsumer.accept(account);
		// 非会员判断条件
		MembershipLevel membershipLevel = customerService
				.getMembershipLevel(account);
		if (Objects.nonNull(membershipLevel)) {
			permissionConsumer.accept(membershipLevel.getFeature());
		}
	}

	@Override
	public void afterPropertiesSet() {
		tokenService = new RedisJwtTokenService(stringRedisClient);
		tokenService.setTokenExpire(properties.getLoginTokenExpire());
		tokenService.setJwtSecret(applicationProperties.getJwtSecret());
	}
}
