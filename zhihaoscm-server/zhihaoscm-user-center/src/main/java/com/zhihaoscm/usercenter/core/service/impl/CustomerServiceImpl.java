package com.zhihaoscm.usercenter.core.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.sdk.wx.miniapp.api.login.Code2SessionResponse;
import com.zhihaoscm.common.sdk.wx.miniapp.api.login.LoginClient;
import com.zhihaoscm.common.util.utils.ExceptionUtil;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.common.util.utils.date.DateFormat;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.dto.CustomerStateRefreshDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.CustomerMinder;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.notice.Messages;
import com.zhihaoscm.domain.bean.vo.CustomerSmsSettingVo;
import com.zhihaoscm.domain.bean.vo.CustomerVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.notice.AppType;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.usercenter.config.properties.SMSProperties;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.core.mapper.CustomerMapper;
import com.zhihaoscm.usercenter.core.service.*;
import com.zhihaoscm.usercenter.utils.PinyinUtils;
import com.zhihaoscm.usercenter.utils.TokenUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 客户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@Slf4j
@Service
public class CustomerServiceImpl
		extends MpLongIdBaseServiceImpl<Customer, CustomerMapper>
		implements CustomerService {

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private CustomerInvoiceHeaderService customerInvoiceHeaderService;

	@Autowired
	private CustomerBankService customerBankService;

	@Autowired
	private CustomerReceivingAddressService customerReceivingAddressService;

	@Autowired
	private PersonalCertificationService personalCertificationService;

	@Autowired
	private MembershipLevelService membershipLevelService;

	@Autowired
	private LoginClient loginClient;

	@Autowired
	private WxMiniProgramProperties miniAppProperties;

	@Autowired
	private SMSProperties smsProperties;

	@Autowired
	private MqUtil mqUtil;

	@Autowired
	private CustomerEnterpriseService customerEnterpriseService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private CustomerWxService customerWxService;

	@Autowired
	private InstitutionApplyService institutionApplyService;

	@Autowired
	private CustomerChangeMobileService customerChangeMobileService;

	public CustomerServiceImpl(CustomerMapper repository) {
		super(repository);
	}

	@Override
	public Page<CustomerVo> paging(Integer page, Integer size, String sortKey,
			String sortOrder, String param, List<Integer> state,
			List<Integer> memberLevel, LocalDateTime activeBeginTime,
			LocalDateTime activeEndTime, LocalDate expiryBeginDate,
			LocalDate expiryEndDate, Integer memberState, Integer appType) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.in(CollectionUtils.isNotEmpty(state), Customer::getState,
				state);

		List<InstitutionApply> institutionApplyList = institutionApplyService
				.findBySearchParam(param);
		List<Long> customerIds = institutionApplyList.stream()
				.map(InstitutionApply::getCustomerId)
				.collect(Collectors.toList());

		queryWrapper.eq(Objects.nonNull(appType), Customer::getAppType,
				appType);
		queryWrapper.eq(Objects.nonNull(memberState), Customer::getMemberState,
				memberState);
		queryWrapper.in(CollectionUtils.isNotEmpty(memberLevel),
				Customer::getMemberLevel, memberLevel);
		queryWrapper.between(
				Objects.nonNull(activeBeginTime)
						&& Objects.nonNull(activeEndTime),
				Customer::getMemberActiveTime, activeBeginTime, activeEndTime);
		queryWrapper.between(
				Objects.nonNull(expiryBeginDate)
						&& Objects.nonNull(expiryEndDate),
				Customer::getMemberExpiryDate, expiryBeginDate, expiryEndDate);
		queryWrapper.and(StringUtils.isNotBlank(param),
				wrapper -> wrapper.like(Customer::getMobile, param).or()
						.like(Customer::getCode, param).or()
						.like(Customer::getInstitutionName, param).or()
						.like(Customer::getRealName, param).or()
						.in(CollectionUtils.isNotEmpty(customerIds),
								Customer::getId, customerIds));

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照创建时间降序排列
			queryWrapper.orderByDesc(Customer::getCreatedTime);
		}
		Page<Customer> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public List<Customer> selector(String searchParam, Integer personalAuth,
			Integer enterpriseAuth, Integer applyState) {
		LambdaQueryWrapper<Customer> wrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(wrapper);
		this.assembleSelectorCondition(searchParam, wrapper);

		wrapper.isNotNull(CommonDef.Symbol.YES.match(personalAuth),
				Customer::getRealName);
		wrapper.isNotNull(CommonDef.Symbol.YES.match(enterpriseAuth),
				Customer::getInstitutionName);
		wrapper.eq(Objects.nonNull(applyState), Customer::getApplyState,
				applyState);
		List<Customer> customers = repository.selectList(wrapper);
		if (Objects.isNull(applyState)) {
			return customers;
		}

		List<InstitutionApply> institutionApplyList = institutionApplyService
				.findBySearchParam(searchParam);

		List<InstitutionApply> institutionApplyLists = institutionApplyService
				.findByCustomerIds(
						customers.stream().map(Customer::getId).toList());
		institutionApplyList = Stream.concat(institutionApplyList.stream(),
				institutionApplyLists.stream()).distinct().toList();

		Map<Long, InstitutionApply> applyMap = institutionApplyList.stream()
				.collect(Collectors.toMap(InstitutionApply::getCustomerId,
						Function.identity()));

		List<Long> customerIds = institutionApplyList.stream()
				.map(InstitutionApply::getCustomerId)
				.collect(Collectors.toList());

		List<Customer> customers1 = this.findByIds(customerIds);
		customers = Stream.concat(customers.stream(), customers1.stream())
				.distinct().collect(Collectors.toList());

		customers.forEach(customer -> {
			if (StringUtils.isBlank(customer.getInstitutionName())) {
				Optional.ofNullable(applyMap.get(customer.getId()))
						.ifPresent(apply -> {
							customer.setInstitutionName(
									apply.getInstitutionName());
							customer.setUnifiedSocialCreditCode(
									apply.getUnifiedSocialCreditCode());
							customer.setLegalRepresentative(
									apply.getLegalRepresentative());
						});
			}
		});

		return customers;

	}

	@Override
	public List<Customer> supplierSelector(String name) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.select(Customer::getId, Customer::getInstitutionName);
		queryWrapper.eq(Customer::getApplyState,
				CommonDef.Symbol.YES.getCode());
		queryWrapper.isNotNull(Customer::getInstitutionName);
		queryWrapper.like(StringUtils.isNotBlank(name),
				Customer::getInstitutionName, name);
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<Customer> findByMobileOrEmail(String account) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Customer::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper
				.and(x -> x
						.eq(StringUtils.isNotBlank(account),
								Customer::getMobile, account)
						.or().eq(StringUtils.isNotBlank(account),
								Customer::getEmail, account));
		return Optional.ofNullable(repository.selectOne(queryWrapper));
	}

	@Override
	public Optional<Customer> findByEmail(String email) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Customer::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(StringUtils.isNotBlank(email), Customer::getEmail,
				email);
		return Optional.ofNullable(repository.selectOne(queryWrapper));
	}

	@Override
	public List<CustomerVo> shippingSelector(String searchParam, Integer state,
			Boolean isReal) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class)
				.eq(Customer::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(state), Customer::getState, state);
		if (Objects.nonNull(isReal)) {
			queryWrapper.isNotNull(isReal, Customer::getRealName);
		}
		this.assembleSelectorCondition(searchParam, queryWrapper);
		return this.packVo(repository.selectList(queryWrapper));
	}

	@Override
	public Optional<Customer> findById(Long customerId) {
		return this.findOne(customerId);
	}

	@Override
	public Optional<CustomerVo> findVoById(Long id) {
		return super.findOne(id).map(this::packVo);
	}

	@Override
	public Optional<Customer> findLastLogin(String mobile) {
		// 获取该用户的所有身份信息
		Optional<Customer> optionalCustomer = this.findByMobile(mobile);
		if (optionalCustomer.isEmpty()) {
			return Optional.empty();
		}
		Customer customer = optionalCustomer.get();
		if (CommonDef.Symbol.NO.match(customer.getState())) {
			return Optional.empty();
		}
		return optionalCustomer;
	}

	@Override
	public Optional<Customer> findByMobile(String mobile) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class).eq(Customer::getMobile, mobile)
				.eq(Customer::getDel, CommonDef.Symbol.NO.getCode());
		return Optional
				.ofNullable(repository.selectOne(queryWrapper, Boolean.FALSE));
	}

	@Override
	public Optional<Customer> findByUnionId(String unionId) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		queryWrapper.eq(Customer::getUnionId, unionId);
		queryWrapper.eq(Customer::getDel, CommonDef.Symbol.NO.getCode());
		return Optional
				.ofNullable(repository.selectOne(queryWrapper, Boolean.FALSE));
	}

	@Override
	public Optional<Enterprise> findEnterpriseByCustomerId(Long customerId) {

		Enterprise enterprise = new Enterprise();
		Optional<Customer> optionalCustomer = super.findOne(customerId);
		if (optionalCustomer.isPresent()) {
			Customer customer = optionalCustomer.get();
			enterprise.setRealName(customer.getRealName());
			enterprise.setMobile(customer.getMobile());
			enterprise.setName(customer.getInstitutionName());
		}

		if (StringUtils.isBlank(enterprise.getName())) {
			institutionApplyService
					.findByCustomerIdAndStateCancel(customerId,
							InstitutionApplyDef.State.APPROVED.getCode(),
							CommonDef.Symbol.NO.getCode())
					.ifPresent(institutionApply -> {
						enterprise
								.setName(institutionApply.getInstitutionName());
						enterprise.setUnifiedSocialCreditCode(
								institutionApply.getUnifiedSocialCreditCode());
						enterprise.setLegalRepresentative(
								institutionApply.getLegalRepresentative());
					});
		}
		return Optional.of(enterprise);
	}

	@Override
	public Optional<Integer> findAssistantSessionLimit(Long id) {
		Optional<Customer> optionalCustomer = super.findOne(id);
		if (optionalCustomer.isEmpty()) {
			return Optional.empty();
		}
		Customer customer = optionalCustomer.get();
		boolean nonMember = Objects.isNull(customer.getMemberLevel())
				|| MembershipLevelDef.Level.NON_MEMBER
						.match(customer.getMemberLevel())
				|| CustomerDef.MemberState.EXPIRY
						.match(customer.getMemberState());
		Optional<MembershipLevel> optionalMembershipLevel;
		if (nonMember) {
			optionalMembershipLevel = membershipLevelService.findOne(Long
					.valueOf(MembershipLevelDef.Level.NON_MEMBER.getCode()));
		} else {
			optionalMembershipLevel = membershipLevelService
					.findOne(Long.valueOf(customer.getMemberLevel()));
		}
		return optionalMembershipLevel.map(
				membershipLevel -> membershipLevel.getFeature().getAssistant());
	}

	@Override
	public Optional<Customer> findByCode(String code) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class).eq(Customer::getCode, code)
				.eq(Customer::getDel, CommonDef.Symbol.NO.getCode());
		return Optional
				.ofNullable(repository.selectOne(queryWrapper, Boolean.FALSE));
	}

	@Override
	public Optional<Customer> findByUnifiedSocialCreditCode(
			String unifiedSocialCreditCode) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class)
				.eq(Customer::getUnifiedSocialCreditCode,
						unifiedSocialCreditCode)
				.eq(Customer::getDel, CommonDef.Symbol.NO.getCode());
		return Optional
				.ofNullable(repository.selectOne(queryWrapper, Boolean.FALSE));
	}

	@Override
	public List<Customer> findAll(LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(queryWrapper);
		// 设置查询条件
		queryWrapper.ge(Objects.nonNull(beginTime), Customer::getCreatedTime,
				beginTime);
		queryWrapper.le(Objects.nonNull(endTime), Customer::getCreatedTime,
				endTime);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Customer> findByNameOrCodeOrMobile(String searchParam) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		queryWrapper.eq(Customer::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Customer::getState, CommonDef.Symbol.YES.getCode());
		boolean hasSearchTerm = StringUtils.isNotEmpty(searchParam)
				|| StringUtils.isNotEmpty(searchParam)
				|| StringUtils.isNotEmpty(searchParam);
		queryWrapper.and(hasSearchTerm,
				x -> x.like(StringUtils.isNotBlank(searchParam),
						Customer::getRealName, searchParam).or()
						.eq(StringUtils.isNotBlank(searchParam),
								Customer::getMobile, searchParam)
						.or()

						.eq(StringUtils.isNotBlank(searchParam),
								Customer::getCode, searchParam));
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<Integer> getCustomerState(Long id) {
		Customer customer = super.findOne(id).orElse(null);
		// 查询用户的换绑手机号记录
		CustomerChangeMobile customerChangeMobile = customerChangeMobileService
				.findByCustomerId(id).orElse(null);
		// 换绑手机号记录不为空 说明当前用户在换绑手机号流程中
		if (Objects.nonNull(customerChangeMobile)) {
			// 判断换绑手机号过程中是否完成个人认证
			CustomerPersonalCertification customerPersonalCertification = personalCertificationService
					.findByCustomerIdLatest(id,
							CertificationDef.CertificationState.CHANGED
									.getCode())
					.orElse(null);
			if (Objects.nonNull(customerPersonalCertification)) {
				// 个人认证成功 则在换绑流程中
				if (customerPersonalCertification.getStatus().equals(
						CertificationDef.CertificationStatus.PASS.getCode())) {
					return Optional.of(
							CustomerChangeMobileDef.ChangeMobileState.PROCESSING
									.getCode());
				}
			}
			// 说明没有进行个人认证或者没有个人认证成功,可以进行换绑
			return Optional.of(
					CustomerChangeMobileDef.ChangeMobileState.YES.getCode());
		}
		// 用户不在换绑手机号流程中 则判断是否可以换绑手机号
		else if (Objects.nonNull(customer)) {
			// institution_name 不为空则在契约锁做了企业认证
			if (StringUtils.isNotBlank(customer.getInstitutionName())) {
				// 不能进行换绑
				return Optional.of(
						CustomerChangeMobileDef.ChangeMobileState.NO.getCode());
			}
			// apply_state为1在我们系统内做了认证
			else if (CommonDef.Symbol.YES.match(customer.getApplyState())) {
				// 可以进行换绑
				return Optional.of(CustomerChangeMobileDef.ChangeMobileState.YES
						.getCode());
			}
			// real_name 不为空则做了个人认证
			else if (StringUtils.isNotBlank(customer.getRealName())) {
				// 可以进行换绑
				return Optional.of(CustomerChangeMobileDef.ChangeMobileState.YES
						.getCode());
			} else {
				// 未实名 可以进行换绑
				return Optional.of(CustomerChangeMobileDef.ChangeMobileState.YES
						.getCode());
			}
		}
		// 为空，一般不存在的情况不换绑
		return Optional.empty();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Customer create(Customer resource) {
		return super.create(resource);
	}

	@FileId(type = 2)
	@Override
	public Customer updateAllProperties(Customer resource) {
		return super.updateAllProperties(resource);
	}

	@Override
	public void updateExpiredMemberState() {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Customer::getMemberState,
				CustomerDef.MemberState.ACTIVE.getCode());
		queryWrapper.lt(Customer::getMemberExpiryDate, LocalDate.now());
		List<Customer> customerList = repository.selectList(queryWrapper);
		if (CollectionUtils.isNotEmpty(customerList)) {
			List<Long> customerIds = customerList.stream().map(Customer::getId)
					.toList();

			LambdaUpdateWrapper<Customer> updateWrapper = Wrappers
					.lambdaUpdate(Customer.class);
			updateWrapper.set(Customer::getMemberState,
					CustomerDef.MemberState.EXPIRY.getCode());
			updateWrapper.in(Customer::getId, customerIds);
			repository.update(updateWrapper);
			// 更新上下文用户会员状态
			customerList.forEach(customer -> customer
					.setMemberState(CustomerDef.MemberState.EXPIRY.getCode()));
			try {
				// 通知前端更新用户信息 刷新上下文
				TokenUtils.batchRefreshCustomerContext(customerList,
						redisClient, 30 * 24 * 60 * 60L);
				// websocket推送会员信息变更
				mqUtil.asyncSend(MqMessage.builder()
						.topic(TopicDef.CUSTOM_USER_CHANGE)
						.message(MessageBuilder.withPayload(JsonUtils
								.objectToJson(new CustomerStateRefreshDto(
										TopicDef.Type.REFRESH_STATE.getCode(),
										customerIds)))
								.build())
						.build());
				// 主账号会员过期后更新子账号状态为已失效
				customerEnterpriseService.batchUpdateAccountState(customerIds,
						CommonDef.Symbol.NO.getCode());
			} catch (Exception e) {
				log.error("update member state error, reason:{}",
						ExceptionUtil.getStackTraceString(e));
			}
		}

	}

	@Override
	public void updateState(Long id, Integer state, Integer origin) {
		this.findOne(id).ifPresent(customer -> {
			customer.setState(state);
			customer.setUpdatedType(origin);
			repository.updateById(customer);
			// 若用户被禁用则删除其token
			if (CommonDef.Symbol.NO.getCode().equals(state)) {
				String originKey = RedisKeys.Cache.MINI_LOGIN_INFO_PREFIX + id;
				String encryptKey = UsernameSalt.encryptMiniLogin(originKey);
				redisClient.keys(encryptKey + "*")
						.forEach(key -> redisClient.delete(key));
				// 通知客户端刷新客户信息
				mqUtil.asyncSend(MqMessage.builder()
						.topic(TopicDef.CUSTOM_USER_CHANGE)
						.message(MessageBuilder.withPayload(JsonUtils
								.objectToJson(new CustomerStateRefreshDto(
										TopicDef.Type.CHOOSE_ENTERPRISE
												.getCode(),
										TokenUtils.getNoticeIds(
												customer.getId(),
												customerEnterpriseService,
												Boolean.FALSE))))
								.build())
						.build());
			}
		});
	}

	@Override
	public Optional<String> getOpenid(String code, Long customerId,
			Integer origin) {
		log.info("get open id request, code:{}, customerId:{}", code,
				customerId);
		String appid;
		String secret;
		if (CustomerDef.Origin.CUSTOMER.match(origin)) {
			appid = miniAppProperties.getAppid();
			secret = miniAppProperties.getSecret();
		} else {
			appid = miniAppProperties.getCarrierAppid();
			secret = miniAppProperties.getCarrierSecret();
		}
		String res = loginClient.code2Session(appid, secret, code,
				"authorization_code");
		log.info("get open id response, code:{}, customerId:{}, res: {}", code,
				customerId, res);
		Code2SessionResponse response = JsonUtils.jsonToObject(res,
				Code2SessionResponse.class);
		if (StringUtils.isBlank(response.getErrmsg())) {
			this.findById(customerId).ifPresent(customer -> {
				if (StringUtils.isBlank(customer.getWxOpenId())) {
					customer.setWxOpenId(response.getOpenid());
					this.updateAllProperties(customer);
					// 刷新上下文
					TokenUtils.refreshCustomerContext(customer, redisClient,
							30 * 24 * 60 * 60L, Boolean.TRUE);
				}
			});
			return Optional.of(response.getOpenid());
		} else {
			log.error("code2Session error, customerId:{}, code:{}, response:{}",
					customerId, code, res);
			return Optional.empty();
		}
	}

	@Override
	public void logOff(Customer customer) {
		customer.setState(CustomerDef.State.CANCELLING_IN_PROGRESS.getCode());
		customer.setLogOffTime(LocalDateTime.now().plusDays(7));
		super.updateAllProperties(customer);
		// 删除缓存中token信息
		TokenUtils.deleteCustomer(customer.getId(), redisClient);
	}

	@Override
	public void restore(Customer customer) {
		customer.setState(CustomerDef.State.ACTIVE.getCode());
		customer.setLogOffTime(null);
		Customer customer1 = super.updateAllProperties(customer);
		TokenUtils.refreshCustomerContext(customer1, redisClient,
				30 * 24 * 60 * 60L, Boolean.TRUE);
	}

	@Override
	public void changeLogOffState() {
		// 查询注销中的账号
		LambdaQueryWrapper<Customer> wrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Customer::getState,
				CustomerDef.State.CANCELLING_IN_PROGRESS.getCode());
		List<Customer> customers = repository.selectList(wrapper);

		// 过滤出注销时间不小于当前时间的用户ID
		List<Long> customerIdList = customers.stream()
				.filter(item -> !LocalDateTime.now().toLocalDate()
						.isBefore(item.getLogOffTime().toLocalDate()))
				.map(Customer::getId).toList();

		// 更新用户注销状态
		if (CollectionUtils.isNotEmpty(customerIdList)) {
			LambdaUpdateWrapper<Customer> updateWrapper = Wrappers
					.lambdaUpdate(Customer.class);
			updateWrapper.set(Customer::getState,
					CustomerDef.State.CANCELLED.getCode());
			updateWrapper.in(Customer::getId, customerIdList);
			updateWrapper.eq(Customer::getDel, CommonDef.Symbol.NO.getCode());
			repository.update(updateWrapper);
		}
	}

	@Override
	public void memberRenewalReminder() {
		LocalDate expiryDate = LocalDate.now().plusDays(3);
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.select(Customer::getMobile, Customer::getMemberLevel,
				Customer::getId);
		queryWrapper.eq(Customer::getMemberState,
				CustomerDef.MemberState.ACTIVE.getCode());
		queryWrapper.eq(Customer::getMemberExpiryDate, expiryDate);
		List<Customer> customers = repository.selectList(queryWrapper);
		if (CollectionUtils.isNotEmpty(customers)) {
			List<MembershipLevel> membershipLevels = membershipLevelService
					.findAll();
			Map<Long, String> idNameMap = membershipLevels.stream()
					.collect(Collectors.toMap(MembershipLevel::getId,
							MembershipLevel::getName));

			DateTimeFormatter dateTimeFormatter = DateTimeFormatter
					.ofPattern(DateFormat.YYYY_MM_DD_CN);
			String dateStr = dateTimeFormatter.format(expiryDate);
			Map<String, String> params = new HashMap<>();
			customers.forEach(customer -> {
				CustomerMinder remind = new CustomerMinder();
				remind.setParams(Map.of());
				remind.setPath(smsProperties.getMembershipPage());
				remind.setRole(null);
				remind.setReceiptor(customer);
				remind.setRemindType(
						CustomerMinder.RemindType.memberRenewalRemind);
				remind.setTitle(UserMessageConstants.MEMBER_UNACTIVE_TEMPLATE);
				remind.getSmsParams().put("client", "链云用户");
				remind.getSmsParams().put("vip_type",
						idNameMap.getOrDefault(
								Long.valueOf(customer.getMemberLevel()),
								MembershipLevelDef.Level.NON_MEMBER.getDesc()));
				remind.getSmsParams().put("date", dateStr);
				mqUtil.asyncSend(MqMessage.builder()
						.topic(TopicDef.CUSTOMER_ACCOUNT_MIND)
						.message(MessageBuilder
								.withPayload(JsonUtils.objectToJson(remind))
								.build())
						.build());
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
								SendType.USERMESSAGE.getCode()))
						.type(UserMessageDef.MessageType.OTHER.getCode())
						.title(UserMessageConstants.MEMBER_UNACTIVE_TEMPLATE)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.MEMBER_DETAIL_PAGE)
						.initiator(UserMessageDef.BusinessInitiator.initiate
								.getCode())
						.templateCode(smsProperties.getMemberRenewalReminder())
						.params(params).mobile(customer.getMobile()).build());

				// APP推送
				messageService.sendNotice(Messages.builder()
						.type(UserMessageDef.MessageType.OTHER.getCode())
						.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
						.content(UserMessageConstants.MEMBER_UNACTIVE_TEMPLATE)
						.title(UserMessageConstants.VIP_TITLE)
						.appTypes(List.of(AppType.LIANYUN, AppType.SHIP))
						.receiptors(List.of(String.valueOf(customer.getId())))
						.moduleType(UserMessageConstants.MEMBER_DETAIL_PAGE)
						.bizNo(String.valueOf(customer.getId())).build());
			});

		}
	}

	@Override
	public List<Customer> findByMemberLevel(Long id) {
		// 查询当前等级的所有用户
		LambdaQueryWrapper<Customer> wrapper = Wrappers
				.lambdaQuery(Customer.class)
				.eq(Customer::getMemberLevel, id.intValue())
				.eq(Customer::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Customer::getMemberState,
						CustomerDef.MemberState.ACTIVE.getCode());
		List<Customer> customers = repository.selectList(wrapper);
		// 如果当前查询的是非会员则将其他等级已过期且已激活的用户加入结果集中
		if (MembershipLevelDef.Level.NON_MEMBER.match(id.intValue())) {
			LambdaQueryWrapper<Customer> queryWrapper = Wrappers
					.lambdaQuery(Customer.class)
					.ne(Customer::getMemberLevel,
							MembershipLevelDef.Level.NON_MEMBER.getCode())
					.eq(Customer::getMemberState,
							CustomerDef.MemberState.EXPIRY.getCode())
					.eq(Customer::getDel, CommonDef.Symbol.NO.getCode())
					.eq(Customer::getMemberState,
							CustomerDef.MemberState.ACTIVE.getCode());
			List<Customer> expriedMembers = repository.selectList(queryWrapper);
			customers.addAll(expriedMembers);
		}
		return customers;
	}

	@Override
	public void deleteCache(String keys) {
		if (StringUtils.isNotBlank(keys)) {
			Set<String> keySets = redisClient.keys(keys);
			keySets.forEach(e -> redisClient.delete(e));
		} else {
			Set<String> keySets = redisClient.keys("SCM:MINI*");
			keySets.forEach(e -> redisClient.delete(e));
		}
	}

	@Override
	public void updateRealNamePinyin() {
		LambdaQueryWrapper<Customer> wrapper = this
				.getRealNamePinyinWrapper(queryWrapper -> {
				});
		List<Customer> customers = repository.selectList(wrapper);
		while (CollectionUtils.isNotEmpty(customers)) {
			customers.forEach(customer -> {
				String realNamePinyin = PinyinUtils
						.getPinyin(customer.getRealName());
				customer.setRealNamePinyin(realNamePinyin);
			});
			super.batchUpdate(customers);
			List<Customer> finalCustomers = customers;
			this.getRealNamePinyinWrapper(queryWrapper -> wrapper.lt(
					Customer::getId,
					finalCustomers.get(finalCustomers.size() - 1).getId()));
			customers = repository.selectList(wrapper);
		}
	}

	private LambdaQueryWrapper<Customer> getRealNamePinyinWrapper(
			Consumer<LambdaQueryWrapper<Customer>> consumer) {
		LambdaQueryWrapper<Customer> wrapper = Wrappers
				.lambdaQuery(Customer.class)
				.eq(Customer::getDel, CommonDef.Symbol.NO.getCode())
				.isNull(Customer::getRealNamePinyin).orderByAsc(Customer::getId)
				.last("LIMIT 2000");
		consumer.accept(wrapper);
		return wrapper;
	}

	@Override
	public Long countByAppType(Integer appType, LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Objects.nonNull(appType), Customer::getAppType,
				appType);
		queryWrapper.ge(Objects.nonNull(beginTime), Customer::getCreatedTime,
				beginTime);
		queryWrapper.le(Objects.nonNull(endTime), Customer::getCreatedTime,
				endTime);
		return repository.selectCount(queryWrapper);
	}

	@Override
	public Long countByMemberLevel(Integer memberLevel, LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		queryWrapper.eq(Objects.nonNull(memberLevel), Customer::getMemberLevel,
				memberLevel);
		this.filterDeleted(queryWrapper);
		queryWrapper.ge(Objects.nonNull(beginTime),
				Customer::getMemberActiveTime, beginTime);
		queryWrapper.le(Objects.nonNull(endTime), Customer::getMemberActiveTime,
				endTime);
		return repository.selectCount(queryWrapper);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void delete(Long id) {
		super.findOne(id).ifPresent(customer -> {
			customer.setDel(CommonDef.Symbol.YES.getCode());
			customer.setUpdatedType(CommonDef.UserType.OUTER.getCode());
			super.updateAllProperties(customer);

			TokenUtils.deleteCustomer(id, redisClient);
			// 通知客户端刷新客户信息
			mqUtil.asyncSend(MqMessage.builder()
					.topic(TopicDef.CUSTOM_USER_CHANGE)
					.message(MessageBuilder.withPayload(
							JsonUtils.objectToJson(new CustomerStateRefreshDto(
									TopicDef.Type.CHOOSE_ENTERPRISE.getCode(),
									TokenUtils.getNoticeIds(customer.getId(),
											customerEnterpriseService,
											Boolean.FALSE))))
							.build())
					.build());
		});
	}

	/**
	 * 组装下拉列表查询条件
	 *
	 * @param searchParam
	 * @param queryWrapper
	 */
	private void assembleSelectorCondition(String searchParam,
			LambdaQueryWrapper<Customer> queryWrapper) {
		if (StringUtils.isNotBlank(searchParam)) {
			queryWrapper.and(StringUtils.isNotBlank(searchParam),
					wrapper -> wrapper.like(Customer::getRealName, searchParam)
							.or()
							.like(Customer::getInstitutionName, searchParam)
							.or().eq(Customer::getCode, searchParam).or()
							.eq(Customer::getNickName, searchParam).or()
							.eq(Customer::getMobile, searchParam));
		}
	}

	/**
	 * 组装用户相关信息
	 *
	 * @param customers
	 * @return
	 */
	private List<CustomerVo> packVo(List<Customer> customers) {
		if (CollectionUtils.isEmpty(customers)) {
			return List.of();
		}
		// 获取所有不重复的客户id
		List<Long> ids = customers.stream().map(Customer::getId)
				.collect(Collectors.toList());
		// 统计客户的地址数量
		List<CustomerReceivingAddress> addresses = customerReceivingAddressService
				.findByCustomerIds(ids);
		Map<Long, Long> addressCountMap = addresses.stream()
				.collect(Collectors.groupingBy(
						CustomerReceivingAddress::getCustomerId,
						Collectors.counting()));
		// 统计客户的银行卡数量
		List<CustomerBank> banks = customerBankService.findByCustomerIds(ids);
		Map<Long, Long> bankCountMap = banks.stream()
				.collect(Collectors.groupingBy(CustomerBank::getCustomerId,
						Collectors.counting()));
		// 统计客户的发票抬头数量
		List<CustomerInvoiceHeader> invoiceHeaders = customerInvoiceHeaderService
				.findByCustomerIds(ids);
		Map<Long, Long> invoiceHeaderCountMap = invoiceHeaders.stream()
				.collect(Collectors.groupingBy(
						CustomerInvoiceHeader::getCustomerId,
						Collectors.counting()));

		return customers.stream().map(customer -> {
			CustomerVo vo = new CustomerVo();
			vo.setCustomer(customer);
			// 本系统已认证
			if (CommonDef.Symbol.YES.match(customer.getApplyState())) {
				institutionApplyService
						.findByCustomerIdAndStateLatest(customer.getId(),
								InstitutionApplyDef.State.APPROVED.getCode())
						.ifPresent(vo::setInstitutionApply);
			}
			vo.setAddressCount(Math.toIntExact(
					addressCountMap.getOrDefault(customer.getId(), 0L)));
			vo.setBankCount(Math.toIntExact(
					bankCountMap.getOrDefault(customer.getId(), 0L)));
			vo.setInvoiceHeaderCount(Math.toIntExact(
					invoiceHeaderCountMap.getOrDefault(customer.getId(), 0L)));
			return vo;
		}).collect(Collectors.toList());
	}

	/**
	 * 组装用户相关信息
	 *
	 * @param customer
	 * @return
	 */
	private CustomerVo packVo(Customer customer) {
		CustomerVo vo = new CustomerVo();
		vo.setCustomer(customer);

		personalCertificationService
				.findByCustomerIdLatest(customer.getId(),
						CertificationDef.CertificationState.VALID.getCode())
				.ifPresent(personal -> {
					if (personal.getStatus()
							.equals(CertificationDef.CertificationStatus.PASS
									.getCode())) {
						vo.setPersonalCertification(personal);
					}
				});

		customerWxService.findByCustomerId(customer.getId()).stream()
				.filter(customerWx -> Objects.equals(0, customerWx.getType()))
				.findFirst().ifPresent(
						customerWx -> vo.setNickName(customerWx.getNickName()));

		List<CustomerInvoiceHeader> invoiceHeaders = customerInvoiceHeaderService
				.findByCustomerId(customer.getId(), null, null, null);
		vo.setInvoiceHeaders(invoiceHeaders);

		List<CustomerBank> banks = customerBankService
				.findByCustomerId(customer.getId(), null, null);
		vo.setBanks(banks);

		List<CustomerReceivingAddress> addresses = customerReceivingAddressService
				.findByCustomerId(customer.getId(), null);
		vo.setAddresses(addresses);
		// 本系统已认证
		if (CommonDef.Symbol.YES.match(customer.getApplyState())) {
			vo.setIsCancelInstitutionApply(CommonDef.Symbol.YES.getCode());
			institutionApplyService
					.findByCustomerIdAndStateLatest(customer.getId(),
							InstitutionApplyDef.State.APPROVED.getCode())
					.ifPresent(vo::setInstitutionApply);
		} else {
			vo.setIsCancelInstitutionApply(CommonDef.Symbol.NO.getCode());
		}
		return vo;
	}

	@Override
	public CustomerSmsSettingVo getCustomerAndSubAccount(Long id,
			String searchParams) {
		CustomerSmsSettingVo vo = new CustomerSmsSettingVo();
		Optional<Customer> optional = super.findOne(id);
		if (optional.isPresent()) {
			vo = new CustomerSmsSettingVo();
			Customer customer = optional.get();
			vo.add(customer.getRealName(), customer.getMobile(),
					customer.getReceiptSms(), id, id);
			List<CustomerEnterprise> subs = customerEnterpriseService
					.findByMainId(id,
							CustomerEnterpriseDef.State.CONFIRMED.getCode());
			if (CollectionUtils.isNotEmpty(subs)) {
				// 先按照创建时间排序
				subs.sort(Comparator
						.comparing(BaseEntityWithLongId::getCreatedTime));
				for (CustomerEnterprise sub : subs) {
					// 已失效的子账号过滤掉
					if (Objects.isNull(sub.getAccountState())
							|| CommonDef.Symbol.NO
									.match(sub.getAccountState())) {
						continue;
					}
					Optional<Customer> optionalCustomer = this
							.findOne(sub.getSubAccountId());
					if (optionalCustomer.isPresent()) {
						Customer subCustomer = optionalCustomer.get();
						vo.add(subCustomer.getRealName(),
								subCustomer.getMobile(), sub.getReceiptSms(),
								subCustomer.getId(), id);
					}
				}
			}
		}
		// 根据参数过滤
		if (StringUtils.isNotBlank(searchParams)) {
			vo.setCustomerSmsVos(
					vo.getCustomerSmsVos().stream().filter(smsVo -> {
						boolean fetch = false;
						if (smsVo.getPhone().contains(searchParams)) {
							return true;
						}
						if (StringUtils.isNotBlank(smsVo.getName())
								&& smsVo.getName().contains(searchParams)) {
							fetch = true;
						}
						return fetch;
					}).toList());
		}
		return vo;
	}

	@Override
	public void saveCustomerAndSubAccount(
			CustomerSmsSettingVo customerSmsSettingVo) {
		if (CollectionUtils
				.isNotEmpty(customerSmsSettingVo.getCustomerSmsVos())) {
			customerSmsSettingVo.getCustomerSmsVos().forEach(customerSmsVo -> {
				// 先保存主账号
				if (Objects.equals(customerSmsVo.getCustomerId(),
						customerSmsVo.getMainId())) {
					this.findById(customerSmsVo.getCustomerId())
							.ifPresent(customer -> {
								customer.setReceiptSms(
										customerSmsVo.getReceiptSms());
								this.update(customer);
							});
				} else {
					// 再保存子账号
					Optional<CustomerEnterprise> enterpriseOp = customerEnterpriseService
							.findByMainAndSubId(customerSmsVo.getCustomerId(),
									customerSmsVo.getMainId(),
									CustomerEnterpriseDef.State.CONFIRMED
											.getCode());
					if (enterpriseOp.isPresent()) {
						CustomerEnterprise enterprise = enterpriseOp.get();
						enterprise.setReceiptSms(customerSmsVo.getReceiptSms());
						customerEnterpriseService.update(enterprise);
					}
				}
			});
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void cancelApply(Customer customer) {
		customer.setApplyState(CommonDef.Symbol.NO.getCode());
		customer.setLegalRepresentative(null);
		customer.setInstitutionName(null);
		customer.setUnifiedSocialCreditCode(null);
		super.updateAllProperties(customer);
		// 将最新一条已认证的认证记录的是否取消设置成已取消
		institutionApplyService
				.findByCustomerIdAndStateLatest(customer.getId(),
						InstitutionApplyDef.State.APPROVED.getCode())
				.ifPresent(apply -> {
					apply.setIsCancel(CommonDef.Symbol.YES.getCode());
					institutionApplyService.updateAllProperties(apply);
				});

		// 刷新上下文
		TokenUtils.refreshCustomerContext(customer, redisClient,
				30 * 24 * 60 * 60L, Boolean.TRUE);

	}

	@Override
	public Optional<Customer> findBySubId(String subId) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		queryWrapper.eq(Customer::getSubId, subId);
		queryWrapper.eq(Customer::getDel, CommonDef.Symbol.NO.getCode());
		return Optional
				.ofNullable(repository.selectOne(queryWrapper, Boolean.FALSE));
	}

}
