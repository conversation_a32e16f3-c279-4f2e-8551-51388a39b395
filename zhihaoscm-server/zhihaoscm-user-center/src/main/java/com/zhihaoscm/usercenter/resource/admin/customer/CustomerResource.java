package com.zhihaoscm.usercenter.resource.admin.customer;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.util.utils.date.DateFormat;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.vo.CustomerVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.resource.validator.customer.CustomerValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "客户管理", description = "客户管理API")
@RestController
@RequestMapping(value = "/customer")
public class CustomerResource {

	@Autowired
	private CustomerService service;

	@Autowired
	private CustomerValidator validator;

	@Operation(summary = "查询客户列表")
	@GetMapping(value = "/paging")
	@Secured(value = { AdminPermissionDef.CUSTOMER_R,
			AdminPermissionDef.CUSTOMER_W })
	public ApiResponse<Page<CustomerVo>> paging(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder,
			@Parameter(description = "查询参数") @RequestParam(value = "param", required = false) String param,
			@Parameter(description = "状态") @RequestParam(value = "state", required = false) List<Integer> state,
			@Parameter(description = "数据来源") @RequestParam(value = "appType", required = false) Integer appType,
			@Parameter(description = "会员等级") @RequestParam(value = "memberLevel", required = false) List<Integer> memberLevel,
			@Parameter(description = "激活日期-开始时间") @DateTimeFormat(pattern = DateFormat.YYYY_MM_DD_HH_MM_SS) @RequestParam(value = "activeBeginTime", required = false) LocalDateTime activeBeginTime,
			@Parameter(description = "激活日期-结束时间") @DateTimeFormat(pattern = DateFormat.YYYY_MM_DD_HH_MM_SS) @RequestParam(value = "activeEndTime", required = false) LocalDateTime activeEndTime,
			@Parameter(description = "到期日期-开始日期") @DateTimeFormat(pattern = DateFormat.YYYY_MM_DD) @RequestParam(value = "expiryBeginDate", required = false) LocalDate expiryBeginDate,
			@Parameter(description = "到期日期-结束日期") @DateTimeFormat(pattern = DateFormat.YYYY_MM_DD) @RequestParam(value = "expiryEndDate", required = false) LocalDate expiryEndDate,
			@Parameter(description = "会员状态 1:激活 2:过期") @RequestParam(value = "memberState", required = false) Integer memberState) {
		return new ApiResponse<>(PageUtil
				.convert(service.paging(page, size, sortKey, sortOrder, param,
						state, memberLevel, activeBeginTime, activeEndTime,
						expiryBeginDate, expiryEndDate, memberState, appType)));
	}

	@Operation(summary = "根据ID查询客户信息")
	@GetMapping(value = "/find/{id}")
	@Secured(value = { AdminPermissionDef.CUSTOMER_R,
			AdminPermissionDef.CUSTOMER_W })
	public ApiResponse<CustomerVo> findVoById(
			@PathVariable(value = "id") Long id) {
		validator.validateId(id);
		return new ApiResponse<>(service.findVoById(id).orElse(null));
	}

	@Operation(summary = "查询客户下拉列表")
	@GetMapping(value = "/selector")
	public ApiResponse<List<Customer>> selector(
			@Parameter(description = "查询参数") @RequestParam(value = "param", required = false) String searchParam,
			@Parameter(description = "是否已进行个人认证 1 是 0 否") @RequestParam(required = false) Integer personalAuth,
			@Parameter(description = "是否已进行企业认证 1 是 0 否") @RequestParam(required = false) Integer enterpriseAuth,
			@Parameter(description = "是否已进行本系统组织认证认证 1 是 0 否") @RequestParam(required = false) Integer applyState) {
		return new ApiResponse<>(service.selector(searchParam, personalAuth,
				enterpriseAuth, applyState));
	}

	@Operation(summary = "选择链云用户")
	@GetMapping("/search")
	@Secured(value = { AdminPermissionDef.CUSTOMIZED_W })
	public ApiResponse<List<Customer>> searchCustomer(
			@Parameter(description = "用户账号") @RequestParam String searchParam) {
		return new ApiResponse<>(service.findByNameOrCodeOrMobile(searchParam));
	}

	@Operation(summary = "查询客户下拉列表-船运需求")
	@GetMapping(value = "/shipping/selector")
	public ApiResponse<List<CustomerVo>> shippingSelector(
			@RequestParam(value = "searchParam", required = false) String searchParam,
			@Parameter(description = "状态") @RequestParam(required = false) Integer state,
			@Parameter(description = "是否实名 true是 false否") @RequestParam(required = false) Boolean isReal) {
		return new ApiResponse<>(
				service.shippingSelector(searchParam, state, isReal));
	}

	@Operation(summary = "启用或禁用客户信息")
	@PutMapping(value = "/update/{id}/{state}")
	@Secured(value = { AdminPermissionDef.CUSTOMER_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = "{{#success}}", type = LogDef.USER_MANAGEMENT_CHAIN_CLOUD_USERS, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#customer.getCode()}}") })
	public ApiResponse<Void> updateState(@PathVariable(value = "id") Long id,
			@PathVariable(value = "state") Integer state) {
		Customer customer = validator.validate(id, state);
		service.updateState(id, state, CommonDef.UserType.OUTER.getCode());
		LogRecordContext.putVariable("success",
				CommonDef.Symbol.NO.match(state) ? LogDef.USER_DISABLE
						: LogDef.USER_ENABLE);
		LogRecordContext.putVariable("customer", customer);
		return new ApiResponse<>();
	}

	@Operation(summary = "清除缓存，自用接口")
	@DeleteMapping("/deleteCache")
	public void deleteCache(@RequestParam(required = false) String key) {
		service.deleteCache(key);
	}

	@Operation(summary = "取消认证")
	@PutMapping("/cancelApply/{id}")
	@Secured(AdminPermissionDef.CUSTOMER_W)
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE_STATUS, success = LogDef.USER_MANAGEMENT_CHAIN_CLOUD_USERS_RELIEVE, type = LogDef.USER_MANAGEMENT_CHAIN_CLOUD_USERS, bizNo = "{{#customer.id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#customer.getCode()}}") })
	public ApiResponse<Void> cancelApply(@PathVariable Long id) {
		Customer customer = validator.validateCancelApply(id);
		LogRecordContext.putVariable("customer", customer);
		service.cancelApply(customer);
		return new ApiResponse<>();
	}

	@Operation(summary = "恢复账号")
	@PutMapping("/restore/{id}")
	@Secured(AdminPermissionDef.CUSTOMER_W)
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE_STATUS, success = LogDef.USER_MANAGEMENT_CHAIN_CLOUD_USERS_RESTORE, type = LogDef.USER_MANAGEMENT_CHAIN_CLOUD_USERS, bizNo = "{{#customer.id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#customer.getCode()}}") })
	public ApiResponse<Void> restore(@PathVariable Long id) {
		Customer customer = validator.validateRestore(id);
		LogRecordContext.putVariable("customer", customer);
		service.restore(customer);
		return new ApiResponse<>();
	}

}
