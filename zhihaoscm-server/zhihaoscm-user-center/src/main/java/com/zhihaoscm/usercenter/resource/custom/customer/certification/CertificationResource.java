package com.zhihaoscm.usercenter.resource.custom.customer.certification;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.dto.PersonalAuthDto;
import com.zhihaoscm.domain.bean.vo.CustomerChangeMobileVo;
import com.zhihaoscm.domain.bean.vo.CustomerPersonalAuthVo;
import com.zhihaoscm.domain.bean.vo.CustomerVo;
import com.zhihaoscm.usercenter.config.properties.ContractLockProperties;
import com.zhihaoscm.usercenter.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.usercenter.core.service.CertificationServiceExt;
import com.zhihaoscm.usercenter.core.service.CustomerEnterpriseCertificationService;
import com.zhihaoscm.usercenter.core.service.PersonalCertificationService;
import com.zhihaoscm.usercenter.resource.form.customer.certification.CustomerPersonalAuthForm;
import com.zhihaoscm.usercenter.resource.validator.customer.certification.CertificationValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "客户认证", description = "客户认证API")
@RestController
@RequestMapping(value = "/customer/certification")
public class CertificationResource {

	@Autowired
	private PersonalCertificationService personalCertificationService;

	@Autowired
	private CustomerEnterpriseCertificationService customerEnterpriseCertificationService;

	@Autowired
	private CertificationServiceExt service;

	@Autowired
	private ContractLockProperties properties;

	@Autowired
	private CertificationValidator validator;

	@Operation(summary = "获取客户个人认证链接")
	@PostMapping(value = "/personal/auth")
	public ApiResponse<CustomerPersonalAuthVo> personalAuth(
			@Validated @RequestBody CustomerPersonalAuthForm form,
			@RequestParam(value = "origin") Integer origin) {
		PersonalAuthDto personalAuthDto = form.convertToDto(properties);
		return new ApiResponse<>(personalCertificationService.personalAuth(
				personalAuthDto.getCustomerId(), personalAuthDto.getMobile(),
				personalAuthDto.getName(), personalAuthDto.getIdNo(),
				personalAuthDto.getCallbackeUrl(), origin,
				personalAuthDto.getCallbackPage()));
	}

	@Operation(summary = "主动获取个人认证结果")
	@GetMapping(value = "/personal/auth/result")
	public ApiResponse<CustomerPersonalAuthVo> personalAuthResult() {
		return new ApiResponse<>(personalCertificationService
				.personalAuthResult(service.findCustomerFromContext().getId()));
	}

	@Operation(summary = "查询用户认证详情")
	@GetMapping(value = "/detail")
	public ApiResponse<CustomerVo> certDetail() {
		return new ApiResponse<>(customerEnterpriseCertificationService
				.certDetail(Objects.requireNonNull(CustomerContextHolder
						.getCustomerLoginVo().getActualAccount().getId()))
				.orElse(null));
	}

	@Operation(summary = "换绑手机号-获取客户个人认证链接")
	@PostMapping(value = "/change/personal/auth")
	public ApiResponse<CustomerPersonalAuthVo> cmPersonalAuth(
			@Validated @RequestBody CustomerPersonalAuthForm form,
			@RequestParam(value = "origin") Integer origin) {
		PersonalAuthDto personalAuthDto = validator.validateCmPersonalAuth(form,
				properties);
		return new ApiResponse<>(personalCertificationService.cmPersonalAuth(
				personalAuthDto.getCustomerId(), personalAuthDto.getMobile(),
				personalAuthDto.getName(), personalAuthDto.getIdNo(),
				personalAuthDto.getCallbackeUrl(), origin,
				personalAuthDto.getCallbackPage()));
	}

	@Operation(summary = "换绑手机号-主动获取个人认证结果")
	@GetMapping(value = "/change/personal/auth/result")
	public ApiResponse<CustomerPersonalAuthVo> cmPersonalAuthResult() {
		return new ApiResponse<>(
				personalCertificationService.cmPersonalAuthResult(
						service.findCustomerFromContext().getId()));
	}

	@Operation(summary = "换绑手机号-查询用户认证详情")
	@GetMapping(value = "/change/detail")
	public ApiResponse<CustomerChangeMobileVo> cmCertDetail() {
		return new ApiResponse<>(customerEnterpriseCertificationService
				.cmCertDetail(Objects.requireNonNull(CustomerContextHolder
						.getCustomerLoginVo().getActualAccount().getId()))
				.orElse(null));
	}
}
