package com.zhihaoscm.usercenter.config.security.custom.apple;

import java.util.Objects;
import java.util.Optional;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationConverter;

import com.zhihaoscm.common.api.util.CommonWebUtils;
import com.zhihaoscm.common.util.utils.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

public class AppleAuthenticationConverter implements AuthenticationConverter {

	@Override
	public Authentication convert(HttpServletRequest request) {
		Optional<AppleLoginForm> login = CommonWebUtils.getRequestBody(request,
				AppleLoginForm.class);

		if (login.isEmpty()) {
			throw new BadCredentialsException(
					"missing apple authentication info!");
		}

		String identityToken = login.get().getIdentityToken();

		if (StringUtils.isBlank(identityToken)) {
			throw new BadCredentialsException("missing identityToken!");
		}

		Integer role = login.get().getAppType();
		if (Objects.isNull(role)) {
			throw new BadCredentialsException("missing appType!");
		}

		return new AppleAuthentication(login.get());
	}
}
