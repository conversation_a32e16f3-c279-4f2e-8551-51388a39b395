package com.zhihaoscm.usercenter.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@Configuration
@ConfigurationProperties(prefix = SMSProperties.PREFIX)
@Data
public class SMSProperties {

	final static String PREFIX = "aliyun.sms";

	// 登录验证码
	private String loginVerifyCode;
	// 我有意向提交验证码
	private String intentionVerifyCode;
	// 会员续费提醒
	private String memberRenewalReminder;
	// pc端换绑手机号成功后发给新手机号
	private String changeCustomerMobileCode;
	/** 意向详情 */
	private String intendDetailPage;
	/** 付款详情 */
	private String paymentDetailPage;
	/** 收款详情 */
	private String receiptDetailPage;
	/** 找船需求详情 */
	private String shipDetailPage;
	/** 船运单详情 */
	private String transportOrderPage;
	/** 承运商船运单详情 */
	private String transferTransportOrderPage;
	/** 承运商抢单详情 */
	private String transferShipRequirePage;
	/** 会员页 */
	private String membershipPage;
	/** 承运商会员页 */
	private String transferMembershipPage;
	/** 受邀请记录详情 */
	private String invitationRecordPage;
	/** 提货单详情 */
	private String purchaseGoodPage;
	/** 项目详情 */
	private String projectPage;
	/** 短信链接小程序 URL Link 失效类型，失效时间：0，失效间隔天数：1 */
	private Integer expireType;
	/** 短信链接失效时间数，默认最长时限30天 */
	private Integer expireNum;
	/** app短信链接 */
	private String appLinkUrl;

}
