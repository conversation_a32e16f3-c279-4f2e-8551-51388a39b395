package com.zhihaoscm.usercenter.resource.custom.callback;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.MD5Utils;
import com.zhihaoscm.domain.bean.dto.PersonalCallbackDto;
import com.zhihaoscm.usercenter.config.properties.ContractLockProperties;
import com.zhihaoscm.usercenter.core.service.PersonalCertificationService;
import com.zhihaoscm.usercenter.utils.CryptUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "回调", description = "回调API")
@RestController
@RequestMapping(value = "/callback")
public class CallbackResource {

	@Autowired
	private PersonalCertificationService personalCertificationService;

	@Autowired
	private ContractLockProperties properties;

	@Operation(summary = "个人认证回调")
	@PostMapping(value = "/personal/certification")
	public ApiResponse<Void> personalCertification(
			@RequestParam Map<String, String> request) {
		log.info("个人认证回调参数：{}", JsonUtils.objectToJson(request));
		this.verifySignature(request);
		PersonalCallbackDto dto = this.assembleCallbackParam(request,
				PersonalCallbackDto.class);
		Integer code = personalCertificationService.personalCallback(
				dto.getMode(), dto.getStatus(), dto.getAuthId());
		ApiResponse<Void> response = new ApiResponse<>();
		response.setCode(String.valueOf(code));
		return response;
	}

	@Operation(summary = "换绑手机号-个人认证回调")
	@PostMapping(value = "/change/personal/certification")
	public ApiResponse<Void> cmPersonalCertification(
			@RequestParam Map<String, String> request) {
		log.info("换绑手机号-个人认证回调参数：{}", JsonUtils.objectToJson(request));
		this.verifySignature(request);
		PersonalCallbackDto dto = this.assembleCallbackParam(request,
				PersonalCallbackDto.class);
		Integer code = personalCertificationService.cmPersonalCallback(
				dto.getMode(), dto.getStatus(), dto.getAuthId());
		ApiResponse<Void> response = new ApiResponse<>();
		response.setCode(String.valueOf(code));
		return response;
	}

	/**
	 * 校验签名
	 * 
	 * @param request
	 */
	private void verifySignature(Map<String, String> request) {
		String sig = MD5Utils
				.md5Hex(request.get("timestamp") + properties.getSecretKey());
		Assert.state(sig.equals(request.get("signature")), "签名校验失败");
	}

	/**
	 * 组装回调参数
	 *
	 * @param request
	 * @return
	 */
	private <T> T assembleCallbackParam(Map<String, String> request,
			Class<T> clazz) {
		String derypt;
		try {
			derypt = CryptUtils.aesDerypt(request.get("content"),
					properties.getSecretKey());
			log.info("第三方认证接口回调参数解密 -> {}", derypt);
			return JsonUtils.jsonToObject(derypt, clazz);
		} catch (Exception e) {
			log.error("企业认证回调参数解密失败 -> {}", e.getMessage(), e);
			throw new BadRequestException("企业认证回调参数解密失败");
		}
	}

}
