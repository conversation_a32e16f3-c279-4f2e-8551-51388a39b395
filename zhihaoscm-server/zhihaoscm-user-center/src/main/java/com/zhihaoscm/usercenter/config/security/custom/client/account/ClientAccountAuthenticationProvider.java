
package com.zhihaoscm.usercenter.config.security.custom.client.account;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.SecuritySettingDevice;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AppTypeDef;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.config.security.custom.ValidatedAuthentication;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.CustomerDisableException;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.CustomerNotFoundException;
import com.zhihaoscm.usercenter.core.service.CustomerEnterpriseService;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.core.service.SecuritySettingDeviceService;
import com.zhihaoscm.usercenter.core.service.WxService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClientAccountAuthenticationProvider
		implements AuthenticationProvider {

	private final StringRedisClient redisClient;

	private final CustomerService customerService;

	private final WxMiniProgramProperties properties;

	private final CustomerEnterpriseService customerEnterpriseService;

	private final SecuritySettingDeviceService securitySettingDeviceService;

	public ClientAccountAuthenticationProvider(StringRedisClient redisClient,
			CustomerService customerService, WxMiniProgramProperties properties,
			CustomerEnterpriseService customerEnterpriseService,
			SecuritySettingDeviceService securitySettingDeviceService) {
		this.redisClient = redisClient;
		this.customerService = customerService;
		this.properties = properties;
		this.customerEnterpriseService = customerEnterpriseService;
		this.securitySettingDeviceService = securitySettingDeviceService;

	}

	@Override
	public Authentication authenticate(Authentication authentication)
			throws AuthenticationException {
		log.info("custom pc login param -> {}",
				JsonUtils.objectToJson(authentication));
		ClientAccountLoginForm loginForm = (ClientAccountLoginForm) authentication
				.getPrincipal();
		// 获取用户
		Customer customer = customerService
				.findByMobileOrEmail(loginForm.getAccount()).orElse(null);
		// 判断其状态是否异常
		if (Objects.isNull(customer)) {
			throw new CustomerNotFoundException(ErrorCode.CODE_30059017, null);
		} else if (CustomerDef.State.INACTIVE.match(customer.getState())) {
			throw new CustomerDisableException(ErrorCode.CODE_30059008, null);
		} else if (CustomerDef.State.CANCELLED.match(customer.getState())) {
			throw new CustomerDisableException(ErrorCode.CODE_30059024, null);
		}
		this.checkLoginPassword(loginForm, customer);

		if (!"***********".equals(loginForm.getAccount())) {
			// 校验是否绑定该设备
			if (Objects.nonNull(loginForm.getDeviceCode())) {
				SecuritySettingDevice securitySettingDevice = securitySettingDeviceService
						.findByCustomerIdAndDeviceCode(customer.getId(),
								loginForm.getDeviceCode())
						.orElse(null);

				if (Objects.nonNull(securitySettingDevice)) {
					securitySettingDevice.setLoginTime(LocalDateTime.now());
					securitySettingDeviceService
							.updateAllProperties(securitySettingDevice);
				} else {
					throw new CustomerDisableException(ErrorCode.CODE_30152001,
							null);
				}
			}
		}

		// 组装代理账号信息
		CustomerLoginVo customerLoginVo = new CustomerLoginVo();
		customerLoginVo.setActualAccount(customer);
		customerLoginVo.setRole(loginForm.getAppType());
		customerLoginVo.setProxyAccount(customer);
		if (!WxService.AppType.chuanwu.name().equals(
				AppTypeDef.AppType.from(loginForm.getAppType()).getName())) {
			// 获取用户上一次登录的代理账号
			String lastLoginKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
					+ loginForm.getAccount();
			String encryptedKey = UsernameSalt.encryptMiniLogin(lastLoginKey);
			String proxyAccountId = redisClient.get(encryptedKey);
			if (StringUtils.isNotBlank(proxyAccountId)) {
				Customer clientById = customerService
						.findById(Long.valueOf(proxyAccountId)).orElse(null);
				if (Objects.nonNull(clientById)) {
					customerLoginVo.setProxyAccount(clientById);
				}
				redisClient.setEx(encryptedKey, proxyAccountId,
						properties.getLoginTokenExpire(), TimeUnit.SECONDS);
			}
		}

		return new ValidatedAuthentication(new LoginUser<>(customerLoginVo,
				String.valueOf(loginForm.getAppType()), customer.getMobile(), List.of()), null, loginForm);
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return ClientAccountAuthentication.class
				.isAssignableFrom(authentication);
	}

	/**
	 * 校验登录密码
	 *
	 * @param loginForm
	 * @param customer
	 */
	private void checkLoginPassword(ClientAccountLoginForm loginForm,
			Customer customer) {
		BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
		if (!passwordEncoder.matches(loginForm.getPassword(),
				customer.getPassword())) {
			throw new CustomerNotFoundException(ErrorCode.CODE_30059017, null);
		}
	}

}
