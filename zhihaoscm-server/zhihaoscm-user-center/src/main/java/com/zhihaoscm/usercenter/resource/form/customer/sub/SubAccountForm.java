package com.zhihaoscm.usercenter.resource.form.customer.sub;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.CustomerEnterprise;
import com.zhihaoscm.domain.meta.biz.CustomerEnterpriseDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.security.custom.CustomerContextHolder;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(name = "SubAccountForm", title = "新增子账号表单对象")
public class SubAccountForm {

	@Schema(description = "受邀人手机号")
	@NotBlank(message = ErrorCode.CODE_30055001)
	private String mobile;

	public CustomerEnterprise convert(Long subAccountId) {
		CustomerEnterprise customerEnterprise = new CustomerEnterprise();
		customerEnterprise.setMainAccountId(CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		customerEnterprise.setSubAccountId(subAccountId);
		customerEnterprise
				.setState(CustomerEnterpriseDef.State.NOT_CONFIRMED.getCode());
		customerEnterprise.setAccountState(CommonDef.Symbol.YES.getCode());
		return customerEnterprise;
	}
}
