package com.zhihaoscm.usercenter.core.service.impl;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.InstitutionApply;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.InstitutionApplyCountVo;
import com.zhihaoscm.domain.bean.vo.InstitutionApplyVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.InstitutionApplyDef;
import com.zhihaoscm.domain.meta.biz.UserMessageConstants;
import com.zhihaoscm.domain.meta.biz.UserMessageDef;
import com.zhihaoscm.domain.meta.biz.WxwDef;
import com.zhihaoscm.usercenter.core.mapper.InstitutionApplyMapper;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.core.service.InstitutionApplyService;
import com.zhihaoscm.usercenter.core.service.MessageService;
import com.zhihaoscm.usercenter.core.service.UserService;
import com.zhihaoscm.usercenter.utils.TokenUtils;

/**
 * <p>
 * 组织机构认证 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@Service
public class InstitutionApplyServiceImpl extends
		MpLongIdBaseServiceImpl<InstitutionApply, InstitutionApplyMapper>
		implements InstitutionApplyService {

	@Autowired
	private CustomerService customerService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private UserService userService;
	@Autowired
	private StringRedisClient redisClient;

	public InstitutionApplyServiceImpl(InstitutionApplyMapper repository) {
		super(repository);
	}

	@Override
	public Page<InstitutionApplyVo> paging(Integer page, Integer size,
			String institutionName, Integer state, String sortKey,
			String sortOrder) {
		LambdaQueryWrapper<InstitutionApply> wrapper = Wrappers
				.lambdaQuery(InstitutionApply.class);
		this.filterDeleted(wrapper);
		wrapper.like(StringUtils.isNotBlank(institutionName),
				InstitutionApply::getInstitutionName, institutionName);
		wrapper.eq(Objects.nonNull(state), InstitutionApply::getState, state);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照更新时间降序排列
			wrapper.orderByAsc(InstitutionApply::getState);
			wrapper.orderByDesc(InstitutionApply::getCreatedTime);
		}
		Page<InstitutionApply> paging = repository
				.selectPage(new Page<>(page, size), wrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Optional<InstitutionApplyVo> findVoById(Long id) {
		return super.findOne(id).map(this::packVo);
	}

	@Override
	public Optional<InstitutionApply> findByCustomerIdAndStateLatest(
			Long customerId, Integer state) {
		LambdaQueryWrapper<InstitutionApply> wrapper = Wrappers
				.lambdaQuery(InstitutionApply.class);
		this.filterDeleted(wrapper);
		wrapper.eq(InstitutionApply::getCustomerId, customerId);
		wrapper.eq(Objects.nonNull(state), InstitutionApply::getState, state);
		wrapper.orderByDesc(InstitutionApply::getCreatedTime);
		wrapper.last("limit 1");
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public Optional<InstitutionApply> findByCustomerIdAndStateCancel(
			Long customerId, Integer state, Integer cancel) {
		LambdaQueryWrapper<InstitutionApply> wrapper = Wrappers
				.lambdaQuery(InstitutionApply.class);
		this.filterDeleted(wrapper);
		wrapper.eq(InstitutionApply::getCustomerId, customerId);
		wrapper.eq(Objects.nonNull(state), InstitutionApply::getState, state);
		wrapper.eq(Objects.nonNull(cancel), InstitutionApply::getIsCancel,
				cancel);
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public Optional<InstitutionApplyCountVo> staticsInstitutionApply(
			boolean hasFull) {
		InstitutionApplyCountVo institutionApplyCountVo = new InstitutionApplyCountVo();
		institutionApplyCountVo.setReviewedCount(0L);
		if (hasFull) {
			LambdaQueryWrapper<InstitutionApply> queryWrapper = Wrappers
					.lambdaQuery(InstitutionApply.class);
			// 统计待审核
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(InstitutionApply::getDel,
					CommonDef.Symbol.NO.getCode());
			queryWrapper.eq(InstitutionApply::getState,
					InstitutionApplyDef.State.PENDING_APPROVE.getCode());
			institutionApplyCountVo
					.setReviewedCount(repository.selectCount(queryWrapper));
			queryWrapper.clear();
		}
		return Optional.of(institutionApplyCountVo);
	}

	@Override
	public List<InstitutionApply> findByUnifiedSocialCreditCodeAndStateAndIsCancel(
			String unifiedSocialCreditCode, Integer state, Integer isCancel,
			Long excludeId) {
		LambdaQueryWrapper<InstitutionApply> wrapper = Wrappers
				.lambdaQuery(InstitutionApply.class);
		this.filterDeleted(wrapper);
		wrapper.eq(InstitutionApply::getUnifiedSocialCreditCode,
				unifiedSocialCreditCode);
		wrapper.eq(InstitutionApply::getState, state);
		wrapper.eq(InstitutionApply::getIsCancel, isCancel);
		wrapper.ne(Objects.nonNull(excludeId), InstitutionApply::getId,
				excludeId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<InstitutionApply> findBySearchParam(String searchParam) {
		LambdaQueryWrapper<InstitutionApply> wrapper = Wrappers
				.lambdaQuery(InstitutionApply.class);
		wrapper.eq(InstitutionApply::getState,
				InstitutionApplyDef.State.APPROVED.getCode());
		wrapper.eq(InstitutionApply::getIsCancel,
				CommonDef.Symbol.NO.getCode());
		wrapper.like(InstitutionApply::getInstitutionName, searchParam);
		return repository.selectList(wrapper);
	}

	@Override
	public List<InstitutionApply> findByCustomerIds(List<Long> customerIds) {
		if (CollectionUtils.isEmpty(customerIds)) {
			return List.of();
		}
		LambdaQueryWrapper<InstitutionApply> wrapper = Wrappers
				.lambdaQuery(InstitutionApply.class);
		this.filterDeleted(wrapper);
		wrapper.eq(InstitutionApply::getState,
				InstitutionApplyDef.State.APPROVED.getCode());
		wrapper.eq(InstitutionApply::getIsCancel,
				CommonDef.Symbol.NO.getCode());
		wrapper.in(CollectionUtils.isNotEmpty(customerIds),
				InstitutionApply::getCustomerId, customerIds);

		return repository.selectList(wrapper);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@FileId
	public InstitutionApply create(InstitutionApply resource) {
		InstitutionApply institutionApply = super.create(resource);
		this.sendNotice(institutionApply);
		return institutionApply;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@FileId(type = 2)
	public InstitutionApply updateAllProperties(InstitutionApply resource) {
		InstitutionApply institutionApply = super.updateAllProperties(resource);
		this.sendNotice(institutionApply);
		return institutionApply;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void approve(InstitutionApply institutionApply) {
		// 如果审核通过 将客户表认证状态修改成已认证
		if (InstitutionApplyDef.State.APPROVED
				.match(institutionApply.getState())) {
			customerService.findOne(institutionApply.getCustomerId())
					.ifPresent(customer -> {
						customer.setApplyState(CommonDef.Symbol.YES.getCode());
						customer.setUnifiedSocialCreditCode(
								institutionApply.getUnifiedSocialCreditCode());
						customer.setInstitutionName(
								institutionApply.getInstitutionName());
						customer.setLegalRepresentative(
								institutionApply.getLegalRepresentative());
						customerService.updateAllProperties(customer);

						// 刷新上下文
						TokenUtils.refreshCustomerContext(customer, redisClient,
								30 * 24 * 60 * 60L, Boolean.TRUE);
					});
		}
		this.sendNotice(institutionApply,
				InstitutionApplyDef.State.APPROVED
						.match(institutionApply.getState())
								? CommonDef.Symbol.NO.getCode()
								: CommonDef.Symbol.YES.getCode());
		super.updateAllProperties(institutionApply);
	}

	/**
	 * 组装vo
	 *
	 * @param records
	 * @return
	 */
	private List<InstitutionApplyVo> packVo(List<InstitutionApply> records) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		List<Long> customerIds = records.stream()
				.map(InstitutionApply::getCustomerId).distinct().toList();
		List<Customer> customerList = customerService.findByIds(customerIds);
		Map<Long, Customer> customerMap = customerService
				.buildIdMap(customerList);
		return records.stream().map(institutionApply -> {
			InstitutionApplyVo institutionApplyVo = new InstitutionApplyVo();
			institutionApplyVo.setInstitutionApply(institutionApply);
			if (Objects.nonNull(institutionApply.getCustomerId())) {
				Customer customer = customerMap
						.get(institutionApply.getCustomerId());
				institutionApplyVo.setCustomer(customer);
			}
			return institutionApplyVo;
		}).toList();
	}

	/**
	 * 组装vo
	 *
	 * @param institutionApply
	 * @return
	 */
	private InstitutionApplyVo packVo(InstitutionApply institutionApply) {
		InstitutionApplyVo institutionApplyVo = new InstitutionApplyVo();
		institutionApplyVo.setInstitutionApply(institutionApply);
		if (Objects.nonNull(institutionApply.getCustomerId())) {
			customerService.findOne(institutionApply.getCustomerId())
					.ifPresent(institutionApplyVo::setCustomer);
		}
		return institutionApplyVo;
	}

	/**
	 * 发送通知
	 *
	 * @param institutionApply
	 */
	private void sendNotice(InstitutionApply institutionApply) {
		messageService.sendNotice(WxwMessage.builder().receiptors(userService
				.findUsersByPermission(AdminPermissionDef.ORG_REVIEW, null)
				.stream().map(user -> String.valueOf(user.getId())).toList())
				.url("/lianYunUser/userManage/institutionApply/audit/"
						.concat(String.valueOf(institutionApply.getId())))
				.prefix(StringUtils.EMPTY)
				.operationModule(WxwDef.NoticeOperationModule.USER.getDesc())
				.desc("，请审核").keyword(String.valueOf(institutionApply.getId()))
				.content(StringUtils.EMPTY).build());
	}

	/**
	 * 站内消息
	 *
	 * @param institutionApply
	 */
	private void sendNotice(InstitutionApply institutionApply, Integer status) {
		Customer customer = customerService
				.findOne(institutionApply.getCustomerId()).orElse(null);
		assert customer != null;
		messageService.sendNotice(UserMessage.builder()
				.type(UserMessageDef.MessageType.OTHER.getCode())
				.title(MessageFormat.format(CommonDef.Symbol.NO.match(status)
						? UserMessageConstants.ORGANIZATION_CERTIFICATION_AGREE
						: UserMessageConstants.ORGANIZATION_CERTIFICATION_TURNDOWN,
						institutionApply.getInstitutionName()))
				.receiptors(List.of(String.valueOf(customer.getId())))
				.url(UserMessageConstants.ORGANIZATION_CERTIFICATION)
				.detailId(String.valueOf(institutionApply.getId()))
				.initiator(UserMessageDef.BusinessInitiator.receipt.getCode())
				.build());
	}
}
