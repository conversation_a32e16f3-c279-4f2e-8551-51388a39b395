package com.zhihaoscm.usercenter.config.security.custom.wx;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.sdk.wx.miniapp.api.login.Code2SessionResponse;
import com.zhihaoscm.common.sdk.wx.miniapp.api.login.LoginClient;
import com.zhihaoscm.common.sdk.wx.miniapp.api.phone.PhonenumberClient;
import com.zhihaoscm.common.sdk.wx.miniapp.api.phone.PhonenumberRequest;
import com.zhihaoscm.common.sdk.wx.miniapp.api.phone.PhonenumberResponse;
import com.zhihaoscm.common.sdk.wx.miniapp.api.pz.AccessTokenClient;
import com.zhihaoscm.common.sdk.wx.miniapp.api.pz.AccessTokenResponse;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AppTypeDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.config.security.custom.ValidatedAuthentication;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.CustomerDisableException;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.CustomerNotFoundException;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.WxAccessTokenNotGetException;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.WxPhoneNoNotGetException;
import com.zhihaoscm.usercenter.core.service.CustomerEnterpriseService;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.core.service.WxService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WxAuthenticationProvider implements AuthenticationProvider {

	private final StringRedisClient redisClient;

	private final CustomerService customerService;

	private final WxMiniProgramProperties properties;

	private final AccessTokenClient accessTokenClient;

	private final PhonenumberClient phoneClient;

	private final LoginClient loginClient;

	private final CustomerEnterpriseService customerEnterpriseService;

	/**
	 * 使用临时凭证code调用微信的getAccessToken接口时需要传递的授权类型，此处只需填写 client_credential
	 */
	private final static String GRANT_TYPE = "client_credential";

	public WxAuthenticationProvider(StringRedisClient redisClient,
			CustomerService customerService, WxMiniProgramProperties properties,
			AccessTokenClient accessTokenClient, PhonenumberClient phoneClient,
			LoginClient loginClient,
			CustomerEnterpriseService customerEnterpriseService) {
		this.redisClient = redisClient;
		this.customerService = customerService;
		this.properties = properties;
		this.accessTokenClient = accessTokenClient;
		this.phoneClient = phoneClient;
		this.loginClient = loginClient;
		this.customerEnterpriseService = customerEnterpriseService;
	}

	@Override
	public Authentication authenticate(Authentication authentication)
			throws AuthenticationException {
		log.info("mini app login param => {}", authentication);
		WxLoginForm uniqueKey = (WxLoginForm) authentication.getPrincipal();
		String mobile;
		if (StringUtils.isBlank(uniqueKey.getMobile())) {
			// 获取调用微信access_token接口的参数
			AccessTokenForm tokenParam = this.getAccessTokenParam(
					AppTypeDef.AppType.from(uniqueKey.getAppType()).getName());
			log.info("mini app login token param => {}",
					JsonUtils.objectToJson(tokenParam));
			// 调用微信获取登录令牌接口
			AccessTokenResponse tokenResponse = this.getAccessToken(tokenParam);
			// 调用微信接口通过临时凭证获取用户手机号
			mobile = this.getMobile(tokenResponse, uniqueKey);
		} else {
			mobile = uniqueKey.getMobile();
		}

		Customer customer;
		customer = customerService.findByMobile(mobile).orElse(null);

		// 判断是否有注册
		if (Objects.isNull(customer)) {
			// 将手机号作为key,临时凭证code作为value存入redis缓存,以便在后续注册时校验,过期时长为2小时
			String key = RedisKeys.Cache.MINI_REGISTER_PREFIX + mobile;
			redisClient.setEx(UsernameSalt.encryptUsername(key),
					uniqueKey.getTempVoucherCode(),
					properties.getTempVoucherExpire(), TimeUnit.SECONDS);
			throw new CustomerNotFoundException(ErrorCode.CODE_20001003,
					mobile);
		} else if (CommonDef.Symbol.NO.getCode().equals(customer.getState())) {
			throw new CustomerDisableException(ErrorCode.CODE_30059008, mobile);
		}

		// 组装代理账号信息
		CustomerLoginVo customerLoginVo = new CustomerLoginVo();
		customerLoginVo.setActualAccount(customer);
		customerLoginVo.setRole(uniqueKey.getAppType());
		customerLoginVo.setProxyAccount(customer);

		// 若客户非承运商身份时,将用户信息存入redis缓存,
		// key为客户的手机号,value是客户的角色编码,承运商端暂时没有代理账号不做其他处理
		if (WxService.AppType.chuanwu.name().equals(
				AppTypeDef.AppType.from(uniqueKey.getAppType()).getName())) {
			if (StringUtils.isBlank(customer.getWxOpenId())) {
				// 若客户是承运商身份,但没有微信openid则给他设置进去
				String res = loginClient.code2Session(
						properties.getCarrierAppid(),
						properties.getCarrierSecret(),
						uniqueKey.getOpenidCode(), "authorization_code");
				log.info("carrier code2Session res => {}", res);
				Code2SessionResponse response = JsonUtils.jsonToObject(res,
						Code2SessionResponse.class);
				if (StringUtils.isBlank(response.getErrmsg())) {
					customer.setWxOpenId(response.getOpenid());
					customerService.update(customer);
				}
			}
		} else {
			// 获取用户上一次登录的代理账号
			String lastLoginKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
					+ mobile;
			String encryptedKey = UsernameSalt.encryptMiniLogin(lastLoginKey);
			String proxyAccountId = redisClient.get(encryptedKey);
			if (StringUtils.isNotBlank(proxyAccountId)) {
				Customer clientById = customerService
						.findById(Long.valueOf(proxyAccountId)).orElse(null);
				if (Objects.nonNull(clientById)) {
					customerLoginVo.setProxyAccount(clientById);
				}
				redisClient.setEx(encryptedKey, proxyAccountId,
						properties.getLoginTokenExpire(), TimeUnit.SECONDS);
			}
		}

		return new ValidatedAuthentication(
				new LoginUser<>(customerLoginVo, String.valueOf(uniqueKey.getAppType()), mobile, List.of()), null,
				null);
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return WxAuthentication.class.isAssignableFrom(authentication);
	}

	/**
	 * 获取access_token参数
	 * 
	 * @param appType
	 *            角色编码
	 * @return 参数
	 */
	private AccessTokenForm getAccessTokenParam(String appType) {
		AccessTokenForm form = new AccessTokenForm();
		form.setGrantType(GRANT_TYPE);
		if (Objects.nonNull(appType)
				&& appType.equals(WxService.AppType.chuanwu.name())) {
			form.setAppid(properties.getCarrierAppid());
			form.setSecret(properties.getCarrierSecret());
		} else {
			form.setAppid(properties.getAppid());
			form.setSecret(properties.getSecret());
		}
		return form;
	}

	/**
	 * 获取调用微信获取用户手机号的接口
	 *
	 * @param tokenResponse
	 * @param uniqueKey
	 * @return
	 */
	private String getMobile(AccessTokenResponse tokenResponse,
			WxLoginForm uniqueKey) {
		PhonenumberResponse phoneResponse;
		try {
			phoneResponse = phoneClient.getPhoneNumber(
					tokenResponse.getAccess_token(),
					new PhonenumberRequest(uniqueKey.getTempVoucherCode()));
		} catch (Exception e) {
			log.error("调用微信接口通过临时凭证获取用户手机号异常! => {}", e.getMessage(), e);
			throw new WxPhoneNoNotGetException("调用微信获取用户手机号异常!");
		}
		if (!ApiResponse.SUCCESS_CODE
				.equals(String.valueOf(phoneResponse.getErrcode()))) {
			log.error("调用微信接口通过临时凭证获取用户手机号失败! => {}",
					phoneResponse.getErrmsg());
			throw new WxPhoneNoNotGetException("调用微信获取用户手机号失败!");
		}
		return phoneResponse.getPhone_info().getPurePhoneNumber();
	}

	/**
	 * 调用微信access_token接口
	 *
	 * @param tokenParam
	 * @return
	 */
	private AccessTokenResponse getAccessToken(AccessTokenForm tokenParam) {
		AccessTokenResponse tokenResponse;
		try {
			tokenResponse = accessTokenClient.getAccessToken(
					tokenParam.getGrantType(), tokenParam.getAppid(),
					tokenParam.getSecret());
		} catch (Exception e) {
			log.error("调用微信获取登录令牌接口异常! => {}", e.getMessage(), e);
			throw new WxAccessTokenNotGetException(ErrorCode.CODE_30059001);
		}
		if (StringUtils.isNotBlank(tokenResponse.getErrmsg())) {
			log.error("调用微信获取登录令牌接口失败! => {}", tokenResponse.getErrmsg());
			throw new WxAccessTokenNotGetException(ErrorCode.CODE_30059002);
		}
		return tokenResponse;
	}

}
