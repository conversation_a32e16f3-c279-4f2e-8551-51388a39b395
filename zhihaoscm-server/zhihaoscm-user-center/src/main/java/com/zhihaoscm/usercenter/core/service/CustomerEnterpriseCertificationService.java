package com.zhihaoscm.usercenter.core.service;

import java.util.Optional;

import com.zhihaoscm.domain.bean.vo.CustomerChangeMobileVo;
import com.zhihaoscm.domain.bean.vo.CustomerVo;

/**
 * <p>
 * 客户企业认证记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
public interface CustomerEnterpriseCertificationService {

	/**
	 * 查询企业认证详情
	 *
	 * @param customerId
	 * @return
	 */
	Optional<CustomerVo> certDetail(Long customerId);

	/**
	 * 换绑手机号-查询企业认证详情
	 *
	 * @param customerId
	 * @return
	 */
	Optional<CustomerChangeMobileVo> cmCertDetail(Long customerId);

}
