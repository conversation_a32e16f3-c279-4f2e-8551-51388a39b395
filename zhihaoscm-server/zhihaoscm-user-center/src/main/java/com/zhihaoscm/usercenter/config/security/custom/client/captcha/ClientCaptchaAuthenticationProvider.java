package com.zhihaoscm.usercenter.config.security.custom.client.captcha;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerWx;
import com.zhihaoscm.domain.bean.entity.SecuritySettingDevice;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AppTypeDef;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.MockDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.config.security.custom.ValidatedAuthentication;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.CustomerDisableException;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.CustomerNotFoundException;
import com.zhihaoscm.usercenter.core.service.*;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClientCaptchaAuthenticationProvider
		implements AuthenticationProvider {

	private final StringRedisClient redisClient;

	private final CustomerService customerService;

	private final WxMiniProgramProperties properties;

	private final CustomerEnterpriseService customerEnterpriseService;

	private final SecuritySettingDeviceService securitySettingDeviceService;

	private final CustomerWxService customerWxService;

	public ClientCaptchaAuthenticationProvider(StringRedisClient redisClient,
			CustomerService customerService, WxMiniProgramProperties properties,
			CustomerEnterpriseService customerEnterpriseService,
			SecuritySettingDeviceService securitySettingDeviceService,
			CustomerWxService customerWxService) {
		this.redisClient = redisClient;
		this.customerService = customerService;
		this.properties = properties;
		this.customerEnterpriseService = customerEnterpriseService;
		this.securitySettingDeviceService = securitySettingDeviceService;
		this.customerWxService = customerWxService;

	}

	@Override
	public Authentication authenticate(Authentication authentication)
			throws AuthenticationException {
		log.info("custom pc login param -> {}",
				JsonUtils.objectToJson(authentication));
		ClientCaptchaLoginForm loginForm = (ClientCaptchaLoginForm) authentication
				.getPrincipal();
		// 校验登录验证码
		if (!MockDef.isMock.apply(loginForm.getMobile(), "pc:mock",
				redisClient)) {
			// 不在白名单中,校验验证码
			this.checkLoginCaptchaCode(loginForm);
		}
		// 获取用户
		Customer customer = customerService.findByMobile(loginForm.getMobile())
				.orElse(null);
		// 判断其状态是否异常
		if (Objects.isNull(customer)) {
			// 将手机号作为key,临时凭证code作为value存入redis缓存,以便在后续注册时校验,过期时长为2小时
			String key = RedisKeys.Cache.PC_REGISTER_PREFIX
					+ loginForm.getMobile();
			redisClient.setEx(UsernameSalt.encryptUsername(key),
					loginForm.getCaptcha(), properties.getTempVoucherExpire(),
					TimeUnit.SECONDS);
			throw new CustomerNotFoundException(ErrorCode.CODE_20001003,
					loginForm.getMobile());
		} else if (CustomerDef.State.INACTIVE.match(customer.getState())) {
			throw new CustomerDisableException(ErrorCode.CODE_30059008,
					loginForm.getMobile());
		} else if (CustomerDef.State.CANCELLED.match(customer.getState())) {
			throw new CustomerDisableException(ErrorCode.CODE_30059024,
					loginForm.getMobile());
		}

		if (Objects.nonNull(loginForm.getId())) {
			CustomerWx customerWx = customerWxService.findOne(loginForm.getId())
					.orElse(null);

			if (Objects.isNull(customerWx)) {
				throw new CustomerNotFoundException(ErrorCode.CODE_20001003,
						loginForm.getMobile());
			}

			if (Objects.equals(0, customerWx.getType())) {
				if (StringUtils.isNotBlank(customer.getUnionId())) {
					throw new CustomerDisableException(ErrorCode.CODE_30059011,
							loginForm.getMobile());
				}
				customer.setUnionId(customerWx.getUnionId());
			} else {
				if (StringUtils.isNotBlank(customer.getSubId())) {
					throw new CustomerDisableException(ErrorCode.CODE_30059011,
							loginForm.getMobile());
				}
				customer.setSubId(customerWx.getUnionId());
			}
			customerService.update(customer);
			customerWx.setCustomerId(customer.getId());
			customerWx.setBindTime(LocalDateTime.now());
			customerWxService.update(customerWx);
		}
		// 兼容没有传deviceCode的老版本
		SecuritySettingDevice securitySettingDevice = null;
		if (Objects.nonNull(loginForm.getDeviceCode())) {
			securitySettingDevice = securitySettingDeviceService
					.findByCustomerIdAndDeviceCode(customer.getId(),
							loginForm.getDeviceCode())
					.orElse(null);
		}
		// 校验是否绑定该设备
		if (Objects.nonNull(securitySettingDevice)) {
			securitySettingDevice.setLoginTime(LocalDateTime.now());
			securitySettingDeviceService
					.updateAllProperties(securitySettingDevice);
		} else {
			List<SecuritySettingDevice> securitySettingDeviceList = securitySettingDeviceService
					.findByCustomerIdAndLoginTime(customer.getId());
			if (securitySettingDeviceList.size() >= 10) {
				securitySettingDeviceService
						.delete(securitySettingDeviceList.get(0).getId());
			}
			securitySettingDevice = new SecuritySettingDevice();
			securitySettingDevice.setDeviceCode(loginForm.getDeviceCode());
			securitySettingDevice.setCustomerId(customer.getId());
			securitySettingDevice.setLoginTime(LocalDateTime.now());
			securitySettingDeviceService.create(securitySettingDevice);
		}

		// 组装代理账号信息
		CustomerLoginVo customerLoginVo = new CustomerLoginVo();
		customerLoginVo.setActualAccount(customer);
		customerLoginVo.setRole(loginForm.getAppType());
		customerLoginVo.setProxyAccount(customer);
		if (!WxService.AppType.chuanwu.name().equals(
				AppTypeDef.AppType.from(loginForm.getAppType()).getName())) {
			// 获取用户上一次登录的代理账号
			String lastLoginKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
					+ loginForm.getMobile();
			String encryptedKey = UsernameSalt.encryptMiniLogin(lastLoginKey);
			String proxyAccountId = redisClient.get(encryptedKey);
			if (StringUtils.isNotBlank(proxyAccountId)) {
				Customer clientById = customerService
						.findById(Long.valueOf(proxyAccountId)).orElse(null);
				if (Objects.nonNull(clientById)) {
					customerLoginVo.setProxyAccount(clientById);
				}
				redisClient.setEx(encryptedKey, proxyAccountId,
						properties.getLoginTokenExpire(), TimeUnit.SECONDS);
			}

		}

		return new ValidatedAuthentication(new LoginUser<>(customerLoginVo,
				String.valueOf(loginForm.getAppType()), customer.getMobile(), List.of()), null, loginForm);
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return ClientCaptchaAuthentication.class
				.isAssignableFrom(authentication);
	}

	/**
	 * 校验登录验证码
	 *
	 * @param loginForm
	 */
	private void checkLoginCaptchaCode(ClientCaptchaLoginForm loginForm) {
		String key = RedisKeys.Cache.PC_LOGIN_MSG_PREFIX
				+ loginForm.getMobile();
		String code = redisClient.get(key);
		if (StringUtils.isBlank(code) || !code.equals(loginForm.getCaptcha())) {
			throw new UsernameNotFoundException(ErrorCode.CODE_30059006);
		}
	}

}
