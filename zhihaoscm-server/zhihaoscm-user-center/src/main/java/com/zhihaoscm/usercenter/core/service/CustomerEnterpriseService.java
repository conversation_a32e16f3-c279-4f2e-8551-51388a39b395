package com.zhihaoscm.usercenter.core.service;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.CustomerEnterprise;
import com.zhihaoscm.domain.bean.vo.CustomerEnterpriseVo;

/**
 * <p>
 * 子账号中间表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public interface CustomerEnterpriseService
		extends MpLongIdBaseService<CustomerEnterprise> {

	/**
	 * 分页查询邀请记录
	 *
	 * @param page
	 *            查询页码
	 * @param size
	 *            步长
	 * @param state
	 *            查询状态
	 * @param mainAccountId
	 *            主账号id
	 * @param searchParam
	 *            查询参数
	 * @param sortKey
	 *            排序键
	 * @param sortDirection
	 *            排序方向
	 * @return 邀请记录分页列表
	 */
	Page<CustomerEnterpriseVo> paging(Integer page, Integer size, Integer state,
			Long mainAccountId, String searchParam, String sortKey,
			String sortDirection, Integer accountState);

	/**
	 * 分页查询受邀记录
	 *
	 * @param page
	 *            查询页码
	 * @param size
	 *            步长
	 * @param actualAccountId
	 *            实际账号id
	 * @param state
	 *            状态
	 * @param institutionName
	 *            机构名称
	 * @return 受邀记录分页列表
	 */
	Page<CustomerEnterpriseVo> invitedPaging(Integer page, Integer size,
			Long actualAccountId, Integer state, String institutionName);

	/**
	 * 查询子账号企业列表
	 *
	 * @param actualAccountId
	 * @return
	 */
	List<CustomerEnterpriseVo> invitedList(Long actualAccountId);

	/**
	 * 根据id查询vo对象
	 *
	 * @param id
	 *            主键id
	 * @param queryType
	 *            查询类型
	 * @return vo对象
	 */
	Optional<CustomerEnterpriseVo> findVoById(Long id, Integer queryType);

	/**
	 * 根据子账号id查询vo对象
	 *
	 * @param subAccountId
	 *            子账号id
	 * @param mainAccountId
	 *            主账号id
	 * @return vo对象
	 */
	Optional<CustomerEnterpriseVo> findSelfVoById(Long subAccountId,
			Long mainAccountId);

	/**
	 * 根据主账号id查询其关联的子账号列表
	 *
	 * @param mainAccountId
	 *            主账号id
	 * @param state
	 *            状态
	 * @return 子账号列表
	 */
	List<CustomerEnterprise> findByMainId(Long mainAccountId, Integer state);

	/**
	 * 根据主账号id查询其关联的子账号列表
	 *
	 * @param mainAccountId
	 *            主账号id
	 * @param state
	 *            状态
	 * @return 子账号列表
	 */
	List<CustomerEnterprise> findSubByMainId(Long mainAccountId, Integer state);

	/**
	 * 根据子账号id查询其关联的主账号列表
	 *
	 * @param subAccountId
	 *            子账号id
	 * @param state
	 *            查询状态
	 * @return 关联列表
	 */
	List<CustomerEnterprise> findBySubId(Long subAccountId, Integer state);

	/**
	 * 根据主账号id和子账号id查询关联记录
	 *
	 * @param subAccountId
	 *            子账号id
	 * @param mainAccountId
	 *            主账号id
	 * @param state
	 *            状态编码
	 * @return 主子账号关联关系
	 */
	Optional<CustomerEnterprise> findByMainAndSubId(Long subAccountId,
			Long mainAccountId, Integer state);

	/**
	 * 根据主账号id和邀请状态查询关联记录（无论账号状态如何）
	 *
	 * @param mainAccountId
	 *            主账号id
	 * @param state
	 *            邀请状态
	 * @return
	 */
	List<CustomerEnterprise> findByMainIdAndState(Long mainAccountId,
			Integer state);

	/**
	 * 确认或拒绝邀请
	 *
	 * @param id
	 *            邀请记录id
	 * @param state
	 *            状态
	 */
	void confirm(Long id, Integer state);

	/**
	 * 删除记录
	 *
	 * @param id
	 *            邀请记录id
	 * @param operationType
	 *            操作类型
	 */
	void delete(Long id, Integer operationType);

	/**
	 * 根据主账号id更新激活状态
	 *
	 * @param mainIds
	 * @param accountState
	 */
	void batchUpdateAccountState(List<Long> mainIds, Integer accountState);

	/**
	 * 刷新子账号的token
	 *
	 * @param
	 * @return
	 */
	List<Long> refreshSubAccountContext(Long mainAccountId,
			Boolean includeYourself);

}
