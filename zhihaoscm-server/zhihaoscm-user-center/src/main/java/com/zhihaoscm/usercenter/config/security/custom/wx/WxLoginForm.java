package com.zhihaoscm.usercenter.config.security.custom.wx;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WxLoginForm {

    /**
     * 临时登录凭证code
     */
    private String tempVoucherCode;

    /**
     * 程序类型
     */
    private Integer appType;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 换取openid的临时凭证
     */
    private String openidCode;

}
