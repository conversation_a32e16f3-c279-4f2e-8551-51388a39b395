package com.zhihaoscm.usercenter.config.security.custom.wx;

import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.Promotion;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.MembershipLevelDef;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WxCreateForm {

	/**
	 * 微信小程序临时凭证
	 */
	private String tempVoucherCode;

	/**
	 * 程序类型
	 */
	private Integer appType;

	/**
	 * 微信小程序昵称
	 */
	private String nickName;

	/**
	 * 微信小程序unionId
	 */
	private String unionId;

	/**
	 * 手机号
	 */
	private String mobile;

	/**
	 * 推广码
	 */
	private String promotionCode;

	/**
	 * 激活码
	 */
	private String activationCode;

	/**
	 * 来源
	 */
	private Integer origin;

	/**
	 * 微信绑定Id
	 */
	private Long id;

	/**
	 * 设备编号
	 */
	private String deviceCode;

	public Customer convert(Promotion promotion) {
		Customer customer = new Customer();
		customer.setIdentity(0);
		customer.setNickName(this.mobile);
		customer.setUnionId(this.unionId);
		customer.setMobile(this.mobile);
		customer.setCreatedType(CommonDef.UserType.OUTER.getCode());
		customer.setUpdatedType(CommonDef.UserType.OUTER.getCode());
		customer.setPromotionCode(this.promotionCode);
		if (Objects.nonNull(promotion)) {
			customer.setPromoterCode(promotion.getCustomerInfo().getCode());
			customer.setPromoterId(promotion.getCustomerId());
		}
		customer.setHasPromotion(CustomerDef.HasPromotion.NO.getCode());
		// 设置用户会员等级
		customer.setMemberLevel(MembershipLevelDef.Level.NON_MEMBER.getCode());
		customer.setApplyState(CommonDef.Symbol.NO.getCode());
		return customer;
	}

}
