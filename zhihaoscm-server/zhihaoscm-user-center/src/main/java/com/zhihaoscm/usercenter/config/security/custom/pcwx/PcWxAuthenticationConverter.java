package com.zhihaoscm.usercenter.config.security.custom.pcwx;

import java.util.Map;
import java.util.Objects;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationConverter;

import com.zhihaoscm.common.api.util.CommonWebUtils;
import com.zhihaoscm.common.security.authentication.CommonWebAuthenticationDetails;
import com.zhihaoscm.common.util.utils.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

public class PcWxAuthenticationConverter implements AuthenticationConverter {

	@Override
	public Authentication convert(HttpServletRequest request) {
		// 从request中获取登录信息
		Map<String, Object> code = CommonWebUtils
				.getParametersStartingWith(request, "code");
		Map<String, Object> url = CommonWebUtils
				.getParametersStartingWith(request, "url");
		Map<String, Object> appType = CommonWebUtils
				.getParametersStartingWith(request, "appType");

		PcWxLoginForm login = new PcWxLoginForm();
		login.setCode((String) code.get(""));
		login.setUrl((String) url.get(""));
		login.setAppType(Integer.parseInt((String) appType.get("")));

		if (StringUtils.isBlank(login.getCode())
				|| Objects.isNull(login.getAppType())
				|| StringUtils.isBlank(login.getUrl())) {
			throw new BadCredentialsException("missing code or appType or url");
		}

		PcWxAuthentication authentication = this.getWxAuthentication(login);
		authentication.setDetails(new CommonWebAuthenticationDetails(request));
		return authentication;
	}

	private PcWxAuthentication getWxAuthentication(PcWxLoginForm login) {
		if (Objects.isNull(login.getCode())) {
			throw new BadCredentialsException("missing code");
		}
		if (Objects.isNull(login.getAppType())) {
			throw new BadCredentialsException("missing appType");
		}
		if (Objects.isNull(login.getUrl())) {
			throw new BadCredentialsException("missing url");
		}
		return new PcWxAuthentication(login, null);
	}
}
