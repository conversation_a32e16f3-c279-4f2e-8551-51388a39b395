package com.zhihaoscm.usercenter.resource.custom.login;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.vo.QrLoginVo;
import com.zhihaoscm.usercenter.core.service.QrLoginService;
import com.zhihaoscm.usercenter.resource.form.qr.login.ConfirmLoginForm;
import com.zhihaoscm.usercenter.resource.validator.qr.login.QrLoginValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "app扫码登录管理", description = "app扫码登录管理API")
@RestController
@RequestMapping("/qr-login")
public class QrLoginController {

	@Autowired
	private QrLoginService service;

	@Autowired
	private QrLoginValidator validator;

	@Operation(summary = "二维码")
	@GetMapping("/generate")
	public ApiResponse<QrLoginVo> generate() {
		return new ApiResponse<>(service.generateLoginCode().orElse(null));
	}

	@Operation(summary = "扫描")
	@PostMapping("/scan")
	public ApiResponse<Void> scan(
			@Validated @RequestBody ConfirmLoginForm form) {
		validator.validateScan(form);
		service.scan(form.getLoginCode());
		return new ApiResponse<>();
	}

	@Operation(summary = "确认登录")
	@PostMapping("/confirm")
	public ApiResponse<Void> confirm(
			@Validated @RequestBody ConfirmLoginForm form,
			@RequestHeader("authorization") String token) {
		validator.validateConfirm(form);
		service.confirmLogin(form.getLoginCode(), token);
		return new ApiResponse<>();
	}

	@Operation(summary = "取消登录")
	@PostMapping("/cancel")
	public ApiResponse<Void> cancel(
			@Validated @RequestBody ConfirmLoginForm form) {
		validator.validateConfirm(form);
		service.cancelLogin(form.getLoginCode());
		return new ApiResponse<>();
	}
}
