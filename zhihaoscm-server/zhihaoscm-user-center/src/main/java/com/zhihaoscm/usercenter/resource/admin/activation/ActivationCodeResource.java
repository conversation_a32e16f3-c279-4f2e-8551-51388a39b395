package com.zhihaoscm.usercenter.resource.admin.activation;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.excel.EasyExcel;
import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.ActivationCode;
import com.zhihaoscm.domain.bean.vo.ActivationCodeVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.domain.meta.biz.MembershipLevelDef;
import com.zhihaoscm.usercenter.core.service.ActivationCodeService;
import com.zhihaoscm.usercenter.core.service.ActivationCodeServiceExt;
import com.zhihaoscm.usercenter.resource.form.activation.ActivationCodeForm;
import com.zhihaoscm.usercenter.resource.form.activation.ActivationCodeItem;
import com.zhihaoscm.usercenter.resource.validator.activation.ActivationCodeValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "激活码管理", description = "激活码管理API")
@RequestMapping("/activation/code")
@RestController
public class ActivationCodeResource {

	@Autowired
	private ActivationCodeService activationCodeService;

	@Autowired
	private ActivationCodeValidator validator;

	@Autowired
	private ActivationCodeServiceExt service;

	@Operation(summary = "查询激活码列表")
	@GetMapping(value = "/paging")
	@Secured({ AdminPermissionDef.CDKEY_R, AdminPermissionDef.CDKEY_W })
	public ApiResponse<Page<ActivationCode>> paging(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "兑换码") @RequestParam(value = "code", required = false) String code,
			@Parameter(description = "查询参数") @RequestParam(required = false, value = "searchParam") String searchParam,
			@Parameter(description = "会员等级") @RequestParam(required = false, value = "level") Integer level,
			@Parameter(description = "是否绑定账号") @RequestParam(required = false, value = "hasBind") Integer hasBind,
			@Parameter(description = "状态") @RequestParam(required = false, value = "state") Integer state) {
		return new ApiResponse<>(PageUtil.convert(activationCodeService
				.paging(page, size, code, searchParam, level, hasBind, state)));
	}

	@Operation(summary = "新增激活码列表")
	@PostMapping
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = LogDef.USER_MANAGEMENT_ACTIVATION_CODE_ADD, type = LogDef.USER_MANAGEMENT_ACTIVATION_CODE, bizNo = "{{#codeId}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#code1}}"),
			@LogRecord.KeyValuePair(key = "#vip_level#", value = "{{#level1}}"),
			@LogRecord.KeyValuePair(key = "#vip_duration#", value = "{{#duration1}}") })
	@Secured({ AdminPermissionDef.CDKEY_W })
	public ApiResponse<List<ActivationCode>> create(
			@RequestBody @Validated ActivationCodeForm form) {
		validator.validateCreate(form);
		List<ActivationCode> activationCodes = activationCodeService
				.batchCreate(form.convertToEntities());
		logFill1(activationCodes.get(0));
		return new ApiResponse<>(activationCodes);
	}

	@Operation(summary = "删除激活码")
	@DeleteMapping(value = { "/batch/delete" })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.DELETE, success = LogDef.USER_MANAGEMENT_ACTIVATION_CODE_DELETE, type = LogDef.USER_MANAGEMENT_ACTIVATION_CODE, bizNo = "{{#codeId}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#code1}}"),
			@LogRecord.KeyValuePair(key = "#vip_level#", value = "{{#level1}}"),
			@LogRecord.KeyValuePair(key = "#vip_duration#", value = "{{#duration1}}") })
	@Secured({ AdminPermissionDef.CDKEY_W })
	public ApiResponse<Void> delete(@RequestBody List<Long> ids) {
		ActivationCode activationCode = activationCodeService
				.findOne(ids.get(0)).orElse(new ActivationCode());
		logFill1(activationCode);
		activationCodeService.batchDelete(ids);
		return new ApiResponse<>();
	}

	@Operation(summary = "导出激活码")
	@GetMapping(value = { "/export" })
	@Secured({ AdminPermissionDef.CDKEY_W })
	public ApiResponse<Void> export(@RequestParam(value = "ids") List<Long> ids,
			HttpServletResponse response) {
		service.setExportReponseFields(response);
		try {
			EasyExcel
					.write(response.getOutputStream(), ActivationCodeItem.class)
					.sheet("模板").doWrite(service.getExportData(ids));
		} catch (IOException e) {
			log.error("导出异常 -> {}", e.getMessage(), e);
			throw new BadRequestException("导出异常");
		}
		return new ApiResponse<>();
	}

	@Operation(summary = "兑换")
	@GetMapping(value = "/exchange")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.DELETE, success = LogDef.USER_MANAGEMENT_RECHARGE_LIST_REDEEM_MEMBERSHIP, type = LogDef.USER_MANAGEMENT_RECHARGE_LIST, bizNo = "{{#_ret.getData().getActivationCode().getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#code}}"),
			@LogRecord.KeyValuePair(key = "#vip_level#", value = "{{#level}}"),
			@LogRecord.KeyValuePair(key = "#vip_duration#", value = "{{#duration}}") })
	public ApiResponse<ActivationCodeVo> exchange(
			@RequestParam(value = "code") String code,
			@RequestParam(value = "customerId") Long customerId) {
		ActivationCodeVo vo = validator.validateExchange(code, customerId);
		if (Objects.nonNull(vo.getErrorCode())) {
			return new ApiResponse<>(vo);
		}
		ActivationCodeVo activationCodeVo = activationCodeService
				.exchange(vo.getActivationCode(), customerId).orElse(null);
		logFill(activationCodeVo);
		return new ApiResponse<>(activationCodeVo);
	}

	@Operation(summary = "根据激活码查询激活码详情")
	@GetMapping(value = "/find/{code}")
	public ApiResponse<ActivationCode> findByCode(
			@PathVariable(value = "code") String code) {
		return new ApiResponse<>(
				activationCodeService.findByCode(code).orElse(null));
	}

	/**
	 * log填充
	 * 
	 * @param activationCodeVo
	 */
	private static void logFill(ActivationCodeVo activationCodeVo) {
		if (Objects.nonNull(activationCodeVo)
				&& Objects.nonNull(activationCodeVo.getActivationCode())) {
			LogRecordContext.putVariable("code",
					activationCodeVo.getActivationCode().getCode());
			LogRecordContext.putVariable("level",
					MembershipLevelDef.Level.from(
							activationCodeVo.getActivationCode().getLevel())
							.getDesc());
			LogRecordContext.putVariable("duration",
					activationCodeVo.getActivationCode().getDuration() + "天");
		}
	}

	private static void logFill1(ActivationCode activationCode) {
		if (Objects.nonNull(activationCode)) {
			LogRecordContext.putVariable("code1", activationCode.getCode());
			LogRecordContext.putVariable("level1", MembershipLevelDef.Level
					.from(activationCode.getLevel()).getDesc());
			LogRecordContext.putVariable("duration1",
					activationCode.getDuration() + "天");
			LogRecordContext.putVariable("codeId", activationCode.getId());
		}
	}

}
