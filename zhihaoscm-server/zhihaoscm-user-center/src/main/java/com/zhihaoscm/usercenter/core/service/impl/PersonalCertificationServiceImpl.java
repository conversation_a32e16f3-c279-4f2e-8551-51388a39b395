package com.zhihaoscm.usercenter.core.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.dto.CustomerStateRefreshDto;
import com.zhihaoscm.domain.bean.dto.PersonalAuthDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.CustomerJsonInfo;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.vo.CustomerPersonalAuthVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.CertificationDef;
import com.zhihaoscm.domain.meta.biz.CustomerChangeMobileDef;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.TopicDef;
import com.zhihaoscm.qiyuesuo.sdk.api.facilitator.FacilitatorClient;
import com.zhihaoscm.qiyuesuo.sdk.request.MiniAppExchangeRequest;
import com.zhihaoscm.qiyuesuo.sdk.request.User;
import com.zhihaoscm.qiyuesuo.sdk.request.UserAuthPage;
import com.zhihaoscm.qiyuesuo.sdk.response.MiniAppExchangeResponse;
import com.zhihaoscm.qiyuesuo.sdk.response.UserAuthPageResponse;
import com.zhihaoscm.qiyuesuo.sdk.response.UserAuthResponse;
import com.zhihaoscm.usercenter.config.properties.SMSProperties;
import com.zhihaoscm.usercenter.core.mapper.PersonalCertificationMapper;
import com.zhihaoscm.usercenter.core.service.*;
import com.zhihaoscm.usercenter.utils.PinyinUtils;
import com.zhihaoscm.usercenter.utils.TokenUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 个人认证记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Slf4j
@Service
public class PersonalCertificationServiceImpl extends
		MpLongIdBaseServiceImpl<CustomerPersonalCertification, PersonalCertificationMapper>
		implements PersonalCertificationService {

	public PersonalCertificationServiceImpl(
			PersonalCertificationMapper repository) {
		super(repository);
	}

	@Autowired
	private ContractLockRecordService contractLockRecordService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private FacilitatorClient facilitatorClient;

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private PromotionDetailService promotionDetailService;

	@Autowired
	private CustomerEnterpriseService customerEnterpriseService;

	@Autowired
	private CustomerChangeMobileService customerChangeMobileService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private MqUtil mqUtil;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Override
	public Optional<CustomerPersonalCertification> findByCustomerId(
			Long customerId) {
		return Optional.ofNullable(repository.selectOne(
				Wrappers.lambdaQuery(CustomerPersonalCertification.class)
						.eq(CustomerPersonalCertification::getCustomerId,
								customerId)
						.eq(CustomerPersonalCertification::getDel,
								CommonDef.Symbol.NO.getCode())));
	}

	@Override
	public Optional<CustomerPersonalCertification> findByCustomerIdLatest(
			Long customerId, Integer state) {
		LambdaQueryWrapper<CustomerPersonalCertification> wrapper = Wrappers
				.lambdaQuery(CustomerPersonalCertification.class);
		this.filterDeleted(wrapper);
		wrapper.eq(CustomerPersonalCertification::getCustomerId, customerId);
		wrapper.orderByDesc(CustomerPersonalCertification::getCreatedTime);
		wrapper.eq(Objects.nonNull(state),
				CustomerPersonalCertification::getState, state);
		wrapper.last("limit 1");
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Integer personalCallback(String mode, String status, String authId) {
		// 首先根据authId调用契约锁接口查询是否存在记录
		UserAuthResponse userAuthResponse = facilitatorClient
				.personalAuthResult(authId, null, null);
		if (!CommonDef.Symbol.NO.getCode().equals(userAuthResponse.getCode())) {
			return CertificationDef.CONTRACT_ERROR_CODE;
		}
		// 将查询到的内容进行落库
		Optional<ContractLockRecord> recordOptional = contractLockRecordService
				.findByAuthIdAndState(authId,
						CertificationDef.CertificationState.VALID.getCode());
		if (recordOptional.isEmpty()) {
			return CertificationDef.CONTRACT_ERROR_CODE;
		}
		ContractLockRecord lockRecord = recordOptional.get();
		Optional<CustomerPersonalCertification> certificationOptional = this
				.findByCustomerIdLatest(lockRecord.getCustomerId(),
						CertificationDef.CertificationState.VALID.getCode());
		if (certificationOptional.isEmpty()) {
			return CertificationDef.CONTRACT_ERROR_CODE;
		}
		CustomerPersonalCertification certification = certificationOptional
				.get();
		this.storageAuthData(userAuthResponse, certification, lockRecord);
		return CertificationDef.CONTRACT_SUCCESS_CODE;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CustomerPersonalAuthVo personalAuthResult(Long customerId) {
		// 校验前置操作是否已进行
		Optional<CustomerPersonalCertification> certificationOptional = this
				.findByCustomerIdLatest(customerId,
						CertificationDef.CertificationState.VALID.getCode());
		if (certificationOptional.isEmpty()) {
			return null;
		}
		CustomerPersonalCertification certification = certificationOptional
				.get();
		// 获取个人认证手机号
		Optional<Customer> customerOptional = customerService
				.findById(customerId);
		if (customerOptional.isEmpty()) {
			throw new BadRequestException("用户不存在");
		}
		Customer customer = customerOptional.get();
		ContractLockRecord lockRecord = contractLockRecordService
				.findByCustomerIdAndState(customerId,
						CertificationDef.InstitutionType.PERSONAL.getCode(),
						CertificationDef.CertificationState.VALID.getCode())
				.orElse(null);
		if (Objects.isNull(lockRecord)) {
			throw new BadRequestException("个人认证记录不存在");
		}
		// 调用契约锁接口获取个人认证结果
		log.info("第三方个人认证参数 -> {}",
				JsonUtils.objectToJson(customer.getMobile()));
		UserAuthResponse userAuthResponse = facilitatorClient
				.personalAuthResult(lockRecord.getAuthId(), "MOBILE",
						customer.getMobile());
		log.info("第三方个人认证结果 -> {}", JsonUtils.objectToJson(userAuthResponse));
		this.storageAuthData(userAuthResponse, certification, lockRecord);
		return new CustomerPersonalAuthVo(certification,
				userAuthResponse.getMessage(), null);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CustomerPersonalAuthVo personalAuth(Long customerId, String mobile,
			String name, String idNo, String callbackeUrl, Integer origin,
			String callbackPage) {
		CustomerPersonalCertification certification;
		ContractLockRecord record;
		// 校验当前是否存在已成功的个人认证记录
		Optional<CustomerPersonalCertification> certificationOptional = this
				.findByCustomerIdLatest(customerId,
						CertificationDef.CertificationState.VALID.getCode());
		if (certificationOptional.isPresent()) {
			certification = certificationOptional.get();
			if (certification.getStatus()
					.equals(CommonDef.Symbol.YES.getCode())) {
				return new CustomerPersonalAuthVo("客户个人实名已认证成功,无需再次认证");
			}
			certification.setName(name);
			certification.setIdNo(idNo);
			record = contractLockRecordService
					.findByCustomerIdAndState(customerId,
							CertificationDef.InstitutionType.PERSONAL.getCode(),
							CertificationDef.CertificationState.VALID.getCode())
					.map(rec -> {
						rec.setParams(
								JsonUtils.objectToJson(new PersonalAuthDto(
										customerId, mobile, name, idNo)));
						return rec;
					}).orElse(null);
		} else {
			// 个人认证信息落库
			CustomerPersonalCertification personalCertification = new CustomerPersonalCertification();
			personalCertification.setCustomerId(customerId);
			personalCertification.setName(name);
			personalCertification.setIdNo(idNo);
			personalCertification.setStatus(CommonDef.Symbol.NO.getCode());
			personalCertification.setState(
					CertificationDef.CertificationState.VALID.getCode());
			certification = this.create(personalCertification);
			// 获取个人认证信息链接调用记录落库
			record = contractLockRecordService
					.findByCustomerIdAndState(customerId,
							CertificationDef.InstitutionType.PERSONAL.getCode(),
							CertificationDef.CertificationState.VALID.getCode())
					.orElse(this.assembleAndCreateLockRecord(customerId, mobile,
							name, idNo));
		}
		User user = new User();
		user.setContact(mobile);
		user.setContactType(CertificationDef.ContactType.MOBILE.name());
		// 调用契约锁的接口获取个人认证链接
		if (CertificationDef.Origin.MINI_APP.match(origin)) {
			MiniAppExchangeRequest request = new MiniAppExchangeRequest();
			request.setUser(user);
			request.setMode("IVS");
			request.setUsername(name);
			request.setIdCardNo(idNo);
			request.setCallbackUrl(callbackeUrl);
			log.info("personal auth mini param: => {}",
					JsonUtils.objectToJson(request));
			MiniAppExchangeResponse authResponse = facilitatorClient
					.miniAppExchangePersonal(request);
			log.info("personal auth mini response: {}",
					JsonUtils.objectToJson(authResponse));
			if (authResponse.getCode().equals(CommonDef.Symbol.NO.getCode())) {
				this.update(certification);
				Objects.requireNonNull(record)
						.setAuthId(authResponse.getResult().getTicket());
				contractLockRecordService.update(record);
				return new CustomerPersonalAuthVo(authResponse.getMessage(),
						authResponse.getResult().getTicket());
			} else {
				certification.setMessage(authResponse.getMessage());
				this.update(certification);
				return new CustomerPersonalAuthVo(authResponse.getMessage());
			}
		} else {
			UserAuthPage userAuthPage = new UserAuthPage();
			userAuthPage.setMode("IVS");
			userAuthPage.setUser(user);
			userAuthPage.setUsername(name);
			userAuthPage.setIdCardNo(idNo);
			userAuthPage.setCallbackUrl(callbackeUrl);
			userAuthPage.setCallbackPage(callbackPage);
			UserAuthPageResponse userAuthPageResponse = facilitatorClient
					.personalAuthPcPage(userAuthPage);
			if (userAuthPageResponse.getCode()
					.equals(CommonDef.Symbol.NO.getCode())) {
				this.update(certification);
				Objects.requireNonNull(record).setAuthId(
						userAuthPageResponse.getResult().getAuthId());
				contractLockRecordService.update(record);
				return new CustomerPersonalAuthVo(
						userAuthPageResponse.getMessage(),
						userAuthPageResponse.getResult().getAuthUrl());
			} else {
				certification.setMessage(userAuthPageResponse.getMessage());
				this.update(certification);
				return new CustomerPersonalAuthVo(
						userAuthPageResponse.getMessage());
			}
		}
	}

	@Override
	public List<CustomerPersonalCertification> findByCustomerIds(
			List<Long> ids) {
		return repository.selectList(
				Wrappers.lambdaQuery(CustomerPersonalCertification.class)
						.in(CustomerPersonalCertification::getCustomerId, ids)
						.eq(CustomerPersonalCertification::getDel,
								CommonDef.Symbol.NO.getCode()));
	}

	/**
	 * 数据脱敏
	 *
	 * @param certification
	 */
	private void dataDesensitization(
			CustomerPersonalCertification certification) {
		if (StringUtils.isNotBlank(certification.getName())) {
			certification.setName(
					CustomerDef.FILTER_LEGAL_REPRESENTATIVE_SENSITIVE_INFO
							.apply(certification.getName()));
		}
		if (StringUtils.isNotBlank(certification.getIdNo())) {
			certification.setIdNo(
					CustomerDef.FILTER_SOCIAL_CREDIT_CODE_SENSITIVE_INFO
							.apply(certification.getIdNo()));
		}
	}

	/**
	 * 将契约锁返回的内容持久化到库里
	 *
	 * @param userAuthResponse
	 *            契约锁返回
	 * @param certification
	 * @param lockRecord
	 */
	private void storageAuthData(UserAuthResponse userAuthResponse,
			CustomerPersonalCertification certification,
			ContractLockRecord lockRecord) {
		if (userAuthResponse.getCode().equals(CommonDef.Symbol.NO.getCode())) {
			if (userAuthResponse.getResult().getRealName()) {
				certification.setStatus(
						CertificationDef.CertificationStatus.PASS.getCode());
				this.update(certification);

				Customer customer = new Customer();
				customer.setId(certification.getCustomerId());
				customer.setRealName(certification.getName());
				customer.setRealNamePinyin(
						PinyinUtils.getPinyin(customer.getRealName()));
				customer.setIdNo(certification.getIdNo());
				Customer update = customerService.update(customer);

				// 保存用户认证信息到推广详情表
				List<PromotionDetail> details = promotionDetailService
						.findByPromotionCode(update.getPromotionCode());
				details.forEach(promotionDetail -> {
					CustomerJsonInfo customerInfo = promotionDetail
							.getCustomerInfo();
					customerInfo.setRealName(update.getRealName());
					promotionDetailService.update(promotionDetail);
				});

				// 刷新上下文
				TokenUtils.refreshCustomerContext(update, redisClient,
						30 * 24 * 60 * 60L, Boolean.TRUE);

				// 数据脱敏
				this.dataDesensitization(certification);
			} else {
				certification.setStatus(
						CertificationDef.CertificationStatus.INVALID.getCode());
				this.update(certification);
			}
		}
		lockRecord.setResult(JsonUtils.objectToJson(userAuthResponse));
		contractLockRecordService.update(lockRecord);
	}

	/**
	 * 将契约锁返回的内容持久化到库里--换绑手机号
	 *
	 * @param userAuthResponse
	 *            契约锁返回
	 * @param certification
	 * @param lockRecord
	 */
	private void cmStorageAuthData(UserAuthResponse userAuthResponse,
			CustomerPersonalCertification certification,
			ContractLockRecord lockRecord) {
		if (userAuthResponse.getCode().equals(CommonDef.Symbol.NO.getCode())) {
			if (userAuthResponse.getResult().getRealName()) {
				certification.setStatus(
						CertificationDef.CertificationStatus.PASS.getCode());
				this.update(certification);
			} else {
				certification.setStatus(
						CertificationDef.CertificationStatus.INVALID.getCode());
				this.update(certification);
			}
		}
		lockRecord.setResult(JsonUtils.objectToJson(userAuthResponse));
		contractLockRecordService.update(lockRecord);
	}

	/**
	 * 组装契约锁调用记录
	 *
	 * @param customerId
	 * @param mobile
	 * @param name
	 * @param idNo
	 * @return
	 */
	private ContractLockRecord assembleAndCreateLockRecord(Long customerId,
			String mobile, String name, String idNo) {
		ContractLockRecord lockRecord = new ContractLockRecord();
		lockRecord.setType(CertificationDef.InstitutionType.PERSONAL.getCode());
		lockRecord.setCustomerId(customerId);
		lockRecord
				.setState(CertificationDef.CertificationState.VALID.getCode());
		lockRecord.setParams(JsonUtils.objectToJson(
				new PersonalAuthDto(customerId, mobile, name, idNo)));
		return contractLockRecordService.create(lockRecord);
	}

	/**
	 * 换绑手机号-个人认证
	 *
	 * @param customerId
	 * @param mobile
	 * @param name
	 * @param idNo
	 * @param callbackeUrl
	 * @param origin
	 * @param callbackPage
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CustomerPersonalAuthVo cmPersonalAuth(Long customerId, String mobile,
			String name, String idNo, String callbackeUrl, Integer origin,
			String callbackPage) {
		// 个人认证记录表
		CustomerPersonalCertification certification;
		// 契约锁调用记录
		ContractLockRecord record;
		// 校验当前是否存在已成功的个人认证记录
		Optional<CustomerPersonalCertification> certificationOptional = this
				.findByCustomerIdLatest(customerId,
						CertificationDef.CertificationState.CHANGED.getCode());
		// 校验当前是否存在换绑手机号记录表
		CustomerChangeMobile customerChangeMobile = customerChangeMobileService
				.findByCustomerId(customerId).orElse(null);
		// 存在换绑手机号记录表
		if (Objects.nonNull(customerChangeMobile)) {
			if (certificationOptional.isPresent()) {
				certification = certificationOptional.get();
				if (certification.getStatus()
						.equals(CommonDef.Symbol.YES.getCode())) {
					return new CustomerPersonalAuthVo("客户个人实名已认证成功,无需再次认证");
				}
				certification.setName(name);
				certification.setIdNo(idNo);
				record = contractLockRecordService.findByCustomerIdAndState(
						customerId,
						CertificationDef.InstitutionType.PERSONAL.getCode(),
						CertificationDef.CertificationState.CHANGED.getCode())
						.map(rec -> {
							rec.setParams(
									JsonUtils.objectToJson(new PersonalAuthDto(
											customerId, mobile, name, idNo)));
							return rec;
						}).orElse(null);
			} else {
				// 个人认证信息落库
				CustomerPersonalCertification personalCertification = new CustomerPersonalCertification();
				personalCertification.setCustomerId(customerId);
				personalCertification.setName(name);
				personalCertification.setIdNo(idNo);
				personalCertification.setStatus(CommonDef.Symbol.NO.getCode());
				personalCertification.setState(
						CertificationDef.CertificationState.CHANGED.getCode());
				certification = this.create(personalCertification);
				// 获取个人认证信息链接调用记录落库
				record = contractLockRecordService.findByCustomerIdAndState(
						customerId,
						CertificationDef.InstitutionType.PERSONAL.getCode(),
						CertificationDef.CertificationState.CHANGED.getCode())
						.orElse(this.cmAssembleAndCreateLockRecord(customerId,
								mobile, name, idNo));
			}
			User user = new User();
			user.setContact(mobile);
			user.setContactType(CertificationDef.ContactType.MOBILE.name());
			// 调用契约锁的接口获取个人认证链接
			if (CertificationDef.Origin.MINI_APP.match(origin)) {
				// 小程序端 调用契约锁的接口获取个人认证链接
				MiniAppExchangeRequest request = new MiniAppExchangeRequest();
				request.setUser(user);
				request.setMode("IVS");
				request.setUsername(name);
				request.setIdCardNo(idNo);
				request.setCallbackUrl(callbackeUrl);
				log.info("personal auth mini param: => {}",
						JsonUtils.objectToJson(request));
				MiniAppExchangeResponse authResponse = facilitatorClient
						.miniAppExchangePersonal(request);
				log.info("personal auth mini response: {}",
						JsonUtils.objectToJson(authResponse));
				if (authResponse.getCode()
						.equals(CommonDef.Symbol.NO.getCode())) {
					// 获取个人认证链接成功 更新个人认证信息
					this.update(certification);
					// 契约锁认证表设置 契约锁认证请求id
					Objects.requireNonNull(record)
							.setAuthId(authResponse.getResult().getTicket());
					contractLockRecordService.update(record);
					// 返回个人认证链接
					return new CustomerPersonalAuthVo(authResponse.getMessage(),
							authResponse.getResult().getTicket());
				} else {
					// 获取个人认证链接成功 更新个人认证信息-设置失败信息
					certification.setMessage(authResponse.getMessage());
					this.update(certification);
					// 返回获取个人认证链接的错误信息
					return new CustomerPersonalAuthVo(
							authResponse.getMessage());
				}
			} else {
				// PC端 小程序船务端 调用契约锁的接口获取个人认证链接
				UserAuthPage userAuthPage = new UserAuthPage();
				userAuthPage.setMode("IVS");
				userAuthPage.setUser(user);
				userAuthPage.setUsername(name);
				userAuthPage.setIdCardNo(idNo);
				userAuthPage.setCallbackUrl(callbackeUrl);
				userAuthPage.setCallbackPage(callbackPage);
				// 调用此接口来获取个人认证页面链接
				UserAuthPageResponse userAuthPageResponse = facilitatorClient
						.personalAuthPcPage(userAuthPage);
				if (userAuthPageResponse.getCode()
						.equals(CommonDef.Symbol.NO.getCode())) {
					// 获取人认证页面链接成功
					this.update(certification);
					Objects.requireNonNull(record).setAuthId(
							userAuthPageResponse.getResult().getAuthId());
					contractLockRecordService.update(record);
					return new CustomerPersonalAuthVo(
							userAuthPageResponse.getMessage(),
							userAuthPageResponse.getResult().getAuthUrl());
				} else {
					// 获取人认证页面链接失败 返回错误信息
					certification.setMessage(userAuthPageResponse.getMessage());
					this.update(certification);
					return new CustomerPersonalAuthVo(
							userAuthPageResponse.getMessage());
				}
			}
		} else {
			return new CustomerPersonalAuthVo("当前用户不存在换绑手机号流程");
		}
	}

	/**
	 * 换绑手机号-查询个人认证结果
	 *
	 * @param customerId
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CustomerPersonalAuthVo cmPersonalAuthResult(Long customerId) {
		// 校验前置操作是否已进行 取最新一条个人认证记录
		Optional<CustomerPersonalCertification> certificationOptional = this
				.findByCustomerIdLatest(customerId,
						CertificationDef.CertificationState.CHANGED.getCode());
		if (certificationOptional.isEmpty()) {
			return null;
		}
		//
		CustomerPersonalCertification certification = certificationOptional
				.get();
		// 获取个人认证手机号
		Optional<Customer> customerOptional = customerService
				.findById(customerId);
		if (customerOptional.isEmpty()) {
			throw new BadRequestException("用户不存在");
		}
		Customer customer = customerOptional.get();
		// 获取数据库的契约锁个人认证记录
		ContractLockRecord lockRecord = contractLockRecordService
				.findByCustomerIdAndState(customerId,
						CertificationDef.InstitutionType.PERSONAL.getCode(),
						CertificationDef.CertificationState.CHANGED.getCode())
				.orElse(null);
		if (Objects.isNull(lockRecord)) {
			throw new BadRequestException("契约锁个人认证记录不存在");
		}
		log.info("第三方个人认证参数 -> {}",
				JsonUtils.objectToJson(customer.getMobile()));
		// 调用契约锁接口获取个人认证结果
		UserAuthResponse userAuthResponse = facilitatorClient
				.personalAuthResult(lockRecord.getAuthId(), "MOBILE",
						customer.getMobile());
		log.info("第三方个人认证结果 -> {}", JsonUtils.objectToJson(userAuthResponse));

		// 将契约锁的信息入库
		this.cmStorageAuthData(userAuthResponse, certification, lockRecord);
		// 返回个人认证信息和错误信息
		return new CustomerPersonalAuthVo(certification,
				userAuthResponse.getMessage(), null);
	}

	/**
	 * 换绑手机号-个人认证回调
	 *
	 * @param mode
	 *            认证模式
	 * @param status
	 *            认证结果
	 * @param authId
	 *            认证请求id
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Integer cmPersonalCallback(String mode, String status,
			String authId) {
		// 首先根据authId调用契约锁接口查询是否存在记录
		UserAuthResponse userAuthResponse = facilitatorClient
				.personalAuthResult(authId, null, null);
		// 契约锁不存在个人实名认证成功的记录
		if (!CommonDef.Symbol.NO.getCode().equals(userAuthResponse.getCode())) {
			return CertificationDef.CONTRACT_ERROR_CODE;
		}
		// 将查询到的内容更新到换绑手机号相关记录去
		Optional<ContractLockRecord> recordOptional = contractLockRecordService
				.findByAuthIdAndState(authId,
						CertificationDef.CertificationState.CHANGED.getCode());
		if (recordOptional.isEmpty()) {
			return CertificationDef.CONTRACT_ERROR_CODE;
		}
		ContractLockRecord lockRecord = recordOptional.get();
		// 查询换绑手机号-个人认证记录
		Optional<CustomerPersonalCertification> certificationOptional = this
				.findByCustomerIdLatest(lockRecord.getCustomerId(),
						CertificationDef.CertificationState.CHANGED.getCode());
		if (certificationOptional.isEmpty()) {
			return CertificationDef.CONTRACT_ERROR_CODE;
		}
		CustomerPersonalCertification certification = certificationOptional
				.get();
		// 将契约锁查询结果存入数据库
		this.cmStorageAuthData(userAuthResponse, certification, lockRecord);
		// 查询出换绑手机号记录
		CustomerChangeMobile customerChangeMobile = customerChangeMobileService
				.findByCustomerId(lockRecord.getCustomerId()).orElse(null);
		// 个人认证成功
		if (Objects.nonNull(customerChangeMobile)
				&& CertificationDef.CertificationStatus.PASS
						.match(certification.getStatus())) {
			// 判断是否完成所有流程 完成所有流程 则更新 个人认证信息记录表 契约锁调用记录-个人认证的信息，customer表的信息
			if (CustomerChangeMobileDef.CustomerState.PERSONAUTH
					.match(customerChangeMobile.getNeedState())) {
				// 更新换绑手机号记录的当前认证状态
				customerChangeMobile.setCurrentState(
						CustomerChangeMobileDef.CustomerState.PERSONAUTH
								.getCode());
				// 更新用户表的手机号
				Customer customer = customerService
						.findOne(lockRecord.getCustomerId()).orElse(null);
				Customer beforeCustomer = new Customer();
				if (Objects.nonNull(customer)) {
					BeanUtils.copyProperties(customer, beforeCustomer);
					customer.setMobile(customerChangeMobile.getMobile());
					customer.setNickName(customerChangeMobile.getMobile());
					customer.setId(certification.getCustomerId());
					customer.setRealName(certification.getName());
					customer.setRealNamePinyin(
							PinyinUtils.getPinyin(customer.getRealName()));
					customer.setIdNo(certification.getIdNo());

					Customer update = customerService.update(customer);

					// 保存用户认证信息到推广详情表
					List<PromotionDetail> details = promotionDetailService
							.findByPromotionCode(update.getPromotionCode());
					details.forEach(promotionDetail -> {
						CustomerJsonInfo customerInfo = promotionDetail
								.getCustomerInfo();
						customerInfo.setRealName(update.getRealName());
						promotionDetailService.update(promotionDetail);
					});
					// 换绑流程完成，换绑手机号中间表记录设置失效
					customerChangeMobile.setDel(CommonDef.Symbol.YES.getCode());
				}
				// 更新契约锁记录-个人认证类型，个人认证表
				this.updateCertificationAndLockRecords(customerChangeMobile,
						certification, lockRecord);
				// 删除token
				TokenUtils.deleteCustomer(customerChangeMobile.getCustomerId(),
						redisClient);
				// 获取用户上一次登录的代理账号
				String lastLoginKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
						+ beforeCustomer.getMobile();
				String encryptedKey = UsernameSalt
						.encryptMiniLogin(lastLoginKey);
				redisClient.delete(encryptedKey);
				// 换绑成功 给换绑后的手机号发短信
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String
								.valueOf(customerChangeMobile.getCustomerId())))
						.templateCode(wxSubscriptionProperties
								.getChangeCustomerMobileCode())
						.params(Map.of("phone",
								customerChangeMobile.getMobile()))
						.mobile(customerChangeMobile.getMobile()).build());
				// 通知客户端刷新客户信息
				mqUtil.asyncSend(MqMessage.builder()
						.topic(TopicDef.CUSTOM_USER_CHANGE)
						.message(MessageBuilder.withPayload(JsonUtils
								.objectToJson(new CustomerStateRefreshDto(
										TopicDef.Type.REFRESH_STATE.getCode(),
										TokenUtils.getNoticeIds(
												customerChangeMobile
														.getCustomerId(),
												customerEnterpriseService,
												Boolean.TRUE))))
								.build())
						.build());
			} else {
				// 没有完成换成手机号全流程的 都是完成了组织机构认证的情况
				customerChangeMobile.setCurrentState(
						CustomerChangeMobileDef.CustomerState.SYSENTERPRISEAUTH
								.getCode());
			}
			customerChangeMobileService
					.updateAllProperties(customerChangeMobile);
		}
		return CertificationDef.CONTRACT_SUCCESS_CODE;
	}

	/**
	 * 换绑手机号-组装契约锁调用记录
	 *
	 * @param customerId
	 * @param mobile
	 * @param name
	 * @param idNo
	 * @return
	 */
	private ContractLockRecord cmAssembleAndCreateLockRecord(Long customerId,
			String mobile, String name, String idNo) {
		ContractLockRecord lockRecord = new ContractLockRecord();
		lockRecord.setType(CertificationDef.InstitutionType.PERSONAL.getCode());
		lockRecord.setCustomerId(customerId);
		lockRecord.setState(
				CertificationDef.CertificationState.CHANGED.getCode());
		lockRecord.setParams(JsonUtils.objectToJson(
				new PersonalAuthDto(customerId, mobile, name, idNo)));
		return contractLockRecordService.create(lockRecord);
	}

	private void updateCertificationAndLockRecords(
			CustomerChangeMobile customerChangeMobile,
			CustomerPersonalCertification certification,
			ContractLockRecord lockRecord) {
		// 根据换绑手机号记录的个人认证id，找出原来有效的个人认证信息 将其状态设置为无效
		CustomerPersonalCertification customerPersonalCertification = this
				.findOne(customerChangeMobile.getPersonalCertificationId())
				.orElse(null);
		if (Objects.nonNull(customerPersonalCertification)) {
			customerPersonalCertification.setState(
					CertificationDef.CertificationState.INVALID.getCode());
			super.updateAllProperties(customerPersonalCertification);
		}
		// 当前换绑手机号的认证记录更新为有效的
		certification
				.setState(CertificationDef.CertificationState.VALID.getCode());
		super.updateAllProperties(certification);
		// 根据换绑手机号记录的契约锁调用记录id，找出原来有效的契约锁调用记录信息 将其状态设置为无效
		ContractLockRecord contractLockRecord = contractLockRecordService
				.findOne(customerChangeMobile.getContractLockPersonalId())
				.orElse(null);
		if (Objects.nonNull(contractLockRecord)) {
			contractLockRecord.setState(
					CertificationDef.CertificationState.INVALID.getCode());
			contractLockRecordService.updateAllProperties(contractLockRecord);
		}
		// 更新契约锁调用记录-个人认证的信息 设置为有效
		lockRecord
				.setState(CertificationDef.CertificationState.VALID.getCode());
		contractLockRecordService.updateAllProperties(lockRecord);
		customerChangeMobileService.updateAllProperties(customerChangeMobile);
	}
}
