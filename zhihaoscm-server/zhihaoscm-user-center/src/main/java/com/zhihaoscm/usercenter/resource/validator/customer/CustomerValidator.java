package com.zhihaoscm.usercenter.resource.validator.customer;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.core.service.CustomerService;

@Component
public class CustomerValidator {

	@Autowired
	private CustomerService customerService;

	private void validateState(Integer state) {
		if (Objects.isNull(state)) {
			throw new BadRequestException(ErrorCode.CODE_30049001);
		}
		CustomerDef.State.from(state);
	}

	public Customer validateId(Long id) {
		if (Objects.isNull(id)) {
			throw new BadRequestException(ErrorCode.CODE_30049002);
		}
		Customer customer = customerService.findById(id).orElse(null);
		if (Objects.isNull(customer)) {
			throw new BadRequestException(ErrorCode.CODE_30049003);
		}
		return customer;
	}

	public Customer validate(Long id, Integer state) {
		Customer customer = this.validateId(id);
		this.validateState(state);
		return customer;
	}

	/**
	 * 校验取消认证
	 *
	 * @return
	 */
	public Customer validateCancelApply(Long id) {
		Customer customer = this.validateId(id);
		// 判断本系统是否进行了认证
		if (CommonDef.Symbol.NO.match(customer.getApplyState())) {
			throw new BadRequestException(ErrorCode.CODE_30037042);
		}
		return customer;
	}

	/**
	 * 校验恢复
	 *
	 * @param id
	 * @return
	 */
	public Customer validateRestore(Long id) {
		Customer customer1 = this.validateId(id);
		if (!CustomerDef.State.CANCELLED.match(customer1.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30037044);
		}
		return customer1;
	}
}
