package com.zhihaoscm.usercenter.core.service.impl;

import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.domain.bean.dto.QrLoginDto;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.bean.vo.QrLoginVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.CertificationDef;
import com.zhihaoscm.domain.meta.biz.TopicDef;
import com.zhihaoscm.usercenter.config.properties.ApplicationProperties;
import com.zhihaoscm.usercenter.config.properties.SMSProperties;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.config.security.custom.RedisJwtTokenService;
import com.zhihaoscm.usercenter.core.service.QrLoginService;
import com.zhihaoscm.usercenter.utils.ThreadPoolUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class QrLoginServiceImpl implements QrLoginService, InitializingBean {

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private SMSProperties smsProperties;

	@Autowired
	private MqUtil mqUtil;

	private RedisJwtTokenService tokenService;

	@Autowired
	private ApplicationProperties applicationProperties;

	@Autowired
	private WxMiniProgramProperties properties;

	@Override
	public Optional<QrLoginVo> generateLoginCode() {
		QrLoginVo qrLoginVo = new QrLoginVo();
		String loginCode = UUID.randomUUID().toString().replace("-", "");
		String key = RedisKeys.Cache.QR_LOGIN + loginCode;
		redisClient.hPut(key, "loginCode", loginCode);
		redisClient.hPut(key, "scan", "0");
		redisClient.expire(key, 5, TimeUnit.MINUTES);
		qrLoginVo.setQrCode(smsProperties.getAppLinkUrl() + "?bizType=qrLogin"
				+ "&loginCode=" + loginCode);
		ThreadPoolUtil.scheduleTask(() -> {
			log.info("loginCode失效:{}", loginCode);
			// 发送ws通知loginCode失效
			mqUtil.asyncSend(
					MqMessage.builder().topic(TopicDef.QR_LOGIN)
							.message(MessageBuilder.withPayload(JsonUtils
									.objectToJson(new QrLoginDto(loginCode,
											TopicDef.Type.QR_LOGIN_EXPIRED
													.getCode(),
											null)))
									.build())
							.build());

		}, 5, TimeUnit.MINUTES, ThreadPoolUtil.getUserScheduledExecutor());
		return Optional.of(qrLoginVo);
	}

	@Override
	public void scan(String loginCode) {
		String key = RedisKeys.Cache.QR_LOGIN + loginCode;
		redisClient.hPut(key, "loginCode", loginCode);
		redisClient.hPut(key, "scan", "1");
		// 发送ws通知loginCode已经被扫描
		mqUtil.asyncSend(
				MqMessage.builder().topic(TopicDef.QR_LOGIN)
						.message(MessageBuilder
								.withPayload(JsonUtils
										.objectToJson(new QrLoginDto(loginCode,
												TopicDef.Type.QR_LOGIN_SCAN
														.getCode(),
												null)))
								.build())
						.build());
	}

	@Override
	public void confirmLogin(String loginCode, String token) {
		String key = RedisKeys.Cache.QR_LOGIN + loginCode;
		redisClient.delete(key);

		LoginUser<CustomerLoginVo> customerLoginVoLoginUser = tokenService
				.readUser(token);
		String accessToken = tokenService.createAccessToken(
				customerLoginVoLoginUser, CertificationDef.Origin.PC.getCode());

		// 发送ws给pc新的token值
		mqUtil.asyncSend(MqMessage.builder().topic(TopicDef.QR_LOGIN)
				.message(MessageBuilder
						.withPayload(
								JsonUtils.objectToJson(new QrLoginDto(loginCode,
										TopicDef.Type.QR_LOGIN_CONFIRM
												.getCode(),
										accessToken)))
						.build())
				.build());
	}

	@Override
	public void cancelLogin(String loginCode) {
		// 发送ws通知loginCode已经取消
		mqUtil.asyncSend(
				MqMessage.builder().topic(TopicDef.QR_LOGIN)
						.message(MessageBuilder
								.withPayload(JsonUtils
										.objectToJson(new QrLoginDto(loginCode,
												TopicDef.Type.QR_LOGIN_CANCEL
														.getCode(),
												null)))
								.build())
						.build());
	}

	@Override
	public void afterPropertiesSet() {
		tokenService = new RedisJwtTokenService(redisClient);
		tokenService.setTokenExpire(properties.getLoginTokenExpire());
		tokenService.setJwtSecret(applicationProperties.getJwtSecret());
	}
}
