package com.zhihaoscm.usercenter.core.service;

import java.util.Objects;

import org.springframework.stereotype.Component;

import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.usercenter.config.security.custom.CustomerContextHolder;

@Component
public class CertificationServiceExt {

	/**
	 * 从上下文中获取客户信息
	 *
	 * @return
	 */
	public Customer findCustomerFromContext() {
		return Objects.requireNonNull(
				CustomerContextHolder.getCustomerLoginVo().getActualAccount());
	}
}
