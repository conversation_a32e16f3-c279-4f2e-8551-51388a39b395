package com.zhihaoscm.usercenter.resource.validator.qr.login;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.resource.form.qr.login.ConfirmLoginForm;

@Component
public class QrLoginValidator {

	@Autowired
	private StringRedisClient redisClient;

	/**
	 * 校验确认
	 * 
	 * @param form
	 */
	public void validateConfirm(ConfirmLoginForm form) {
		String loginCode = form.getLoginCode();
		if (StringUtils.isBlank(redisClient
				.hGet(RedisKeys.Cache.QR_LOGIN + loginCode, "loginCode"))) {
			throw new BadRequestException(ErrorCode.CODE_30193002);
		}
	}

	/**
	 * 校验扫码
	 * 
	 * @param form
	 */
	public void validateScan(ConfirmLoginForm form) {
		this.validateConfirm(form);
		String key = RedisKeys.Cache.QR_LOGIN + form.getLoginCode();
		String scan = redisClient.hGet(key, "scan");
		if ("1".equals(scan)) {
			throw new BadRequestException(ErrorCode.CODE_30193002);
		}
	}
}
