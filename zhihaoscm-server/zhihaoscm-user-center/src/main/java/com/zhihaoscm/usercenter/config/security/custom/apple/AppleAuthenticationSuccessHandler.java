package com.zhihaoscm.usercenter.config.security.custom.apple;

import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.util.CommonWebUtils;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.security.token.TokenService;
import com.zhihaoscm.domain.meta.biz.CertificationDef;
import com.zhihaoscm.domain.utils.ReflectionHelper;
import com.zhihaoscm.usercenter.config.security.custom.TokenResponse;
import com.zhihaoscm.usercenter.config.security.custom.ValidatedAuthentication;

import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AppleAuthenticationSuccessHandler
		implements AuthenticationSuccessHandler {

	private final TokenService<?> tokenService;

	public AppleAuthenticationSuccessHandler(TokenService<?> tokenService) {
		this.tokenService = tokenService;
	}

	@Override
	public void onAuthenticationSuccess(HttpServletRequest request,
			HttpServletResponse response, FilterChain chain,
			Authentication authentication) {
		onAuthenticationSuccess(request, response, authentication);
	}

	@Override
	public void onAuthenticationSuccess(HttpServletRequest request,
			HttpServletResponse response, Authentication authentication) {
		try {
			Object principal = authentication.getPrincipal();
			ValidatedAuthentication validatedAuthentication = (ValidatedAuthentication) authentication;
			if (principal instanceof LoginUser user) {
				String token = tokenService.createAccessToken(user,
						CertificationDef.Origin.PC.getCode());
				CommonWebUtils.renderJson(response,
						new ApiResponse<>(new TokenResponse(token,
								user.getPassword(), null)),
						HttpStatus.OK.value());
				response.sendRedirect(ReflectionHelper
						.get(validatedAuthentication.getObj(), "url")
						+ "?token=" + token);
			}
		} catch (Exception e) {
			if (log.isDebugEnabled()) {
				log.debug("", e);
			}

			throw new InternalAuthenticationServiceException(
					"failure to create token");
		}
	}

}
