package com.zhihaoscm.usercenter.resource.common;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.mybatis.plus.resource.MpLongIdBaseResource;
import com.zhihaoscm.domain.bean.entity.CustomerEnterprise;
import com.zhihaoscm.usercenter.core.service.CustomerEnterpriseService;

/**
 * <p>
 * 子账号中间表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@RestController
@RequestMapping("/customer-enterprise-rest")
public class CustomerEnterpriseResource extends
		MpLongIdBaseResource<CustomerEnterprise, CustomerEnterpriseService> {

	public CustomerEnterpriseResource(CustomerEnterpriseService service) {
		super(service);
	}

	@GetMapping(value = "find/mainid")
	List<CustomerEnterprise> findByMainId(@RequestParam Long mainAccountId,
			@RequestParam(required = false) Integer state) {
		return service.findByMainId(mainAccountId, state);
	}

	@GetMapping("/find/main/sub")
	CustomerEnterprise findByMainAndSubId(@RequestParam Long subAccountId,
			@RequestParam Long mainAccountId,
			@RequestParam(required = false) Integer state) {
		return service.findByMainAndSubId(subAccountId, mainAccountId, state)
				.orElse(null);
	}

}
