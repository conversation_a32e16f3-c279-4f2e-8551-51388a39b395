package com.zhihaoscm.usercenter.core.service.impl;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.dto.CustomerStateRefreshDto;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerEnterprise;
import com.zhihaoscm.domain.bean.json.CustomerMinder;
import com.zhihaoscm.domain.bean.json.notice.Messages;
import com.zhihaoscm.domain.bean.security.LoginUser;
import com.zhihaoscm.domain.bean.vo.CustomerEnterpriseVo;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.notice.AppType;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.usercenter.config.properties.SMSProperties;
import com.zhihaoscm.usercenter.core.mapper.CustomerEnterpriseMapper;
import com.zhihaoscm.usercenter.core.service.CustomerEnterpriseService;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.core.service.MessageService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 子账号中间表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Slf4j
@Service
public class CustomerEnterpriseServiceImpl extends
		MpLongIdBaseServiceImpl<CustomerEnterprise, CustomerEnterpriseMapper>
		implements CustomerEnterpriseService {

	@Autowired
	private CustomerService customerService;
	@Autowired
	private SMSProperties smsProperties;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private MqUtil mqUtil;
	@Autowired
	private MessageService messageService;

	public CustomerEnterpriseServiceImpl(CustomerEnterpriseMapper repository) {
		super(repository);
	}

	@Override
	public Page<CustomerEnterpriseVo> paging(Integer page, Integer size,
			Integer state, Long mainAccountId, String searchParam,
			String sortKey, String sortDirection, Integer accountState) {
		Page<CustomerEnterprise> pageParam = new Page<>(page, size);
		Page<CustomerEnterprise> record = repository.paging(pageParam,
				mainAccountId, state, searchParam, sortKey, sortDirection,
				accountState);
		return PageUtil.getRecordsInfoPage(record, this.packVo(
				record.getRecords(), CustomerEnterprise::getSubAccountId));
	}

	@Override
	public Page<CustomerEnterpriseVo> invitedPaging(Integer page, Integer size,
			Long actualAccountId, Integer state, String institutionName) {
		Page<CustomerEnterprise> pageParam = new Page<>(page, size);
		pageParam.setOptimizeCountSql(Boolean.FALSE);
		Page<CustomerEnterprise> record = repository.invitedPaging(pageParam,
				actualAccountId, state, institutionName);
		return PageUtil.getRecordsInfoPage(record, this.packVo(
				record.getRecords(), CustomerEnterprise::getMainAccountId));
	}

	@Override
	public List<CustomerEnterpriseVo> invitedList(Long actualAccountId) {
		LambdaQueryWrapper<CustomerEnterprise> wrapper = Wrappers
				.lambdaQuery(CustomerEnterprise.class)
				.eq(CustomerEnterprise::getSubAccountId, actualAccountId)
				.eq(CustomerEnterprise::getDel, CommonDef.Symbol.NO.getCode())
				.eq(CustomerEnterprise::getAccountState,
						CommonDef.Symbol.YES.getCode())
				.eq(CustomerEnterprise::getState,
						CustomerEnterpriseDef.State.CONFIRMED.getCode())
				.orderByDesc(CustomerEnterprise::getCreatedTime);
		List<CustomerEnterprise> customerEnterpriseList = repository
				.selectList(wrapper);
		// 过滤出是认证通过的
		return this.packCustomerEnterpriseVo(customerEnterpriseList,
				CustomerEnterprise::getMainAccountId);
	}

	@Override
	public Optional<CustomerEnterpriseVo> findVoById(Long id,
			Integer queryType) {
		return super.findOne(id).map(ce -> packVo(ce, queryType));
	}

	@Override
	public Optional<CustomerEnterpriseVo> findSelfVoById(Long subAccountId,
			Long mainAccountId) {
		CustomerEnterpriseVo vo = new CustomerEnterpriseVo();
		this.findByMainAndSubId(subAccountId, mainAccountId,
				CustomerEnterpriseDef.State.CONFIRMED.getCode())
				.ifPresent(vo::setCustomerEnterprise);
		customerService.findOne(subAccountId).ifPresent(vo::setAccount);
		return Optional.of(vo);
	}

	@Override
	public List<CustomerEnterprise> findByMainId(Long mainAccountId,
			Integer state) {
		LambdaQueryWrapper<CustomerEnterprise> wrapper = this.buildFindWrapper(
				mainAccountId, state, CustomerEnterprise::getMainAccountId);
		wrapper.eq(CustomerEnterprise::getAccountState,
				CommonDef.Symbol.YES.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public List<CustomerEnterprise> findSubByMainId(Long mainAccountId,
			Integer state) {
		LambdaQueryWrapper<CustomerEnterprise> wrapper = this.buildFindWrapper(
				mainAccountId, state, CustomerEnterprise::getMainAccountId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<CustomerEnterprise> findBySubId(Long subAccountId,
			Integer state) {
		LambdaQueryWrapper<CustomerEnterprise> wrapper = this.buildFindWrapper(
				subAccountId, state, CustomerEnterprise::getSubAccountId);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<CustomerEnterprise> findByMainAndSubId(Long subAccountId,
			Long mainAccountId, Integer state) {
		LambdaQueryWrapper<CustomerEnterprise> wrapper = Wrappers
				.lambdaQuery(CustomerEnterprise.class)
				.eq(CustomerEnterprise::getSubAccountId, subAccountId)
				.eq(CustomerEnterprise::getMainAccountId, mainAccountId)
				.eq(Objects.nonNull(state), CustomerEnterprise::getState, state)
				.eq(CustomerEnterprise::getDel, CommonDef.Symbol.NO.getCode())
				.last("LIMIT 1");
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public List<CustomerEnterprise> findByMainIdAndState(Long mainAccountId,
			Integer state) {
		LambdaQueryWrapper<CustomerEnterprise> queryWrapper = Wrappers
				.lambdaQuery(CustomerEnterprise.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(CustomerEnterprise::getMainAccountId, mainAccountId).eq(
				Objects.nonNull(state), CustomerEnterprise::getState, state);
		return repository.selectList(queryWrapper);
	}

	@Override
	public void confirm(Long id, Integer state) {
		this.findOne(id).ifPresent(customerEnterprise -> {
			customerEnterprise.setState(state);
			super.updateAllProperties(customerEnterprise);
		});
	}

	@Override
	public CustomerEnterprise create(CustomerEnterprise entity) {
		// 子账号在系统中不存在时不做任何响应
		if (Objects.isNull(entity.getSubAccountId())) {
			return null;
		}
		// 获取主账号信息
		Customer mainAccount = this.getAccount(entity.getMainAccountId());
		Customer subAccount = this.getAccount(entity.getSubAccountId());

		CustomerEnterprise customerEnterprise = super.create(entity);
		// 发送短信通知子账号持有人进行确认
		CustomerMinder remind = new CustomerMinder();
		remind.setParams(Map.of(ForeignUrlDef.PARAM_ID,
				String.valueOf(customerEnterprise.getId())));
		remind.setPath(smsProperties.getInvitationRecordPage());
		remind.setRole(null);
		remind.setReceiptor(subAccount);
		remind.setRemindType(CustomerMinder.RemindType.subAccountCreate);
		remind.setTitle(MessageFormat.format(
				UserMessageConstants.CONFIRM_ADD_SUB_TEMPLATE,
				mainAccount.getInstitutionName()));
		remind.setDetailId(String.valueOf(entity.getId()));
		remind.getSmsParams().put("co_name", mainAccount.getInstitutionName());
		mqUtil.asyncSend(MqMessage.builder()
				.topic(TopicDef.CUSTOMER_ACCOUNT_MIND)
				.message(MessageBuilder
						.withPayload(JsonUtils.objectToJson(remind)).build())
				.build());

		// APP推送
		messageService.sendNotice(Messages.builder()
				.type(UserMessageDef.MessageType.OTHER.getCode())
				.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
				.content(MessageFormat.format(
						UserMessageConstants.CONFIRM_ADD_SUB_TEMPLATE,
						mainAccount.getInstitutionName()))
				.title(UserMessageConstants.SUB_ACCOUNT_TITLE)
				.appTypes(List.of(AppType.LIANYUN))
				.receiptors(List.of(subAccount.getId().toString()))
				.moduleType(UserMessageConstants.INVITE_DETAIL_PAGE)
				.bizNo(String.valueOf(entity.getId())).build());

		return customerEnterprise;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(Long id, Integer operationType) {
		if (CustomerEnterpriseDef.OperationType.DELETE_ACCOUNT
				.match(operationType)) {
			AtomicReference<Boolean> isDel = new AtomicReference<>(
					Boolean.FALSE);
			super.findOne(id).ifPresent(customerEnterprise -> {
				// 若此时子账号正在登陆代理账号则删除其token
				Customer subAccount = this
						.getAccount(customerEnterprise.getSubAccountId());
				String originalKey = RedisKeys.Cache.MINI_LOGIN_INFO_PREFIX
						+ subAccount.getId();
				String encryptKey = UsernameSalt.encryptMiniLogin(originalKey)
						+ "*";
				Set<String> keys = redisClient.keys(encryptKey);
				for (String key : keys) {
					LoginUser<CustomerLoginVo> loginUser = JsonUtils
							.jsonToObject(redisClient.get(key),
									new TypeReference<>() {
									});
					if (Objects.equals(
							loginUser.getUser().getProxyAccount().getId(),
							customerEnterprise.getMainAccountId())) {
						redisClient.delete(key);
						isDel.set(Boolean.TRUE);
					}
				}
				// 发送ws通知
				if (isDel.get()) {
					// 删除上次登陆企业的记录
					String lastOriginalKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
							+ subAccount.getMobile();
					String lastEncryptKey = UsernameSalt
							.encryptMiniLogin(lastOriginalKey);
					redisClient.delete(lastEncryptKey);
					mqUtil.asyncSend(MqMessage.builder()
							.topic(TopicDef.CUSTOM_USER_CHANGE)
							.message(MessageBuilder.withPayload(JsonUtils
									.objectToJson(new CustomerStateRefreshDto(
											TopicDef.Type.CHOOSE_ENTERPRISE
													.getCode(),
											List.of(subAccount.getId()))))
									.build())
							.build());
				}
			});
		}
		super.delete(id);
	}

	/**
	 * 构建查询条件装饰器
	 *
	 * @param accountId
	 *            账号id
	 * @param state
	 *            状态
	 * @param column
	 *            查询字段
	 * @return 查询条件装饰器
	 */
	private LambdaQueryWrapper<CustomerEnterprise> buildFindWrapper(
			Long accountId, Integer state,
			SFunction<CustomerEnterprise, ?> column) {
		LambdaQueryWrapper<CustomerEnterprise> wrapper = Wrappers
				.lambdaQuery(CustomerEnterprise.class).eq(column, accountId)
				.eq(CustomerEnterprise::getDel, CommonDef.Symbol.NO.getCode());
		if (Objects.nonNull(state)) {
			wrapper.eq(CustomerEnterprise::getState, state);
		}
		return wrapper;
	}

	/**
	 * 通过主账号id获取主账号信息
	 *
	 * @param accountId
	 *            账号id
	 * @return 主账号信息
	 */
	private Customer getAccount(Long accountId) {
		return customerService.findOne(accountId).orElse(null);
	}

	/**
	 * 打包vo对象
	 *
	 * @param records
	 *            子账号实体列表
	 * @param func
	 *            获取账号id
	 * @return vo对象列表
	 */
	private List<CustomerEnterpriseVo> packVo(List<CustomerEnterprise> records,
			Function<CustomerEnterprise, Long> func) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		return records.stream().map(customerEnterprise -> {
			CustomerEnterpriseVo vo = new CustomerEnterpriseVo();
			vo.setCustomerEnterprise(customerEnterprise);
			customerService.findOne(func.apply(customerEnterprise))
					.ifPresent(vo::setAccount);
			return vo;
		}).toList();
	}

	/**
	 * 打包vo对象
	 *
	 * @param records
	 *            子账号实体列表
	 * @param func
	 *            获取账号id
	 * @return vo对象列表
	 */
	private List<CustomerEnterpriseVo> packCustomerEnterpriseVo(
			List<CustomerEnterprise> records,
			Function<CustomerEnterprise, Long> func) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		// 主账号ID集合
		List<Long> customerIds = records.stream()
				.map(CustomerEnterprise::getMainAccountId).toList();
		// 认证通过的ID集合
		List<Long> sealAdminCustomerIds = customerService.findByIds(customerIds)
				.stream()
				.filter(item -> CommonDef.Symbol.YES
						.match(item.getApplyState()))
				.map(Customer::getId).toList();

		// 过滤出认证的数据
		return records.stream().filter(
				item -> sealAdminCustomerIds.contains(item.getMainAccountId()))
				.map(customerEnterprise -> {
					CustomerEnterpriseVo vo = new CustomerEnterpriseVo();
					vo.setCustomerEnterprise(customerEnterprise);
					customerService.findOne(func.apply(customerEnterprise))
							.ifPresent(vo::setAccount);
					return vo;
				}).toList();
	}

	/**
	 * 打包vo对象
	 *
	 * @param customerEnterprise
	 *            关联表实体
	 * @param queryType
	 *            查询类型
	 * @return vo对象
	 */
	private CustomerEnterpriseVo packVo(CustomerEnterprise customerEnterprise,
			Integer queryType) {
		CustomerEnterpriseVo vo = new CustomerEnterpriseVo();
		vo.setCustomerEnterprise(customerEnterprise);
		if (CustomerEnterpriseDef.QueryType.MAIN_ACCOUNT.match(queryType)) {
			customerService.findOne(customerEnterprise.getMainAccountId())
					.ifPresent(vo::setAccount);
		} else if (CustomerEnterpriseDef.QueryType.SUB_ACCOUNT
				.match(queryType)) {
			customerService.findOne(customerEnterprise.getSubAccountId())
					.ifPresent(vo::setAccount);
		}
		return vo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchUpdateAccountState(List<Long> mainIds,
			Integer accountState) {
		if (CollectionUtils.isEmpty(mainIds)) {
			return;
		}
		LambdaQueryWrapper<CustomerEnterprise> wrapper = Wrappers
				.lambdaQuery(CustomerEnterprise.class);
		wrapper.in(CustomerEnterprise::getMainAccountId, mainIds);
		wrapper.eq(CustomerEnterprise::getAccountState,
				CommonDef.Symbol.YES.getCode());
		wrapper.in(CustomerEnterprise::getState,
				CustomerEnterpriseDef.State.CONFIRMED.getCode());
		List<CustomerEnterprise> customerEnterprises = repository
				.selectList(wrapper);
		if (CollectionUtils.isEmpty(customerEnterprises)) {
			return;
		}
		List<Long> ids = new ArrayList<>();
		customerEnterprises.forEach(customerEnterprise -> {
			AtomicReference<Boolean> isDel = new AtomicReference<>(
					Boolean.FALSE);
			// 若此时子账号正在登陆代理账号则删除其token
			Customer subAccount = this
					.getAccount(customerEnterprise.getSubAccountId());
			String originalKey = RedisKeys.Cache.MINI_LOGIN_INFO_PREFIX
					+ subAccount.getId();
			String encryptKey = UsernameSalt.encryptMiniLogin(originalKey)
					+ "*";
			Set<String> keys = redisClient.keys(encryptKey);
			for (String key : keys) {
				LoginUser<CustomerLoginVo> loginUser = JsonUtils.jsonToObject(
						redisClient.get(key), new TypeReference<>() {
						});
				if (Objects.equals(
						loginUser.getUser().getProxyAccount().getId(),
						customerEnterprise.getMainAccountId())) {
					redisClient.delete(key);
					isDel.set(Boolean.TRUE);
				}
			}
			// 发送ws通知
			if (isDel.get()) {
				// 删除上次登陆企业的记录
				String lastOriginalKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
						+ subAccount.getMobile();
				String lastEncryptKey = UsernameSalt
						.encryptMiniLogin(lastOriginalKey);
				redisClient.delete(lastEncryptKey);
				ids.add(subAccount.getId());

			}
		});
		if (!ids.isEmpty()) {
			mqUtil.asyncSend(MqMessage.builder()
					.topic(TopicDef.CUSTOM_USER_CHANGE)
					.message(MessageBuilder.withPayload(
							JsonUtils.objectToJson(new CustomerStateRefreshDto(
									TopicDef.Type.CHOOSE_ENTERPRISE.getCode(),
									ids)))
							.build())
					.build());
		}
		LambdaUpdateWrapper<CustomerEnterprise> updateWrapper = Wrappers
				.lambdaUpdate(CustomerEnterprise.class);
		updateWrapper.set(CustomerEnterprise::getAccountState, accountState);
		updateWrapper.in(CustomerEnterprise::getMainAccountId, mainIds);
		repository.update(updateWrapper);
	}

	@Override
	public List<Long> refreshSubAccountContext(Long id,
			Boolean includeYourself) {
		Customer customer = this.getAccount(id);
		LambdaQueryWrapper<CustomerEnterprise> wrapper = Wrappers
				.lambdaQuery(CustomerEnterprise.class);
		wrapper.eq(CustomerEnterprise::getMainAccountId, id);
		wrapper.eq(CustomerEnterprise::getAccountState,
				CommonDef.Symbol.YES.getCode());
		wrapper.in(CustomerEnterprise::getState,
				CustomerEnterpriseDef.State.CONFIRMED);
		List<CustomerEnterprise> customerEnterprises = repository
				.selectList(wrapper);
		if (CollectionUtils.isEmpty(customerEnterprises)) {
			if (includeYourself) {
				return List.of(id);
			}
			return List.of();
		}
		List<Long> ids = new ArrayList<>();
		customerEnterprises.forEach(customerEnterprise -> {
			// 若此时子账号正在登陆代理账号则删除其token
			Customer subAccount = this
					.getAccount(customerEnterprise.getSubAccountId());
			String originalKey = RedisKeys.Cache.MINI_LOGIN_INFO_PREFIX
					+ subAccount.getId();
			String encryptKey = UsernameSalt.encryptMiniLogin(originalKey)
					+ "*";
			Set<String> keys = redisClient.keys(encryptKey);
			for (String key : keys) {
				LoginUser<CustomerLoginVo> loginUser = JsonUtils.jsonToObject(
						redisClient.get(key), new TypeReference<>() {
						});
				if (Objects.equals(
						loginUser.getUser().getProxyAccount().getId(),
						customerEnterprise.getMainAccountId())) {
					loginUser.getUser().setProxyAccount(customer);
					redisClient.setEx(key, JsonUtils.objectToJson(loginUser),
							30 * 24 * 60 * 60L, TimeUnit.SECONDS);
					ids.add(subAccount.getId());
				}
			}
		});
		if (includeYourself) {
			ids.add(id);
		}
		return ids;
	}

	@Override
	public CustomerEnterprise updateAllProperties(CustomerEnterprise resource) {
		// 如果子账号是已失效，需更新token信息
		this.refreshToken(resource);
		return super.updateAllProperties(resource);
	}

	/**
	 * 刷新token
	 * 
	 * @param resource
	 */
	private void refreshToken(CustomerEnterprise resource) {
		if (CommonDef.Symbol.YES.match(resource.getAccountState())) {
			return;
		}
		List<Long> ids = new ArrayList<>();
		AtomicReference<Boolean> isDel = new AtomicReference<>(Boolean.FALSE);
		// 若此时子账号正在登陆代理账号则删除其token
		Customer subAccount = this.getAccount(resource.getSubAccountId());
		String originalKey = RedisKeys.Cache.MINI_LOGIN_INFO_PREFIX
				+ subAccount.getId();
		String encryptKey = UsernameSalt.encryptMiniLogin(originalKey) + "*";
		Set<String> keys = redisClient.keys(encryptKey);
		for (String key : keys) {
			LoginUser<CustomerLoginVo> loginUser = JsonUtils
					.jsonToObject(redisClient.get(key), new TypeReference<>() {
					});
			if (Objects.equals(loginUser.getUser().getProxyAccount().getId(),
					resource.getMainAccountId())) {
				redisClient.delete(key);
				isDel.set(Boolean.TRUE);
			}
		}
		// 发送ws通知
		if (isDel.get()) {
			// 删除上次登陆企业的记录
			String lastOriginalKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
					+ subAccount.getMobile();
			String lastEncryptKey = UsernameSalt
					.encryptMiniLogin(lastOriginalKey);
			redisClient.delete(lastEncryptKey);
			ids.add(subAccount.getId());

		}
		if (!ids.isEmpty()) {
			mqUtil.asyncSend(MqMessage.builder()
					.topic(TopicDef.CUSTOM_USER_CHANGE)
					.message(MessageBuilder.withPayload(
							JsonUtils.objectToJson(new CustomerStateRefreshDto(
									TopicDef.Type.CHOOSE_ENTERPRISE.getCode(),
									ids)))
							.build())
					.build());
		}
	}
}
