package com.zhihaoscm.usercenter.core.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.DxyzmUtils;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.dto.CustomerStateRefreshDto;
import com.zhihaoscm.domain.bean.entity.ContractLockRecord;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerChangeMobile;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.EmailMessage;
import com.zhihaoscm.domain.bean.vo.CustomerVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.CertificationDef;
import com.zhihaoscm.domain.meta.biz.CustomerChangeMobileDef;
import com.zhihaoscm.domain.meta.biz.TopicDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.properties.SMSProperties;
import com.zhihaoscm.usercenter.utils.RegexUtils;
import com.zhihaoscm.usercenter.utils.TokenUtils;

/**
 * 短信服务
 */
@Component
public class MsgService {

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private SMSProperties smsProperties;

	@Autowired
	private MessageService messageService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private CustomerChangeMobileService customerChangeMobileService;

	@Autowired
	private CustomerEnterpriseCertificationService customerEnterpriseCertificationService;

	@Autowired
	private ContractLockRecordService contractLockRecordService;

	@Autowired
	private CustomerEnterpriseService customerEnterpriseService;

	@Autowired
	private MqUtil mqUtil;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	/**
	 * 验证码过期时间
	 */
	private final static Long LOGIN_EXPIRE = 5 * 60L;

	/**
	 * 发送手机登录验证码
	 *
	 * @param mobile
	 */
	public void send(String mobile) {
		send(RedisKeys.Cache.LOGIN_MSG_PREFIX, mobile, null,
				smsProperties.getLoginVerifyCode());
	}

	/**
	 * 校验手机验证码
	 *
	 * @param mobile
	 * @param code
	 * @return
	 */
	public Boolean check(String mobile, String code) {
		String key = RedisKeys.Cache.DEMAND_MSG_PREFIX + mobile;
		String value = redisClient.get(key);
		if (StringUtils.isBlank(value)) {
			return false;
		}
		return value.equals(code);
	}

	/**
	 * 发送其他
	 *
	 * @param mobile
	 */
	public void sendOther(String mobile) {
		send(RedisKeys.Cache.DEMAND_MSG_PREFIX, mobile, null,
				smsProperties.getIntentionVerifyCode());
	}

	/**
	 * 发送换绑手机号验证码
	 *
	 * @param mobile
	 */
	public void sendChangeMobile(String mobile) {
		send(RedisKeys.Cache.CHANGE_MOBILE_MSG_PREFIX, mobile, null,
				smsProperties.getIntentionVerifyCode());
	}

	/**
	 * 输完验证码后返回
	 *
	 * @param mobile
	 * @return
	 */
	public Optional<Integer> changeMobile(String mobile, Long id) {
		Customer customer = customerService.findOne(id).orElse(null);
		CustomerVo customerVo = customerEnterpriseCertificationService
				.certDetail(id).orElse(null);
		// 查询用户的换绑手机号记录
		CustomerChangeMobile customerChangeMobile = customerChangeMobileService
				.findByCustomerId(id).orElse(null);
		// 用户信息不为空
		if (Objects.nonNull(customer)) {
			// institution_name 不为空则在契约锁做了企业认证 这种情况不能进行换绑
			if (StringUtils.isNotBlank(customer.getInstitutionName())) {
				// 返回当前用户所处状态
				return Optional
						.of(CustomerChangeMobileDef.CustomerState.ENTERPRISEAUTH
								.getCode());
			}
			// apply_state为1在我们系统内做了企业认证,完成个人认证就换绑成功
			else if (CommonDef.Symbol.YES.match(customer.getApplyState())) {
				if (Objects.nonNull(customerChangeMobile)) {
					// 当前用户存在换绑手机号记录则更新记录
					this.createOrUpCustomerChangeMobile(id,
							CustomerChangeMobileDef.CustomerState.PERSONAUTH
									.getCode(),
							CustomerChangeMobileDef.CustomerState.UNREGISTERED
									.getCode(),
							mobile, customerVo, customerChangeMobile);
				} else {
					// 不存在则插入一条换绑手机号记录 只需要完成个人认证就换绑成功
					this.createOrUpCustomerChangeMobile(id,
							CustomerChangeMobileDef.CustomerState.PERSONAUTH
									.getCode(),
							CustomerChangeMobileDef.CustomerState.UNREGISTERED
									.getCode(),
							mobile, customerVo, null);
				}
				// 返回当前用户所处状态
				return Optional.of(
						CustomerChangeMobileDef.CustomerState.SYSENTERPRISEAUTH
								.getCode());
			}
			// real_name 不为空则做了个人认证，完成个人认证就换绑成功
			else if (StringUtils.isNotBlank(customer.getRealName())) {
				if (Objects.nonNull(customerChangeMobile)) {
					// 当前用户存在换绑手机号记录则更新记录
					this.createOrUpCustomerChangeMobile(id,
							CustomerChangeMobileDef.CustomerState.PERSONAUTH
									.getCode(),
							CustomerChangeMobileDef.CustomerState.UNREGISTERED
									.getCode(),
							mobile, customerVo, customerChangeMobile);
				} else {
					// 不存在则插入一条换绑手机号记录
					this.createOrUpCustomerChangeMobile(id,
							CustomerChangeMobileDef.CustomerState.PERSONAUTH
									.getCode(),
							CustomerChangeMobileDef.CustomerState.UNREGISTERED
									.getCode(),
							mobile, customerVo, null);
				}
				// 返回当前用户所处状态
				return Optional
						.of(CustomerChangeMobileDef.CustomerState.PERSONAUTH
								.getCode());
			} else {
				// 获取用户上一次登录的代理账号
				String lastLoginKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
						+ customer.getMobile();
				String encryptedKey = UsernameSalt
						.encryptMiniLogin(lastLoginKey);
				redisClient.delete(encryptedKey);
				// 未实名的直接更新customer表数据
				customer.setMobile(mobile);
				customer.setNickName(mobile);
				customerService.updateAllProperties(customer);
				// 删除token
				TokenUtils.deleteCustomer(customer.getId(), redisClient);
				// 换绑成功 给换绑后的手机号发短信
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(wxSubscriptionProperties
								.getChangeCustomerMobileCode())
						.params(Map.of("phone", customer.getMobile()))
						.mobile(customer.getMobile()).build());
				// 通知客户端刷新客户信息
				mqUtil.asyncSend(MqMessage.builder()
						.topic(TopicDef.CUSTOM_USER_CHANGE)
						.message(MessageBuilder.withPayload(JsonUtils
								.objectToJson(new CustomerStateRefreshDto(
										TopicDef.Type.REFRESH_STATE.getCode(),
										TokenUtils.getNoticeIds(
												customer.getId(),
												customerEnterpriseService,
												Boolean.TRUE))))
								.build())
						.build());
				return Optional
						.of(CustomerChangeMobileDef.CustomerState.UNREGISTERED
								.getCode());
			}
		} else {
			return Optional.empty();
		}
	}

	/**
	 * 客户PC端发送手机登录验证码
	 *
	 * @param mobile
	 * @param serialNumber
	 */
	public void sendPcLogin(String mobile, String serialNumber) {
		String key = RedisKeys.Cache.PC_CUSTOM_CAPTCHA_PREFIX + serialNumber;
		String verfied = redisClient.get(key);
		if ("8888".equals(serialNumber)) {
			verfied = "true";
		}
		if (StringUtils.isBlank(verfied) || !Boolean.parseBoolean(verfied)) {
			throw new BadRequestException(ErrorCode.CODE_30011001);
		}
		this.send(RedisKeys.Cache.PC_LOGIN_MSG_PREFIX, mobile, null,
				smsProperties.getLoginVerifyCode());
	}

	private void send(String msgPrefix, String mobile, String email,
			String msgCode) {
		String key = "";
		String code = DxyzmUtils.getNonceNumber(6);
		if (StringUtils.isNotBlank(email)) {
			key = msgPrefix + email;
			EmailMessage sms = EmailMessage.builder().email(email).code(code)
					.build();
			messageService.sendNotice(sms);
		}

		if (StringUtils.isNotBlank(mobile)) {
			key = msgPrefix + mobile;
			String value = redisClient.get(key);
			if (StringUtils.isNotBlank(value)) {
				return;
			}
			AliMessage sms = AliMessage.builder().templateCode(msgCode)
					.params(Map.of("code", code)).mobile(mobile).build();
			Long tenantId = Objects.isNull(UserInfoContextHolder.getContext())
					? null
					: UserInfoContextHolder.getContext().getTenantId();
			if (Objects.nonNull(tenantId)) {
				sms.setTenantId(tenantId);
			}
			messageService.sendNotice(sms);
		}
		redisClient.setEx(key, code, LOGIN_EXPIRE, TimeUnit.SECONDS);
	}

	public void createOrUpCustomerChangeMobile(Long id, Integer needState,
			Integer currentState, String mobile, CustomerVo customerVo,
			CustomerChangeMobile customerChangeMobile1) {
		CustomerChangeMobile customerChangeMobile = new CustomerChangeMobile();
		if (Objects.nonNull(customerChangeMobile1)) {
			customerChangeMobile = customerChangeMobile1;
		}
		customerChangeMobile.setCustomerId(id);
		customerChangeMobile.setNeedState(needState);
		customerChangeMobile.setCurrentState(currentState);
		customerChangeMobile.setMobile(mobile);
		// 契约锁调用记录-企业认证
		ContractLockRecord lockRecordEnterprise = contractLockRecordService
				.findByCustomerIdAndState(id,
						CertificationDef.InstitutionType.ENTERPRISE.getCode(),
						CertificationDef.CertificationState.VALID.getCode())
				.orElse(null);
		if (Objects.nonNull(lockRecordEnterprise)) {
			customerChangeMobile
					.setContractLockEnterpriseId(lockRecordEnterprise.getId());
		}
		// 契约锁调用记录-个人认证
		ContractLockRecord lockRecordPersonal = contractLockRecordService
				.findByCustomerIdAndState(id,
						CertificationDef.InstitutionType.PERSONAL.getCode(),
						CertificationDef.CertificationState.VALID.getCode())
				.orElse(null);
		if (Objects.nonNull(lockRecordPersonal)) {
			customerChangeMobile
					.setContractLockPersonalId(lockRecordPersonal.getId());
		}
		if (Objects.nonNull(customerVo)) {
			// 客户个人认证信息不为空时 将个人认证信息的id设置进换绑手机号记录
			if (Objects.nonNull(customerVo.getPersonalCertification())) {
				customerChangeMobile.setPersonalCertificationId(
						customerVo.getPersonalCertification().getId());
			}
		}
		if (Objects.nonNull(customerChangeMobile1)) {
			customerChangeMobileService
					.updateAllProperties(customerChangeMobile);
		} else {
			customerChangeMobileService.create(customerChangeMobile);
		}
	}

	/**
	 * 找回密码发送验证码
	 *
	 * @param account
	 */
	public void sendCaptcha(String account) {
		if (RegexUtils.match(RegexUtils.PHONE_CARD_PATTERN, account)) {
			send(RedisKeys.Cache.CAPTCHA_MOBILE_MSG_PREFIX, account, null,
					smsProperties.getLoginVerifyCode());
		} else if (RegexUtils.match(RegexUtils.EMAIL_PATTERN, account)) {
			send(RedisKeys.Cache.CAPTCHA_EMAIL_MSG_PREFIX, null, account, null);
		}
	}

	/**
	 * 找回密码校验验证码
	 *
	 * @param account
	 */
	public Boolean checkCaptcha(String account, String code) {
		if (RegexUtils.match(RegexUtils.PHONE_CARD_PATTERN, account)) {
			String key = RedisKeys.Cache.CAPTCHA_MOBILE_MSG_PREFIX + account;
			String value = redisClient.get(key);
			return code.equals(value);
		} else if (RegexUtils.match(RegexUtils.EMAIL_PATTERN, account)) {
			String key = RedisKeys.Cache.CAPTCHA_EMAIL_MSG_PREFIX + account;
			String value = redisClient.get(key);
			return code.equals(value);
		}
		return false;
	}

	/**
	 * 发送验证码-注销账号
	 *
	 * @param mobile
	 */
	public void sendLogOffCode(String mobile) {
		this.send(RedisKeys.Cache.LOG_OFF_MSG_PREFIX, mobile, null,
				smsProperties.getLoginVerifyCode());
	}

	/**
	 * 校验验证码-注销账号
	 *
	 * @param mobile
	 * @param code
	 * @return
	 */
	public Boolean checkLogOffCode(String mobile, String code) {
		String key = RedisKeys.Cache.LOG_OFF_MSG_PREFIX + mobile;
		String value = redisClient.get(key);
		return code.equals(value);
	}
}
