package com.zhihaoscm.usercenter.config.security.custom.client.mobile;

import java.util.Objects;
import java.util.Optional;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationConverter;

import com.zhihaoscm.common.api.util.CommonWebUtils;
import com.zhihaoscm.common.util.utils.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

public class ClientMobileAuthenticationConverter
		implements AuthenticationConverter {

	@Override
	public Authentication convert(HttpServletRequest request) {
		Optional<ClientMobileLoginForm> login = CommonWebUtils
				.getRequestBody(request, ClientMobileLoginForm.class);

		if (login.isEmpty()) {
			throw new BadCredentialsException("missing accessToken");
		}

		ClientMobileLoginForm loginForm = login.get();

		if (StringUtils.isBlank(loginForm.getAccessToken())) {
			throw new BadCredentialsException("missing accessToken");
		}
		if (Objects.isNull(loginForm.getAppType())) {
			throw new BadCredentialsException("missing appType");
		}
		if (Objects.isNull(loginForm.getOrigin())) {
			throw new BadCredentialsException("missing origin");
		}
		return new ClientMobileAuthentication(login.get());
	}

}
