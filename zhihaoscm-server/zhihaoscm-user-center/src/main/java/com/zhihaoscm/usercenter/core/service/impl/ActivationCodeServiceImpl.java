package com.zhihaoscm.usercenter.core.service.impl;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.dto.ExchangeMemberDto;
import com.zhihaoscm.domain.bean.entity.ActivationCode;
import com.zhihaoscm.domain.bean.entity.MembershipLevel;
import com.zhihaoscm.domain.bean.json.CustomerJsonInfo;
import com.zhihaoscm.domain.bean.vo.ActivationCodeVo;
import com.zhihaoscm.domain.meta.biz.ActivationCodeDef;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.MemberOpenRecordDef;
import com.zhihaoscm.usercenter.core.mapper.ActivationCodeMapper;
import com.zhihaoscm.usercenter.core.service.ActivationCodeService;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.core.service.MemberOpenRecordService;
import com.zhihaoscm.usercenter.core.service.MembershipLevelService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Slf4j
@Service
public class ActivationCodeServiceImpl
		extends MpLongIdBaseServiceImpl<ActivationCode, ActivationCodeMapper>
		implements ActivationCodeService {

	@Autowired
	private MemberOpenRecordService openRecordService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private MembershipLevelService membershipLevelService;

	public ActivationCodeServiceImpl(ActivationCodeMapper repository) {
		super(repository);
	}

	@Override
	public Page<ActivationCode> paging(Integer page, Integer size, String code,
			String searchParam, Integer level, Integer hasBind, Integer state) {
		LambdaQueryWrapper<ActivationCode> wrapper = Wrappers
				.lambdaQuery(ActivationCode.class)
				.eq(Objects.nonNull(level), ActivationCode::getLevel, level)
				.eq(Objects.nonNull(hasBind), ActivationCode::getHasBind,
						hasBind)
				.eq(Objects.nonNull(state), ActivationCode::getState, state)
				.eq(ActivationCode::getDel, CommonDef.Symbol.NO.getCode())
				.eq(StringUtils.isNotBlank(code), ActivationCode::getCode, code)
				.like(StringUtils.isNoneBlank(searchParam),
						ActivationCode::getCustomerInfo, searchParam)
				.orderByDesc(ActivationCode::getCreatedTime);
		Page<ActivationCode> pageRecord = repository
				.selectPage(new Page<>(page, size), wrapper);
		this.desensitivity(pageRecord);
		return pageRecord;
	}

	@Override
	public Optional<ActivationCode> findByCode(String code) {
		LambdaQueryWrapper<ActivationCode> wrapper = Wrappers
				.lambdaQuery(ActivationCode.class)
				.eq(ActivationCode::getCode, code)
				.eq(ActivationCode::getDel, CommonDef.Symbol.NO.getCode());
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ActivationCodeVo> exchange(ActivationCode activationCode,
			Long customerId) {
		ExchangeMemberDto dto = new ExchangeMemberDto();
		dto.setOpenMode(MemberOpenRecordDef.OpenMode.ACTIVATION_CODE.getCode());
		dto.setCustomerId(customerId);
		dto.setMemberLevel(activationCode.getLevel());
		dto.setDays(activationCode.getDuration());
		dto.setActivationCode(activationCode.getCode());
		Optional<MembershipLevel> optionalMembershipLevel = membershipLevelService
				.findOne(Long.valueOf(dto.getMemberLevel()));
		if (optionalMembershipLevel.isEmpty()) {
			return Optional.of(new ActivationCodeVo(
					ActivationCodeDef.ErrorMessage.REDEMPTION_CLOSED
							.getCode()));
		}
		if (CommonDef.Symbol.NO.getCode()
				.equals(optionalMembershipLevel.get().getState())) {
			return Optional.of(new ActivationCodeVo(
					ActivationCodeDef.ErrorMessage.REDEMPTION_CLOSED
							.getCode()));
		}

		if (!openRecordService.exchangeMember(dto)) {
			return Optional.of(new ActivationCodeVo(
					ActivationCodeDef.ErrorMessage.REDEMPTION_FAILED
							.getCode()));
		}
		if (ActivationCodeDef.HasBind.NO.match(activationCode.getHasBind())) {
			activationCode.setHasBind(ActivationCodeDef.HasBind.YES.getCode());
			activationCode.setCustomerId(customerId);
			CustomerJsonInfo customerInfo = new CustomerJsonInfo();
			customerInfo.setId(customerId);
			customerService.findById(customerId).ifPresent(customer -> {
				customerInfo.setMobile(customer.getMobile());
				customerInfo.setCode(customer.getCode());
				customerInfo.setRealName(customer.getRealName());
				customerInfo.setInstitutionName(customer.getInstitutionName());
				customerInfo.setCreatedTime(customer.getCreatedTime());
			});
			activationCode.setCustomerInfo(customerInfo);
		}
		activationCode.setState(ActivationCodeDef.State.USED.getCode());
		this.updateAllProperties(activationCode);
		return Optional.of(new ActivationCodeVo(activationCode,
				ActivationCodeDef.ErrorMessage.REDEMPTION_SUCCESS.getCode()));
	}

	@Override
	public List<ActivationCode> findByCustomerId(Long customerId) {
		LambdaQueryWrapper<ActivationCode> wrapper = Wrappers
				.lambdaQuery(ActivationCode.class)
				.eq(ActivationCode::getCustomerId, customerId)
				.eq(ActivationCode::getDel, CommonDef.Symbol.NO.getCode())
				.orderBy(Boolean.TRUE, Boolean.TRUE, ActivationCode::getState)
				.orderBy(Boolean.TRUE, Boolean.FALSE,
						ActivationCode::getCreatedTime);
		return repository.selectList(wrapper);
	}

	@Override
	public void markExpiredRecords() {
		// 构造查询器
		Function<Long, LambdaQueryWrapper<ActivationCode>> wrapperFunc = id -> Wrappers
				.lambdaQuery(ActivationCode.class).select(ActivationCode::getId)
				.eq(ActivationCode::getState,
						ActivationCodeDef.State.UNUSED.getCode())
				.eq(ActivationCode::getDel, CommonDef.Symbol.NO.getCode())
				.lt(ActivationCode::getExpirationDate, LocalDate.now())
				.gt(Objects.nonNull(id), ActivationCode::getId, id)
				.orderByAsc(ActivationCode::getId).last("limit 5000");
		// 循环查询
		List<ActivationCode> list = repository
				.selectList(wrapperFunc.apply(null));
		while (CollectionUtils.isNotEmpty(list)) {
			List<Long> ids = list.stream().map(ActivationCode::getId).toList();
			LambdaUpdateWrapper<ActivationCode> updateWrapper = Wrappers
					.lambdaUpdate(ActivationCode.class)
					.set(ActivationCode::getState,
							ActivationCodeDef.State.OVERDUE.getCode())
					.in(ActivationCode::getId, ids);
			repository.update(updateWrapper);
			list = repository
					.selectList(wrapperFunc.apply(ids.get(ids.size() - 1)));
		}
	}

	/**
	 * 激活码客户手机号信息脱敏
	 * 
	 * @param pageRecord
	 */
	private void desensitivity(Page<ActivationCode> pageRecord) {
		pageRecord.getRecords().forEach(activationCode -> {
			CustomerJsonInfo customerInfo = activationCode.getCustomerInfo();
			if (Objects.nonNull(customerInfo)) {
				customerInfo.setMobile(CustomerDef.FILTER_MOBILE_SENSITIVE_INFO
						.apply(customerInfo.getMobile()));
			}
		});
	}
}
