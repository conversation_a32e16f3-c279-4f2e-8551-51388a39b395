package com.zhihaoscm.usercenter.config.security.custom.captcha;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerWx;
import com.zhihaoscm.domain.bean.entity.SecuritySettingDevice;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.MockDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.config.security.custom.ValidatedAuthentication;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.CustomerDisableException;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.CustomerNotFoundException;
import com.zhihaoscm.usercenter.core.service.CustomerEnterpriseService;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.core.service.CustomerWxService;
import com.zhihaoscm.usercenter.core.service.SecuritySettingDeviceService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CaptchaAuthenticationProvider implements AuthenticationProvider {

	private final StringRedisClient redisClient;

	private final CustomerService customerService;

	private final WxMiniProgramProperties properties;

	private final CustomerEnterpriseService customerEnterpriseService;

	private final CustomerWxService customerWxService;

	private final SecuritySettingDeviceService securitySettingDeviceService;

	public CaptchaAuthenticationProvider(StringRedisClient redisClient,
			CustomerService customerService, WxMiniProgramProperties properties,
			CustomerEnterpriseService customerEnterpriseService,
			CustomerWxService customerWxService,
			SecuritySettingDeviceService securitySettingDeviceService) {
		this.redisClient = redisClient;
		this.customerService = customerService;
		this.properties = properties;
		this.customerEnterpriseService = customerEnterpriseService;
		this.customerWxService = customerWxService;
		this.securitySettingDeviceService = securitySettingDeviceService;
	}

	@Override
	public Authentication authenticate(Authentication authentication)
			throws AuthenticationException {
		log.info("custom pc login param -> {}",
				JsonUtils.objectToJson(authentication));
		CaptchaLoginForm loginForm = (CaptchaLoginForm) authentication
				.getPrincipal();
		// 校验登录验证码
		if (!MockDef.isMock.apply(loginForm.getMobile(), "pc:mock",
				redisClient)) {
			// 不在白名单中,校验验证码
			this.checkLoginCaptchaCode(loginForm);
		}
		// 获取用户
		Customer customer = customerService.findByMobile(loginForm.getMobile())
				.orElse(null);
		// 判断其状态是否异常
		if (Objects.isNull(customer)) {
			// 将手机号作为key,临时凭证code作为value存入redis缓存,以便在后续注册时校验,过期时长为2小时
			String key = RedisKeys.Cache.PC_REGISTER_PREFIX
					+ loginForm.getMobile();
			redisClient.setEx(UsernameSalt.encryptUsername(key),
					loginForm.getCaptcha(), properties.getTempVoucherExpire(),
					TimeUnit.SECONDS);
			throw new CustomerNotFoundException(ErrorCode.CODE_20001003,
					loginForm.getMobile());
		} else if (CustomerDef.State.INACTIVE.match(customer.getState())) {
			throw new CustomerDisableException(ErrorCode.CODE_30059008,
					loginForm.getMobile());
		} else if (CustomerDef.State.CANCELLED.match(customer.getState())) {
			throw new CustomerDisableException(ErrorCode.CODE_30059024,
					loginForm.getMobile());
		}

		if (Objects.nonNull(loginForm.getId())) {
			CustomerWx customerWx = customerWxService.findOne(loginForm.getId())
					.orElse(null);

			if (Objects.isNull(customerWx)) {
				throw new CustomerNotFoundException(ErrorCode.CODE_20001003,
						loginForm.getMobile());
			}
			if (StringUtils.isNotBlank(customer.getUnionId())) {
				throw new CustomerDisableException(ErrorCode.CODE_30059011,
						loginForm.getMobile());
			}
			customer.setUnionId(customerWx.getUnionId());
			customerService.update(customer);
			customerWx.setCustomerId(customer.getId());
			customerWx.setBindTime(LocalDateTime.now());
			customerWxService.update(customerWx);
		}

		// 校验是否绑定该设备
		SecuritySettingDevice securitySettingDevice = null;
		if (Objects.nonNull(loginForm.getDeviceCode())){
			securitySettingDevice = securitySettingDeviceService
					.findByCustomerIdAndDeviceCode(customer.getId(),
							loginForm.getDeviceCode())
					.orElse(null);
		}
		if (Objects.nonNull(securitySettingDevice)) {
			securitySettingDevice.setLoginTime(LocalDateTime.now());
			securitySettingDeviceService
					.updateAllProperties(securitySettingDevice);
		} else {
			List<SecuritySettingDevice> securitySettingDeviceList = securitySettingDeviceService
					.findByCustomerIdAndLoginTime(customer.getId());
			if (securitySettingDeviceList.size() >= 10) {
				securitySettingDeviceService
						.delete(securitySettingDeviceList.get(0).getId());
			}
			securitySettingDevice = new SecuritySettingDevice();
			securitySettingDevice.setDeviceCode(loginForm.getDeviceCode());
			securitySettingDevice.setCustomerId(customer.getId());
			securitySettingDevice.setLoginTime(LocalDateTime.now());
			securitySettingDeviceService.create(securitySettingDevice);
		}

		// 组装代理账号信息
		CustomerLoginVo customerLoginVo = new CustomerLoginVo();
		customerLoginVo.setActualAccount(customer);
		customerLoginVo.setRole(loginForm.getAppType());
		customerLoginVo.setProxyAccount(customer);
		// 获取用户上一次登录的代理账号
		String lastLoginKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
				+ loginForm.getMobile();
		String encryptedKey = UsernameSalt.encryptMiniLogin(lastLoginKey);
		String proxyAccountId = redisClient.get(encryptedKey);
		if (StringUtils.isNotBlank(proxyAccountId)) {
			Customer clientById = customerService
					.findById(Long.valueOf(proxyAccountId)).orElse(null);
			if (Objects.nonNull(clientById)) {
				customerLoginVo.setProxyAccount(clientById);
			}
			redisClient.setEx(encryptedKey, proxyAccountId,
					properties.getLoginTokenExpire(), TimeUnit.SECONDS);
		}

		return new ValidatedAuthentication(
				new LoginUser<>(customerLoginVo,
						String.valueOf(loginForm.getAppType()),
						customer.getMobile(),
						List.of()),
				null, null);
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return CpatchaAuthentication.class.isAssignableFrom(authentication);
	}

	/**
	 * 校验登录验证码
	 *
	 * @param loginForm
	 */
	private void checkLoginCaptchaCode(CaptchaLoginForm loginForm) {
		String key = RedisKeys.Cache.PC_LOGIN_MSG_PREFIX
				+ loginForm.getMobile();
		String code = redisClient.get(key);
		if (StringUtils.isBlank(code) || !code.equals(loginForm.getCaptcha())) {
			throw new UsernameNotFoundException(ErrorCode.CODE_30059006);
		}
	}

}
