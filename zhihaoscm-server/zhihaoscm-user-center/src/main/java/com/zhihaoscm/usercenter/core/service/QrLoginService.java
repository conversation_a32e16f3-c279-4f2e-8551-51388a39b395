package com.zhihaoscm.usercenter.core.service;

import java.util.Optional;

import com.zhihaoscm.domain.bean.vo.QrLoginVo;

public interface QrLoginService {

	/**
	 * 生成登录token
	 * 
	 * @return
	 */
	Optional<QrLoginVo> generateLoginCode();

	/**
	 * 扫描
	 *
	 * @param loginCode
	 */
	void scan(String loginCode);

	/**
	 * 确认登录
	 * 
	 * @param loginToken
	 * @param token
	 */
	void confirmLogin(String loginToken, String token);

	/**
	 * 取消登录
	 * 
	 * @param loginCode
	 */
	void cancelLogin(String loginCode);
}
