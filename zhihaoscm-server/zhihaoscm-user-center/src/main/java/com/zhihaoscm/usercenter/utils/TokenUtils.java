package com.zhihaoscm.usercenter.utils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.messaging.support.MessageBuilder;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.mybatis.plus.util.UpdateUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.dto.CustomerStateRefreshDto;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.security.LoginUser;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.TopicDef;
import com.zhihaoscm.usercenter.core.service.CustomerEnterpriseService;
import com.zhihaoscm.usercenter.core.service.CustomerService;

public class TokenUtils {

	private TokenUtils() {
	}

	/**
	 * 刷新用户上下文
	 *
	 * @param old
	 * @param redisClient
	 * @param tokenExpire
	 * @param sendUserChangeNotice
	 *            是否发送用户变更通知
	 */
	public static void refreshCustomerContext(Customer old,
			StringRedisClient redisClient, Long tokenExpire,
			Boolean sendUserChangeNotice) {
		if (Objects.isNull(old)) {
			return;
		}
		CustomerService customerService = SpringUtil
				.getBean(CustomerService.class);
		Customer customer = customerService.findById(old.getId()).orElse(null);
		if (Objects.isNull(customer)) {
			return;
		}
		UpdateUtil.copyNonNullProperties(old, customer);

		Long id = old.getId();
		// 将用户信息存入redis
		String originKey = RedisKeys.Cache.MINI_LOGIN_INFO_PREFIX + id;
		// 加密后的key
		String encryptKey = UsernameSalt.encryptMiniLogin(originKey);
		Set<String> keys = redisClient.keys(encryptKey + "*");
		for (String key : keys) {
			String existLoginUser = redisClient.get(key);
			if (StringUtils.isNotBlank(existLoginUser)) {
				LoginUser<CustomerLoginVo> customerLoginUser = JsonUtils
						.jsonToObject(existLoginUser, new TypeReference<>() {
						});
				// 代理账号和实际账号相同时一起刷新
				LoginUser<CustomerLoginVo> user = new LoginUser<>(
						new CustomerLoginVo(
								customerLoginUser.getUser().getProxyAccount()
										.getId().equals(customer.getId())
												? customer
												: customerLoginUser.getUser()
														.getProxyAccount(),
								customerLoginUser.getUser().getRole(),
								customer),
						String.valueOf(customerLoginUser.getUsername()),
						customer.getMobile(),
						List.of());
				// 用户的信息转换成string
				String userStr = JsonUtils.objectToJson(user);
				// 生成 token
				redisClient.setEx(key, userStr, tokenExpire, TimeUnit.SECONDS);
			}
		}

		if (sendUserChangeNotice) {
			MqUtil mqUtil = SpringUtil.getBean(MqUtil.class);
			CustomerEnterpriseService customerEnterpriseService = SpringUtil
					.getBean(CustomerEnterpriseService.class);
			// 通知客户端刷新客户信息
			mqUtil.asyncSend(MqMessage.builder()
					.topic(TopicDef.CUSTOM_USER_CHANGE)
					.message(MessageBuilder.withPayload(
							JsonUtils.objectToJson(new CustomerStateRefreshDto(
									TopicDef.Type.REFRESH_STATE.getCode(),
									TokenUtils.getNoticeIds(customer.getId(),
											customerEnterpriseService,
											Boolean.TRUE))))
							.build())
					.build());
		}

	}

	/**
	 * 批量刷新用户上下文
	 *
	 * @param customers
	 * @param redisClient
	 * @param tokenExpire
	 */
	public static void batchRefreshCustomerContext(List<Customer> customers,
			StringRedisClient redisClient, Long tokenExpire) {
		if (CollectionUtils.isEmpty(customers)) {
			return;
		}
		customers.forEach(customer -> refreshCustomerContext(customer,
				redisClient, tokenExpire, Boolean.FALSE));
	}

	/**
	 * 删除用户上下文
	 *
	 * @param id
	 * @param redisClient
	 */
	public static void deleteCustomer(Long id, StringRedisClient redisClient) {
		// 将用户信息存入redis
		String originKey = RedisKeys.Cache.MINI_LOGIN_INFO_PREFIX + id;
		// 加密后的key
		String encryptKey = UsernameSalt.encryptMiniLogin(originKey);
		Set<String> keys = redisClient.keys(encryptKey + "*");
		// 删除
		for (String key : keys) {
			redisClient.delete(key);
		}
	}

	/**
	 * 根据主账号ID获取通知ID列表
	 *
	 * @param mainAccountId
	 *            主账号id
	 * @return 通知id列表
	 */
	public static List<Long> getNoticeIds(Long mainAccountId,
			CustomerEnterpriseService customerEnterpriseService,
			Boolean includeYourself) {
		if (Objects.isNull(mainAccountId)) {
			return List.of();
		}
		return customerEnterpriseService.refreshSubAccountContext(mainAccountId,
				includeYourself);
	}

}
