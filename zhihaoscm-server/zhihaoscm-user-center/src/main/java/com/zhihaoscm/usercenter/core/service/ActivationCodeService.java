package com.zhihaoscm.usercenter.core.service;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.ActivationCode;
import com.zhihaoscm.domain.bean.vo.ActivationCodeVo;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
public interface ActivationCodeService
		extends MpLongIdBaseService<ActivationCode> {

	/**
	 * 分页查询激活码列表
	 * 
	 * @param page
	 * @param size
	 * @param searchParam
	 * @param level
	 * @param hasBind
	 * @param state
	 * @return
	 */
	Page<ActivationCode> paging(Integer page, Integer size, String code,
			String searchParam, Integer level, Integer hasBind, Integer state);

	/**
	 * 根据激活码查询
	 * 
	 * @param code
	 * @return
	 */
	Optional<ActivationCode> findByCode(String code);

	/**
	 * 兑换激活码
	 *
	 * @param activationCode
	 * @param customerId
	 * @return
	 */
	Optional<ActivationCodeVo> exchange(ActivationCode activationCode,
			Long customerId);

	/**
	 * 根据客户ID查询激活码列表
	 * 
	 * @param customerId
	 * @return
	 */
	List<ActivationCode> findByCustomerId(Long customerId);

	/**
	 * 标记过期记录
	 */
	void markExpiredRecords();

}
