package com.zhihaoscm.usercenter.config.security.custom.pcwx;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerWx;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.config.security.custom.ValidatedAuthentication;
import com.zhihaoscm.usercenter.config.security.custom.pcwx.exception.CustomerWxDisableException;
import com.zhihaoscm.usercenter.config.security.custom.pcwx.exception.CustomerWxNotBindException;
import com.zhihaoscm.usercenter.config.security.custom.pcwx.exception.CustomerWxTokenNotGetException;
import com.zhihaoscm.usercenter.core.service.CustomerEnterpriseService;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.core.service.CustomerWxService;
import com.zhihaoscm.usercenter.core.service.WxService;
import com.zhihaoscm.wx.response.AccessToken;
import com.zhihaoscm.wx.response.UserInfo;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PcWxAuthenticationProvider implements AuthenticationProvider {

	private final WxService wxService;

	private final CustomerService customerService;

	private final CustomerWxService customerWxService;

	private final WxMiniProgramProperties properties;

	private final StringRedisClient redisClient;

	private final CustomerEnterpriseService customerEnterpriseService;

	public PcWxAuthenticationProvider(StringRedisClient redisClient,
			WxService wxService, CustomerService customerService,
			CustomerWxService customerWxService,
			WxMiniProgramProperties properties,
			CustomerEnterpriseService customerEnterpriseService) {
		this.redisClient = redisClient;
		this.customerService = customerService;
		this.wxService = wxService;
		this.customerWxService = customerWxService;
		this.properties = properties;
		this.customerEnterpriseService = customerEnterpriseService;
	}

	@Override
	public Authentication authenticate(Authentication authentication)
			throws AuthenticationException {
		PcWxLoginForm loginForm = (PcWxLoginForm) authentication.getPrincipal();
		AccessToken accessToken = this.findUserIdByCode(loginForm.getCode());
		// 通过code从微信平台获取unionId
		String unionId = accessToken.getUnionId();
		if (StringUtils.isBlank(unionId)) {
			throw new CustomerWxTokenNotGetException(ErrorCode.CODE_30059002);
		}
		Customer customer = customerService.findByUnionId(unionId).orElse(null);
		if (Objects.isNull(customer)) {
			CustomerWx customerWx = customerWxService.findByUnionIdAndType(
					unionId, CommonDef.Symbol.NO.getCode()).orElse(null);
			// 判断临时表是否存在
			if (Objects.nonNull(customerWx)) {
				throw new CustomerWxNotBindException(ErrorCode.CODE_30059010,
						customerWx.getId(), null,
						loginForm.getUrl());
			}
			// 获取微信用户信息
			if (StringUtils.isNotBlank(accessToken.getAccessToken())
					&& StringUtils.isNotBlank(accessToken.getOpenid())) {
				UserInfo userInfo = wxService
						.findByTokenAndOpenId(accessToken.getAccessToken(),
								accessToken.getOpenid())
						.orElse(null);
				if (Objects.isNull(userInfo)) {
					throw new CustomerWxTokenNotGetException(
							ErrorCode.CODE_30059002);
				}
				customerWx = new CustomerWx();
				customerWx.setUnionId(unionId);
				customerWx.setNickName(userInfo.getNickName());
				customerWx.setType(CommonDef.Symbol.NO.getCode());
				customerWxService.create(customerWx);
				throw new CustomerWxNotBindException(ErrorCode.CODE_30059010,
						customerWx.getId(), null,
						loginForm.getUrl());
			}
		} else if (CustomerDef.State.INACTIVE.match(customer.getState())) {
			throw new CustomerWxDisableException(ErrorCode.CODE_30059008,
					customer.getUnionId());
		}

		// 组装代理账号信息
		CustomerLoginVo customerLoginVo = new CustomerLoginVo();
		customerLoginVo.setActualAccount(customer);
		customerLoginVo.setRole(loginForm.getAppType());
		customerLoginVo.setProxyAccount(customer);
		// 获取用户上一次登录的代理账号
		assert customer != null;
		String lastLoginKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
				+ customer.getMobile();
		String encryptedKey = UsernameSalt.encryptMiniLogin(lastLoginKey);
		String proxyAccountId = redisClient.get(encryptedKey);
		if (StringUtils.isNotBlank(proxyAccountId)) {
			Customer clientById = customerService
					.findById(Long.valueOf(proxyAccountId)).orElse(null);
			if (Objects.nonNull(clientById)) {
				customerLoginVo.setProxyAccount(clientById);
			}
			redisClient.setEx(encryptedKey, proxyAccountId,
					properties.getLoginTokenExpire(), TimeUnit.SECONDS);
		}

		return new ValidatedAuthentication(
				new LoginUser<>(customerLoginVo,
						String.valueOf(loginForm.getAppType()),
						customer.getMobile(),
						List.of()),
				null, loginForm);
	}

	private AccessToken findUserIdByCode(String code) {
		AccessToken accessToken = wxService.findTokenByCode(code).orElse(null);
		assert accessToken != null;
		if (StringUtils.isBlank(accessToken.getAccessToken())) {
			throw new CustomerWxTokenNotGetException(ErrorCode.CODE_30059002);
		}
		return accessToken;
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return (PcWxAuthentication.class.isAssignableFrom(authentication));
	}
}
