package com.zhihaoscm.usercenter.config.security.custom.apple;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UsernameSalt;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerWx;
import com.zhihaoscm.domain.bean.entity.SecuritySettingDevice;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.config.security.custom.ValidatedAuthentication;
import com.zhihaoscm.usercenter.config.security.custom.apple.exception.AppleIdentityTokenErrorException;
import com.zhihaoscm.usercenter.config.security.custom.apple.exception.AppleSubIdDisableException;
import com.zhihaoscm.usercenter.config.security.custom.pcwx.exception.CustomerNotBindException;
import com.zhihaoscm.usercenter.config.security.custom.pcwx.exception.CustomerWxDisableException;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.CustomerDisableException;
import com.zhihaoscm.usercenter.core.service.*;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AppleAuthenticationProvider implements AuthenticationProvider {

	private final AppleAuthenService appleAuthenService;

	private final CustomerService customerService;

	private final StringRedisClient redisClient;

	private final WxMiniProgramProperties properties;

	private final CustomerEnterpriseService customerEnterpriseService;

	private final CustomerWxService customerWxService;

	private final SecuritySettingDeviceService securitySettingDeviceService;

	public AppleAuthenticationProvider(StringRedisClient redisClient,
			AppleAuthenService appleAuthenService,
			WxMiniProgramProperties properties, CustomerService customerService,
			CustomerWxService customerWxService,
			CustomerEnterpriseService customerEnterpriseService,
			SecuritySettingDeviceService securitySettingDeviceService) {
		this.redisClient = redisClient;
		this.appleAuthenService = appleAuthenService;
		this.customerWxService = customerWxService;
		this.customerService = customerService;
		this.properties = properties;
		this.customerEnterpriseService = customerEnterpriseService;
		this.securitySettingDeviceService = securitySettingDeviceService;
	}

	@Override
	public Authentication authenticate(Authentication authentication)
			throws AuthenticationException {
		log.info("apple account login param => {}", authentication);
		AppleLoginForm loginForm = (AppleLoginForm) authentication
				.getPrincipal();
		String identityToken = loginForm.getIdentityToken();
		if (StringUtils.isBlank(identityToken)) {
			throw new AppleIdentityTokenErrorException(ErrorCode.CODE_30059025);
		}
		String subId;
		try {
			Map<String, Object> result = appleAuthenService
					.getAppleUserInfo(identityToken);
			if (Objects.nonNull(result) && Objects.nonNull(result.get("sub"))) {
				subId = result.get("sub").toString();
			} else {
				throw new AppleSubIdDisableException(ErrorCode.CODE_30059026);
			}
		} catch (Exception e) {
			throw new AppleSubIdDisableException(ErrorCode.CODE_30059026);
		}
		if (StringUtils.isBlank(subId)) {
			throw new AppleSubIdDisableException(ErrorCode.CODE_30059026);
		}
		Customer customer = customerService.findBySubId(subId).orElse(null);
		if (Objects.isNull(customer)) {
			CustomerWx customerWx = customerWxService
					.findByUnionIdAndType(subId, CommonDef.Symbol.YES.getCode())
					.orElse(null);
			// 判断临时表是否存在
			if (Objects.nonNull(customerWx)) {
				throw new CustomerNotBindException(ErrorCode.CODE_30059027,
						customerWx.getId());
			}

			customerWx = new CustomerWx();
			customerWx.setUnionId(subId);
			customerWx.setType(CommonDef.Symbol.YES.getCode());
			customerWxService.create(customerWx);
			throw new CustomerNotBindException(ErrorCode.CODE_30059027,
					customerWx.getId());
		} else if (CustomerDef.State.INACTIVE.match(customer.getState())) {
			throw new CustomerWxDisableException(ErrorCode.CODE_30059008,
					customer.getUnionId());
		} else if (CustomerDef.State.CANCELLED.match(customer.getState())) {
			throw new CustomerDisableException(ErrorCode.CODE_30059024,
					customer.getUnionId());
		}

		// 校验是否绑定该设备
		SecuritySettingDevice securitySettingDevice = null;
		if (Objects.nonNull(loginForm.getDeviceCode())){
			securitySettingDevice = securitySettingDeviceService
					.findByCustomerIdAndDeviceCode(customer.getId(),
							loginForm.getDeviceCode())
					.orElse(null);
		}
		if (Objects.nonNull(securitySettingDevice)) {
			securitySettingDevice.setLoginTime(LocalDateTime.now());
			securitySettingDeviceService
					.updateAllProperties(securitySettingDevice);
		} else {
			List<SecuritySettingDevice> securitySettingDeviceList = securitySettingDeviceService
					.findByCustomerIdAndLoginTime(customer.getId());
			if (securitySettingDeviceList.size() >= 10) {
				securitySettingDeviceService
						.delete(securitySettingDeviceList.get(0).getId());
			}
			securitySettingDevice = new SecuritySettingDevice();
			securitySettingDevice.setDeviceCode(loginForm.getDeviceCode());
			securitySettingDevice.setCustomerId(customer.getId());
			securitySettingDevice.setLoginTime(LocalDateTime.now());
			securitySettingDeviceService.create(securitySettingDevice);
		}

		// 组装代理账号信息
		CustomerLoginVo customerLoginVo = new CustomerLoginVo();
		customerLoginVo.setActualAccount(customer);
		customerLoginVo.setRole(loginForm.getAppType());
		customerLoginVo.setProxyAccount(customer);
		// 获取用户上一次登录的代理账号
		String lastLoginKey = RedisKeys.Cache.MINI_LAST_LOGIN_PREFIX
				+ customer.getMobile();
		String encryptedKey = UsernameSalt.encryptMiniLogin(lastLoginKey);
		String proxyAccountId = redisClient.get(encryptedKey);
		if (StringUtils.isNotBlank(proxyAccountId)) {
			Customer clientById = customerService
					.findById(Long.valueOf(proxyAccountId)).orElse(null);
			if (Objects.nonNull(clientById)) {
				customerLoginVo.setProxyAccount(clientById);
			}
			redisClient.setEx(encryptedKey, proxyAccountId,
					properties.getLoginTokenExpire(), TimeUnit.SECONDS);
		}

		return new ValidatedAuthentication(
				new LoginUser<>(customerLoginVo,
						String.valueOf(loginForm.getAppType()),
						customer.getMobile(),
						List.of()),
				null, loginForm);
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return (AppleAuthentication.class.isAssignableFrom(authentication));
	}
}
