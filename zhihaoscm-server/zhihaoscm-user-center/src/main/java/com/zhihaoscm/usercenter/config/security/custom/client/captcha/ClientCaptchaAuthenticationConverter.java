package com.zhihaoscm.usercenter.config.security.custom.client.captcha;

import java.util.Objects;
import java.util.Optional;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationConverter;

import com.zhihaoscm.common.api.util.CommonWebUtils;
import com.zhihaoscm.common.util.utils.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

public class ClientCaptchaAuthenticationConverter
		implements AuthenticationConverter {

	@Override
	public Authentication convert(HttpServletRequest request) {
		Optional<ClientCaptchaLoginForm> login = CommonWebUtils
				.getRequestBody(request, ClientCaptchaLoginForm.class);

		if (login.isEmpty()) {
			throw new BadCredentialsException(
					"missing moble or role or cpatcha or origin");
		}

		ClientCaptchaLoginForm loginForm = login.get();

		if (StringUtils.isBlank(loginForm.getMobile())) {
			throw new BadCredentialsException("missing mobile");
		}
		if (StringUtils.isBlank(loginForm.getCaptcha())) {
			throw new BadCredentialsException("missing captcha");
		}
		if (Objects.isNull(loginForm.getAppType())) {
			throw new BadCredentialsException("missing appType");
		}
		if (Objects.isNull(loginForm.getOrigin())) {
			throw new BadCredentialsException("missing origin");
		}
		return new ClientCaptchaAuthentication(login.get());
	}

}
