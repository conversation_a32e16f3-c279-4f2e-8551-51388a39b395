package com.zhihaoscm.usercenter.resource.common;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.mybatis.plus.resource.MpLongIdBaseResource;
import com.zhihaoscm.domain.bean.entity.CustomerReceivingAddress;
import com.zhihaoscm.usercenter.core.service.CustomerReceivingAddressService;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "客户地址", description = "客户地址API")
@RestController
@RequestMapping(value = "/customer-receiving-address-rest")
public class CustomerReceivingAddressResource extends
		MpLongIdBaseResource<CustomerReceivingAddress, CustomerReceivingAddressService> {

	public CustomerReceivingAddressResource(
			CustomerReceivingAddressService service) {
		super(service);
	}
}
