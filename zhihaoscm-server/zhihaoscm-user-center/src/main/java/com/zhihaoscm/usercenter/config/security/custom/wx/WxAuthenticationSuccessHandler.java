package com.zhihaoscm.usercenter.config.security.custom.wx;

import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.util.CommonWebUtils;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.security.exception.UnSupportedPrincipalException;
import com.zhihaoscm.common.security.token.TokenService;
import com.zhihaoscm.domain.meta.biz.AppTypeDef;
import com.zhihaoscm.domain.meta.biz.CertificationDef;
import com.zhihaoscm.usercenter.config.security.custom.TokenResponse;

import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WxAuthenticationSuccessHandler
		implements AuthenticationSuccessHandler {

	private final TokenService<?> tokenService;

	public WxAuthenticationSuccessHandler(TokenService<?> tokenService) {
		this.tokenService = tokenService;
	}

	@Override
	public void onAuthenticationSuccess(HttpServletRequest request,
			HttpServletResponse response, FilterChain chain,
			Authentication authentication) {
		onAuthenticationSuccess(request, response, authentication);
	}

	@Override
	public void onAuthenticationSuccess(HttpServletRequest request,
			HttpServletResponse response, Authentication authentication) {
		try {
			Object principal = authentication.getPrincipal();
			if (principal instanceof LoginUser user) {
				String token;
				if (AppTypeDef.AppType.CHUAN_WU
						.match(Integer.valueOf(user.getUsername()))) {
					token = tokenService.createAccessToken(user,
							CertificationDef.Origin.MINI_APP_SHIP.getCode());
				} else {
					token = tokenService.createAccessToken(user,
							CertificationDef.Origin.MINI_APP.getCode());
				}
				CommonWebUtils.renderJson(response,
						new ApiResponse<>(new TokenResponse(token,
								user.getPassword(), null)),
						HttpStatus.OK.value());
			} else {
				throw new UnSupportedPrincipalException(
						WxAuthenticationSuccessHandler.class.getSimpleName()
								+ " is not supported this principal type:"
								+ principal.getClass().getSimpleName());
			}
		} catch (Exception e) {
			if (log.isDebugEnabled()) {
				log.debug("", e);
			}

			throw new InternalAuthenticationServiceException(
					"failure to create token");
		}
	}

}
