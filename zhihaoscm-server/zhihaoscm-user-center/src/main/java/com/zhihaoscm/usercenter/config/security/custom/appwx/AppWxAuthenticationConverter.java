package com.zhihaoscm.usercenter.config.security.custom.appwx;

import java.util.Objects;
import java.util.Optional;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationConverter;

import com.zhihaoscm.common.api.util.CommonWebUtils;
import com.zhihaoscm.common.util.utils.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

public class AppWxAuthenticationConverter implements AuthenticationConverter {

	@Override
	public Authentication convert(HttpServletRequest request) {

		Optional<AppWxLoginForm> login = CommonWebUtils.getRequestBody(request,
				AppWxLoginForm.class);

		if (login.isEmpty()) {
			throw new BadCredentialsException("missing code or role ");
		}

		AppWxLoginForm loginForm = login.get();

		if (StringUtils.isBlank(loginForm.getCode())) {
			throw new BadCredentialsException("missing code");
		}
		if (Objects.isNull(loginForm.getAppType())) {
			throw new BadCredentialsException("missing appType");
		}
		return new AppWxAuthentication(login.get());
	}

}
