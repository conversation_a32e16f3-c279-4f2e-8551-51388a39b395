package com.zhihaoscm.usercenter.resource.form.institution.apply;

import org.hibernate.validator.constraints.Length;

import com.zhihaoscm.domain.bean.entity.InstitutionApply;
import com.zhihaoscm.domain.meta.biz.InstitutionApplyDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.security.custom.CustomerContextHolder;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Schema(name = "InstitutionApplyForm", description = "机构认证表单")
@Data
public class InstitutionApplyForm {

	@Schema(title = "组织机构名称")
	@NotBlank(message = ErrorCode.CODE_30022006)
	@Length(max = 40, message = ErrorCode.CODE_30022007)
	private String institutionName;

	@Schema(title = "统一社会信用代码")
	@NotBlank(message = ErrorCode.CODE_30022008)
	@Pattern(regexp = "^[a-zA-Z0-9]{18}$", message = ErrorCode.CODE_30022009)
	private String unifiedSocialCreditCode;

	@Schema(title = "营业执照文件id")
	@NotNull(message = ErrorCode.CODE_30022005)
	private Long businessLicenseFileId;

	@Schema(title = "授权委托书文件id")
	@NotNull(message = ErrorCode.CODE_30022022)
	private Long powerAttorneyFileId;

	@Schema(title = "法定代表人")
	@NotBlank(message = ErrorCode.CODE_30022010)
	@Length(max = 10, message = ErrorCode.CODE_30022011)
	private String legalRepresentative;

	public InstitutionApply convertToEntity() {
		InstitutionApply institutionApply = new InstitutionApply();
		institutionApply.setInstitutionName(this.institutionName);
		institutionApply
				.setUnifiedSocialCreditCode(this.unifiedSocialCreditCode);
		institutionApply.setBusinessLicenseFileId(this.businessLicenseFileId);
		institutionApply.setPowerAttorneyFileId(this.powerAttorneyFileId);
		institutionApply.setLegalRepresentative(this.legalRepresentative);
		institutionApply
				.setState(InstitutionApplyDef.State.PENDING_APPROVE.getCode());
		institutionApply.setCustomerId(CustomerContextHolder
				.getCustomerLoginVo().getActualAccount().getId());
		return institutionApply;
	}

	public InstitutionApply convertToEntity(InstitutionApply institutionApply) {
		institutionApply.setInstitutionName(this.institutionName);
		institutionApply
				.setUnifiedSocialCreditCode(this.unifiedSocialCreditCode);
		institutionApply.setBusinessLicenseFileId(this.businessLicenseFileId);
		institutionApply.setPowerAttorneyFileId(this.powerAttorneyFileId);
		institutionApply.setLegalRepresentative(this.legalRepresentative);
		institutionApply
				.setState(InstitutionApplyDef.State.PENDING_APPROVE.getCode());
		institutionApply.setApproveTime(null);
		institutionApply.setApproveBy(null);
		institutionApply.setApproveName(null);
		institutionApply.setRemark(null);
		return institutionApply;
	}
}
