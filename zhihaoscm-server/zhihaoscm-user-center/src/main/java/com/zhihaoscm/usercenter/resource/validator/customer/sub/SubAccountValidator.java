package com.zhihaoscm.usercenter.resource.validator.customer.sub;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerEnterprise;
import com.zhihaoscm.domain.bean.entity.MembershipLevel;
import com.zhihaoscm.domain.meta.biz.CustomerEnterpriseDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.usercenter.core.service.CustomerEnterpriseService;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.core.service.CustomerServiceExt;
import com.zhihaoscm.usercenter.resource.form.customer.sub.SubAccountForm;
import com.zhihaoscm.usercenter.resource.form.customer.sub.SubAccountUpdateForm;

@Component
public class SubAccountValidator {

	@Autowired
	private CustomerEnterpriseService customerEnterpriseService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private CustomerServiceExt customerServiceExt;

	/**
	 * 校验新增
	 *
	 * @param form
	 *            表单对象
	 */
	public Long validateCreate(SubAccountForm form) {
		// 校验主账号是否已完成企业认证
		Customer mainAccount = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount();
		Assert.state(StringUtils.isNotBlank(mainAccount.getInstitutionName()),
				ErrorCode.CODE_30055003);
		// 校验操作人是否为当前记录主账号
		this.validateOperatorId(mainAccount.getId());
		// 校验主账号是否已经拥有指定数量的子账号
		List<CustomerEnterprise> hasSubAccount = customerEnterpriseService
				.findByMainId(mainAccount.getId(), null);
		if (CollectionUtils.isNotEmpty(hasSubAccount)) {
			hasSubAccount = hasSubAccount.stream().filter(
					customerEnterprise -> !CustomerEnterpriseDef.State.REJECTED
							.getCode().equals(customerEnterprise.getState()))
					.toList();
		}
		MembershipLevel membershipLevel = customerServiceExt
				.getMembershipLevel(Objects.requireNonNull(customerService
						.findById(mainAccount.getId()).orElse(null)));
		Assert.state(membershipLevel.getFeature().getSubAccountNum() > 0,
				ErrorCode.CODE_30055020);
		Assert.state(hasSubAccount.size() < 100, ErrorCode.CODE_30055018);
		Assert.state(hasSubAccount.size() < membershipLevel.getFeature()
				.getSubAccountNum(), ErrorCode.CODE_30055019);
		// 校验邀请加入的子账号是否是自己
		Assert.state(!Objects.equals(mainAccount.getMobile(), form.getMobile()),
				ErrorCode.CODE_30055004);
		// 校验子账号加入企业数量是否已超上限
		Customer subAccount = customerService.findByMobile(form.getMobile())
				.orElse(null);
		if (Objects.nonNull(subAccount)) {
			// 校验是否已邀请过该子账号
			List<CustomerEnterprise> hasSubAccounts = customerEnterpriseService
					.findSubByMainId(mainAccount.getId(), null);
			boolean match = hasSubAccounts.stream()
					.filter(ce -> CustomerEnterpriseDef.State.CONFIRMED
							.match(ce.getState())
							|| CustomerEnterpriseDef.State.NOT_CONFIRMED
									.match(ce.getState()))
					.anyMatch(ce -> Objects.equals(ce.getSubAccountId(),
							subAccount.getId()));
			Assert.state(!match, ErrorCode.CODE_30055006);
		}
		return Objects.isNull(subAccount) ? null : subAccount.getId();
	}

	/**
	 * 校验更新
	 *
	 * @param id
	 *            主键id
	 * @param form
	 *            变淡对象
	 */
	public CustomerEnterprise validateUpdate(Long id,
			SubAccountUpdateForm form) {
		CustomerEnterprise customerEnterprise = this.validateExist(id);
		// 校验操作人是否为当前记录主账号
		this.validateOperatorId(customerEnterprise.getMainAccountId());
		// 激活前校验子账号是否超限
		if (CommonDef.Symbol.YES.match(form.getAccountState())) {
			this.validateSubAccountNum();
		}
		customerEnterprise.setAccountState(form.getAccountState());
		customerEnterprise.setReceiptSms(form.getReceiptSms());
		return customerEnterprise;
	}

	/**
	 * 校验是否存在
	 *
	 * @param id
	 *            主键id
	 * @return 实体
	 */
	public CustomerEnterprise validateExist(Long id) {
		CustomerEnterprise customerEnterprise = customerEnterpriseService
				.findOne(id).orElse(null);
		Assert.notNull(customerEnterprise, ErrorCode.CODE_30055007);
		return customerEnterprise;
	}

	/**
	 * 校验确认或拒绝
	 *
	 * @param id
	 *            主键id
	 * @param state
	 *            状态
	 */
	public void validateConfirm(Long id, Integer state) {
		// 校验状态是否合法
		CustomerEnterpriseDef.State.from(state);
		// 校验邀请记录是否存在
		CustomerEnterprise customerEnterprise = this.validateExist(id);
		if (!customerEnterprise.getSubAccountId().equals(CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId())) {
			throw new BadRequestException(ErrorCode.CODE_30055024);
		}
		// 如果是确认状态则判断当前受邀人已加入企业的数量是否超过上限
		if (CustomerEnterpriseDef.State.CONFIRMED.match(state)) {
			List<CustomerEnterprise> invitedRecords = customerEnterpriseService
					.findByMainId(customerEnterprise.getMainAccountId(),
							CustomerEnterpriseDef.State.CONFIRMED.getCode());

			Customer mainAccount = customerService
					.findById(customerEnterprise.getMainAccountId())
					.orElse(null);
			if (Objects.isNull(mainAccount)
					|| StringUtils.isBlank(mainAccount.getRealName())) {
				throw new BadRequestException(ErrorCode.CODE_30053002);
			}
			MembershipLevel membershipLevel = customerServiceExt
					.getMembershipLevel(mainAccount);
			Assert.state(membershipLevel.getFeature().getSubAccountNum() > 0,
					ErrorCode.CODE_30055022);
			Assert.state(
					CollectionUtils.isEmpty(invitedRecords)
							|| invitedRecords.size() < membershipLevel
									.getFeature().getSubAccountNum(),
					ErrorCode.CODE_30055008);
		}
	}

	/**
	 * 校验删除
	 *
	 * @param id
	 *            关联关系id
	 * @param operationType
	 *            操作类型
	 */
	public void validateDelete(Long id, Integer operationType) {
		// 校验关联关系是否存在
		CustomerEnterprise customerEnterprise = this.validateExist(id);
		// 校验操作类型是否合法
		CustomerEnterpriseDef.OperationType.from(operationType);
		// 判断操作类型的业务边界
		if (CustomerEnterpriseDef.OperationType.DELETE_RECORD
				.match(operationType)) {
			Assert.state(
					!CustomerEnterpriseDef.State.CONFIRMED
							.match(customerEnterprise.getState()),
					ErrorCode.CODE_30055010);
		} else {
			Assert.state(
					CustomerEnterpriseDef.State.CONFIRMED
							.match(customerEnterprise.getState()),
					ErrorCode.CODE_30055012);
		}
		this.validateOperatorId(customerEnterprise.getMainAccountId());
	}

	/**
	 * 校验操作人是否为当前记录主账号
	 *
	 * @param mainAccountId
	 *            主账号id
	 */
	private void validateOperatorId(Long mainAccountId) {
		// 校验操作是否合法
		Long actualId = CustomerContextHolder.getCustomerLoginVo()
				.getActualAccount().getId();
		Assert.state(Objects.equals(actualId, mainAccountId),
				ErrorCode.CODE_30055011);
	}

	/**
	 * 激活子账号前校验子账号是否超限
	 */
	private void validateSubAccountNum() {
		Long mainId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		// 校验主账号是否已经拥有指定数量的子账号
		List<CustomerEnterprise> hasSubAccount = customerEnterpriseService
				.findByMainId(mainId, null);
		if (CollectionUtils.isNotEmpty(hasSubAccount)) {
			hasSubAccount = hasSubAccount.stream().filter(
					customerEnterprise -> !CustomerEnterpriseDef.State.REJECTED
							.getCode().equals(customerEnterprise.getState()))
					.toList();
		}
		MembershipLevel membershipLevel = customerServiceExt
				.getMembershipLevel(Objects.requireNonNull(
						customerService.findById(mainId).orElse(null)));
		Assert.state(membershipLevel.getFeature().getSubAccountNum() > 0,
				ErrorCode.CODE_30055020);
		Assert.state(
				CollectionUtils.size(hasSubAccount) <= membershipLevel
						.getFeature().getSubAccountNum(),
				ErrorCode.CODE_30055021);
	}

}
