package com.zhihaoscm.usercenter.config.security.custom.apple;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppleLoginForm {

	/**
	 * 苹果登录凭证
	 */
	private String identityToken;

	/**
	 * 客户登录身份
	 */
	private Integer appType;

	/**
	 * 设备编号
	 */
	private String deviceCode;
}
