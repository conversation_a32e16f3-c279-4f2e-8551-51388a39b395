package com.zhihaoscm.usercenter.resource.validator.customer.certification;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.domain.bean.dto.PersonalAuthDto;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerChangeMobile;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.properties.ContractLockProperties;
import com.zhihaoscm.usercenter.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.usercenter.core.service.CustomerChangeMobileService;
import com.zhihaoscm.usercenter.resource.form.customer.certification.CustomerPersonalAuthForm;
import com.zhihaoscm.usercenter.resource.validator.customer.CustomerCustomValidator;

@Component
public class CertificationValidator {

	@Autowired
	private CustomerCustomValidator customerValidator;
	@Autowired
	private CustomerChangeMobileService customerChangeMobileService;

	/**
	 * 校验换绑手机号-获取客户个人认证链接
	 */
	public PersonalAuthDto validateCmPersonalAuth(CustomerPersonalAuthForm form,
			ContractLockProperties properties) {
		// 校验客户是否存在
		Customer customer = customerValidator
				.validateExist(CustomerContextHolder.getCustomerLoginVo()
						.getActualAccount().getId());
		// 校验是否在换绑手机号流程中
		CustomerChangeMobile customerChangeMobile = customerChangeMobileService
				.findByCustomerId(customer.getId()).orElse(null);
		if (Objects.isNull(customerChangeMobile)) {
			throw new BadRequestException(ErrorCode.CODE_30056006);
		}
		return form.cmConvertToDto(properties, customerChangeMobile);
	}
}
