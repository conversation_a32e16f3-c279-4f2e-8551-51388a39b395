package com.zhihaoscm.usercenter.config.security.custom;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.ExceptionTranslationFilter;
import org.springframework.security.web.access.intercept.AuthorizationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.sdk.wx.miniapp.api.login.LoginClient;
import com.zhihaoscm.common.sdk.wx.miniapp.api.phone.PhonenumberClient;
import com.zhihaoscm.common.sdk.wx.miniapp.api.pz.AccessTokenClient;
import com.zhihaoscm.common.security.authentication.LogoutSuccessHandlerImpl;
import com.zhihaoscm.common.security.authentication.parse.token.TokenAuthenticationProvider;
import com.zhihaoscm.common.security.authorization.MethodSecurityConfig;
import com.zhihaoscm.common.security.exception.handler.AccessDeniedHandlerImpl;
import com.zhihaoscm.usercenter.config.properties.ApplicationProperties;
import com.zhihaoscm.usercenter.config.properties.WxMiniProgramProperties;
import com.zhihaoscm.usercenter.config.security.custom.account.PcAccountAuthenticationConverter;
import com.zhihaoscm.usercenter.config.security.custom.account.PcAccountAuthenticationFilter;
import com.zhihaoscm.usercenter.config.security.custom.account.PcAccountAuthenticationProvider;
import com.zhihaoscm.usercenter.config.security.custom.account.PcAccountAuthenticationSuccessHandler;
import com.zhihaoscm.usercenter.config.security.custom.apple.AppleAuthenticationConverter;
import com.zhihaoscm.usercenter.config.security.custom.apple.AppleAuthenticationFilter;
import com.zhihaoscm.usercenter.config.security.custom.apple.AppleAuthenticationProvider;
import com.zhihaoscm.usercenter.config.security.custom.apple.AppleAuthenticationSuccessHandler;
import com.zhihaoscm.usercenter.config.security.custom.appwx.AppWxAuthenticationConverter;
import com.zhihaoscm.usercenter.config.security.custom.appwx.AppWxAuthenticationFilter;
import com.zhihaoscm.usercenter.config.security.custom.appwx.AppWxAuthenticationProvider;
import com.zhihaoscm.usercenter.config.security.custom.appwx.AppWxAuthenticationSuccessHandler;
import com.zhihaoscm.usercenter.config.security.custom.captcha.CaptchaAuthenticationConverter;
import com.zhihaoscm.usercenter.config.security.custom.captcha.CaptchaAuthenticationFilter;
import com.zhihaoscm.usercenter.config.security.custom.captcha.CaptchaAuthenticationProvider;
import com.zhihaoscm.usercenter.config.security.custom.captcha.PcAuthenticationSuccessHandler;
import com.zhihaoscm.usercenter.config.security.custom.client.account.ClientAccountAuthenticationConverter;
import com.zhihaoscm.usercenter.config.security.custom.client.account.ClientAccountAuthenticationFilter;
import com.zhihaoscm.usercenter.config.security.custom.client.account.ClientAccountAuthenticationProvider;
import com.zhihaoscm.usercenter.config.security.custom.client.account.ClientAccountAuthenticationSuccessHandler;
import com.zhihaoscm.usercenter.config.security.custom.client.captcha.ClientCaptchaAuthenticationConverter;
import com.zhihaoscm.usercenter.config.security.custom.client.captcha.ClientCaptchaAuthenticationFilter;
import com.zhihaoscm.usercenter.config.security.custom.client.captcha.ClientCaptchaAuthenticationProvider;
import com.zhihaoscm.usercenter.config.security.custom.client.captcha.ClientCaptchaAuthenticationSuccessHandler;
import com.zhihaoscm.usercenter.config.security.custom.client.mobile.ClientMobileAuthenticationConverter;
import com.zhihaoscm.usercenter.config.security.custom.client.mobile.ClientMobileAuthenticationFilter;
import com.zhihaoscm.usercenter.config.security.custom.client.mobile.ClientMobileAuthenticationProvider;
import com.zhihaoscm.usercenter.config.security.custom.client.mobile.ClientMobileAuthenticationSuccessHandler;
import com.zhihaoscm.usercenter.config.security.custom.pcwx.PcWxAuthenticationConverter;
import com.zhihaoscm.usercenter.config.security.custom.pcwx.PcWxAuthenticationFilter;
import com.zhihaoscm.usercenter.config.security.custom.pcwx.PcWxAuthenticationProvider;
import com.zhihaoscm.usercenter.config.security.custom.pcwx.PcWxAuthenticationSuccessHandler;
import com.zhihaoscm.usercenter.config.security.custom.wx.*;
import com.zhihaoscm.usercenter.config.security.filter.ParseTokenAuthenticationFilter;
import com.zhihaoscm.usercenter.core.service.*;

import jakarta.servlet.Filter;

@EnableWebSecurity
@Import(MethodSecurityConfig.class)
@Configuration
public class CustomWebSecurityConfiguration {

	private final String[] WHITE_URL_LIST = { "/custom/link/**",
			"/custom/v3/api-docs/**", "/custom/file/**",
			"/custom/information/**", "/custom/callback/**",
			"/custom/assistants-plugin-callback/**",
			"/custom/product-type-index/**", "/custom/shipping/price/index/**",
			"/custom/customer/supplier-selector", "/custom/customer/password",
			"/custom/customer/client/password", "/custom/product/**",
			"/custom/product-type/**", "/custom/read-quantity/**",
			"/custom/banner/findList", "/custom/article/find",
			"/custom/purchase-demand/hall", "/custom/purchase-demand/vo/*",
			"/custom/enterprise-custom/find/**",
			"/custom/enterprise-style-custom/find/**",
			"/custom/enterprise-custom/find/withoutRole/**",
			"/custom/article/findByTenantIdAndTypeAndApp",
			"/custom/file/withoutRole/ids",
			"/custom/shipping/plat/custom/paging", "/custom/msg/send/login",
			"/custom/msg/send/captcha", "/custom/msg/check/captcha",
			"/custom/captcha/send/graph", "/custom/captcha/check/graph",
			"/custom/advertisement-position/**", "/custom/composite/index/line",
			"/custom/payment/wxpay/callback", "/custom/payment/alipay/callback",
			"/custom/captcha/send", "/custom/captcha/graph",
			"/custom/information/download-cover/**",
			"/custom/project/channel/paging", "/custom/project/vo/*",
			"/custom/wechat/official/signa",
			"/custom/shipping-composite-index/**",
			"/custom/composite/index/**","/custom/qr-login/generate" };
	@Autowired
	private CustomerService customerService;
	@Autowired
	private WxService wxService;
	@Autowired
	private StringRedisClient stringRedisClient;
	@Autowired
	private ApplicationProperties applicationProperties;
	@Autowired
	private WxMiniProgramProperties properties;
	@Autowired
	private AccessTokenClient accessTokenClient;
	@Autowired
	private PhonenumberClient phoneClient;
	@Autowired
	private PromotionService promotionService;
	@Autowired
	private ActivationCodeService activationCodeService;
	@Autowired
	private PromotionDetailService promotionDetailService;
	@Autowired
	private LoginClient loginClient;
	@Autowired
	private CustomerEnterpriseService customerEnterpriseService;
	@Autowired
	private BusinessConfigService businessConfigService;
	@Autowired
	private CustomerWxService customerWxService;
	@Autowired
	private CustomerChangeMobileService customerChangeMobileService;
	@Autowired
	private SecuritySettingDeviceService securitySettingDeviceService;
	@Autowired
	private AppleAuthenService appleAuthenService;
	@Autowired
	private PhoneAuthService phoneAuthService;

	private RedisJwtTokenService tokenService() {
		RedisJwtTokenService tokenService = new RedisJwtTokenService(
				stringRedisClient);
		tokenService.setTokenExpire(properties.getLoginTokenExpire());
		tokenService.setJwtSecret(applicationProperties.getJwtSecret());
		return tokenService;
	}

	private WxAuthenticationProvider wxAuthenticationProvider() {
		return new WxAuthenticationProvider(stringRedisClient, customerService,
				properties, accessTokenClient, phoneClient, loginClient,
				customerEnterpriseService);
	}

	private PcWxAuthenticationProvider pcWxAuthenticationProvider() {
		return new PcWxAuthenticationProvider(stringRedisClient, wxService,
				customerService, customerWxService, properties,
				customerEnterpriseService);
	}

	private AppWxAuthenticationProvider appWxAuthenticationProvider() {
		return new AppWxAuthenticationProvider(stringRedisClient, wxService,
				customerService, customerWxService, properties,
				customerEnterpriseService, securitySettingDeviceService);
	}

	private PcAccountAuthenticationProvider pcAccountAuthenticationProvider() {
		return new PcAccountAuthenticationProvider(stringRedisClient,
				customerService, properties, customerEnterpriseService,
				securitySettingDeviceService);
	}

	private CaptchaAuthenticationProvider captchaAuthenticationProvider() {
		return new CaptchaAuthenticationProvider(stringRedisClient,
				customerService, properties, customerEnterpriseService,
				customerWxService, securitySettingDeviceService);
	}

	private ClientCaptchaAuthenticationProvider clientCaptchaAuthenticationProvider() {
		return new ClientCaptchaAuthenticationProvider(stringRedisClient,
				customerService, properties, customerEnterpriseService,
				securitySettingDeviceService, customerWxService);
	}

	private ClientAccountAuthenticationProvider clientAccountAuthenticationProvider() {
		return new ClientAccountAuthenticationProvider(stringRedisClient,
				customerService, properties, customerEnterpriseService,
				securitySettingDeviceService);
	}

	private ClientMobileAuthenticationProvider clientMobileAuthenticationProvider() {
		return new ClientMobileAuthenticationProvider(stringRedisClient,
				customerService, properties, customerEnterpriseService,
				phoneAuthService, securitySettingDeviceService);
	}

	private TokenAuthenticationProvider tokenAuthenticationProvider() {
		return new TokenAuthenticationProvider(tokenService());
	}

	private AppleAuthenticationProvider appleAuthenticationProvider() {
		return new AppleAuthenticationProvider(stringRedisClient,
				appleAuthenService, properties, customerService,
				customerWxService, customerEnterpriseService,
				securitySettingDeviceService);
	}

	private ProviderManager providerManager() {
		return new ProviderManager(wxAuthenticationProvider(),
				pcWxAuthenticationProvider(), appWxAuthenticationProvider(),
				pcAccountAuthenticationProvider(),
				tokenAuthenticationProvider(), captchaAuthenticationProvider(),
				clientCaptchaAuthenticationProvider(),
				clientAccountAuthenticationProvider(),
				clientMobileAuthenticationProvider(),
				appleAuthenticationProvider());
	}

	private WxAuthenticationFilter wxAuthenticationFilter() {
		return new WxAuthenticationFilter(providerManager(),
				new WxAuthenticationConverter(),
				new CustomAuthenticationFailureHandler(applicationProperties),
				new WxAuthenticationSuccessHandler(tokenService()));
	}

	private PcWxAuthenticationFilter pcWxAuthenticationFilter() {
		return new PcWxAuthenticationFilter(providerManager(),
				new PcWxAuthenticationConverter(),
				new CustomAuthenticationFailureHandler(applicationProperties),
				new PcWxAuthenticationSuccessHandler(tokenService()));
	}

	private AppWxAuthenticationFilter appWxAuthenticationFilter() {
		return new AppWxAuthenticationFilter(providerManager(),
				new AppWxAuthenticationConverter(),
				new CustomAuthenticationFailureHandler(applicationProperties),
				new AppWxAuthenticationSuccessHandler(tokenService()));
	}

	private PcAccountAuthenticationFilter pcAccountAuthenticationFilter() {
		return new PcAccountAuthenticationFilter(providerManager(),
				new PcAccountAuthenticationConverter(),
				new CustomAuthenticationFailureHandler(applicationProperties),
				new PcAccountAuthenticationSuccessHandler(tokenService()));
	}

	private CaptchaAuthenticationFilter captchaAuthenticationFilter() {
		return new CaptchaAuthenticationFilter(providerManager(),
				new CaptchaAuthenticationConverter(),
				new CustomAuthenticationFailureHandler(applicationProperties),
				new PcAuthenticationSuccessHandler(tokenService()));
	}

	private ClientAccountAuthenticationFilter clientAccountAuthenticationFilter() {
		return new ClientAccountAuthenticationFilter(providerManager(),
				new ClientAccountAuthenticationConverter(),
				new CustomAuthenticationFailureHandler(applicationProperties),
				new ClientAccountAuthenticationSuccessHandler(tokenService()));
	}

	private ClientCaptchaAuthenticationFilter clientCaptchaAuthenticationFilter() {
		return new ClientCaptchaAuthenticationFilter(providerManager(),
				new ClientCaptchaAuthenticationConverter(),
				new CustomAuthenticationFailureHandler(applicationProperties),
				new ClientCaptchaAuthenticationSuccessHandler(tokenService()));
	}

	private AppleAuthenticationFilter appleAuthenticationFilter() {
		return new AppleAuthenticationFilter(providerManager(),
				new AppleAuthenticationConverter(),
				new CustomAuthenticationFailureHandler(applicationProperties),
				new AppleAuthenticationSuccessHandler(tokenService()));
	}

	private ClientMobileAuthenticationFilter clientMobileAuthenticationFilter() {
		return new ClientMobileAuthenticationFilter(providerManager(),
				new ClientMobileAuthenticationConverter(),
				new CustomAuthenticationFailureHandler(applicationProperties),
				new ClientMobileAuthenticationSuccessHandler(tokenService()));
	}

	private WxCreateFilter wxCreateFilter() {
		return new WxCreateFilter(stringRedisClient, customerService,
				new AntPathRequestMatcher("/custom/create", "POST"),
				tokenService(), promotionService, activationCodeService,
				promotionDetailService, businessConfigService,
				customerWxService, customerChangeMobileService,
				securitySettingDeviceService);
	}

	/**
	 * 解析 TOKEN 过滤器
	 */
	private ParseTokenAuthenticationFilter parseTokenAuthenticationFilter() {
		return new ParseTokenAuthenticationFilter(providerManager(),
				WHITE_URL_LIST);
	}

	private LogoutSuccessHandlerImpl logoutSuccessHandler() {
		return new LogoutSuccessHandlerImpl(tokenService());
	}

	private Filter userContextFilter() {
		return new CustomerContextFilter();
	}

	@Bean
	public SecurityFilterChain customFilterChain(HttpSecurity http)
			throws Exception {
		http.securityMatcher("/custom/**")
				.addFilterBefore(wxAuthenticationFilter(),
						ExceptionTranslationFilter.class)
				.addFilterBefore(pcWxAuthenticationFilter(),
						ExceptionTranslationFilter.class)
				.addFilterBefore(appWxAuthenticationFilter(),
						ExceptionTranslationFilter.class)
				.addFilterBefore(pcAccountAuthenticationFilter(),
						ExceptionTranslationFilter.class)
				.addFilterBefore(captchaAuthenticationFilter(),
						ExceptionTranslationFilter.class)
				.addFilterBefore(wxCreateFilter(),
						ExceptionTranslationFilter.class)
				.addFilterBefore(clientCaptchaAuthenticationFilter(),
						ExceptionTranslationFilter.class)
				.addFilterBefore(clientMobileAuthenticationFilter(),
						ExceptionTranslationFilter.class)
				.addFilterBefore(clientAccountAuthenticationFilter(),
						ExceptionTranslationFilter.class)
				.addFilterBefore(appleAuthenticationFilter(),
						ExceptionTranslationFilter.class)
				.addFilterBefore(parseTokenAuthenticationFilter(),
						ExceptionTranslationFilter.class)
				.addFilterAfter(userContextFilter(), AuthorizationFilter.class)
				.exceptionHandling(exceptionHandling -> exceptionHandling
						.accessDeniedHandler(new AccessDeniedHandlerImpl()))
				// 前后端分离不需要csrf
				.csrf(AbstractHttpConfigurer::disable)
				.formLogin(AbstractHttpConfigurer::disable)
				.httpBasic(AbstractHttpConfigurer::disable)
				.logout(AbstractHttpConfigurer::disable)
				.headers(AbstractHttpConfigurer::disable)
				.anonymous(AbstractHttpConfigurer::disable)
				.securityContext(AbstractHttpConfigurer::disable)
				.requestCache(AbstractHttpConfigurer::disable)
				.sessionManagement(AbstractHttpConfigurer::disable)
				.authorizeHttpRequests(configurer -> configurer
						.requestMatchers(HttpMethod.OPTIONS, "/**").permitAll()
						.requestMatchers(WHITE_URL_LIST).permitAll()
						.anyRequest().permitAll())
				.logout().logoutUrl("/custom/logout")
				.logoutSuccessHandler(logoutSuccessHandler());
		return http.build();
	}

}
