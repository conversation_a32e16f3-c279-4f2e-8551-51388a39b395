package com.zhihaoscm.time.schedule.job.shipping;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementAcceptDef;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementPlatDef;
import com.zhihaoscm.time.schedule.client.ShippingRequirementAcceptClient;
import com.zhihaoscm.time.schedule.client.ShippingRequirementPlatClient;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ShippingRequirementPlatJobs {

	@Autowired
	private ShippingRequirementPlatClient platClient;

	@Autowired
	private ShippingRequirementAcceptClient acceptClient;

	@XxlJob("expiration")
	public void expiration() {
		log.info(">>>>>>>>>>>expiration start<<<<<<<<<<<");
		// 获取所有已发布的平台需求
		List<ShippingRequirementPlat> published = platClient.findByState(
				ShippingRequirementPlatDef.State.TO_BE_ALLOCATED.getCode());
		if (CollectionUtils.isEmpty(published)) {
			log.info(">>>>>>>>>>>expiration has not execute<<<<<<<<<<<<<");
			return;
		}
		// 过滤已过期的需求并更新其状态为已结束
		List<ShippingRequirementPlat> expiredList = published
				.stream().filter(plat -> plat.getLoadDate()
						.plusDays(plat.getLoadDays()).isBefore(LocalDate.now()))
				.toList();
		if (CollectionUtils.isEmpty(expiredList)) {
			log.info(">>>>>>>>>>>expiration has not execute<<<<<<<<<<<<<");
			return;
		}
		expiredList.forEach(plat -> {
			plat.setState(ShippingRequirementPlatDef.State.ENDED.getCode());
			plat.setOrderAcceptanceState(
					ShippingRequirementPlatDef.OrderAcceptanceState.CONFIRMED
							.getCode());
			if (Objects.nonNull(plat.getSourceAppId())) {
				// 结束的如果是三方需求，需要把信息推送给三方
				platClient.handleEndThirdParty(plat);
			}
			platClient.update(plat);
		});
		// 将已关联到平台需求的货主需求状态更新为接单失败
		List<String> ids = expiredList.stream()
				.map(ShippingRequirementPlat::getId).toList();
		acceptClient.findByPlatIds(ids).forEach(accept -> {
			accept.setState(
					ShippingRequirementAcceptDef.PlatformState.ORDER_ACCEPTANCE_FAILED
							.getCode());
			acceptClient.update(accept);
		});
		log.info(">>>>>>>>>>>expiration end<<<<<<<<<<<<<");
	}

}
