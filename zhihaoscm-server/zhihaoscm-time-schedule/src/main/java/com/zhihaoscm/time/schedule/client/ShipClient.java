package com.zhihaoscm.time.schedule.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "zhihaoscm-service", path = "/ship", url = "${application.config.zhihaoscm-service}")
public interface ShipClient {

    @PostMapping("/updateWarehouseState")
    void updateWarehouseState();

    @PostMapping("/recognize/id-card")
    void recognizeIdCard();
}
