package com.zhihaoscm.time.schedule.client;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;

@FeignClient(name = "zhihaoscm-service", path = "/shipping/plat", url = "${application.config.zhihaoscm-service}")
public interface ShippingRequirementPlatClient {

	@GetMapping(value = "/find/state")
	List<ShippingRequirementPlat> findByState(
			@RequestParam(value = "state") Integer state);

	@PutMapping
	void update(@RequestBody ShippingRequirementPlat plat);

	@PostMapping(value = "/handle/end")
	void handleEndThirdParty(@RequestBody ShippingRequirementPlat plat);
}
