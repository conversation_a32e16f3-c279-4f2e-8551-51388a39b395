package com.zhihaoscm.time.schedule.job.ship;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zhihaoscm.time.schedule.client.ShipClient;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ShipJob {

    @Autowired
    private ShipClient shipClient;


    @XxlJob("updateWarehouseState")
    public void updateWarehouseState() {
        log.info("updateWarehouseState start-----------------");
        shipClient.updateWarehouseState();
        log.info("updateWarehouseState end----------------");
    }

    @XxlJob("recognizeIdCard")
    public void recognizeIdCard() {
        log.info("识别身份证正反面刷新数据开始 {}", LocalDateTime.now());
        shipClient.recognizeIdCard();
        log.info("识别身份证正反面刷新数据结束");
    }
}
