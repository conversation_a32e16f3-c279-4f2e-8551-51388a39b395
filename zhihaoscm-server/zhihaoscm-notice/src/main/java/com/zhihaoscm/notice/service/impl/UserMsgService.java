package com.zhihaoscm.notice.service.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.UserMessage;
import com.zhihaoscm.domain.bean.json.notice.Message;
import com.zhihaoscm.domain.meta.biz.AppTypeDef;
import com.zhihaoscm.domain.meta.biz.UserMessageDef;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.notice.bean.convert.MessageBuilder;
import com.zhihaoscm.notice.client.CustomerClient;
import com.zhihaoscm.notice.core.service.UserMessageService;
import com.zhihaoscm.notice.service.MessageService;

/**
 * 用户消息服务
 */

@Service
public class UserMsgService implements MessageService {

	@Autowired
	UserMessageService userMessageService;

	@Autowired
	MessageBuilder builder;

	@Autowired
	private CustomerClient customerService;

	@Override
	public void sendMessage(Integer type, Message message,
			List<String> receiptors) {
		if (CollectionUtils.isEmpty(receiptors)
				|| !(message instanceof com.zhihaoscm.domain.bean.json.notice.UserMessage userMessage)) {
			return;
		}
		receiptors.forEach(receiptor -> {
			Customer customer = customerService
					.findById(Long.valueOf(receiptor));
			if (customer != null) {
				UserMessage mess = builder.buildUserMessage(
						userMessage.getType(), userMessage.getTitle(),
						userMessage.getContent(), Long.valueOf(receiptor),
						userMessage.getUrl(),
						userMessage.getRole() != null
								? AppTypeDef.AppType.from(userMessage.getRole())
								: null,
						userMessage.getDetailId(),
						userMessage.getInitiator() != null
								? userMessage.getInitiator()
								: UserMessageDef.BusinessInitiator.receipt
										.getCode());
				userMessageService.create(mess);
			}
		});
	}

	@Override
	public Integer getType() {
		return SendType.USERMESSAGE.getCode();
	}
}
