package com.zhihaoscm.notice.core.service.impl;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.UserMessage;
import com.zhihaoscm.domain.bean.vo.UnReadUserMessageVo;
import com.zhihaoscm.domain.meta.biz.AppTypeDef;
import com.zhihaoscm.domain.meta.biz.UserMessageConstants;
import com.zhihaoscm.domain.meta.biz.UserMessageDef;
import com.zhihaoscm.notice.core.mapper.UserMessageMapper;
import com.zhihaoscm.notice.core.service.UserMessageService;

@Service
public class UserMessageServiceImpl
		extends MpLongIdBaseServiceImpl<UserMessage, UserMessageMapper>
		implements UserMessageService {

	public UserMessageServiceImpl(UserMessageMapper repository) {
		super(repository);
	}

	@Override
	public Page<UserMessage> paging(Integer page, Integer size,
			LocalDateTime date, String name, Integer type, Long customerId,
			Integer role, String sortKey, String sortOrder) {
		LambdaQueryWrapper<UserMessage> queryWrapper = Wrappers
				.lambdaQuery(UserMessage.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(StringUtils.isNotBlank(name), UserMessage::getTitle,
				name);
		queryWrapper.eq(Objects.nonNull(type), UserMessage::getType, type);
		queryWrapper.eq(Objects.nonNull(customerId), UserMessage::getCustomerId,
				customerId);
		queryWrapper.le(Objects.nonNull(date), UserMessage::getSendTime, date);
		if (Objects.nonNull(role)) {
			// 特殊处理，如果角色是承运商，则不查角色为空的数据,避免承运商收到货主消息
			if (AppTypeDef.AppType.CHUAN_WU.match(role)) {
				// 特殊处理，留言反馈的站内（20）不区分角色，因此承运商角色也能看到
				queryWrapper.and(r -> r.eq(UserMessage::getRole, role).or().eq(
						UserMessage::getUrl,
						UserMessageConstants.MESSAGE_FEEDBACK_DETAIL_PAGE));
			} else {
				queryWrapper.and(r -> r.eq(UserMessage::getRole, role).or()
						.isNull(UserMessage::getRole));
			}
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			queryWrapper.orderByAsc(UserMessage::getMessageState);
			queryWrapper.orderByDesc(UserMessage::getSendTime);
		}
		return repository.selectPage(new Page<>(page, size), queryWrapper);
	}

	@Override
	public void readed(List<Long> ids, Integer type) {
		if (CollectionUtils.isEmpty(ids)) {
			return;
		}
		LambdaUpdateWrapper<UserMessage> updateWrapper = Wrappers
				.lambdaUpdate(UserMessage.class);
		updateWrapper.set(UserMessage::getMessageState,
				UserMessageDef.MessageState.READED.getCode());
		updateWrapper.eq(Objects.nonNull(type), UserMessage::getType, type);
		updateWrapper.in(UserMessage::getId, ids);
		repository.update(updateWrapper);
	}

	@Override
	public void batchDelete(List<Long> ids, Integer type) {
		if (CollectionUtils.isEmpty(ids)) {
			return;
		}
		LambdaUpdateWrapper<UserMessage> updateWrapper = Wrappers
				.lambdaUpdate(UserMessage.class);
		updateWrapper.set(UserMessage::getDel, CommonDef.Symbol.YES.getCode());
		updateWrapper.eq(Objects.nonNull(type), UserMessage::getType, type);
		updateWrapper.in(UserMessage::getId, ids);
		repository.update(updateWrapper);
	}

	@Override
	public void deleteAll(String customerId, Integer type, Integer roleId) {
		if (StringUtils.isEmpty(customerId)) {
			return;
		}
		LambdaUpdateWrapper<UserMessage> updateWrapper = Wrappers
				.lambdaUpdate(UserMessage.class);
		updateWrapper.set(UserMessage::getDel, CommonDef.Symbol.YES.getCode());
		updateWrapper.eq(UserMessage::getCustomerId, customerId);
		updateWrapper.eq(Objects.nonNull(type), UserMessage::getType, type);
		if (Objects.nonNull(roleId)) {
			updateWrapper.and(r -> r.eq(UserMessage::getRole, roleId).or()
					.isNull(UserMessage::getRole));
		}
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (Objects.nonNull(context)) {
			Long userId = context.getUserId();
			updateWrapper.set(UserMessage::getUpdatedBy, userId);
			updateWrapper.set(UserMessage::getUpdatedTime, new Date());
		}
		repository.update(updateWrapper);
	}

	@Override
	public void readedAll(String customerId, Integer type, Integer roleId) {
		if (StringUtils.isEmpty(customerId)) {
			return;
		}
		LambdaUpdateWrapper<UserMessage> updateWrapper = Wrappers
				.lambdaUpdate(UserMessage.class);
		updateWrapper.set(UserMessage::getMessageState,
				UserMessageDef.MessageState.READED.getCode());
		updateWrapper.eq(UserMessage::getCustomerId, customerId);
		if (Objects.nonNull(roleId)) {
			updateWrapper.and(r -> r.eq(UserMessage::getRole, roleId).or()
					.isNull(UserMessage::getRole));
		}
		updateWrapper.eq(Objects.nonNull(type), UserMessage::getType, type);
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (Objects.nonNull(context)) {
			Long userId = context.getUserId();
			updateWrapper.set(UserMessage::getUpdatedBy, userId);
			updateWrapper.set(UserMessage::getUpdatedTime, new Date());
		}
		repository.update(updateWrapper);
	}

	@Override
	public UnReadUserMessageVo getUnreadMessage(String customerId,
			Integer roleId) {
		UnReadUserMessageVo vo = new UnReadUserMessageVo();
		LambdaQueryWrapper<UserMessage> queryWrapper = Wrappers
				.lambdaQuery(UserMessage.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(UserMessage::getCustomerId, customerId);
		queryWrapper.eq(UserMessage::getMessageState,
				UserMessageDef.MessageState.UNREAD.getCode());
		if (Objects.nonNull(roleId)) {
			// 特殊处理，如果角色是承运商，则不查角色为空的数据,避免承运商收到货主消息
			if (AppTypeDef.AppType.CHUAN_WU.match(roleId)) {
				queryWrapper.eq(UserMessage::getRole, roleId);
			} else {
				queryWrapper.and(r -> r.eq(UserMessage::getRole, roleId).or()
						.isNull(UserMessage::getRole));
			}
		}
		List<UserMessage> messages = repository.selectList(queryWrapper);
		if (CollectionUtils.isNotEmpty(messages)) {
			vo.setAllType((long) messages.size());

			vo.setTransform(messages.stream()
					.filter(message -> UserMessageDef.MessageType.TRANSFORM
							.match(message.getType()))
					.count());
			vo.setOther(messages.stream()
					.filter(message -> UserMessageDef.MessageType.OTHER
							.match(message.getType()))
					.count());
			vo.setCustomerId(customerId);
		}
		// 单独查询其他
		LambdaQueryWrapper<UserMessage> otherWrapper = Wrappers
				.lambdaQuery(UserMessage.class);
		this.filterDeleted(otherWrapper);
		otherWrapper.eq(UserMessage::getCustomerId, customerId);
		otherWrapper.eq(UserMessage::getMessageState,
				UserMessageDef.MessageState.UNREAD.getCode());
		otherWrapper.eq(UserMessage::getType,
				UserMessageDef.MessageType.OTHER.getCode());
		if (Objects.nonNull(roleId)) {
			otherWrapper.and(r -> r.eq(UserMessage::getRole, roleId).or()
					.isNull(UserMessage::getRole));
		}
		List<UserMessage> otherMessages = repository.selectList(otherWrapper);
		vo.setOther(Objects.isNull(otherMessages) ? 0L
				: (long) CollectionUtils.size(otherMessages));
		vo.setAllType(vo.getAllType() + vo.getOther());
		return vo;
	}
}
