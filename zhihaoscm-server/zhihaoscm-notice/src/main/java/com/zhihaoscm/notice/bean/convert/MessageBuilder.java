package com.zhihaoscm.notice.bean.convert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.domain.bean.entity.User;
import com.zhihaoscm.domain.bean.entity.UserMessage;
import com.zhihaoscm.domain.meta.biz.AppTypeDef;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.UserMessageDef;
import com.zhihaoscm.notice.bean.entity.alismsmessage.ALiSmsMessage;
import com.zhihaoscm.notice.bean.entity.wxwmessage.WxwMessage;
import com.zhihaoscm.notice.client.UserClient;

/**
 * 用户消息构造器
 * 
 */
@Component("messageBuilder")
public class MessageBuilder {

	@Autowired
	private UserClient userService;

	/**
	 * 构造阿里云短信对象
	 * 
	 * @param tempCode
	 *            -- 短信模板代码
	 * @param msgParam
	 *            -- 短信透传参数
	 * @param mobile
	 *            -- 发送手机号，空则取用户手机号
	 * @param ids
	 *            -- 群发用户id列表
	 * @param customerId
	 *            -- 单发用户id
	 * @param role
	 *            -- 用户角色
	 * @return
	 */
	public ALiSmsMessage buildAliMessage(String tempCode,
			Map<String, String> msgParam, String mobile, List<Long> ids,
			Long customerId, CustomerDef.Role role, Long tenantId) {
		ALiSmsMessage message = new ALiSmsMessage();
		message.setTemplateCode(tempCode);
		message.setParams(msgParam);
		message.setMobile(mobile);
		message.setCustomerIds(ids);
		message.setRole(role);
		message.setCustomerId(customerId);
		message.setTenantId(tenantId);
		return message;
	}

	/**
	 * 构建用户信息
	 *
	 * @param type
	 * @param title
	 * @param content
	 * @param customerId
	 * @param url
	 * @return
	 */
	public UserMessage buildUserMessage(Integer type, String title,
			String content, Long customerId, String url, AppTypeDef.AppType role,
			String detailId, Integer initiator) {
		UserMessage message = new UserMessage();
		message.setSendTime(LocalDateTime.now());
		message.setMessageState(UserMessageDef.MessageState.UNREAD.getCode());
		message.setType(
				type != null ? UserMessageDef.MessageType.from(type).getCode()
						: null);
		message.setTitle(title);
		message.setContent(content);
		message.setCustomerId(customerId);
		message.setUrl(url);
		message.setRole(Objects.nonNull(role) ? role.getCode() : null);
		message.setDetailId(detailId);
		message.setInitiator(initiator);
		return message;
	}

	public WxwMessage buildWxwMessage(List<Long> ids, String routerPath,
			String prefix, String operationModule, String desc, String keyword,
			String customizeNoticeMsg) {
		if (CollectionUtils.isEmpty(ids)) {
			return null;
		}
		WxwMessage mess = new WxwMessage();
		List<User> users = userService.findByIds(ids);
		if (CollectionUtils.isEmpty(users)) {
			return null;
		}
		String touser = users.stream().map(User::getWxwUserId)
				.collect(Collectors.joining("|"));
		mess.setReceptors(touser);
		mess.setRouterPath(routerPath);
		mess.setPrefix(prefix);
		mess.setOperationModule(operationModule);
		mess.setDesc(desc);
		mess.setKeyword(keyword);
		mess.setCustomizeNoticeMsg(customizeNoticeMsg);
		return mess;
	}
}
