package com.zhihaoscm.notice.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zhihaoscm.aliyun.sms.sdk.protocol.AliyunSmsService;
import com.zhihaoscm.aliyun.sms.sdk.request.SmsCommonRequest;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerEnterprise;
import com.zhihaoscm.domain.bean.entity.EnterpriseCustom;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.Message;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.CustomerEnterpriseDef;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.notice.bean.convert.MessageBuilder;
import com.zhihaoscm.notice.bean.entity.alismsmessage.ALiSmsMessage;
import com.zhihaoscm.notice.client.CustomerClient;
import com.zhihaoscm.notice.client.EnterpriseCustomClient;
import com.zhihaoscm.notice.client.SubAccountClient;
import com.zhihaoscm.notice.config.properties.SMSProperties;
import com.zhihaoscm.notice.service.MessageService;
import com.zhihaoscm.notice.utils.ThreadPoolUtil;

/**
 * 阿里云短信服务
 */
@Service
public class AliMessageService implements MessageService {

	@Autowired
	MessageBuilder builder;
	@Autowired
	private AliyunSmsService smsService;
	@Autowired
	private CustomerClient customerService;
	@Autowired
	private SubAccountClient customerEnterpriseService;
	@Autowired
	private EnterpriseCustomClient enterpriseCustomService;
	@Autowired
	private SMSProperties smsProperties;

	@Override
	public void sendMessage(Integer type, Message message,
			List<String> receiptors) {
		if (!(message instanceof AliMessage aliMessage)) {
			return;
		}

		// 单独处理验证码
		if (Objects.nonNull(aliMessage.getParams())
				&& aliMessage.getParams().containsKey("code")
				&& aliMessage.getParams().size() == 1) {
			sendRequest(aliMessage.getTemplateCode(), aliMessage.getTenantId(),
					aliMessage.getMobile(), aliMessage.getParams());
			return;
		}
		if (CollectionUtils.isEmpty(receiptors)
				&& StringUtils.isEmpty(aliMessage.getMobile())) {
			return;
		}
		List<Long> receiptorLongIds = CollectionUtils.isNotEmpty(receiptors)
				? receiptors.stream().map(Long::valueOf).toList()
				: List.of();
		ALiSmsMessage aLiSmsMessage = builder.buildAliMessage(
				aliMessage.getTemplateCode(), aliMessage.getParams(),
				aliMessage.getMobile(),
				receiptorLongIds.size() == 1 ? null : receiptorLongIds,
				receiptorLongIds.size() == 1 ? receiptorLongIds.get(0) : null,
				Objects.isNull(message.getRole()) ? null
						: CustomerDef.Role.from(message.getRole()),
				aliMessage.getTenantId());
		send(aLiSmsMessage);
	}

	private void send(ALiSmsMessage notice) {
		if (CollectionUtils.isNotEmpty(notice.getCustomerIds())) {
			// 批量发送短信
			List<Long> filterIds = notice.getCustomerIds();
			ThreadPoolUtil.submitTask(() -> filterIds.forEach(customerId -> {
				Customer customer = customerService.findById(customerId);
				if (customer != null) {
					Map<String, String> mutableMap = new HashMap<>();
					if (Objects.nonNull(notice.getParams())) {
						mutableMap = new HashMap<>(notice.getParams());
					}
					mutableMap.put("client",
							Objects.isNull(notice.getRole()) ? "链云用户"
									: notice.getRole().getName());
					if (check(notice.getCustomerId())) {
						sendRequest(notice.getTemplateCode(),
								notice.getTenantId(), customer.getMobile(),
								mutableMap);
					}
					// 给子账号发短信
					this.batchSendToSubAccount(notice);

				}
			}), ThreadPoolUtil.getUSER_EXECUTOR());
		} else {
			// 单条短信发送
			if (Objects.nonNull(notice.getCustomerId())) {
				ThreadPoolUtil.submitTask(() -> {
					Customer customer = customerService
							.findById(notice.getCustomerId());
					if (customer != null) {
						Map<String, String> mutableMap = new HashMap<>();
						if (Objects.nonNull(notice.getParams())) {
							mutableMap = new HashMap<>(notice.getParams());
						}
						mutableMap.put("client",
								Objects.isNull(notice.getRole()) ? "链云用户"
										: notice.getRole().getName());
						if (check(notice.getCustomerId())) {
							sendRequest(notice.getTemplateCode(),
									notice.getTenantId(),
									StringUtils.isNoneBlank(notice.getMobile())
											? notice.getMobile()
											: customer.getMobile(),
									mutableMap);
						}
						// 给子账号发短信
						this.sendToSubAccount(notice);
					}
				}, ThreadPoolUtil.getUSER_EXECUTOR());
			} else if (StringUtils.isNoneBlank(notice.getMobile())) {
				ThreadPoolUtil.submitTask(() -> {
					Map<String, String> mutableMap = new HashMap<>();
					if (Objects.nonNull(notice.getParams())) {
						mutableMap = new HashMap<>(notice.getParams());
					}

					mutableMap.put("client",
							Objects.isNull(notice.getRole()) ? "链云用户"
									: notice.getRole().getName());
					sendRequest(notice.getTemplateCode(), notice.getTenantId(),

							notice.getMobile(), mutableMap);

				}, ThreadPoolUtil.getUSER_EXECUTOR());
			}
		}
	}

	/**
	 * 校验用户是否接收短信
	 *
	 * @param customerId
	 * @return
	 */
	public boolean check(Long customerId) {
		boolean flag = false;
		Customer op = customerService.findById(customerId);
		if (Objects.nonNull(op)) {
			flag = CommonDef.Symbol.YES.match(op.getReceiptSms());
		}
		return flag;
	}

	/**
	 * 给子账号发短信
	 *
	 * @param notice
	 */
	private void sendToSubAccount(ALiSmsMessage notice) {
		Customer customer = customerService.findById(notice.getCustomerId());
		if (Objects.isNull(customer)) {
			return;
		}
		// 校验主账号会员是否过期
		if (CustomerDef.MemberState.EXPIRY.match(customer.getMemberState())) {
			return;
		}
		List<CustomerEnterprise> subs = customerEnterpriseService.findByMainId(
				customer.getId(),
				CustomerEnterpriseDef.State.CONFIRMED.getCode());
		if (CollectionUtils.isEmpty(subs)) {
			return;
		}
		List<Long> subIds = subs.stream()
				.filter(sub -> CommonDef.Symbol.YES.match(sub.getAccountState())
						&& CommonDef.Symbol.YES.match(sub.getReceiptSms()))
				.map(CustomerEnterprise::getSubAccountId).toList();
		ThreadPoolUtil.submitTask(() -> subIds.forEach(customerId -> {
			Customer sub = customerService.findById(customerId);
			if (Objects.nonNull(sub)) {
				Map<String, String> mutableMap = new HashMap<>(
						notice.getParams());
				mutableMap.put("client",
						Objects.isNull(notice.getRole()) ? "链云用户"
								: notice.getRole().getName());
				sendRequest(notice.getTemplateCode(), notice.getTenantId(),
						sub.getMobile(), mutableMap);

			}

		}), ThreadPoolUtil.getUSER_EXECUTOR());
	}

	/**
	 * 给子账号发短信
	 *
	 * @param notice
	 */
	private void batchSendToSubAccount(ALiSmsMessage notice) {
		List<Long> customerIds = notice.getCustomerIds();
		for (Long cid : customerIds) {
			Customer customer = customerService.findById(cid);
			if (Objects.isNull(customer)) {
				return;
			}
			// 校验主账号会员是否过期
			if (CustomerDef.MemberState.EXPIRY
					.match(customer.getMemberState())) {
				return;
			}
			List<CustomerEnterprise> subs = customerEnterpriseService
					.findByMainId(customer.getId(),
							CustomerEnterpriseDef.State.CONFIRMED.getCode());
			if (CollectionUtils.isEmpty(subs)) {
				return;
			}
			List<Long> subIds = subs.stream().filter(
					sub -> CommonDef.Symbol.YES.match(sub.getAccountState())
							&& CommonDef.Symbol.YES.match(sub.getReceiptSms()))
					.map(CustomerEnterprise::getSubAccountId).toList();
			ThreadPoolUtil.submitTask(() -> subIds.forEach(customerId -> {
				Customer sub = customerService.findById(customerId);
				if (Objects.nonNull(sub)) {
					Map<String, String> mutableMap = new HashMap<>(
							notice.getParams());
					mutableMap.put("client",
							Objects.isNull(notice.getRole()) ? "链云用户"
									: notice.getRole().getName());
					sendRequest(notice.getTemplateCode(), notice.getTenantId(),
							sub.getMobile(), mutableMap);

				}

			}), ThreadPoolUtil.getUSER_EXECUTOR());
		}
	}

	/**
	 * 发送短信
	 *
	 * @param tplCode
	 * @param tenantId
	 * @param mobile
	 * @param mutableMap
	 */
	public void sendRequest(String tplCode, Long tenantId, String mobile,
			Map<String, String> mutableMap) {
		SmsCommonRequest smsCommonRequest = SmsCommonRequest.buildSingleSms(
				mobile, smsProperties.getSignName(), tplCode,
				JsonUtils.objectToJson(mutableMap));
		if (Objects.nonNull(tenantId)) {
			EnterpriseCustom enterpriseCustom = enterpriseCustomService
					.findById(tenantId);
			if (Objects.nonNull(enterpriseCustom)) {
				smsCommonRequest.setAccessKey(enterpriseCustom.getAccessKey());
				smsCommonRequest.setSecretKey(enterpriseCustom.getSecretKey());
				smsCommonRequest.setTenantId(enterpriseCustom.getId());
				smsCommonRequest.setSignName(enterpriseCustom.getSignName());
				smsCommonRequest.setTemplateCode(
						enterpriseCustom.getLoginMsgTemplateId());
			}
		}
		smsService.sendNoticeSms(smsCommonRequest);
	}

	@Override
	public Integer getType() {
		return SendType.ALIMESSAGE.getCode();
	}
}
