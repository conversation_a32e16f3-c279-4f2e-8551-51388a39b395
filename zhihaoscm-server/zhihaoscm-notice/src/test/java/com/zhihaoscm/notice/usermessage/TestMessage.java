package com.zhihaoscm.notice.usermessage;

import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.UserMessage;
import com.zhihaoscm.domain.bean.json.notice.Messages;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.notice.bean.entity.alismsmessage.ALiSmsMessage;
import com.zhihaoscm.notice.bean.convert.MessageBuilder;
import com.zhihaoscm.notice.processor.MessageSender;
import com.zhihaoscm.notice.service.impl.WxwMessageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest()
public class TestMessage implements InitializingBean {

	@Autowired
	private MessageSender sender;
	@Autowired
	private MessageBuilder messageBuilder;
	@Autowired
	private WxwMessageService wxwMessageService;
	@Autowired
	private MqUtil mqUtil;

	@Test
	public void testSendUserMessage() {
		Integer type = null;
		String title = "交易消息测试";
		String content = "您的项目有一笔收款待确认";
		Long customerId = 479L;
		String url = "1";
		for (int i = 10; i > 0; i--) {
			if (i % 3 == 0) {
				type = UserMessageDef.MessageType.TRANSACTION.getCode();
			} else if (i % 3 == 1) {
				type = UserMessageDef.MessageType.TRANSFORM.getCode();
			} else {
				type = UserMessageDef.MessageType.OTHER.getCode();
			}
			title = title + i;
			UserMessage message = messageBuilder.buildUserMessage(type, title,
					content, customerId, url, AppTypeDef.AppType.LIAN_YUN,
					String.valueOf(0L),
					UserMessageDef.BusinessInitiator.initiate.getCode());
			//sender.sendMessage(SendType.USERMESSAGE, message, null);
		}
	}

	@Test
	public void testSendAliSms() {
		Long customerId = 486L;
		Map<String, String> msgParams = Map.of("code", "1234");
		String mobile = "***********";
		ALiSmsMessage message = messageBuilder.buildAliMessage("SMS_465660519",
				msgParams, mobile, null, customerId, CustomerDef.Role.CUSTOMER,null);
		//sender.sendMessage(SendType.ALIMESSAGE, null, message);
	}

	@Test
	public void testWxwMessage(){
		WxwMessage message = WxwMessage.builder().url("/supplyManage/product")
				.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
				.operationModule(WxwDef.NoticeOperationModule.PRODUCT.getDesc())
				.desc("新增待审核").keyword("40021390240500011")
				.content(StringUtils.EMPTY).build();
		wxwMessageService.sendMessage(SendType.WXWMESSAGE.getCode(), message,
				List.of("54"));
	}

	@Test
	public void testMQWxwMessage() {
		WxwMessage message = WxwMessage.builder().url("/supplyManage/product")
				.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
				.operationModule(WxwDef.NoticeOperationModule.PRODUCT.getDesc())
				.desc("新增待审核").keyword("40021390240500011")
				.content(StringUtils.EMPTY).receiptors(List.of("54")).build();
		mqUtil.asyncSend(MqMessage.builder().topic(TopicDef.SEND_NOTICE)
				.message(org.springframework.messaging.support.MessageBuilder
						.withPayload(JsonUtils.objectToJson(message)).build())
				.build());
	}

	@Test
	public void testMQAliSms() {
		Map<String, String> msgParams = Map.of("code", "1234");
		String mobile = "***********";
		Messages messages = Messages.builder().templateCode("SMS_465660519")
				.params(msgParams).mobile(mobile).receiptors(List.of("427"))
				.role(CustomerDef.Role.CUSTOMER.getCode())
				.messageTypes(List.of(SendType.ALIMESSAGE.getCode())).build();
		mqUtil.asyncSend(MqMessage.builder().topic(TopicDef.SEND_NOTICE)
				.message(org.springframework.messaging.support.MessageBuilder
						.withPayload(JsonUtils.objectToJson(messages)).build())
				.build());
	}

	@Test
	public void testMQUserMessage() {
		Integer type = null;
		String title = "交易消息测试";
		String content = "您的项目有一笔收款待确认";
		for (int i = 2; i > 0; i--) {
			if (i % 3 == 0) {
				type = UserMessageDef.MessageType.TRANSACTION.getCode();
			} else if (i % 3 == 1) {
				type = UserMessageDef.MessageType.TRANSFORM.getCode();
			} else {
				type = UserMessageDef.MessageType.OTHER.getCode();
			}
			title = title + i;
			Messages messages = Messages.builder().type(type).detailId("1")
					.receiptors(List.of("427")).url("www.baidu.com")
					.title(title).content(content)
					.role(CustomerDef.Role.CUSTOMER.getCode())
					.messageTypes(List.of(SendType.USERMESSAGE.getCode()))
					.build();
			mqUtil.asyncSend(MqMessage.builder().topic(TopicDef.SEND_NOTICE)
					.message(
							org.springframework.messaging.support.MessageBuilder
									.withPayload(
											JsonUtils.objectToJson(messages))
									.build())
					.build());
		}
	}

	@Override
	public void afterPropertiesSet() {

	}
}
