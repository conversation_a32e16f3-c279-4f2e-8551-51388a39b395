package com.zhihaoscm.service.usercenter;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.domain.bean.entity.User;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.service.core.service.usercenter.PromotionDetailService;
import com.zhihaoscm.service.core.service.usercenter.PromotionService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

@RunWith(SpringRunner.class)
@SpringBootTest()
public class UserCenterTest implements InitializingBean {

	@Test
	public void testService() {
		// 激活码
		/*
		 * ActivationCode code =
		 * activationCodeService.findByCode("1a7770014c8a48e89d4b").get();
		 * activationCodeService.findByCustomerId(427L); code.setId(null); code
		 * = activationCodeService.create(code);
		 * activationCodeService.update(code); System.out.println(code);
		 */

		// 业务设置
		//BusinessConfig config = businessConfigService.findByType(1).get();

		// 客户账号
		/*CustomerAccount account = customerAccountService.findOne(427L).get();
		account.setBalance(BigDecimal.TEN);
		customerAccountService.updateAllProperties(account);*/

		// 客户银行
		//CustomerBank bank = customerBankService.findOne(48L).get();

		// 客户
		/*List<Long> ids = List.of(1L, 2L, 3L, 1L);
		customerService.isRepeat(ids);
		Long roleCount = customerService.countByRoleCode(1,
				LocalDateTime.of(2001, 1, 1, 1, 1),
				LocalDateTime.of(2031, 1, 1, 1, 1));
		roleCount = customerService.countByMemberLevel(1,
				LocalDateTime.of(2001, 1, 1, 1, 1),
				LocalDateTime.of(2031, 1, 1, 1, 1));

		List<Customer> customers = customerService.findAll(LocalDateTime.of(2001, 1, 1, 1, 1),
				LocalDateTime.of(2031, 1, 1, 1, 1));
		customers = customerService
				.findByIdsNoDeleted(List.of(427L));
		Customer customer = customerService.findOne(427L).get();
		MembershipLevel level = customerService.getMembershipLevel(customer);
		Optional<Customer> optional = customerService.findByMobile("***********");
		optional = customerService.findLastLogin("***********");
		Optional<CustomerVo> vo = customerService.findVoById(427L);
		customers = customerService.selector(null,null,null,null);
		optional = customerService.findByUnionId("10000560");
		Optional<Enterprise> enterprise = customerService.findEnterpriseByCustomerId(427L);*/

		//微信绑定
		//CustomerWx wx = customerWxService.bind("123456",427L).get();
		/*CustomerWx wx = null;
		wx = customerWxService.findOne(wx.getId()).get();
		wx = customerWxService.findByUnionId("123456").get();
		wx = customerWxService.findByCustomerId(427L).get();
		wx.setId(null);
		wx = customerWxService.create(wx);
		wx.setUnionId("12345666");
		customerWxService.update(wx);
		customerWxService.untie(427L);
		//部门
		Dept dept = deptService.findOne(43L).get();
		dept.setName("测试2");
		dept = deptService.update(dept);
		dept.setId(null);
		dept = deptService.create(dept);
		deptService.delete(dept.getId());
		//开会员服务
		BigDecimal amount = memberOpenRecordService.findAmountByMemberLevel(2,LocalDateTime.of(2001, 1, 1, 1, 1),
				LocalDateTime.of(2031, 1, 1, 1, 1)).get();
		OnlinePayOpenMemberDto dto = new OnlinePayOpenMemberDto();
		memberOpenRecordService.onlinePayOpenMember(dto);
		//会员等级
		amount = membershipLevelService.computePrice(2,1).get();
		//员工
		Person person = personService.findByCode("WeiZhiPeng",false);
		person = personService.findOne(54L).get();*/
		//印章
		/*List<Seal> seals = sealService.findByMainId(139L);
		seals = sealService.findSubByMainId(139L,97L);
		sealService.revokeSubSeal(139L,97L);
		Seal seal = sealService.findOne(136L).get();
		seal.setId(null);
		seal = sealService.create(seal);
		seal.setName("测试===");
		sealService.updateAllProperties(seal);
		sealService.delete(seal.getId());*/
		//用户
		Optional<User> user = userService.validateIsExist(54L);
		List<User> users = userService.findUsersByPermission(AdminPermissionDef.SHIP_DOWNPAYMENT_W,null);
		Map<Long, User> idUserMap = userService.getIdMap(List.of(user.get().getId()));
		user = userService.findByNameAndEmployeeId("魏志鹏","334");
		user = userService.findByEmployeeId("334");
		users = userService.selector(null,null);
		users = userService.findAll();
		LoginUser<User> loginUser = userService.readUser("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.IjU0LTE3MzM0NTIxMzEwNzEtYzZhMTRiOGZmNGZiNGVmMDk4YjkzN2RhOTQ0MGFhMTki.afzh2mn18g2iiHDCr1LaQGTweXJFWszZvelCL8z7_Sw");
	}
	@Autowired
	PromotionService promotionService;
	@Autowired
	PromotionDetailService promotionDetailService;
	@Autowired
	UserService userService;

	@Override
	public void afterPropertiesSet() throws Exception {

	}
}
