package com.zhihaoscm.service;

import org.junit.jupiter.api.Test;

import com.zhihaoscm.common.mybatis.plus.util.Generator;

public class GeneratorCode {

	@Test
	void generate() {
		Generator.generate(
				"D:\\project\\zhihao\\zhihaoscm-server\\zhihaoscm-service",
				"D:\\project\\zhihao\\zhihaoscm-server\\zhihaoscm-domain",
				Generator.LONG_ID,
				"************************************************************************************************",
				"root", "123456",
				"t_oil_index_snapshot,t_oil_index,t_oil_index_version,t_oil_index_version_record",
				"com.zhihaoscm.domain.bean.entity",
				"com.zhihaoscm.service.core.mapper",
				"com.zhihaoscm.service.core.service",
				"com.zhihaoscm.service.resource", "t_");
	}

	@Test
	void generate1() {
		Generator.generate(
				"C:\\project\\zhihao\\zhihaoscm-server\\zhihaoscm-service",
				"C:\\project\\zhihao\\zhihaoscm-server\\zhihaoscm-domain",
				Generator.STRING_ID,
				"************************************************************************************************",
				"root", "123456",
				"t_product_consignment",
				"com.zhihaoscm.domain.bean.entity",
				"com.zhihaoscm.service.core.mapper",
				"com.zhihaoscm.service.core.service",
				"com.zhihaoscm.service.resource", "t_");
	}
}
