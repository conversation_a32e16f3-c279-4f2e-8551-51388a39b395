#!/bin/sh
# limit jvm memory
limit_in_bytes=$(cat /sys/fs/cgroup/memory/memory.limit_in_bytes)

# If not default limit_in_bytes in cgroup
if [ "$limit_in_bytes" -ne "9223372036854771712" ]
then
    limit_in_megabytes=$(expr $limit_in_bytes \/ 1048576)
    heap_size=$(expr $limit_in_megabytes - $RESERVED_MEM_SIZE)

    # 如果计算出的堆内存超过上限，则使用上限值
    if [ "$heap_size" -gt "$MAX_HEAP_MB" ]; then
        heap_size=$MAX_HEAP_MB
    fi

    export JAVA_OPTS="-Xmx${heap_size}m $JAVA_OPTS"
    echo JAVA_OPTS=$JAVA_OPTS
fi

echo "********************************************************"
echo "Starting the zhihaoscm service "
echo "********************************************************"

java -Xmx2g -Xms2g \
     -Dspring.cloud.nacos.config.server-addr=$CONFIG_CENTER_URI \
     -Dspring.cloud.nacos.config.namespace=$CONFIG_NAMESPACE \
     -Dspring.cloud.nacos.discovery.ip=$CLIENT_IP \
     -Dspring.cloud.nacos.config.enabled=$NACOS_CONFIG_ENABLED \
     -Dspring.cloud.nacos.config.username=$NACOS_ACCOUNT \
     -Dspring.cloud.nacos.config.password=$NACOS_PASSWORD \
     -Dspring.cloud.nacos.discovery.username=$NACOS_ACCOUNT \
     -Dspring.cloud.nacos.discovery.password=$NACOS_PASSWORD \
     -Duser.timezone=GMT+08 \
     -javaagent:/var/skywalking/agent/skywalking-agent.jar \
     -Dskywalking.agent.service_name=$GROUP_NAME::zhihaoscm-service \
     -Dskywalking.collector.backend_service=***************:11800 \
     -jar \
     -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:$DEBUG_PORT \
     /usr/local/source/@project.build.finalName@.jar

