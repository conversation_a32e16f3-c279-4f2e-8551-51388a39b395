server:
    port: 8080

# Application Configuration
application:
    config:
        # 契约锁认证完毕返回页面
        contract-callback-page: /pages/contact/contact
        # jwt密钥
        jwt-secret: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9"
        zhihaoscm-notice: http://***************:8081
        user-center: http://localhost:8082
        zhihaoscm-service-prod: http://localhost:7080

# spring相关配置
spring:
    main:
        allow-bean-definition-overriding: true
        allow-circular-references: true
    servlet:
        multipart:
            max-file-size: 15MB
            max-request-size: 15MB
    cloud:
        nacos:
            discovery:
                # dev 开启服务发现
                register-enabled: false
                enabled: false
            config:
                username: hotfix
                enabled: false
        # Feign客户端配置
        openfeign:
            client:
                config:
                    zhihaoscm-notice:
                        requestInterceptors:
                            - com.zhihaoscm.common.gray.GrayscaleFeignInterceptor
                    user-center:
                        requestInterceptors:
                            - com.zhihaoscm.service.config.security.custom.CustomerTransferFeignInterceptor
                            - com.zhihaoscm.service.config.security.admin.UserTransferFeignInterceptor
    # 数据库配置
    datasource:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: jdbc:mysql://***************:3306/zhihaoscm?useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: root
        password: 123456
redis:
    cache:
        host: ***************
        port: 6379
        password: 123456
        database: 0
        timeout: 10000
        ttl: 30d
        jedis:
            pool:
                max-active: 8
                max-idle: 8
                min-idle: 0
                max-wait: -1ms
    enterpriseCustom: enterpriseCustomInfo
    enterpriseCustomChannel: enterpriseCustomInfoChannel


springdoc:
    swagger-ui:
        enabled: true
    # 指定包下面的接口生成文档
    packages-to-scan: com.zhihaoscm.service.resource
    api-docs:
        path: /v3/api-docs
    group-configs:
        -   group: 'admin'
            paths-to-match: '/admin/**'
            packages-to-scan:
                # 配置接口文档扫描包路径
                - com.zhihaoscm.service.resource.admin
        -   group: 'custom'
            paths-to-match: '/custom/**'
            packages-to-scan:
                # 配置接口文档扫描包路径
                - com.zhihaoscm.service.resource.custom

# 中交查车配置
zhongjiao:
    username: eba86970-bdf4-4fd4-926c-ae91f605f4aa
    secret: ******************************
    host: https://zhiyunopenapi.95155.com
    private-key: a559cb06-e215-4ffa-bb9f-197d2e8131eb
    clientld: 947c64fb-ba22-458b-9e39-9685bd893813

aliyun:
    captcha:
        accessKey: LTAI5tNkZziT4Un6GBEkwvKj
        secretKey: ******************************
        endpoint: captcha.cn-shanghai.aliyuncs.com
    oss:
        access-key: LTAI5tDxzE2RmUGyCAjvJYLR
        secret-key: ******************************
        bucket: zhihaoscm-dev
        public-bucket: zhihaoscm-public
        endpoint: oss-cn-hangzhou.aliyuncs.com
        url-expire: 7200
        cache-expire: 5400
        custom-domain-name: devstatic.zhihaoscm.com
    sms:
        access-key: LTAI5tNn8xdUUdqidPD69UWL
        secret-key: ******************************
        endpoint: dysmsapi.aliyuncs.com
        connect-timeout: 5
        read-timeout: 10
        # 等待用户确认 模板code
        waitUserConfirmCode: SMS_474260103
        # 项目预付款余额不足提醒 模板code
        waitPaymentCode: SMS_474135105
        # 确认收款提醒 模板code
        collectionConfirmationCode: SMS_474300092
        # 收款驳回提醒  模板code
        collectionRejectCode: SMS_474310097
        # 提货新增提醒 模板code
        createPurchaseGoodsCode: SMS_474225100
        # 会员续费提醒 模板Code （修改）
        memberRenewalReminder: SMS_489415125
        # 主账号邀请子账号 （修改）
        subAccountConfirmCode: SMS_489545129
        # 付款作废确认提醒
        paymentAbortConfirmCode: SMS_474155446
        #======================物流短信开始=============================
        # 新增船运单关联货主 （修改）
        receiveShippingOrderCode: SMS_489385136
        # 船运需求匹配成功生成船运单 （修改）
        receiveShippingOrderCode2: SMS_489375127
        # 船主收到船运单提醒-生成船运单时 模板code
        captainReceiveShippingOrderCode: SMS_489385136
        # 船主抢单失败提醒-(平台船运需求被平台拒绝抢单失败时,平台船运需求主动结束,平台胎运需求被其他船主抢单成功)（修改）
        orderFailedCode: SMS_489375128
        # 货主支付船务信息服务费提醒-船运单状态进入待支付船务信息服务费且货主未支付 (修改)
        ownerPayShippingServiceFeeCode: SMS_489380131
        # 船主支付船务信息服务费提醒-船运单状态进入待支付船务信息服务费且船主未支付 (修改)
        captainPayShippingServiceFeeCode: SMS_489495112
        # 平台邀请承运商抢单提醒（有船主） （修改）
        platformTransferShipRequireCode: SMS_489820315
        # 平台邀请承运商抢单提醒（无船主） （修改）
        platformTransferShipRequireCode2: SMS_489695338
        # 船主一键订油后后台报计划了（但船主没支付） (修改)
        carrierPayOilFeeCode: SMS_489575146
        # 确认船主支付了油费且报计划了 （修改）
        carrierConfirmRefuelingCode: SMS_489530115
        #======================物流短信结束=============================
        ####### 短信链接页面配置 #######
        intendDetailPage : /page/business/common/intend/intendDetail/intendDetail
        # 付款详情
        paymentDetailPage : /page/business/common/purchasePayment/purchasePaymentDetail/purchasePaymentDetail
        # 收款详情
        receiptDetailPage : /page/business/common/purchasePayment/purchasePaymentDetail/purchasePaymentDetail
        # 找船需求详情
        shipDetailPage : /page/business/customer/shipOrder/transportOrderShip/detail/detail
        # 船运单详情
        transportOrderPage : /page/business/customer/shipOrder/transportOrderShip/detail/detail
        # 承运商船运单详情
        transferTransportOrderPage : /pages/transportOrderShipDetail/detail
        # 承运商抢单详情
        transferShipRequirePage : /pages/shipRequire/order/detail/detail
        # 会员页
        membershipPage : /page/vip/membership/membership
        # 承运商会员页
        transferMembershipPage : /pages/vip/membership/membership
        # 受邀请记录详情
        invitationRecordPage : /page/setting/invitationRecord/detail/detail
        # 提货单详情
        purchaseGoodPage : /page/business/common/purchaseGoods/purchaseGoodsDetail/purchaseGoodsDetail
        # 项目详情
        projectPage: /page/business/common/project/projectDetail/projectDetail
        # 承运商船运需求详情
        transferShipRequireDetailPage: /pages/shipRequire/require/detail/detail
        # 短信链接小程序 URL Link 失效类型，失效时间：0，失效间隔天数：1
        expireType: 1
        # 短信链接失效时间，默认最长时限30天
        expireNum: 30
        # app短信链接
        appLinkUrl: https://dev.zhihaoscm.cn/app-version/index

    ocr:
        accessKey: LTAI5tLmSgitNEggu3agu7g7
        secretKey: ******************************
        endpoint: ocr-api.cn-hangzhou.aliyuncs.com





mybatis-plus:
    # 别名 bean包
    typeAliasesPackage: com.lianxin.tffm.core.bean.entity
    mapperLocations: classpath:mybatis/mapper/**/*.xml
    global-config:
        banner: false
        db-config:
            update-strategy: ignored
    configuration:
        mapUnderscoreToCamelCase: true
        lazyLoadingEnabled: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    type-handlers-package: com.zhihaoscm.common.mybatis.plus.json.handler,com.zhihaoscm.domain.bean.json.handler

# 契约锁相关配置
qiyuesuo:
    url: https://openapi.qiyuesuo.cn
    appToken: ZXE2kLBXKR
    appSecret: ******************************
    goodsCategoryId: 3210504295250256791
    reconciliationCategoryId: 3210483514868867600
    goodsReceiptCategoryId: 3210529196300337640
    creator-contact: 18607096269
    contract-category-id: 3208362134912209735
    secret-key: JvKkUNeyS6wqBnmP
    personal-callback-url: http://*************:10005/dev/api/custom/callback/personal/certification
    enterprise-callback-url: http://*************:10005/dev/api/custom/callback/enterprise/certification
    lyAppToken: SMb8m0jbd7
    lyAppSecret: 4cdWNtBl2G74yWnkx34NOSrz8yeDl6
    callbackUrl: https://dev.api.zhihaoscm.cn/custom/callback/contract/sign

# 宇视云相关配置
yushiyun:
    app-id: 529431360104824833
    secret-key: 2e627cbb1c52819287a48075c6a929c9
    host: https://ezcloud.uniview.com
    token-prefix: 'yushiyun:token'
    expire-time: 5
    video-expire-time: 300
    protocol: 2
    quality: 2

# 船顺网相关配置
ships66:
    url: http://saas.ships66.com
    accessKey: 283760d91b92eb706ef2abd1cc66f175

wxw:
    url: https://qyapi.weixin.qq.com/cgi-bin
    corpId: ww7b5030278e2a0960
    corpSecret: -GJs3KmfZ9pH61NSHSR8vxOMQtp-0aqAL-TZxLhy6k4
    sToken: fr7VNEjWc4Q
    encodingAESKey: HvXJRv3F43Lpk3APJ7H5iADGNsRhrsecaRE5Ycm4Lou
    redirectUri: https://www.zhihaoscm.cn/dev/zhihaoscm-admin
    agentId: 1000002

wx-pc:
    appid: wx52bad7fb1d5b2565
    secret: 94108ac9a31154180e4bef585a41f105
    grant_type: authorization_code

hkws:
    client-id: 706ad5d265f94ec796f57beb184c2b98
    client-secret: d6198cb37d65421d92e22d3b7c1192fa
    host: https://api2.hik-cloud.com
    token-prefix: 'hkws:token'
    expire-time: 6
    group-no: 0

rocketmq:
    name-server: ***************:9876
    producer:
        group: zhihao_group
        send-message-timeout: 3000
        send-message-max-message-size: 4096
        retry-times-when-send-failed: 3
        retry-times-when-send-async-failed: 2
        sendMessageTimeout: 50000
    consumer:
        group: zhihao_group
        pull-batch-size: 5
    enhance:
        enabledIsolation: true
        environment: ${spring.cloud.nacos.config.username}
ai-service: openAiServiceImpl
openai:
    proxyHost: ***************
    proxyPort: 7890
    apiUrl: https://api.openai.com
    apiKey: ***************************************************
    apiBeta: assistants=v2

# miniapp相关配置
wx-mini-app:
    url: https://api.weixin.qq.com
    appid: wx8c10c99edf9d8908
    secret: ********************************
    carrier-appid: wxc4bf3cdba4aff288
    carrier-secret: a9349d147ad0fadcaac3a73a436dc399
    env-version: trial
    official-appid: wxd5f92dce453a98a1
    official-secret: 24e635b9056d3ea57a2e770f4d8309aa

# 支付宝支付
alipay:
    ali-pay-cert-path: ${user.dir}/zhihaoscm-server/zhihaoscm-service/src/main/resources/pay/alipay/sandbox/alipayPublicCert.crt
    ali-pay-root-cert-path: ${user.dir}/zhihaoscm-server/zhihaoscm-service/src/main/resources/pay/alipay/sandbox/alipayRootCert.crt
    app-cert-path: ${user.dir}/zhihaoscm-server/zhihaoscm-service/src/main/resources/pay/alipay/sandbox/appPublicCert.crt
    app-id: 9021000137681624
    gateway-url: https://openapi-sandbox.dl.alipaydev.com/gateway.do
    notify-url: http://fa46en.natappfree.cc/payment/alipay/callback
    private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCbJ2gHWfcoJnrzHuZ71z/Asxw4udxrZuPiQbIvtD/iZlpvaEdjUZXp+q1zYcOl0LTA13C0V9JJTqyT5tNILd6zC2HFnwsRWWMu6IyRP+u5H8XnRYzKOvQpg7I/e15JLNXht7Em0qHo6Z/UsRRl6NR1yVgwAZNXs/0olvwkebAQNUjDO7D4KcNWcGsDFZLUty3eUNP34HiIEyqIFFsDeMmHmHJ2hCLICjgwz9OvaW/tcJ48XmZN8QbrpbiCShzuFkXYFfnI+atN9BCWqbBcY+G3IGP2/svSYlpR8mSCIDVWFH8E5VpGWdBqsaytD3cR371Eavid63WKIK7+k7KFaeUTAgMBAAECggEAXVydEK9mNlbRbTKO5r5jsoUU2l2c8VP8lM2Ld4EzX/zPNnK9YlpZp24cqW2O2Vx5KAF75ElkVJBu8KDZSnmvpHWY494MPuhW8ock7tJT+hXQdJY5XgeTnNeOjvyhg87i9OwgQYhFvW23q7jS2W/9SNNbqaeJDiFUmPQhzaEb/OPimyO/CUqPsEKf7yBcAUAwIfzUDyzW2gZ4N+jCGcl5i2C+sEbEble8IODebL4rxASD8wyzLCpmMBQkL8SuiyxkuWHJQ5nJyDJHaUaq3pwwxiW1Hf68FMCS3HMh4nquishfA96WrHGcZ2dS60iyxlIGSG53T1SP0w2+wEdIcTogQQKBgQDZiWpowoNtmbd/UVR0V+KpdCiMRZoXwmmKWYWQZdk4vb1Ia3X+UoPUtRXfBJUJH6WqQgtQ7zkYEedH3ks3F7QxAiCs4CLvtsv3/C2/KUmx/FjAifu+ZfrbaHMvLKImFh19hOQUPewPOqwctqh5ePi8MMPcj6kM4qa8Hyf8LyfbawKBgQC2lksnJeDAmG7CmsW/J9DY16Z2Tji7jpsYmOifBy2oExd4WbJ6N81qdtaZ08zk4ZO/0JEaJGNsiVZxp2RBKUYd1aFcWbGXnU9pTx3xofvWBwa3AZdONlasFiVMaEnIe5BtkcgHGOpVp8DXRkbOIzf9Foe33eKmtzpEoH3bnvju+QKBgGHjr0mS9jd0q5kUTQ/JmIquZayxiWTVrE6AeFUBL92TrECRcthN41rtXfksg7BGWHMkIxl5fNSzAcNaabhR51kCXxfMCIycUm62QRO7jZkVOA70SJ9mHptrBiIdUJ4Y545bJZNiRf6YZWjKXUfEQxHUU97FmsncmDEhSAn4YlQfAoGAKp+NLbde7zbmbFF7JjOiQmydNHLwNYCF3kkbzLv9QYYrbXHrAzLxpg/V6xUReMDcH+QNa3/hId4xqth7w+ZhsudDAu8BUTZs6lWI1PaOdpkvTRt2+dAfjY6h6GNUDhQiVCG7A0UitxoxH1ANiWOZV2CiEB8hIPAXDxw0JUfILukCgYEAow8LcCK/uZPZaNH9jyqOs/xKjZ1iFvB7F47gu5nqQR0cVVE13/RJn+4Qfdp6ag5FiPaUUvx3yg1PQBzplWDSV1iRn7/5Z2XTr9HxQ8dvbLG2lrLmKA5sLR9VwKqLbIR2JigNb0TKm73oYPzFU7bXF55JZQYsNpCRSc4zww56Pfw=
    public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjgeLoN0PRQKIQb8GYvzhfCvee1x5pBkLF+dRpNhDAUBhUlMx/EQzxUdq2uf8rCAYV5QMWhgqtr84phuymzuRSQ/sr6NfS83AmTlsl7Hk4PkjOcDVyhbsT3KNpZU/Xyz5fw4mr1ibDZjcb/E9f7vcUuxfxwJQb0MgWQt4jtgFgFb4eppjgwmaxakyv8/3GiIvjt8NHjdK68dn3v/y4jRWTv9CVbuu3IbEb4rLLfmk6vjOdCTdVBG5j7I8DNcwQCRjgoYryzI83DM9Ub7pBwpAdIn2wmGNSxyae87ccXUVNA6JJSoeozXMPFFSpZQbPKYUWR9bFaTqgfvAsndAsBhAkQIDAQAB
    expire-time: 10
    qrcode-width: 138
# 微信支付
wxpay:
    api-v3-key: f5twhN71r6QtbQBCkPrb9P9p24KhJwgh
    # 小程序
    appid: wx8c10c99edf9d8908
    carrier-appid: wxc4bf3cdba4aff288
    # 移动端
    application-id: wx4a3ec0e66f85ddcd
    carrier-application-id: wxac3ce68b24e9044d
    domain: https://api.mch.weixin.qq.com
    merchant-id: 1667103453
    merchant-serial-number: 4D1B741008220085A2182A8FB968C69EE39FF763
    notify-url: http://fa46en.natappfree.cc/payment/wxpay/callback
    private-key-path: ${user.dir}/zhihaoscm-server/zhihaoscm-service/src/main/resources/pay/wxpay/apiclient_key.pem
    refund-url: http://fa46en.natappfree.cc/payment/wxpay/refund/callback
    expire-time: 10

vector-store:
    redis:
        mode: redisearch
    redis-url: redis://***************:6378
    embedding-model-app-id: sk-75db93b87063407c8a52116ba4f11ab9

llm:
    vision:
        api-key: sk-7cbc925d0e864f23b1f6bedcbce2490f
img-classify:
    url: http://quickstart-20240910-h9v9.1411403821482727.cn-hangzhou.pai-eas.aliyuncs.com
    token: ZTJiMWM5ZmJlMmM4MjcwYjg2OTA0ZWJjNzBjMjBhODYxMTI4OTg0Ng==
    strategy: model_inference
coze:
    bot-id: 7419140452311564340
    token: pat_ODMHsdybIEkr8ZtWJ4uJjqmfoKiJUENcxQHoOmo5mcAKQU8PZcqrqusF1VaWoGXZ
    callUpperLimit: 5
    dailyReviewWorkflowId: 7424105514692657162
    dailyReviewImgId: 180000
    warehouseStateWorkflowId: 7443723357662560308
    generateSessionTitleWorkflowId: 7448105226823745588
    goodsTypeConvertWorkflowId: 7503830307424256015
    ocr-ship-business-workflow-id: 7512348343366254619
    read-timeout: 15000
    connect-timeout: 15000
    ocr-crew-certificate-workflow-id: 7514513559800397833
    ocr-ship-env-prot-cert-workflow-id: 7516452248034967606
# 加密配置
encrypt:
    secret-key: 1234567891234567
    url-patterns:
        - /custom/product-type-index/detail
        - /custom/shipping/price/index/*
        - /custom/information/detail/*
        - /custom/sand-academy/vo/*

#一句话
nls:
    access-key-id: LTAI5tJoqrKhrmihfv1ctpgt
    access-key-secret: ******************************
    appKey: IhxWxroGOxzqsl6N

# 天地图
tianditu:
    accessKey: 85814748d871d987b3f0587d04715c96
    url: http://api.tianditu.gov.cn

# 高德
gaode:
    access-key: 2c93a1b3e2cb4e43e173c50caa49792d
    url: https://restapi.amap.com

#AI热词
hot-word:
    regionId: cn-shanghai
    akId: LTAI5tJoqrKhrmihfv1ctpgt
    akSecret: ******************************

# 图像推理
imgInference:
    classificationUrl: http://***************:8086
    regressionUrl: http://***************:8087
# 长江航道局网站的证书地址
certificate:
    chang-jiang-waterway: certificate/cjhdj.com.cn.pem
# 自动生成资讯的图片Id
info-img:
    water-level: 182001
    waterway-notice: 182002

# app版本
app:
    config:
        versions:
            - version: "1.0.0"
              type: "app"
              client-type: "android"
              url: "www.baidu.com"
            - version: "1.0.0"
              type: "carrier-app"
              client-type: "android"
              url: "www.baidu.com"
            - version: "1.0.0"
              type: "app"
              client-type: "ios"
              url: "www.baidu.com"
            - version: "1.0.0"
              type: "carrier-app"
              client-type: "ios"
              url: "www.baidu.com"





# 冈好运信息
tran-sport:
    id: 99999999999


sanyi:
    #    url: http://*************:9999
    #    url: https://ys.31help.com:9997
    url: https://autohub.zhihaoscm.com/
    #    url: https://www.chinamdvr.com:9997/
    username: admin
    #    username: zhihaoscm
    password: e10adc3949ba59abbe56e057f20f883e
    #    password: 670b14728ad9902aecba32e22fa4f6bd
    #    media-ip: ***************
    media-ip: *************
    media-port: 9990
    intercom-port: 9989
    teamId: 2
    #    teamId: 10153
    #    teamId: 1178
    keyType: 2
    sub-msg-ids: "0300|0101|0301"
    #    sub-msg-ids: "0300"
    push-host: autohub.zhihaoscm.com
    push-pwd: 123456
    push-user-name: admin
    push-port: 10100

# 缓存时间配置
cache:
    config:
        ttl: 10

# 船务专员配置
shipping-handler:
    handler-ids:
        - 248
        - 292
        - 101
        - 104
        - 369
        - 382

# 黄码港
hmg:
    url: https://test.hmgszkj.com/sotms/api/v1
    appId: 83e37ebd81e34869a0bff70bc7717f5c
    appKey: 0ecjY1u9vZSVKqhYzL75QBpuY22Oen20
    accountNo: zhapi
    notify-url: https://dev.api.zhihaoscm.cn/admin/transport/order/ship/callback
#识别银行卡
bank-card:
    app-code: 5e77fea8fe414f5c8c776e3c513fa17e
    url: https://hjcnaps.market.alicloudapi.com

# 拼团商品
group:
    products:
        - id: 1
          productTypeId: "000512"
          name: "牛车河砂（江阴靠港价）"
          productTypeName: "牛车河砂"
          productTypeShortName: "牛车河砂"
          spec: "2.0-2.4"
          area: "湖北省黄冈市团风县牛车河水库采区"
          areaAbbreviation: "黄冈团风"
          groupPrice: 52
          originalPrice: 54
          groupRequirement: "20"
          priceType: "江阴靠港价"
          delivery: "江苏江阴长虹国际码头"
          siltContent: "0.61%"
          crushValue: "19.4%"
          moistureContent: "1.3%"
          finePowderContent: "6%"
          description: "牛车河砂产自湖北省黄冈市团风县牛车河水库，源自天然古河道，经河水长期冲刷筛选而成，品质卓越，是建筑及工业领域的优质材料。牛车河砂有五大优势：1、产量大、供应稳定、质量稳定，每天可生产 2 万吨，质量一致性高；2、性价比高，与天然砂质量一致，但价格堪比机制砂。3、含泥量极低，有效减少对混凝土等产品强度和耐久性的负面影响，确保建筑结构稳固；4、细粉含量适中，既保证材料的填充性，又不会因细粉过多导致需水量增加、工作性能变差，能提升产品综合性能；5、含水率稳定且较低，在使用过程中能更好地控制材料配比，避免因水分波动影响产品质量，保障施工和生产的稳定性。"
          remark: "以上价格为江苏省江阴市江苏长宏国际港口靠港价格"
          imageUrl: "https://static.zhihaoscm.cn/miniprogram/tuan-produc/1.png"
          supplier: "江西志豪新材料集团有限公司（厂家直营）"
          groupMinTons: 1
          deadline: "2025-08-30T00:00:00"

        - id: 2
          productTypeId: "001530"
          name: "罗田水洗砂（产地上船价）"
          productTypeName: "水洗砂"
          productTypeShortName: "水洗砂"
          spec: "2.0-2.3"
          area: "湖北省黄冈市罗田县广源集团渣土处置中心"
          areaAbbreviation: "黄冈罗田"
          groupPrice: 45
          originalPrice: 47
          groupRequirement: "10"
          priceType: "产地上船价"
          delivery: "湖北省黄冈市楚江码头"
          siltContent: "2.52%"
          crushValue: "24%"
          moistureContent: "8.42%"
          finePowderContent: "23.4%"
          description: "水洗砂主要采区位于湖北省黄冈市罗田县，细数模度2.2-2.3，压碎值较高，含泥量<2%、含水量<1%，表面很均匀，颗粒表面有棱角。建筑公司是水洗砂的主要客户之一，它们需要大量的砂石骨料来制作混凝土，用于各种建筑和基础设施项目。预制构件厂需要使用水洗砂作为原料，生产混凝土预制构件，如梁、板、柱等。"
          remark: "以上价格为湖北省黄冈市黄州区楚江码头上船价格"
          imageUrl: "https://static.zhihaoscm.cn/miniprogram/tuan-produc/2.png"
          supplier: "罗田渣土处置中心、罗田百纳（厂家直营）"
          groupMinTons: 1
          deadline: "2025-08-30T00:00:00"

        - id: 3
          productTypeId: ""
          name: "浠水鹅卵石机制砂（产地上船价）"
          productTypeName: "鹅卵石机制砂"
          productTypeShortName: "鹅卵石机制砂"
          spec: "0-3mm"
          area: "黄冈市浠水县湖北瑞朗新型建材有限公司"
          areaAbbreviation: "黄冈浠水"
          groupPrice: 53
          originalPrice: 55
          groupRequirement: "10"
          priceType: "产地上船价"
          delivery: "湖北省黄冈市浠水县兰溪码头"
          siltContent: "1.76%"
          crushValue: "21.9%"
          moistureContent: "6.55%"
          finePowderContent: "22%"
          description: "鹅卵石机制砂是一种以鹅卵石为原料，通过破碎、筛分、制砂等工艺制成的建筑用砂。浠水县鹅卵石机制砂经过多次破碎和筛分，颗粒大小均匀，不含过多细颗粒和粉尘，硬度高、抗压性强，制成的机制砂具有较高的强度和稳定性，在自然环境中经过长期的水流冲刷和摩擦，耐磨性好，制成的机制砂也继承了这一特性。"
          remark: "以上价格为湖北省黄冈市浠水县兰溪码头上船价格"
          imageUrl: "https://static.zhihaoscm.cn/miniprogram/tuan-produc/3.png"
          supplier: "湖北瑞朗新型建材有限公司（厂家直营）"
          groupMinTons: 1
          deadline: "2025-08-30T00:00:00"

        - id: 4
          productTypeId: "001310"
          name: "鄱阳湖砂（芜湖靠港价）"
          productTypeName: "鄱阳湖砂"
          productTypeShortName: "鄱阳湖砂"
          spec: "2.0-2.8"
          area: "江西省鄱阳湖九江水域都昌可采区"
          areaAbbreviation: "鄱阳湖"
          groupPrice: 58
          originalPrice: 60
          groupRequirement: "30"
          priceType: "芜湖靠港价"
          delivery: "安徽省芜湖市中洋码头"
          siltContent: "1.01%"
          crushValue: "17.9%"
          moistureContent: "7.28%"
          finePowderContent: "24%"
          description: "鄱阳湖砂主采区位于江西省九江市鄱阳湖湖域规划可采区内，鄱阳湖砂的开采受到严格的规划和监管，2025年鄱阳湖主要在都昌规划年开采量为1600万吨，开采时间为2025年7月1日至12月31日。鄱阳湖砂因质量优越，是可用于高标建筑和基础设施建设的材料，因其质量稳定、供应稳定而受到市场的青睐‌。"
          remark: "以上价格为安徽省芜湖市中洋码头靠港价格"
          imageUrl: "https://static.zhihaoscm.cn/miniprogram/tuan-produc/4.png"
          supplier: "安徽启源建材商贸有限公司（贸易商）"
          groupMinTons: 1
          deadline: "2025-08-30T00:00:00"

        - id: 5
          productTypeId: "001110"
          name: "洞庭湖砂（产地上船价）"
          productTypeName: "洞庭湖砂"
          productTypeShortName: "鄱阳湖砂"
          spec: "2.0-2.8"
          area: "湖南省岳阳市湘阴县易婆塘采区"
          areaAbbreviation: "岳阳湘阴"
          groupPrice: 43
          originalPrice: 45
          groupRequirement: "35"
          priceType: "产地上船价"
          delivery: "湖南省岳阳市湘阴县虞公港"
          siltContent: "0.86%"
          crushValue: "12.2%"
          moistureContent: "6.44%"
          finePowderContent: "14%"
          description: "洞庭湖砂，主采区位于湖南省环洞庭湖的常德、岳阳、益阳三大湖区市，其中，湘阴易婆塘采区位于岳阳市湘阴县。洞庭湖砂因其粒度均匀、强度和稳定性优于其他地区的河砂，是理想的建筑材料。其高品质使得洞庭湖砂在市场上具有较高的需求和价格。"
          remark: "以上价格为湖南省岳阳市湘阴县虞公港上船价格"
          imageUrl: "https://static.zhihaoscm.cn/miniprogram/tuan-produc/5.png"
          supplier: "湘阴县广羽贸易有限公司（厂家直营）"
          groupMinTons: 1
          deadline: "2025-08-30T00:00:00"

        - id: 6
          productTypeId: ""
          name: "英山钾长石机制砂（产地上船价）"
          productTypeName: "钾长石机制砂"
          productTypeShortName: "钾长石机制砂"
          spec: "0-3mm"
          area: "湖北省黄冈市英山县施家湖采石场有限公司"
          areaAbbreviation: "黄冈英山"
          groupPrice: 55
          originalPrice: 57
          groupRequirement: "10"
          priceType: "产地上船价"
          delivery: "湖北省黄冈市楚江码头"
          siltContent: "0.5%"
          crushValue: "13%"
          moistureContent: "5.3%"
          finePowderContent: "9%"
          description: "钾长石机制砂是一种以钾长石为原料，通过破碎、筛分等工艺制成的砂状建筑材料。钾长石是一种富钾的硅酸盐矿物，具有熔点低、熔融间隔时间长、熔融粘度高、流动性小等特点。这些特性使其在玻璃、陶瓷等行业中具有重要应用价值。钾长石机制砂因其良好的物理性能，可广泛应用于建筑、混凝土搅拌站等领域，能够替代天然河砂。此外，钾长石机制砂还可用于玻璃、陶瓷等行业。"
          remark: "以上价格为湖北省黄冈市黄州区楚江码头上船价格"
          imageUrl: "https://static.zhihaoscm.cn/miniprogram/tuan-produc/6.png"
          supplier: "英山县施家湖采石场有限公司（厂家直营）"
          groupMinTons: 1
          deadline: "2025-08-30T00:00:00"

        - id: 7
          productTypeId: ""
          name: "机制砂（产地上船价）"
          productTypeName: "机制砂"
          productTypeShortName: "机制砂"
          spec: "0-5mm"
          area: "湖北省鄂州市鄂城区临空经济区杨叶镇团山村工业园"
          areaAbbreviation: "鄂州鄂城"
          groupPrice: 42
          originalPrice: 44
          groupRequirement: "20"
          priceType: "产地上船价"
          delivery: "湖北省鄂州市五丈港"
          siltContent: "2.5%"
          crushValue: "24%"
          moistureContent: "18%"
          finePowderContent: "7.84%"
          description: "黑色机制砂以其耐磨性和良好的物理性能，在建筑和基础设施建设中具有广泛的应用前景。同时，它在装饰和特殊应用领域也展现出独特的优势。"
          remark: "以上价格为湖北省鄂州市五丈港上船价格"
          imageUrl: "https://static.zhihaoscm.cn/miniprogram/tuan-produc/7.png"
          supplier: "湖北瓦精建筑工程有限公司（厂家直营）"
          groupMinTons: 1
          deadline: "2025-08-30T00:00:00"