<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhihaoscm.service.core.mapper.OrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhihaoscm.domain.bean.entity.Order">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="del" property="del" />
        <result column="origin" property="origin" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="order_type" property="orderType" />
        <result column="product_id" property="productId" />
        <result column="product_info" property="productInfo" />
        <result column="customer_id" property="customerId" />
        <result column="customer_enterprise" property="customerEnterprise" />
        <result column="address_id" property="addressId" />
        <result column="address_info" property="addressInfo" />
        <result column="ton" property="ton" />
        <result column="unit_price" property="unitPrice" />
        <result column="estimated_amount" property="estimatedAmount" />
        <result column="pay_amount" property="payAmount" />
        <result column="refund_amount" property="refundAmount" />
        <result column="Locking_time" property="lockingTime" />
        <result column="handler_id" property="handlerId" />
        <result column="handler_name" property="handlerName" />
        <result column="remark" property="remark" />
    </resultMap>

</mapper>
