<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhihaoscm.service.core.mapper.SampleApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhihaoscm.domain.bean.entity.SampleApply">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="del" property="del" />
        <result column="origin" property="origin" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="product_id" property="productId" />
        <result column="customer_id" property="customerId" />
        <result column="address_id" property="addressId" />
        <result column="address_info" property="addressInfo" />
        <result column="remark" property="remark" />
        <result column="state" property="state" />
        <result column="logistics_number" property="logisticsNumber" />
        <result column="handle_remark" property="handleRemark" />
    </resultMap>

</mapper>
