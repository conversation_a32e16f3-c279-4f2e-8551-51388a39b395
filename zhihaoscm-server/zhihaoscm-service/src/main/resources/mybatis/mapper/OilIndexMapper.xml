<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhihaoscm.service.core.mapper.OilIndexMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhihaoscm.domain.bean.entity.OilIndex">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="del" property="del"/>
        <result column="origin" property="origin"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="version_id" property="versionId"/>
        <result column="version_date" property="versionDate"/>
        <result column="zero_diesel_listing_price" property="zeroDieselListingPrice"/>
        <result column="zero_diesel_actual_selling_price" property="zeroDieselActualSellingPrice"/>
        <result column="light_fuel_listing_price" property="lightFuelListingPrice"/>
        <result column="light_fuel_actual_selling_price" property="lightFuelActualSellingPrice"/>
    </resultMap>

    <resultMap id="OilIndexVoMap" type="com.zhihaoscm.domain.bean.vo.OilIndexVo">
        <result property="lightFuelPriceChangeAmount" column="lightFuelPriceChangeAmount"/>
        <result property="zeroDieselPriceChangeAmount" column="zeroDieselPriceChangeAmount"/>
        <association property="oilIndex" javaType="com.zhihaoscm.domain.bean.entity.OilIndex">
            <id property="id" column="oil_index_id"/>
            <result property="del" column="oil_index_del"/>
            <result property="tenantId" column="oil_index_tenant_id"/>
            <result property="origin" column="oil_index_origin"/>
            <result property="createdBy" column="oil_index_created_by"/>
            <result property="createdTime" column="oil_index_created_time"/>
            <result property="updatedBy" column="oil_index_updated_by"/>
            <result property="updatedTime" column="oil_index_updated_time"/>
            <result property="oilSiteId" column="oil_site_id"/>
            <result property="versionId" column="version_id"/>
            <result property="versionDate" column="version_date"/>
            <result property="zeroDieselListingPrice" column="zero_diesel_listing_price"/>
            <result property="zeroDieselActualSellingPrice" column="zero_diesel_actual_selling_price"/>
            <result property="lightFuelListingPrice" column="light_fuel_listing_price"/>
            <result property="lightFuelActualSellingPrice" column="light_fuel_actual_selling_price"/>
        </association>
        <association property="oilSite" javaType="com.zhihaoscm.domain.bean.entity.OilSite">
            <id property="id" column="oil_site_id"/>
            <result property="del" column="oil_site_del"/>
            <result property="tenantId" column="oil_site_tenant_id"/>
            <result property="origin" column="oil_site_origin"/>
            <result property="createdBy" column="oil_site_created_by"/>
            <result property="createdTime" column="oil_site_created_time"/>
            <result property="updatedBy" column="oil_site_updated_by"/>
            <result property="updatedTime" column="oil_site_updated_time"/>
            <result property="name" column="oil_site_name"/>
            <result property="brand" column="brand"/>
            <result property="sitePhone" column="site_phone"/>
            <result property="phone" column="phone"/>
            <result property="quantity" column="quantity"/>
            <result property="provinceCode" column="province_code"/>
            <result property="cityCode" column="city_code"/>
            <result property="regionCode" column="region_code"/>
            <result property="provinceName" column="province_name"/>
            <result property="cityName" column="city_name"/>
            <result property="regionName" column="region_name"/>
            <result property="address" column="address"/>
            <result property="latLon" column="lat_lon"/>
            <result property="state" column="state"/>
        </association>
    </resultMap>

    <select id="customPaging" resultMap="OilIndexVoMap">
        SELECT * FROM (
        WITH valid_data AS (
        SELECT i.id as oil_index_id,
        i.del as oil_index_del,
        i.tenant_id as oil_index_tenant_id,
        i.origin as oil_index_origin,
        i.created_by as oil_index_created_by,
        i.created_time as oil_index_created_time,
        i.updated_by as oil_index_updated_by,
        i.updated_time as oil_index_updated_time,
        i.oil_site_id,
        i.version_id,
        i.version_date,
        i.zero_diesel_listing_price,
        i.zero_diesel_actual_selling_price,
        i.light_fuel_listing_price,
        i.light_fuel_actual_selling_price,
        s.id as oil_site_id,
        s.del as oil_site_del,
        s.tenant_id as oil_site_tenant_id,
        s.origin as oil_site_origin,
        s.created_by as oil_site_created_by,
        s.created_time as oil_site_created_time,
        s.updated_by as oil_site_updated_by,
        s.updated_time as oil_site_updated_time,
        s.name as oil_site_name,
        s.brand,
        s.site_phone,
        s.phone,
        s.quantity,
        s.province_code,
        s.province_name,
        s.city_code,
        s.city_name,
        s.region_code,
        s.region_name,
        s.address,
        s.lat_lon,
        s.state
        FROM t_oil_index i
        JOIN t_oil_site s ON s.id = i.oil_site_id
        WHERE i.del = 0
        AND s.del = 0
        AND s.state = 1
        ),

        <!--每组区域品牌的最新版本号-->
        latest_version AS (
        SELECT province_code, city_code, brand, MAX(version_date) AS version_date
        FROM valid_data
        GROUP BY province_code, city_code, brand
        ),

        <!-- 每组区域品牌的上一次版本号-->
        previous_version AS (
        SELECT vd.province_code, vd.city_code, vd.brand,
        MAX(vd.version_date) AS previous_version_date
        FROM valid_data vd
        JOIN latest_version lv ON vd.province_code = lv.province_code
        AND vd.city_code = lv.city_code
        AND vd.brand = lv.brand
        WHERE vd.version_date &lt; lv.version_date
                                GROUP BY vd.province_code, vd.city_code, vd.brand
        ),

        <!--最新版本下的挂牌价最低值-->
        latest_group AS (
        SELECT vd.province_code,
        vd.province_name,
        vd.city_code,
        vd.city_name,
        vd.brand,
        lv.version_date,
        MIN(vd.zero_diesel_listing_price) AS zero_diesel_listing_price,
        MIN(vd.light_fuel_listing_price) AS light_fuel_listing_price
        FROM valid_data vd
        JOIN latest_version lv ON vd.province_code = lv.province_code
        AND vd.city_code = lv.city_code
        AND vd.brand = lv.brand
        AND vd.version_date = lv.version_date
        GROUP BY vd.province_code, vd.province_name, vd.city_code, vd.city_name, vd.brand, lv.version_date
        ),

        <!--上一版本下的挂牌价最低值-->
        previous_group AS (
        SELECT vd.province_code,
        vd.province_name,
        vd.city_code,
        vd.city_name,
        vd.brand,
        pv.previous_version_date,
        MIN(vd.zero_diesel_listing_price) AS previous_min_zero_diesel_listing_price,
        MIN(vd.light_fuel_listing_price) AS previous_min_light_fuel_listing_price
        FROM valid_data vd
        JOIN previous_version pv ON vd.province_code = pv.province_code
        AND vd.city_code = pv.city_code
        AND vd.brand = pv.brand
        AND vd.version_date = pv.previous_version_date
        GROUP BY vd.province_code, vd.province_name, vd.city_code, vd.city_name, vd.brand, pv.previous_version_date
        )

        SELECT
        l.province_code,
        l.province_name,
        l.city_code,
        l.city_name,
        l.brand,
        l.version_date,
        p.previous_version_date,
        p.previous_min_zero_diesel_listing_price,
        p.previous_min_light_fuel_listing_price,
        l.zero_diesel_listing_price,
        l.light_fuel_listing_price,

        CASE
        WHEN l.zero_diesel_listing_price IS NOT NULL
        AND p.previous_min_zero_diesel_listing_price IS NOT NULL
        THEN CAST(l.zero_diesel_listing_price AS DECIMAL(10, 2)) -
        CAST(p.previous_min_zero_diesel_listing_price AS DECIMAL(10, 2))
        ELSE NULL
        END AS zeroDieselPriceChangeAmount,

        CASE
        WHEN l.light_fuel_listing_price IS NOT NULL
        AND p.previous_min_light_fuel_listing_price IS NOT NULL
        THEN CAST(l.light_fuel_listing_price AS DECIMAL(10, 2)) -
        CAST(p.previous_min_light_fuel_listing_price AS DECIMAL(10, 2))
        ELSE NULL
        END AS lightFuelPriceChangeAmount

        FROM latest_group l
        LEFT JOIN previous_group p
        ON l.province_code = p.province_code
        AND l.city_code = p.city_code
        AND l.brand = p.brand
        ) AS temp
        <where>
            <if test="provinceCode!=null and provinceCode!=''">
                AND temp.province_code = #{provinceCode}
            </if>
            <if test="brand!=null and brand!=''">
                AND temp.brand like concat('%',#{brand},'%')
            </if>
        </where>
        ORDER BY CONVERT(temp.province_name USING gbk), CONVERT(temp.city_name USING gbk)
    </select>
    <select id="findByProvinceCodeAndCityCodeAndBrand"
            resultMap="BaseResultMap">
        WITH ranked_index AS (
        SELECT
        i.*,
        s.province_code,
        s.province_name,
        s.city_code,
        s.city_name,
        s.brand
        FROM
        t_oil_index i
        JOIN t_oil_site s ON s.id = i.oil_site_id
        WHERE
        i.del = 0
        AND s.del = 0
        AND s.state = 1
        ),
        latest_version_per_group AS (
        SELECT
        province_code,
        city_code,
        brand,
        MAX(version_date) AS version_date
        FROM
        ranked_index
        GROUP BY
        province_code, city_code, brand
        ),
        latest_records AS (
        -- 只保留每组的最新 version_date 下的所有记录
        SELECT r.*
        FROM ranked_index r
        JOIN latest_version_per_group v
        ON r.province_code = v.province_code
        AND r.city_code = v.city_code
        AND r.brand = v.brand
        AND r.version_date = v.version_date
        ),
        lowest_price_per_group AS (
        SELECT
        province_code,
        province_name,
        city_code,
        city_name,
        brand,
        version_date,
        MIN(zero_diesel_listing_price) AS zero_diesel_listing_price,
        MIN(zero_diesel_actual_selling_price) AS zero_diesel_actual_selling_price,
        MIN(light_fuel_listing_price) AS light_fuel_listing_price,
        MIN(light_fuel_actual_selling_price) AS light_fuel_actual_selling_price
        FROM
        latest_records
        GROUP BY
        province_code,
        province_name,
        city_code,
        city_name,
        brand,
        version_date
        )
        SELECT *
        FROM lowest_price_per_group
        <where>
            <if test="provinceCode!=null and provinceCode!=''">
                AND province_code = #{provinceCode}
            </if>
            <if test="cityCode!=null and cityCode!=''">
                AND city_code = #{cityCode}
            </if>
            <if test="brand!=null and brand!=''">
                AND brand = #{brand}
            </if>
        </where>
    </select>

    <select id="findByVersionDate" resultMap="BaseResultMap">
        WITH ranked_daily_index AS (
            SELECT
                i.*,
                s.province_code,
                s.province_name,
                s.city_code,
                s.city_name,
                s.brand,
                ROW_NUMBER() OVER (PARTITION BY s.id, i.version_date ORDER BY i.version_date DESC) AS rn
            FROM t_oil_index i
                JOIN t_oil_site s ON s.id = i.oil_site_id
        WHERE i.del = 0
          AND s.del = 0
          AND s.state = 1),

            <!--每个站点每天的最新版本-->
            latest_daily_site_price AS (
            SELECT *
            FROM ranked_daily_index
            WHERE rn = 1)
        <!--聚合每天每个城市品牌下的价格-->
        SELECT
            version_date,
            province_code,
            province_name,
            city_code,
            city_name,
            brand,
            MIN(zero_diesel_listing_price)         AS zero_diesel_listing_price,
            MIN(zero_diesel_actual_selling_price)  AS zero_diesel_actual_selling_price,
            MIN(light_fuel_listing_price)          AS light_fuel_listing_price,
            MIN(light_fuel_actual_selling_price)   AS light_fuel_actual_selling_price
        FROM latest_daily_site_price
        <where>
            <if test="provinceCode!=null and provinceCode!=''">
                AND province_code = #{provinceCode}
            </if>
            <if test="cityCode!=null and cityCode!=''">
                AND city_code = #{cityCode}
            </if>
            <if test="brand!=null and brand!=''">
                AND brand = #{brand}
            </if>
            <if test="beginTime!=null">
                AND version_date &gt;= #{beginTime}
            </if>
            <if test="endTime!=null">
                AND version_date &lt;= #{endTime}
            </if>
        </where>
        GROUP BY version_date, province_code, province_name, city_code, city_name, brand
        ORDER BY version_date DESC, province_name, city_name, brand;
    </select>

</mapper>
