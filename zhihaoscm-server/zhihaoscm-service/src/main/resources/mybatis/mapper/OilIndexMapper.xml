<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhihaoscm.service.core.mapper.OilIndexMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhihaoscm.domain.bean.entity.OilIndex">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="del" property="del"/>
        <result column="origin" property="origin"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="version_id" property="versionId"/>
        <result column="version_date" property="versionDate"/>
        <result column="zero_diesel_listing_price" property="zeroDieselListingPrice"/>
        <result column="zero_diesel_actual_selling_price" property="zeroDieselActualSellingPrice"/>
        <result column="light_fuel_listing_price" property="lightFuelListingPrice"/>
        <result column="light_fuel_actual_selling_price" property="lightFuelActualSellingPrice"/>
    </resultMap>

    <resultMap id="OilIndexVoMap" type="com.zhihaoscm.domain.bean.vo.OilIndexVo">
        <result property="lightFuelPriceChangeAmount" column="lightFuelPriceChangeAmount"/>
        <result property="zeroDieselPriceChangeAmount" column="zeroDieselPriceChangeAmount"/>
        <association property="oilIndex" javaType="com.zhihaoscm.domain.bean.entity.OilIndex" autoMapping="true">
        </association>
        <association property="oilSite" javaType="com.zhihaoscm.domain.bean.entity.OilSite" autoMapping="true">
            <result property="provinceCode" column="province_code" />
            <result property="provinceName" column="province_name" />
            <result property="cityCode" column="city_code" />
            <result property="cityName" column="city_name" />
            <result property="brand" column="brand" />
        </association>
    </resultMap>

    <select id="customPaging" resultMap="OilIndexVoMap">
        SELECT * FROM (
        WITH valid_data AS (
        SELECT i.*,
        s.province_code,
        s.province_name,
        s.city_code,
        s.city_name,
        s.brand
        FROM t_oil_index i
        JOIN t_oil_site s ON s.id = i.oil_site_id
        WHERE i.del = 0
        AND s.del = 0
        AND s.state = 1
        ),

        <!--每组区域品牌的最新版本号-->
        latest_version AS (
        SELECT province_code, city_code, brand, MAX(version_date) AS version_date
        FROM valid_data
        GROUP BY province_code, city_code, brand
        ),

        <!-- 每组区域品牌的上一次版本号-->
        previous_version AS (
        SELECT vd.province_code, vd.city_code, vd.brand,
        MAX(vd.version_date) AS previous_version_date
        FROM valid_data vd
        JOIN latest_version lv ON vd.province_code = lv.province_code
        AND vd.city_code = lv.city_code
        AND vd.brand = lv.brand
        WHERE vd.version_date &lt; lv.version_date
                                GROUP BY vd.province_code, vd.city_code, vd.brand
        ),

        <!--最新版本下的挂牌价最低值-->
        latest_group AS (
        SELECT vd.province_code,
        vd.province_name,
        vd.city_code,
        vd.city_name,
        vd.brand,
        lv.version_date,
        MIN(vd.zero_diesel_listing_price) AS zero_diesel_listing_price,
        MIN(vd.light_fuel_listing_price) AS light_fuel_listing_price
        FROM valid_data vd
        JOIN latest_version lv ON vd.province_code = lv.province_code
        AND vd.city_code = lv.city_code
        AND vd.brand = lv.brand
        AND vd.version_date = lv.version_date
        GROUP BY vd.province_code, vd.province_name, vd.city_code, vd.city_name, vd.brand, lv.version_date
        ),

        <!--上一版本下的挂牌价最低值-->
        previous_group AS (
        SELECT vd.province_code,
        vd.province_name,
        vd.city_code,
        vd.city_name,
        vd.brand,
        pv.previous_version_date,
        MIN(vd.zero_diesel_listing_price) AS previous_min_zero_diesel_listing_price,
        MIN(vd.light_fuel_listing_price) AS previous_min_light_fuel_listing_price
        FROM valid_data vd
        JOIN previous_version pv ON vd.province_code = pv.province_code
        AND vd.city_code = pv.city_code
        AND vd.brand = pv.brand
        AND vd.version_date = pv.previous_version_date
        GROUP BY vd.province_code, vd.province_name, vd.city_code, vd.city_name, vd.brand, pv.previous_version_date
        )

        SELECT
        l.province_code,
        l.province_name,
        l.city_code,
        l.city_name,
        l.brand,
        l.version_date,
        p.previous_version_date,
        p.previous_min_zero_diesel_listing_price,
        p.previous_min_light_fuel_listing_price,
        l.zero_diesel_listing_price,
        l.light_fuel_listing_price,

        CASE
        WHEN l.zero_diesel_listing_price IS NOT NULL
        AND p.previous_min_zero_diesel_listing_price IS NOT NULL
        THEN CAST(l.zero_diesel_listing_price AS DECIMAL(10, 2)) -
        CAST(p.previous_min_zero_diesel_listing_price AS DECIMAL(10, 2))
        ELSE NULL
        END AS zeroDieselPriceChangeAmount,

        CASE
        WHEN l.light_fuel_listing_price IS NOT NULL
        AND p.previous_min_light_fuel_listing_price IS NOT NULL
        THEN CAST(l.light_fuel_listing_price AS DECIMAL(10, 2)) -
        CAST(p.previous_min_light_fuel_listing_price AS DECIMAL(10, 2))
        ELSE NULL
        END AS lightFuelPriceChangeAmount

        FROM latest_group l
        LEFT JOIN previous_group p
        ON l.province_code = p.province_code
        AND l.city_code = p.city_code
        AND l.brand = p.brand
        ) AS temp
        <where>
            <if test="provinceCode!=null and provinceCode!=''">
                AND temp.province_code = #{provinceCode}
            </if>
            <if test="brand!=null and brand!=''">
                AND temp.brand like concat('%',#{brand},'%')
            </if>
        </where>
        ORDER BY CONVERT(temp.province_name USING gbk), CONVERT(temp.city_name USING gbk)
    </select>
    <select id="findByProvinceCodeAndCityCodeAndBrand"
            resultMap="BaseResultMap">
        WITH ranked_index AS (
        SELECT
        i.*,
        s.province_code,
        s.province_name,
        s.city_code,
        s.city_name,
        s.brand
        FROM
        t_oil_index i
        JOIN t_oil_site s ON s.id = i.oil_site_id
        WHERE
        i.del = 0
        AND s.del = 0
        AND s.state = 1
        ),
        latest_version_per_group AS (
        SELECT
        province_code,
        city_code,
        brand,
        MAX(version_date) AS version_date
        FROM
        ranked_index
        GROUP BY
        province_code, city_code, brand
        ),
        latest_records AS (
        -- 只保留每组的最新 version_date 下的所有记录
        SELECT r.*
        FROM ranked_index r
        JOIN latest_version_per_group v
        ON r.province_code = v.province_code
        AND r.city_code = v.city_code
        AND r.brand = v.brand
        AND r.version_date = v.version_date
        ),
        lowest_price_per_group AS (
        SELECT
        province_code,
        province_name,
        city_code,
        city_name,
        brand,
        version_date,
        MIN(zero_diesel_listing_price) AS zero_diesel_listing_price,
        MIN(zero_diesel_actual_selling_price) AS zero_diesel_actual_selling_price,
        MIN(light_fuel_listing_price) AS light_fuel_listing_price,
        MIN(light_fuel_actual_selling_price) AS light_fuel_actual_selling_price
        FROM
        latest_records
        GROUP BY
        province_code,
        province_name,
        city_code,
        city_name,
        brand,
        version_date
        )
        SELECT *
        FROM lowest_price_per_group
        <where>
            <if test="provinceCode!=null and provinceCode!=''">
                AND province_code = #{provinceCode}
            </if>
            <if test="cityCode!=null and cityCode!=''">
                AND city_code = #{cityCode}
            </if>
            <if test="brand!=null and brand!=''">
                AND brand = #{brand}
            </if>
        </where>
    </select>

    <select id="findByVersionDate" resultMap="BaseResultMap">
        WITH ranked_daily_index AS (
            SELECT
                i.*,
                s.province_code,
                s.province_name,
                s.city_code,
                s.city_name,
                s.brand,
                ROW_NUMBER() OVER (PARTITION BY s.id, i.version_date ORDER BY i.version_date DESC) AS rn
            FROM t_oil_index i
                JOIN t_oil_site s ON s.id = i.oil_site_id
        WHERE i.del = 0
          AND s.del = 0
          AND s.state = 1),

            <!--每个站点每天的最新版本-->
            latest_daily_site_price AS (
            SELECT *
            FROM ranked_daily_index
            WHERE rn = 1)
        <!--聚合每天每个城市品牌下的价格-->
        SELECT
            version_date,
            province_code,
            province_name,
            city_code,
            city_name,
            brand,
            MIN(zero_diesel_listing_price)         AS zero_diesel_listing_price,
            MIN(zero_diesel_actual_selling_price)  AS zero_diesel_actual_selling_price,
            MIN(light_fuel_listing_price)          AS light_fuel_listing_price,
            MIN(light_fuel_actual_selling_price)   AS light_fuel_actual_selling_price
        FROM latest_daily_site_price
        <where>
            <if test="provinceCode!=null and provinceCode!=''">
                AND province_code = #{provinceCode}
            </if>
            <if test="cityCode!=null and cityCode!=''">
                AND city_code = #{cityCode}
            </if>
            <if test="brand!=null and brand!=''">
                AND brand = #{brand}
            </if>
            <if test="beginTime!=null">
                AND version_date &gt;= #{beginTime}
            </if>
            <if test="endTime!=null">
                AND version_date &lt;= #{endTime}
            </if>
        </where>
        GROUP BY version_date, province_code, province_name, city_code, city_name, brand
        ORDER BY version_date DESC, province_name, city_name, brand;
    </select>

</mapper>
