<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhihaoscm.service.core.mapper.ReceivePaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhihaoscm.domain.bean.entity.ReceivePayment">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="del" property="del" />
        <result column="origin" property="origin" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="payment_type" property="paymentType" />
        <result column="schedule_id" property="scheduleId" />
        <result column="customer_id" property="customerId" />
        <result column="customer_enterprise" property="customerEnterprise" />
        <result column="ton" property="ton" />
        <result column="ton_prove_file_id" property="tonProveFileId" />
        <result column="amount" property="amount" />
        <result column="pay_time" property="payTime" />
        <result column="pay_end_time" property="payEndTime" />
        <result column="remark" property="remark" />
        <result column="state" property="state" />
    </resultMap>

</mapper>
