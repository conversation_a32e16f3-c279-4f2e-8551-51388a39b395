package com.zhihaoscm.service.resource.validator.sample.apply;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.AddressInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.ProductInfo;
import com.zhihaoscm.domain.meta.biz.SampleApplyDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.SampleApplyService;
import com.zhihaoscm.service.core.service.usercenter.CustomerReceivingAddressService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.resource.form.sample.apply.SampleApplyForm;
import com.zhihaoscm.service.resource.form.sample.apply.SampleApplyHandleForm;
import com.zhihaoscm.service.resource.validator.product.consignment.ProductConsignmentValidator;

/**
 * 寄样校验器
 */
@Component
public class SampleApplyValidator {

	@Autowired
	private SampleApplyService service;

	@Autowired
	private CustomerReceivingAddressService customerReceivingAddressService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private ProductConsignmentValidator productConsignmentValidator;

	/**
	 * 校验是否存在
	 * 
	 * @param id
	 * @return
	 */
	public SampleApply validateExist(Long id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30197001));
	}

	/**
	 * 校验新增
	 * 
	 * @param form
	 * @return
	 */
	public SampleApply validateCreate(SampleApplyForm form) {
		// 判断用户是否已经认证
		if (!CommonDef.Symbol.YES.match(CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getApplyState())) {
			throw new BadRequestException(ErrorCode.CODE_30197009);
		}
		// 校验商品是否已经申请过
		List<SampleApply> sampleApplies = service.findByProductIdAndStates(
				form.getProductId(),
				List.of(SampleApplyDef.State.PENDING.getCode(),
						SampleApplyDef.State.SHIPPED.getCode()));
		if (CollectionUtils.isNotEmpty(sampleApplies)) {
			throw new BadRequestException(ErrorCode.CODE_30197010);
		}
		CustomerReceivingAddress customerReceivingAddress = customerReceivingAddressService
				.findById(form.getAddressId());
		if (Objects.isNull(customerReceivingAddress)) {
			throw new BadRequestException(ErrorCode.CODE_30197011);
		}
		// 校验商品信息并填充
		ProductConsignment productConsignment = productConsignmentValidator
				.validateExist(form.getProductId());
		ProductInfo productInfo = new ProductInfo();
		BeanUtils.copyProperties(productConsignment, productInfo);
		// 客户信息
		Customer customer = customerService.findOne(CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId())
				.orElse(new Customer());
		Enterprise customerEnterprise = new Enterprise();
		BeanUtils.copyProperties(customer, customerEnterprise);
		SampleApply sampleApply = form.convertToEntity();
		// 地址信息
		AddressInfo addressInfo = new AddressInfo();
		sampleApply.setCustomerEnterprise(customerEnterprise);
		BeanUtils.copyProperties(customerReceivingAddress, addressInfo);
		sampleApply.setAddressInfo(addressInfo);
		sampleApply.setProductInfo(productInfo);
		return sampleApply;
	}

	/**
	 * 校验处理
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public SampleApply validateHandle(Long id, SampleApplyHandleForm form) {
		SampleApply exist = this.validateExist(id);
		if (!SampleApplyDef.State.PENDING.match(exist.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30197012);
		}
		if (SampleApplyDef.State.SHIPPED.match(form.getState())) {
			if (Objects.isNull(form.getLogisticsNumber())) {
				throw new BadRequestException(ErrorCode.CODE_30197013);
			}
		}
		// 设置处理人信息
		SampleApply sampleApply = form.convertToEntity(exist);
		User user = UserContextHolder.getUser();
		sampleApply.setHandleId(Objects.requireNonNull(user).getId());
		sampleApply.setHandleName(Objects.requireNonNull(user).getName());
		return exist;
	}
}
