package com.zhihaoscm.service.resource.admin.information;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.dto.InformationDto;
import com.zhihaoscm.domain.bean.entity.Information;
import com.zhihaoscm.domain.bean.vo.InformationVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.domain.meta.error.ErrorCodeDef;
import com.zhihaoscm.service.core.service.InformationService;
import com.zhihaoscm.service.resource.form.information.InformationDelForm;
import com.zhihaoscm.service.resource.form.information.InformationForm;
import com.zhihaoscm.service.resource.validator.information.InformationValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "砂石资讯", description = "砂石资讯API")
@RestController
@RequestMapping("/information")
public class InformationResource {

	@Autowired
	private InformationService informationService;
	@Autowired
	private InformationValidator informationValidator;

	@GetMapping("/paging")
	@Operation(summary = "分页查询砂石资讯")
	@Secured({ AdminPermissionDef.INFO_W, AdminPermissionDef.INFO_R })
	public ApiResponse<Page<InformationVo>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "标题") @RequestParam(required = false) String title,
			@Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startTime,
			@Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endTime,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder,
			@Parameter(description = "分类：1:行业动态、2:分析报告、3每日评述") @RequestParam(required = false) Integer classify,
			@Parameter(description = "收费类型：1:免费内容、2:会员专享") @RequestParam(required = false) Integer feeType,
			@Parameter(description = "展示位置") @RequestParam(required = false) Integer showLocation,
			@Parameter(description = "PC端展示位置") @RequestParam(required = false) Integer pcShowLocation,
			@Parameter(description = "上架状态：0：已下架 1：已上架") @RequestParam(required = false) Integer state) {
		return new ApiResponse<>(PageUtil.convert(informationService.paging(
				page, size, title, startTime, endTime, sortKey, sortOrder,
				classify, feeType, showLocation, pcShowLocation, state)));
	}

	@Operation(summary = "分页查询相关砂石资讯")
	@GetMapping("/related-paging")
	public ApiResponse<Page<InformationVo>> relatedPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "品类id") @RequestParam String productTypeId) {
		return new ApiResponse<>(PageUtil.convert(
				informationService.relatedPaging(page, size, productTypeId)));
	}

	@GetMapping("/selector")
	@Operation(summary = "砂石咨询下拉列表")
	public ApiResponse<List<Information>> selector(
			@Parameter(description = "标题") @RequestParam(required = false) String title) {
		return new ApiResponse<>(informationService.selector(title));
	}

	@Operation(summary = "查找单个砂石资讯数据")
	@GetMapping("/{id}")
	@Secured({ AdminPermissionDef.INFO_W, AdminPermissionDef.INFO_R })
	public ApiResponse<InformationVo> findById(@PathVariable Long id) {
		InformationVo informationVo = informationService.detail(id, null)
				.orElse(null);
		if (Objects.isNull(informationVo)) {
			throw new BadRequestException(ErrorCodeDef.CODE_30047001.getCode());
		}
		return new ApiResponse<>(informationVo);
	}

	@Operation(summary = "新增")
	@PostMapping
	@Secured({ AdminPermissionDef.INFO_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = LogDef.INDEX_MANAGEMENT_SAND_NEWS_ADD, type = LogDef.INDEX_MANAGEMENT_SAND_NEWS, bizNo = "{{#_ret.getData().getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getTitle()}}") })
	public ApiResponse<Information> create(
			@Validated @RequestBody InformationForm informationForm) {
		informationValidator.validate(informationForm);
		InformationDto informationDto = informationForm
				.convertToDto(new Information());
		return new ApiResponse<>(informationService.create(
				informationDto.getInformation(),
				informationDto.getActiveFileIds(), informationDto.getTagIds()));
	}

	@Operation(summary = "修改")
	@PutMapping("/{id}")
	@Secured({ AdminPermissionDef.INFO_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.INDEX_MANAGEMENT_SAND_NEWS_EDIT, type = LogDef.INDEX_MANAGEMENT_SAND_NEWS, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#informationForm.getTitle()}}") })
	public ApiResponse<Void> update(@PathVariable Long id,
			@Validated @RequestBody InformationForm informationForm) {
		Information information = informationService.findOne(id).orElse(null);
		if (Objects.isNull(information)) {
			throw new BadRequestException(ErrorCodeDef.CODE_30047001.getCode());
		}
		informationValidator.validate(informationForm);
		InformationDto informationDto = informationForm
				.convertToDto(information);
		informationService.update(informationDto.getInformation(),
				informationDto.getActiveFileIds(),
				informationDto.getUnActiveFileIds(),
				informationDto.getTagIds());
		return new ApiResponse<>();
	}

	@Operation(summary = "删除")
	@DeleteMapping("/delete")
	@Secured({ AdminPermissionDef.INFO_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.DELETE, success = LogDef.INDEX_MANAGEMENT_SAND_NEWS_DELETE, type = LogDef.INDEX_MANAGEMENT_SAND_NEWS, bizNo = "{{#informationDelForm.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#informationTitle}}") })
	public ApiResponse<Void> delete(
			@Validated @RequestBody InformationDelForm informationDelForm) {
		InformationDto informationDto = informationDelForm.convertToDto();
		Information information = informationService
				.findOne(informationDelForm.getId()).orElse(new Information());
		LogRecordContext.putVariable("informationTitle",
				information.getTitle());
		informationService.delete(informationDto.getInformation().getId(),
				informationDto.getUnActiveFileIds());
		return new ApiResponse<>();
	}

	@Operation(summary = "更新状态")
	@PostMapping("/update/{id}/state/{state}")
	@Secured({ AdminPermissionDef.INFO_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = "{{#success}}", type = LogDef.INDEX_MANAGEMENT_SAND_NEWS, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#informationTitle}}") })
	public ApiResponse<Void> updateState(@PathVariable Long id,
			@Parameter(description = "1 上架 0 下架") @PathVariable Integer state) {
		informationValidator.validateDownShelf(id, state);
		informationService.updateState(id, state);
		Information information = informationService.findOne(id)
				.orElse(new Information());
		LogRecordContext.putVariable("informationTitle",
				information.getTitle());
		if (CommonDef.Symbol.YES.match(state)) {
			LogRecordContext.putVariable("success",
					LogDef.INDEX_MANAGEMENT_SAND_NEWS_PUT_ON_SHELF);
		} else {
			LogRecordContext.putVariable("success",
					LogDef.INDEX_MANAGEMENT_SAND_NEWS_PUT_OFF_SHELF);
		}
		return new ApiResponse<>();
	}

}
