package com.zhihaoscm.service.core.service.impl;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zhihaoscm.aliyun.ocr.sdk.protocol.AliyunOCRService;
import com.zhihaoscm.aliyun.ocr.sdk.res.BusinessLicenseInfo;
import com.zhihaoscm.aliyun.ocr.sdk.res.IdCardInfo;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.OCRService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class OCRServiceImpl implements OCRService {

	@Autowired
	private AliyunOCRService ocrService;


	@Override
	public Optional<BusinessLicenseInfo> recognizeBusinessLicense(File file) {
		Optional<BusinessLicenseInfo> businessLicenseInfo = ocrService
				.recognizeBusinessLicense(file.getPath());
		if (businessLicenseInfo.isEmpty()) {
			throw new BadRequestException(ErrorCode.CODE_30022021);
		}
		return businessLicenseInfo;
	}


	@Override
	public Optional<IdCardInfo> recognizeIdCardBackAndFace(File file){
		return ocrService.recognizeIdCardBackAndFace(file.getPath());
	}

}
