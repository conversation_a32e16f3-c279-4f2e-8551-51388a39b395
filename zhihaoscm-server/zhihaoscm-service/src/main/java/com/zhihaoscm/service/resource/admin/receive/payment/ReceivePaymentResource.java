package com.zhihaoscm.service.resource.admin.receive.payment;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.ReceivePayment;
import com.zhihaoscm.domain.bean.vo.ReceivePaymentVo;
import com.zhihaoscm.service.core.service.ReceivePaymentService;
import com.zhihaoscm.service.resource.form.receive.payment.ReceivePaymentForm;
import com.zhihaoscm.service.resource.validator.receive.payment.ReceivePaymentValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * <p>
 * 收款信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@RestController
@RequestMapping("/receive-payment")
public class ReceivePaymentResource {

	@Autowired
	private ReceivePaymentService service;

	@Autowired
	private ReceivePaymentValidator validator;

	@Operation(summary = "分页查询")
	@GetMapping("/paging")
	public ApiResponse<Page<ReceivePayment>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "收款ID") @RequestParam(required = false) String id,
			@Parameter(description = "订单ID") @RequestParam(required = false) String orderId,
			@Parameter(description = "状态（1待处理 2已寄出 3无效信息）") @RequestParam(required = false) List<Integer> states,
			@Parameter(description = "费用类型（1待处理 2已寄出 3无效信息）") @RequestParam(required = false) List<Integer> paymentTypes,
			@Parameter(description = "申请日期开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@Parameter(description = "申请日期开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder,
			@Parameter(description = "采购账号信息：实名、组织机构认证、手机号，模糊搜索") @RequestParam(required = false) String keyword) {
		return new ApiResponse<>(PageUtil.convert(
				service.paging(page, size, id, orderId, states, paymentTypes,
						beginTime, endTime, sortKey, sortOrder, keyword)));
	}

	@Operation(summary = "金额查询")
	@GetMapping("/amount")
	public ApiResponse<ReceivePaymentVo> findAmount() {
		return new ApiResponse<>(
				service.findAmount().orElse(new ReceivePaymentVo()));
	}

	@Operation(summary = "新增")
	@PostMapping
	public ApiResponse<ReceivePayment> create(
			@Validated @RequestBody ReceivePaymentForm form) {
		ReceivePayment receivePayment = validator.validateCreate(form);
		return new ApiResponse<>(service.create(receivePayment));
	}

	@Operation(summary = "修改")
	@PutMapping("/{id}")
	public ApiResponse<ReceivePayment> update(@PathVariable String id,
			@Validated @RequestBody ReceivePaymentForm form) {
		ReceivePayment receivePayment = validator.validateUpdate(id, form);
		return new ApiResponse<>(service.updateAllProperties(receivePayment));
	}

	@Operation(summary = "取消")
	@PutMapping("/cancel/{id}")
	public ApiResponse<Void> cancel(@PathVariable String id) {
		ReceivePayment receivePayment = validator.validateCancel(id);
		service.cancel(receivePayment);
		return new ApiResponse<>();
	}

}
