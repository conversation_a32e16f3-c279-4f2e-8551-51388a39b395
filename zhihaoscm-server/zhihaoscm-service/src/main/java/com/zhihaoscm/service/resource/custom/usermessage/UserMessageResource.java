package com.zhihaoscm.service.resource.custom.usermessage;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.common.util.utils.date.DateFormat;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.entity.UserMessage;
import com.zhihaoscm.domain.bean.vo.UnReadUserMessageVo;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.service.client.UserMessageClient;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.BaseUserCenterService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.InstitutionApplyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "用户消息", description = "用户消息API")
@RestController
@RequestMapping("/user/message")
public class UserMessageResource {

	@Autowired
	private UserMessageClient userMessageService;

	/** 驳回单据和对应的服务关系维护 */
	private static final Map<String, Class> PAGE_SERVICE_MAP = new ConcurrentHashMap<>();

	/** 驳回单据和驳回人的关系维护(驳回人无法查看已驳回的单据) */
	private static final Map<String, String> PAGE_OPER_FIELD_NAME_MAP = new ConcurrentHashMap<>();

	@Operation(summary = "查询用户消息列表")
	@GetMapping(value = "/paging")
	public ApiResponse<Page<UserMessage>> paging(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "发送时间-小于此时间的数据") @DateTimeFormat(pattern = DateFormat.YYYY_MM_DD_HH_MM_SS) @RequestParam(value = "sendDate", required = false) LocalDateTime sendDate,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder,
			@Parameter(description = "查询参数") @RequestParam(value = "searchParam", required = false) String searchParam,
			@Parameter(description = "消息类型") @RequestParam(value = "type", required = false) Integer type,
			@Parameter(description = "接收人id") @RequestParam(value = "customerId", required = false) Long customerId) {
		Integer role = Objects
				.nonNull(CustomerContextHolder.getCustomerLoginVo())
						? CustomerContextHolder.getCustomerLoginVo().getRole()
						: null;
		return new ApiResponse<>(userMessageService.paging(page, size, sendDate,
				sortKey, sortOrder, searchParam, type, role,
				Objects.nonNull(CustomerContextHolder.getCustomerLoginVo())
						? CustomerContextHolder.getCustomerLoginVo()
								.getProxyAccount().getId()
						: customerId));
	}

	@Operation(summary = "批量已读消息")
	@PostMapping(value = "/readed")
	public ApiResponse<Void> readed(@RequestBody List<Long> ids,
			@RequestParam(required = false, value = "type") Integer type) {
		userMessageService.readed(ids, type);
		return new ApiResponse<>();
	}

	@Operation(summary = "批量删除用户信息")
	@PostMapping(value = "/batchDelete")
	public ApiResponse<Void> batchDelete(@RequestBody List<Long> ids,
			@RequestParam(required = false, value = "type") Integer type) {
		userMessageService.batchDelete(ids, type);
		return new ApiResponse<>();
	}

	@Operation(summary = "全部删除用户信息")
	@PostMapping(value = "/deleteAll")
	public ApiResponse<Void> deleteAll(
			@RequestParam(value = "customerId", required = false) String customerId,
			@RequestParam(required = false, value = "type") Integer type) {
		Integer role = Objects
				.nonNull(CustomerContextHolder.getCustomerLoginVo())
						? CustomerContextHolder.getCustomerLoginVo().getRole()
						: null;
		userMessageService.deleteAll(
				Objects.nonNull(CustomerContextHolder.getCustomerLoginVo())
						? String.valueOf(CustomerContextHolder
								.getCustomerLoginVo().getProxyAccount().getId())
						: customerId,
				type, role);
		return new ApiResponse<>();
	}

	@Operation(summary = "全部已读用户信息")
	@PostMapping("/readedAll")
	public ApiResponse<Void> readedAll(
			@RequestParam(value = "customerId", required = false) String customerId,
			@RequestParam(required = false, value = "type") Integer type) {
		Integer role = Objects
				.nonNull(CustomerContextHolder.getCustomerLoginVo())
						? CustomerContextHolder.getCustomerLoginVo().getRole()
						: null;
		userMessageService.readedAll(
				Objects.nonNull(CustomerContextHolder.getCustomerLoginVo())
						? String.valueOf(CustomerContextHolder
								.getCustomerLoginVo().getProxyAccount().getId())
						: customerId,
				type, role);
		return new ApiResponse<>();
	}

	@Operation(summary = "各类型未读消息数量")
	@PostMapping("/getUnreadMessage")
	public ApiResponse<UnReadUserMessageVo> getUnreadMessage(
			@RequestParam(value = "customerId", required = false) String customerId) {
		Integer role = Objects
				.nonNull(CustomerContextHolder.getCustomerLoginVo())
						? CustomerContextHolder.getCustomerLoginVo().getRole()
						: null;
		return new ApiResponse<>(userMessageService.getUnreadMessage(role,
				Objects.nonNull(CustomerContextHolder.getCustomerLoginVo())
						? String.valueOf(CustomerContextHolder
								.getCustomerLoginVo().getProxyAccount().getId())
						: customerId));
	}

	@Operation(summary = "获取消息跳转实体是否存在")
	@GetMapping("/entity/exist")
	public ApiResponse<Integer> entityExist(
			@RequestParam(value = "id") Long id) {
		AtomicReference<Integer> result = new AtomicReference<>(
				CommonDef.Symbol.NO.getCode());
		UserMessage message = userMessageService.findById(id);
		if (Objects.nonNull(message)) {
			Class clazz = PAGE_SERVICE_MAP.get(message.getUrl());
			if (Objects.isNull(clazz)) {
				return new ApiResponse<>(result.get());
			}
			if (Objects.isNull(message.getDetailId())) {
				// 如果是存在服务但是不存在dettailId，那就是不需要id的页面跳转，可以直接跳
				result.set(CommonDef.Symbol.YES.getCode());
			} else {
				Object serviceObj = SpringUtil.getBean(clazz);
				if (serviceObj instanceof MpLongIdBaseService service) {
					service.findOne(Long.valueOf(message.getDetailId()))
							.ifPresent(longIdEntity -> result
									.set(CommonDef.Symbol.YES.getCode()));
				} else if (serviceObj instanceof MpStringIdBaseService service) {
					service.findOne(message.getDetailId())
							.ifPresent(longIdEntity -> {
								// 如果是ShippingRequirementPlat，并且状态是已取消ENDED的看不到，只能看到已发布的
								if (longIdEntity instanceof ShippingRequirementPlat plat) {
									if (ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
											.match(plat.getState())) {
										result.set(judgeRejectBill(longIdEntity,
												message.getUrl()));
									}
								} else {
									result.set(judgeRejectBill(longIdEntity,
											message.getUrl()));
								}
							});
				} else if (serviceObj instanceof BaseUserCenterService service) {
					service.findOne(Long.valueOf(message.getDetailId()))
							.ifPresent(longIdEntity -> result
									.set(CommonDef.Symbol.YES.getCode()));
				}

			}
		}
		return new ApiResponse<>(result.get());
	}

	/**
	 * 判断驳回相关单据是否跳转 -> 逻辑如下 收付款单、签收单、对账单，驳回人无法 查看对应的单据，跳转返回0
	 * 
	 * @param longIdEntity
	 * @param page
	 * @return
	 */
	private Integer judgeRejectBill(Object longIdEntity, String page) {
		Integer result = CommonDef.Symbol.YES.getCode();
		if (!PAGE_OPER_FIELD_NAME_MAP.containsKey(page)) {
			return result;
		}
		try {
			Integer state = Integer
					.parseInt(getFeildValue("state", longIdEntity));
			boolean isReject = PurchasePaymentDef.State.REJECTED.getCode()
					.equals(state);
			if (UserMessageConstants.SIGN_IN_DETAIL_PAGE.equals(page)
					|| UserMessageConstants.ACCOUNT_DETAIL_PAGE.equals(page)) {
				// 签收、对账单获取状态需要做位运算
				isReject = PurchaseGoodsReceiptDef.State.REJECTED
						.match(PurchaseContractDef.GET_HIGH_STATE.apply(state));
			}
			if (isReject) {
				// 已驳回，驳回人无法查看对应的单据
				String operator = getFeildValue(
						PAGE_OPER_FIELD_NAME_MAP.get(page), longIdEntity);
				String currentUserId = String.valueOf(CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount().getId());
				if (currentUserId.equals(operator)) {
					result = CommonDef.Symbol.NO.getCode();
				}
			}
		} catch (Exception e) {
			return CommonDef.Symbol.YES.getCode();
		}
		return result;
	}

	/**
	 * 通过反射获取对象属性值
	 * 
	 * @param fieldName
	 * @param entity
	 * @return
	 * @throws Exception
	 */
	String getFeildValue(String fieldName, Object entity) throws Exception {
		Class cls = entity.getClass();
		String result = null;
		Field[] declaredFields = cls.getDeclaredFields();
		for (Field field : declaredFields) {
			if (fieldName.equals(field.getName())) {
				PropertyDescriptor pd = new PropertyDescriptor(field.getName(),
						cls);
				Method getMethod = pd.getReadMethod();
				Object invoke = getMethod.invoke(entity);
				result = String.valueOf(invoke);
				break;
			}
		}
		return result;
	}

	/*
	 * 界面地址和服务关系维护
	 */
	static {
		PAGE_SERVICE_MAP.put(UserMessageConstants.FIND_BOAT_DETAIL_PAGE,
				ShippingRequirementPlatService.class);
		PAGE_SERVICE_MAP.put(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE,
				TransportOrderShipService.class);
		PAGE_SERVICE_MAP.put(
				UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE,
				TransportOrderShipService.class);
		PAGE_SERVICE_MAP.put(UserMessageConstants.GRAP_ORDER_DETAIL_PAGE,
				ShippingRequirementAcceptService.class);
		PAGE_SERVICE_MAP.put(UserMessageConstants.SHIP_MONITOR_DETAIL_PAGE,
				ShipMonitorShareService.class);
		PAGE_SERVICE_MAP.put(UserMessageConstants.SUB_ACCOUNT_LIST_PAGE,
				CustomerService.class);
		PAGE_SERVICE_MAP.put(
				UserMessageConstants.TRANSFER_SHIP_REQUIRE_DETAIL_PAGE,
				ShippingRequirementPlatService.class);
		PAGE_SERVICE_MAP.put(UserMessageConstants.ORGANIZATION_CERTIFICATION,
				InstitutionApplyService.class);
		PAGE_SERVICE_MAP.put(UserMessageConstants.MESSAGE_FEEDBACK_DETAIL_PAGE,
				MessageFeedbackService.class);
		PAGE_SERVICE_MAP.put(UserMessageConstants.OIL_ORDER_DETAIL_PAGE,
				OilOrderService.class);
		PAGE_OPER_FIELD_NAME_MAP.put(UserMessageConstants.RECEIPT_DETAIL_PAGE,
				"collectCustomerId");
		PAGE_OPER_FIELD_NAME_MAP.put(UserMessageConstants.PAYMENT_DETAIL_PAGE,
				"collectCustomerId");
		PAGE_OPER_FIELD_NAME_MAP.put(UserMessageConstants.SIGN_IN_DETAIL_PAGE,
				"receiverCustomerId");
		PAGE_OPER_FIELD_NAME_MAP.put(UserMessageConstants.ACCOUNT_DETAIL_PAGE,
				"purchaseCustomerId");
	}
}
