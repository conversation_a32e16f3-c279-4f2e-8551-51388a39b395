package com.zhihaoscm.service.resource.validator.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.meta.biz.FileDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.FileService;

@Component
public class FileValidator {

	@Autowired
	private FileService fileService;

	/**
	 * 验证文件
	 *
	 * @param file
	 *            文件
	 */
	public void validateFile(MultipartFile file) {
		// 验证文件大小
		if (file.getSize() > FileDef.MAX_FILE_SIZE) {
			throw new BadRequestException(ErrorCode.CODE_30040001);
		}
	}

	/**
	 * 验证文件是否存在
	 * 
	 * @param id
	 * @return
	 */
	public File validateExist(Long id) {
		return fileService.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30040002));
	}
}
