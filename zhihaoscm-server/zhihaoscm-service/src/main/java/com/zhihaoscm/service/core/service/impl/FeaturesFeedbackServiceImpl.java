package com.zhihaoscm.service.core.service.impl;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.FeaturesFeedback;
import com.zhihaoscm.domain.bean.entity.User;
import com.zhihaoscm.domain.bean.json.ArrayHandlerRecord;
import com.zhihaoscm.domain.bean.json.CustomerJsonInfo;
import com.zhihaoscm.domain.bean.json.HandlerRecordInfo;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.FeaturesFeedbackCountVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.FeaturesFeedbackDef;
import com.zhihaoscm.domain.meta.biz.WxwDef;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.FeaturesFeedbackMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.UserService;

@Service
public class FeaturesFeedbackServiceImpl extends
		MpLongIdBaseServiceImpl<FeaturesFeedback, FeaturesFeedbackMapper>
		implements FeaturesFeedbackService {
	public FeaturesFeedbackServiceImpl(FeaturesFeedbackMapper repository) {
		super(repository);
	}

	@Autowired
	private ShippingRequirementPlatService shippingRequirementPlatService;

	@Autowired
	private ShippingRequirementAcceptService shippingRequirementAcceptService;

	@Autowired
	private OilOrderService oilOrderService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private UserService userService;

	@Override
	public Page<FeaturesFeedback> paging(Integer page, Integer size,
			String customerEnterprise, LocalDateTime startTime,
			LocalDateTime endTime, Integer type, String sortOrder,
			String handleName, List<Integer> state) {
		LambdaQueryWrapper<FeaturesFeedback> queryWrapper = Wrappers
				.lambdaQuery(FeaturesFeedback.class)
				.eq(FeaturesFeedback::getDel, CommonDef.Symbol.NO.getCode())
				.apply(StringUtils.isNotBlank(customerEnterprise),
						"(customer_enterprise -> '$.realName' LIKE CONCAT('%',{0},'%') "
								+ "OR customer_enterprise -> '$.mobile' LIKE CONCAT('%',{0},'%')"
								+ "OR customer_enterprise -> '$.code' LIKE CONCAT('%',{0},'%'))",
						customerEnterprise)
				.between(Objects.nonNull(startTime) && Objects.nonNull(endTime),
						FeaturesFeedback::getCreatedTime, startTime, endTime)
				.eq(Objects.nonNull(type), FeaturesFeedback::getType, type)
				.like(StringUtils.isNotBlank(handleName),
						FeaturesFeedback::getHandlerName, handleName)
				.in(CollectionUtils.isNotEmpty(state),
						FeaturesFeedback::getState, state);
		if (StringUtils.isNotBlank(sortOrder)) {
			queryWrapper.last("order by created_time " + sortOrder);
		} else {
			queryWrapper.orderByAsc(FeaturesFeedback::getState)
					.orderByDesc(FeaturesFeedback::getCreatedTime);
		}

		return repository.selectPage(new Page<>(page, size), queryWrapper);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<FeaturesFeedback> create(FeaturesFeedback featuresFeedback,
			Long acceptId) {
		Customer customer = CustomerContextHolder.getCustomerLoginVo()
				.getActualAccount();
		CustomerJsonInfo customerJsonInfo = new CustomerJsonInfo();
		BeanUtils.copyProperties(customer, customerJsonInfo);
		featuresFeedback.setCustomerEnterprise(customerJsonInfo);
		featuresFeedback.setMobile(customer.getMobile());

		// 根据featuresFeedback的类型，设置不同的snapshotType和snapshot
		switch (FeaturesFeedbackDef.Type.from(featuresFeedback.getType())) {
			case RESOURCE_FEEDBACK -> {
				// 如果类型为RESOURCE_FEEDBACK，设置snapshotType为TIME_INAPPROPRIATE
				featuresFeedback.setSnapshotType(
						FeaturesFeedbackDef.SnapshotType.TIME_INAPPROPRIATE
								.getCode());
				// 根据srpId查找shippingRequirementPlatService，如果存在，则将vo转换为json字符串，并设置到featuresFeedback的snapshot中
				shippingRequirementPlatService
						.findVoById(featuresFeedback.getSrpId())
						.ifPresent(vo -> {
							try {
								featuresFeedback.setSnapshot(new ObjectMapper()
										.registerModule(new JavaTimeModule())
										.writeValueAsString(vo));
							} catch (JsonProcessingException e) {
								throw new RuntimeException(e);
							}
						});
			}
			case BOAT_OWNER_CANCELLED -> {
				// 如果类型为BOAT_OWNER_CANCELLED，设置snapshotType为ORDER_DETAILS
				featuresFeedback.setSnapshotType(
						FeaturesFeedbackDef.SnapshotType.ORDER_DETAILS
								.getCode());
				// 根据acceptId查找shippingRequirementAcceptService，如果存在，则将vo转换为json字符串，并设置到featuresFeedback的snapshot中
				shippingRequirementAcceptService.findVoById(acceptId)
						.ifPresent(vo -> {
							try {
								featuresFeedback.setSnapshot(new ObjectMapper()
										.registerModule(new JavaTimeModule())
										.writeValueAsString(vo));
							} catch (JsonProcessingException e) {
								throw new RuntimeException(e);
							}
						});
			}
			case SHIPPER_CANCELLED -> {
				// 如果类型为SHIPPER_CANCELLED，设置snapshotType为PRICE_TOO_LOW
				featuresFeedback.setSnapshotType(
						FeaturesFeedbackDef.SnapshotType.PRICE_TOO_LOW
								.getCode());
				// 货主的船运需求一直都没展示船主的报价信息，目前不需要存
				Map<String, Object> map = new HashMap<>();
				// 根据srcId和null查找shippingRequirementCustomerService，如果存在，则将vo转换为json字符串，并设置到featuresFeedback的snapshot中
				shippingRequirementPlatService
						.orderDetail(featuresFeedback.getSrpId(), null)
						.ifPresent(vo -> {
							try {
								map.put("shipping", vo.getPlat());
								map.put("priceIndex", vo.getPriceIndex());
								map.put("owener", vo.getOwener());
								map.put("orderShips", vo.getOrderShips());
								map.put("orderShipVos", vo.getOrderShipVos());
								map.put("title", vo.getTitle());
								map.put("sourcePortName",
										vo.getSourcePortName());
								map.put("destinationPortName",
										vo.getDestinationPortName());
								map.put("tarnsPortOrderState",
										vo.getTarnsPortOrderState());
								map.put("publishTime", vo.getPublishTime());
								map.put("shipName", vo.getShipName());
								featuresFeedback.setSnapshot(new ObjectMapper()
										.registerModule(new JavaTimeModule())
										.writeValueAsString(map));
							} catch (JsonProcessingException e) {
								throw new RuntimeException(e);
							}
						});
			}
			case OIL_ORDER_CANCELLED -> {
				// 如果类型为OIL_ORDER_CANCELLED，设置snapshotType为ORDER_DETAILS
				featuresFeedback.setSnapshotType(
						FeaturesFeedbackDef.SnapshotType.OIL_ORDER_DETAILS
								.getCode());
				// 根据oilOrderId查找，如果存在，则将vo转换为json字符串，并设置到featuresFeedback的snapshot中
				oilOrderService
						.findVoById(featuresFeedback.getSrpId(), null, null)
						.ifPresent(vo -> {
							try {
								featuresFeedback.setSnapshot(new ObjectMapper()
										.registerModule(new JavaTimeModule())
										.writeValueAsString(vo));
							} catch (JsonProcessingException e) {
								throw new RuntimeException(e);
							}
						});
			}
		}

		FeaturesFeedback feedback = super.create(featuresFeedback);

		// 发送企微通知
		this.sendNotice(feedback);

		// 返回创建的反馈信息
		return Optional.of(feedback);
	}

	@Override
	public void updateAllProperties(Long id, Integer state, String remark,
			User user) {
		FeaturesFeedback featuresFeedback = this.findOne(id).orElse(null);
		assert featuresFeedback != null;
		featuresFeedback.setState(state);
		featuresFeedback.setHandlerName(user.getName());
		featuresFeedback.setHandlerId(user.getId());
		HandlerRecordInfo handlerRecordInfo = new HandlerRecordInfo();
		handlerRecordInfo.setHandlerId(user.getId());
		handlerRecordInfo.setHandlerName(user.getName());
		handlerRecordInfo.setHandleTime(
				LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS));
		handlerRecordInfo.setRemark(remark);
		ArrayHandlerRecord handlerRecord = featuresFeedback.getHandlerRecord();
		if (Objects.isNull(handlerRecord)) {
			handlerRecord = new ArrayHandlerRecord();
			handlerRecord.add(handlerRecordInfo);
		} else {
			handlerRecord.add(handlerRecordInfo);
		}
		featuresFeedback.setHandlerRecord(handlerRecord);
		super.updateAllProperties(featuresFeedback);
	}

	@Override
	public Optional<FeaturesFeedbackCountVo> statisticsFeaturesFeedback(
			boolean hasFull) {
		FeaturesFeedbackCountVo featuresFeedbackCountVo = new FeaturesFeedbackCountVo();
		featuresFeedbackCountVo.setWaitHandle(0L);
		if (hasFull) {
			LambdaQueryWrapper<FeaturesFeedback> wrapper = Wrappers
					.lambdaQuery(FeaturesFeedback.class);
			this.filterDeleted(wrapper);
			wrapper.eq(FeaturesFeedback::getState,
					FeaturesFeedbackDef.State.PENDING.getCode());
			Long waitHandle = repository.selectCount(wrapper);
			featuresFeedbackCountVo.setWaitHandle(waitHandle);
			wrapper.clear();
		}
		return Optional.of(featuresFeedbackCountVo);
	}

	/**
	 * 发送通知
	 *
	 * @param featuresFeedback
	 */
	private void sendNotice(FeaturesFeedback featuresFeedback) {
		messageService.sendNotice(WxwMessage.builder().receiptors(userService
				.findUsersByPermission(AdminPermissionDef.FUNCTION_W, null)
				.stream().map(user -> String.valueOf(user.getId())).toList())
				.url("/manageCenter/messageManage/featuresFeedback/info/"
						.concat(String.valueOf(featuresFeedback.getId())))
				.prefix("用户" + CustomerContextHolder.getCustomerLoginVo()
						.getActualAccount().getCode())
				.operationModule(WxwDef.NoticeOperationModule.COMMIT.getDesc())
				.desc("反馈，请处理")
				.keyword(FeaturesFeedbackDef.Type
						.from(featuresFeedback.getType()).getName())
				.content(StringUtil.EMPTY_STRING).build());
	}

}
