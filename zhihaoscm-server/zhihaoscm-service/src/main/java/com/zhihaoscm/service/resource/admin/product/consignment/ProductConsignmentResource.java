package com.zhihaoscm.service.resource.admin.product.consignment;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.dto.ProductConsignmentDto;
import com.zhihaoscm.domain.bean.entity.ProductConsignment;
import com.zhihaoscm.domain.bean.vo.ProductConsignmentVo;
import com.zhihaoscm.domain.bean.vo.ServiceSpecialVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.ProductConsignmentService;
import com.zhihaoscm.service.resource.form.product.consignment.ProductConsignmentAuditForm;
import com.zhihaoscm.service.resource.form.product.consignment.ProductConsignmentDeleteForm;
import com.zhihaoscm.service.resource.form.product.consignment.ProductConsignmentForm;
import com.zhihaoscm.service.resource.form.product.consignment.ProductConsignmentWeightForm;
import com.zhihaoscm.service.resource.validator.product.consignment.ProductConsignmentValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 寄售商品 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Tag(name = "寄售商品管理", description = "寄售商品管理API")
@RestController
@RequestMapping("/product-consignment")
public class ProductConsignmentResource {

	@Autowired
	private ProductConsignmentService service;
	@Autowired
	private ProductConsignmentValidator validator;

	@Operation(summary = "分页查询商品列表")
	@GetMapping("/paging")
	@Secured({ AdminPermissionDef.PRODUCT_R, AdminPermissionDef.PRODUCT_MANAGE,
			AdminPermissionDef.PRODUCT_DEAL })
	public ApiResponse<Page<ProductConsignmentVo>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "商品编号或商品标题") @RequestParam(required = false) String keyword,
			@Parameter(description = "品类id") @RequestParam(required = false) String productTypeId,
			@Parameter(description = "供应商id") @RequestParam(required = false) Long supplierId,
			@Parameter(description = "商品状态") @RequestParam(required = false) Integer state,
			@Parameter(description = "发布状态") @RequestParam(required = false) List<Integer> publishState,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder) {
		return new ApiResponse<>(PageUtil
				.convert(service.paging(page, size, keyword, productTypeId,
						supplierId, state, publishState, sortKey, sortOrder)));
	}

	@Operation(summary = "查询商品详情")
	@GetMapping("/vo/{id}")
	@Secured({ AdminPermissionDef.PRODUCT_R, AdminPermissionDef.PRODUCT_MANAGE,
			AdminPermissionDef.PRODUCT_DEAL })
	public ApiResponse<ProductConsignmentVo> findVoById(
			@PathVariable String id) {
		ProductConsignmentVo vo = service.findVoById(id, null).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30196001));
		return new ApiResponse<>(vo);
	}

	@Operation(summary = "新增商品")
	@PostMapping
	@Secured({ AdminPermissionDef.PRODUCT_DEAL })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = LogDef.GOODS_MANAGEMENT_GOODS_MANAGEMENT_ADD, type = LogDef.GOODS_MANAGEMENT_PRODUCT_CONSIGNMENT_MANAGEMENT, bizNo = "{{#_ret.getData().getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#product.getProductTypeName()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#product.getId()}}") })
	public ApiResponse<ProductConsignment> create(
			@Validated @RequestBody ProductConsignmentForm form) {
		// 校验新增
		validator.validateCreate(form);
		ProductConsignmentDto dto = form.convertToDto();
		ProductConsignment result = service.create(dto.getProductConsignment(),
				dto.getActiveFileIds());
		LogRecordContext.putVariable("product", result);
		return new ApiResponse<>(result);
	}

	@Operation(summary = "编辑商品")
	@PutMapping("/{id}")
	@Secured({ AdminPermissionDef.PRODUCT_DEAL })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.GOODS_MANAGEMENT_GOODS_MANAGEMENT_EDIT, type = LogDef.GOODS_MANAGEMENT_PRODUCT_CONSIGNMENT_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#product.getProductTypeName()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#product.getId()}}") })
	public ApiResponse<ProductConsignment> update(@PathVariable String id,
			@Validated @RequestBody ProductConsignmentForm form) {
		// 校验更新
		validator.validateUpdate(id, form);
		ProductConsignmentDto dto = form.convertToDto(new ProductConsignment());
		ProductConsignment result = service.update(dto.getProductConsignment(),
				dto.getActiveFileIds(), dto.getUnActiveFileIds());
		LogRecordContext.putVariable("product", result);
		return new ApiResponse<>(result);
	}

	@Operation(summary = "审核商品")
	@PutMapping("/audit/{id}")
	@Secured({ AdminPermissionDef.PRODUCT_MANAGE })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE_STATUS, success = LogDef.GOODS_MANAGEMENT_GOODS_MANAGEMENT_REVIEW, type = LogDef.GOODS_MANAGEMENT_PRODUCT_CONSIGNMENT_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#product.getProductTypeName()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#product.getId()}}") })
	public ApiResponse<Void> audit(@PathVariable String id,
			@RequestBody ProductConsignmentAuditForm form) {
		// 校验审核
		ProductConsignment result = validator.validateAudit(id);
		LogRecordContext.putVariable("product", result);
		form.update(result);
		service.audit(result);
		return new ApiResponse<>();
	}

	@Operation(summary = "下架商品")
	@PutMapping("/down-shelf/{id}")
	@Secured({ AdminPermissionDef.PRODUCT_DEAL })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE_STATUS, success = LogDef.GOODS_MANAGEMENT_GOODS_MANAGEMENT_TAKE_OFF_SHELF, type = LogDef.GOODS_MANAGEMENT_PRODUCT_CONSIGNMENT_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#product.getProductTypeName()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#product.getId()}}") })
	public ApiResponse<Void> downShelf(@PathVariable String id) {
		// 校验下架
		ProductConsignment result = validator.validateDownShelf(id);
		LogRecordContext.putVariable("product", result);
		service.downShelf(result);
		return new ApiResponse<>();
	}

	@Operation(summary = "关闭商品")
	@PutMapping("/close/{id}")
	@Secured({ AdminPermissionDef.PRODUCT_CLOSE })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE_STATUS, success = LogDef.GOODS_MANAGEMENT_GOODS_MANAGEMENT_CLOSE, type = LogDef.GOODS_MANAGEMENT_PRODUCT_CONSIGNMENT_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#product.getProductTypeName()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#product.getId()}}") })
	public ApiResponse<Void> close(@PathVariable String id,
			@Validated @RequestBody ProductConsignmentDeleteForm form) {
		// 校验关闭
		ProductConsignment result = validator.validateClose(id);
		LogRecordContext.putVariable("product", result);
		service.close(result, form.getUnActiveFileIds());
		return new ApiResponse<>();
	}

	@Operation(summary = "修改商品的权重值")
	@PutMapping("/weight/{id}")
	public ApiResponse<Void> updateWeight(@PathVariable String id,
			@Validated @RequestBody ProductConsignmentWeightForm form) {
		ProductConsignment product = validator.validateExist(id);
		product.setWeight(form.getWeight());
		service.updateAllProperties(product);
		return new ApiResponse<>();
	}

	@Operation(summary = "指派专员")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.GOODS_MANAGEMENT_GOODS_MANAGEMENT_CHANGE_ASSIGN, type = LogDef.GOODS_MANAGEMENT_PRODUCT_CONSIGNMENT_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#product.getProductTypeName()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#product.getId()}}") })
	@GetMapping(value = "/assign/{id}/{handler-id}")
	@Secured(value = { AdminPermissionDef.PRODUCT_MANAGE })
	public ApiResponse<Void> assign(@PathVariable(value = "id") String id,
			@PathVariable(value = "handler-id") Long handlerId) {
		ProductConsignment product = validator.validateAssign(id, handlerId);
		LogRecordContext.putVariable("product", product);
		service.assign(id, handlerId);
		return new ApiResponse<>();
	}

	@Operation(summary = "获取专员列表")
	@GetMapping(value = "/find/specials")
	@Secured(value = { AdminPermissionDef.PRODUCT_MANAGE })
	public ApiResponse<List<ServiceSpecialVo>> findServiceSpecials(
			@RequestParam(required = false, value = "name") String name) {
		return new ApiResponse<>(service.findServiceSpecials(name));
	}
}
