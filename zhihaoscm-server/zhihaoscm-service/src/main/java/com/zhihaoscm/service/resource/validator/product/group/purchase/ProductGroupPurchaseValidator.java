package com.zhihaoscm.service.resource.validator.product.group.purchase;

import java.math.BigDecimal;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.domain.bean.entity.ProductGroupPurchase;
import com.zhihaoscm.domain.meta.biz.ProductGroupPurchaseDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.ProductGroupPurchaseService;
import com.zhihaoscm.service.core.service.usercenter.UserService;
import com.zhihaoscm.service.resource.form.product.group.purchase.ProductGroupPurchaseForm;
import com.zhihaoscm.service.resource.validator.product.consignment.ProductConsignmentValidator;
import com.zhihaoscm.service.resource.validator.product.type.ProductTypeValidator;

@Component
public class ProductGroupPurchaseValidator {
	@Autowired
	private ProductGroupPurchaseService service;
	@Autowired
	private ProductTypeValidator productTypeValidator;
	@Autowired
	private ProductConsignmentValidator productConsignmentValidator;
	@Autowired
	private UserService userValidator;

	/**
	 * 校验是否存在
	 *
	 * @param id
	 */
	public ProductGroupPurchase validateExist(String id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30196001));
	}

	/**
	 * 校验新增
	 *
	 * @param form
	 */
	public void validateCreate(ProductGroupPurchaseForm form) {

		// 如果是否含税为含税，则税率必填
		productConsignmentValidator.validateTaxRate(form.getIsIncludeTax(),
				form.getTaxRate());
		// 校验品类存在
		productTypeValidator.validateExist(form.getProductTypeId());
		// 校验参数值
		productConsignmentValidator
				.validateParameterInfo(form.getParameterInfo());
		// 校验商品标签
		productConsignmentValidator
				.validateProductLabel(form.getProductLabel());
		// 校验起订吨数(拼团最低吨数)
		this.validateMinOrderTon(form.getMinOrderTon(),
				form.getGroupSuccessTon(), form.getMinAdjustmentTon());
		// 结算方式为支付定金，则货款比例、固定金额二选一
		this.validateSettlementMethod(form);
		// 校验浮动比例和浮动固定吨数
		this.validateFloatType(form);
	}

	/**
	 * 校验修改
	 * 
	 * @param form
	 */
	public void validateUpdate(String id, ProductGroupPurchaseForm form) {
		ProductGroupPurchase product = this.validateExist(id);
		// 状态为 下架+待审核 下架+未通过 下架+已下架 才允许修改
		if (!ProductGroupPurchaseDef.State.DOWN_SHELF
				.match(product.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30196061);
		}
		if (!ProductGroupPurchaseDef.PublishState.NOT_AUDIT
				.match(product.getPublishState())
				&& !ProductGroupPurchaseDef.PublishState.FAIL
						.match(product.getPublishState())
				&& !ProductGroupPurchaseDef.PublishState.DOWN_SHELF
						.match(product.getPublishState())) {
			throw new BadRequestException(ErrorCode.CODE_30196061);
		}
		this.validateCreate(form);
	}

	public ProductGroupPurchase validateAudit(String id) {
		ProductGroupPurchase product = this.validateExist(id);
		// 状态为 上架+待审核 下架+待审核 才允许审核
		if (!ProductGroupPurchaseDef.PublishState.NOT_AUDIT
				.match(product.getPublishState())) {
			throw new BadRequestException(ErrorCode.CODE_30196062);
		}
		return product;
	}

	/**
	 * 校验下架
	 */
	public ProductGroupPurchase validateDownShelf(String id) {
		ProductGroupPurchase product = this.validateExist(id);
		// 状态为 上架+已发布 上架+未通过 才允许下架
		if (!ProductGroupPurchaseDef.State.UP_SHELF.match(product.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30196063);
		}
		if (!ProductGroupPurchaseDef.PublishState.PASS
				.match(product.getPublishState())
				&& !ProductGroupPurchaseDef.PublishState.FAIL
						.match(product.getPublishState())) {
			throw new BadRequestException(ErrorCode.CODE_30196063);
		}
		return product;
	}

	/**
	 * 校验关闭
	 */
	public ProductGroupPurchase validateClose(String id) {
		ProductGroupPurchase product = this.validateExist(id);
		// 状态为 下架+待审核 下架+未通过 下架+已下架 才允许关闭
		if (!ProductGroupPurchaseDef.State.DOWN_SHELF
				.match(product.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30196064);
		}
		if (!ProductGroupPurchaseDef.PublishState.NOT_AUDIT
				.match(product.getPublishState())
				&& !ProductGroupPurchaseDef.PublishState.FAIL
						.match(product.getPublishState())
				&& !ProductGroupPurchaseDef.PublishState.DOWN_SHELF
						.match(product.getPublishState())) {
			throw new BadRequestException(ErrorCode.CODE_30196064);
		}
		return product;
	}

	/**
	 * 校验指派
	 *
	 * @param id
	 * @param handlerId
	 */
	public ProductGroupPurchase validateAssign(String id, Long handlerId) {
		ProductGroupPurchase product = this.validateExist(id);
		// 验证操作专员是否存在
		userValidator.validateIsExist(handlerId);
		// 状态为 下架+关闭 不能变更指派
		if (ProductGroupPurchaseDef.State.DOWN_SHELF.match(product.getState())
				&& ProductGroupPurchaseDef.PublishState.CLOSE
						.match(product.getPublishState())) {
			throw new BadRequestException(ErrorCode.CODE_30196066);
		}
		return product;
	}

	/**
	 * 校验起订吨数和最小调整吨数
	 * 
	 * @param minOrderTon
	 *            起订吨数（拼团最低吨数）
	 * @param groupSuccessTon
	 *            拼团成功吨数
	 * @param minAdjustmentTon
	 *            最小调整吨数
	 */
	private void validateMinOrderTon(Integer minOrderTon,
			Integer groupSuccessTon, Integer minAdjustmentTon) {
		// 起订吨数（拼团最低吨数）不能超过拼团成功吨数
		if (minOrderTon > groupSuccessTon) {
			throw new BadRequestException(ErrorCode.CODE_30196075);
		}
		// 最小调整吨数不能小于1，且不能超过起订吨数（拼团最低吨数）（团购）
		if (minAdjustmentTon < 1 || minAdjustmentTon > minOrderTon) {
			throw new BadRequestException(ErrorCode.CODE_30196085);
		}
	}

	/**
	 * 校验下单方式
	 */
	private void validateSettlementMethod(ProductGroupPurchaseForm form) {
		Integer settlementMethod = form.getSettlementMethod();
		Integer depositPaymentMethod = form.getDepositPaymentMethod();
		BigDecimal paymentRatio = form.getPaymentRatio();
		BigDecimal fixedAmount = form.getFixedAmount();
		if (ProductGroupPurchaseDef.SettlementMethod.PAY_DEPOSIT
				.match(settlementMethod)) {
			// 选择支付定金时，需要选择货款比例或者固定金额
			if (Objects.isNull(depositPaymentMethod)) {
				throw new BadRequestException(ErrorCode.CODE_30196043);
			}
			if (ProductGroupPurchaseDef.DepositPaymentMethod.PAYMENT_RATIO
					.match(depositPaymentMethod)) {
				if (Objects.isNull(paymentRatio)) {
					throw new BadRequestException(ErrorCode.CODE_30196045);
				}
				// 货款比例是大于0，小于100的整数（1-99）
				if (paymentRatio.compareTo(BigDecimal.valueOf(1)) < 0
						|| paymentRatio.compareTo(BigDecimal.valueOf(99)) > 0
						|| paymentRatio.stripTrailingZeros().scale() > 0) {
					throw new BadRequestException(ErrorCode.CODE_30196046);
				}
			} else {
				if (Objects.isNull(fixedAmount)) {
					throw new BadRequestException(ErrorCode.CODE_30196047);
				}
				// 固定金额大于0
				if (fixedAmount.compareTo(BigDecimal.valueOf(1)) < 0
						|| fixedAmount.stripTrailingZeros().scale() > 0) {
					throw new BadRequestException(ErrorCode.CODE_30196048);
				}
				// 固定金额小于 1 000 000 的整数
				if (fixedAmount.compareTo(BigDecimal.valueOf(999999)) > 0) {
					throw new BadRequestException(ErrorCode.CODE_30196049);
				}
			}
		}
	}

	/**
	 * 校验浮动比例和浮动固定吨数
	 */
	private void validateFloatType(ProductGroupPurchaseForm form) {
		Integer floatType = form.getFloatType();
		BigDecimal groupFloatRatio = form.getGroupFloatRatio();
		Integer groupFloatTon = form.getGroupFloatTon();
		Integer groupSuccessTon = form.getGroupSuccessTon();
		if (ProductGroupPurchaseDef.FloatType.FLOAT_RATIO.match(floatType)) {
			if (Objects.isNull(groupFloatRatio)) {
				throw new BadRequestException(ErrorCode.CODE_30196080);
			}
			// 浮动比例比例（0-100）
			if (groupFloatRatio.compareTo(BigDecimal.valueOf(0)) < 0
					|| groupFloatRatio.compareTo(BigDecimal.valueOf(100)) > 0
					|| groupFloatRatio.stripTrailingZeros().scale() > 0) {
				throw new BadRequestException(ErrorCode.CODE_30196081);
			}
		} else {
			if (Objects.isNull(groupFloatTon)) {
				throw new BadRequestException(ErrorCode.CODE_30196082);
			}
			// 浮动固定吨数需要大于0
			if (groupFloatTon.compareTo(1) < 0) {
				throw new BadRequestException(ErrorCode.CODE_30196083);
			}
			// 浮动固定吨数需要小于拼团成功吨数
			if (groupFloatTon.compareTo(groupSuccessTon) > 0) {
				throw new BadRequestException(ErrorCode.CODE_30196084);
			}
		}

	}
}
