package com.zhihaoscm.service.resource.admin.oil.index;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.vo.OilIndexVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.service.core.service.OilIndexService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 油品指数 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Tag(name = "油品指数", description = "油品指数API")
@RestController
@RequestMapping("/oil-index")
public class OilIndexResource {

	@Autowired
	private OilIndexService service;

	@Operation(summary = "分页查询")
	@GetMapping("/paging")
	@Secured({ AdminPermissionDef.PETROL_INDEX_R,
			AdminPermissionDef.PETROL_INDEX_DEAL,
			AdminPermissionDef.PETROL_INDEX_MANAGE })
	public ApiResponse<Page<OilIndexVo>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "版本id") @RequestParam(required = false) Long versionId,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder,
			@Parameter(description = "站点编号或名称") @RequestParam(required = false) String keyword,
			@Parameter(description = "品牌") @RequestParam(required = false) String brand,
			@Parameter(description = "省份名称") @RequestParam(required = false) String provinceName,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
		return new ApiResponse<>(PageUtil.convert(
				service.paging(page, size, versionId, sortKey, sortOrder,
						keyword, brand, provinceName, beginTime, endTime)));
	}

}
