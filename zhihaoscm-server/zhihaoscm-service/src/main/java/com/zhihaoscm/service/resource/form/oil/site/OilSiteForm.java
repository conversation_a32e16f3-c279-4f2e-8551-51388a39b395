package com.zhihaoscm.service.resource.form.oil.site;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.domain.bean.entity.OilSite;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "OilSiteForm", title = "油站表单")
public class OilSiteForm {

	@Schema(title = "站点名称")
	@Length(min = 1, max = 32, message = ErrorCode.CODE_30154003)
	@NotBlank(message = ErrorCode.CODE_30154002)
	private String name;

	@Schema(description = "品牌")
	@NotBlank(message = ErrorCode.CODE_30154005)
	@Length(min = 1, max = 10, message = ErrorCode.CODE_30154006)
	private String brand;

	@Schema(title = "站点联系方式")
	@NotBlank(message = ErrorCode.CODE_30154018)
	@Length(min = 1, max = 11, message = ErrorCode.CODE_30154019)
	private String sitePhone;

	@Schema(title = "维护人联系方式")
	@NotBlank(message = ErrorCode.CODE_30154007)
	@Length(min = 1, max = 11, message = ErrorCode.CODE_30154008)
	private String phone;

	@Schema(description = "价格*数量")
	@NotNull(message = ErrorCode.CODE_30154009)
	@Range(min = 1, max = 5, message = ErrorCode.CODE_30154010)
	private Integer quantity;

	@Schema(title = "省编码")
	@NotBlank(message = ErrorCode.CODE_30154011)
	private String provinceCode;

	@Schema(title = "城市编码")
	@NotBlank(message = ErrorCode.CODE_30154012)
	private String cityCode;

	@Schema(title = "区域编码")
	@NotBlank(message = ErrorCode.CODE_30154013)
	private String regionCode;

	@Schema(title = "省名称")
	@NotBlank(message = ErrorCode.CODE_30154020)
	private String provinceName;

	@Schema(title = "城市名称")
	@NotBlank(message = ErrorCode.CODE_30154021)
	private String cityName;

	@Schema(title = "区域名称")
	@NotBlank(message = ErrorCode.CODE_30154022)
	private String regionName;

	@Schema(title = "详细地址")
	@NotBlank(message = ErrorCode.CODE_30154014)
	private String address;

	@Schema(title = "经纬度")
	@NotBlank(message = ErrorCode.CODE_30154015)
	private String latLon;

	public OilSite convertToEntity() {
		OilSite oilSite = new OilSite();
		return this.updateData(oilSite);
	}

	private OilSite updateData(OilSite oilSite) {
		oilSite.setName(this.getName());
		oilSite.setBrand(this.getBrand());
		oilSite.setSitePhone(this.getSitePhone());
		oilSite.setPhone(this.getPhone());
		oilSite.setQuantity(this.getQuantity());
		oilSite.setProvinceCode(this.getProvinceCode());
		oilSite.setCityCode(this.getCityCode());
		oilSite.setRegionCode(this.getRegionCode());
		oilSite.setProvinceName(this.getProvinceName());
		oilSite.setCityName(this.getCityName());
		oilSite.setRegionName(this.getRegionName());
		oilSite.setAddress(this.getAddress());
		oilSite.setLatLon(this.getLatLon());
		return oilSite;
	}

	public OilSite convertToEntity(OilSite oilSite) {
		return this.updateData(oilSite);
	}
}
