package com.zhihaoscm.service.core.service.impl;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.bank.card.sdk.client.BankCardClient;
import com.zhihaoscm.bank.card.sdk.enums.BankCardDef;
import com.zhihaoscm.bank.card.sdk.request.BankCardRequest;
import com.zhihaoscm.bank.card.sdk.response.BankCardResponse;
import com.zhihaoscm.bank.card.sdk.response.BaseResponse;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.entity.OwnerShippingDeposit;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.json.CustomerBankInfo;
import com.zhihaoscm.domain.bean.json.CustomerJsonInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.Application.ApplicationTransfer;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.ShipTrajectoryVo;
import com.zhihaoscm.domain.exception.DynamicsBadRequestException;
import com.zhihaoscm.domain.meta.ApplicationDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.hmg.sdk.api.HmgClient;
import com.zhihaoscm.hmg.sdk.enums.HmgDef;
import com.zhihaoscm.hmg.sdk.request.HmgCancelChargeRequest;
import com.zhihaoscm.hmg.sdk.request.HmgChargeRequest;
import com.zhihaoscm.hmg.sdk.request.HmgSaveWaybillTracksRequest;
import com.zhihaoscm.hmg.sdk.request.Track;
import com.zhihaoscm.hmg.sdk.response.HmgBaseResponse;
import com.zhihaoscm.hmg.sdk.response.HmgSettlementUrlResponse;
import com.zhihaoscm.hmg.sdk.response.callback.WaybillPaymentInfo;
import com.zhihaoscm.service.aop.history.HistoryContext;
import com.zhihaoscm.service.core.mapper.OwnerShippingDepositMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 货主船运定金 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Slf4j
@Service
public class OwnerShippingDepositServiceImpl extends
		MpStringIdBaseServiceImpl<OwnerShippingDeposit, OwnerShippingDepositMapper>
		implements OwnerShippingDepositService {

	public OwnerShippingDepositServiceImpl(
			OwnerShippingDepositMapper repository) {
		super(repository);
	}

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private ShipService shipService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private ShippingRequirementAcceptService shippingRequirementAcceptService;

	@Autowired
	private HmgClient hmgClient;

	@Autowired
	private TransportOrderShipService transportOrderShipService;

	@Autowired
	private BankCardClient bankCardClient;

	@Autowired
	private MqUtil mqUtil;

	@Autowired
	private FileService fileService;

	@Autowired
	private MessageService messageService;

	@Override
	public Page<OwnerShippingDeposit> paging(Integer page, Integer size,
			String id, Integer state, Integer type, Long ownerId,
			Long captainId, String ownerKeyword) {
		// 查询船运定金表中结算状态为待结算并且船运单已经关闭的数据
		List<OwnerShippingDeposit> ownerShippingDeposits = this.findClosed();
		List<String> shipDepositIds = ownerShippingDeposits.stream()
				.map(OwnerShippingDeposit::getId).toList();
		// 查询船运定金表中结算状态为待结算并且船运单已经被物理删除的数据
		List<OwnerShippingDeposit> ownerShippingDeposits1 = this.findDeleted();
		List<String> shipDepositIds1 = ownerShippingDeposits1.stream()
				.map(OwnerShippingDeposit::getId).toList();
		LambdaQueryWrapper<OwnerShippingDeposit> queryWrapper = Wrappers
				.lambdaQuery(OwnerShippingDeposit.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Objects.nonNull(state), OwnerShippingDeposit::getState,
				state);
		queryWrapper.eq(Objects.nonNull(ownerId),
				OwnerShippingDeposit::getOwnerId, ownerId);
		queryWrapper.eq(Objects.nonNull(captainId),
				OwnerShippingDeposit::getCaptainId, captainId);
		if (StringUtils.isNotBlank(id)) {
			queryWrapper.and(x -> x.eq(OwnerShippingDeposit::getId, id).or()
					.eq(OwnerShippingDeposit::getRelationCode, id));
		}
		queryWrapper.eq(OwnerShippingDeposit::getPaymentMethod,
				TransportOrderShipDef.DepositPayType.PAY_ZH.getCode());
		queryWrapper.apply(StringUtils.isNotBlank(ownerKeyword),
				"(owner_enterprise -> '$.institutionName' LIKE CONCAT('%',{0},'%') "
						+ "OR owner_enterprise -> '$.realName' LIKE CONCAT('%',{0},'%') "
						+ "OR owner_enterprise -> '$.mobile' LIKE CONCAT('%',{0},'%') "
						+ "OR owner_enterprise -> '$.code' LIKE CONCAT('%',{0},'%'))",
				ownerKeyword);
		queryWrapper.eq(Objects.nonNull(type), OwnerShippingDeposit::getType,
				type);
		// 过滤出船运定金表中结算状态为待结算并且船运单已经关闭的数据
		queryWrapper.notIn(CollectionUtils.isNotEmpty(shipDepositIds),
				OwnerShippingDeposit::getId, shipDepositIds);
		// 过滤出船运定金表中结算状态为待结算并且船运单已经被物理删除的数据
		queryWrapper.notIn(CollectionUtils.isNotEmpty(shipDepositIds1),
				OwnerShippingDeposit::getId, shipDepositIds1);
		queryWrapper.orderByDesc(OwnerShippingDeposit::getCreatedTime);
		return repository.selectPage(new Page<>(page, size), queryWrapper);
	}

	@Override
	public List<OwnerShippingDeposit> findByRelationCode(String relationCode) {
		LambdaQueryWrapper<OwnerShippingDeposit> wrapper = Wrappers
				.lambdaQuery(OwnerShippingDeposit.class);
		this.filterDeleted(wrapper);
		wrapper.eq(OwnerShippingDeposit::getRelationCode, relationCode);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<OwnerShippingDeposit> findByRelationCodeAndType(
			String relationCode, Integer type) {
		LambdaQueryWrapper<OwnerShippingDeposit> wrapper = Wrappers
				.lambdaQuery(OwnerShippingDeposit.class);
		this.filterDeleted(wrapper);
		wrapper.eq(OwnerShippingDeposit::getRelationCode, relationCode);
		wrapper.eq(OwnerShippingDeposit::getType, type);
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public List<OwnerShippingDeposit> findByRelationCodeAndTypes(
			String relationCode, List<Integer> types) {
		LambdaQueryWrapper<OwnerShippingDeposit> wrapper = Wrappers
				.lambdaQuery(OwnerShippingDeposit.class);
		this.filterDeleted(wrapper);
		wrapper.eq(OwnerShippingDeposit::getRelationCode, relationCode);
		wrapper.eq(OwnerShippingDeposit::getType, types);
		return repository.selectList(wrapper);
	}

	@Override
	public List<OwnerShippingDeposit> findByRelationCodes(
			List<String> relationCodes) {
		LambdaQueryWrapper<OwnerShippingDeposit> wrapper = Wrappers
				.lambdaQuery(OwnerShippingDeposit.class);
		this.filterDeleted(wrapper);
		wrapper.in(OwnerShippingDeposit::getRelationCode, relationCodes);
		return repository.selectList(wrapper);
	}

	@Override
	public List<OwnerShippingDeposit> findClosed() {
		return repository.findClosed();
	}

	@Override
	public List<OwnerShippingDeposit> findDeleted() {
		return repository.findDeleted();
	}

	@FileId
	@Override
	public OwnerShippingDeposit create(OwnerShippingDeposit resource) {
		return super.create(resource);
	}

	@FileId(type = 2)
	@Override
	public OwnerShippingDeposit updateAllProperties(
			OwnerShippingDeposit resource) {
		return super.updateAllProperties(resource);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public OwnerShippingDeposit pay(OwnerShippingDeposit ownerShippingDeposit,
			TransportOrderShip transportOrderShip) {
		this.packOwnerShippingDeposit(ownerShippingDeposit, transportOrderShip);
		try {
			if (TransportOrderShipDef.DepositPayType.PAY_ZH
					.match(transportOrderShip.getPayType())) {
				// 查询银行卡联行号
				BankCardRequest bankCardRequest = new BankCardRequest();
				bankCardRequest.setBankcard(
						ownerShippingDeposit.getCaptainBankInfo().getAccount());
				BaseResponse<BankCardResponse> response = bankCardClient
						.queryBankCard(bankCardRequest);
				if (!BankCardDef.Code.SUCCESS.match(response.getCode())) {
					throw new RuntimeException(response.getMsg());
				}
				CustomerBankInfo captainBankInfo = ownerShippingDeposit
						.getCaptainBankInfo();
				captainBankInfo.setCNaps(response.getData().getCNaps());
				captainBankInfo.setBank(response.getData().getBank());
				ownerShippingDeposit.setCaptainBankInfo(captainBankInfo);

				// 判断是不是第一次支付运费
				List<OwnerShippingDeposit> ownerShippingDepositList = this
						.findByRelationCodeAndTypes(transportOrderShip.getId(),
								List.of(OwnerShippingDepositDef.Type.BALANCE_PAYMENT
										.getCode(),
										OwnerShippingDepositDef.Type.DEPARTURE_FEE
												.getCode(),
										OwnerShippingDepositDef.Type.DEMURRAGE
												.getCode()));
				// 说明是第一次
				if (CollectionUtils.isEmpty(ownerShippingDepositList)) {
					// 保存轨迹
					this.saveWaybillTracks(ownerShippingDeposit);
				}
				// 修改运单信息
				transportOrderShipService.updateWaybill(transportOrderShip);
				this.saveCharge(ownerShippingDeposit, transportOrderShip);
			} else {
				// 自行支付
				// 推送支付成功的船运费用列表过去
				// 船运单货主是第三方平台的
				ownerShippingDeposit.setApproveTime(LocalDateTime.now());
				if (Objects.nonNull(transportOrderShip.getAppId())) {
					// 推送支付成功的船运费用列表给三方
					handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
							.getCode(),
							ShippingCallbackDef.PushType.PAY_OWNER_SHIPPING_DEPOSIT
									.getCode(),
							transportOrderShip.getId());
				}

			}

		} catch (Exception e) {
			log.error("黄码港新增对账失败:{}", e.getMessage());
			throw new BadRequestException(ErrorCode.CODE_30191007);
		}
		return this.create(ownerShippingDeposit);
	}

	@History(success = HistoryDef.HELP_THE_OWNER_PAY, bizNo = "{{#transportOrderShip.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, kvParam = {
			@History.KeyValuePair(key = "#type#", value = "{{#type!=null?#type:''}}") })
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void adminPay(OwnerShippingDeposit ownerShippingDeposit,
			TransportOrderShip transportOrderShip) {
		this.pay(ownerShippingDeposit, transportOrderShip);
		HistoryContext.putVariable("type", OwnerShippingDepositDef.Type
				.from(ownerShippingDeposit.getType()).getName());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void resetPay(OwnerShippingDeposit ownerShippingDeposit,
			TransportOrderShip transportOrderShip) {
		try {
			if (TransportOrderShipDef.DepositPayType.PAY_ZH
					.match(transportOrderShip.getPayType())) {

				// 查询银行卡联行号
				CustomerBankInfo captainBankInfo = ownerShippingDeposit
						.getCaptainBankInfo();
				if (StringUtils.isBlank(captainBankInfo.getCNaps())) {
					BankCardRequest bankCardRequest = new BankCardRequest();
					bankCardRequest.setBankcard(captainBankInfo.getAccount());
					BaseResponse<BankCardResponse> response = bankCardClient
							.queryBankCard(bankCardRequest);
					if (!BankCardDef.Code.SUCCESS.match(response.getCode())) {
						log.error("查询银行卡失败:{}", response.getMsg());
						throw new RuntimeException("查询银行卡失败");
					}
					captainBankInfo.setCNaps(response.getData().getCNaps());
					captainBankInfo.setBank(response.getData().getBank());
					ownerShippingDeposit.setCaptainBankInfo(captainBankInfo);
				}
				this.saveCharge(ownerShippingDeposit, transportOrderShip);
			} else {
				// 自行支付
				// 推送支付成功的船运费用列表过去
				// 船运单货主是第三方平台的
				ownerShippingDeposit.setApproveTime(LocalDateTime.now());
				if (Objects.nonNull(transportOrderShip.getAppId())) {
					// 推送支付成功的船运费用列表给三方
					handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
							.getCode(),
							ShippingCallbackDef.PushType.PAY_OWNER_SHIPPING_DEPOSIT
									.getCode(),
							transportOrderShip.getId());
				}

			}

		} catch (Exception e) {
			log.error("黄码港修改对账失败:{}", e.getMessage());
			throw new RuntimeException("黄码港修改对账失败", e);
		}
		this.updateAllProperties(ownerShippingDeposit);
	}

	@History(success = HistoryDef.MODIFY_PAYMENT, bizNo = "{{#transportOrderShip.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, kvParam = {
			@History.KeyValuePair(key = "#type#", value = "{{#type!=null?#type:''}}") })
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void adminResetPay(OwnerShippingDeposit ownerShippingDeposit,
			TransportOrderShip transportOrderShip) {
		HistoryContext.putVariable("type", OwnerShippingDepositDef.Type
				.from(ownerShippingDeposit.getType()).getName());
		this.resetPay(ownerShippingDeposit, transportOrderShip);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void cancelPay(OwnerShippingDeposit ownerShippingDeposit) {
		try {
			ownerShippingDeposit
					.setState(OwnerShippingDepositDef.State.TO_BE_RECONCILIATION
							.getCode());
			// 调用黄码港取消对账接口
			Integer thirdCode = OwnerShippingDepositDef.Type
					.fromThirdCode(ownerShippingDeposit.getType());
			HmgCancelChargeRequest hmgCancelChargeRequest = new HmgCancelChargeRequest();
			hmgCancelChargeRequest
					.setTpWaybillNo(ownerShippingDeposit.getRelationCode());
			hmgCancelChargeRequest.setType(thirdCode);
			HmgBaseResponse<Object> resp = hmgClient
					.cancelCharge(hmgCancelChargeRequest);
			log.info("黄码港取消对账参数:{}",
					JsonUtils.objectToJson(hmgCancelChargeRequest));
			if (!CommonDef.Symbol.NO.match(resp.getCode())) {
				throw new RuntimeException("黄码港取消对账失败");
			}
		} catch (Exception e) {
			log.error("黄码港取消对账失败:{}", e.getMessage());
			throw new RuntimeException("黄码港取消对账失败", e);
		}
		super.updateAllProperties(ownerShippingDeposit);
	}

	@History(success = HistoryDef.CANCEL_THE_PAYMENT, bizNo = "{{#ownerShippingDeposit.getRelationCode()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, kvParam = {
			@History.KeyValuePair(key = "#type#", value = "{{#type!=null?#type:''}}") })
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void adminCancelPay(OwnerShippingDeposit ownerShippingDeposit) {
		this.cancelPay(ownerShippingDeposit);
		HistoryContext.putVariable("type", OwnerShippingDepositDef.Type
				.from(ownerShippingDeposit.getType()).getName());
	}

	@Override
	public void handleWaybillPaymentInfoCallback(
			WaybillPaymentInfo waybillPaymentInfo) {
		// 修改支付状态
		// 类型
		String type = waybillPaymentInfo.getType();
		Integer typeCode = OwnerShippingDepositDef.Type
				.getCodeByThirdCode(Integer.valueOf(type));
		// 运单id
		String relationCode = waybillPaymentInfo.getTpWaybillNo();
		// 支付状态
		Integer status = waybillPaymentInfo.getStatus();
		Integer state = null;
		switch (HmgDef.ReconciliationStatus.from(status)) {
			// 付款中
			case PAYING ->
				state = OwnerShippingDepositDef.State.TO_BE_PAID.getCode();
			// 已付款
			case PAID -> state = OwnerShippingDepositDef.State.PAID.getCode();
			// 付款失败
			case PAYMENT_FAILED ->
				state = OwnerShippingDepositDef.State.PAYMENT_FAILED.getCode();
		}
		if (Objects.nonNull(state)) {
			LambdaUpdateWrapper<OwnerShippingDeposit> wrapper = Wrappers
					.lambdaUpdate(OwnerShippingDeposit.class);
			wrapper.eq(OwnerShippingDeposit::getRelationCode, relationCode);
			wrapper.eq(OwnerShippingDeposit::getType, typeCode);
			wrapper.eq(OwnerShippingDeposit::getDel,
					CommonDef.Symbol.NO.getCode());
			wrapper.set(OwnerShippingDeposit::getState, state);
			wrapper.set(OwnerShippingDeposit::getRemark,
					waybillPaymentInfo.getRemark());
			wrapper.set(StringUtils.isNotBlank(waybillPaymentInfo.getTradeNo()),
					OwnerShippingDeposit::getTradeNo,
					waybillPaymentInfo.getTradeNo());
			wrapper.set(OwnerShippingDeposit::getApproveTime,
					waybillPaymentInfo.getPayTime());
			repository.update(wrapper);
		}

		if (OwnerShippingDepositDef.State.PAID.match(state)) {
			// 推送支付成功的过去
			TransportOrderShip transportOrderShip = transportOrderShipService
					.findOne(relationCode).orElse(null);
			if (Objects.nonNull(transportOrderShip)) {
				// 船运单货主是第三方平台的
				if (Objects.nonNull(transportOrderShip.getAppId())) {
					// 服务商垫付时黄码港支付成功后推送给三方
					handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
							.getCode(),
							ShippingCallbackDef.PushType.HMG_PAY_SUCCESS
									.getCode(),
							relationCode);
				}
			}
		}

		// 如果黄码港是支付失败，需要给此处理权限船运单的专员（上下游）发送企微消息提醒
		if (HmgDef.ReconciliationStatus.PAYMENT_FAILED.match(status)) {
			TransportOrderShip transportOrderShip = transportOrderShipService
					.findOne(relationCode).orElse(null);
			if (Objects.nonNull(transportOrderShip)) {
				List<String> handerIds = new ArrayList<>();
				// 上游专员
				Long upstreamHandlerId = transportOrderShip
						.getUpstreamHandlerId();
				// 下游专员
				Long downstreamHandlerId = transportOrderShip
						.getDownstreamHandlerId();
				if (Objects.nonNull(upstreamHandlerId)) {
					handerIds.add(String.valueOf(upstreamHandlerId));
				}
				if (Objects.nonNull(downstreamHandlerId)
						&& !upstreamHandlerId.equals(downstreamHandlerId)) {
					handerIds.add(String.valueOf(downstreamHandlerId));
				}
				if (CollectionUtils.isNotEmpty(handerIds)) {
					messageService.sendNotice(WxwMessage.builder()
							.receiptors(handerIds)
							.url("/logistics/shippingNeed/waybillManagementInfo/"
									.concat(transportOrderShip.getId()))
							.prefix(WxwDef.NoticePrefix.YOU_FOLLOW_UP.getDesc())
							.operationModule(
									WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
											.getDesc())
							.desc("服务商垫付"
									+ OwnerShippingDepositDef.Type
											.from(typeCode).getName()
									+ "失败，请及时跟进支付状态")
							.keyword(transportOrderShip.getId())
							.content(StringUtils.EMPTY).build());
				}
			}
		}

	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void confirmPay(OwnerShippingDeposit ownerShippingDeposit,
			TransportOrderShip transportOrderShip) {
		// 将定金船主确认状态设置为已确认
		ownerShippingDeposit
				.setCaptainConfirmState(CommonDef.Symbol.YES.getCode());
		switch (TransportOrderShipDef.DepositPayType
				.from(ownerShippingDeposit.getPaymentMethod())) {
			case PAY_ONESELF -> {
				// 自行支付
				if (OwnerShippingDepositDef.Type.DEPOSIT
						.match(ownerShippingDeposit.getType())) {
					// 如果类型是定金的话 将船运单状态流到下一状态
					transportOrderShipService.confirm(transportOrderShip);
				}
			}
			case PAY_ZH -> {
				// 垫付
				if (OwnerShippingDepositDef.Type.DEPOSIT_ONE
						.match(ownerShippingDeposit.getType())) {
					// 如果类型是定金1的话 将船运单状态流到下一状态
					transportOrderShipService.confirm(transportOrderShip);
				}
			}
		}

		if (Objects.nonNull(transportOrderShip.getAppId())) {
			// 确认船运费用后把船运费用推送给三方
			handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
					.getCode(),
					ShippingCallbackDef.PushType.CONFIRM_RECEIVE_DEPOSIT
							.getCode(),
					ownerShippingDeposit.getId());
		}

		super.updateAllProperties(ownerShippingDeposit);
	}

	@History(success = HistoryDef.HELP_THE_BOAT_OWNER_CONFIRM_RECEIPT, bizNo = "{{#transportOrderShip.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, kvParam = {
			@History.KeyValuePair(key = "#type#", value = "{{#type!=null?#type:''}}") })
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void adminConfirmPay(OwnerShippingDeposit ownerShippingDeposit,
			TransportOrderShip transportOrderShip) {
		this.confirmPay(ownerShippingDeposit, transportOrderShip);
		HistoryContext.putVariable("type", OwnerShippingDepositDef.Type
				.from(ownerShippingDeposit.getType()).getName());
	}

	@Override
	public void syncSettlementInfo() {
		// 查询所有tradeNo不为空的数据 且文件id是空的数据 类型是黄码港类型
		LambdaQueryWrapper<OwnerShippingDeposit> wrapper = Wrappers
				.lambdaQuery(OwnerShippingDeposit.class);
		super.filterDeleted(wrapper);
		wrapper.isNotNull(OwnerShippingDeposit::getTradeNo);
		wrapper.eq(OwnerShippingDeposit::getPaymentMethod,
				TransportOrderShipDef.DepositPayType.PAY_ZH.getCode());
		wrapper.and(x -> x.isNull(OwnerShippingDeposit::getPaymentFileId).or()
				.isNull(OwnerShippingDeposit::getReceiptFileId));
		List<OwnerShippingDeposit> ownerShippingDeposits = repository
				.selectList(wrapper);

		if (CollectionUtils.isNotEmpty(ownerShippingDeposits)) {
			for (OwnerShippingDeposit ownerShippingDeposit : ownerShippingDeposits) {
				HmgBaseResponse<HmgSettlementUrlResponse> response = hmgClient
						.getSettlementUrl(ownerShippingDeposit.getTradeNo());
				if (CommonDef.Symbol.NO.match(response.getCode())) {
					HmgSettlementUrlResponse data = response.getData();
					if (Objects.nonNull(data)) {
						String settlementFileUrl = data.getSettlementFileUrl();
						if (StringUtils.isNotBlank(settlementFileUrl)) {
							Long settlementFileId = fileService.uploadFile(
									settlementFileUrl,
									"结算单" + "." + this.getFileExtensionFromUrl(
											settlementFileUrl));
							ownerShippingDeposit
									.setPaymentFileId(settlementFileId);
						}

						String receiptFileUrl = data.getReceiptFileUrl();
						if (StringUtils.isNotBlank(receiptFileUrl)) {
							Long receiptFileId = fileService.uploadFile(
									receiptFileUrl,
									"银行电子回单" + "."
											+ this.getFileExtensionFromUrl(
													receiptFileUrl));
							ownerShippingDeposit
									.setReceiptFileId(receiptFileId);
						}
						OwnerShippingDeposit result = super.updateAllProperties(
								ownerShippingDeposit);
						// 推送的过去
						TransportOrderShip transportOrderShip = transportOrderShipService
								.findOne(result.getRelationCode()).orElse(null);
						if (Objects.nonNull(transportOrderShip)) {
							// 船运单货主是第三方平台的
							if (Objects
									.nonNull(transportOrderShip.getAppId())) {
								// 黄码港推送结算凭证后推送给三方
								handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
										.getCode(),
										ShippingCallbackDef.PushType.HMG_PUSH_PAY_FILE
												.getCode(),
										result.getId());
							}
						}

					}
				}
			}
		}
	}

	/**
	 * 黄码港新增、修改对账
	 * 
	 * @param ownerShippingDeposit
	 * @param transportOrderShip
	 */
	private void saveCharge(OwnerShippingDeposit ownerShippingDeposit,
			TransportOrderShip transportOrderShip) {
		Integer thirdCode = OwnerShippingDepositDef.Type
				.fromThirdCode(ownerShippingDeposit.getType());
		HmgChargeRequest req = new HmgChargeRequest();
		req.setTpWaybillNo(transportOrderShip.getId());
		req.setType(thirdCode);
		req.setMoney(ownerShippingDeposit.getAmount().toString());
		// 待付款
		req.setStatus(HmgDef.ReconciliationStatus.PENDING_PAYMENT.getCode());
		// 收款户名
		req.setPayeeName(ownerShippingDeposit.getCaptainBankInfo().getName());
		req.setPayeeAccount(
				ownerShippingDeposit.getCaptainBankInfo().getAccount());
		req.setPayeeBankType(
				ownerShippingDeposit.getCaptainBankInfo().getBank());
		req.setPayeeBankBranchNo(
				ownerShippingDeposit.getCaptainBankInfo().getCNaps());
		HmgBaseResponse<Object> hmgChargeResponse = hmgClient.saveCharge(req);
		log.info("黄码港新增对账参数:{}", JsonUtils.objectToJson(req));
		if (!CommonDef.Symbol.NO.match(hmgChargeResponse.getCode())) {
			log.error("黄码港新增对账失败:{}", hmgChargeResponse.getMsg());
			throw new DynamicsBadRequestException(ErrorCode.CODE_30191007,
					hmgChargeResponse.getMsg());
		}
	}

	/**
	 * 组装实体
	 *
	 * @param ownerShippingDeposit
	 * @param transportOrderShip
	 */
	private void packOwnerShippingDeposit(
			OwnerShippingDeposit ownerShippingDeposit,
			TransportOrderShip transportOrderShip) {
		ownerShippingDeposit.setOwnerId(transportOrderShip.getOwnerId());
		ownerShippingDeposit.setRelationCode(transportOrderShip.getId());
		ownerShippingDeposit
				.setShippingRequirementId(transportOrderShip.getSrpId());
		Integer code = null;
		switch (OwnerShippingDepositDef.Type
				.from(ownerShippingDeposit.getType())) {
			case DEPOSIT_ONE, DEPOSIT_TWO, DEPOSIT_THREE, DEPOSIT -> code = 1;
			case BALANCE_PAYMENT, DEPARTURE_FEE, DEMURRAGE, FREIGHT -> code = 2;
		}

		ownerShippingDeposit
				.setId(AutoCodeDef.CREATE_AUTO_CODE.apply(redisClient,
						AutoCodeDef.BusinessCode.SHIPPING_DEPOSIT.getCode(),
						RedisKeys.Cache.SHIPPING_DEPOSIT, "0" + code, 5,
						AutoCodeDef.DATE_TYPE.yyMM));

		CustomerJsonInfo customerInfo = new CustomerJsonInfo();
		Enterprise ownerEnterprise = transportOrderShip.getOwnerEnterprise();
		customerInfo.setRealName(ownerEnterprise.getRealName());
		customerInfo.setMobile(ownerEnterprise.getMobile());
		customerInfo.setInstitutionName(ownerEnterprise.getName());
		ownerShippingDeposit.setOwnerEnterprise(customerInfo);
		customerService.findOne(transportOrderShip.getOwnerId())
				.ifPresent(customer -> {
					// 货主信息
					CustomerJsonInfo customerJsonInfo = new CustomerJsonInfo();
					BeanUtils.copyProperties(customer, customerJsonInfo);
					ownerShippingDeposit.setOwnerEnterprise(customerJsonInfo);
				});
		// 船主信息
		ownerShippingDeposit.setCaptainId(transportOrderShip.getCaptainId());
		customerService.findOne(transportOrderShip.getCaptainId())
				.ifPresent(customer -> {
					CustomerJsonInfo customerJsonInfo = new CustomerJsonInfo();
					BeanUtils.copyProperties(customer, customerJsonInfo);
					ownerShippingDeposit.setCaptainEnterprise(customerJsonInfo);
				});
		// 船舶信息
		shipService.findOne(transportOrderShip.getShipId()).ifPresent(ship -> {
			ownerShippingDeposit.setShipId(ship.getId());
			ownerShippingDeposit.setShipName(ship.getName());
			ownerShippingDeposit.setShipCnName(ship.getCnname());
		});
		// 支付方式
		ownerShippingDeposit.setPaymentMethod(transportOrderShip.getPayType());
		if (Objects.nonNull(transportOrderShip.getSraId())) {
			shippingRequirementAcceptService
					.findOne(transportOrderShip.getSraId())
					.ifPresent(shippingRequirementAccept -> {
						ownerShippingDeposit.setCaptainBankId(
								shippingRequirementAccept.getCaptainBankId());
						ownerShippingDeposit.setCaptainBankInfo(
								shippingRequirementAccept.getCaptainBankInfo());
					});
		} else {
			ownerShippingDeposit
					.setCaptainBankId(transportOrderShip.getCaptainBankId());
			ownerShippingDeposit.setCaptainBankInfo(
					transportOrderShip.getCaptainBankInfo());
		}
		// 船主确认状态
		ownerShippingDeposit
				.setCaptainConfirmState(CommonDef.Symbol.NO.getCode());
	}

	/**
	 * 保存轨迹
	 * 
	 * @param ownerShippingDeposit
	 */
	private void saveWaybillTracks(OwnerShippingDeposit ownerShippingDeposit) {
		HmgSaveWaybillTracksRequest hmgSaveWaybillTracksRequest = new HmgSaveWaybillTracksRequest();
		hmgSaveWaybillTracksRequest
				.setTpWaybillNo(ownerShippingDeposit.getRelationCode());
		hmgSaveWaybillTracksRequest.setLocationWay("10");

		List<Track> hmgWaybillTrackList = new ArrayList<>();
		// 根据运单id查询船舶轨迹
		List<ShipTrajectoryVo> shipTrajectoryVoList = transportOrderShipService
				.findAisTrackById(ownerShippingDeposit.getRelationCode());
		if (CollectionUtils.isEmpty(shipTrajectoryVoList)) {
			return;
		}
		for (ShipTrajectoryVo shipTrajectoryVo : shipTrajectoryVoList) {
			Track track = new Track();
			track.setLat(String.valueOf(shipTrajectoryVo.getLat()));
			track.setLng(String.valueOf(shipTrajectoryVo.getLon()));
			if (Objects.nonNull(shipTrajectoryVo.getCreatedAt())) {
				LocalDateTime dateTime = LocalDateTime.ofInstant(
						Instant.ofEpochMilli(shipTrajectoryVo.getCreatedAt()),
						ZoneId.systemDefault());
				String formatted = dateTime.format(
						DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
				track.setLocationTime(formatted);
			}
			track.setSog(String.valueOf(shipTrajectoryVo.getSpeed()));
			track.setCog(String.valueOf(shipTrajectoryVo.getCog()));
			hmgWaybillTrackList.add(track);
		}
		hmgSaveWaybillTracksRequest.setTracks(hmgWaybillTrackList);
		try {
			log.info("黄码港新增轨迹参数:{}",
					JsonUtils.objectToJson(hmgSaveWaybillTracksRequest));
			HmgBaseResponse<Object> response = hmgClient
					.saveWaybillTracks(hmgSaveWaybillTracksRequest);
			log.info("黄码港新增轨迹结果:{}", JsonUtils.objectToJson(response));
			if (!CommonDef.Symbol.NO.match(response.getCode())) {
				throw new RuntimeException(response.getMsg());
			}
		} catch (Exception e) {
			log.error("黄码港新增轨迹失败:{}",
					JsonUtils.objectToJson(hmgSaveWaybillTracksRequest), e);
			throw new DynamicsBadRequestException(ErrorCode.CODE_30191008,
					e.getMessage());
		}

	}

	/**
	 * 获取文件后缀
	 * 
	 * @param fileUrl
	 * @return
	 */
	public String getFileExtensionFromUrl(String fileUrl) {
		if (fileUrl == null || fileUrl.isEmpty()) {
			return "";
		}
		int lastDotIndex = fileUrl.lastIndexOf('.');
		if (lastDotIndex == -1 || lastDotIndex < fileUrl.lastIndexOf('/')) {
			return "";
		}
		return fileUrl.substring(lastDotIndex + 1);
	}

	/**
	 * 处理推送
	 *
	 * @param type
	 * @param action
	 * @param id
	 */
	public void handle(Integer type, Integer action, String id) {
		if (Objects.isNull(type) || Objects.isNull(action)
				|| Objects.isNull(id)) {
			return;
		}
		ApplicationTransfer transfer = new ApplicationTransfer(type, action,
				id);
		mqUtil.asyncSend(MqMessage.builder()
				.topic(TopicDef.APPLICATION_CONNECTOR)
				.message(MessageBuilder
						.withPayload(JsonUtils.objectToJson(transfer)).build())
				.build());
	}
}
