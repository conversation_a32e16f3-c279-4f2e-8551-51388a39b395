package com.zhihaoscm.service.resource.form.business.opportunity;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "BusinessOpportunityTakedownForm", description = "商机下架表单")
public class BusinessOpportunityTakedownForm {

	@Schema(title = "下架原因")
	@NotNull(message = ErrorCode.CODE_30194008)
	@Range(min = 1, max = 5, message = ErrorCode.CODE_30194009)
	private Integer downShelfReason;

	@Schema(title = "备注")
	@Length(max = 32, message = ErrorCode.CODE_30194010)
	private String remark;

}
