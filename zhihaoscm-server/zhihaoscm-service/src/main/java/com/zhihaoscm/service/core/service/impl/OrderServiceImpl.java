package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.vo.OrderVo;
import com.zhihaoscm.domain.bean.vo.ServiceSpecialVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.service.core.mapper.OrderMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.UserService;

/**
 * <p>
 * 订单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
public class OrderServiceImpl extends
		MpStringIdBaseServiceImpl<Order, OrderMapper> implements OrderService {

	public OrderServiceImpl(OrderMapper repository) {
		super(repository);
	}

	@Autowired
	private UserService userService;

	@Autowired
	private AssignService assignService;

	@Autowired
	private OrderRemarkService orderRemarkService;

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private ReceivePaymentService receivePaymentService;

	@Autowired
	private ProductConsignmentService productConsignmentService;

	@Override
	public Page<Order> paging(Integer page, Integer size, String id,
			String productId, Integer orderType, List<Integer> states,
			String keyword, String sortKey, String sortOrder,
			LocalDateTime beginTime, LocalDateTime endTime) {
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		// 采购账号信息：实名、组织机构认证、手机号，模糊搜索
		wrapper.apply(StringUtils.isNotBlank(keyword),
				"(customer_enterprise -> '$.institutionName' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_enterprise -> '$.realName' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_enterprise -> '$.mobile' LIKE CONCAT('%',{0},'%'))",
				keyword);
		wrapper.eq(Objects.nonNull(id), Order::getId, id);
		wrapper.eq(Objects.nonNull(productId), Order::getProductId, productId);
		wrapper.eq(Objects.nonNull(orderType), Order::getOrderType, orderType);
		wrapper.in(Objects.nonNull(states), Order::getState, states);

		wrapper.ge(Objects.nonNull(beginTime), Order::getCreatedTime,
				beginTime);
		wrapper.le(Objects.nonNull(endTime), Order::getCreatedTime, endTime);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("ORDER BY " + sortKey + " " + sortOrder);
		} else {
			wrapper.orderByDesc(Order::getCreatedTime);
		}
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Page<OrderVo> customPaging(Integer page, Integer size,
			Integer orderType, List<Integer> states, String keyword,
			String sortKey, String sortOrder, LocalDateTime beginTime,
			LocalDateTime endTime, Long customerId) {
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		// 商品名称或订单编号
		wrapper.apply(StringUtils.isNotBlank(keyword),
				"(product_info -> '$.institutionName' LIKE CONCAT('%',{0},'%') "
						+ "OR id = {0} OR product_id = {0} ))",
				keyword);
		wrapper.eq(Objects.nonNull(orderType), Order::getOrderType, orderType);
		wrapper.in(Objects.nonNull(states), Order::getState, states);
		wrapper.ge(Objects.nonNull(beginTime), Order::getCreatedTime,
				beginTime);
		wrapper.le(Objects.nonNull(endTime), Order::getCreatedTime, endTime);
		wrapper.eq(Order::getCustomerId, customerId);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("ORDER BY " + sortKey + " " + sortOrder);
		} else {
			wrapper.orderByDesc(Order::getCreatedTime);
		}
		Page<Order> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public List<ServiceSpecialVo> findServiceSpecials() {
		// 查询拥有服务专员角色的账号
		List<User> attaches = userService.findUsersByPermission(
				AdminPermissionDef.TRADE_ORDER_DEAL, null);
		if (CollectionUtils.isEmpty(attaches)) {
			return List.of();
		}
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class)
				.eq(Order::getDel, CommonDef.Symbol.NO.getCode())
				.isNotNull(Order::getHandlerId);

		List<Order> transportOrderShipList = repository.selectList(wrapper);
		// 根据处理人id分组
		Map<Long, Long> orderMap = transportOrderShipList.stream()
				.collect(Collectors.groupingBy(Order::getHandlerId,
						Collectors.counting()));

		return attaches.stream().map(user -> {
			ServiceSpecialVo vo = new ServiceSpecialVo();
			vo.setUser(user);
			vo.setOrderCount(orderMap.getOrDefault(user.getId(), 0L));
			return vo;
		}).sorted(Comparator.comparingLong(ServiceSpecialVo::getOrderCount)
				.reversed()).toList();
	}

	@Override
	public Optional<OrderVo> findVoById(String id) {
		return super.findOne(id).map(this::packVo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Order create(Order resource) {
		// 生成id
		resource.setId(AutoCodeDef.CREATE_AUTO_CODE.apply(redisClient,
				AutoCodeDef.BusinessCode.ORDER_PREFIX.getCode(),
				RedisKeys.Cache.ORDER_GENERATOR, "0" + resource.getOrderType(),
				5, AutoCodeDef.DATE_TYPE.yyMM));
		// 生成默认付款单
		this.handleReceivePayment(resource);
		return super.create(resource);
	}

	@Override
	public void delete(String id, Integer userType) {
		LambdaUpdateWrapper<Order> wrapper = Wrappers.lambdaUpdate(Order.class);
		wrapper.eq(Order::getId, id);
		wrapper.eq(Order::getDel, CommonDef.Symbol.NO.getCode());
		if (CommonDef.UserType.OUTER.getCode().equals(userType)) {
			wrapper.set(Order::getCustomerDel, CommonDef.Symbol.YES.getCode());
		} else {
			wrapper.set(Order::getCustomerDel, CommonDef.Symbol.YES.getCode());
			wrapper.set(Order::getDel, CommonDef.Symbol.YES.getCode());
		}
		repository.update(wrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void assign(Order order, Long handlerId) {
		// 获取上游专员信息
		User user = userService.findOne(handlerId).orElse(new User());
		order.setHandlerId(handlerId);
		order.setHandlerName(user.getName());
		super.updateAllProperties(order);
		// 插入记录到指派表
		Assign assign = new Assign();
		assign.setCorrelationId(Long.valueOf(order.getId()));
		assign.setUserId(handlerId);
		assign.setType(AssignDef.Type.ORDER.getCode());
		assignService.create(assign);
	}

	@Override
	public void cancel(Order order) {
		order.setState(OrderDef.State.CANCELLED.getCode());
		super.updateAllProperties(order);
	}

	@Override
	public void finish(Order order) {
		order.setState(OrderDef.State.COMPLETED.getCode());
		super.updateAllProperties(order);
	}

	/**
	 * 组装vo
	 * 
	 * @param order
	 * @return
	 */
	private OrderVo packVo(Order order) {
		OrderVo orderVo = new OrderVo();
		orderVo.setOrder(order);
		Map<String, List<OrderRemark>> remarkMap = orderRemarkService
				.findByOrderIds(List.of(order.getId())).stream()
				.collect(Collectors.groupingBy(OrderRemark::getOrderId));
		Map<String, List<ReceivePayment>> receivePaymentMap = receivePaymentService
				.findByOrderIds(List.of(order.getId())).stream()
				.collect(Collectors.groupingBy(ReceivePayment::getOrderId));
		orderVo.setRemarks(remarkMap.get(order.getId()));
		orderVo.setReceivePayments(receivePaymentMap.get(order.getId()));
		return orderVo;
	}

	/**
	 * 组装vo
	 *
	 * @param orders
	 * @return
	 */
	private List<OrderVo> packVo(List<Order> orders) {
		if (CollectionUtils.isEmpty(orders)) {
			return List.of();
		}
		List<String> orderIds = orders.stream().map(Order::getId).distinct()
				.toList();
		Map<String, List<OrderRemark>> remarkMap = orderRemarkService
				.findByOrderIds(orderIds).stream()
				.collect(Collectors.groupingBy(OrderRemark::getOrderId));

		Map<String, List<ReceivePayment>> receivePaymentMap = receivePaymentService
				.findByOrderIds(orderIds).stream()
				.collect(Collectors.groupingBy(ReceivePayment::getOrderId));

		List<OrderVo> orderVos = new ArrayList<>();
		for (Order order : orders) {
			OrderVo orderVo = new OrderVo();
			orderVo.setRemarks(remarkMap.get(order.getId()));
			orderVo.setReceivePayments(receivePaymentMap.get(order.getId()));
			orderVo.setOrder(order);
			orderVos.add(orderVo);
		}
		return orderVos;
	}

	/**
	 * 生成付款单
	 *
	 * @param resource
	 */
	private void handleReceivePayment(Order resource) {
		ReceivePayment receivePayment = new ReceivePayment();
		receivePayment.setOrderId(resource.getId());
		receivePayment
				.setState(ReceivePaymentDef.State.PENDING_PAYMENT.getCode());
		receivePayment.setCustomerId(resource.getCustomerId());
		receivePayment.setCustomerEnterprise(resource.getCustomerEnterprise());
		receivePayment.setPayEndTime(LocalDateTime.now().plusHours(
				ReceivePaymentDef.PaymentType.ORDER_PAYMENT.getHour()));
		receivePayment.setTon(BigDecimal.valueOf(resource.getTon()));

		// 查询商品的支付模式
		switch (OrderDef.OrderType.from(resource.getOrderType())) {
			case CONSIGNMENT_PRODUCTS -> {
				ProductConsignment productConsignment = productConsignmentService
						.findOne(resource.getProductId())
						.orElse(new ProductConsignment());
				switch (ProductConsignmentDef.SettlementMethod
						.from(productConsignment.getSettlementMethod())) {
					// 全款
					case FULL_PAYMENT -> {
						receivePayment.setAmount(resource.getEstimatedAmount());
						receivePayment.setPaymentType(
								ReceivePaymentDef.PaymentType.ORDER_PAYMENT
										.getCode());

					}
					// 定金
					case PAY_DEPOSIT -> {
						receivePayment.setPaymentType(
								ReceivePaymentDef.PaymentType.ORDER_DEPOSIT
										.getCode());
						switch (ProductConsignmentDef.DepositPaymentMethod.from(
								productConsignment.getDepositPaymentMethod())) {

							// 货款比例
							case PAYMENT_RATIO -> {
								BigDecimal amount = resource
										.getEstimatedAmount()
										.multiply(productConsignment
												.getPaymentRatio())
										.divide(new BigDecimal(100), 2,
												RoundingMode.HALF_UP);
								receivePayment.setAmount(
										amount.compareTo(BigDecimal.ZERO) == 0
												? new BigDecimal("0.01")
												: amount);
							}
							// 固定金额
							case FIXED_AMOUNT -> receivePayment.setAmount(
									productConsignment.getFixedAmount());
						}

					}
				}
			}
			// todo
			case GROUP_BUYING_PRODUCTS -> {
			}
			case PICKING_UP_MISSING_PRODUCTS -> {
			}
		}
		receivePaymentService.create(receivePayment);
	}
}
