package com.zhihaoscm.service.resource.validator.product.type;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Product;
import com.zhihaoscm.domain.bean.entity.ProductType;
import com.zhihaoscm.domain.bean.json.ArrayPlacePriceType;
import com.zhihaoscm.domain.bean.json.PlacePriceType;
import com.zhihaoscm.domain.meta.biz.ProductTypeDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.ProductService;
import com.zhihaoscm.service.core.service.ProductTypeService;
import com.zhihaoscm.service.resource.form.product.type.ProductTypeForm;
import com.zhihaoscm.service.resource.validator.tag.TagValidator;

@Component
public class ProductTypeValidator {

	@Autowired
	private ProductTypeService productTypeService;
	@Autowired
	private ProductService productService;
	@Autowired
	private TagValidator tagValidator;

	public ProductType validateExist(String id) {
		return productTypeService.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30095018));
	}

	/**
	 * 校验该品类是否存在
	 *
	 * @param form
	 *
	 */
	public void validateCreateExist(ProductTypeForm form) {

		ProductType productType = productTypeService.findOne(form.getId())
				.orElse(null);
		if (Objects.nonNull(productType)) {
			throw new BadRequestException(ErrorCode.CODE_30095010);
		}
		this.validateTag(form);
	}

	/**
	 * 校验产地价格类型
	 *
	 * @param placePriceTypes
	 */
	public void validatePlacePriceTypes(ArrayPlacePriceType placePriceTypes) {
		// 校验产地价格类型默认
		long defaultCount = placePriceTypes.stream().filter(
				x -> x.getDefaultPrice().equals(CommonDef.Symbol.YES.getCode()))
				.count();
		if (defaultCount == 0) {
			throw new BadRequestException(ErrorCode.CODE_30095011);
		}
		if (defaultCount > 1) {
			throw new BadRequestException(ErrorCode.CODE_30095012);
		}
		// 校验产地价格类型名称不能重复
		Set<String> uniqueNames = new HashSet<>();
		boolean hasDuplicate = placePriceTypes.stream()
				.map(PlacePriceType::getPlacePriceType)
				.anyMatch(name -> !uniqueNames.add(name));
		if (hasDuplicate) {
			throw new BadRequestException(ErrorCode.CODE_30095013);
		}
	}

	/**
	 * 检验类型
	 *
	 * @param type
	 */
	public void validateType(Integer type) {
		// 检验类型是否存在
		if (Objects.isNull(ProductTypeDef.Type.from(type))) {
			throw new BadRequestException(ErrorCode.CODE_30095014);
		}
	}

	/**
	 * 检验区域
	 *
	 * @param region
	 */
	public void validateRegion(Integer region) {
		// 检验区域是否存在
		if (Objects.isNull(ProductTypeDef.Region.from(region))) {
			throw new BadRequestException(ErrorCode.CODE_30095015);
		}

	}

	/**
	 * 校验更新
	 *
	 * @param id
	 * @param form
	 * @return
	 */
	public ProductType validateUpdate(String id, ProductTypeForm form) {
		ProductType productType = productTypeService.findOne(id).orElse(null);
		if (Objects.isNull(productType)) {
			throw new BadRequestException(ErrorCode.CODE_30090009);
		}
		this.validateTag(form);
		return productType;
	}

	/**
	 * 删除校验
	 *
	 * @param id
	 */
	public void validateDelete(String id) {
		List<Product> products = productService.findUnclosedByProductTypeId(id);
		if (CollectionUtils.isNotEmpty(products)) {
			throw new BadRequestException(ErrorCode.CODE_30095016);
		}
	}

	/**
	 * 校验标签
	 *
	 * @param form
	 */
	private void validateTag(ProductTypeForm form) {
		this.validatePlacePriceTypes(form.getPlacePriceTypes());
		this.validateType(form.getType());
		this.validateRegion(form.getRegion());
		List<Long> tagIds = form.getTagIds();
		Set<Long> tagSet = new HashSet<>();
		if (CollectionUtils.isNotEmpty(tagIds)) {
			for (Long tagId : tagIds) {
				tagValidator.validateExist(tagId);
				if (tagSet.contains(tagId)) {
					throw new BadRequestException(ErrorCode.CODE_30095017);
				}
				tagSet.add(tagId);
			}
		}
	}
}
