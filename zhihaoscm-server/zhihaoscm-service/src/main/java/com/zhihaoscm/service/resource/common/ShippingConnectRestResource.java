package com.zhihaoscm.service.resource.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.domain.bean.vo.ShippingPlatAndTransportVo;
import com.zhihaoscm.service.core.service.ShippingRequirementPlatService;
import com.zhihaoscm.service.resource.form.shipping.connect.ShippingRequirementPlatDto;

/**
 * @program: codeSpace
 * @ClassName ShippingConnectResource
 * @description:
 * @author: 魏志鹏
 * @create: 2025-04-21 11 08
 * @Version 1.0
 **/
@RestController
@RequestMapping("/shipping-connect-rest")
public class ShippingConnectRestResource {

    @Autowired
    private ShippingRequirementPlatService shippingRequirementPlatService;
    @Autowired
    private StringRedisClient redisClient;
    
    @PostMapping("/create/plat")
    public String handleCreatePlat(
            @Validated @RequestBody ShippingRequirementPlatDto form) {
        return shippingRequirementPlatService
                .createPlat(form.convertToEntity(redisClient));

    }
    
    @PostMapping(value = "/cancel/plat/{id}")
    public void handleCancelPlat(@PathVariable String id) {
        shippingRequirementPlatService.complete(id);
        
    }

	@GetMapping("/plat-transport")
	public ShippingPlatAndTransportVo handleFindShippingPlatAndTransportVoById(
			@RequestParam String platId) {
		return shippingRequirementPlatService
				.findShippingPlatAndTransportVoById(platId).orElse(null);
	}
}
