package com.zhihaoscm.service.core.service;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.entity.ProductConsignment;
import com.zhihaoscm.domain.bean.vo.ProductConsignmentVo;
import com.zhihaoscm.domain.bean.vo.ServiceSpecialVo;

/**
 * <p>
 * 寄售商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface ProductConsignmentService
		extends MpStringIdBaseService<ProductConsignment> {

	/**
	 * 管理后台分页
	 */
	Page<ProductConsignmentVo> paging(Integer page, Integer size,
			String keyword, String productTypeId, Long supplierId,
			Integer state, List<Integer> publishState, String sortKey,
			String sortOrder);

	/**
	 * 链云砂石分页
	 */
	Page<ProductConsignmentVo> customPaging(Integer page, Integer size,
			String keyword, String productTypeId, String supplierName,
			String cityCode, Long customId);

	/**
	 * 查询商品详情
	 */
	Optional<ProductConsignmentVo> findVoById(String id, Long customId);

	/**
	 * 新增寄售商品
	 */
	ProductConsignment create(ProductConsignment product,
			List<Long> activeFileIds);

	/**
	 * 编辑商品
	 */
	ProductConsignment update(ProductConsignment product,
			List<Long> activeFileIds, List<Long> unActiveFileIds);

	/**
	 * 审核
	 */
	void audit(ProductConsignment product);

	/**
	 * 下架
	 */
	void downShelf(ProductConsignment product);

	/**
	 * 关闭
	 */
	void close(ProductConsignment product, List<Long> unActiveFileIds);

	/**
	 * 指派专员
	 *
	 * @param id
	 * @param handlerId
	 */
	void assign(String id, Long handlerId);

	/**
	 * 获取专员列表
	 */
	List<ServiceSpecialVo> findServiceSpecials(String name);
}
