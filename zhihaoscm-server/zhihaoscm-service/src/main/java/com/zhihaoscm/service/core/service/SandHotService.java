package com.zhihaoscm.service.core.service;

import java.util.List;
import java.util.Optional;

import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.SandHot;
import com.zhihaoscm.domain.bean.vo.SandHotVo;

/**
 * <p>
 * 砂石热点 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface SandHotService extends MpLongIdBaseService<SandHot> {

	/**
	 * 分页查询-管理后台
	 */
	com.zhihaoscm.common.bean.page.Page<SandHotVo> paging(Integer page,
			Integer size, String title, List<Integer> type, Integer state,
			String sortKey, String sortOrder);

	/**
	 * 分页查询-用户PC
	 */
	com.zhihaoscm.common.bean.page.Page<SandHotVo> customPaging(Integer page,
			Integer size, String title, Integer type);

	/**
	 * 分页查询相关砂石热点 不针对分身版
	 */
	com.zhihaoscm.common.bean.page.Page<SandHotVo> relatedPaging(Integer page,
			Integer size, Long sandHotId);

	/**
	 * 查找单个砂石学院数据
	 */
	Optional<SandHotVo> findVoById(Long id);

	/**
	 * 新增
	 */
	SandHot create(SandHot sandHot, List<Long> activeFileIds,
			List<Long> tagIds);

	/**
	 * 更新
	 */
	SandHot update(SandHot sandHot, List<Long> activeFileIds,
			List<Long> unActiveFileIds, List<Long> tagIds);

	/**
	 * 删除
	 *
	 * @param id
	 * @param unActiveFileIds
	 */
	void delete(Long id, List<Long> unActiveFileIds);

	/**
	 * 更新状态
	 *
	 * @param id
	 * @param state
	 */
	void updateState(Long id, Integer state);
}
