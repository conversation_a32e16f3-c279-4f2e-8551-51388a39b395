package com.zhihaoscm.service.core.processor.contract.processor.contract;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.zhihaoscm.qiyuesuo.sdk.request.Signatory;

@Service
public class ContractProcessorImpl implements ContractProcessor {

    @Override
    public void signComplete(String tableId) {

    }

    @Override
    public void signReject(String tableId, String contact) {

    }

    @Override
    public void signing(String correlationId, String name, String callbackType) {

    }

    @Override
    public void initDraftParams(String tableId, Map<Long, Signatory> signatoryMap, Map<Long, Long> customerMap) {

    }

    @Override
    public void sendInvalid(String tableId, String name) {

    }

    @Override
    public void invaliding(String tableId, String name) {

    }

    @Override
    public void invalided(String tableId, String name) {

    }

    @Override
    public void rejectInvalid(String tableId, String name) {

    }

    @Override
    public Boolean support(Integer integer) {
        return null;
    }
}
