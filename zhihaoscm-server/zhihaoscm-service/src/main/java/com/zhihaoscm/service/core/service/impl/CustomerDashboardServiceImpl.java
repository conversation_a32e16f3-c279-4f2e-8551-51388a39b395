package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.dto.DashboardScopeDto;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.vo.MemberCustomerVo;
import com.zhihaoscm.domain.bean.vo.MemberRevenueStatisticVo;
import com.zhihaoscm.domain.bean.vo.RegisterCustomerVo;
import com.zhihaoscm.domain.meta.biz.AppTypeDef;
import com.zhihaoscm.domain.meta.biz.DashboardDef;
import com.zhihaoscm.domain.meta.biz.MembershipLevelDef;
import com.zhihaoscm.domain.utils.DashboardDateUtils;
import com.zhihaoscm.service.core.service.CustomerDashboardService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.MemberOpenRecordService;

@Service
public class CustomerDashboardServiceImpl implements CustomerDashboardService {

	@Lazy
	@Autowired
	private CustomerService customerService;

	@Lazy
	@Autowired
	private MemberOpenRecordService memberOpenRecordService;

	@Override
	public Optional<RegisterCustomerVo> registeredCustomerStatistic(
			Integer scope, LocalDateTime beginTime, LocalDateTime endTime) {
		RegisterCustomerVo registerCustomerVo = new RegisterCustomerVo();
		if (Objects.nonNull(scope) && !DashboardDef.Scope.TOTAL.match(scope)) {
			beginTime = DashboardDateUtils.calcBeginDate(scope);
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}
		// 查询全部用户
		List<Customer> customerServiceAll = customerService.findAll(beginTime,
				endTime);

		// 累计注册用户
		registerCustomerVo
				.setTotalRegisterNum((long) customerServiceAll.size());
		// 采购商-链云
		long totalRegisterCustomerNum = customerServiceAll.stream().filter(
				item -> AppTypeDef.AppType.LIAN_YUN.match(item.getAppType()))
				.count();
		registerCustomerVo
				.setTotalRegisterCustomerNum(totalRegisterCustomerNum);

		// 承运商-船务
		long totalRegisterCarrierNum = customerServiceAll.stream().filter(
				item -> AppTypeDef.AppType.CHUAN_WU.match(item.getAppType()))
				.count();
		registerCustomerVo.setTotalRegisterCarrierNum(totalRegisterCarrierNum);

		// 累计推广注册
		long totalRegisterExtensionNum = customerServiceAll.stream()
				.filter(item -> item.getPromoterId() != null).count();
		registerCustomerVo
				.setTotalRegisterExtensionNum(totalRegisterExtensionNum);
		// 自行注册
		registerCustomerVo.setTotalRegisterSelfNum(
				(long) customerServiceAll.size() - totalRegisterExtensionNum);

		// 累计个人注册
		long totalPersonalCertificationNum = customerServiceAll.stream()
				.filter(item -> StringUtils.isNotBlank(item.getRealName()))
				.count();
		registerCustomerVo.setTotalPersonalCertificationNum(
				totalPersonalCertificationNum);
		// 累计组织机构认证
		long totalOrganizationCertificationNum = customerServiceAll.stream()
				.filter(item -> CommonDef.Symbol.YES
						.match(item.getApplyState()))
				.count();
		registerCustomerVo.setTotalOrganizationCertificationNum(
				totalOrganizationCertificationNum);

		return Optional.of(registerCustomerVo);
	}

	@Override
	public Optional<RegisterCustomerVo> addRegisteredCustomerAnalysis(
			Integer scope, LocalDateTime date) {
		RegisterCustomerVo registerCustomerVo = new RegisterCustomerVo();
		// 累计注册采购商
		List<Long> addRegisterCustomerNums = new ArrayList<>();

		// 累计注册承运商
		List<Long> addRegisterCarrierNums = new ArrayList<>();

		List<DashboardScopeDto> dateScope = DashboardDateUtils
				.getDateScope(scope, null, date);
		if (CollectionUtils.isNotEmpty(dateScope)) {
			dateScope.forEach(x -> {
				Long totalRegisterCustomerNum = customerService.countByAppType(
						AppTypeDef.AppType.LIAN_YUN.getCode(), x.getBeginTime(),
						x.getEndTime());
				// 采购商-链云
				addRegisterCustomerNums.add(totalRegisterCustomerNum);

				// 承运商-船务
				long totalRegisterCarrierNum = customerService.countByAppType(
						AppTypeDef.AppType.CHUAN_WU.getCode(), x.getBeginTime(),
						x.getEndTime());
				addRegisterCarrierNums.add(totalRegisterCarrierNum);
			});
		}
		registerCustomerVo.setAddRegisterCustomerNums(addRegisterCustomerNums);
		registerCustomerVo.setAddRegisterCarrierNums(addRegisterCarrierNums);
		return Optional.of(registerCustomerVo);
	}

	@Override
	public Optional<MemberCustomerVo> memberCustomerStatistic(Integer scope,
			LocalDateTime beginTime, LocalDateTime endTime) {
		if (Objects.nonNull(scope) && !DashboardDef.Scope.TOTAL.match(scope)) {
			beginTime = DashboardDateUtils.calcBeginDate(scope);
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}
		MemberCustomerVo memberCustomerVo = new MemberCustomerVo();
		long totalBaseMemberNum = customerService.countByMemberLevel(
				MembershipLevelDef.Level.BASE.getCode(), beginTime, endTime);
		memberCustomerVo.setTotalBaseMemberNum(totalBaseMemberNum);
		long totalSeniorMemberNum = customerService.countByMemberLevel(
				MembershipLevelDef.Level.SENIOR.getCode(), beginTime, endTime);
		memberCustomerVo.setTotalSeniorMemberNum(totalSeniorMemberNum);
		long totalEnterpriseGradeMemberNum = customerService.countByMemberLevel(
				MembershipLevelDef.Level.ENTERPRISE_GRADE.getCode(), beginTime,
				endTime);
		memberCustomerVo.setTotalEnterpriseGradeMemberNum(
				totalEnterpriseGradeMemberNum);
		// 累计会员数
		memberCustomerVo.setTotalMemberNum(totalBaseMemberNum
				+ totalSeniorMemberNum + totalEnterpriseGradeMemberNum);
		return Optional.of(memberCustomerVo);
	}

	@Override
	public Optional<MemberCustomerVo> addMemberCustomerStatistic(Integer scope,
			LocalDateTime beginTime, LocalDateTime endTime) {
		MemberCustomerVo memberCustomerVo = new MemberCustomerVo();
		if (Objects.nonNull(scope) && !DashboardDef.Scope.TOTAL.match(scope)) {
			beginTime = DashboardDateUtils.calcBeginDate(scope);
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}
		// 新增基础会员
		long addBaseMemberNum = customerService.countByMemberLevel(
				MembershipLevelDef.Level.BASE.getCode(), beginTime, endTime);
		// 新增高级会员
		long addSeniorMemberNum = customerService.countByMemberLevel(
				MembershipLevelDef.Level.SENIOR.getCode(), beginTime, endTime);
		// 新增企业会员
		long addEnterpriseGradeMemberNum = customerService.countByMemberLevel(
				MembershipLevelDef.Level.ENTERPRISE_GRADE.getCode(), beginTime,
				endTime);
		memberCustomerVo.setAddBaseMemberNum(addBaseMemberNum);
		memberCustomerVo.setAddSeniorMemberNum(addSeniorMemberNum);
		memberCustomerVo
				.setAddEnterpriseGradeMemberNum(addEnterpriseGradeMemberNum);
		return Optional.of(memberCustomerVo);
	}

	@Override
	public Optional<MemberCustomerVo> addMemberCustomerAnalysis(Integer scope,
			LocalDateTime date) {
		MemberCustomerVo memberCustomerVo = new MemberCustomerVo();
		// 新增基础会员
		List<Long> addBaseMemberNums = new ArrayList<>();
		// 新增高级会员
		List<Long> addSeniorMemberNums = new ArrayList<>();
		// 新增企业会员
		List<Long> addEnterpriseGradeMemberNums = new ArrayList<>();

		List<DashboardScopeDto> dateScope = DashboardDateUtils
				.getDateScope(scope, null, date);
		if (CollectionUtils.isNotEmpty(dateScope)) {
			dateScope
					.forEach(
							x -> this
									.addMemberCustomerStatistic(null,
											x.getBeginTime(), x.getEndTime())
									.ifPresent(vo -> {
										addBaseMemberNums
												.add(vo.getAddBaseMemberNum());
										addSeniorMemberNums.add(
												vo.getAddSeniorMemberNum());
										addEnterpriseGradeMemberNums.add(vo
												.getAddEnterpriseGradeMemberNum());
									}));
		}

		memberCustomerVo.setAddBaseMemberNums(addBaseMemberNums);
		memberCustomerVo.setAddSeniorMemberNums(addSeniorMemberNums);
		memberCustomerVo
				.setAddEnterpriseGradeMemberNums(addEnterpriseGradeMemberNums);
		return Optional.of(memberCustomerVo);
	}

	@Override
	public Optional<MemberCustomerVo> customerConvertAnalysis(Integer scope,
			LocalDateTime beginTime, LocalDateTime endTime) {
		if (Objects.nonNull(scope) && !DashboardDef.Scope.TOTAL.match(scope)) {
			beginTime = DashboardDateUtils.calcBeginDate(scope);
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}
		MemberCustomerVo memberCustomerVo = new MemberCustomerVo();
		// 用户数
		long registerNum = customerService.countByAppType(null, beginTime,
				endTime);
		memberCustomerVo.setRegisterNum(registerNum);

		long totalBaseMemberNum = customerService.countByMemberLevel(
				MembershipLevelDef.Level.BASE.getCode(), beginTime, endTime);
		long totalSeniorMemberNum = customerService.countByMemberLevel(
				MembershipLevelDef.Level.SENIOR.getCode(), beginTime, endTime);
		long totalEnterpriseGradeMemberNum = customerService.countByMemberLevel(
				MembershipLevelDef.Level.ENTERPRISE_GRADE.getCode(), beginTime,
				endTime);
		// 会员数
		long memberNum = totalBaseMemberNum + totalSeniorMemberNum
				+ totalEnterpriseGradeMemberNum;

		// 会员数
		memberCustomerVo.setMemberNum(memberNum);
		// 会员转化率
		if (registerNum != 0 && memberNum != 0) {
			BigDecimal memberConvertRate = new BigDecimal(memberNum)
					.multiply(new BigDecimal(100))
					.divide(new BigDecimal(registerNum), 2,
							RoundingMode.HALF_UP);
			memberCustomerVo.setMemberConvertRate(memberConvertRate);
		} else {
			memberCustomerVo.setMemberConvertRate(new BigDecimal(0));
		}

		return Optional.of(memberCustomerVo);
	}

	@Override
	public Optional<MemberRevenueStatisticVo> memberRevenueStatistic(
			Integer scope, LocalDateTime beginTime, LocalDateTime endTime) {
		if (Objects.nonNull(scope) && !DashboardDef.Scope.TOTAL.match(scope)) {
			beginTime = DashboardDateUtils.calcBeginDate(scope);
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}
		MemberRevenueStatisticVo memberRevenueStatisticVo = new MemberRevenueStatisticVo();
		memberOpenRecordService
				.findAmountByMemberLevel(null, beginTime, endTime).ifPresent(
						amount -> memberRevenueStatisticVo.setTotalMemberCost(
								amount.divide(BigDecimal.valueOf(10000), 2,
										RoundingMode.HALF_UP)));
		memberOpenRecordService.findAmountByMemberLevel(
				MembershipLevelDef.Level.BASE.getCode(), beginTime, endTime)
				.ifPresent(amount -> memberRevenueStatisticVo
						.setTotalBaseMemberCost(
								amount.divide(BigDecimal.valueOf(10000), 2,
										RoundingMode.HALF_UP)));
		memberOpenRecordService.findAmountByMemberLevel(
				MembershipLevelDef.Level.SENIOR.getCode(), beginTime, endTime)
				.ifPresent(amount -> memberRevenueStatisticVo
						.setTotalSeniorMemberCost(
								amount.divide(BigDecimal.valueOf(10000), 2,
										RoundingMode.HALF_UP)));
		memberOpenRecordService
				.findAmountByMemberLevel(
						MembershipLevelDef.Level.ENTERPRISE_GRADE.getCode(),
						beginTime, endTime)
				.ifPresent(amount -> memberRevenueStatisticVo
						.setTotalEnterpriseGradeMemberCost(
								amount.divide(BigDecimal.valueOf(10000), 2,
										RoundingMode.HALF_UP)));
		return Optional.of(memberRevenueStatisticVo);
	}

	@Override
	public Optional<MemberRevenueStatisticVo> addMemberRevenueAnalysis(
			Integer scope, LocalDateTime date) {
		MemberRevenueStatisticVo memberRevenueStatisticVo = new MemberRevenueStatisticVo();

		// 累计基础会员总费用
		List<BigDecimal> totalBaseMemberCosts = new ArrayList<>();
		// 累计高级会员总费用
		List<BigDecimal> totalSeniorMemberCosts = new ArrayList<>();
		// 累计企业会员总费用
		List<BigDecimal> totalEnterpriseGradeMemberCosts = new ArrayList<>();
		List<DashboardScopeDto> dateScope = DashboardDateUtils
				.getDateScope(scope, null, date);
		if (CollectionUtils.isNotEmpty(dateScope)) {
			dateScope.forEach(x -> {
				memberOpenRecordService
						.findAmountByMemberLevel(
								MembershipLevelDef.Level.BASE.getCode(),
								x.getBeginTime(), x.getEndTime())
						.ifPresent(amount -> totalBaseMemberCosts
								.add(amount.divide(new BigDecimal(10000), 2,
										RoundingMode.HALF_UP)));
				memberOpenRecordService
						.findAmountByMemberLevel(
								MembershipLevelDef.Level.SENIOR.getCode(),
								x.getBeginTime(), x.getEndTime())
						.ifPresent(amount -> totalSeniorMemberCosts
								.add(amount.divide(new BigDecimal(10000), 2,
										RoundingMode.HALF_UP)));
				memberOpenRecordService
						.findAmountByMemberLevel(
								MembershipLevelDef.Level.ENTERPRISE_GRADE
										.getCode(),
								x.getBeginTime(), x.getEndTime())
						.ifPresent(amount -> totalEnterpriseGradeMemberCosts
								.add(amount.divide(new BigDecimal(10000), 2,
										RoundingMode.HALF_UP)));
			});
		}

		memberRevenueStatisticVo.setTotalBaseMemberCosts(totalBaseMemberCosts);
		memberRevenueStatisticVo
				.setTotalSeniorMemberCosts(totalSeniorMemberCosts);
		memberRevenueStatisticVo.setTotalEnterpriseGradeMemberCosts(
				totalEnterpriseGradeMemberCosts);
		return Optional.of(memberRevenueStatisticVo);
	}
}
