package com.zhihaoscm.service.core.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.entity.ReceivePayment;
import com.zhihaoscm.domain.bean.vo.ReceivePaymentVo;

/**
 * <p>
 * 收款信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface ReceivePaymentService
		extends MpStringIdBaseService<ReceivePayment> {

	/**
	 * 分页查询
	 * 
	 * @param page
	 * @param size
	 * @param id
	 * @param orderId
	 * @param states
	 * @param beginTime
	 * @param endTime
	 * @param sortKey
	 * @param sortOrder
	 * @param keyword
	 * @return
	 */
	Page<ReceivePayment> paging(Integer page, Integer size, String id,
			String orderId, List<Integer> states, List<Integer> paymentTypes,
			LocalDateTime beginTime, LocalDateTime endTime, String sortKey,
			String sortOrder, String keyword);

	/**
	 * 金额查询
	 * 
	 * @return
	 */
	Optional<ReceivePaymentVo> findAmount();

	/**
	 * 根据订单id查询收款信息
	 * 
	 * @param orderIds
	 * @return
	 */
	List<ReceivePayment> findByOrderIds(List<String> orderIds);

	/**
	 * 取消付款
	 * 
	 * @param receivePayment
	 */
	void cancel(ReceivePayment receivePayment);
}
