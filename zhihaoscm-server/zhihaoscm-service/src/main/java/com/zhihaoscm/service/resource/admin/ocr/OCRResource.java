package com.zhihaoscm.service.resource.admin.ocr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.aliyun.ocr.sdk.res.IdCardInfo;
import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.service.core.service.OCRService;
import com.zhihaoscm.service.resource.validator.file.FileValidator;
import com.zhihaoscm.service.resource.validator.ocr.OCRValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "基于阿里云OCR的文件管理服务", description = "基于阿里云OCR的文件管理服务API")
@RequestMapping("/ocr")
@RestController
public class OCRResource {

	@Autowired
	private OCRService ocrService;
	@Autowired
	private FileValidator fileValidator;
	@Autowired
	private OCRValidator ocrValidator;

	@Operation(summary = "识别身份证")
	@GetMapping("/id-card")
	public ApiResponse<IdCardInfo> recognizeIdCardBackAndFace(
			@Parameter(description = "文件id") @RequestParam Long fileId,
			@Parameter(description = "身份证正反面：1（正面国徽）2（反面人像）") @RequestParam Integer type) {
		File file = fileValidator.validateExist(fileId);
		IdCardInfo info = ocrService.recognizeIdCardBackAndFace(file)
				.orElse(null);
		ocrValidator.validateIdCard(info, type);
		return new ApiResponse<>(info);
	}

}
