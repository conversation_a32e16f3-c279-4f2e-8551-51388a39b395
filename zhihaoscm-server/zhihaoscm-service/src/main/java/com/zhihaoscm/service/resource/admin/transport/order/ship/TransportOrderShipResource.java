package com.zhihaoscm.service.resource.admin.transport.order.ship;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.bean.entity.TransportOrderDetailsShip;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.entity.User;
import com.zhihaoscm.domain.bean.vo.ServiceSpecialVo;
import com.zhihaoscm.domain.bean.vo.TransportOrderShipVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderShipDef;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.core.service.TransportOrderShipService;
import com.zhihaoscm.service.core.service.usercenter.UserService;
import com.zhihaoscm.service.resource.form.transport.order.details.ship.DeliveryInfoForm;
import com.zhihaoscm.service.resource.form.transport.order.ship.ConfirmationDepartureForm;
import com.zhihaoscm.service.resource.form.transport.order.ship.TransportOrderShipForm;
import com.zhihaoscm.service.resource.validator.file.FileValidator;
import com.zhihaoscm.service.resource.validator.transport.order.details.ship.TransportOrderDetailsShipValidator;
import com.zhihaoscm.service.resource.validator.transport.order.ship.TransportOrderShipValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "船运单管理", description = "船运单管理API")
@RestController
@RequestMapping("/transport/order/ship")
public class TransportOrderShipResource {

	@Autowired
	private TransportOrderShipService transportOrderShipService;

	@Autowired
	private TransportOrderShipValidator validator;

	@Autowired
	private UserService userService;

	@Autowired
	private TransportOrderDetailsShipValidator transportOrderDetailsShipValidator;

	@Autowired
	private FileValidator fileValidator;

	@Operation(summary = "查询船运单列表")
	@GetMapping(value = "/paging")
	@Secured({ AdminPermissionDef.SHIP_DEMAND_R })
	public ApiResponse<Page<TransportOrderShipVo>> paging(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "排序字段") @RequestParam(value = "sortKey", required = false) String sortKey,
			@Parameter(description = "排序顺序, ASC升序,DESC倒序") @RequestParam(value = "sortOrder", required = false) String sortOrder,
			@Parameter(description = "货主名称或编号") @RequestParam(value = "keyword", required = false) String keyword,
			@Parameter(description = "码头管关键字") @RequestParam(value = "portKeyword", required = false) String portKeyword,
			@Parameter(description = "船舶关键字") @RequestParam(value = "shipKeyword", required = false) String shipKeyword,
			@Parameter(description = "船务专员") @RequestParam(value = "handlerName", required = false) String handlerName,
			@Parameter(description = "类型") @RequestParam(value = "type", required = false) String type,
			@Parameter(description = "状态") @RequestParam(value = "state", required = false) List<Integer> state,
			@Parameter(description = "创建日期起") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@Parameter(description = "创建日期止") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
			@Parameter(description = "用户ID") @RequestParam(required = false) Long customerId) {
		boolean hasFull = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.SHIP_DEMAND_MANAGE
						.getPermission());
		return new ApiResponse<>(
				PageUtil.convert(transportOrderShipService.paging(page, size,
						sortKey, sortOrder, keyword, portKeyword, shipKeyword,
						handlerName, type, state, beginTime, endTime, hasFull,
						Objects.requireNonNull(UserContextHolder.getUser())
								.getId(),
						customerId)));
	}

	@Operation(summary = "下拉列表")
	@GetMapping("/selector")
	public ApiResponse<List<TransportOrderShip>> selector(
			@RequestParam(value = "keyword", required = false) String keyword) {
		return new ApiResponse<>(
				transportOrderShipService.selector(keyword, null));
	}

	@Operation(summary = "船运单下拉vo")
	@GetMapping("/selector-vo")
	public ApiResponse<List<TransportOrderShipVo>> selectorVo(
			@RequestParam(value = "keyword", required = false) String keyword) {
		return new ApiResponse<>(
				transportOrderShipService.selectorVo(keyword, null));
	}

	@Operation(summary = "服务专员下拉")
	@GetMapping(value = "/specials-selector")
	public ApiResponse<List<User>> specialsSelector(
			@RequestParam(value = "keyword", required = false) String keyword) {
		return new ApiResponse<>(
				transportOrderShipService.specialsSelector(keyword));
	}

	@Operation(summary = "查询船运单详情")
	@GetMapping("/vo/{id}")
	@Secured({ AdminPermissionDef.SHIP_DEMAND_R })
	public ApiResponse<TransportOrderShipVo> findVoById(
			@PathVariable String id) {
		return new ApiResponse<>(
				transportOrderShipService.findVoById(id).orElse(null));
	}

	@Operation(summary = "船务交易进去船运单详情")
	@GetMapping("/detail/{id}")
	@Secured({ AdminPermissionDef.SHIP_DEMAND_R })
	public ApiResponse<TransportOrderShipVo> findDetail(
			@PathVariable String id) {
		validator.validateDetail(id);
		return new ApiResponse<>(
				transportOrderShipService.findVoById(id).orElse(null));
	}

	@Operation(summary = "查询专员")
	@GetMapping(value = "/find/specials")
	public ApiResponse<List<ServiceSpecialVo>> findServiceSpecials(
			@Parameter(description = "1上游专员 2下游专员") @RequestParam(value = "serviceSpecialsType") Integer serviceSpecialsType) {
		return new ApiResponse<>(transportOrderShipService
				.findServiceSpecials(serviceSpecialsType));
	}

	@Operation(summary = "新增船运单")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = LogDef.SHIPPING_ORDER_ADD, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, bizNo = "{{#_ret.getData().getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getId()}}"),
			@LogRecord.KeyValuePair(key = "#type#", value = "{{#type}}") })
	@PostMapping
	@Secured({ AdminPermissionDef.SHIP_DEMAND_MANAGE })
	public ApiResponse<TransportOrderShip> create(
			@Validated @RequestBody TransportOrderShipForm form) {
		validator.validateCustomerIsRepeat(form);
		TransportOrderShip transportOrderShip = transportOrderShipService
				.create(form.convertToEntity());
		LogRecordContext.putVariable("type", TransportOrderShipDef.Type
				.from(transportOrderShip.getType()).getName());
		return new ApiResponse<>(transportOrderShip);
	}

	@Operation(summary = "关闭船运单")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.DELETE, success = LogDef.SHIPPING_ORDER_CLOSE, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@DeleteMapping("/{id}")
	@Secured({ AdminPermissionDef.SHIP_ORDER_CLOSE })
	public ApiResponse<Void> delete(@PathVariable String id) {
		validator.validateDelete(id);
		transportOrderShipService.delete(id);
		return new ApiResponse<>();
	}

	@Operation(summary = "开始装货")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.SHIPPING_ORDER_START_LOADING, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@PutMapping("/start-loading/{id}")
	@Secured({ AdminPermissionDef.SHIP_ORDER_DEAL })
	public ApiResponse<Void> startLoading(@PathVariable String id,
			@Validated @RequestBody DeliveryInfoForm form) {
		TransportOrderDetailsShip transportOrderDetailsShip = validator
				.validateStartLoading(id, form);
		transportOrderShipService.startLoading(id,
				form.convertToEntity(transportOrderDetailsShip));
		return new ApiResponse<>();
	}

	@Operation(summary = "发航申请")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.SHIPPING_ORDER_APPLY_SAILING, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@PutMapping("/flight-application/{id}")
	@Secured({ AdminPermissionDef.SHIP_ORDER_DEAL })
	public ApiResponse<Void> flightApplication(@PathVariable String id) {
		validator.validateFlightApplication(id);
		transportOrderShipService.updateState(id,
				TransportOrderShipDef.State.AWAITING_DEPARTURE.getCode());
		return new ApiResponse<>();
	}

	@Operation(summary = "确认卸货")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.SHIPPING_ORDER_COMPLETE_UNLOADING, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@PutMapping("/confirm-unloading/{id}")
	@Secured({ AdminPermissionDef.SHIP_ORDER_DEAL })
	public ApiResponse<Void> confirmUnloading(@PathVariable String id) {
		validator.validateConfirmUnloading(id);
		transportOrderShipService.updateState(id,
				TransportOrderShipDef.State.DISCHARGED.getCode());
		return new ApiResponse<>();
	}

	@Operation(summary = "完成")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.SHIPPING_ORDER_COMPLETE, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@PutMapping("/complete/{id}")
	@Secured({ AdminPermissionDef.SHIP_DEMAND_MANAGE })
	public ApiResponse<Void> complete(@PathVariable String id) {
		validator.validateComplete(id);
		transportOrderShipService.updateState(id,
				TransportOrderShipDef.State.COMPLETED.getCode());
		return new ApiResponse<>();
	}

	@Operation(summary = "指派专员")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.ASSIGN, success = LogDef.SHIPPING_ORDER_ASSIGN, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}"),
			@LogRecord.KeyValuePair(key = "#type#", value = "{{#name}}") })
	@PutMapping(value = "/assign/{id}")
	@Secured({ AdminPermissionDef.SHIP_DEMAND_MANAGE })
	public ApiResponse<Void> assign(@PathVariable(value = "id") String id,
			@Parameter(description = "上游专员id ") @RequestParam(value = "upstreamHandlerId", required = false) Long upstreamHandlerId,
			@Parameter(description = "下游专员id") @RequestParam(value = "downstreamHandlerId", required = false) Long downstreamHandlerId) {
		transportOrderShipService.assign(id, upstreamHandlerId,
				downstreamHandlerId);
		Long userId = Objects.nonNull(upstreamHandlerId) ? upstreamHandlerId
				: downstreamHandlerId;
		LogRecordContext.putVariable("name",
				userService.findOne(userId).orElse(new User()).getName());
		return new ApiResponse<>();
	}

	@Operation(summary = "管理后台代货主发航确认")
	@PutMapping(value = "/confirmation-departure/{id}")
	@Secured(value = { AdminPermissionDef.SHIP_DEMAND_OPERATE })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE_STATUS, success = LogDef.SHIPPING_ORDER_CONFIRM_DEPARTURE, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}"),
			@LogRecord.KeyValuePair(key = "#type#", value = "{{#type}}") })
	public ApiResponse<Void> confirmationDeparture(
			@PathVariable(value = "id") String id,
			@Validated @RequestBody ConfirmationDepartureForm form) {
		TransportOrderShip transportOrderShip = transportOrderDetailsShipValidator
				.validateConfirmationDeparture(id);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipValidator
				.validateExist(id);
		LogRecordContext.putVariable("type", TransportOrderShipDef.Type
				.from(transportOrderShip.getType()).getName());
		transportOrderShipService.adminConfirmationDeparture(
				form.convertToEntity(transportOrderDetailsShip));
		return new ApiResponse<>();
	}

	@Operation(summary = "管理后台代货主同意卸货")
	@PutMapping(value = "/agree-unload/{id}")
	@Secured(value = { AdminPermissionDef.SHIP_DEMAND_OPERATE })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE_STATUS, success = LogDef.SHIPPING_ORDER_AGREE_UNLOAD, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}"),
			@LogRecord.KeyValuePair(key = "#type#", value = "{{#type}}") })
	public ApiResponse<Void> agreeUnload(
			@PathVariable(value = "id") String id) {
		TransportOrderShip transportOrderShip = transportOrderDetailsShipValidator
				.validateAgreeUnload(id);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipValidator
				.validateExist(id);
		LogRecordContext.putVariable("type", TransportOrderShipDef.Type
				.from(transportOrderShip.getType()).getName());
		transportOrderShipService.adminAgreeUnload(transportOrderDetailsShip);
		return new ApiResponse<>();
	}

	@Operation(summary = "管理后台代船主到港确认")
	@PutMapping("/confirmation-arrival-port/{id}")
	@Secured(value = { AdminPermissionDef.SHIP_DEMAND_OPERATE })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE_STATUS, success = LogDef.SHIPPING_ORDER_CONFIRM_ARRIVAL_PORT, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}"),
			@LogRecord.KeyValuePair(key = "#type#", value = "{{#type}}") })
	public ApiResponse<Void> confirmationArrivalPort(@PathVariable String id,
			@Parameter(description = "到港视频ID") @RequestParam(value = "arrivalVideoId", required = false) Long arrivalVideoId) {
		TransportOrderShip transportOrderShip = transportOrderDetailsShipValidator
				.validateConfirmationArrivalPort(id);
		LogRecordContext.putVariable("type", TransportOrderShipDef.Type
				.from(transportOrderShip.getType()).getName());
		transportOrderShipService.adminConfirmationArrivalPort(id,
				arrivalVideoId);
		return new ApiResponse<>();
	}

	@Operation(summary = "黄码港回调")
	@PostMapping("/callback")
	public String callback(@RequestBody Map<String, Object> params) {
		if (Objects.nonNull(params)) {
			log.info("黄码港回调参数:{}", JsonUtils.objectToJson(params));
			transportOrderShipService.callback(params);
		}
		return "SUCCESS";
	}

	@Operation(summary = "上传文件（水印）")
	@PostMapping(value = "/upload/watermark/{id}", consumes = "multipart/form-data")
	public ApiResponse<File> uploadWatermark(@RequestPart MultipartFile file,
			@Parameter(description = "运单id") @PathVariable String id)
			throws Exception {
		fileValidator.validateFile(file);
		TransportOrderShip transportOrderShip = validator.validateExist(id);
		File dbFile = transportOrderShipService.uploadWatermark(file,
				transportOrderShip);
		return new ApiResponse<>(dbFile);
	}

}
