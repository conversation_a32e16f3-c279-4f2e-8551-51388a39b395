package com.zhihaoscm.service.resource.form.receive.payment;

import java.math.BigDecimal;

import org.hibernate.validator.constraints.Length;

import com.zhihaoscm.domain.bean.entity.ReceivePayment;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Digits;
import lombok.Data;

@Data
public class ReceivePaymentForm {

	@Schema(title = "支付类型(1订单货款,2订单定金,3排期货款,4发航货款,5开仓货款)")
	private Integer paymentType;

	@Schema(title = "排期id")
	private String scheduleId;

	@Schema(title = "吨数")
	@DecimalMin(value = "0.01", message = "数值不能小于0")
	@DecimalMax(value = "1000000.00", message = "数值不能超过1000000")
	@Digits(integer = 7, fraction = 2, message = "整数最多7位，小数最多2位")
	private BigDecimal ton;

	@Schema(title = "吨位证明文件ID")
	private Long tonProveFileId;

	@Schema(title = "金额")
	@DecimalMin(value = "0.01", message = "数值必须大于0")
	@Digits(integer = 9, fraction = 2, message = "整数最多9位，小数最多2位")
	private BigDecimal amount;

	@Schema(title = "费用说明")
	@Length(max = 100, message = "长度不能超过100")
	private String remark;

	public ReceivePayment convertToEntity(ReceivePayment receivePayment) {
		receivePayment.setPaymentType(this.paymentType);
		receivePayment.setScheduleId(this.scheduleId);
		receivePayment.setTon(this.ton);
		receivePayment.setTonProveFileId(this.tonProveFileId);
		receivePayment.setAmount(this.amount);
		receivePayment.setRemark(this.remark);
		return receivePayment;
	}
}
