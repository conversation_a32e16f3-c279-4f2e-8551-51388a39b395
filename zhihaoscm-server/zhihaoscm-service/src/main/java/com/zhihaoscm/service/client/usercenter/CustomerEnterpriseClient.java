package com.zhihaoscm.service.client.usercenter;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.CustomerEnterprise;
import com.zhihaoscm.domain.bean.vo.CustomerEnterpriseVo;

import io.swagger.v3.oas.annotations.Parameter;

@FeignClient(name = "user-center", path = "/customer-enterprise-rest", url = "${application.config.user-center}")
public interface CustomerEnterpriseClient
		extends MpLongIdBaseClient<CustomerEnterprise> {

	@GetMapping("paging")
	Page<CustomerEnterpriseVo> paging(
			@RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@RequestParam(value = "state", required = false) Integer state,
			@RequestParam(value = "mainAccountId") Long mainAccountId,
			@RequestParam(value = "searchParam", required = false) String searchParam,
			@RequestParam(value = "sortKey") String sortKey,
			@RequestParam(value = "sortDirection") String sortDirection,
			@RequestParam(value = "accountState", required = false) Integer accountState);

	@GetMapping(value = "/invited/paging")
	Page<CustomerEnterpriseVo> invitedPaging(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_MINI_PAGE_SIZE_STR) Integer size,
			@RequestParam(value = "actualAccountId", required = false) Long actualAccountId,
			@RequestParam(value = "state", required = false) Integer state,
			@RequestParam(value = "insititutionName", required = false) String insititutionName);

	@GetMapping(value = "/find/vo")
	CustomerEnterpriseVo findVoById(@RequestParam Long id,
			@RequestParam(required = false) Integer queryType);

	@GetMapping(value = "/find/self/vo")
	CustomerEnterpriseVo findSelfVoById(@RequestParam Long subAccountId,
			@RequestParam Long mainAccountId);

	@GetMapping(value = "find/mainid")
	List<CustomerEnterprise> findByMainId(@RequestParam Long mainAccountId,
			@RequestParam(required = false) Integer state);

	@GetMapping("/invited/list")
	List<CustomerEnterpriseVo> invitedList(@RequestParam Long actualAccountId);

	@GetMapping("/find/subid")
	List<CustomerEnterprise> findBySubId(@RequestParam Long subAccountId,
			@RequestParam(required = false) Integer state);

	@GetMapping("/find/main/sub")
	CustomerEnterprise findByMainAndSubId(@RequestParam Long subAccountId,
			@RequestParam Long mainAccountId,
			@RequestParam(required = false) Integer state);

	@GetMapping("/find/main/state")
	List<CustomerEnterprise> findByMainIdAndState(
			@RequestParam Long mainAccountId,
			@RequestParam(required = false) Integer state);

	@PostMapping("/confirm")
	void confirm(@RequestParam Long id,
			@RequestParam(required = false) Integer state);

	@PostMapping("/delete")
	void delete(@RequestParam Long id,
			@RequestParam(required = false) Integer operationType);

	@PostMapping("/batch/update/state")
	void batchUpdateAccountState(@RequestBody List<Long> mainIds,
			@RequestParam Integer accountState);

	@PostMapping("/refresh/subaccount/context")
	List<Long> refreshSubAccountContext(@RequestParam Long mainAccountId);

}
