package com.zhihaoscm.service.resource.admin.business.opportunity;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.BusinessOpportunity;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.service.core.service.BusinessOpportunityService;
import com.zhihaoscm.service.resource.form.business.opportunity.BusinessOpportunityAuditForm;
import com.zhihaoscm.service.resource.form.business.opportunity.BusinessOpportunityForm;
import com.zhihaoscm.service.resource.form.business.opportunity.BusinessOpportunityTakedownForm;
import com.zhihaoscm.service.resource.validator.business.opportunity.BusinessOpportunityValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * <p>
 * 商机 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@RestController
@RequestMapping("/business-opportunity")
public class BusinessOpportunityResource {

	@Autowired
	private BusinessOpportunityService service;
	@Autowired
	private BusinessOpportunityValidator validator;

	@Operation(summary = "分页查询")
	@GetMapping("/paging")
	@Secured({ AdminPermissionDef.BUSINESS_R, AdminPermissionDef.BUSINESS_W,
			AdminPermissionDef.BUSINESS_REVIEW })
	public ApiResponse<Page<BusinessOpportunity>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "标题") @RequestParam(required = false) String keyword,
			@Parameter(description = "信息类型") @RequestParam(required = false) List<Integer> type,
			@Parameter(description = "状态") @RequestParam(required = false) List<Integer> state,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder) {
		return new ApiResponse<>(PageUtil.convert(service.paging(page, size,
				keyword, type, state, sortKey, sortOrder)));
	}

	@Operation(summary = "查询商机详情")
	@GetMapping("/{id}")
	@Secured({ AdminPermissionDef.BUSINESS_R, AdminPermissionDef.BUSINESS_W,
			AdminPermissionDef.BUSINESS_REVIEW })
	public ApiResponse<BusinessOpportunity> findById(@PathVariable Long id) {
		return new ApiResponse<>(service.findOne(id).orElse(null));
	}

	@Operation(summary = "新增商机")
	@PostMapping
	@Secured({ AdminPermissionDef.BUSINESS_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = LogDef.BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT_ADD, type = LogDef.BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT, bizNo = "{{#_ret.getData().getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#_ret.getData().getTitle()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getId()}}") })
	public ApiResponse<BusinessOpportunity> create(
			@Validated @RequestBody BusinessOpportunityForm form) {
		BusinessOpportunity businessOpportunity = validator
				.validateCreate(form);
		return new ApiResponse<>(service.create(businessOpportunity));
	}

	@Operation(summary = "编辑商机")
	@PutMapping("/{id}")
	@Secured({ AdminPermissionDef.BUSINESS_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT_EDIT, type = LogDef.BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#_ret.getData().getTitle()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	public ApiResponse<BusinessOpportunity> update(@PathVariable Long id,
			@Validated @RequestBody BusinessOpportunityForm form) {
		BusinessOpportunity businessOpportunity = validator.validateUpdate(id,
				form);
		return new ApiResponse<>(
				service.updateAllProperties(businessOpportunity));
	}

	@Operation(summary = "审核商机")
	@PutMapping("/audit/{id}")
	@Secured({ AdminPermissionDef.BUSINESS_REVIEW })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE_STATUS, success = LogDef.BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT_AUDIT, type = LogDef.BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#result.getTitle()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	public ApiResponse<Void> audit(@PathVariable Long id,
			@RequestBody BusinessOpportunityAuditForm auditForm) {
		BusinessOpportunity businessOpportunity = validator.validateAudit(id,
				auditForm);
		service.audit(businessOpportunity);
		BusinessOpportunity result = service.findOne(id)
				.orElse(new BusinessOpportunity());
		LogRecordContext.putVariable("result", result);
		return new ApiResponse<>();
	}

	@Operation(summary = "下架商机")
	@PutMapping("/down-shelf/{id}")
	@Secured({ AdminPermissionDef.BUSINESS_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE_STATUS, success = LogDef.BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT_TAKE_OFF_SHELF, type = LogDef.BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#result.getTitle()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	public ApiResponse<Void> downShelf(@PathVariable Long id,
			@RequestBody BusinessOpportunityTakedownForm form) {
		BusinessOpportunity businessOpportunity = validator
				.validateDownShelf(id, form);
		service.downShelf(businessOpportunity);
		BusinessOpportunity result = service.findOne(id)
				.orElse(new BusinessOpportunity());
		LogRecordContext.putVariable("result", result);
		return new ApiResponse<>();
	}

	@Operation(summary = "删除商机")
	@DeleteMapping(value = "/{id}")
	@Secured({ AdminPermissionDef.BUSINESS_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.DELETE, success = LogDef.BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT_DEL, type = LogDef.BUSINESS_OPPORTUNITY_MANAGEMENT_BUSINESS_OPPORTUNITY_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#prd_type_name#", value = "{{#result.getTitle()}}"),
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	public ApiResponse<Void> delete(@PathVariable Long id) {

		validator.validateDelete(id);
		BusinessOpportunity result = service.findOne(id)
				.orElse(new BusinessOpportunity());
		LogRecordContext.putVariable("result", result);
		service.delete(id);
		return new ApiResponse<>();
	}
}
