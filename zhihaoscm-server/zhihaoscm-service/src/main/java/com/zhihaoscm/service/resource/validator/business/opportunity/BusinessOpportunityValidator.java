package com.zhihaoscm.service.resource.validator.business.opportunity;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.BusinessOpportunity;
import com.zhihaoscm.domain.bean.json.AuditInfo;
import com.zhihaoscm.domain.bean.json.TakedownInfo;
import com.zhihaoscm.domain.meta.biz.BusinessOpportunityDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.BusinessOpportunityCollectService;
import com.zhihaoscm.service.core.service.BusinessOpportunityService;
import com.zhihaoscm.service.resource.form.business.opportunity.BusinessOpportunityAuditForm;
import com.zhihaoscm.service.resource.form.business.opportunity.BusinessOpportunityForm;
import com.zhihaoscm.service.resource.form.business.opportunity.BusinessOpportunityTakedownForm;

/**
 * 油品费用校验器
 */
@Component
public class BusinessOpportunityValidator {

	@Autowired
	private BusinessOpportunityService service;
	@Autowired
	private BusinessOpportunityCollectService collectService;

	/**
	 * 校验数据是否存在
	 *
	 * @param id
	 * @return
	 */
	public BusinessOpportunity validateExist(Long id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30194001));
	}

	/**
	 * 校验创建
	 */
	public BusinessOpportunity validateCreate(BusinessOpportunityForm form) {
		BusinessOpportunity businessOpportunity = form.convertToEntity();
		if (Objects.isNull(businessOpportunity.getIsRelatedOwner())) {
			throw new BadRequestException(ErrorCode.CODE_30194024);
		}
		// 如果“是否关联用户”为否，设置关联用户id为空，否则校验不能为空
		if (CommonDef.Symbol.NO
				.match(businessOpportunity.getIsRelatedOwner())) {
			businessOpportunity.setOwnerId(null);
		} else {
			if (Objects.isNull(businessOpportunity.getOwnerId())) {
				throw new BadRequestException(ErrorCode.CODE_30194015);
			}
		}
		return businessOpportunity;
	}

	/**
	 * 校验编辑
	 */
	public BusinessOpportunity validateUpdate(Long id,
			BusinessOpportunityForm form) {
		BusinessOpportunity businessOpportunity = this.validateExist(id);
		// 待审核、未通过、已下架才能编辑
		if (!BusinessOpportunityDef.State.TO_BE_REVIEWED
				.match(businessOpportunity.getState())
				&& !BusinessOpportunityDef.State.FAILED
						.match(businessOpportunity.getState())
				&& !BusinessOpportunityDef.State.OFF_SHELF
						.match(businessOpportunity.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30194019);
		}
		return form.convertToEntity(businessOpportunity);
	}

	/**
	 * 校验审核
	 */
	public BusinessOpportunity validateAudit(Long id,
			BusinessOpportunityAuditForm auditForm) {
		BusinessOpportunity businessOpportunity = this.validateExist(id);
		// 待审核才能审核
		if (!BusinessOpportunityDef.State.TO_BE_REVIEWED
				.match(businessOpportunity.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30194018);
		}
		// 设置审核信息
		AuditInfo auditInfo = new AuditInfo();
		auditInfo.setState(auditForm.getState());
		// 不通过：未通过原因必填，备注选填
		if (CommonDef.Symbol.NO.match(auditForm.getState())) {
			if (Objects.isNull(auditForm.getFailedReason())) {
				throw new BadRequestException(ErrorCode.CODE_30194008);
			}
			auditInfo.setFailedReason(auditForm.getFailedReason());
			auditInfo.setRemark(auditForm.getRemark());
		} else {
			// 通过：联系方式必填
			if (Objects.isNull(auditForm.getContactMobile())) {
				throw new BadRequestException(ErrorCode.CODE_30194011);
			}
			auditInfo.setContactMobile(auditForm.getContactMobile());
		}
		businessOpportunity.setAuditInfo(auditInfo);
		return businessOpportunity;
	}

	/**
	 * 校验下架
	 */
	public BusinessOpportunity validateDownShelf(Long id,
			BusinessOpportunityTakedownForm form) {
		BusinessOpportunity businessOpportunity = this.validateExist(id);
		// 上架中才能下架
		if (!BusinessOpportunityDef.State.ON_SHELF
				.match(businessOpportunity.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30194021);
		}
		// 设置下架信息
		TakedownInfo takedownInfo = new TakedownInfo();
		takedownInfo.setDownShelfReason(form.getDownShelfReason());
		takedownInfo.setRemark(form.getRemark());
		businessOpportunity.setTakedownInfo(takedownInfo);
		return businessOpportunity;
	}

	/**
	 * 校验删除
	 */
	public void validateDelete(Long id) {
		BusinessOpportunity businessOpportunity = this.validateExist(id);
		// 未通过、已下架才能删除
		if (!BusinessOpportunityDef.State.FAILED
				.match(businessOpportunity.getState())
				&& !BusinessOpportunityDef.State.OFF_SHELF
						.match(businessOpportunity.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30194020);
		}
	}

	/**
	 * 校验用户收藏、取消收藏
	 * 
	 * @param id
	 * @param state
	 * @param customId
	 */
	public void validateCollect(Long id, Integer state, Long customId) {
		// 收藏商机列表还需要展示 已下架 或 被删除 或 待审核 的，显示效果就是已失效
		BusinessOpportunity businessOpportunity = service.findOneWithDeleted(id)
				.orElse(null);
		if (Objects.isNull(businessOpportunity)) {
			throw new BadRequestException(ErrorCode.CODE_30194001);
		}

		if (CommonDef.Symbol.YES.match(state)) {
			// 收藏操作
			// 未收藏才能收藏
			if (collectService.findByCustomIdAndBusinessId(customId, id)
					.isPresent()) {
				throw new BadRequestException(ErrorCode.CODE_30194022);
			}
		} else {
			// 取消收藏操作
			// 收藏了才能取消收藏
			if (collectService.findByCustomIdAndBusinessId(customId, id)
					.isEmpty()) {
				throw new BadRequestException(ErrorCode.CODE_30194023);
			}
		}

	}

}
