package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.CompositeIndex;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.CompositeIndexCountVo;
import com.zhihaoscm.domain.bean.vo.CompositeIndexStatisticVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.CompositeIndexDef;
import com.zhihaoscm.domain.meta.biz.DashboardDef;
import com.zhihaoscm.domain.meta.biz.WxwDef;
import com.zhihaoscm.domain.utils.DashboardDateUtils;
import com.zhihaoscm.service.core.mapper.CompositeIndexMapper;
import com.zhihaoscm.service.core.service.CompositeIndexService;
import com.zhihaoscm.service.core.service.MessageService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

/**
 * <p>
 * 综合指数 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@Service
public class CompositeIndexServiceImpl
		extends MpLongIdBaseServiceImpl<CompositeIndex, CompositeIndexMapper>
		implements CompositeIndexService {

	public CompositeIndexServiceImpl(CompositeIndexMapper repository) {
		super(repository);
	}

	@Autowired
	private UserService userService;

	@Autowired
	private MessageService messageService;

	@Override
	public Page<CompositeIndex> paging(Integer page, Integer size,
			LocalDateTime priceDate, Integer state, Integer publishType) {
		LambdaQueryWrapper<CompositeIndex> wrapper = Wrappers
				.lambdaQuery(CompositeIndex.class)
				.eq(Objects.nonNull(priceDate), CompositeIndex::getPriceDate,
						priceDate)
				.eq(Objects.nonNull(state), CompositeIndex::getState, state)
				.eq(CompositeIndex::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Objects.nonNull(publishType),
						CompositeIndex::getPublishType, publishType)
				.orderByDesc(CompositeIndex::getPriceDate,
						CompositeIndex::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), wrapper);

	}

	@Override
	public Optional<CompositeIndex> findByPriceDate(LocalDateTime priceDate) {
		LambdaQueryWrapper<CompositeIndex> wrapper = Wrappers
				.lambdaQuery(CompositeIndex.class)
				.eq(CompositeIndex::getPriceDate, priceDate)
				.eq(CompositeIndex::getDel, CommonDef.Symbol.NO.getCode());
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public List<CompositeIndex> findByPriceDate(LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<CompositeIndex> wrapper = Wrappers
				.lambdaQuery(CompositeIndex.class)
				.ge(CompositeIndex::getPriceDate, beginTime)
				.le(CompositeIndex::getPriceDate, endTime)
				.eq(CompositeIndex::getState,
						CompositeIndexDef.State.PUBLISHED.getCode())
				.eq(CompositeIndex::getDel, CommonDef.Symbol.NO.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<CompositeIndexStatisticVo> detail() {
		CompositeIndexStatisticVo compositeIndexStatisticVo = new CompositeIndexStatisticVo();
		// 计算最新综合指数
		this.findLatestDate(null).ifPresent(lastIndex -> {
			LocalDateTime priceDate = lastIndex.getPriceDate();
			compositeIndexStatisticVo.setNewIndexDate(priceDate);
			compositeIndexStatisticVo
					.setNewCompositeIndex(lastIndex.getCompositeIndex());
			compositeIndexStatisticVo
					.setNewNaturalSandIndex(lastIndex.getNaturalSandIndex());
			compositeIndexStatisticVo
					.setNewGravelIndex(lastIndex.getGravelIndex());
			compositeIndexStatisticVo.setNewMechanizedSandIndex(
					lastIndex.getMechanizedSandIndex());

			LocalDateTime date = priceDate.minusDays(1).with(LocalTime.MIN);
			this.findLatestDate(date).ifPresent(lastSecondIndex -> {
				// 前一日的指数不为空且前两日的数据不能为空且不能为0
				if (Objects.nonNull(lastIndex.getCompositeIndex())
						&& Objects.nonNull(lastSecondIndex.getCompositeIndex())
						&& BigDecimal.ZERO.compareTo(
								lastSecondIndex.getCompositeIndex()) != 0) {
					BigDecimal subtract = lastIndex.getCompositeIndex()
							.subtract(lastSecondIndex.getCompositeIndex());
					compositeIndexStatisticVo
							.setCompositeIndexChangeAmount(subtract);
					compositeIndexStatisticVo
							.setCompositeIndexChangePercent(subtract
									.divide(lastSecondIndex.getCompositeIndex(),
											4, RoundingMode.HALF_UP)
									.multiply(new BigDecimal(100)));
				}
				// 天然砂
				if (Objects.nonNull(lastIndex.getNaturalSandIndex())
						&& Objects
								.nonNull(lastSecondIndex.getNaturalSandIndex())
						&& BigDecimal.ZERO.compareTo(
								lastSecondIndex.getNaturalSandIndex()) != 0) {
					BigDecimal subtract = lastIndex.getNaturalSandIndex()
							.subtract(lastSecondIndex.getNaturalSandIndex());
					compositeIndexStatisticVo
							.setNaturalSandIndexChangeAmount(subtract);
					compositeIndexStatisticVo
							.setNaturalSandIndexChangePercent(subtract
									.divide(lastSecondIndex
											.getNaturalSandIndex(), 4,
											RoundingMode.HALF_UP)
									.multiply(new BigDecimal(100)));
				}
				// 碎石
				if (Objects.nonNull(lastIndex.getGravelIndex())
						&& Objects.nonNull(lastSecondIndex.getGravelIndex())
						&& BigDecimal.ZERO.compareTo(
								lastSecondIndex.getGravelIndex()) != 0) {
					BigDecimal subtract = lastIndex.getGravelIndex()
							.subtract(lastSecondIndex.getGravelIndex());
					compositeIndexStatisticVo
							.setGravelIndexChangeAmount(subtract);
					compositeIndexStatisticVo
							.setGravelIndexChangePercent(subtract
									.divide(lastSecondIndex.getGravelIndex(), 4,
											RoundingMode.HALF_UP)
									.multiply(new BigDecimal(100)));
				}
				// 机制砂
				if (Objects.nonNull(lastIndex.getMechanizedSandIndex())
						&& Objects.nonNull(
								lastSecondIndex.getMechanizedSandIndex())
						&& BigDecimal.ZERO.compareTo(lastSecondIndex
								.getMechanizedSandIndex()) != 0) {
					BigDecimal subtract = lastIndex.getMechanizedSandIndex()
							.subtract(lastSecondIndex.getMechanizedSandIndex());
					compositeIndexStatisticVo
							.setMechanizedSandIndexChangeAmount(subtract);
					compositeIndexStatisticVo
							.setMechanizedSandIndexChangePercent(subtract
									.divide(lastSecondIndex
											.getMechanizedSandIndex(), 4,
											RoundingMode.HALF_UP)
									.multiply(new BigDecimal(100)));
				}
			});
		});
		return Optional.of(compositeIndexStatisticVo);
	}

	@Override
	public Optional<CompositeIndex> findLatestDate(LocalDateTime date) {
		LambdaQueryWrapper<CompositeIndex> wrapper = Wrappers
				.lambdaQuery(CompositeIndex.class)
				.eq(CompositeIndex::getState,
						CompositeIndexDef.State.PUBLISHED.getCode())
				.eq(CompositeIndex::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Objects.nonNull(date), CompositeIndex::getPriceDate, date)
				.orderByDesc(CompositeIndex::getPriceDate).last("limit 1");
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public Optional<CompositeIndexStatisticVo> line(Integer scope) {
		CompositeIndexStatisticVo vo = new CompositeIndexStatisticVo();
		switch (CompositeIndexDef.Scope.from(scope)) {
			case LATEST_ONE_WEEK ->
				this.protract(CompositeIndexDef.Scope.LATEST_ONE_WEEK, vo);
			case LATEST_ONE_MONTH ->
				this.protract(CompositeIndexDef.Scope.LATEST_ONE_MONTH, vo);
			case LATEST_ONE_YEAR -> {
				this.protract(CompositeIndexDef.Scope.LATEST_ONE_YEAR, vo);
				List<CompositeIndex> compositeIndexLines = vo
						.getCompositeIndexLines();
				List<CompositeIndex> list = compositeIndexLines.stream().filter(
						item -> !(Objects.nonNull(item.getNaturalSandIndex())
								&& BigDecimal.ZERO.compareTo(
										item.getNaturalSandIndex()) == 0
								&& Objects.nonNull(item.getCompositeIndex())
								&& BigDecimal.ZERO.compareTo(
										item.getCompositeIndex()) == 0
								&& Objects.nonNull(item.getGravelIndex())
								&& BigDecimal.ZERO
										.compareTo(item.getGravelIndex()) == 0
								&& Objects
										.nonNull(item.getMechanizedSandIndex())
								&& BigDecimal.ZERO.compareTo(
										item.getMechanizedSandIndex()) == 0))
						.toList();
				vo.setCompositeIndexLines(list);
			}
		}
		return Optional.of(vo);
	}

	@Override
	public CompositeIndexCountVo statisticsCompositeIndex(Boolean hasFull) {
		CompositeIndexCountVo compositeIndexCountVo = new CompositeIndexCountVo();
		compositeIndexCountVo.setUnPublished(0L);
		if (hasFull) {
			LambdaQueryWrapper<CompositeIndex> queryWrapper = Wrappers
					.lambdaQuery(CompositeIndex.class);
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(CompositeIndex::getState,
					CompositeIndexDef.State.UNPUBLISHED.getCode());
			Long unPublished = repository.selectCount(queryWrapper);
			compositeIndexCountVo.setUnPublished(unPublished);
			queryWrapper.clear();
		}
		return compositeIndexCountVo;
	}

	@Override
	public List<CompositeIndex> batchCreate(List<CompositeIndex> list) {
		List<CompositeIndex> compositeIndexList = super.batchCreate(list);
		if (CollectionUtils.isNotEmpty(compositeIndexList)) {
			for (CompositeIndex form : compositeIndexList) {
				sendPublishNotice(form);
			}
		}
		return list;
	}

	@Override
	public CompositeIndex updateAllProperties(CompositeIndex resource) {
		CompositeIndex compositeIndex = super.updateAllProperties(resource);
		sendPublishNotice(compositeIndex);
		return compositeIndex;
	}

	@Override
	public void publish(Long id) {
		LambdaUpdateWrapper<CompositeIndex> wrapper = Wrappers
				.lambdaUpdate(CompositeIndex.class);
		wrapper.eq(CompositeIndex::getId, id);
		wrapper.set(CompositeIndex::getState,
				CompositeIndexDef.State.PUBLISHED.getCode());
		wrapper.set(CompositeIndex::getPublishType,
				CompositeIndexDef.PublishType.MANUAL.getCode());
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (Objects.nonNull(context)) {
			Long userId = context.getUserId();
			wrapper.set(CompositeIndex::getUpdatedBy, userId);
		}
		repository.update(wrapper);
	}

	@Override
	public void revoke(Long id) {
		LambdaUpdateWrapper<CompositeIndex> wrapper = Wrappers
				.lambdaUpdate(CompositeIndex.class);
		wrapper.eq(CompositeIndex::getId, id);
		wrapper.set(CompositeIndex::getState,
				CompositeIndexDef.State.UNPUBLISHED.getCode());
		wrapper.set(CompositeIndex::getPublishType, null);
		// 置空版本ID
		wrapper.set(CompositeIndex::getVersionId, null);
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (Objects.nonNull(context)) {
			Long userId = context.getUserId();
			wrapper.set(CompositeIndex::getUpdatedBy, userId);
		}
		repository.update(wrapper);

		this.findOne(id).ifPresent(compositeIndex -> messageService
				.sendNotice(WxwMessage.builder().receiptors(userService
						.findUsersByPermission(
								AdminPermissionDef.COMPSITE_INDEX_DEAL, null)
						.stream().map(user -> String.valueOf(user.getId()))
						.toList())
						.url("/indexResearch/indexManage/compositeIndex/info/"
								.concat(String.valueOf(compositeIndex.getId())))
						.prefix(WxwDef.NoticePrefix.YOU_SUBMIT.getDesc())
						.operationModule(
								WxwDef.NoticeOperationModule.COMPOSITE_INDEX
										.getDesc())
						.desc("已撤回")
						.keyword(String.valueOf(
								compositeIndex.getPriceDate().toLocalDate()))
						.content(StringUtils.EMPTY).build()));
	}

	/**
	 * 绘制折线图数据
	 *
	 * @param scope
	 * @param vo
	 * @return
	 */
	private void protract(CompositeIndexDef.Scope scope,
			CompositeIndexStatisticVo vo) {
		// 获取最新的综合指数日期
		LocalDateTime latestDate = LocalDateTime.now().with(LocalTime.MIN);
		// 初始化最近一周的综合指数
		Map<LocalDateTime, CompositeIndex> lineMap = Stream
				.iterate(latestDate,
						localDate -> localDate.minusDays(1).with(LocalTime.MIN))
				.limit(this.calcScope(latestDate, scope)).collect(
						Collectors.toMap(key -> key, this::initCompositeIndex));

		List<LocalDateTime> dateTimeList = lineMap.keySet().stream().toList();

		// 获取最小时间
		LocalDateTime beginTime = Collections.min(dateTimeList);

		// 获取最大时间
		LocalDateTime endTime = Collections.max(dateTimeList);

		// 综合砂石指数
		List<CompositeIndex> compositeIndexList = this
				.findByPriceDate(beginTime, endTime);
		compositeIndexList.stream().collect(
				Collectors.toMap(CompositeIndex::getPriceDate, index -> index))
				.forEach((key, value) -> {
					if (lineMap.containsKey(key)) {
						lineMap.put(key, value);
					}
				});
		vo.setCompositeIndexLines(lineMap.values().stream()
				.sorted(Comparator.comparing(CompositeIndex::getPriceDate))
				.toList());

	}

	/**
	 * 计算月份天数
	 *
	 * @return
	 */
	private Integer calcScope(LocalDateTime latestDate,
			CompositeIndexDef.Scope scope) {
		return switch (scope) {
			case LATEST_ONE_WEEK -> 7;
			case LATEST_ONE_MONTH -> 1 + Math.toIntExact(Duration.between(
					Objects.requireNonNull(DashboardDateUtils
							.calcBeginDate(DashboardDef.Scope.MONTH.getCode())),
					latestDate).toDays());
			case LATEST_ONE_YEAR -> 1 + Math.toIntExact(Duration.between(
					Objects.requireNonNull(DashboardDateUtils
							.calcBeginDate(DashboardDef.Scope.YEAR.getCode())),
					latestDate).toDays());
		};
	}

	/**
	 * 初始化最近一周的综合指数
	 *
	 * @param date
	 * @return
	 */
	private CompositeIndex initCompositeIndex(LocalDateTime date) {
		CompositeIndex index = new CompositeIndex();
		index.setPriceDate(date);
		index.setCompositeIndex(BigDecimal.ZERO);
		index.setMechanizedSandIndex(BigDecimal.ZERO);
		index.setGravelIndex(BigDecimal.ZERO);
		index.setNaturalSandIndex(BigDecimal.ZERO);
		return index;
	}

	/**
	 * 发送发布通知
	 *
	 * @param resource
	 */
	private void sendPublishNotice(CompositeIndex resource) {
		if (CompositeIndexDef.State.UNPUBLISHED.match(resource.getState())) {
			messageService.sendNotice(WxwMessage.builder()
					.receiptors(userService
							.findUsersByPermission(
									AdminPermissionDef.COMPSITE_INDEX_MANAGE,
									null)
							.stream().map(user -> String.valueOf(user.getId()))
							.toList())
					.url("/indexResearch/indexManage/compositeIndex/info/"
							.concat(String.valueOf(resource.getId())))
					.prefix(WxwDef.NoticePrefix.YOU_HAVE_NEW.getDesc())
					.operationModule(
							WxwDef.NoticeOperationModule.COMPOSITE_INDEX
									.getDesc())
					.desc("待发布")
					.keyword(String
							.valueOf(resource.getPriceDate().toLocalDate()))
					.content(StringUtils.EMPTY).build());
		}
	}

}
