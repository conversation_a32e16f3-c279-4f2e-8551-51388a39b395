package com.zhihaoscm.service.resource.admin.oil.site;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.OilSite;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.service.core.service.OilSiteService;
import com.zhihaoscm.service.resource.form.oil.site.OilSiteForm;
import com.zhihaoscm.service.resource.validator.oil.site.OilSiteValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 油品站点 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@Tag(name = "油品站点", description = "油品站点API")
@RestController
@RequestMapping("/oil-site")
public class OilSiteResource {

	@Autowired
	private OilSiteService oilSiteService;
	@Autowired
	private OilSiteValidator oilSiteValidator;

	@GetMapping("/paging")
	@Operation(summary = "分页查询站点")
	@Secured({ AdminPermissionDef.GAS_STATION_R,
			AdminPermissionDef.GAS_STATION_W })
	public ApiResponse<Page<OilSite>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "站点名称或编号") @RequestParam(required = false) String keyword,
			@Parameter(description = "站点品牌") @RequestParam(required = false) String brand,
			@Parameter(description = "站点状态") @RequestParam(required = false) Integer state,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder) {
		return new ApiResponse<>(PageUtil.convert(oilSiteService.paging(page,
				size, keyword, brand, state, sortKey, sortOrder)));
	}

	@Operation(summary = "站点下拉列表")
	@GetMapping("/paging-selector")
	public ApiResponse<Page<OilSite>> pagingSelector(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "站点名称") @RequestParam(required = false) String name) {
		return new ApiResponse<>(PageUtil
				.convert(oilSiteService.pagingSelector(page, size, name)));
	}

	@Operation(summary = "查找单个站点详情数据")
	@GetMapping("/{id}")
	@Secured({ AdminPermissionDef.GAS_STATION_R,
			AdminPermissionDef.GAS_STATION_W })
	public ApiResponse<OilSite> findById(@PathVariable Long id) {
		return new ApiResponse<>(oilSiteValidator.validateExist(id));
	}

	@Operation(summary = "添加站点")
	@PostMapping
	@Secured({ AdminPermissionDef.GAS_STATION_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = LogDef.OIL_SITE_ADD, type = LogDef.OIL_SITE_MANAGEMENT, bizNo = "{{#_ret.getData().getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getName()}}") })
	public ApiResponse<OilSite> create(
			@Validated @RequestBody OilSiteForm oilSiteForm) {
		OilSite oilSite = oilSiteValidator.validateCreate(oilSiteForm);
		return new ApiResponse<>(oilSiteService.create(oilSite));
	}

	@Operation(summary = "修改站点")
	@PutMapping("/{id}")
	@Secured({ AdminPermissionDef.GAS_STATION_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.OIL_SITE_EDIT, type = LogDef.OIL_SITE_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#oilSiteForm.getName()}}") })
	public ApiResponse<OilSite> update(@PathVariable Long id,
			@Validated @RequestBody OilSiteForm oilSiteForm) {
		OilSite oilSite = oilSiteValidator.validateUpdate(id, oilSiteForm);
		return new ApiResponse<>(oilSiteService.updateAllProperties(oilSite));
	}

	@Operation(summary = "删除站点")
	@DeleteMapping("/{id}")
	@Secured({ AdminPermissionDef.GAS_STATION_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.DELETE, success = LogDef.OIL_SITE_DELETE, type = LogDef.OIL_SITE_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#oilSite.getName()}}") })
	public ApiResponse<Void> delete(@PathVariable Long id) {
		oilSiteValidator.validateDelete(id);
		LogRecordContext.putVariable("oilSite",
				oilSiteService.findOne(id).orElse(new OilSite()));
		oilSiteService.delete(id);
		return new ApiResponse<>();
	}

	@Operation(summary = "更新状态")
	@PostMapping("/update/{id}/{state}")
	@Secured({ AdminPermissionDef.GAS_STATION_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = "{{#success}}", type = LogDef.OIL_SITE_MANAGEMENT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#title}}") })
	public ApiResponse<Void> updateState(@PathVariable Long id,
			@Parameter(description = "1 启用 0 禁用") @PathVariable Integer state) {
		oilSiteValidator.validateUpdateState(id, state);
		oilSiteService.updateState(id, state);
		OilSite oilSite = oilSiteService.findOne(id).orElse(new OilSite());
		LogRecordContext.putVariable("title", oilSite.getName());
		if (CommonDef.Symbol.YES.match(state)) {
			LogRecordContext.putVariable("success", LogDef.OIL_SITE_ENABLE);
		} else {
			LogRecordContext.putVariable("success", LogDef.OIL_SITE_DISABLE);
		}
		return new ApiResponse<>();
	}
}
