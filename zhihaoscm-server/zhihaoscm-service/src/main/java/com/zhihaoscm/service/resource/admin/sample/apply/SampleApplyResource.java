package com.zhihaoscm.service.resource.admin.sample.apply;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.SampleApply;
import com.zhihaoscm.service.core.service.SampleApplyService;
import com.zhihaoscm.service.resource.form.sample.apply.SampleApplyHandleForm;
import com.zhihaoscm.service.resource.validator.sample.apply.SampleApplyValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 申请寄样信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Tag(name = "申请寄样信息表", description = "申请寄样信息表API")
@RestController
@RequestMapping("/sample-apply")
public class SampleApplyResource {

	@Autowired
	private SampleApplyService service;

	@Autowired
	private SampleApplyValidator validator;

	@Operation(summary = "分页查询")
	@GetMapping("/paging")
	public ApiResponse<Page<SampleApply>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "商品ID") @RequestParam(required = false) String productId,
			@Parameter(description = "状态（1待处理 2已寄出 3无效信息）") @RequestParam(required = false) Integer state,
			@Parameter(description = "申请日期开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@Parameter(description = "申请日期开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder,
			@Parameter(description = "采购账号信息：实名、组织机构认证、手机号，模糊搜索") @RequestParam(required = false) String keyword) {
		return new ApiResponse<>(
				PageUtil.convert(service.paging(page, size, productId, state,
						beginTime, endTime, sortKey, sortOrder, keyword)));
	}

	@Operation(summary = "处理")
	@PutMapping("/handle/{id}")
	public ApiResponse<Void> handle(@PathVariable Long id,
			@Validated @RequestBody SampleApplyHandleForm form) {
		SampleApply sampleApply = validator.validateHandle(id, form);
		service.updateAllProperties(sampleApply);
		return new ApiResponse<>();
	}
}
