package com.zhihaoscm.service.core.service;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Claim;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.json.CustomerJsonInfo;
import com.zhihaoscm.domain.meta.biz.ClaimDef;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

@Component
public class ClaimServiceExt {

	/**
	 * 组装申领实体
	 *
	 * @param ship
	 * @param promoterName
	 * @param promoterMobile
	 * @param customerService
	 * @return
	 */
	public Claim assembleClaim(Ship ship, Customer customer,
			String promoterName, String promoterMobile,
			CustomerService customerService) {
		Claim claim = new Claim();
		claim.setCustomerInfo(this.getCustomerJsonInfo(customer));
		claim.setShipId(ship.getId());
		claim.setShipName(ship.getName());
		claim.setShipNameCn(ship.getCnname());
		claim.setPromoterName(promoterName);
		claim.setPromoterMobile(promoterMobile);
		claim.setState(ClaimDef.State.CLAIMING.getCode());

		if (StringUtils.isNotBlank(promoterName)) {
			customerService.selector(promoterName, null, null).stream()
					.filter(c -> StringUtils.isNotBlank(c.getMobile())
							&& StringUtils.isNotBlank(c.getRealName())
							&& c.getMobile().equals(promoterMobile)
							&& c.getRealName().equals(promoterName))
					.findFirst().ifPresent(c -> {
						claim.setPromoterId(c.getId());
						claim.setPromoter(this.getCustomerJsonInfo(c));
					});
		}

		return claim;
	}

	private @NotNull CustomerJsonInfo getCustomerJsonInfo(Customer c) {
		CustomerJsonInfo info = new CustomerJsonInfo();
		info.setId(c.getId());
		info.setCode(c.getCode());
		info.setMobile(c.getMobile());
		info.setRealName(c.getRealName());
		info.setInstitutionName(c.getInstitutionName());
		info.setCreatedTime(c.getCreatedTime());
		return info;
	}

}
