package com.zhihaoscm.service.resource.custom.sample.apply;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.entity.SampleApply;
import com.zhihaoscm.service.core.service.SampleApplyService;
import com.zhihaoscm.service.resource.form.sample.apply.SampleApplyForm;
import com.zhihaoscm.service.resource.validator.sample.apply.SampleApplyValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 申请寄样信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Tag(name = "申请寄样信息表", description = "申请寄样信息表API")
@RestController
@RequestMapping("/sample-apply")
public class SampleApplyResource {

	@Autowired
	private SampleApplyService service;

	@Autowired
	private SampleApplyValidator validator;

	@Operation(summary = "新增")
	@PostMapping
	public ApiResponse<SampleApply> create(
			@Validated @RequestBody SampleApplyForm form) {
		return new ApiResponse<>(
				service.create(validator.validateCreate(form)));
	}
}
