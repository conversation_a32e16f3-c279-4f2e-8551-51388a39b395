package com.zhihaoscm.service.core.service.impl;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;

import org.apache.commons.collections4.CollectionUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.aliyun.oss.sdk.protocol.AliyunFileService;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.util.utils.ExceptionUtil;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.date.DateFormat;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.FetchWaterWay;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.bean.entity.Information;
import com.zhihaoscm.domain.bean.entity.ReadQuantity;
import com.zhihaoscm.domain.bean.entity.TagBusiness;
import com.zhihaoscm.domain.bean.vo.InformationCountVo;
import com.zhihaoscm.domain.bean.vo.InformationVo;
import com.zhihaoscm.domain.meta.biz.InformationDef;
import com.zhihaoscm.domain.meta.biz.ReadQuantityDef;
import com.zhihaoscm.domain.meta.biz.TagBusinessDef;
import com.zhihaoscm.service.config.properties.CertificateProperties;
import com.zhihaoscm.service.config.properties.InfoImgProperties;
import com.zhihaoscm.service.core.mapper.InformationMapper;
import com.zhihaoscm.service.core.service.*;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 砂石资讯 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Slf4j
@Service
public class InformationServiceImpl
		extends MpLongIdBaseServiceImpl<Information, InformationMapper>
		implements InformationService {

	@Autowired
	private FileService fileService;
	@Autowired
	private ReadQuantityService readQuantityService;
	@Autowired
	private TagBusinessService tagBusinessService;
	@Autowired
	private TagService tagService;
	@Autowired
	private InfoImgProperties infoImgProperties;
	@Autowired
	private CertificateProperties certificateProperties;
	@Autowired
	private AliyunFileService aliyunFileService;

	public InformationServiceImpl(InformationMapper repository) {
		super(repository);
	}

	@Override
	public Page<InformationVo> paging(Integer page, Integer size, String title,
			LocalDate startTime, LocalDate endTime, String sortKey,
			String sortOrder, Integer classify, Integer feeType,
			Integer showLocation, Integer pcShowLocation, Integer state) {
		LambdaQueryWrapper<Information> wrapper = Wrappers
				.lambdaQuery(Information.class);
		wrapper.eq(Information::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.like(StringUtils.isNotBlank(title), Information::getTitle,
				title);
		wrapper.eq(Objects.nonNull(classify), Information::getClassify,
				classify);
		wrapper.eq(Objects.nonNull(feeType), Information::getFeeType, feeType);
		wrapper.eq(Objects.nonNull(showLocation), Information::getShowLocation,
				showLocation);
		wrapper.eq(Objects.nonNull(pcShowLocation),
				Information::getPcShowLocation, pcShowLocation);
		wrapper.eq(Objects.nonNull(state), Information::getState, state);
		wrapper.ge(Objects.nonNull(startTime), Information::getPublishDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), Information::getPublishDate,
				endTime);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 发布日期倒序，发布日期相同按更新时间倒序
			wrapper.orderByDesc(Information::getPublishDate)
					.orderByDesc(Information::getUpdatedTime);
		}
		Page<Information> pageResult = repository
				.selectPage(new Page<>(page, size), wrapper);
		List<InformationVo> informationVos = this
				.packVo(pageResult.getRecords());
		return PageUtil.getRecordsInfoPage(pageResult, informationVos);
	}

	@Override
	public Page<InformationVo> relatedPaging(Integer page, Integer size,
			String productTypeId) {
		List<String> informationIds = this.getInformationIds(productTypeId);
		if (CollectionUtils.isEmpty(informationIds)) {
			return new Page<>();
		}
		LambdaQueryWrapper<Information> wrapper = Wrappers
				.lambdaQuery(Information.class);
		wrapper.eq(Information::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Information::getState, CommonDef.Symbol.YES.getCode());
		wrapper.in(Information::getId, informationIds);
		wrapper.orderByDesc(Information::getPublishDate);
		Page<Information> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Page<InformationVo> customPaging(Integer page, Integer size,
			String keyword, String tenantId, Integer state,
			String publishDateStart, String publishDateFinish, Integer category,
			List<Integer> classify, Integer showLocation, String sortKey,
			String sortOrder) {
		// 判断用户来源
		Integer origin = CommonDef.UserType.INNER.getCode();
		if (StringUtils.isNotBlank(tenantId)) {
			origin = CommonDef.UserType.OUTER.getCode();
		}
		Page<Information> pageResult;
		if (CommonDef.UserType.INNER.match(origin)) {
			LambdaQueryWrapper<Information> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(Information::getDel, CommonDef.Symbol.NO.getCode())
					.eq(Objects.nonNull(state), Information::getState, state)
					.in(CollectionUtils.isNotEmpty(classify),
							Information::getClassify, classify)
					.like(StringUtils.isNotBlank(keyword),
							Information::getTitle, keyword)
					.between(StringUtils.isNotBlank(publishDateStart)
							&& StringUtils.isNotBlank(publishDateFinish),
							Information::getPublishDate, publishDateStart,
							publishDateFinish);
			queryWrapper.eq(Objects.nonNull(showLocation),
					Information::getShowLocation, showLocation);
			queryWrapper.and(wrapper -> wrapper.isNull(Information::getTenantId)
					.or().eq(Information::getTenantId, ""));
			if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
				queryWrapper.last("order by " + sortKey + " " + sortOrder);
			} else {
				// 默认按照更新时间降序排列
				queryWrapper.orderByDesc(Information::getPublishDate);
			}
			pageResult = repository.selectPage(new Page<>(page, size),
					queryWrapper);
		} else {
			// 分身版用户的资讯
			LambdaQueryWrapper<Information> wrapper = Wrappers
					.lambdaQuery(Information.class)
					.eq(Information::getTenantId, tenantId)
					.eq(Information::getDel, CommonDef.Symbol.NO.getCode())
					.eq(Objects.nonNull(state), Information::getState, state)
					.eq(Information::getCategory, category)
					.in(CollectionUtils.isNotEmpty(classify),
							Information::getClassify, classify)
					.like(StringUtils.isNotBlank(keyword),
							Information::getTitle, keyword)
					.between(StringUtils.isNotBlank(publishDateStart)
							&& StringUtils.isNotBlank(publishDateFinish),
							Information::getPublishDate, publishDateStart,
							publishDateFinish)
					.orderBy(Boolean.TRUE, Boolean.FALSE,
							Information::getPublishDate);
			pageResult = repository.selectPage(new Page<>(page, size), wrapper);
		}
		return PageUtil.getRecordsInfoPage(pageResult,
				this.packVo(pageResult.getRecords()));
	}

	@Override
	public List<InformationVo> detailRelated(Long informationId) {
		// 相关相关资讯展示与该资讯有一个及以上共同标签资讯。按发布日期倒序最多展示10条，不足10条展示真实数量，没有相同标签的不展示“相关资讯”标题
		List<String> informationIds = this
				.getRelatedInformationIds(informationId);
		if (CollectionUtils.isEmpty(informationIds)) {
			return List.of();
		}
		LambdaQueryWrapper<Information> wrapper = Wrappers
				.lambdaQuery(Information.class);
		wrapper.eq(Information::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Information::getState, CommonDef.Symbol.YES.getCode());
		wrapper.in(Information::getId, informationIds);
		wrapper.ne(Information::getId, informationId);
		wrapper.orderByDesc(Information::getPublishDate);
		wrapper.last("LIMIT 10");
		List<Information> informationList = repository.selectList(wrapper);
		return this.packVo(informationList);
	}

	@Override
	public List<InformationVo> findByShowLocation(List<Integer> showLocations,
			List<Integer> classify) {
		LambdaQueryWrapper<Information> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Information::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Information::getState, CommonDef.Symbol.YES.getCode());
		queryWrapper.in(CollectionUtils.isNotEmpty(classify),
				Information::getClassify, classify);
		queryWrapper.in(CollectionUtils.isNotEmpty(showLocations),
				Information::getShowLocation, showLocations);
		queryWrapper.isNotNull(Information::getShowLocation);
		List<Information> information = repository.selectList(queryWrapper);
		List<Information> list1 = information.stream()
				.collect(Collectors.groupingBy(Information::getShowLocation))
				.values().stream()
				.map(list -> list.stream()
						.max(Comparator.comparing(Information::getUpdatedTime)))
				.filter(Optional::isPresent).map(Optional::get).toList();
		return this.packVo(list1);
	}

	@Override
	public List<InformationVo> findByPcShowLocation() {
		List<Information> result = new ArrayList<>();

		// 精选资讯：资讯默认展示4条，默认资讯展示位置为该位置的更新时间倒序的
		LambdaQueryWrapper<Information> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Information::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Information::getState, CommonDef.Symbol.YES.getCode());
		queryWrapper.isNotNull(Information::getPcShowLocation)
				.orderByAsc(Information::getPcShowLocation)
				.orderByDesc(Information::getUpdatedTime);
		List<Information> informationList = repository.selectList(queryWrapper);
		if (CollectionUtils.isNotEmpty(informationList)) {
			result = informationList.stream()
					.collect(Collectors
							.groupingBy(Information::getPcShowLocation))
					.values().stream()
					.map(list -> list.stream().max(
							Comparator.comparing(Information::getUpdatedTime)))
					.filter(Optional::isPresent).map(Optional::get).toList();
		}
		// 少于4条，没数据的位置按更新时间依次填充（倒序）
		if (result.size() < 4) {
			result = this.paddingData(result);
		}
		return this.packVo(result);
	}

	@Override
	public List<InformationVo> findByClassify(Integer classify) {
		// 白露专区：按创建时间最新4条
		LambdaQueryWrapper<Information> queryWrapper = Wrappers
				.lambdaQuery(Information.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Information::getState, CommonDef.Symbol.YES.getCode());
		queryWrapper
				.eq(Objects.nonNull(classify), Information::getClassify,
						classify)
				.orderByDesc(Information::getCreatedTime).last("LIMIT 4");
		return this.packVo(repository.selectList(queryWrapper));
	}

	@Override
	public List<Information> selector(String title) {
		LambdaQueryWrapper<Information> wrapper = Wrappers
				.lambdaQuery(Information.class)
				.eq(Information::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Information::getState, CommonDef.Symbol.YES.getCode())
				.like(StringUtils.isNotBlank(title), Information::getTitle,
						title);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<InformationVo> detail(Long id, String tenantId) {
		InformationVo informationVo = new InformationVo();
		if (Objects.isNull(tenantId)) {
			Optional<Information> optional = super.findOne(id);
			if (optional.isEmpty()) {
				return Optional.empty();
			}
			informationVo.setInformation(optional.get());
		} else {
			LambdaQueryWrapper<Information> wrapper = Wrappers
					.lambdaQuery(Information.class).eq(Information::getId, id)
					.eq(Information::getTenantId, Long.valueOf(tenantId))
					.eq(Information::getDel, CommonDef.Symbol.NO.getCode());
			Information information = repository.selectOne(wrapper);
			if (Objects.isNull(information)) {
				return Optional.empty();
			}
			informationVo.setInformation(information);
		}

		// 设置阅读量
		List<ReadQuantity> readQuantities = readQuantityService
				.findByUniIdsAndType(Set.of(id),
						ReadQuantityDef.Type.SAND_INFO.getCode());
		if (!CollectionUtils.isEmpty(readQuantities)) {
			informationVo.setReadQuantity(readQuantities.get(0));
		} else {
			ReadQuantity readQuantity = new ReadQuantity();
			readQuantity.setQuantity(0L);
			informationVo.setReadQuantity(readQuantity);
		}
		if (informationVo.getInformation().getId() != null) {
			Information voInformation = informationVo.getInformation();
			List<TagBusiness> tagBusinessList = tagBusinessService
					.findByBusinessIdAndType(voInformation.getId().toString(),
							TagBusinessDef.Type.Information.getCode());
			if (CollectionUtils.isNotEmpty(tagBusinessList)) {
				List<Long> tagIds = tagBusinessList.stream()
						.map(TagBusiness::getTagId).distinct().toList();
				informationVo.setTags(tagService.findByIds(tagIds));
			}
		}
		return Optional.of(informationVo);

	}

	@Override
	public InformationCountVo statisticsInformationUnlisted(Boolean hasFull) {
		InformationCountVo informationCountVo = new InformationCountVo();
		informationCountVo.setUnlistedCount(0L);
		if (hasFull) {
			LambdaQueryWrapper<Information> queryWrapper = Wrappers
					.lambdaQuery(Information.class);
			// 统计 未上架
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(Information::getState, 0);
			Long unlistedCount = repository.selectCount(queryWrapper);
			informationCountVo.setUnlistedCount(unlistedCount);
			queryWrapper.clear();
		}
		return informationCountVo;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@FileId
	public Information create(Information information,
			@FileId List<Long> activeFileIds, List<Long> tagIds) {
		Information result = super.create(information);
		this.handelTag(result, tagIds);
		return information;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@FileId(type = 2)
	public void update(Information information,
			@FileId List<Long> activeFileIds,
			@FileId(type = 2) List<Long> unActiveFileIds, List<Long> tagIds) {
		Information result = super.updateAllProperties(information);
		// 先删除资讯的标签，再进行重新新增
		tagBusinessService.deleteByBusinessIdAndType(
				information.getId().toString(),
				TagBusinessDef.Type.Information.getCode());
		this.handelTag(result, tagIds);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@FileId(type = 3)
	public void delete(Long id, @FileId(type = 2) List<Long> unActiveFileIds) {
		super.delete(id);
		tagBusinessService.deleteByBusinessIdAndType(String.valueOf(id),
				TagBusinessDef.Type.Information.getCode());
	}

	@Override
	public void updateState(Long id, Integer state) {
		LambdaUpdateWrapper<Information> lambdaUpdateWrapper = Wrappers
				.lambdaUpdate(Information.class).eq(Information::getId, id)
				.set(Information::getState, state);
		repository.update(lambdaUpdateWrapper);
	}

	@Override
	public boolean validateDailyReviewExist() {
		LambdaQueryWrapper<Information> queryWrapper = Wrappers
				.lambdaQuery(Information.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Information::getPublishDate, LocalDate.now());
		queryWrapper.eq(Information::getClassify,
				InformationDef.Classify.DAILY_REVIEW.getCode());
		return repository.exists(queryWrapper);
	}

	@Override
	public void generateWaterLevelAndReport() {
		Optional<java.io.File> optionalFile = this.downloadCertFromOss();
		if (optionalFile.isEmpty()) {
			return;
		}

		// 使用下载的证书创建自定义 SSLSocketFactory
		Optional<SSLSocketFactory> optionalSSLSocketFactory = createCustomSslSocketFactory(
				optionalFile.get());
		if (optionalSSLSocketFactory.isEmpty()) {
			return;
		}
		List<Information> informations = new ArrayList<>();
		this.handleWaterLevel(informations, optionalSSLSocketFactory.get());
		this.handleWaterwayNotice(informations, optionalSSLSocketFactory.get());
		if (CollectionUtils.isNotEmpty(informations)) {
			log.info("已抓取到航道通告，数量：{}", informations.size());
			super.batchCreate(informations);
		}
		log.info("未抓取到航道通告");
	}

	private Optional<SSLSocketFactory> createCustomSslSocketFactory(
			java.io.File file) {
		// Step 1: 加载证书
		try {
			CertificateFactory cf = CertificateFactory.getInstance("X.509");
			Certificate cert;
			try (InputStream fis = new FileInputStream(file)) {
				cert = cf.generateCertificate(fis);
			}

			// Step 2: 创建 KeyStore 并导入证书
			KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
			// 初始化为空的 KeyStore
			keyStore.load(null, null);
			keyStore.setCertificateEntry("oss-cert", cert);

			// Step 3: 初始化 TrustManagerFactory
			TrustManagerFactory tmf = TrustManagerFactory
					.getInstance(TrustManagerFactory.getDefaultAlgorithm());
			tmf.init(keyStore);

			// Step 4: 创建 SSLContext
			SSLContext sslContext = SSLContext.getInstance("TLS");
			sslContext.init(null, tmf.getTrustManagers(),
					new java.security.SecureRandom());

			return Optional.of(sslContext.getSocketFactory());
		} catch (Exception e) {
			log.error("create SocketFactory error:{}",
					ExceptionUtil.buildErrorMessage(e));
		}
		return Optional.empty();
	}

	private Optional<java.io.File> downloadCertFromOss() {
		java.io.File file = new java.io.File("/usr/local/certificate/cert.pem");
		if (file.exists()) {
			return Optional.of(file);
		}
		Optional<InputStream> optionalInputStream = aliyunFileService
				.downloadPublicBucket(
						certificateProperties.getChangJiangWaterway());
		return optionalInputStream.map(inputStream -> {
			try (FileOutputStream outputStream = new FileOutputStream(file)) {
				// 将 InputStream 转换为 File
				inputStream.transferTo(outputStream);

				// 验证文件是否成功写入
				if (file.exists()) {
					log.info("File written successfully: {}",
							file.getAbsolutePath());
					return file;
				} else {
					log.info("Failed to write file.");
				}
			} catch (Exception e) {
				log.error("down cert error:{}",
						ExceptionUtil.buildErrorMessage(e));
			}
			return null;
		});
	}

	/**
	 * 处理水位资讯
	 */
	private void handleWaterLevel(List<Information> informations,
			SSLSocketFactory sslSocketFactory) {

		try {
			DateTimeFormatter timeFormatter = DateTimeFormatter
					.ofPattern(DateFormat.YYYY_MM_DD);
			String waterLevelListUrl = "https://www.cjhdj.com.cn/hdfw/sw/";

			Map<String, FetchWaterWay> urlTitleMap = this.fetchDetailUrl(
					timeFormatter, waterLevelListUrl,
					InformationDef.Classify.WATER_LEVEL.getCode(),
					sslSocketFactory);
			if (urlTitleMap.isEmpty()) {
				log.info("未抓取到水位信息");
				return;
			}
			for (Map.Entry<String, FetchWaterWay> entry : urlTitleMap
					.entrySet()) {
				String detailUrl = entry.getKey();
				// Step 3: 获取水位详情页信息
				Document detailPage = Jsoup.connect(detailUrl)
						.sslSocketFactory(sslSocketFactory).get();

				// Step 4: Locate the table containing water level data
				Element table = detailPage.selectFirst("div.xl_con1 table");
				if (table == null) {
					log.info("水位详情页数据不存在");
					return;
				}

				Information information = this.buildInformation(
						InformationDef.Classify.WATER_LEVEL.getCode(),
						infoImgProperties.getWaterLevel());
				FetchWaterWay fetchWaterWay = entry.getValue();
				information.setPublishDate(timeFormatter.parse(
						fetchWaterWay.getPublishDate(), LocalDate::from));
				information.setTitle(fetchWaterWay.getTitle());
				StringBuilder tableStr = new StringBuilder(
						"<table><thead><tr><th>站点</th><th>水位</th><th>涨落</th></tr></thead><tbody>");
				Elements rows = table.select("tr");
				// Step 4: 处理行数据
				for (int i = 1; i < rows.size(); i++) {
					// Skip header row
					Elements cells = rows.get(i).select("td");
					if (cells.size() == 3) {
						tableStr.append("<tr>");
						for (Element cell : cells) {
							tableStr.append("<td>").append(cell.text())
									.append("</td>");
						}
						tableStr.append("</tr>");
					}
				}
				tableStr.append("</tbody></table>");
				information.setContent(tableStr.toString());
				informations.add(information);
			}
		} catch (IOException e) {
			log.error("生成水位资讯失败，error：{}", ExceptionUtil.buildErrorMessage(e));
		}
	}

	/**
	 * 处理航道通告
	 *
	 * @param informations
	 */
	private void handleWaterwayNotice(List<Information> informations,
			SSLSocketFactory sslSocketFactory) {
		try {
			DateTimeFormatter timeFormatter = DateTimeFormatter
					.ofPattern(DateFormat.YYYY_MM_DD);
			String waterLevelListUrl = "https://www.cjhdj.com.cn/hdfw/hdtg/";

			Map<String, FetchWaterWay> urlTitleMap = this.fetchDetailUrl(
					timeFormatter, waterLevelListUrl,
					InformationDef.Classify.WATERWAY_NOTICE.getCode(),
					sslSocketFactory);
			if (urlTitleMap.isEmpty()) {
				log.info("未抓取到航道通告");
				return;
			}
			// https://www.cjhdj.com.cn/hdfw/hdtg/xy/202412/t20241226_426789.shtml
			for (Map.Entry<String, FetchWaterWay> entry : urlTitleMap
					.entrySet()) {
				String detailUrl = entry.getKey();
				// Step 3: 获取水位详情页信息
				Document detailPage = Jsoup.connect(detailUrl)
						.sslSocketFactory(sslSocketFactory).get();

				Element content = detailPage
						.selectFirst("div.cn > div:nth-of-type(2)");
				if (content == null) {
					log.info("未找到航道通告正文内容！");
					return;
				}

				// 修改 <p> 标签，移除 line-height 样式
				Elements pElements = content.select("p[style]");
				for (Element p : pElements) {
					String style = p.attr("style");
					// 移除 line-height 样式
					style = style.replaceAll("line-height:\\s*[^;]+;", "");
					p.attr("style", style);
				}

				// 修改 <span> 标签，将 font-size 替换为 16px
				Elements spanElements = content.select("span[style]");
				for (Element span : spanElements) {
					String style = span.attr("style");
					// 替换 font-size 样式
					style = style.replaceAll("font-size:\\s*[^;]+;",
							"font-size: 16px;");
					span.attr("style", style);
				}

				Information information = this.buildInformation(
						InformationDef.Classify.WATERWAY_NOTICE.getCode(),
						infoImgProperties.getWaterwayNotice());
				FetchWaterWay fetchWaterWay = entry.getValue();
				information.setPublishDate(timeFormatter.parse(
						fetchWaterWay.getPublishDate(), LocalDate::from));
				information.setTitle(fetchWaterWay.getTitle());
				information.setContent(content.html());
				informations.add(information);
			}
		} catch (IOException e) {
			log.error("生成航道通告资讯失败，error：{}",
					ExceptionUtil.buildErrorMessage(e));
		}
	}

	private Map<String, FetchWaterWay> fetchDetailUrl(
			DateTimeFormatter timeFormatter, String listUrl, Integer classify,
			SSLSocketFactory sslSocketFactory) throws IOException {
		// Step 1: 获取水位列表页
		Document listPage = Jsoup.connect(listUrl)
				.sslSocketFactory(sslSocketFactory).get();

		String today = timeFormatter.format(LocalDate.now());
		String yesterday = timeFormatter.format(LocalDate.now().minusDays(1));

		// 查找今天和昨天的水位数据用于去重
		Set<String> dateTitleSet = this
				.findByClassifyAndPublishDate(classify,
						List.of(today, yesterday))
				.stream().map(e -> timeFormatter.format(e.getPublishDate())
						+ "_" + e.getTitle())
				.collect(Collectors.toSet());

		// Step 2: 解析列表页关于今当天和昨天的数据
		Elements li = listPage.select("div.gl_list1 li");
		// Select all
		Map<String, FetchWaterWay> urlTitleMap = new HashMap<>();
		for (Element element : li) {
			String span = element.select("span").get(0).text();
			// 筛选日期为今天和昨天的数据
			if (span.equals(timeFormatter.format(LocalDate.now()))
					|| span.equals(timeFormatter
							.format(LocalDate.now().minusDays(1)))) {
				Element link = element.select("a[href]").get(0);
				// 去重处理，过滤已发布的水位信息
				if (!dateTitleSet.contains(span + "_" + link.text())) {
					FetchWaterWay fetchWaterWay = new FetchWaterWay();
					fetchWaterWay.setTitle(link.text());
					fetchWaterWay.setPublishDate(span);
					String href = link.attr("href");
					String detailUrl = listUrl + href.substring(2);
					// 保存详情页和标题的关联关系
					urlTitleMap.put(detailUrl, fetchWaterWay);
				}
			}
		}
		return urlTitleMap;
	}

	private Information buildInformation(Integer classify, Long imgId) {
		Information information = new Information();
		information.setType(InformationDef.Type.IMG.getCode());
		information.setClassify(classify);
		information.setCategory(InformationDef.Category.INFORMATION.getCode());
		information.setFeeType(InformationDef.FeeType.FREE_CONTENT.getCode());
		information.setSource(InformationDef.CJHDJ);
		information.setCoverFileId(imgId);
		information.setDisclaimers(InformationDef.DISCLAIMERS);
		information.setState(CommonDef.Symbol.YES.getCode());
		information.setOrigin(CommonDef.UserType.INNER.getCode());
		return information;
	}

	private List<Information> findByClassifyAndPublishDate(Integer classify,
			List<String> dates) {
		LambdaQueryWrapper<Information> queryWrapper = Wrappers
				.lambdaQuery(Information.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Information::getClassify, classify);
		queryWrapper.in(Information::getPublishDate, dates);
		return repository.selectList(queryWrapper);
	}

	/**
	 * 组装数据
	 *
	 * @param list
	 * @return
	 */
	private List<InformationVo> packVo(List<Information> list) {
		if (CollectionUtils.isEmpty(list)) {
			return List.of();
		}

		// 文件id集合
		Set<Long> fileIds = new HashSet<>();
		// 资讯id集合
		Set<Long> ids = new HashSet<>();
		list.forEach(information -> {
			fileIds.add(information.getCoverFileId());
			ids.add(information.getId());
		});

		Map<Long, File> fileMap = fileService
				.getIdMap(new ArrayList<>(fileIds));
		Map<Long, ReadQuantity> readQuantityMap = readQuantityService
				.findByUniIdsAndType(ids,
						ReadQuantityDef.Type.SAND_INFO.getCode())
				.stream().collect(Collectors.toMap(ReadQuantity::getUniId,
						Function.identity()));
		return list.stream().map(e -> {
			InformationVo vo = new InformationVo();
			vo.setInformation(e);

			// 设置封面
			vo.setCover(fileMap.get(e.getCoverFileId()));
			// 设置阅读量
			ReadQuantity readQuantity = readQuantityMap.get(e.getId());
			if (Objects.isNull(readQuantity)) {
				readQuantity = new ReadQuantity();
				readQuantity.setQuantity(0L);
			}
			vo.setReadQuantity(readQuantity);

			// 设置标签
			List<TagBusiness> tagBusinessList = tagBusinessService
					.findByBusinessIdAndType(e.getId().toString(),
							TagBusinessDef.Type.Information.getCode());
			List<Long> tagIdList = tagBusinessList.stream()
					.map(TagBusiness::getTagId).distinct().toList();
			if (CollectionUtils.isNotEmpty(tagIdList)) {
				vo.setTags(tagService.findByIds(tagIdList));
			}
			return vo;
		}).toList();
	}

	/**
	 * 获取资讯id集合
	 *
	 * @param productTypeId
	 * @return
	 */
	private List<String> getInformationIds(String productTypeId) {
		List<TagBusiness> tagBusinessList = tagBusinessService
				.findByBusinessIdAndType(productTypeId,
						TagBusinessDef.Type.PRODUCT_TYPE.getCode());
		List<Long> tagIds = tagBusinessList.stream().map(TagBusiness::getTagId)
				.distinct().toList();
		if (CollectionUtils.isEmpty(tagIds)) {
			return List.of();
		}
		List<TagBusiness> tagBusinesses = tagBusinessService
				.findByTagIdsAndType(tagIds,
						TagBusinessDef.Type.Information.getCode());
		return tagBusinesses.stream().map(TagBusiness::getBusinessId).distinct()
				.toList();
	}

	/**
	 * 根据资讯id，获取相关资讯
	 *
	 * @param informationId
	 * @return
	 */
	private List<String> getRelatedInformationIds(Long informationId) {
		List<TagBusiness> tagBusinessList = tagBusinessService
				.findByBusinessIdAndType(String.valueOf(informationId),
						TagBusinessDef.Type.Information.getCode());
		List<Long> tagIds = tagBusinessList.stream().map(TagBusiness::getTagId)
				.distinct().toList();
		if (CollectionUtils.isEmpty(tagIds)) {
			return List.of();
		}
		List<TagBusiness> tagBusinesses = tagBusinessService
				.findByTagIdsAndType(tagIds,
						TagBusinessDef.Type.Information.getCode());
		return tagBusinesses.stream().map(TagBusiness::getBusinessId).distinct()
				.toList();
	}

	/**
	 * 精选资讯:按PC端展示位置排少于4条，没数据的位置按更新时间依次填充（倒序）
	 *
	 * @param pcShowLocationList
	 * @return
	 */
	private List<Information> paddingData(
			List<Information> pcShowLocationList) {
		List<Information> result = new ArrayList<>();
		List<Long> informationIds = pcShowLocationList.stream()
				.map(Information::getId).toList();
		// 除此之外的资讯列表（更新时间倒序，最多4条）
		LambdaQueryWrapper<Information> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Information::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Information::getState, CommonDef.Symbol.YES.getCode());
		queryWrapper
				.notIn(CollectionUtils.isNotEmpty(informationIds),
						Information::getId, informationIds)
				.orderByDesc(Information::getUpdatedTime).last("LIMIT 4");
		List<Information> updatedTimeInformationList = repository
				.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(pcShowLocationList)) {
			result = updatedTimeInformationList;
		} else {
			Map<Integer, Information> pcShowLocationMap = pcShowLocationList
					.stream()
					.collect(Collectors.toMap(Information::getPcShowLocation,
							Function.identity()));
			// 拼接
			for (int i = 1; i <= 4; i++) {
				if (pcShowLocationMap.containsKey(i)) {
					result.add(pcShowLocationMap.get(i));
				} else {
					if (CollectionUtils
							.isNotEmpty(updatedTimeInformationList)) {
						result.add(updatedTimeInformationList.remove(0));
					}
				}
			}
		}
		return result;
	}

	/**
	 * 处理标签
	 *
	 * @param information
	 * @param tagIds
	 */
	private void handelTag(Information information, List<Long> tagIds) {
		if (CollectionUtils.isNotEmpty(tagIds)) {
			for (Long tagId : tagIds) {
				TagBusiness tagBusiness = new TagBusiness();
				tagBusiness.setBusinessId(information.getId().toString());
				tagBusiness.setTagId(tagId);
				tagBusiness.setType(TagBusinessDef.Type.Information.getCode());
				tagBusiness.setName(information.getTitle());
				tagBusinessService.create(tagBusiness);
			}
		}
	}

}
