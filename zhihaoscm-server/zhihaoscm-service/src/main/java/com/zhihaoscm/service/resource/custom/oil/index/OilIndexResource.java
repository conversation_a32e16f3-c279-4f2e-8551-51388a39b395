package com.zhihaoscm.service.resource.custom.oil.index;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.vo.OilIndexDetailVo;
import com.zhihaoscm.domain.bean.vo.OilIndexStatisticVo;
import com.zhihaoscm.domain.bean.vo.OilIndexVo;
import com.zhihaoscm.service.core.service.OilIndexService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 油品指数 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Tag(name = "油品指数", description = "油品指数API")
@RestController
@RequestMapping("/oil-index")
public class OilIndexResource {

	@Autowired
	private OilIndexService service;

	@Operation(summary = "油品指数")
	@GetMapping("/custom-paging")
	public ApiResponse<Page<OilIndexVo>> customPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "省编码") @RequestParam(required = false) String provinceCode,
			@Parameter(description = "品牌") @RequestParam(required = false) String brand) {
		return new ApiResponse<>(PageUtil.convert(
				service.customPaging(page, size, provinceCode, brand)));
	}

	@Operation(summary = "小程序端油品指数详情")
	@GetMapping("/detail")
	public ApiResponse<OilIndexDetailVo> detail(
			@Parameter(description = "省编码") @RequestParam String provinceCode,
			@Parameter(description = "市编码") @RequestParam String cityCode,
			@Parameter(description = "品牌") @RequestParam String brand,
			@Parameter(description = "当前经度") @RequestParam Double lon,
			@Parameter(description = "当前纬度") @RequestParam Double lat) {
		return new ApiResponse<>(service
				.detail(provinceCode, cityCode, brand, lon, lat).orElse(null));
	}

	@Operation(summary = "站点油品指数折线图")
	@GetMapping("/line")
	public ApiResponse<OilIndexStatisticVo> line(
			@Parameter(description = "站点编号") @RequestParam Long oilSiteId,
			@Parameter(description = "1 近一周 2近一月") @RequestParam(defaultValue = "1") Integer scope) {
		return new ApiResponse<>(service.line(oilSiteId, scope).orElse(null));
	}

	@Operation(summary = "区域油品指数折线图")
	@GetMapping("/area/line")
	public ApiResponse<OilIndexStatisticVo> areaLine(
			@Parameter(description = "省编码") @RequestParam String provinceCode,
			@Parameter(description = "市编码") @RequestParam String cityCode,
			@Parameter(description = "品牌") @RequestParam String brand,
			@Parameter(description = "1 近一周 2近一月") @RequestParam(defaultValue = "1") Integer scope) {
		return new ApiResponse<>(service
				.areaLine(provinceCode, cityCode, brand, scope).orElse(null));
	}
}
