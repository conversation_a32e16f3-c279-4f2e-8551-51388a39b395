package com.zhihaoscm.service.resource.validator.supplier;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Supplier;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.SupplierService;
import com.zhihaoscm.service.resource.form.supplier.SupplierForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @author: xiaYZ 2025/7/31
 * @version: 1.0
 */
@Component
public class SupplierValidator {

    @Autowired
    private SupplierService supplierService;

    public Supplier validateCreate(SupplierForm form) {
        Supplier supplier = new Supplier();
        BeanUtil.copyProperties(form, supplier);
        this.validateScCode(supplier.getSocialCreditCode(), null);
        return supplier;
    }


    public Supplier validateUpdate(Long id, SupplierForm form) {
        Supplier supplier = new Supplier();
        BeanUtil.copyProperties(form, supplier);
        this.validateScCode(supplier.getSocialCreditCode(), id);
        supplier.setId(id);
        return supplier;
    }


    /**
     * 验证社会信用代码是否存在
     *
     * @param id id
     * <AUTHOR>
     * create time: 2025/07/31@param socialCreditCode 社会信用代码@param socialCreditCode 社会信用代码
     **/
    public void validateScCode(String socialCreditCode, Long id) {
        LambdaQueryWrapper<Supplier> queryWrapper = Wrappers.lambdaQuery(Supplier.class)
                .eq(Supplier::getSocialCreditCode, socialCreditCode)
                .ne(Objects.nonNull(id), Supplier::getId, id)
                .eq(Supplier::getDel, CommonDef.Symbol.NO.getCode());
        boolean exists = supplierService.getRepository().exists(queryWrapper);
        if (exists) {
            throw new BadRequestException(ErrorCode.CODE_30198010);
        }
    }

}
