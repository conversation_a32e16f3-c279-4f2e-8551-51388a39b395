package com.zhihaoscm.service.resource.common;

import java.util.List;

import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.mybatis.plus.resource.MpStringIdBaseResource;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.vo.ShippingPlatAndTransportVo;
import com.zhihaoscm.service.core.service.ShippingRequirementPlatService;

/**
 * <p>
 * 平台船运需求 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@RestController
@RequestMapping("/shipping/plat")
public class ShippingRequirementPlatResource extends
		MpStringIdBaseResource<ShippingRequirementPlat, ShippingRequirementPlatService> {

	public ShippingRequirementPlatResource(
			ShippingRequirementPlatService service) {
		super(service);
	}

	@GetMapping(value = "/find/state")
	public List<ShippingRequirementPlat> findByState(
			@RequestParam(value = "state") Integer state) {
		return service.findByState(state);
	}

	@PostMapping(value = "/create/plat")
	String createPlat(
			@RequestBody ShippingRequirementPlat shippingRequirementPlat) {
		return service.createPlat(shippingRequirementPlat);
	}

	@PostMapping(value = "/complete")
	void complete(@RequestParam String id) {
		service.complete(id);
	}

	@PostMapping(value = "/handle/end")
	void handleEndThirdParty(@RequestBody ShippingRequirementPlat plat) {
		service.handleEndThirdParty(plat);
	}

	@GetMapping(value = "/plat-transport")
	public ShippingPlatAndTransportVo findShippingPlatAndTransportVoById(
			@RequestParam String platId) {
		return service.findShippingPlatAndTransportVoById(platId).orElse(null);
	}
}
