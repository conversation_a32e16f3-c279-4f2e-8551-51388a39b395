package com.zhihaoscm.service.resource.custom.ship.info.service.fee;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.dto.ShipInfoServiceFeePayDto;
import com.zhihaoscm.domain.bean.entity.ShipInfoServiceFee;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.meta.biz.HistoryDef;
import com.zhihaoscm.domain.meta.biz.ShipInfoServiceFeeDef;
import com.zhihaoscm.service.aop.history.HistoryContext;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.ShipInfoServiceFeeService;
import com.zhihaoscm.service.resource.form.ship.info.service.fee.ShipInfoServiceFeePayForm;
import com.zhihaoscm.service.resource.validator.ship.info.service.fee.ShipInfoServiceFeeValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "船务信息服务费", description = "船务信息服务费API")
@RestController
@RequestMapping("/ship-info-service-fee")
public class ShipInfoServiceFeeResource {

	@Autowired
	private ShipInfoServiceFeeService service;

	@Autowired
	private ShipInfoServiceFeeValidator validator;

	@Operation(summary = "分页查询船务信息服务费")
	@GetMapping("/custom-paging")
	public ApiResponse<Page<ShipInfoServiceFee>> customPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "编号或者关联船运单号") @RequestParam(required = false) String idOrCode,
			@Parameter(description = "状态") @RequestParam(required = false) Integer state,
			@Parameter(description = "类型 1货主 2船主") @RequestParam Integer type,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder) {
		return new ApiResponse<>(PageUtil.convert(service.customPaging(page,
				size, idOrCode, state, type, CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount().getId(),
				sortKey, sortOrder)));
	}

	@Operation(summary = "查询船务信息服务费详情")
	@GetMapping("/{id}")
	public ApiResponse<ShipInfoServiceFee> findById(@PathVariable String id) {
		return new ApiResponse<>(validator.validateExist(id));
	}

	@Operation(summary = "线下支付船务信息服务费 1货主 2船主")
	@PostMapping("/pay")
	@History(success = HistoryDef.PAY_SHIPPING_INFORMATION_SERVICE_FEE, bizNo = "{{#bizNo}}", module = HistoryDef.SHIPPING_DOCUMENT_OPERATOR,origin = HistoryDef.CUSTOM)
	public ApiResponse<Void> pay(
			@Validated @RequestBody ShipInfoServiceFeePayForm form) {
		TransportOrderShip transportOrderShip = validator.validatePay(form);
		// 货主船运单被三方需求中盛关联了的船运单货主端（小程序、app、pc）暂时隐藏操作按钮，只能在后台或供应链端操作
		validator.validateOwnerOperate(form);
		if (ShipInfoServiceFeeDef.Type.OWNER.match(form.getType())) {
			HistoryContext.putVariable("bizNo",form.getRelationCode());
		} else {
			HistoryContext.putVariable("bizNo","");
		}
		ShipInfoServiceFeePayDto dto = form.convertToDto(transportOrderShip);
		service.pay(dto.getTransportOrderShip(), dto.getType(),
				dto.getPayFileId(),
				ShipInfoServiceFeeDef.PayType.OFFLINE.getCode());
		return new ApiResponse<>();
	}

}
