package com.zhihaoscm.service.resource.admin.supplier;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.Supplier;
import com.zhihaoscm.service.core.service.SupplierService;
import com.zhihaoscm.service.resource.form.supplier.SupplierForm;
import com.zhihaoscm.service.resource.validator.supplier.SupplierValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * @author: xiaYZ 2025/7/31
 * @version: 1.0
 */
@Tag(name = "供应商管理")
@RestController
@RequestMapping("supplier-admin")
public class SupplierAdminResource {

	@Autowired
	private SupplierService supplierService;

    @Autowired
	private SupplierValidator supplierValidator;

	@GetMapping("paging")
	@Operation(summary = "分页查询供应商管理数据")
	public ApiResponse<Page<Supplier>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "供应商名称") @RequestParam(required = false) String supplierName,
			@Parameter(description = "关联集团公司id") @RequestParam(required = false) String groupCompanyId,
			@Parameter(description = "类型  1 矿山，2 砂场，3矿山加工厂，4贸易商，5其他") @RequestParam(required = false) Integer type) {
		return new ApiResponse<>(PageUtil.convert(supplierService.paging(page,
				size, supplierName, groupCompanyId, type)));
	}

	@PostMapping("create")
	@Operation(summary = "创建供应商")
	public ApiResponse<Supplier> create(
			@Validated @RequestBody SupplierForm form) {
		Supplier supplier = supplierValidator.validateCreate(form);
		return new ApiResponse<>(supplierService.create(supplier));
	}

	@PutMapping("update/{id}")
	@Operation(summary = "修改供应商")
	public ApiResponse<Supplier> update(
			@Validated @RequestBody SupplierForm form, @PathVariable Long id) {
		Supplier supplier = supplierValidator.validateUpdate(id, form);
		return new ApiResponse<>(supplierService.update(supplier));
	}

	@DeleteMapping("delete/{id}")
	@Operation(summary = "删除供应商")
	public ApiResponse<Void> delete(@PathVariable Long id) {
		supplierService.delete(id);
		return new ApiResponse<>();
	}

	@GetMapping("find/{id}")
	public ApiResponse<Supplier> findById(@PathVariable Long id) {
		return new ApiResponse<>(supplierService.findOne(id).orElse(null));
	}

}
