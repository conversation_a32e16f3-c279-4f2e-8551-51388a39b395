package com.zhihaoscm.service.resource.custom.shipping.platform;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.vo.ShippingRequirementCustomerVo;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.ShippingRequirementPlatService;
import com.zhihaoscm.service.resource.form.shipping.customer.CustomShippingRequirementCustomerForm;
import com.zhihaoscm.service.resource.validator.shipping.plat.ShippingRequirementPlatValidator;
import com.zhihaoscm.service.resource.validator.transport.order.ship.TransportOrderShipValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "货主平台船运需求", description = "货主平台船运需求API")
@RestController
@RequestMapping(value = "/shipping/plat/owner")
public class ShippingRequirementPlatOwnerResource {

	@Autowired
	private ShippingRequirementPlatService platService;

	@Autowired
	private ShippingRequirementPlatValidator platValidator;

	@Autowired
	private TransportOrderShipValidator transportOrderShipValidator;

	@Autowired
	private StringRedisClient redisClient;

	@Operation(summary = "货主运单查询列表")
	@GetMapping(value = "/custom/orderPaging")
	public ApiResponse<Page<ShippingRequirementCustomerVo>> orderPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_MINI_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "船舶名称、MMSI、编号") @RequestParam(required = false, name = "keyword") String keyword,
			@Parameter(description = "运单状态") @RequestParam(required = false, name = "states") List<Integer> states,
			@Parameter(description = "编号") @RequestParam(required = false, name = "title") String title,
			@Parameter(description = "船舶关键词") @RequestParam(required = false, name = "shipKey") String shipKey,
			@Parameter(description = "始发港") @RequestParam(required = false) String sourcePortName,
			@Parameter(description = "目的港") @RequestParam(required = false) String destinationPortName) {
		return new ApiResponse<>(platService.orderPaging(page, size, keyword,
				states, title, shipKey, sourcePortName, destinationPortName));
	}

	@Operation(summary = "货主运单详情")
	@GetMapping(value = "/custom/orderDetail")
	public ApiResponse<ShippingRequirementCustomerVo> orderDetail(
			@Parameter(description = "船运需求id") @RequestParam(required = false) String requireId,
			@Parameter(description = "船运单id") @RequestParam(required = false) String orderId) {
		if (StringUtils.isNotBlank(orderId)) {
			transportOrderShipValidator.validateDetail(orderId);
		}
		return new ApiResponse<>(
				platService.orderDetail(requireId, orderId).orElse(null));
	}

	@Operation(summary = "查询船运需求详情")
	@GetMapping(value = "/find/vo/{id}")
	public ApiResponse<ShippingRequirementCustomerVo> findVoById(
			@PathVariable(value = "id") String id) {
		return new ApiResponse<>(platService.findOwnerVoById(id).orElse(null));
	}

	@Operation(summary = "货主获取上次一条找船数据")
	@GetMapping("/last")
	public ApiResponse<ShippingRequirementPlat> findLast() {
		return new ApiResponse<>(platService.findLast(CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId()).orElse(null));
	}

	@Operation(summary = "货主新增船运需求")
	@PostMapping
	public ApiResponse<ShippingRequirementPlat> create(
			@Validated @RequestBody CustomShippingRequirementCustomerForm form) {
		platValidator.validateOwnerCreate(form);
		return new ApiResponse<>(platService
				.ownerCreate(form.convertToEntity(redisClient)).orElse(null));
	}

	@Operation(summary = "货主取消船运需求")
	@GetMapping(value = "/cancel/{id}")
	public ApiResponse<Void> ownerCancel(
			@PathVariable(value = "id") String id) {
		// 找船中取消操作，处理中可以取消找船
		platValidator.validateOwnerCancel(id);
		// 取消后，船运需求发布默认已结束
		platService.complete(id);
		return new ApiResponse<>();
	}

}
