package com.zhihaoscm.service.resource.admin.advertisement.position;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.domain.bean.entity.AdvertisementPosition;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.AdvertisementPositionDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.service.core.service.AdvertisementPositionService;
import com.zhihaoscm.service.resource.form.advertisement.position.AdvertisementPositionForm;
import com.zhihaoscm.service.resource.validator.advertisement.position.AdvertisementPositionValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "广告位管理", description = "广告位管理API")
@RequestMapping("/advertisement-position")
@RestController
public class AdvertisementPositionResource {

	@Autowired
	private AdvertisementPositionService service;
	@Autowired
	private AdvertisementPositionValidator validator;

	@Operation(summary = "全部广告位")
	@Secured(value = AdminPermissionDef.AD_R)
	@GetMapping("/all")
	public ApiResponse<List<AdvertisementPosition>> findAll() {
		return new ApiResponse<>(service.findAll());
	}

	@Operation(summary = "根据广告位和广告位号查询查询")
	@Secured(value = AdminPermissionDef.AD_R)
	@GetMapping("/position")
	public ApiResponse<AdvertisementPosition> findByPosition(
			@RequestParam(value = "positionType") Integer positionType,
			@RequestParam(value = "position") Integer position) {
		return new ApiResponse<>(
				service.findByPosition(positionType, position).orElse(null));
	}

	@Operation(summary = "根据id查询")
	@Secured(value = AdminPermissionDef.AD_R)
	@GetMapping("/{id}")
	public ApiResponse<AdvertisementPosition> findById(@PathVariable Long id) {
		return new ApiResponse<>(validator.validateExist(id));
	}

	@Operation(summary = "修改")
	@Secured(value = AdminPermissionDef.AD_W)
	@PutMapping("/{id}")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.PC_AD_MODIFY, type = LogDef.PLATFORM_MANAGEMENT_ADVERTISING_MANAGEMENT_PC, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#value}}") })
	public ApiResponse<AdvertisementPosition> update(
			@PathVariable("id") Long id,
			@Validated @RequestBody AdvertisementPositionForm form) {
		AdvertisementPosition advertisementPosition = validator
				.validateUpdate(id, form);
		AdvertisementPosition update = service
				.update(form.convertToEntity(advertisementPosition));
		AdvertisementPositionDef.PositionType from = AdvertisementPositionDef.PositionType
				.from(update.getPositionType());
		LogRecordContext.putVariable("value", from.getName());
		return new ApiResponse<>(update);
	}

	@Operation(summary = "删除")
	@Secured(value = AdminPermissionDef.AD_W)
	@DeleteMapping("/{id}")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.DELETE, success = LogDef.PC_AD_RESET, type = LogDef.PLATFORM_MANAGEMENT_ADVERTISING_MANAGEMENT_PC, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#value}}") })
	public ApiResponse<Void> delete(@PathVariable Long id) {
		AdvertisementPosition position = validator.validateExist(id);
		AdvertisementPositionDef.PositionType from = AdvertisementPositionDef.PositionType
				.from(position.getPositionType());
		LogRecordContext.putVariable("value", from.getName());
		service.delete(id);
		return new ApiResponse<>();
	}

}
