package com.zhihaoscm.service.resource.form.oil.fee;

import java.math.BigDecimal;

import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.domain.bean.entity.OilFee;
import com.zhihaoscm.domain.meta.biz.OilFeeDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(name = "OilFeePayForm", title = "油品费用支付表单")
public class OilFeePayForm {

	@Schema(title = "支付凭证-最多5张")
	@NotEmpty(message = ErrorCode.CODE_30155008)
	@Size(max = 5, message = ErrorCode.CODE_30155009)
	private ArrayLong payFileIds;

	@Schema(title = "油品订单id")
	@NotNull(message = ErrorCode.CODE_30155007)
	private String oilOrderId;

	@Schema(title = "油费金额")
	@NotNull(message = ErrorCode.CODE_30155006)
	private BigDecimal amount;

	public OilFee convertToEntity() {
		OilFee oilFee = new OilFee();
		oilFee.setAmount(this.getAmount());
		oilFee.setPayFileIds(this.getPayFileIds());
		oilFee.setOilOrderId(this.getOilOrderId());
		oilFee.setPayType(OilFeeDef.PayType.OFFLINE.getCode());
		return oilFee;
	}
}
