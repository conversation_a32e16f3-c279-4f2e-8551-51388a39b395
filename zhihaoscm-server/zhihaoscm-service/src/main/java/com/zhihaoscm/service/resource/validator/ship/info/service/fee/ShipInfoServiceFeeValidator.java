package com.zhihaoscm.service.resource.validator.ship.info.service.fee;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.domain.bean.entity.ShipInfoServiceFee;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.meta.biz.ShipInfoServiceFeeDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderShipDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.ShipInfoServiceFeeService;
import com.zhihaoscm.service.resource.form.ship.info.service.fee.ShipInfoServiceFeePayForm;
import com.zhihaoscm.service.resource.validator.transport.order.ship.TransportOrderShipValidator;

/**
 * 船务信息服务费校验器
 */
@Component
public class ShipInfoServiceFeeValidator {

	@Autowired
	private TransportOrderShipValidator transportOrderShipValidator;

	@Autowired
	private ShipInfoServiceFeeService shipInfoServiceFeeService;

	/**
	 * 校验支付
	 *
	 * @param form
	 * @return
	 */
	public TransportOrderShip validatePay(ShipInfoServiceFeePayForm form) {
		TransportOrderShip transportOrderShip = transportOrderShipValidator
				.validateExist(form.getRelationCode());

		// 不是管理后台的操作 需要校验当前操作人是否是货主
		if (Objects.isNull(UserContextHolder.getUser())) {
			switch (ShipInfoServiceFeeDef.Type.from(form.getType())) {
				case OWNER -> {
					if (!CustomerContextHolder.getCustomerLoginVo()
							.getProxyAccount().getId()
							.equals(transportOrderShip.getOwnerId())) {
						throw new BadRequestException(ErrorCode.CODE_403);
					}
				}
				case CAPTAIN -> {
					if (!CustomerContextHolder.getCustomerLoginVo()
							.getProxyAccount().getId()
							.equals(transportOrderShip.getCaptainId())) {
						throw new BadRequestException(ErrorCode.CODE_403);
					}
				}
			}

		} else {
			transportOrderShipValidator.validateHandler(form.getRelationCode());
		}

		if (!TransportOrderShipDef.State.SHIP_INFO_FEE_TO_BE_PAID
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30122006);
		}
		switch (ShipInfoServiceFeeDef.Type.from(form.getType())) {
			case OWNER -> {
				// 校验是不是代操作
				if (Objects.isNull(UserContextHolder.getUser())) {
					if (!CustomerContextHolder.getCustomerLoginVo()
							.getProxyAccount().getId()
							.equals(transportOrderShip.getOwnerId())) {
						throw new BadRequestException(ErrorCode.CODE_403);
					}
				}

				if (!ShipInfoServiceFeeDef.State.UNPAID.match(
						transportOrderShip.getOwnerShipInfoServiceFeeState())) {
					throw new BadRequestException(ErrorCode.CODE_30122005);
				}
			}
			case CAPTAIN -> {
				if (Objects.isNull(UserContextHolder.getUser())) {
					if (!CustomerContextHolder.getCustomerLoginVo()
							.getProxyAccount().getId()
							.equals(transportOrderShip.getCaptainId())) {
						throw new BadRequestException(ErrorCode.CODE_403);
					}
				}

				if (!ShipInfoServiceFeeDef.State.UNPAID.match(transportOrderShip
						.getCaptainShipInfoServiceFeeState())) {
					throw new BadRequestException(ErrorCode.CODE_30122005);
				}
			}
		}
		return transportOrderShip;
	}

	/**
	 * 校验数据是否存在
	 *
	 * @param id
	 * @return
	 */
	public ShipInfoServiceFee validateExist(String id) {
		ShipInfoServiceFee serviceFee = shipInfoServiceFeeService.findOne(id)
				.orElse(null);
		if (Objects.isNull(serviceFee)) {
			throw new BadRequestException(ErrorCode.CODE_30122008);
		}
		return serviceFee;
	}

	/**
	 * 校验确认
	 *
	 * @param id
	 * @return
	 */
	public ShipInfoServiceFee validateConfirm(String id) {
		ShipInfoServiceFee serviceFee = this.validateExist(id);
		if (!ShipInfoServiceFeeDef.State.UN_CONFIRM
				.match(serviceFee.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30122009);
		}
		if (!ShipInfoServiceFeeDef.PayType.OFFLINE
				.match(serviceFee.getPayType())) {
			throw new BadRequestException(ErrorCode.CODE_30122010);
		}
		return serviceFee;
	}

	/**
	 * 货主船运单被三方需求中盛关联了的船运单货主端（小程序、app、pc）暂时隐藏操作按钮，只能在后台或供应链端操作
	 * 
	 * @param form
	 */
	public void validateOwnerOperate(ShipInfoServiceFeePayForm form) {
		// 如果被三方关联了，如果是货主，就不能操作，需要提示
		if (ShipInfoServiceFeeDef.Type.OWNER.match(form.getType())) {
			transportOrderShipValidator
					.validateOwnerOperate(form.getRelationCode());
		}
	}
}
