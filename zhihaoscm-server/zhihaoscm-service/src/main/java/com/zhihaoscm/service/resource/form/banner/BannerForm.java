package com.zhihaoscm.service.resource.form.banner;

import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.domain.bean.entity.Banner;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "BannerForm", title = "轮播图表单对象")
public class BannerForm {

	@Schema(title = "位置 1 链云砂石 4 链云船务")
	@NotNull(message = ErrorCode.CODE_30032001)
	@Range(min = 1, max = 4, message = ErrorCode.CODE_30032002)
	private Integer pos;

	@Schema(title = "图片文件id")
	@NotNull(message = ErrorCode.CODE_30032003)
	private Long fileId;

	@Schema(title = "排序")
	@NotNull(message = ErrorCode.CODE_30032004)
	private Integer sort;

	@Schema(title = "跳转类型 1 商品 2 沙价 3 运价 4 链接 5资讯 6功能页")
	@NotNull(message = ErrorCode.CODE_30032005)
	@Range(min = 1, max = 6, message = ErrorCode.CODE_30032006)
	private Integer type;

	@Schema(title = "内容")
	@NotNull(message = ErrorCode.CODE_30032007)
	private String value;

	public Banner convertToEntity() {
		Banner banner = new Banner();
		this.setData(banner);
		return banner;
	}

	public Banner convertToEntity(Banner banner) {
		this.setData(banner);
		return banner;
	}

	@Hidden
	private void setData(Banner banner) {
		banner.setPos(this.getPos());
		banner.setFileId(this.getFileId());
		banner.setSort(this.getSort());
		banner.setType(this.getType());
		banner.setValue(this.getValue());
	}
}
