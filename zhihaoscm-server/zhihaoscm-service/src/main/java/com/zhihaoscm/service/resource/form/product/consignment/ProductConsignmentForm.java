package com.zhihaoscm.service.resource.form.product.consignment;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.common.bean.json.GeoPoint;
import com.zhihaoscm.domain.bean.dto.ProductConsignmentDto;
import com.zhihaoscm.domain.bean.entity.ProductConsignment;
import com.zhihaoscm.domain.bean.json.ArrayBankAccountInfo;
import com.zhihaoscm.domain.bean.json.ArrayParameterInfo;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

@Data
@Schema(name = "ProductConsignmentForm", description = "寄售商品表单")
public class ProductConsignmentForm {

	@Schema(title = "商品标题")
	@Length(min = 1, max = 30, message = ErrorCode.CODE_30196003)
	@NotBlank(message = ErrorCode.CODE_30196002)
	private String title;

	@Schema(title = "权重")
	@Range(min = 0, max = 10000, message = ErrorCode.CODE_30196004)
	private Integer weight;

	@Schema(title = "销售单价")
	@NotNull(message = ErrorCode.CODE_30196005)
	@Digits(integer = 5, fraction = 2, message = ErrorCode.CODE_30196006)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30196006)
	@DecimalMax(value = "10000.00", message = ErrorCode.CODE_30196006)
	private BigDecimal unitPrice;

	@Schema(title = "是否含税：1是 0否")
	@NotNull(message = ErrorCode.CODE_30196007)
	@Range(min = 0, max = 1, message = ErrorCode.CODE_30196008)
	private Integer isIncludeTax;

	@Schema(title = "税率")
	@Range(min = 1, max = 4, message = ErrorCode.CODE_30196010)
	private Integer taxRate;

	@Schema(title = "主图文件id")
	@NotNull(message = ErrorCode.CODE_30196011)
	private Long imgMainId;

	@Schema(title = "其他图片文件id")
	// @Size(max = 5, message = ErrorCode.CODE_30094005)
	private ArrayLong imgIds;

	@Schema(title = "视频id")
	@NotNull(message = ErrorCode.CODE_30196012)
	private Long videoId;

	@Schema(title = "供货商id")
	@NotNull(message = ErrorCode.CODE_30196013)
	private Long supplierId;

	@Schema(title = "品类id")
	@NotBlank(message = ErrorCode.CODE_30196014)
	private String productTypeId;

	@Schema(title = "规格")
	@NotBlank(message = ErrorCode.CODE_30196015)
	@Length(min = 1, max = 10, message = ErrorCode.CODE_30196016)
	private String productTypeSpec;

	@Schema(title = "参数信息")
	@Size(max = 10, message = ErrorCode.CODE_30196017)
	private ArrayParameterInfo parameterInfo;

	@Schema(title = "商品标签")
	@Size(max = 5, message = ErrorCode.CODE_30196021)
	private ArrayString productLabel;

	@Schema(title = "商品详情介绍")
	@NotBlank(message = ErrorCode.CODE_30196023)
	private String introduce;

	@Schema(title = "提货开始日期")
	@FutureOrPresent(message = ErrorCode.CODE_30196025)
	@NotNull(message = ErrorCode.CODE_30196024)
	private LocalDateTime deliveryStartTime;

	@Schema(title = "提货周期")
	@NotNull(message = ErrorCode.CODE_30196026)
	@Range(min = 1, max = 1000, message = ErrorCode.CODE_30196027)
	private Integer deliveryCycle;

	@Schema(title = "省编码")
	@NotBlank(message = ErrorCode.CODE_30196028)
	private String provinceCode;

	@Schema(title = "城市编码")
	@NotBlank(message = ErrorCode.CODE_30196029)
	private String cityCode;

	@Schema(title = "区域编码")
	@NotBlank(message = ErrorCode.CODE_30196030)
	private String regionCode;

	@Schema(title = "详细地址")
	@NotBlank(message = ErrorCode.CODE_30196031)
	private String address;

	@Schema(title = "经纬度")
	@NotNull(message = ErrorCode.CODE_30196032)
	private GeoPoint geo;

	@Schema(title = "库存量（吨）")
	@NotNull(message = ErrorCode.CODE_30196033)
	@Range(min = 1, max = 10000000, message = ErrorCode.CODE_30196034)
	private Integer inventoryQuantity;

	@Schema(title = "起订吨数（吨）")
	@NotNull(message = ErrorCode.CODE_30196035)
	@Range(min = 1, message = ErrorCode.CODE_30196036)
	private Integer minOrderTon;

	@Schema(title = "最小调整吨数（吨）")
	@NotNull(message = ErrorCode.CODE_30196038)
	private Integer minAdjustmentTon;

	@Schema(title = "提货说明")
	@Length(max = 300, message = ErrorCode.CODE_30196040)
	private String deliveryDescn;

	@Schema(title = "结算方式：1全部货款 2支付定金")
	@NotNull(message = ErrorCode.CODE_30196041)
	@Range(min = 1, max = 2, message = ErrorCode.CODE_30196042)
	private Integer settlementMethod;

	@Schema(title = "支付定金方式：1货款比例 2固定金额")
	@Range(min = 1, max = 2, message = ErrorCode.CODE_30196044)
	private Integer depositPaymentMethod;

	@Schema(title = "货款比例")
	private BigDecimal paymentRatio;

	@Schema(title = "固定金额")
	private BigDecimal fixedAmount;

	@Schema(title = "结算说明")
	@Length(max = 300, message = ErrorCode.CODE_30196051)
	private String settlementDescn;

	@Schema(title = "服务保障")
	@Length(max = 300, message = ErrorCode.CODE_30196052)
	private String serviceGuarantee;

	@Schema(title = "规则")
	@NotBlank(message = ErrorCode.CODE_30196053)
	@Length(min = 1, max = 1000, message = ErrorCode.CODE_30196054)
	private String regulation;

	@Schema(title = "商品专员id")
	@NotNull(message = ErrorCode.CODE_30196055)
	private Long handlerId;

	@Schema(title = "商品专员联系电话")
	@NotNull(message = ErrorCode.CODE_30196056)
	@Size(max = 3, message = ErrorCode.CODE_30196057)
	private ArrayString handlerMobile;

	@Schema(title = "收款账号")
	@Size(max = 5, message = ErrorCode.CODE_30196058)
	private ArrayBankAccountInfo receivingAccount;

	@Schema(title = "要激活的文件id")
	private List<Long> activeFileIds;

	@Schema(title = "要取消激活的文件id")
	private List<Long> unActiveFileIds;

	public ProductConsignmentDto convertToDto() {
		ProductConsignmentDto dto = new ProductConsignmentDto();
		ProductConsignment product = new ProductConsignment();
		// 新增的时候才能选择，只能通过指派修改
		product.setHandlerId(this.getHandlerId());
		this.updateData(product);
		dto.setProductConsignment(product);
		dto.setActiveFileIds(this.getActiveFileIds());
		dto.setUnActiveFileIds(this.getUnActiveFileIds());
		return dto;
	}

	public ProductConsignmentDto convertToDto(ProductConsignment product) {
		ProductConsignmentDto dto = new ProductConsignmentDto();
		this.updateData(product);
		dto.setProductConsignment(product);
		dto.setActiveFileIds(this.getActiveFileIds());
		dto.setUnActiveFileIds(this.getUnActiveFileIds());
		return dto;
	}

	private void updateData(ProductConsignment product) {
		product.setTitle(this.getTitle());
		product.setWeight(this.getWeight());
		product.setUnitPrice(this.getUnitPrice());
		product.setIsIncludeTax(this.getIsIncludeTax());
		product.setTaxRate(this.getTaxRate());
		product.setImgMainId(this.getImgMainId());
		product.setImgIds(this.getImgIds());
		product.setVideoId(this.getVideoId());
		product.setSupplierId(this.getSupplierId());
		product.setProductTypeId(this.getProductTypeId());
		product.setProductTypeSpec(this.getProductTypeSpec());
		product.setParameterInfo(this.getParameterInfo());
		product.setProductLabel(this.getProductLabel());
		product.setIntroduce(this.getIntroduce());
		product.setDeliveryStartTime(this.getDeliveryStartTime());
		product.setDeliveryCycle(this.getDeliveryCycle());
		product.setProvinceCode(this.getProvinceCode());
		product.setCityCode(this.getCityCode());
		product.setRegionCode(this.getRegionCode());
		product.setAddress(this.getAddress());
		product.setGeo(this.getGeo());
		product.setInventoryQuantity(this.getInventoryQuantity());
		product.setMinOrderTon(this.getMinOrderTon());
		product.setMinAdjustmentTon(this.getMinAdjustmentTon());
		product.setDeliveryDescn(this.getDeliveryDescn());
		product.setSettlementMethod(this.getSettlementMethod());
		product.setDepositPaymentMethod(this.getDepositPaymentMethod());
		product.setPaymentRatio(this.getPaymentRatio());
		product.setFixedAmount(this.getFixedAmount());
		product.setSettlementDescn(this.getSettlementDescn());
		product.setServiceGuarantee(this.getServiceGuarantee());
		product.setRegulation(this.getRegulation());
		product.setHandlerMobile(this.getHandlerMobile());
		product.setReceivingAccount(this.getReceivingAccount());
	}

}
