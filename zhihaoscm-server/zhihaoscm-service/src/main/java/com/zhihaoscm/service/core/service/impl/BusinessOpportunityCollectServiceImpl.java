package com.zhihaoscm.service.core.service.impl;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.domain.bean.entity.BusinessOpportunityCollect;
import com.zhihaoscm.service.core.mapper.BusinessOpportunityCollectMapper;
import com.zhihaoscm.service.core.service.BusinessOpportunityCollectService;

/**
 * <p>
 * 收藏商机 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class BusinessOpportunityCollectServiceImpl extends
		MpLongIdBaseServiceImpl<BusinessOpportunityCollect, BusinessOpportunityCollectMapper>
		implements BusinessOpportunityCollectService {

	public BusinessOpportunityCollectServiceImpl(
			BusinessOpportunityCollectMapper repository) {
		super(repository);
	}

	@Override
	public List<BusinessOpportunityCollect> findByCustomId(Long customId) {
		LambdaQueryWrapper<BusinessOpportunityCollect> wrapper = Wrappers
				.lambdaQuery(BusinessOpportunityCollect.class);
		this.filterDeleted(wrapper);
		wrapper.eq(BusinessOpportunityCollect::getCustomId, customId);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<BusinessOpportunityCollect> findByCustomIdAndBusinessId(
			Long customId, Long businessOpportunityId) {
		LambdaQueryWrapper<BusinessOpportunityCollect> wrapper = Wrappers
				.lambdaQuery(BusinessOpportunityCollect.class);
		this.filterDeleted(wrapper);
		wrapper.eq(BusinessOpportunityCollect::getCustomId, customId);
		wrapper.eq(BusinessOpportunityCollect::getBusinessId,
				businessOpportunityId);
		// 创建时间最新
		wrapper.orderByDesc(BusinessOpportunityCollect::getCreatedTime);
		wrapper.last("limit 1");
		return Optional.ofNullable(repository.selectOne(wrapper));

	}
}
