package com.zhihaoscm.service.resource.custom.ship;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.GeoPoint;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.meta.biz.ShipDef;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.ShipService;
import com.zhihaoscm.service.resource.form.ship.CustomShipUpdateForm;
import com.zhihaoscm.service.resource.form.ship.ShipExtendCustomForm;
import com.zhihaoscm.service.resource.form.ship.ShipExtendForm;
import com.zhihaoscm.service.resource.form.ship.ShipPortHistoryForm;
import com.zhihaoscm.service.resource.validator.ship.ShipValidator;
import com.zhihaoscm.ships66.sdk.response.ShipInfo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "船舶管理", description = "船舶管理API")
@RestController
@RequestMapping(value = "/ship")
public class ShipResource {

	@Autowired
	private ShipService service;

	@Autowired
	private ShipValidator validator;

	@Operation(summary = "查询船舶列表")
	@GetMapping(value = "/custom-paging")
	public ApiResponse<Page<ShipVo>> customPaging(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size) {
		return new ApiResponse<>(PageUtil
				.convert(service.customPaging(page, size, CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount().getId())));
	}

	@Operation(summary = "船舶下拉列表")
	@GetMapping("/selector")
	public ApiResponse<Page<Ship>> selector(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "状态") @RequestParam(value = "state", required = false) Integer state,
			@Parameter(description = "船舶名或者mmsi") @RequestParam(value = "keyword", required = false) String keyword) {
		return new ApiResponse<>(
				PageUtil.convert(service.selector(page, size, state, keyword)));
	}

	@Operation(summary = "根据船舶中文名查询船舶")
	@GetMapping(value = "/cname")
	public ApiResponse<List<Ship>> findByCname(@RequestParam String cname) {
		return new ApiResponse<>(service.findByCname(cname));
	}

	@Operation(summary = "承运商船舶下拉")
	@GetMapping("/carrier-selector/{id}")
	public ApiResponse<Ship> carrierSelector(
			@PathVariable(value = "id") String id) {
		validator.validateId(id);
		return new ApiResponse<>(service.carrierSelector(id).orElse(null));
	}

	@Operation(summary = "查询船舶详情")
	@GetMapping(value = "/vo/{id}")
	public ApiResponse<ShipVo> findVoById(
			@PathVariable(value = "id") String id) {
		validator.validateExist(id);
		return new ApiResponse<>(service.findVoById(id).orElse(null));
	}

	@Operation(summary = "查询当前承运商已认证船舶列表")
	@GetMapping("/carrier")
	public ApiResponse<List<Ship>> findByCarrier(
			@RequestParam(value = "keyword", required = false) String keyword) {
		return new ApiResponse<>(service.findByCarrier(CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId(), keyword));
	}

	@Operation(summary = "查询当前承运商已认证船舶列表(未上传内河船舶检验报告)")
	@GetMapping("/carrier-no-report-file")
	public ApiResponse<List<Ship>> findByCarrierAndNoReportFile() {
		return new ApiResponse<>(
				service.findByCarrierAndNoReportFile(CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount().getId()));
	}

	@Operation(summary = "识别船舶船员适任证书")
	@PostMapping("/recognize-crew-certificate")
	public ApiResponse<String> recognizeCrewCertificate(
			@RequestParam Long fileId) {
		return new ApiResponse<>(
				service.recognizeCrewCertificate(fileId).orElse(null));
	}

	@Operation(summary = "查询船舶并新增")
	@GetMapping(value = "/find-create/{id}")
	public ApiResponse<ShipInfoVo> findByIdAndCreate(
			@PathVariable(value = "id") String id) {
		validator.validateId(id);
		Long userId;
		try {
			userId = CustomerContextHolder.getCustomerLoginVo()
					.getProxyAccount().getId();
		} catch (Exception e) {
			userId = null;
		}

		ShipInfo shipInfo = service.findByAsi(id);
		return new ApiResponse<>(service
				.findByIdAndCreate(shipInfo, userId,
						ShipDef.MonitoringChannels.MINI_PROGRAM.getCode())
				.orElse(null));
	}

	@Operation(summary = "更新船舶")
	@PutMapping(value = "/{id}")
	public ApiResponse<Void> update(@PathVariable(value = "id") String id,
			@Validated @RequestBody CustomShipUpdateForm form) {
		Ship ship = validator.validateUpdate(id);
		service.updateAllProperties(form.convertToEntity(ship));
		return new ApiResponse<>();
	}

	@Operation(summary = "区域查船")
	@PostMapping(value = "/extent")
	public ApiResponse<List<ShipInfoVo>> findByExtent(
			@RequestBody ShipExtendForm form) {
		validator.validateExtent(form);
		List<ShipInfo> shipInfoByExtent = service.findShipInfoByExtent(
				form.getLeftTopPoint(), form.getRightTopPoint(),
				form.getRightBottomPoint(), form.getLeftBottomPoint(),
				form.getType(), form.getTime());
		return new ApiResponse<>(service.findByExtent(shipInfoByExtent));
	}

	@Operation(summary = "自定义查船")
	@PostMapping(value = "/extent/custom")
	public ApiResponse<List<ShipInfoVo>> findByCustomExtent(
			@RequestBody ShipExtendCustomForm from) {
		validator.validateExtentCustom(from);
		List<GeoPoint> geoPointList = from.getGeoPointList();
		return new ApiResponse<>(service.findByCustomExtent(geoPointList,
				from.getType(), from.getTime()));
	}

	@Operation(summary = "查询船舶轨迹")
	@GetMapping(value = "/aisTrack")
	public ApiResponse<List<ShipTrajectoryVo>> findByAisTrack(
			@Parameter(description = "mmsi") @RequestParam(value = "mmsi") String mmsi,
			@Parameter(description = "开始时间戳") @RequestParam(value = "start") Long start,
			@Parameter(description = "结束时间戳") @RequestParam(value = "end") Long end) {
		return new ApiResponse<>(service.findByAisTrack(mmsi, start, end));
	}

	@Operation(summary = "根据关键字查询")
	@GetMapping(value = "/keyword")
	public ApiResponse<List<Ship>> findByKeyword(
			@Parameter(description = "关键字") @RequestParam String keyword,
			@Parameter(description = "限制 默认 10 条") @RequestParam(value = "limit", required = false) Integer limit,
			@Parameter(description = "类型 （船舶 SHIP 航标 VMN 网位仪 NETSONDE)") @RequestParam(value = "type", required = false, defaultValue = "SHIP") String type) {
		return new ApiResponse<>(service.findByKeyword(keyword, limit, type));
	}

	@Operation(summary = "查询船舶、码头历史记录")
	@GetMapping("/history")
	public ApiResponse<List<ShipPortHistoryVo>> findShipPortHistory() {
		return new ApiResponse<>(service.findShipPortHistory(
				Objects.requireNonNull(CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount()).getId(),
				ShipDef.MonitoringChannels.MINI_PROGRAM.getCode()));
	}

	@Operation(summary = "根据货主找船需求智能推荐船舶", hidden = true)
	@GetMapping(value = "/find/custom/{id}")
	public ApiResponse<List<ShipSmartVo>> findByShipCustom(
			@PathVariable(value = "id") Long id) {
		return new ApiResponse<>(service.findByShipCustom(id,
				ShipDef.MonitoringChannels.MINI_PROGRAM.getCode()));
	}

	@Operation(summary = "新增船舶、码头历史记录")
	@PostMapping("/history")
	public ApiResponse<Void> createHistory(
			@RequestBody ShipPortHistoryForm form) {
		service.createShipPortHistory(
				Objects.requireNonNull(CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount()).getId(),
				ShipDef.MonitoringChannels.MINI_PROGRAM.getCode(),
				form.convertToEntity());
		return new ApiResponse<>();
	}

	@Operation(summary = "删除船舶、码头历史记录")
	@DeleteMapping("/history")
	public ApiResponse<Void> deleteShipPortHistory(
			@RequestBody ShipPortHistoryForm form) {
		service.deleteShipPortHistory(
				Objects.requireNonNull(CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount()).getId(),
				ShipDef.MonitoringChannels.MINI_PROGRAM.getCode(),
				form.convertToEntity());
		return new ApiResponse<>();
	}

	@Operation(summary = "清空船舶、码头历史记录")
	@DeleteMapping("/all-history")
	public ApiResponse<Void> deleteAllShipPortHistory() {
		service.deleteAllShipPortHistory(
				Objects.requireNonNull(CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount()).getId(),
				ShipDef.MonitoringChannels.MINI_PROGRAM.getCode());
		return new ApiResponse<>();
	}

}
