package com.zhihaoscm.service.core.service;

import java.util.Optional;

import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.AdvertisementPosition;

/**
 * <p>
 * 广告位 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
public interface AdvertisementPositionService
		extends MpLongIdBaseService<AdvertisementPosition> {

	/**
	 * 根据广告位和广告位号查询查询
	 *
	 * @param position
	 * @return
	 */
	Optional<AdvertisementPosition> findByPosition(Integer positionType,
			Integer position);

	/**
	 * 判断广告位是否存在
	 *
	 * @param advertId
	 * @return
	 */
	Boolean existAdvertId(Long advertId);
}
