package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.OilOrder;
import com.zhihaoscm.domain.bean.entity.OilOrderSnapshot;
import com.zhihaoscm.domain.bean.entity.OilSite;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.OilOrderVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AutoCodeDef;
import com.zhihaoscm.domain.meta.biz.OilOrderDef;
import com.zhihaoscm.domain.meta.biz.WxwDef;
import com.zhihaoscm.service.core.mapper.OilOrderMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

/**
 * <p>
 * 油品订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Service
public class OilOrderServiceImpl
		extends MpStringIdBaseServiceImpl<OilOrder, OilOrderMapper>
		implements OilOrderService {

	@Autowired
	private OilSiteService oilSiteService;
	@Autowired
	private OilOrderSnapshotService oilOrderSnapshotService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private ShipService shipService;
	@Autowired
	private UserService userService;
	@Autowired
	private MessageService messageService;

	public OilOrderServiceImpl(OilOrderMapper repository) {
		super(repository);
	}

	@Override
	public Page<OilOrderVo> customPaging(Integer page, Integer size,
			Integer state, Long customId) {
		LambdaQueryWrapper<OilOrder> wrapper = Wrappers
				.lambdaQuery(OilOrder.class);
		this.filterDeleted(wrapper);
		// 用户id
		wrapper.eq(OilOrder::getCaptainId, customId);
		// 业务状态
		wrapper.eq(Objects.nonNull(state), OilOrder::getState, state);
		// 预约中1、进行中2、已完成3、已取消4，相同状态更新时间倒序默认展示5条
		wrapper.orderByAsc(OilOrder::getState)
				.orderByDesc(OilOrder::getUpdatedTime);
		Page<OilOrder> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Optional<OilOrderVo> findVoById(String id, Double lon, Double lat) {
		return this.findOne(id)
				.map(oilOrder -> this.packVo(oilOrder, lon, lat));
	}

	@Override
	public Optional<OilOrderVo> findLast(Long customId, List<Integer> states) {
		LambdaQueryWrapper<OilOrder> queryWrapper = Wrappers
				.lambdaQuery(OilOrder.class);
		this.filterDeleted(queryWrapper);
		// 用户id
		queryWrapper.eq(OilOrder::getCaptainId, customId);
		// 状态
		queryWrapper.in(CollectionUtils.isNotEmpty(states), OilOrder::getState,
				states);
		// 创建时间最新
		queryWrapper.orderByDesc(OilOrder::getCreatedTime);
		queryWrapper.last("limit 1");
		OilOrder oilOrder = repository.selectOne(queryWrapper);
		OilOrderVo vo = this.packVo(oilOrder, null, null);
		return Optional.ofNullable(vo);
	}

	@FileId
	@Override
	public OilOrder create(OilOrder resource) {
		return super.create(resource);
	}

	@FileId(type = 2)
	@Override
	public OilOrder updateAllProperties(OilOrder resource) {
		return super.updateAllProperties(resource);
	}

	@FileId(type = 3)
	@Override
	public void delete(String id) {
		super.delete(id);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void agree(OilOrder oilOrder) {
		// 设置用户同意加油协议
		oilOrder.setIsAgreeProtocol(CommonDef.Symbol.YES.getCode());
		oilOrder = this.updateAllProperties(oilOrder);
		// 同步修改订单快照
		OilOrderSnapshot snapshot = this.toSnapshot(oilOrder);
		oilOrderSnapshotService.updateAllProperties(snapshot);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public OilOrder booking(OilOrder oilOrder) {
		LocalDateTime bookingTime = LocalDateTime.now();
		// 设置订单id
		oilOrder.setId(AutoCodeDef.CREATE_AUTO_CODE.apply(
				SpringUtil.getBean(StringRedisClient.class),
				AutoCodeDef.BusinessCode.OIL_ORDER.getCode(),
				RedisKeys.Cache.OIL_ORDER, "01", 5,
				AutoCodeDef.DATE_TYPE.yyMM));
		// 设置船主信息
		customerService.findVoById(oilOrder.getCaptainId())
				.ifPresent(customerVo -> {
					Enterprise captainEnterprise = new Enterprise();
					captainEnterprise.setName(
							customerVo.getCustomer().getInstitutionName());
					captainEnterprise.setUnifiedSocialCreditCode(customerVo
							.getCustomer().getUnifiedSocialCreditCode());
					captainEnterprise.setLegalRepresentative(
							customerVo.getCustomer().getLegalRepresentative());
					captainEnterprise.setRealName(
							customerVo.getCustomer().getRealName());
					captainEnterprise
							.setCode(customerVo.getCustomer().getCode());
					if (Objects.isNull(captainEnterprise.getMobile())) {
						captainEnterprise.setMobile(
								customerVo.getCustomer().getMobile());
					}
					oilOrder.setCaptainInfo(captainEnterprise);
				});
		// 设置船舶信息
		shipService.findOne(oilOrder.getShipId()).ifPresent(ship -> {
			oilOrder.setShipName(ship.getName());
			oilOrder.setShipCnName(ship.getCnname());
		});
		// 设置下单时间
		oilOrder.setBookingTime(bookingTime);
		// 如果加油量和油价都不为空，相乘得到预约油费
		if (Objects.nonNull(oilOrder.getBookingRefuelingVolume())
				&& Objects.nonNull(oilOrder.getBookingRefuelingPrice())) {
			oilOrder.setBookingRefuelingCost(
					oilOrder.getBookingRefuelingVolume()
							.multiply(oilOrder.getBookingRefuelingPrice()));
		}
		// 设置状态
		oilOrder.setState(OilOrderDef.State.BOOKING.getCode());
		OilOrder order = this.create(oilOrder);
		// 同步新增订单快照
		OilOrderSnapshot snapshot = this.toSnapshot(order);
		oilOrderSnapshotService.create(snapshot);
		// 发送企微消息
		this.sendWxNotice(order, OilOrderDef.NoticeType.TO_BE_CHECK);

		return order;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void confirmRefueling(OilOrder oilOrder) {
		// 订单进行中状态改为 待完成加油
		oilOrder.setProgressState(
				OilOrderDef.ProgressState.AWAITING_COMPLETION.getCode());
		oilOrder = this.updateAllProperties(oilOrder);
		// 同步修改订单快照的进行中状态和确认加油时间
		OilOrderSnapshot snapshot = this.toSnapshot(oilOrder);
		oilOrderSnapshotService.updateAllProperties(snapshot);
		this.sendWxNotice(oilOrder, OilOrderDef.NoticeType.TO_BE_COMPLETE);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void cancel(String id) {
		LocalDateTime cancelTime = LocalDateTime.now();
		this.findOne(id).ifPresent(oilOrder -> {
			// 记录取消前业务状态、取消前进行中状态
			oilOrder.setBeforeCancelState(oilOrder.getState());
			oilOrder.setBeforeCancelProgressState(oilOrder.getProgressState());
			// 设置订单业务状态为已取消
			oilOrder.setState(OilOrderDef.State.CANCELED.getCode());
			// 设置取消时间
			oilOrder.setCancelTime(cancelTime);
			OilOrder order = this.updateAllProperties(oilOrder);
			// 同步修改订单快照
			OilOrderSnapshot snapshot = this.toSnapshot(order);
			oilOrderSnapshotService.updateAllProperties(snapshot);
		});
	}

	@Override
	public OilOrderSnapshot toSnapshot(OilOrder order) {
		OilOrderSnapshot snapshot = new OilOrderSnapshot();
		BeanUtils.copyProperties(order, snapshot);
		return snapshot;
	}

	@Override
	public void sendWxNotice(OilOrder oilOrder,
			OilOrderDef.NoticeType noticeType) {
		messageService.sendNotice(WxwMessage.builder().receiptors(userService
				.findUsersByPermission(AdminPermissionDef.GAS_ORDER_DEAL, null)
				.stream().map(user -> String.valueOf(user.getId())).toList())
				.url("/logistics/oil/oilTypeManagement/detail/"
						.concat(oilOrder.getId()))
				.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
				.operationModule(noticeType.getState()
						+ WxwDef.NoticeOperationModule.OIL_ORDER.getDesc())
				.desc(noticeType.getOperate()).keyword(oilOrder.getId())
				.content(StringUtils.EMPTY).build());
	}

	/**
	 * 组装数据
	 */
	private OilOrderVo packVo(OilOrder oilOrder, Double lon, Double lat) {

		if (Objects.nonNull(oilOrder)) {
			OilOrderVo vo = new OilOrderVo();
			vo.setOilOrder(oilOrder);
			if (Objects.nonNull(oilOrder.getOilSiteId())) {
				OilSite oilSite = oilSiteService
						.findOneWithDeleted(oilOrder.getOilSiteId())
						.orElse(null);
				if (Objects.nonNull(oilSite)) {
					vo.setOilSite(oilSite);
					// 计算每个站点距离用户的距离
					Map<Long, BigDecimal> distanceMap = oilSiteService
							.calcDistance(lon, lat, List.of(oilSite));
					vo.setDistance(
							Objects.nonNull(distanceMap.get(oilSite.getId()))
									? distanceMap.get(oilSite.getId())
									: BigDecimal.ZERO);
				}

			}
			return vo;
		}
		return null;
	}

	/**
	 * 组装数据
	 */
	private List<OilOrderVo> packVo(List<OilOrder> oilOrderList) {
		if (CollectionUtils.isEmpty(oilOrderList)) {
			return List.of();
		}
		List<Long> oilSiteIds = oilOrderList.stream()
				.map(OilOrder::getOilSiteId).toList();
		Map<Long, OilSite> oilSiteMap = oilSiteService.getIdMap(oilSiteIds);

		return oilOrderList.stream().map(item -> {
			OilOrderVo vo = new OilOrderVo();
			vo.setOilOrder(item);
			if (Objects.nonNull(item.getOilSiteId())) {
				vo.setOilSite(oilSiteMap.get(item.getOilSiteId()));
			}
			return vo;
		}).toList();
	}
}
