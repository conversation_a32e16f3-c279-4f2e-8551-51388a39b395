package com.zhihaoscm.service.resource.custom.business.opportunity;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.domain.bean.vo.BusinessOpportunityVo;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.BusinessOpportunityService;
import com.zhihaoscm.service.resource.validator.business.opportunity.BusinessOpportunityValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * <p>
 * 商机 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@RestController
@RequestMapping("/business-opportunity")
public class BusinessOpportunityResource {

	@Autowired
	private BusinessOpportunityService service;
	@Autowired
	private BusinessOpportunityValidator validator;

	@Operation(summary = "分页查询")
	@GetMapping("/custom-paging")
	public ApiResponse<Page<BusinessOpportunityVo>> customPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "标题") @RequestParam(required = false) String keyword,
			@Parameter(description = "信息类型") @RequestParam(required = false) List<Integer> type,
			@Parameter(description = "一级或二级地址编码") @RequestParam(required = false) String addressCode,
			@Parameter(description = "发布开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@Parameter(description = "发布结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
		return new ApiResponse<>(service.customPaging(page, size, keyword, type,
				addressCode, beginTime, endTime, CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount().getId()));
	}

	@Operation(summary = "分页查询收藏商机列表")
	@GetMapping("/custom-paging-collect")
	public ApiResponse<Page<BusinessOpportunityVo>> customPagingCollect(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "标题") @RequestParam(required = false) String keyword) {
		return new ApiResponse<>(service.customPagingCollect(page, size,
				keyword, CustomerContextHolder.getCustomerLoginVo()
						.getProxyAccount().getId()));
	}

	@Operation(summary = "相关商机列表")
	@GetMapping("/related-list")
	public ApiResponse<List<BusinessOpportunityVo>> relatedList(
			@Parameter(description = "当前商机id") @RequestParam Long businessOpportunityId) {
		return new ApiResponse<>(
				service.relatedList(businessOpportunityId, CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount().getId()));
	}

	@Operation(summary = "查询商机详情")
	@GetMapping("/{id}")
	public ApiResponse<BusinessOpportunityVo> findById(@PathVariable Long id) {
		return new ApiResponse<>(service.findVoById(id, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId()).orElse(null));
	}

	@Operation(summary = "收藏、取消收藏商机")
	@PostMapping("/collect/{id}/{state}")
	public ApiResponse<Void> collect(@PathVariable Long id,
			@Parameter(description = "1 收藏 0 取消收藏") @PathVariable Integer state) {
		Long customId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		validator.validateCollect(id, state, customId);
		service.collect(id, state, customId);
		return new ApiResponse<>();
	}
}
