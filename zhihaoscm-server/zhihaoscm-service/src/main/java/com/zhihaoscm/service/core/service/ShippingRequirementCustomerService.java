package com.zhihaoscm.service.core.service;

import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementCustomer;
import com.zhihaoscm.domain.bean.vo.ShippingRequirementCustomerCountVo;

/**
 * <p>
 * 货主船运需求 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
public interface ShippingRequirementCustomerService
		extends MpLongIdBaseService<ShippingRequirementCustomer> {

	/**
	 * 统计货主船运需求数量
	 *
	 * @return
	 */
	ShippingRequirementCustomerCountVo statiscShippingRequirementCustomer(
			Long userId, Boolean hasFull, Boolean hasDeal);

}
