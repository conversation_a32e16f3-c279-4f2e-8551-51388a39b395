package com.zhihaoscm.service.resource.form.business.opportunity;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "BusinessOpportunityAuditForm", description = "商机审核表单")
public class BusinessOpportunityAuditForm {

	@Schema(title = "审核结果：1通过、0不通过")
	@NotNull(message = ErrorCode.CODE_30194016)
	private Integer state;

	@Schema(title = "备注（不通过）")
	@Length(max = 32, message = ErrorCode.CODE_30194010)
	private String remark;

	@Schema(title = "未通过原因（不通过）")
	@Range(min = 1, max = 5, message = ErrorCode.CODE_30194009)
	private Integer failedReason;

	@Schema(title = "联系方式(通过)")
	private String contactMobile;
}
