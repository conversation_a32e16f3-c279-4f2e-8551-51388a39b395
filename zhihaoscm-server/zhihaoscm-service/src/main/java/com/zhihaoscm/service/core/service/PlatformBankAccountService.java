package com.zhihaoscm.service.core.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.PlatformBankAccount;

/**
 * <p>
 * 平台银行账户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface PlatformBankAccountService
		extends MpLongIdBaseService<PlatformBankAccount> {

	/**
	 * 分页查询平台银行账号
	 *
	 * @param page
	 * @param size
	 * @return
	 */
	Page<PlatformBankAccount> paging(Integer page, Integer size);

	/**
	 * 平台银行账号下拉列表
	 *
	 * @return
	 */
	List<PlatformBankAccount> selector(String name, Integer state,
			List<Long> useTypes);

	/**
	 * 根据状态和使用场景查询平台银行账号列表
	 * 
	 * @param state
	 * @param useTypes
	 * @return
	 */
	List<PlatformBankAccount> findByStateAndUseTypes(Integer state,
			List<Long> useTypes);

}
