package com.zhihaoscm.service.resource.custom.oil.site;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.domain.bean.vo.OilSiteProvinceVo;
import com.zhihaoscm.domain.bean.vo.OilSiteVo;
import com.zhihaoscm.service.core.service.OilSiteService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 油品站点 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@Tag(name = "油品站点", description = "油品站点API")
@RestController
@RequestMapping("/oil-site")
public class OilSiteResource {

	@Autowired
	private OilSiteService oilSiteService;

	@GetMapping("/paging")
	@Operation(summary = "查询站点列表")
	public ApiResponse<List<OilSiteProvinceVo>> paging(
			@Parameter(description = "站点名称") @RequestParam(required = false) String keyword,
			@Parameter(description = "省编码") @RequestParam(required = false) String provinceCode,
			@Parameter(description = "当前经度") @RequestParam(required = false) Double lon,
			@Parameter(description = "当前纬度") @RequestParam(required = false) Double lat) {
		return new ApiResponse<>(
				oilSiteService.customPaging(keyword, provinceCode, lon, lat));
	}

	@GetMapping("/paging-search")
	@Operation(summary = "搜索页-分页查询站点")
	public ApiResponse<Page<OilSiteVo>> pagingSearch(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "站点名称") @RequestParam(required = false) String keyword,
			@Parameter(description = "省编码") @RequestParam(required = false) String provinceCode,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder,
			@Parameter(description = "当前经度") @RequestParam(required = false) Double lon,
			@Parameter(description = "当前纬度") @RequestParam(required = false) Double lat) {
		return new ApiResponse<>(oilSiteService.customPagingSearch(page, size,
				keyword, provinceCode, sortKey, sortOrder, lon, lat));
	}

	@Operation(summary = "查询有站点的一级地址")
	@GetMapping("/find-site-province")
	public ApiResponse<List<String>> findSiteProvince() {
		return new ApiResponse<>(oilSiteService.findSiteProvince());
	}

	@Operation(summary = "查找单个站点详情数据")
	@GetMapping("/vo/{id}")
	public ApiResponse<OilSiteVo> findVoById(@PathVariable Long id,
			@Parameter(description = "当前经度") @RequestParam(required = false) Double lon,
			@Parameter(description = "当前纬度") @RequestParam(required = false) Double lat) {
		return new ApiResponse<>(
				oilSiteService.findVoById(id, lon, lat).orElse(null));
	}

}
