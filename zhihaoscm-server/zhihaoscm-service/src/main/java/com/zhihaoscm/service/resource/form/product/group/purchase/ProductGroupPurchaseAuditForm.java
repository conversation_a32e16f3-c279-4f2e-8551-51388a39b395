package com.zhihaoscm.service.resource.form.product.group.purchase;

import java.time.LocalDateTime;

import com.zhihaoscm.domain.bean.entity.ProductGroupPurchase;
import com.zhihaoscm.domain.meta.biz.ProductGroupPurchaseDef;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "商品审核表单")
public class ProductGroupPurchaseAuditForm {

	@Schema(title = "审核状态 true 同意  false 拒绝")
	private Boolean state;

	@Schema(title = "审核意见")
	private String auditDescn;

	public void update(ProductGroupPurchase product) {

		if (this.getState()) {
			if (ProductGroupPurchaseDef.State.DOWN_SHELF
					.match(product.getState())) {
				product.setState(
						ProductGroupPurchaseDef.State.UP_SHELF.getCode());
				product.setPublishState(
						ProductGroupPurchaseDef.PublishState.PASS.getCode());
			} else {
				product.setState(
						ProductGroupPurchaseDef.State.DOWN_SHELF.getCode());
				product.setPublishState(
						ProductGroupPurchaseDef.PublishState.DOWN_SHELF
								.getCode());
			}
		} else {
			product.setPublishState(
					ProductGroupPurchaseDef.PublishState.FAIL.getCode());
		}
		product.setAuditDescn(this.getAuditDescn());
		product.setAuditTime(LocalDateTime.now());
	}
}
