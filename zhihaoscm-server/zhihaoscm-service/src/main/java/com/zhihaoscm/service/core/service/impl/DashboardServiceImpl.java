package com.zhihaoscm.service.core.service.impl;

import java.util.List;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.vo.GoodsFindShipStatisticVo;
import com.zhihaoscm.domain.meta.biz.ShipDef;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementPlatDef;
import com.zhihaoscm.service.core.service.DashboardService;
import com.zhihaoscm.service.core.service.ShipService;
import com.zhihaoscm.service.core.service.ShippingRequirementPlatService;

@Service
public class DashboardServiceImpl implements DashboardService {

	@Lazy
	@Autowired
	private ShipService shipService;

	@Lazy
	@Autowired
	private ShippingRequirementPlatService shippingRequirementPlatService;

	@Override
	public Optional<GoodsFindShipStatisticVo> shipStatistic() {
		GoodsFindShipStatisticVo vo = new GoodsFindShipStatisticVo();

		// 累计船舶数量
		Long totalShipNum = shipService.countByState(null, null, null, null,
				null);
		vo.setTotalShipNum(totalShipNum);
		// 认证船舶数量
		long totalShipApplyNum = shipService.countByState(
				ShipDef.State.AUTHENTICATED.getCode(), null, null, null, null);
		vo.setTotalShipApplyNum(totalShipApplyNum);

		return Optional.of(vo);
	}

	@Override
	public Optional<GoodsFindShipStatisticVo> customStatistic() {
		GoodsFindShipStatisticVo vo = new GoodsFindShipStatisticVo();

		// 认证船舶
		List<Ship> ships = shipService
				.findByState(ShipDef.State.AUTHENTICATED.getCode());
		if (CollectionUtils.isNotEmpty(ships)) {
			// 认证船舶数量
			long totalShipApplyNum = ships.size();
			vo.setTotalShipApplyNum(totalShipApplyNum);
			// 认证船舶的用户数量
			long totalRegisterCarrierNum = ships.stream().map(Ship::getCarrier)
					.distinct().count();
			vo.setTotalRegisterCarrierNum(totalRegisterCarrierNum);
		}

		return Optional.of(vo);
	}

	@Override
	public Optional<GoodsFindShipStatisticVo> shippingStatistic() {
		GoodsFindShipStatisticVo vo = new GoodsFindShipStatisticVo();

		// 查询全部平台船运需求
		List<ShippingRequirementPlat> shippingRequirementPlatServiceAll = shippingRequirementPlatService
				.findAll();
		// 累计货船匹配运单-平台船运需求列表的所有数量
		// 已关闭的数据不在页面上显示
		long totalShippingRequirementPlatNum = shippingRequirementPlatServiceAll
				.stream()
				.filter(item -> !ShippingRequirementPlatDef.State.CLOSED
						.match(item.getState()))
				.count();
		vo.setTotalShippingRequirementPlatNum(totalShippingRequirementPlatNum);

		return Optional.of(vo);
	}
}
