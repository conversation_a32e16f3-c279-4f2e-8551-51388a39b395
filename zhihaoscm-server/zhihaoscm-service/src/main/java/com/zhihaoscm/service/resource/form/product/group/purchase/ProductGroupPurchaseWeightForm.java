package com.zhihaoscm.service.resource.form.product.group.purchase;

import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "商品权重表单对象")
public class ProductGroupPurchaseWeightForm {

	@Schema(title = "权重")
	@NotNull(message = ErrorCode.CODE_30094003)
	@Range(min = 0, max = 10000, message = ErrorCode.CODE_30094003)
	private Integer weight;
}
