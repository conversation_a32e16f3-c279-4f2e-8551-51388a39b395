package com.zhihaoscm.service.resource.admin.oil.order.snapshot;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.entity.OilOrder;
import com.zhihaoscm.domain.bean.entity.OilOrderSnapshot;
import com.zhihaoscm.domain.bean.vo.OilOrderSnapshotVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.HistoryDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.service.core.service.OilOrderSnapshotService;
import com.zhihaoscm.service.resource.form.oil.order.OilOrderBookingAdminForm;
import com.zhihaoscm.service.resource.form.oil.order.OilOrderCheckForm;
import com.zhihaoscm.service.resource.form.oil.order.OilOrderConfirmForm;
import com.zhihaoscm.service.resource.form.oil.order.OilOrderPlanForm;
import com.zhihaoscm.service.resource.validator.oil.order.OilOrderSnapshotValidator;
import com.zhihaoscm.service.resource.validator.oil.order.OilOrderValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 油品订单快照 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@Tag(name = "油品订单", description = "油品订单API")
@RestController
@RequestMapping("/oil-order-snapshot")
public class OilOrderSnapshotResource {
	@Autowired
	private OilOrderSnapshotService service;
	@Autowired
	private OilOrderSnapshotValidator validator;
	@Autowired
	private OilOrderValidator orderValidator;

	@Operation(summary = "分页查询油品订单列表")
	@GetMapping(value = "/paging")
	@Secured({ AdminPermissionDef.GAS_ORDER_R,
			AdminPermissionDef.GAS_ORDER_DEAL,
			AdminPermissionDef.GAS_ORDER_MANAGE })
	public ApiResponse<Page<OilOrderSnapshotVo>> customPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "订单编号、船舶名称或MMSI") @RequestParam(required = false) String shipName,
			@Parameter(description = "承运商账号信息") @RequestParam(required = false) String customerInfo,
			@Parameter(description = "站点名称") @RequestParam(required = false) String siteName,
			@Parameter(description = "加油开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@Parameter(description = "加油结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
			@Parameter(description = "订单方式:1.自行下单/2.平台代下") @RequestParam(required = false) Integer orderType,
			@Parameter(description = "业务状态:1.预约中、2.进行中、3.已完成、4.已取消") @RequestParam(required = false) Integer state,
			@Parameter(description = "支付状态:1.待确认/2.已支付") @RequestParam(required = false) Integer payState,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder) {
		return new ApiResponse<>(PageUtil.convert(service.paging(page, size,
				shipName, customerInfo, siteName, beginTime, endTime, orderType,
				state, payState, sortKey, sortOrder)));
	}

	@Operation(summary = "查询油品订单详情")
	@GetMapping("/vo/{id}")
	@Secured({ AdminPermissionDef.GAS_ORDER_R,
			AdminPermissionDef.GAS_ORDER_DEAL,
			AdminPermissionDef.GAS_ORDER_MANAGE })
	public ApiResponse<OilOrderSnapshotVo> findVoById(@PathVariable String id) {
		return new ApiResponse<>(service.findVoById(id).orElse(null));
	}

	@Operation(summary = "预约加油")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = LogDef.OIL_ORDER_ADD, type = LogDef.LOGISTICS_MANAGEMENT_OIL_ORDER, bizNo = "{{#_ret.getData().getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getId()}}") })
	@PostMapping("/booking-refueling")
	@Secured({ AdminPermissionDef.GAS_ORDER_DEAL })
	public ApiResponse<OilOrder> bookingRefueling(
			@Validated @RequestBody OilOrderBookingAdminForm form) {
		return new ApiResponse<>(service.adminBooking(form.convertToEntity()));
	}

	@Operation(summary = "核对油品订单")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.OIL_ORDER_CHECK, type = LogDef.LOGISTICS_MANAGEMENT_OIL_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@PutMapping(value = "/check-refueling/{id}")
	@Secured({ AdminPermissionDef.GAS_ORDER_DEAL })
	public ApiResponse<Void> checkRefueling(@PathVariable String id,
			@Validated @RequestBody OilOrderCheckForm form) {
		OilOrderSnapshot snapshot = validator.validateCheckRefueling(id, form);
		service.checkRefueling(snapshot, form.getCheckType());
		return new ApiResponse<>();
	}

	@Operation(summary = "报计划")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.OIL_ORDER_PLAN, type = LogDef.LOGISTICS_MANAGEMENT_OIL_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@PutMapping(value = "/plan/{id}")
	@Secured({ AdminPermissionDef.GAS_ORDER_DEAL })
	@History(success = HistoryDef.OIL_ORDER_PLAN, isSaveIdentity = true, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_OIL_ORDER)
	public ApiResponse<Void> plan(@PathVariable String id,
			@Validated @RequestBody OilOrderPlanForm form) {
		OilOrderSnapshot snapshot = validator.validatePlan(id, form);
		service.plan(snapshot);
		return new ApiResponse<>();
	}

	@Operation(summary = "确认加油")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.OIL_ORDER_CONFIRM, type = LogDef.LOGISTICS_MANAGEMENT_OIL_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@PutMapping(value = "/confirm-refueling/{id}")
	@Secured({ AdminPermissionDef.GAS_ORDER_DEAL })
	@History(success = HistoryDef.OIL_ORDER_CONFIRM, isSaveIdentity = true, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_OIL_ORDER)
	public ApiResponse<Void> confirmRefueling(@PathVariable String id,
			@Validated @RequestBody OilOrderConfirmForm form) {
		OilOrder oilOrder = orderValidator.validateConfirmRefueling(id,
				CommonDef.Symbol.YES.getCode());
		service.confirmRefueling(form.convertToEntity(oilOrder));
		return new ApiResponse<>();
	}

	@Operation(summary = "完成加油")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.OIL_ORDER_COMPLETE, type = LogDef.LOGISTICS_MANAGEMENT_OIL_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@PutMapping(value = "/complete/{id}")
	@Secured({ AdminPermissionDef.GAS_ORDER_DEAL })
	@History(success = HistoryDef.OIL_ORDER_COMPLETE, isSaveIdentity = true, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_OIL_ORDER)
	public ApiResponse<Void> complete(@PathVariable String id) {
		validator.validateComplete(id);
		service.complete(id);
		return new ApiResponse<>();
	}

	@Operation(summary = "取消订单")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.OIL_ORDER_CANCEL, type = LogDef.LOGISTICS_MANAGEMENT_OIL_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@PutMapping(value = "/cancel/{id}")
	@Secured({ AdminPermissionDef.GAS_ORDER_DEAL })
	@History(success = HistoryDef.OIL_ORDER_CANCEL, isSaveIdentity = true, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_OIL_ORDER)
	public ApiResponse<Void> cancel(@PathVariable String id) {
		orderValidator.validateCancel(id);
		service.cancel(id);
		return new ApiResponse<>();
	}

	@Operation(summary = "删除")
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.OIL_ORDER_DELETE, type = LogDef.LOGISTICS_MANAGEMENT_OIL_ORDER, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#id}}") })
	@DeleteMapping(value = "/delete/{id}")
	@Secured({ AdminPermissionDef.GAS_ORDER_MANAGE })
	public ApiResponse<Void> delete(@PathVariable String id) {
		validator.validateDelete(id);
		service.delete(id);
		return new ApiResponse<>();
	}
}
