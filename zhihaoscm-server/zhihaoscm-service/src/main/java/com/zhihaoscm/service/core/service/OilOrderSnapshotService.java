package com.zhihaoscm.service.core.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.entity.OilOrder;
import com.zhihaoscm.domain.bean.entity.OilOrderSnapshot;
import com.zhihaoscm.domain.bean.vo.OilOrderSnapshotCountVo;
import com.zhihaoscm.domain.bean.vo.OilOrderSnapshotVo;

/**
 * <p>
 * 油品订单快照 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
public interface OilOrderSnapshotService
		extends MpStringIdBaseService<OilOrderSnapshot> {

	/**
	 * 后台分页查询油品订单列表
	 */
	Page<OilOrderSnapshotVo> paging(Integer page, Integer size, String shipName,
			String customerInfo, String siteName, LocalDateTime beginTime,
			LocalDateTime endTime, Integer orderType, Integer state,
			Integer payState, String sortKey, String sortOrder);

	/**
	 * 根据id查询订单
	 */
	Optional<OilOrderSnapshotVo> findVoById(String id);

	/**
	 * 根据状态查询
	 * 
	 * @param states
	 * @return
	 */
	List<OilOrderSnapshot> findByStates(List<Integer> states);

	/**
	 * 管理后台新增油品订单
	 * 
	 * @param oilOrder
	 */
	OilOrder adminBooking(OilOrder oilOrder);

	/**
	 * 核对订单
	 * 
	 * @param snapshot
	 * @param checkType
	 *            核对方式：1.提交/2.保存
	 */
	void checkRefueling(OilOrderSnapshot snapshot, Integer checkType);

	/**
	 * 报计划
	 * 
	 * @param snapshot
	 */
	void plan(OilOrderSnapshot snapshot);

	/**
	 * 确认加油
	 */
	void confirmRefueling(OilOrder oilOrder);

	/**
	 * 后台完成订单
	 * 
	 * @param id
	 */
	void complete(String id);

	/**
	 * 后台取消订单
	 * 
	 * @param id
	 */
	void cancel(String id);

	/**
	 * 待办统计-油品订单
	 *
	 * @param hasFull
	 *            处理权限
	 * @return
	 */
	OilOrderSnapshotCountVo statisticsOilOrderSnapshot(Boolean hasFull);

	/**
	 * 油费已确认，且已完成报计划,给船主发送短信和站内，提醒船主去确认加油
	 */
	void sendConfirmRefuelingNotice(OilOrderSnapshot snapshot);
}
