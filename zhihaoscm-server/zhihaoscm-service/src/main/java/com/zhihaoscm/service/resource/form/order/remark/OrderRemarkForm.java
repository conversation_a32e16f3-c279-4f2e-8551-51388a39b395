package com.zhihaoscm.service.resource.form.order.remark;

import org.hibernate.validator.constraints.Length;

import com.zhihaoscm.domain.bean.entity.OrderRemark;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class OrderRemarkForm {

	@Schema(title = "订单ID")
	@NotBlank(message = "订单ID不能为空")
	private String orderId;

	@Schema(title = "附件ID")
	@NotNull(message = "附件ID不能为空")
	private Long fileId;

	@Schema(title = "备注")
	@Length(max = 200, message = "备注不能超过200个字符")
	private String remark;

	public OrderRemark convertToEntity() {
		OrderRemark orderRemark = new OrderRemark();
		orderRemark.setOrderId(this.orderId);
		orderRemark.setRemark(this.remark);
		orderRemark.setFileId(this.fileId);
		return orderRemark;
	}
}
