package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.OilIndex;
import com.zhihaoscm.domain.bean.entity.OilSite;
import com.zhihaoscm.domain.bean.vo.OilSiteProvinceVo;
import com.zhihaoscm.domain.bean.vo.OilSiteVo;
import com.zhihaoscm.domain.utils.GeoUtils;
import com.zhihaoscm.domain.utils.PaginationUtils;
import com.zhihaoscm.service.core.mapper.OilSiteMapper;
import com.zhihaoscm.service.core.service.OilIndexService;
import com.zhihaoscm.service.core.service.OilSiteService;

/**
 * <p>
 * 油品站点 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class OilSiteServiceImpl
		extends MpLongIdBaseServiceImpl<OilSite, OilSiteMapper>
		implements OilSiteService {

	@Autowired
	private OilIndexService oilIndexService;

	public OilSiteServiceImpl(OilSiteMapper repository) {
		super(repository);
	}

	@Override
	public Page<OilSite> paging(Integer page, Integer size, String keyword,
			String brand, Integer state, String sortKey, String sortOrder) {
		LambdaQueryWrapper<OilSite> wrapper = Wrappers
				.lambdaQuery(OilSite.class);
		this.filterDeleted(wrapper);
		// 站点名称模糊搜索、编号模糊搜索
		wrapper.and(StringUtils.isNotBlank(keyword),
				w -> w.like(OilSite::getId, keyword).or().like(OilSite::getName,
						keyword));
		// 站点品牌
		wrapper.like(StringUtils.isNotBlank(brand), OilSite::getBrand, brand);
		// 站点状态
		wrapper.eq(Objects.nonNull(state), OilSite::getState, state);
		// 排序
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 按状态已启用1、已禁用0排序，状态相同按创建时间倒序
			wrapper.orderByDesc(OilSite::getState)
					.orderByDesc(OilSite::getCreatedTime)
					.orderByDesc(OilSite::getId);
		}
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public List<OilSiteProvinceVo> customPaging(String keyword,
			String provinceCode, Double lon, Double lat) {
		LambdaQueryWrapper<OilSite> wrapper = Wrappers
				.lambdaQuery(OilSite.class);
		this.filterDeleted(wrapper);
		// 已启用
		wrapper.eq(OilSite::getState, CommonDef.Symbol.YES.getCode());
		// 站点名称模糊搜索
		wrapper.like(StringUtils.isNotBlank(keyword), OilSite::getName,
				keyword);
		// 一级地址匹配
		wrapper.eq(StringUtils.isNotBlank(provinceCode),
				OilSite::getProvinceCode, provinceCode);
		// 按拼音排序省份
		wrapper.last("ORDER BY CONVERT(province_name USING gbk)");
		// 按省份名称顺序，再按实际售卖价顺序，再按最低实际售卖价的数量倒序，再按距离顺序，最后按创建时间倒序
		return this.toProvinceVoList(repository.selectList(wrapper), lon, lat);
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<OilSiteVo> customPagingSearch(
			Integer page, Integer size, String keyword, String provinceCode,
			String sortKey, String sortOrder, Double lon, Double lat) {
		LambdaQueryWrapper<OilSite> wrapper = Wrappers
				.lambdaQuery(OilSite.class);
		this.filterDeleted(wrapper);
		// 已启用
		wrapper.eq(OilSite::getState, CommonDef.Symbol.YES.getCode());
		// 站点名称模糊搜索
		wrapper.like(StringUtils.isNotBlank(keyword), OilSite::getName,
				keyword);
		// 一级地址匹配
		wrapper.eq(StringUtils.isNotBlank(provinceCode),
				OilSite::getProvinceCode, provinceCode);
		// 按拼音排序省份
		wrapper.last("ORDER BY CONVERT(province_name USING gbk)");
		// 按省份名称顺序，再按实际售卖价顺序，再按最低实际售卖价的数量倒序，再按距离顺序，最后按创建时间倒序
		return PaginationUtils.handelPage(page, size,
				this.packListVo(repository.selectList(wrapper), lon, lat));
	}

	@Override
	public Page<OilSite> pagingSelector(Integer page, Integer size,
			String name) {
		LambdaQueryWrapper<OilSite> wrapper = Wrappers
				.lambdaQuery(OilSite.class);
		this.filterDeleted(wrapper);
		// 已启用
		wrapper.eq(OilSite::getState, CommonDef.Symbol.YES.getCode());
		// 模糊匹配站点名称
		wrapper.like(StringUtils.isNotBlank(name), OilSite::getName, name);
		// 站点编号顺序排序
		wrapper.orderByAsc(OilSite::getId);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Optional<OilSiteVo> findVoById(Long id, Double lon, Double lat) {
		return this.findOne(id).map(oilSite -> this.packVo(oilSite, lon, lat));
	}

	@Override
	public List<OilSite> findByNameAndNotInId(String name, Long id) {
		LambdaQueryWrapper<OilSite> wrapper = Wrappers
				.lambdaQuery(OilSite.class);
		this.filterDeleted(wrapper);
		wrapper.eq(StringUtils.isNotBlank(name), OilSite::getName, name)
				.ne(Objects.nonNull(id), OilSite::getId, id);
		return repository.selectList(wrapper);
	}

	@Override
	public List<OilSite> findByState(Integer state) {
		LambdaQueryWrapper<OilSite> wrapper = Wrappers
				.lambdaQuery(OilSite.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(state), OilSite::getState, state);
		return repository.selectList(wrapper);
	}

	@Override
	public List<OilSite> findByNameLikeWithDeleted(String siteName) {
		LambdaQueryWrapper<OilSite> wrapper = Wrappers
				.lambdaQuery(OilSite.class);
		wrapper.like(StringUtils.isNotBlank(siteName), OilSite::getName,
				siteName);
		return repository.selectList(wrapper);
	}

	@Override
	public List<String> findSiteProvince() {
		List<String> provinceCodeList = new ArrayList<>();
		List<OilSite> oilSiteList = this
				.findByState(CommonDef.Symbol.YES.getCode());
		if (CollectionUtils.isNotEmpty(oilSiteList)) {
			provinceCodeList = oilSiteList.stream()
					.map(OilSite::getProvinceCode).distinct().toList();
		}
		return provinceCodeList;
	}

	@Override
	public List<OilSite> findByNameOrId(String keyword) {
		if (StringUtils.isBlank(keyword)) {
			return List.of();
		}
		LambdaQueryWrapper<OilSite> wrapper = Wrappers
				.lambdaQuery(OilSite.class);
		wrapper.and(w -> w.like(OilSite::getName, keyword).or()
				.like(OilSite::getId, keyword));
		return repository.selectList(wrapper);
	}

	@Override
	public List<OilSite> findByBrandLike(String brand) {
		if (StringUtils.isBlank(brand)) {
			return List.of();
		}
		LambdaQueryWrapper<OilSite> wrapper = Wrappers
				.lambdaQuery(OilSite.class);
		wrapper.like(OilSite::getBrand, brand);
		return repository.selectList(wrapper);
	}

	@Override
	public List<OilSite> findByProvinceNameLike(String provinceName) {
		if (StringUtils.isBlank(provinceName)) {
			return List.of();
		}
		LambdaQueryWrapper<OilSite> wrapper = Wrappers
				.lambdaQuery(OilSite.class);
		wrapper.like(OilSite::getProvinceName, provinceName);
		return repository.selectList(wrapper);
	}

	@Override
	public void updateState(Long id, Integer state) {
		LambdaUpdateWrapper<OilSite> updateWrapper = Wrappers
				.lambdaUpdate(OilSite.class).eq(OilSite::getId, id)
				.set(OilSite::getState, state);
		repository.update(updateWrapper);
	}

	/**
	 * 组装分页查询vo
	 */
	private OilSiteVo packVo(OilSite oilSite, Double lon, Double lat) {
		OilSiteVo vo = new OilSiteVo();
		if (Objects.isNull(oilSite)) {
			return vo;
		}
		vo.setOilSite(oilSite);
		// 计算每个站点距离用户的距离
		Map<Long, BigDecimal> distanceMap = this.calcDistance(lon, lat,
				List.of(oilSite));
		vo.setDistance(Objects.nonNull(distanceMap.get(oilSite.getId()))
				? distanceMap.get(oilSite.getId())
				: BigDecimal.ZERO);
		// 设置油品指数
		oilIndexService.findByOilSiteId(oilSite.getId())
				.ifPresent(vo::setOilIndex);
		return vo;
	}

	/**
	 * 组装查询vo
	 */
	@Override
	public List<OilSiteVo> packListVo(List<OilSite> records, Double lon,
			Double lat) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		List<OilSiteVo> oilSiteVos = new ArrayList<>();

		// 油站ids
		List<Long> oilSiteIds = records.stream().map(OilSite::getId).toList();
		// 根据油站ids查询油品指数
		List<OilIndex> oilIndexes = oilIndexService
				.findByOilSiteIds(oilSiteIds);
		// 根据油站id进行分组，查出每个油站的最新的价格
		Map<Long, OilIndex> oilIndexMap = oilIndexes.stream()
				.collect(Collectors.groupingBy(OilIndex::getOilSiteId,
						Collectors.collectingAndThen(
								Collectors.maxBy(Comparator
										.comparing(OilIndex::getVersionDate)),
								optional -> optional.orElse(null))));

		// 计算每个站点距离用户的距离
		Map<Long, BigDecimal> distanceMap = this.calcDistance(lon, lat,
				records);
		for (OilSite record : records) {
			OilSiteVo vo = new OilSiteVo();
			vo.setOilSite(record);
			vo.setDistance(Objects.nonNull(distanceMap.get(record.getId()))
					? distanceMap.get(record.getId())
					: BigDecimal.ZERO);
			OilIndex oilIndex = oilIndexMap.get(record.getId());
			if (Objects.nonNull(oilIndex)) {
				vo.setOilIndex(oilIndex);
				// 设置最低实际售卖价
				Long lightFuelListingPrice = oilIndex
						.getLightFuelListingPrice();
				Long zeroDieselListingPrice = oilIndex
						.getZeroDieselListingPrice();
				if (Objects.nonNull(lightFuelListingPrice)
						&& Objects.nonNull(zeroDieselListingPrice)) {
					// 两个实际售卖价都不为空，取价格最低的
					vo.setLowestListingPrice(Math.min(lightFuelListingPrice,
							zeroDieselListingPrice));
					// 如果两个价格一样，数量就是2，否则是1
					vo.setLowestListingPriceCount(
							lightFuelListingPrice.equals(zeroDieselListingPrice)
									? 2L
									: 1L);
				} else if (Objects.nonNull(lightFuelListingPrice)) {
					// 只有轻质燃油实际售卖价不为空
					vo.setLowestListingPrice(lightFuelListingPrice);
					vo.setLowestListingPriceCount(1L);
				} else if (Objects.nonNull(zeroDieselListingPrice)) {
					// 只有0号柴油实际售卖价不为空
					vo.setLowestListingPrice(zeroDieselListingPrice);
					vo.setLowestListingPriceCount(1L);
				}
			}
			oilSiteVos.add(vo);
		}
		// 按省份名称顺序，再按实际售卖价顺序，再按最低实际售卖价的数量倒序，再按距离顺序，最后按创建时间倒序
		// 获取中文拼音比较器
		Collator collator = Collator.getInstance(Locale.CHINA);
		return oilSiteVos.stream().sorted(Comparator
				.comparing((OilSiteVo vo) -> vo.getOilSite().getProvinceName(),
						collator)
				.thenComparing(OilSiteVo::getLowestListingPrice,
						Comparator.nullsLast(Comparator.naturalOrder()))
				.thenComparing(OilSiteVo::getLowestListingPriceCount,
						Comparator.nullsLast(Comparator.reverseOrder()))
				.thenComparing(OilSiteVo::getDistance,
						Comparator.nullsLast(Comparator.naturalOrder()))
				.thenComparing(vo -> vo.getOilSite().getCreatedTime(),
						Comparator.nullsLast(Comparator.reverseOrder())))
				.toList();
	}

	/**
	 * 将排序后的oilSiteVoSortedList按省份名称分组
	 */
	private List<OilSiteProvinceVo> toProvinceVoList(List<OilSite> records,
			Double lon, Double lat) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		List<OilSiteProvinceVo> vos = new ArrayList<>();

		// 按省份分组，保留查出来的省份的顺序
		Map<String, List<OilSite>> oilSiteGroupMap = records.stream()
				.collect(Collectors.groupingBy(OilSite::getProvinceName,
						LinkedHashMap::new, Collectors.toList()));
		// 将每个省份下的油站数据分别进行组装并放到OilSiteProvinceVo中
		for (Map.Entry<String, List<OilSite>> entry : oilSiteGroupMap
				.entrySet()) {
			OilSiteProvinceVo provinceVo = new OilSiteProvinceVo();
			provinceVo.setProvinceName(entry.getKey());
			// 将OilSite排序
			provinceVo.setOilSiteVoList(
					this.packListVo(entry.getValue(), lon, lat));
			vos.add(provinceVo);
		}
		return vos;
	}

	/**
	 * 计算每个站点距离用户的距离
	 * 
	 * @param lon
	 *            经度
	 * @param lat
	 *            纬度
	 */
	@Override
	public Map<Long, BigDecimal> calcDistance(Double lon, Double lat,
			List<OilSite> records) {
		Map<Long, BigDecimal> distanceMap = new HashMap<>();
		if (CollectionUtils.isEmpty(records) || Objects.isNull(lon)
				|| Objects.isNull(lat)) {
			return distanceMap;
		}

		for (OilSite oilSite : records) {
			// 按逗号分割
			String[] parts = oilSite.getLatLon().split(",");
			// 经度
			double siteLon = Double.parseDouble(parts[0].trim());
			// 纬度
			double siteLat = Double.parseDouble(parts[1].trim());

			// 计算距离（米）
			double distance = 1000
					* GeoUtils.haversineDistance(lat, lon, siteLat, siteLon);

			// 转换为BigDecimal，保留2位小数
			BigDecimal distanceBigDecimal = BigDecimal.valueOf(distance)
					.setScale(2, RoundingMode.HALF_UP);

			distanceMap.put(oilSite.getId(), distanceBigDecimal);
		}

		return distanceMap;
	}
}
