package com.zhihaoscm.service.resource.custom.product.consignment;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.vo.ProductConsignmentVo;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.ProductConsignmentService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 寄售商品 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Tag(name = "寄售商品管理", description = "寄售商品管理API")
@RestController
@RequestMapping("/product-consignment")
public class ProductConsignmentResource {

	@Autowired
	private ProductConsignmentService service;

	@Operation(summary = "分页查询商品列表")
	@GetMapping("/custom-paging")
	public ApiResponse<Page<ProductConsignmentVo>> customPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "商品标题") @RequestParam(required = false) String keyword,
			@Parameter(description = "品类id") @RequestParam(required = false) String productTypeId,
			@Parameter(description = "供应商名称") @RequestParam(required = false) String supplierName,
			@Parameter(description = "二级地址编码") @RequestParam(required = false) String cityCode) {
		return new ApiResponse<>(PageUtil.convert(service.customPaging(page,
				size, keyword, productTypeId, supplierName, cityCode,
				CustomerContextHolder.getCustomerLoginVo().getProxyAccount()
						.getId())));
	}

	@Operation(summary = "查询商品详情")
	@GetMapping("/vo/{id}")
	public ApiResponse<ProductConsignmentVo> findVoById(
			@PathVariable String id) {
		ProductConsignmentVo vo = service.findVoById(id, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId()).orElse(null);
		if (Objects.isNull(vo)) {
			throw new BadRequestException(ErrorCode.CODE_30196001);
		}
		return new ApiResponse<>(vo);
	}
}
