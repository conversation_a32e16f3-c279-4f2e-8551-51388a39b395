package com.zhihaoscm.service.core.service.usercenter;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.MembershipLevel;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.bean.vo.CustomerVo;
import com.zhihaoscm.service.client.usercenter.CustomerClient;
import com.zhihaoscm.service.config.security.AuthenService;

/**
 * <p>
 * 用户服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Service
public class CustomerService implements AuthenService<CustomerLoginVo>,
		BaseUserCenterService<Customer> {

	@Autowired
	private CustomerClient client;

	public Optional<Customer> findById(Long id) {
		return Optional.ofNullable(client.findById(id));
	}

	@Override
	public Optional<Customer> findOne(Long id) {
		return Optional.ofNullable(client.findById(id));
	}

	public Customer updateAllProperties(Customer resource) {
		return client.updateAllProperties(resource);
	}

	public Long countByAppType(Integer appType, LocalDateTime beginTime,
			LocalDateTime endTime) {
		return client.countByAppType(appType, beginTime, endTime);
	}

	public List<Customer> findByIdsNoDeleted(Collection<Long> ids) {
		return client.findByIdsNoDeleted(ids);
	}

	public List<Customer> findAll(LocalDateTime beginTime,
			LocalDateTime endTime) {
		return client.findAll(beginTime, endTime);
	}

	public List<Customer> findAll() {
		return client.findAll();
	}

	public Long countByMemberLevel(Integer memberLevel, LocalDateTime beginTime,
			LocalDateTime endTime) {
		return client.countByMemberLevel(memberLevel, beginTime, endTime);
	}

	public MembershipLevel getMembershipLevel(Customer customer) {
		return client.getMembershipLevel(customer);
	}

	public Customer update(Customer resource) {
		return client.update(resource);
	}

	public Customer create(Customer resource) {
		return client.create(resource);
	}

	public List<Customer> findByIds(List<Long> ids) {
		return client.findByIds(ids);
	}

	public Optional<CustomerVo> findVoById(Long id) {
		return Optional.ofNullable(client.findVoById(id));
	}

	public List<Customer> selector(String searchParam, Integer personalAuth,
			Integer enterpriseAuth) {
		return client.selector(searchParam, personalAuth, enterpriseAuth);
	}

	public Optional<Enterprise> findEnterpriseByCustomerId(Long customerId) {
		return Optional
				.ofNullable(client.findEnterpriseByCustomerId(customerId));
	}

	public void batchUpdate(List<Customer> datas) {
		client.batchUpdate(datas);
	}

	/**
	 * 根据code获取客户信息
	 *
	 * @param code
	 * @return
	 */
	public Optional<Customer> findByCode(String code) {
		return Optional.ofNullable(client.findByCode(code));
	}

	public List<Customer> supplierSelector(String name) {
		return client.supplierSelector(name);
	}

	/**
	 * 从token中解析用户
	 *
	 * @param token
	 * @return
	 */
	@Override
	public LoginUser<CustomerLoginVo> readUser(String token) {
		return client.parseToken(token);
	}
}
