package com.zhihaoscm.service.resource.admin.platform;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.PlatformBankAccount;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.service.core.service.PlatformBankAccountService;
import com.zhihaoscm.service.resource.form.platform.PlatformBankAccountForm;
import com.zhihaoscm.service.resource.validator.platform.PlatformValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "平台银行账号", description = "平台银行账号API")
@RestController
@RequestMapping("/platform/bank-account")
public class PlatformBankAccountResource {

	@Autowired
	private PlatformBankAccountService platformBankAccountService;
	@Autowired
	private PlatformValidator platformValidator;

	@Operation(summary = "分页查询平台银行账号")
	@GetMapping("/paging")
	@Secured(AdminPermissionDef.ACCOUNT_MANAGE)
	public ApiResponse<Page<PlatformBankAccount>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size) {
		return new ApiResponse<>(PageUtil
				.convert(platformBankAccountService.paging(page, size)));
	}

	@Operation(summary = "平台银行账号下拉列表")
	@GetMapping("/selector")
	public ApiResponse<List<PlatformBankAccount>> selector(
			@RequestParam(required = false) String name,
			@RequestParam(required = false) Integer state,
			@RequestParam(required = false) List<Long> useTypes) {
		return new ApiResponse<>(
				platformBankAccountService.selector(name, state, useTypes));
	}

	@Operation(summary = "根据id查询平台银行账号信息")
	@GetMapping("/{id}")
	@Secured(AdminPermissionDef.ACCOUNT_MANAGE)
	public ApiResponse<PlatformBankAccount> findById(@PathVariable Long id) {
		return new ApiResponse<>(
				platformBankAccountService.findOne(id).orElse(null));
	}

	@Operation(summary = "新增平台银行账户")
	@PostMapping
	@Secured(AdminPermissionDef.ACCOUNT_MANAGE)
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = LogDef.PLATFORM_BANK_ACCOUNT_ADD, type = LogDef.FINANCE_MANAGEMENT_PLATFORM_BANK_ACCOUNT, bizNo = "{{#_ret.getData().getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getName()}}") })
	public ApiResponse<PlatformBankAccount> create(
			@Validated @RequestBody PlatformBankAccountForm form) {
		platformValidator.validateCreate(form);
		return new ApiResponse<>(platformBankAccountService
				.create(form.convertToEntity(new PlatformBankAccount())));
	}

	@Operation(summary = "修改平台银行账户")
	@PutMapping("/{id}")
	@Secured(AdminPermissionDef.ACCOUNT_MANAGE)
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.PLATFORM_BANK_ACCOUNT_EDIT, type = LogDef.FINANCE_MANAGEMENT_PLATFORM_BANK_ACCOUNT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getName()}}") })
	public ApiResponse<PlatformBankAccount> update(@PathVariable Long id,
			@Validated @RequestBody PlatformBankAccountForm form) {
		PlatformBankAccount platformBankAccount = platformBankAccountService
				.updateAllProperties(
						platformValidator.validateUpdate(id, form));
		return new ApiResponse<>(platformBankAccount);
	}

	@Operation(summary = "更新平台银行账户状态")
	@PutMapping("/update/{id}/{state}")
	@Secured(AdminPermissionDef.ACCOUNT_MANAGE)
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = "{{#success}}", type = LogDef.FINANCE_MANAGEMENT_PLATFORM_BANK_ACCOUNT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getName()}}") })
	public ApiResponse<PlatformBankAccount> updateState(@PathVariable Long id,
			@PathVariable Integer state) {
		PlatformBankAccount platformBankAccount1 = platformBankAccountService
				.updateAllProperties(
						platformValidator.validateUpdateState(id, state));
		LogRecordContext.putVariable("success",
				CommonDef.Symbol.NO.match(state)
						? LogDef.PLATFORM_BANK_ACCOUNT_DISABLE
						: LogDef.PLATFORM_BANK_ACCOUNT_ENABLE);
		return new ApiResponse<>(platformBankAccount1);
	}
}
