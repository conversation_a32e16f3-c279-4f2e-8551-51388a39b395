package com.zhihaoscm.service.resource.form.supplier;

import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import java.math.BigDecimal;

/**
 * @author: xiaYZ 2025/7/31
 * @version: 1.0
 */
@Data
@Schema(name = "供应商管理")
public class SupplierForm {

    @Schema(description = "供应商名称")
    @NotBlank(message = ErrorCode.CODE_30198001)
    private String supplierName;

    @Schema(description = "关联集团id")
    private String groupCompanyId;

    @Schema(description = "关联集团名称")
    private String groupCompanyName;

    @Schema(description = "集团类型 1 矿山，2 砂场，3矿山加工厂，4贸易商，5其他")
    @NotNull(message = ErrorCode.CODE_30198002)
    @Range(min = 1, max = 5, message = ErrorCode.CODE_30198002)
    private Integer type;

    @Schema(description = "企业标签")
    @Size(max = 6, message = ErrorCode.CODE_30198003)
    private ArrayString labels;

    @Schema(description = "省份")
    @NotBlank(message = ErrorCode.CODE_30198004)
    private String province;

    @Schema(description = "地级市")
    @NotBlank(message = ErrorCode.CODE_30198005)
    private String city;

    @Schema(description = "县区")
    private String district;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "经度")
    @NotNull(message = ErrorCode.CODE_30198006)
    private BigDecimal lng;

    @Schema(description = "纬度")
    @NotNull(message = ErrorCode.CODE_30198006)
    private BigDecimal lat;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "主营品类")
    @NotBlank(message = ErrorCode.CODE_30198007)
    private String mainCategories;

    @Schema(description = "企业logo文件id")
    @NotNull(message = ErrorCode.CODE_30198008)
    private Long logoFileId;

    @Schema(description = "统一社会信用代码")
    @NotBlank(message = ErrorCode.CODE_30198009)
    private String socialCreditCode;

    @Schema(description = "法定代表人")
    @NotBlank(message = ErrorCode.CODE_30198011)
    private String legalRepresentative;

    @Schema(description = "移动端图片文件id")
    private Long mobileSideFileId;

    @Schema(description = "PC端图片文件id")
    private Long pcSideFileId;

    @Schema(description = "描述介绍")
    @Length(max = 300, message = ErrorCode.CODE_30198012)
    private String description;

    @Schema(description = "展示位置（合作矿厂位置）")
    @Range(min = 1, max = 8, message = ErrorCode.CODE_30198013)
    private Integer showLocation;


}
