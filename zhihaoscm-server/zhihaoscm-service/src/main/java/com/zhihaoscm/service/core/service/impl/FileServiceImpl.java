package com.zhihaoscm.service.core.service.impl;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import javax.imageio.ImageIO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.aliyun.oss.sdk.config.AliyunOSSProperties;
import com.zhihaoscm.aliyun.oss.sdk.protocol.AliyunFileService;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.multipart.file.CustomMultipartFile;
import com.zhihaoscm.common.api.util.multipart.file.MultipartFileUtils;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.ExceptionUtil;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.UUIDUtil;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.FileDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.mapper.FileMapper;
import com.zhihaoscm.service.core.service.FileService;
import com.zhihaoscm.service.utils.admin.WatermarkUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 附件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Slf4j
@Service
public class FileServiceImpl extends MpLongIdBaseServiceImpl<File, FileMapper>
		implements FileService {

	@Autowired
	private AliyunFileService aliyunFileService;
	@Autowired
	private AliyunOSSProperties aliyunOSSProperties;
	@Autowired
	private StringRedisClient redisClient;

	public FileServiceImpl(FileMapper repository) {
		super(repository);
	}

	@Override
	public Optional<File> findOne(Long id) {
		Optional<File> optionalFile = super.findOne(id);
		return optionalFile.map(file -> {
			this.replacePath(file);
			return file;
		});
	}

	@Override
	public Optional<File> findOne(Long id, boolean isDeletedCache) {
		Optional<File> optionalFile = Optional
				.ofNullable(repository.selectById(id));
		return optionalFile.map(file -> {
			if (isDeletedCache) {
				// 删掉缓存，重新查
				String key = RedisKeys.Cache.FILE_CACHE_PREFIX + id;
				redisClient.delete(key);
			}
			this.replacePath(file, 24 * 60 * 60);
			return file;
		});
	}

	@Override
	public Optional<File> findOneWithUnsigned(Long id) {
		return super.findOne(id);
	}

	@Override
	public List<File> findByIds(Collection<Long> ids) {
		List<File> files = super.findByIds(ids);
		files.forEach(this::replacePath);
		return files;
	}

	@Override
	public List<File> findByIdsNoDeleted(Collection<Long> ids) {
		List<File> files = super.findByIdsNoDeleted(ids);
		files.forEach(this::replacePath);
		return files;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<File> upload(MultipartFile file, String fileName)
			throws Exception {
		String originalFileName = file.getOriginalFilename();
		assert originalFileName != null;
		String suffix = FileDef.GET_FILE_SUFFIX.apply(originalFileName);
		// 存储到对象存储中的名称
		String storeFileName = UUIDUtil.random() + suffix;
		// 存储到对象存储中的路径 YYYY-MM-DD/storeFileName
		String filePath = FileDef.BUILD_FILE_PATH.apply(storeFileName);

		if (aliyunFileService.upload(filePath, file.getInputStream())) {
			// 保存文件信息
			File dbFile = new File();
			dbFile.setName(StringUtils.isNotBlank(fileName) ? fileName
					: originalFileName);
			dbFile.setType(suffix);
			dbFile.setPath(filePath);
			dbFile.setState(FileDef.State.INACTIVE.getCode());
			dbFile = super.create(dbFile);
			return this.findOne(dbFile.getId());
		}
		return Optional.empty();
	}

	@Override
	public Optional<String> transferToPublic(String filePath, String folder) {
		// 去掉 URL 参数（? 后面的内容）
		String path = filePath.split("\\?")[0];

		// 提取文件名
		String fileName = path.substring(path.lastIndexOf('/') + 1);

		// 提取后缀名（不带 .）
		String extension = "";
		int dotIndex = fileName.lastIndexOf('.');
		if (dotIndex != -1 && dotIndex < fileName.length() - 1) {
			extension = fileName.substring(dotIndex + 1);
		}
		String storeFilePath = folder + "/" + UUIDUtil.random() + "."
				+ extension;
		boolean flag = false;
		try (InputStream inputStream = new URL(filePath).openStream()) {
			flag = aliyunFileService.uploadToPublic(storeFilePath, inputStream);
		} catch (Exception e) {
			log.error("upload error:{}", ExceptionUtil.buildErrorMessage(e));
		}
		return flag ? Optional.of(FileDef.PUBLIC_BUCKET_PREFIX + storeFilePath)
				: Optional.empty();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<File> uploadWatermark(MultipartFile file, String fileName,
			String watermark) throws Exception {
		// 1. 获取原始文件名和类型
		String originalFilename = file.getOriginalFilename();

		// 2. 判断是否为图片类型
		String contentType = file.getContentType();

		try {
			if (Objects.nonNull(contentType)
					&& contentType.startsWith("image/")) {
				// 不是图片类型，直接返回原文件或抛异常
				String ext = "png";
				if (originalFilename != null
						&& originalFilename.contains(".")) {
					ext = originalFilename
							.substring(originalFilename.lastIndexOf('.') + 1);
				}

				// 3. MultipartFile转BufferedImage
				BufferedImage originalImage = ImageIO
						.read(file.getInputStream());
				if (originalImage == null) {
					log.error("无法读取图片文件，可能是不支持的格式或已损坏");
					return this.upload(file, StringUtils.EMPTY);
				}
				int imageWidth = originalImage.getWidth();
				int watermarkWidth = Math.max(200, (int) (imageWidth / 1.2));
				int fontSize = Math.max(12, imageWidth / 50);

				// 4. 生成水印图片
				BufferedImage watermarkImg = WatermarkUtil.createWatermarkImage(
						watermark, fontSize, 0.9f, watermarkWidth);

				// 5. 合成水印
				BufferedImage watermarked = net.coobird.thumbnailator.Thumbnails
						.of(originalImage).scale(1.0)
						.watermark(
								net.coobird.thumbnailator.geometry.Positions.TOP_LEFT,
								watermarkImg, 1.0f)
						.outputQuality(1.0).asBufferedImage();

				// 6. BufferedImage转为字节流
				ByteArrayOutputStream baos = new ByteArrayOutputStream();
				ImageIO.write(watermarked, ext, baos);
				baos.flush();

				// 7. 构造新的MultipartFile
				MultipartFile newFile = new CustomMultipartFile("file",
						originalFilename, contentType, baos.toByteArray());
				return this.upload(newFile, StringUtils.EMPTY);
			}
		} catch (Exception e) {
			log.error("上传水印图片失败");
			return this.upload(file, StringUtils.EMPTY);
		}
		return this.upload(file, StringUtils.EMPTY);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<File> uploadAndActive(MultipartFile file, String fileName)
			throws IOException {
		String originalFileName = file.getOriginalFilename();
		assert originalFileName != null;
		String suffix = FileDef.GET_FILE_SUFFIX.apply(originalFileName);
		// 存储到对象存储中的名称
		String storeFileName = UUIDUtil.random() + suffix;
		// 存储到对象存储中的路径 YYYY-MM-DD/storeFileName
		String filePath = FileDef.BUILD_FILE_PATH.apply(storeFileName);

		if (aliyunFileService.upload(filePath, file.getInputStream())) {
			// 保存文件信息
			File dbFile = new File();
			dbFile.setName(StringUtils.isNotBlank(fileName) ? fileName
					: originalFileName);
			dbFile.setType(suffix);
			dbFile.setPath(filePath);
			dbFile.setState(FileDef.State.ACTIVE.getCode());
			dbFile = super.create(dbFile);
			return this.findOne(dbFile.getId());
		}
		return Optional.empty();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long uploadFile(String fileUrl, String fileName) {
		if (Objects.isNull(fileUrl)) {
			return null;
		}
		try {
			MultipartFile multipartFile = MultipartFileUtils
					.urlToMultipartFile(fileUrl, fileName);
			File newFile = this.uploadAndActive(multipartFile, fileName)
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30008001));

			return newFile.getId();

		} catch (Exception e) {
			log.error("下载代开协议文件上传到链云异常 ===> {}", e.getMessage());
			throw new BadRequestException(ErrorCode.CODE_30008001);
		}
	}

	@Override
	public Optional<String> downloadTemplate(Integer type) {
		FileDef.ImportTemplate importTemplate = FileDef.ImportTemplate
				.from(type);
		if (Objects.isNull(importTemplate)) {
			return Optional.empty();
		}
		return aliyunFileService.getPresignedUrl(importTemplate.getName());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(File file) {
		super.delete(file.getId());
		// 删除oss上的文件
		aliyunFileService.delete(file.getPath());
	}

	@Override
	public void active(Long id) {
		File file = new File();
		file.setId(id);
		file.setState(FileDef.State.ACTIVE.getCode());
		super.update(file);
	}

	@Override
	public void batchActive(List<Long> activeIds) {
		LambdaUpdateWrapper<File> updateWrapper = Wrappers
				.lambdaUpdate(File.class).in(File::getId, activeIds)
				.set(File::getState, FileDef.State.ACTIVE.getCode());
		repository.update(updateWrapper);
	}

	@Override
	public void batchUnActive(List<Long> unActiveIds) {
		LambdaUpdateWrapper<File> updateWrapper = Wrappers
				.lambdaUpdate(File.class).in(File::getId, unActiveIds)
				.set(File::getState, FileDef.State.INACTIVE.getCode());
		repository.update(updateWrapper);
	}

	@Override
	public InputStream download(String name) {
		return aliyunFileService.download(name);
	}

	/**
	 * 替换文件路径
	 * 
	 * @param file
	 */
	private void replacePath(File file) {
		String url = redisClient
				.get(RedisKeys.Cache.FILE_CACHE_PREFIX + file.getId());
		if (StringUtils.isNotBlank(url)) {
			file.setPath(url);
			return;
		}

		Optional<String> optionalUrl = aliyunFileService
				.getPresignedUrl(file.getPath());
		if (optionalUrl.isPresent()) {
			redisClient.setEx(RedisKeys.Cache.FILE_CACHE_PREFIX + file.getId(),
					optionalUrl.get(), aliyunOSSProperties.getCacheExpire(),
					TimeUnit.SECONDS);
			file.setPath(optionalUrl.get());
		}
	}

	/**
	 * 替换文件路径
	 *
	 * @param file
	 */
	private void replacePath(File file, Integer urlExpire) {
		String url = redisClient
				.get(RedisKeys.Cache.FILE_CACHE_PREFIX + file.getId());
		if (StringUtils.isNotBlank(url)) {
			file.setPath(url);
			return;
		}

		Optional<String> optionalUrl = aliyunFileService
				.getPresignedUrl(file.getPath(), urlExpire);
		if (optionalUrl.isPresent()) {
			redisClient.setEx(RedisKeys.Cache.FILE_CACHE_PREFIX + file.getId(),
					optionalUrl.get(), aliyunOSSProperties.getCacheExpire(),
					TimeUnit.SECONDS);
			file.setPath(optionalUrl.get());
		}
	}
}
