package com.zhihaoscm.service.resource;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.api.ErrorEntity;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.exception.ResourceNotFoundException;
import com.zhihaoscm.domain.exception.DynamicsBadRequestException;
import com.zhihaoscm.domain.exception.ServiceRequestException;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.meta.error.ErrorCodeDef;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

/**
 * 全局异常处理
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {
	@ExceptionHandler(AccessDeniedException.class)
	public ResponseEntity<ApiResponse<Void>> accessDeniedException(
			AccessDeniedException e) {
		// 异常处理在 ExceptionTranslationFilter中进行处理
		throw e;
	}

	/**
	 * 未找到资源错误
	 */
	@ExceptionHandler(ResourceNotFoundException.class)
	public ResponseEntity<ApiResponse<Object>> resourceNotFoundException(
			ResourceNotFoundException e) {
		log.warn("resource not found, resource: ", e);

		ErrorCodeDef apiErrorCodeDef = ErrorCodeDef.CODE_404;

		// 错误对象
		ApiResponse<Object> domain = new ApiResponse<>(
				apiErrorCodeDef.getCode(), e.getResource());

		return new ResponseEntity<>(domain, HttpStatus.NOT_FOUND);
	}

	@ExceptionHandler(UsernameNotFoundException.class)
	public ResponseEntity<ApiResponse<Void>> usernameNotFoundException(
			UsernameNotFoundException e) {
		log.warn("user name not found : ", e);

		ApiResponse<Void> domain = new ApiResponse<>(
				ErrorCodeDef.CODE_30059004.getCode(),
				ErrorCodeDef.CODE_30059004.getMessage());

		return new ResponseEntity<>(domain, HttpStatus.UNAUTHORIZED);
	}

	@ExceptionHandler(BadCredentialsException.class)
	public ResponseEntity<ApiResponse<Void>> badCredentialsException(
			BadCredentialsException e) {
		log.warn("bad credentials, code:", e);

		ApiResponse<Void> domain = new ApiResponse<>(
				ErrorCodeDef.CODE_30059005.getCode(),
				ErrorCodeDef.CODE_30059005.getMessage());

		return new ResponseEntity<>(domain, HttpStatus.UNAUTHORIZED);
	}

	/**
	 * 表单属性外的验证不通过
	 */
	@ExceptionHandler(BadRequestException.class)
	public ResponseEntity<ApiResponse<Void>> badRequestException(
			BadRequestException e) {

		log.warn("bad request, code:", e);

		// 错误对象
		ApiResponse<Void> domain = new ApiResponse<>(
				ErrorCodeDef.CODE_30006011.getCode(),
				ErrorCodeDef.CODE_30006011.getMessage());

		// 有详细错误抛出
		if (Objects.nonNull(e.getMessage())) {
			List<ErrorEntity> errorEntities = new ArrayList<>();
			ErrorEntity apiError = new ErrorEntity();
			apiError.setCode(e.getMessage());
			setMessage(e, apiError);
			apiError.setParams(e.getParams());
			errorEntities.add(apiError);
			domain.setErrors(errorEntities);
		}

		return new ResponseEntity<>(domain, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(ServiceRequestException.class)
	public ResponseEntity<ApiResponse<Void>> serviceRequestException(
			ServiceRequestException e) {

		log.warn("service request, code:", e);

		// 错误对象
		ApiResponse<Void> domain = new ApiResponse<>(
				ErrorCodeDef.CODE_400.getCode(),
				ErrorCodeDef.CODE_400.getMessage());
		// 有详细错误抛出
		if (Objects.nonNull(e.getMessage())) {
			List<ErrorEntity> errorEntities = new ArrayList<>();
			ErrorEntity apiError = new ErrorEntity();
			apiError.setCode(e.getCode());
			apiError.setMessage(e.getMessage());
			errorEntities.add(apiError);
			domain.setErrors(errorEntities);
		}
		return new ResponseEntity<>(domain, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(IllegalArgumentException.class)
	public ResponseEntity<ApiResponse<Void>> illegalArgumentException(
			IllegalArgumentException e) {
		log.warn("illegal argument, code:", e);

		// 错误对象
		ApiResponse<Void> domain = new ApiResponse<>(
				ErrorCodeDef.CODE_30006011.getCode(),
				ErrorCodeDef.CODE_30006011.getMessage());

		// 有详细错误抛出
		if (Objects.nonNull(e.getMessage())) {
			List<ErrorEntity> errorEntities = new ArrayList<>();
			ErrorEntity apiError = new ErrorEntity();
			apiError.setCode(e.getMessage());
			setMessage(e, apiError);

			errorEntities.add(apiError);
			domain.setErrors(errorEntities);
		}

		return new ResponseEntity<>(domain, HttpStatus.BAD_REQUEST);
	}

	private void setMessage(Exception e, ErrorEntity apiError) {
		ErrorCodeDef from = ErrorCodeDef.from(e.getMessage());
		ErrorCodeDef customFrom = null;
		ErrorCodeDef errorCodeDef = null;
		if (Objects.isNull(from)) {
			customFrom = ErrorCodeDef.from(e.getMessage());
			if (Objects.isNull(customFrom)) {
				errorCodeDef = ErrorCodeDef.from(e.getMessage());
			}
		}
		if (Objects.nonNull(from)) {
			apiError.setMessage(from.getMessage());
			apiError.setI18n(ErrorCodeDef.I18N.apply(e.getMessage()));
		}
		if (Objects.nonNull(customFrom)) {
			apiError.setMessage(customFrom.getMessage());
			apiError.setI18n(ErrorCodeDef.I18N.apply(e.getMessage()));
		}
		if (Objects.nonNull(errorCodeDef)) {
			apiError.setMessage(errorCodeDef.getMessage());
			apiError.setI18n(ErrorCodeDef.I18N.apply(e.getMessage()));
		}
	}

	private void setMessage(ObjectError e, ErrorEntity apiError) {
		ErrorCodeDef from = ErrorCodeDef.from(e.getDefaultMessage());
		ErrorCodeDef customFrom = null;
		ErrorCodeDef errorCodeDef = null;
		if (Objects.isNull(from)) {
			customFrom = ErrorCodeDef.from(e.getDefaultMessage());
			if (Objects.isNull(customFrom)) {
				errorCodeDef = ErrorCodeDef.from(e.getDefaultMessage());
			}
		}
		if (Objects.nonNull(from)) {
			apiError.setMessage(from.getMessage());
			apiError.setI18n(ErrorCodeDef.I18N.apply(e.getDefaultMessage()));
		}
		if (Objects.nonNull(customFrom)) {
			apiError.setMessage(customFrom.getMessage());
			apiError.setI18n(ErrorCodeDef.I18N.apply(e.getDefaultMessage()));
		}
		if (Objects.nonNull(errorCodeDef)) {
			apiError.setMessage(errorCodeDef.getMessage());
			apiError.setI18n(ErrorCodeDef.I18N.apply(e.getDefaultMessage()));
		}
	}

	@ExceptionHandler(IllegalStateException.class)
	public ResponseEntity<ApiResponse<Void>> illegalStateException(
			IllegalStateException e) {
		log.warn("illegal state, code:", e);

		// 错误对象
		ApiResponse<Void> domain = new ApiResponse<>(
				ErrorCodeDef.CODE_30006011.getCode(),
				ErrorCodeDef.CODE_30006011.getMessage());

		// 有详细错误抛出
		if (Objects.nonNull(e.getMessage())) {
			List<ErrorEntity> errorEntities = new ArrayList<>();
			ErrorEntity apiError = new ErrorEntity();
			apiError.setCode(e.getMessage());
			setMessage(e, apiError);
			errorEntities.add(apiError);
			domain.setErrors(errorEntities);
		}

		return new ResponseEntity<>(domain, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(Exception.class)
	public ResponseEntity<ApiResponse<Void>> allOtherException(Exception e) {
		log.error("unknow exception :  ", e);

		// 错误对象, Exception 为未知异常
		ApiResponse<Void> domain = new ApiResponse<>(ErrorCode.CODE_001,
				"Internal Server Error");

		return new ResponseEntity<>(domain, HttpStatus.INTERNAL_SERVER_ERROR);
	}

	/**
	 * 定制请求参数注解验证错误的处理，返回自定义的Response规范, 复写基类的方法
	 */
	@Override
	protected ResponseEntity<Object> handleMethodArgumentNotValid(
			MethodArgumentNotValidException ex, @NonNull HttpHeaders headers,
			@NonNull HttpStatusCode status, @NonNull WebRequest request) {

		List<ErrorEntity> errorEntitys = new ArrayList<>();

		// 获取错误信息
		BindingResult errors = ex.getBindingResult();

		errors.getFieldErrors().forEach(error -> {
			ErrorEntity apiError = new ErrorEntity();
			apiError.setCode(error.getDefaultMessage());
			setMessage(error, apiError);
			if (Objects.nonNull(error.getArguments())
					&& Objects.nonNull(error.getArguments()[0])) {
				if (error.getArguments()[0] instanceof Map) {
					apiError.setParams((Map) error.getArguments()[0]);
				}
			}

			String field = error.getField();

			// 判断错误的field是否来自于数组中的字段 materials[0].name -> name0
			Pattern p = Pattern.compile("(\\[[^\\]]*\\])");
			Matcher m = p.matcher(field);
			if (m.find()) {
				String index = m.group().substring(1, m.group().length() - 1);
				field = StringUtils.substringAfter(field, ".") + index;
			}

			apiError.setField(field);

			errorEntitys.add(apiError);
		});

		// 错误对象
		ApiResponse<Void> domain = new ApiResponse<>(
				ErrorCodeDef.CODE_30006011.getCode(), null, errorEntitys, null);
		return new ResponseEntity<>(domain, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(DynamicsBadRequestException.class)
	public ResponseEntity<ApiResponse<Void>> dynamicsBadRequestException(
			DynamicsBadRequestException e) {

		log.warn("bad request, code:", e);
		// 错误对象
		ApiResponse<Void> domain = new ApiResponse<>(
				ErrorCodeDef.CODE_30006011.getCode(),
				ErrorCodeDef.CODE_30006011.getMessage());

		// 有详细错误抛出
		if (Objects.nonNull(e.getMessage())) {
			List<ErrorEntity> errorEntities = new ArrayList<>();
			ErrorEntity apiError = new ErrorEntity();
			apiError.setCode(e.getMessage());
			ErrorCodeDef from = ErrorCodeDef.from(e.getMessage());
			if (Objects.nonNull(from)) {
				String format = MessageFormat.format(from.getMessage(),
						e.getExt());
				apiError.setMessage(format);
				apiError.setI18n(ErrorCodeDef.I18N.apply(e.getMessage()));
			}

			apiError.setParams(e.getParams());
			errorEntities.add(apiError);
			domain.setErrors(errorEntities);
		}

		return new ResponseEntity<>(domain, HttpStatus.BAD_REQUEST);
	}
}
