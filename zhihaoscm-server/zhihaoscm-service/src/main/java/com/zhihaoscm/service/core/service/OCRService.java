package com.zhihaoscm.service.core.service;

import java.util.Optional;

import com.zhihaoscm.aliyun.ocr.sdk.res.BusinessLicenseInfo;
import com.zhihaoscm.aliyun.ocr.sdk.res.IdCardInfo;
import com.zhihaoscm.domain.bean.entity.File;

/**
 * 基于阿里云OCR的文件管理服务
 */
public interface OCRService {

	/**
	 * 识别营业执照
	 *
	 * @param file
	 * @return
	 */
	Optional<BusinessLicenseInfo> recognizeBusinessLicense(File file);

	/**
	 * 识别身份证
	 * 
	 * @param file
	 * @return
	 */
	Optional<IdCardInfo> recognizeIdCardBackAndFace(File file);
}
