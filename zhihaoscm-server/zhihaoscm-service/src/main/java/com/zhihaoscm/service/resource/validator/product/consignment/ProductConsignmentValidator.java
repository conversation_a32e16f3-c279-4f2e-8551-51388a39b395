package com.zhihaoscm.service.resource.validator.product.consignment;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.domain.bean.entity.Advert;
import com.zhihaoscm.domain.bean.entity.Banner;
import com.zhihaoscm.domain.bean.entity.ProductConsignment;
import com.zhihaoscm.domain.bean.entity.Search;
import com.zhihaoscm.domain.bean.json.ArrayParameterInfo;
import com.zhihaoscm.domain.bean.json.ParameterInfo;
import com.zhihaoscm.domain.meta.biz.AdvertDef;
import com.zhihaoscm.domain.meta.biz.BannerDef;
import com.zhihaoscm.domain.meta.biz.ProductConsignmentDef;
import com.zhihaoscm.domain.meta.biz.SearchDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.AdvertService;
import com.zhihaoscm.service.core.service.BannerService;
import com.zhihaoscm.service.core.service.ProductConsignmentService;
import com.zhihaoscm.service.core.service.SearchService;
import com.zhihaoscm.service.core.service.usercenter.UserService;
import com.zhihaoscm.service.resource.form.product.consignment.ProductConsignmentForm;
import com.zhihaoscm.service.resource.validator.product.type.ProductTypeValidator;

@Component
public class ProductConsignmentValidator {
	@Autowired
	private BannerService bannerService;
	@Autowired
	private SearchService searchService;
	@Autowired
	private AdvertService advertService;
	@Autowired
	private ProductConsignmentService service;
	@Autowired
	private ProductTypeValidator productTypeValidator;
	@Autowired
	private UserService userValidator;

	/**
	 * 校验是否存在
	 *
	 * @param id
	 */
	public ProductConsignment validateExist(String id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30196001));
	}

	/**
	 * 校验新增
	 *
	 * @param form
	 */
	public void validateCreate(ProductConsignmentForm form) {

		// 如果是否含税为含税，则税率必填
		this.validateTaxRate(form.getIsIncludeTax(), form.getTaxRate());
		// 校验品类存在
		productTypeValidator.validateExist(form.getProductTypeId());
		// 校验参数值
		this.validateParameterInfo(form.getParameterInfo());
		// 校验商品标签
		this.validateProductLabel(form.getProductLabel());
		// 校验起订吨数
		this.validateMinOrderTon(form.getMinOrderTon(),
				form.getInventoryQuantity(), form.getMinAdjustmentTon());
		// 结算方式为支付定金，则货款比例、固定金额二选一
		this.validateSettlementMethod(form);
	}

	/**
	 * 校验修改
	 * 
	 * @param form
	 */
	public void validateUpdate(String id, ProductConsignmentForm form) {
		ProductConsignment product = this.validateExist(id);
		// 状态为 下架+待审核 下架+未通过 下架+已下架 才允许修改
		if (!ProductConsignmentDef.State.DOWN_SHELF.match(product.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30196061);
		}
		if (!ProductConsignmentDef.PublishState.NOT_AUDIT
				.match(product.getPublishState())
				&& !ProductConsignmentDef.PublishState.FAIL
						.match(product.getPublishState())
				&& !ProductConsignmentDef.PublishState.DOWN_SHELF
						.match(product.getPublishState())) {
			throw new BadRequestException(ErrorCode.CODE_30196061);
		}
		this.validateCreate(form);
	}

	public ProductConsignment validateAudit(String id) {
		ProductConsignment product = this.validateExist(id);
		// 状态为 上架+待审核 下架+待审核 才允许审核
		if (!ProductConsignmentDef.PublishState.NOT_AUDIT
				.match(product.getPublishState())) {
			throw new BadRequestException(ErrorCode.CODE_30196062);
		}
		return product;
	}

	/**
	 * 校验下架
	 */
	public ProductConsignment validateDownShelf(String id) {
		ProductConsignment product = this.validateExist(id);
		// 状态为 上架+已发布 上架+未通过 才允许下架
		if (!ProductConsignmentDef.State.UP_SHELF.match(product.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30196063);
		}
		if (!ProductConsignmentDef.PublishState.PASS
				.match(product.getPublishState())
				&& !ProductConsignmentDef.PublishState.FAIL
						.match(product.getPublishState())) {
			throw new BadRequestException(ErrorCode.CODE_30196063);
		}
		// todo 绑定广告位、关键词后不允许下架
		List<Banner> bannerList = bannerService.findByTypeAndValue(
				BannerDef.Type.PRODUCT.getCode(),
				String.valueOf(product.getId()));
		List<Search> searchList = searchService.findByTypeAndBusinessId(
				SearchDef.Type.PRODUCT.getCode(),
				String.valueOf(product.getId()));
		List<Advert> advertList = advertService.findByButton(
				AdvertDef.AdvertType.PRODUCT.getCode(),
				String.valueOf(product.getId()));
		if (CollectionUtils.isNotEmpty(bannerList)
				|| CollectionUtils.isNotEmpty(searchList)
				|| CollectionUtils.isNotEmpty(advertList)) {
			throw new BadRequestException(ErrorCode.CODE_30094042);
		}
		return product;
	}

	/**
	 * 校验关闭
	 */
	public ProductConsignment validateClose(String id) {
		ProductConsignment product = this.validateExist(id);
		// 状态为 下架+待审核 下架+未通过 下架+已下架 才允许关闭
		if (!ProductConsignmentDef.State.DOWN_SHELF.match(product.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30196064);
		}
		if (!ProductConsignmentDef.PublishState.NOT_AUDIT
				.match(product.getPublishState())
				&& !ProductConsignmentDef.PublishState.FAIL
						.match(product.getPublishState())
				&& !ProductConsignmentDef.PublishState.DOWN_SHELF
						.match(product.getPublishState())) {
			throw new BadRequestException(ErrorCode.CODE_30196064);
		}
		return product;
	}

	/**
	 * 校验指派
	 *
	 * @param id
	 * @param handlerId
	 */
	public ProductConsignment validateAssign(String id, Long handlerId) {
		ProductConsignment product = this.validateExist(id);
		// 验证操作专员是否存在
		userValidator.validateIsExist(handlerId);
		// 状态为 下架+关闭 不能变更指派
		if (ProductConsignmentDef.State.DOWN_SHELF.match(product.getState())
				&& ProductConsignmentDef.PublishState.CLOSE
						.match(product.getPublishState())) {
			throw new BadRequestException(ErrorCode.CODE_30196066);
		}
		return product;
	}

	/**
	 * 校验税率
	 */
	public void validateTaxRate(Integer isIncludeTax, Integer taxRate) {
		// 如果是否含税为含税，则税率必填
		if (CommonDef.Symbol.YES.match(isIncludeTax)
				&& Objects.isNull(taxRate)) {
			throw new BadRequestException(ErrorCode.CODE_30196009);
		}
	}

	/**
	 * 校验参数值
	 */
	public void validateParameterInfo(ArrayParameterInfo parameterInfo) {
		if (CollectionUtils.isNotEmpty(parameterInfo)) {
			// 最大10个参数
			if (parameterInfo.size() > 10) {
				throw new BadRequestException(ErrorCode.CODE_30196017);
			}
			for (ParameterInfo info : parameterInfo) {
				// 参数值必须 名称和值都有
				if (Objects.isNull(info.getParameterName())
						|| Objects.isNull(info.getParameterValue())) {
					throw new BadRequestException(ErrorCode.CODE_30196018);
				}
				// 参数名称不超过10个字符
				if (info.getParameterName().length() > 10) {
					throw new BadRequestException(ErrorCode.CODE_30196019);
				}
			}
		}
	}

	/**
	 * 校验商品标签
	 */
	public void validateProductLabel(ArrayString productLabel) {
		if (CollectionUtils.isNotEmpty(productLabel)) {
			// 最多新增5个标签
			if (productLabel.size() > 5) {
				throw new BadRequestException(ErrorCode.CODE_30196021);
			}
			for (String label : productLabel) {
				// 单标签最多6个字
				if (label.length() > 6) {
					throw new BadRequestException(ErrorCode.CODE_30196022);
				}
			}
		}
	}

	/**
	 * 校验起订吨数和最小调整吨数
	 * 
	 * @param minOrderTon
	 *            起订吨数
	 * @param inventoryQuantity
	 *            库存量
	 * @param minAdjustmentTon
	 *            最小调整吨数
	 */
	private void validateMinOrderTon(Integer minOrderTon,
			Integer inventoryQuantity, Integer minAdjustmentTon) {
		// 起订吨数不能超过库存量
		if (minOrderTon > inventoryQuantity) {
			throw new BadRequestException(ErrorCode.CODE_30196037);
		}
		// 最小调整吨数不能小于1，且不能超过起订吨数（寄售）
		if (minAdjustmentTon < 1 || minAdjustmentTon > minOrderTon) {
			throw new BadRequestException(ErrorCode.CODE_30196039);
		}
	}

	/**
	 * 校验下单方式
	 */
	private void validateSettlementMethod(ProductConsignmentForm form) {
		Integer settlementMethod = form.getSettlementMethod();
		Integer depositPaymentMethod = form.getDepositPaymentMethod();
		BigDecimal paymentRatio = form.getPaymentRatio();
		BigDecimal fixedAmount = form.getFixedAmount();
		if (ProductConsignmentDef.SettlementMethod.PAY_DEPOSIT
				.match(settlementMethod)) {
			// 选择支付定金时，需要选择货款比例或者固定金额
			if (Objects.isNull(depositPaymentMethod)) {
				throw new BadRequestException(ErrorCode.CODE_30196043);
			}
			if (ProductConsignmentDef.DepositPaymentMethod.PAYMENT_RATIO
					.match(depositPaymentMethod)) {
				if (Objects.isNull(paymentRatio)) {
					throw new BadRequestException(ErrorCode.CODE_30196045);
				}
				// 货款比例是大于0，小于100的整数（1-99）
				if (paymentRatio.compareTo(BigDecimal.valueOf(1)) < 0
						|| paymentRatio.compareTo(BigDecimal.valueOf(99)) > 0
						|| paymentRatio.stripTrailingZeros().scale() > 0) {
					throw new BadRequestException(ErrorCode.CODE_30196046);
				}
			} else {
				if (Objects.isNull(fixedAmount)) {
					throw new BadRequestException(ErrorCode.CODE_30196047);
				}
				// 固定金额大于0
				if (fixedAmount.compareTo(BigDecimal.valueOf(1)) < 0
						|| fixedAmount.stripTrailingZeros().scale() > 0) {
					throw new BadRequestException(ErrorCode.CODE_30196048);
				}
				// 固定金额小于 1 000 000 的整数
				if (fixedAmount.compareTo(BigDecimal.valueOf(999999)) > 0) {
					throw new BadRequestException(ErrorCode.CODE_30196049);
				}
			}
		}
	}
}
