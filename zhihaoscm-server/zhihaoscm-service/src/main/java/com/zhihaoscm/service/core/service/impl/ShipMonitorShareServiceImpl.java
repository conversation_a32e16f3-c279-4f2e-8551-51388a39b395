package com.zhihaoscm.service.core.service.impl;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.entity.ShipMonitorShare;
import com.zhihaoscm.domain.bean.json.notice.Messages;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.ShipMonitorShareVo;
import com.zhihaoscm.domain.meta.biz.AppTypeDef;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.UserMessageConstants;
import com.zhihaoscm.domain.meta.biz.UserMessageDef;
import com.zhihaoscm.domain.meta.notice.AppType;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.service.core.mapper.ShipMonitorShareMapper;
import com.zhihaoscm.service.core.service.DeviceService;
import com.zhihaoscm.service.core.service.MessageService;
import com.zhihaoscm.service.core.service.ShipMonitorShareService;
import com.zhihaoscm.service.core.service.ShipService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 船舶监控分享 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@Service
@Slf4j
public class ShipMonitorShareServiceImpl extends
		MpLongIdBaseServiceImpl<ShipMonitorShare, ShipMonitorShareMapper>
		implements ShipMonitorShareService {

	public ShipMonitorShareServiceImpl(ShipMonitorShareMapper repository) {
		super(repository);
	}

	@Autowired
	private DeviceService deviceService;
	@Autowired
	private ShipService shipService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private MessageService messageService;

	@Override
	public Optional<ShipMonitorShareVo> findVoByCustomerIdAndShipId(
			Long customerId, String shipId) {
		LambdaQueryWrapper<ShipMonitorShare> queryWrapper = Wrappers
				.lambdaQuery(ShipMonitorShare.class)
				.eq(ShipMonitorShare::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(ShipMonitorShare::getSharerId, customerId)
				.eq(ShipMonitorShare::getShipId, shipId);
		// 查询出分享有效期未过期的记录
		queryWrapper.and(
				wrapper -> wrapper.isNull(ShipMonitorShare::getMonitorValidity)
						.or().ge(ShipMonitorShare::getMonitorValidity,
								LocalDateTime.now().withNano(0)));
		return Optional.ofNullable(repository.selectOne(queryWrapper))
				.map(this::packVo);
	}

	@Override
	public List<ShipMonitorShare> findByCustomerId(Long customerId) {
		LambdaQueryWrapper<ShipMonitorShare> queryWrapper = Wrappers
				.lambdaQuery(ShipMonitorShare.class)
				.eq(ShipMonitorShare::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(ShipMonitorShare::getSharerId, customerId);
		// 查询出分享有效期未过期的船舶监控记录
		queryWrapper.and(
				wrapper -> wrapper.isNull(ShipMonitorShare::getMonitorValidity)
						.or().ge(ShipMonitorShare::getMonitorValidity,
								LocalDateTime.now().withNano(0)));
		// 默认按照生效时间（即创建时间）降序排列
		queryWrapper.orderByDesc(ShipMonitorShare::getCreatedTime);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<ShipMonitorShare> findByShipId(String shipId) {
		LambdaQueryWrapper<ShipMonitorShare> queryWrapper = Wrappers
				.lambdaQuery(ShipMonitorShare.class)
				.eq(ShipMonitorShare::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(ShipMonitorShare::getShipId, shipId);
		// 查询出分享有效期未过期的记录
		queryWrapper.and(
				wrapper -> wrapper.isNull(ShipMonitorShare::getMonitorValidity)
						.or().ge(ShipMonitorShare::getMonitorValidity,
								LocalDateTime.now().withNano(0)));
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<ShipMonitorShareVo> findByShipIds(String shipId) {
		LambdaQueryWrapper<ShipMonitorShare> queryWrapper = Wrappers
				.lambdaQuery(ShipMonitorShare.class)
				.eq(ShipMonitorShare::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(ShipMonitorShare::getShipId, shipId);
		return this.packVo(repository.selectList(queryWrapper));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void create(List<ShipMonitorShare> shipMonitorShares) {
		if (CollectionUtils.isNotEmpty(shipMonitorShares)) {
			// 先删除同一个船舶的监控分享记录，再进行重新新增
			this.deleteByShipId(shipMonitorShares.get(0).getShipId());
			super.batchCreate(shipMonitorShares);
			// 发送站内消息 被分享者会收到站内消息，${client}邀请您查看船舶(${ship_name}) 监控
			shipMonitorShares.forEach(share -> {
				Optional<Ship> op = shipService.findOne(share.getShipId());
				op.ifPresent(ship -> {
					Optional<Customer> sharerOp = customerService
							.findOne(share.getCreatedBy());
					String shareName = sharerOp.isPresent()
							? !StringUtil.isNullOrEmpty(
									sharerOp.get().getInstitutionName())
											? sharerOp.get()
													.getInstitutionName()
											: sharerOp.get().getRealName()
							: CustomerDef.Role.CARRIER.getName();
					messageService.sendNotice(UserMessage.builder()
							.type(UserMessageDef.MessageType.OTHER.getCode())
							.title(MessageFormat.format(
									UserMessageConstants.SHARE_MONITOR_TEMPLATE,
									shareName, ship.getName()))
							.receiptors(List
									.of(String.valueOf(share.getSharerId())))
							.url(UserMessageConstants.SHIP_MONITOR_DETAIL_PAGE)
							.detailId(String.valueOf(share.getId()))
							.initiator(UserMessageDef.BusinessInitiator.receipt
									.getCode())
							.role(AppTypeDef.AppType.CHUAN_WU.getCode())
							.build());
					// APP推送
					messageService.sendNotice(Messages.builder()
							.type(UserMessageDef.MessageType.OTHER.getCode())
							.messageTypes(
									List.of(SendType.PUSH_MESSAGE.getCode()))
							.content(MessageFormat.format(
									UserMessageConstants.SHARE_MONITOR_TEMPLATE,
									shareName, ship.getName()))
							.appTypes(List.of(AppType.SHIP))
							.title(UserMessageConstants.MONITOR_TITLE)
							.receiptors(List
									.of(String.valueOf(share.getSharerId())))
							.moduleType(
									UserMessageConstants.SHIP_MONITOR_DETAIL_PAGE)
							.bizNo(share.getId().toString()).build());

				});
			});
		}

	}

	@Override
	public void deleteByShipId(String shipId) {
		LambdaUpdateWrapper<ShipMonitorShare> wrapper = Wrappers
				.lambdaUpdate(ShipMonitorShare.class);
		wrapper.eq(ShipMonitorShare::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.eq(ShipMonitorShare::getShipId, shipId);
		repository.delete(wrapper);
	}

	/**
	 * 组装数据
	 *
	 * @param shipMonitorShare
	 * @return
	 */
	private ShipMonitorShareVo packVo(ShipMonitorShare shipMonitorShare) {
		ShipMonitorShareVo shipMonitorShareVo = new ShipMonitorShareVo();
		shipMonitorShareVo.setShipMonitorShare(shipMonitorShare);

		if (Objects.nonNull(shipMonitorShare.getShipId())) {
			// 查询设备信息
			deviceService.findByMasterId(shipMonitorShare.getShipId())
					.ifPresent(shipMonitorShareVo::setDevice);
			// 查询船舶信息
			shipService.findOne(shipMonitorShare.getShipId())
					.ifPresent(shipMonitorShareVo::setShip);
		}
		return shipMonitorShareVo;
	}

	/**
	 * 封装船舶分享记录信息
	 *
	 * @param list
	 * @return
	 */
	private List<ShipMonitorShareVo> packVo(List<ShipMonitorShare> list) {
		if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
			return List.of();
		}
		List<ShipMonitorShareVo> shipMonitorShareVoList = new ArrayList<>();
		for (ShipMonitorShare shipMonitorShare : list) {
			ShipMonitorShareVo shipMonitorShareVo = new ShipMonitorShareVo();
			shipMonitorShareVo.setShipMonitorShare(shipMonitorShare);
			if (Objects.nonNull(shipMonitorShare.getSharerId())) {
				// 查询被分享人的用户信息
				customerService.findOne(shipMonitorShare.getSharerId())
						.ifPresent(shipMonitorShareVo::setCustomer);
			}
			shipMonitorShareVoList.add(shipMonitorShareVo);
		}
		return shipMonitorShareVoList;
	}
}
