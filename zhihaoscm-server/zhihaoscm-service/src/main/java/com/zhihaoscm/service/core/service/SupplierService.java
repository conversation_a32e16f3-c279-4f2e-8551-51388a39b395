package com.zhihaoscm.service.core.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.Supplier;

/**
 * @author: xiaYZ 2025/7/31
 * @version: 1.0
 */
public interface SupplierService extends MpLongIdBaseService<Supplier> {

    /**
     * 分页查询供应商管理数据
     *
     * @param page           页
     * @param size           大小
     * @param supplierName   供应商名称
     * @param groupCompanyId 组公司id
     * @param type           类型
     * @return {@link Page }<{@link Supplier }>
     * <AUTHOR>
     * create time: 2025/07/31
     **/
    Page<Supplier> paging(Integer page, Integer size, String supplierName,String  groupCompanyId,Integer type);

}
