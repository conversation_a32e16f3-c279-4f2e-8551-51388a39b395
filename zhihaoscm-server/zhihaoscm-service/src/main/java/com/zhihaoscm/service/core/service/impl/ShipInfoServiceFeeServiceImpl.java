package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.PlatformAdvancePaymentRecord;
import com.zhihaoscm.domain.bean.entity.ShipInfoServiceFee;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.json.CustomerJsonInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.Application.ApplicationTransfer;
import com.zhihaoscm.domain.bean.json.notice.Messages;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.ShipInfoServiceFeeCountVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.ApplicationDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.notice.AppType;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.service.aop.history.HistoryContext;
import com.zhihaoscm.service.core.mapper.ShipInfoServiceFeeMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

/**
 * <p>
 * 船务信息服务费 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Service
public class ShipInfoServiceFeeServiceImpl extends
		MpStringIdBaseServiceImpl<ShipInfoServiceFee, ShipInfoServiceFeeMapper>
		implements ShipInfoServiceFeeService {

	@Lazy
	@Autowired
	private ShipService shipService;
	@Lazy
	@Autowired
	private CustomerService customerService;
	@Lazy
	@Autowired
	private TransportOrderShipService transportOrderShipService;
	@Autowired
	private PlatformAdvancePaymentRecordService platformAdvancePaymentRecordService;
	@Autowired
	private UserService userService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private MqUtil mqUtil;

	public ShipInfoServiceFeeServiceImpl(ShipInfoServiceFeeMapper repository) {
		super(repository);
	}

	@Override
	public Page<ShipInfoServiceFee> paging(Integer page, Integer size,
			String idOrCode, String customerInfo, String shipName,
			Integer state, Integer type, String sortKey, String sortOrder) {
		LambdaQueryWrapper<ShipInfoServiceFee> queryWrapper = Wrappers
				.lambdaQuery(ShipInfoServiceFee.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Objects.nonNull(state), ShipInfoServiceFee::getState,
				state);
		queryWrapper.and(StringUtils.isNotBlank(idOrCode),
				wrapper -> wrapper.eq(ShipInfoServiceFee::getId, idOrCode).or()
						.eq(ShipInfoServiceFee::getRelationCode, idOrCode));
		queryWrapper.eq(Objects.nonNull(type), ShipInfoServiceFee::getType,
				type);
		queryWrapper.apply(StringUtils.isNotBlank(customerInfo),
				"(customer_info -> '$.institutionName' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_info -> '$.realName' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_info -> '$.mobile' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_info -> '$.code' LIKE CONCAT('%',{0},'%'))",
				customerInfo);

		queryWrapper.and(StringUtils.isNotBlank(shipName),
				wrapper -> wrapper
						.like(ShipInfoServiceFee::getShipName, shipName).or()
						.like(ShipInfoServiceFee::getShipCnName, shipName));
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			queryWrapper.orderByDesc(ShipInfoServiceFee::getCreatedTime);
		}
		return repository.selectPage(new Page<>(page, size), queryWrapper);
	}

	@Override
	public Page<ShipInfoServiceFee> customPaging(Integer page, Integer size,
			String idOrCode, Integer state, Integer type, Long customerId,
			String sortKey, String sortOrder) {
		LambdaQueryWrapper<ShipInfoServiceFee> queryWrapper = Wrappers
				.lambdaQuery(ShipInfoServiceFee.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Objects.nonNull(state), ShipInfoServiceFee::getState,
				state);
		queryWrapper.and(StringUtils.isNotBlank(idOrCode),
				wrapper -> wrapper.eq(ShipInfoServiceFee::getId, idOrCode).or()
						.eq(ShipInfoServiceFee::getRelationCode, idOrCode));

		queryWrapper.eq(Objects.nonNull(type), ShipInfoServiceFee::getType,
				type);
		queryWrapper.eq(Objects.nonNull(customerId),
				ShipInfoServiceFee::getCustomerId, customerId);

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			queryWrapper.orderByDesc(ShipInfoServiceFee::getCreatedTime);
		}
		return repository.selectPage(new Page<>(page, size), queryWrapper);
	}

	@Override
	public List<ShipInfoServiceFee> findByDateScope(LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<ShipInfoServiceFee> queryWrapper = Wrappers
				.lambdaQuery(ShipInfoServiceFee.class);
		this.filterDeleted(queryWrapper);
		// 已支付状态且支付时间在时间范围内
		queryWrapper
				.eq(ShipInfoServiceFee::getState,
						ShipInfoServiceFeeDef.State.SUCCESS.getCode())
				.ge(Objects.nonNull(beginTime), ShipInfoServiceFee::getPayTime,
						beginTime)
				.le(Objects.nonNull(endTime), ShipInfoServiceFee::getPayTime,
						endTime);
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<BigDecimal> findAmountByType(Integer type,
			LocalDateTime beginTime, LocalDateTime endTime) {
		LambdaQueryWrapper<ShipInfoServiceFee> queryWrapper = Wrappers
				.query(ShipInfoServiceFee.class).select("SUM(amount) amount")
				.lambda();
		queryWrapper
				.eq(ShipInfoServiceFee::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Objects.nonNull(type), ShipInfoServiceFee::getType, type)
				.ge(Objects.nonNull(beginTime), ShipInfoServiceFee::getPayTime,
						beginTime)
				.le(Objects.nonNull(endTime), ShipInfoServiceFee::getPayTime,
						endTime);
		ShipInfoServiceFee shipInfoServiceFee = repository
				.selectOne(queryWrapper);
		return Objects.isNull(shipInfoServiceFee) ? Optional.of(BigDecimal.ZERO)
				: Optional.ofNullable(shipInfoServiceFee.getAmount());
	}

	@Override
	public Optional<ShipInfoServiceFee> findByRelationCodeAndType(
			String relationCode, Integer type) {
		LambdaQueryWrapper<ShipInfoServiceFee> queryWrapper = Wrappers
				.lambdaQuery(ShipInfoServiceFee.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(ShipInfoServiceFee::getRelationCode, relationCode);
		queryWrapper.eq(ShipInfoServiceFee::getType, type);
		return Optional.ofNullable(repository.selectOne(queryWrapper));
	}

	@Override
	public ShipInfoServiceFeeCountVo staticsShipInfoServiceFee(
			Boolean hasFull) {
		ShipInfoServiceFeeCountVo shipInfoServiceFeeCountVo = new ShipInfoServiceFeeCountVo();
		LambdaQueryWrapper<ShipInfoServiceFee> queryWrapper = Wrappers
				.lambdaQuery(ShipInfoServiceFee.class);
		if (hasFull) {
			// 统计待确认船务信息服务费
			queryWrapper.eq(ShipInfoServiceFee::getState,
					ShipInfoServiceFeeDef.State.UN_CONFIRM.getCode());
			queryWrapper.eq(ShipInfoServiceFee::getDel,
					CommonDef.Symbol.NO.getCode());
			Long infoService = repository.selectCount(queryWrapper);
			shipInfoServiceFeeCountVo.setInfoService(infoService);
			queryWrapper.clear();
		} else {
			shipInfoServiceFeeCountVo.setInfoService(0L);
		}
		return shipInfoServiceFeeCountVo;
	}

	@FileId
	@Override
	public ShipInfoServiceFee create(ShipInfoServiceFee resource) {
		return super.create(resource);
	}

	/**
	 * 生成船务信息服务费
	 *
	 * @param transportOrderShip
	 * @param type
	 * @param payType
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void pay(TransportOrderShip transportOrderShip, Integer type,
			Long payFileId, Integer payType) {
		ShipInfoServiceFee serviceFee = new ShipInfoServiceFee();
		serviceFee.setShipId(transportOrderShip.getShipId());
		// 设置支付类型
		serviceFee.setPayType(payType);
		shipService.findOne(transportOrderShip.getShipId()).ifPresent(ship -> {
			serviceFee.setShipName(ship.getName());
			serviceFee.setShipCnName(ship.getCnname());
		});
		// 设置类型
		serviceFee.setType(type);
		serviceFee.setPayFileId(payFileId);
		serviceFee.setShippingRequirementId(transportOrderShip.getSrpId());
		serviceFee.setRelationCode(transportOrderShip.getId());
		serviceFee.setId(AutoCodeDef.CREATE_AUTO_CODE.apply(
				SpringUtil.getBean(StringRedisClient.class),
				AutoCodeDef.BusinessCode.SHIP_INFO_SERVICE_FEE.getCode(),
				RedisKeys.Cache.SHIP_INFO_SERVICE_FEE, "0" + type, 5,
				AutoCodeDef.DATE_TYPE.yyMM));
		switch (ShipInfoServiceFeeDef.Type.from(type)) {
			case OWNER -> {
				// 货主
				serviceFee.setCustomerId(transportOrderShip.getOwnerId());
				customerService.findOne(transportOrderShip.getOwnerId())
						.ifPresent(customer -> packCustomerInfo(serviceFee,
								customer));
				serviceFee.setAmount(
						transportOrderShip.getOwnerShipInfoServiceFee());
				// 船运单船主服务费支付状态为确认中
				transportOrderShip.setOwnerShipInfoServiceFeeState(
						ShipInfoServiceFeeDef.State.UN_CONFIRM.getCode());
				// 支付类型
				transportOrderShip.setOwnerShipInfoServiceFeePayType(payType);
			}

			case CAPTAIN -> {
				// 船主
				serviceFee.setCustomerId(transportOrderShip.getCaptainId());
				customerService.findOne(transportOrderShip.getCaptainId())
						.ifPresent(customer -> packCustomerInfo(serviceFee,
								customer));
				serviceFee.setAmount(
						transportOrderShip.getCaptainShipInfoServiceFee());
				// 船运单货主服务费支付状态为确认中
				transportOrderShip.setCaptainShipInfoServiceFeeState(
						ShipInfoServiceFeeDef.State.UN_CONFIRM.getCode());
				// 支付类型
				transportOrderShip.setCaptainShipInfoServiceFeePayType(payType);
			}

		}
		switch (ShipInfoServiceFeeDef.PayType.from(payType)) {
			case OFFLINE -> {
				serviceFee.setState(
						ShipInfoServiceFeeDef.State.UN_CONFIRM.getCode());
				transportOrderShipService
						.updateAllProperties(transportOrderShip);
				messageService.sendNotice(WxwMessage.builder()
						.receiptors(userService.findUsersByPermission(
								AdminPermissionDef.SHIP_ORDER_SERVICE_FEE_W,
								null).stream()
								.map(user -> String.valueOf(user.getId()))
								.toList())
						.url("/expenseManagement/shippingDeal/shippingServiceFee/detail/"
								.concat(serviceFee.getId()))
						.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
						.operationModule(
								WxwDef.NoticeOperationModule.SHIP_INFO_SERVICE_FEE
										.getDesc())
						.desc("待确认").keyword(serviceFee.getId())
						.content(StringUtils.EMPTY).build());
			}
			case ONLINE -> {
				serviceFee.setPayTime(LocalDateTime.now());
				this.check(serviceFee);
			}
		}

		this.create(serviceFee);
	}

	@History(success = HistoryDef.HELP_PAY_THE_INFORMATION_SERVICE_FEE, bizNo = "{{#transportOrderShip.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, kvParam = {
			@History.KeyValuePair(key = "#type#", value = "{{#type!=null?#type:''}}") })
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void adminPay(TransportOrderShip transportOrderShip, Integer type,
			Long payFileId, Integer payType) {
		this.pay(transportOrderShip, type, payFileId, payType);
		HistoryContext.putVariable("type",
				ShipInfoServiceFeeDef.Type.from(type).getName());
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void confirm(ShipInfoServiceFee serviceFee) {
		// 判断另一方是否支付 并且生成平台收款
		this.check(serviceFee);
		// 获取当前登录用户id
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (Objects.nonNull(context)) {
			Long userId = context.getUserId();
			serviceFee.setConfirmBy(userId);
			userService.findOne(userId).ifPresent(user -> {
				serviceFee.setConfirmBy(user.getId());
				serviceFee.setConfirmTime(LocalDateTime.now());
				serviceFee.setConfirmByName(user.getName());
			});
		}
		super.updateAllProperties(serviceFee);
		// 船主/货主支付信息服务费后，后台确认后，给货主、承运商发送短信和站内消息
		transportOrderShipService.findOne(serviceFee.getRelationCode())
				.ifPresent(transportOrderShip -> {
					if (ShipInfoServiceFeeDef.Type.OWNER
							.match(serviceFee.getType())) {

						// 给货主发信息服务费确认提醒
						messageService.sendNotice(Messages.builder()
								.messageTypes(
										List.of(SendType.USERMESSAGE.getCode()))
								.type(UserMessageDef.MessageType.TRANSFORM
										.getCode())
								.title(MessageFormat.format(
										UserMessageConstants.INFO_SERVICE_FEE_CONFIRMATION_TEMPLATE,
										transportOrderShip.getId()))
								.receiptors(List.of(String.valueOf(
										transportOrderShip.getOwnerId())))
								.url(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
								.detailId(String
										.valueOf(transportOrderShip.getId()))
								.initiator(
										UserMessageDef.BusinessInitiator.initiate
												.getCode())
								.mobile(transportOrderShip.getOwnerEnterprise()
										.getMobile())
								.build());

						// 发送app推送
						messageService.sendNotice(Messages.builder()
								.messageTypes(List
										.of(SendType.PUSH_MESSAGE.getCode()))
								.type(UserMessageDef.MessageType.TRANSFORM
										.getCode())
								.title(UserMessageConstants.TRANSPORT_TITLE)
								.content(MessageFormat.format(
										UserMessageConstants.INFO_SERVICE_FEE_CONFIRMATION_TEMPLATE,
										transportOrderShip.getId()))
								.appTypes(
										List.of(AppType.LIANYUN, AppType.SHIP))
								.bizNo(String
										.valueOf(transportOrderShip.getId()))
								.moduleType(
										UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
								.receiptors(List.of(String.valueOf(
										transportOrderShip.getOwnerId())))
								.build());
					} else {

						// 给船主发信息服务费确认提醒
						messageService.sendNotice(Messages.builder()
								.messageTypes(
										List.of(SendType.USERMESSAGE.getCode()))
								.type(UserMessageDef.MessageType.TRANSFORM
										.getCode())
								.title(MessageFormat.format(
										UserMessageConstants.INFO_SERVICE_FEE_CONFIRMATION_TEMPLATE,
										transportOrderShip.getId()))
								.receiptors(List.of(String.valueOf(
										transportOrderShip.getCaptainId())))
								.url(UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
								.role(AppTypeDef.AppType.CHUAN_WU.getCode())
								.detailId(String
										.valueOf(transportOrderShip.getId()))
								.initiator(
										UserMessageDef.BusinessInitiator.initiate
												.getCode())
								.mobile(transportOrderShip
										.getCaptainEnterprise().getMobile())
								.build());

						// 发送app推送
						messageService.sendNotice(Messages.builder()
								.messageTypes(List
										.of(SendType.PUSH_MESSAGE.getCode()))
								.type(UserMessageDef.MessageType.TRANSFORM
										.getCode())
								.title(UserMessageConstants.TRANSPORT_TITLE)
								.content(MessageFormat.format(
										UserMessageConstants.INFO_SERVICE_FEE_CONFIRMATION_TEMPLATE,
										transportOrderShip.getId()))
								.appTypes(
										List.of(AppType.LIANYUN, AppType.SHIP))
								.bizNo(String
										.valueOf(transportOrderShip.getId()))
								.moduleType(
										UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
								.receiptors(List.of(String.valueOf(
										transportOrderShip.getCaptainId())))
								.build());
					}
				});
	}

	/**
	 * 判断另一方是否支付 并且生成平台收款
	 *
	 * @param serviceFee
	 */
	private void check(ShipInfoServiceFee serviceFee) {
		switch (ShipInfoServiceFeeDef.Type.from(serviceFee.getType())) {
			case CAPTAIN ->
				// 判断货主是否支付
				transportOrderShipService.findOne(serviceFee.getRelationCode())
						.ifPresent(transportOrderShip -> {
							if (ShipInfoServiceFeeDef.State.SUCCESS
									.match(transportOrderShip
											.getOwnerShipInfoServiceFeeState())) {
								// 修改船运单状态
								transportOrderShip.setState(
										TransportOrderShipDef.State.TO_BE_LOADED
												.getCode());
								// 船主与货主的信息服务费不均为0，管理后台确认完信息服务费后 上游专员接收信息
								if (Objects.nonNull(transportOrderShip
										.getUpstreamHandlerId())) {
									messageService.sendNotice(WxwMessage
											.builder()
											.receiptors(List.of(String
													.valueOf(transportOrderShip
															.getUpstreamHandlerId())))
											.url("/logistics/shippingNeed/waybillManagementInfo/"
													.concat(transportOrderShip
															.getId()))
											.prefix(WxwDef.NoticePrefix.YOU_HAVE
													.getDesc())
											.operationModule(
													WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
															.getDesc())
											.desc("待装货")
											.keyword(transportOrderShip.getId())
											.content(StringUtils.EMPTY)
											.build());
								}
							}
							// 支付类型
							transportOrderShip
									.setCaptainShipInfoServiceFeePayType(
											serviceFee.getPayType());
							transportOrderShip
									.setCaptainShipInfoServiceFeeState(
											ShipInfoServiceFeeDef.State.SUCCESS
													.getCode());
							transportOrderShipService
									.updateAllProperties(transportOrderShip);
							if (Objects
									.nonNull(transportOrderShip.getAppId())) {
								// 船主线上支付完信息服务费或者管理后台确认线下支付的船务信息服务费
								handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
										.getCode(),
										ShippingCallbackDef.PushType.PAY_OR_CONFIRM_SERVICE
												.getCode(),
										transportOrderShip.getId());
							}
						});
			case OWNER ->
				// 判断船主是否支付
				transportOrderShipService.findOne(serviceFee.getRelationCode())
						.ifPresent(transportOrderShip -> {
							if (ShipInfoServiceFeeDef.State.SUCCESS
									.match(transportOrderShip
											.getCaptainShipInfoServiceFeeState())) {
								// 修改船运单状态
								transportOrderShip.setState(
										TransportOrderShipDef.State.TO_BE_LOADED
												.getCode());
								// 船主与货主的信息服务费不均为0，管理后台确认完信息服务费后 上游专员接收信息
								if (Objects.nonNull(transportOrderShip
										.getUpstreamHandlerId())) {
									messageService.sendNotice(WxwMessage
											.builder()
											.receiptors(List.of(String
													.valueOf(transportOrderShip
															.getUpstreamHandlerId())))
											.url("/logistics/shippingNeed/waybillManagementInfo/"
													.concat(transportOrderShip
															.getId()))
											.prefix(WxwDef.NoticePrefix.YOU_HAVE
													.getDesc())
											.operationModule(
													WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
															.getDesc())
											.desc("待装货")
											.keyword(transportOrderShip.getId())
											.content(StringUtils.EMPTY)
											.build());
								}
							}
							// 支付类型
							transportOrderShip
									.setOwnerShipInfoServiceFeePayType(
											serviceFee.getPayType());
							transportOrderShip.setOwnerShipInfoServiceFeeState(
									ShipInfoServiceFeeDef.State.SUCCESS
											.getCode());
							transportOrderShipService
									.updateAllProperties(transportOrderShip);
						});
		}

		// 生产平台预付款
		platformAdvancePaymentRecordService
				.create(buildPlatformAdvancePaymentRecord(serviceFee));
		// 设置状态为已支付
		serviceFee.setState(ShipInfoServiceFeeDef.State.SUCCESS.getCode());
	}

	/**
	 * 组装客户信息
	 *
	 * @param serviceFee
	 * @param customer
	 */
	private void packCustomerInfo(ShipInfoServiceFee serviceFee,
			Customer customer) {
		CustomerJsonInfo customerJsonInfo = new CustomerJsonInfo();
		customerJsonInfo.setId(customer.getId());
		customerJsonInfo.setCode(customer.getCode());
		customerJsonInfo.setMobile(customer.getMobile());
		customerJsonInfo.setRealName(customer.getRealName());
		customerJsonInfo.setInstitutionName(customer.getInstitutionName());
		serviceFee.setCustomerInfo(customerJsonInfo);
	}

	/**
	 * 构建平台预付款
	 *
	 * @param serviceFee
	 * @return
	 */
	private PlatformAdvancePaymentRecord buildPlatformAdvancePaymentRecord(
			ShipInfoServiceFee serviceFee) {
		// 生产平台预付款
		PlatformAdvancePaymentRecord platformAdvancePaymentRecord = new PlatformAdvancePaymentRecord();
		customerService.findOne(serviceFee.getCustomerId())
				.ifPresent(customer -> {
					Enterprise enterprise = new Enterprise();
					enterprise.setName(customer.getRealName());
					enterprise.setMobile(customer.getMobile());
					enterprise.setLegalRepresentative(
							customer.getLegalRepresentative());
					enterprise.setUnifiedSocialCreditCode(
							customer.getUnifiedSocialCreditCode());
					enterprise.setCode(customer.getCode());
					platformAdvancePaymentRecord.setEnterprise(enterprise);
				});

		switch (ShipInfoServiceFeeDef.Type.from(serviceFee.getType())) {
			case OWNER -> {
				platformAdvancePaymentRecord.setId(AutoCodeDef.CREATE_AUTO_CODE
						.apply(SpringUtil.getBean(StringRedisClient.class),
								AutoCodeDef.BusinessCode.PLATFORM_ADVANCE_PAYMENT
										.getCode(),
								RedisKeys.Cache.PLATFORM_ADVANCE_PAYMENT, "02",
								5, AutoCodeDef.DATE_TYPE.yyMM));
				platformAdvancePaymentRecord.setSource(
						PlatformAdvancePaymentRecordDef.Source.OWNER_SHIP_INFO_SERVICE_FEE
								.getCode());
			}
			case CAPTAIN -> {
				platformAdvancePaymentRecord.setId(AutoCodeDef.CREATE_AUTO_CODE
						.apply(SpringUtil.getBean(StringRedisClient.class),
								AutoCodeDef.BusinessCode.PLATFORM_ADVANCE_PAYMENT
										.getCode(),
								RedisKeys.Cache.PLATFORM_ADVANCE_PAYMENT, "03",
								5, AutoCodeDef.DATE_TYPE.yyMM));
				platformAdvancePaymentRecord.setSource(
						PlatformAdvancePaymentRecordDef.Source.CAPTAIN_SHIP_INFO_SERVICE_FEE
								.getCode());
			}
		}

		platformAdvancePaymentRecord.setRelationCode(serviceFee.getId());
		platformAdvancePaymentRecord.setCollectionTime(serviceFee.getPayTime());
		// 应收金额
		platformAdvancePaymentRecord
				.setReceivableAmount(serviceFee.getAmount());

		switch (ShipInfoServiceFeeDef.PayType.from(serviceFee.getPayType())) {

			case ONLINE -> {
				platformAdvancePaymentRecord.setCollectionMethod(
						PlatformAdvancePaymentRecordDef.CollectionMethod.WECHAT
								.getCode());
				platformAdvancePaymentRecord.setRate(
						new BigDecimal(PlatformAdvancePaymentRecordDef.RATE));
				// 计算实收金额 付款金额 * 0.006 四舍五入保留两位小数 不满足0.01没有手续费
				BigDecimal rate = platformAdvancePaymentRecord.getRate()
						.divide(BigDecimal.valueOf(100), 3, RoundingMode.DOWN);
				// 手续费
				BigDecimal serviceCharge = platformAdvancePaymentRecord
						.getReceivableAmount().multiply(rate)
						.setScale(2, RoundingMode.HALF_UP);
				if (serviceCharge.compareTo(
						PlatformAdvancePaymentRecordDef.MIN_SERVICE_CHARGE) < 0) {
					platformAdvancePaymentRecord.setPaidInAmount(
							platformAdvancePaymentRecord.getReceivableAmount());
				} else {
					platformAdvancePaymentRecord.setPaidInAmount(
							platformAdvancePaymentRecord.getReceivableAmount()
									.subtract(serviceCharge));
				}
			}
			case OFFLINE -> {
				platformAdvancePaymentRecord.setCollectionMethod(
						PlatformAdvancePaymentRecordDef.CollectionMethod.OFFLINE
								.getCode());
				platformAdvancePaymentRecord.setRate(BigDecimal.ZERO);
				platformAdvancePaymentRecord
						.setPaidInAmount(serviceFee.getAmount());
			}
		}
		return platformAdvancePaymentRecord;
	}

	public void handle(Integer type, Integer action, String id) {
		if (Objects.isNull(type) || Objects.isNull(action)
				|| Objects.isNull(id)) {
			return;
		}
		ApplicationTransfer transfer = new ApplicationTransfer(type, action,
				id);
		mqUtil.asyncSend(MqMessage.builder()
				.topic(TopicDef.APPLICATION_CONNECTOR)
				.message(MessageBuilder
						.withPayload(JsonUtils.objectToJson(transfer)).build())
				.build());
	}
}
