package com.zhihaoscm.service.resource.form.sample.apply;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.domain.bean.entity.SampleApply;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SampleApplyHandleForm {

	@Schema(title = "状态(1待处理,2已寄出,3无效信息)")
	@NotNull(message = ErrorCode.CODE_30197005)
	@Range(min = 2, max = 3, message = ErrorCode.CODE_30197006)
	private Integer state;

	@Schema(title = "物流单号")
	@Length(max = 100, message = ErrorCode.CODE_30197007)
	private String logisticsNumber;

	@Schema(title = "处理备注")
	@Length(max = 100, message = ErrorCode.CODE_30197008)
	private String handleRemark;

	public SampleApply convertToEntity(SampleApply sampleApply) {
		sampleApply.setState(this.state);
		sampleApply.setLogisticsNumber(this.logisticsNumber);
		sampleApply.setHandleRemark(this.handleRemark);
		return sampleApply;
	}
}
