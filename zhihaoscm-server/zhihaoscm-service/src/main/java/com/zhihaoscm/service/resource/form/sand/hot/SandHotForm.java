package com.zhihaoscm.service.resource.form.sand.hot;

import java.time.LocalDateTime;
import java.util.List;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.domain.bean.dto.SandHotDto;
import com.zhihaoscm.domain.bean.entity.SandHot;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(name = "SandHotForm", title = "砂石热点表单对象")
public class SandHotForm {

	@Schema(title = "标题")
	@NotBlank(message = ErrorCode.CODE_30192002)
	@Length(min = 1, max = 50, message = ErrorCode.CODE_30192002)
	private String title;

	@Schema(title = "分类")
	@NotNull(message = ErrorCode.CODE_30192003)
	@Range(min = 1, max = 5, message = ErrorCode.CODE_30192004)
	private Integer type;

	@Schema(title = "来源")
	@Length(max = 10, message = ErrorCode.CODE_30192007)
	private String source;

	@Schema(title = "发布日期")
	@NotNull(message = ErrorCode.CODE_30192008)
	private LocalDateTime publishDate;

	@Schema(title = "图片id")
	private Long fileId;

	@Schema(title = "内容富文本")
	@NotBlank(message = ErrorCode.CODE_30192011)
	private String content;

	@Schema(title = "免责声明")
	@Length(max = 300, message = ErrorCode.CODE_30192012)
	private String disclaimers;

	@Schema(title = "标签id集合")
	@Size(max = 10, message = ErrorCode.CODE_30192006)
	private List<Long> tagIds;

	@Schema(title = "要激活的文件id")
	private List<Long> activeFileIds;

	@Schema(title = "要删除的文件id")
	private List<Long> unActiveFileIds;

	public SandHotDto convertToDto(SandHot sandHot) {
		this.update(sandHot);
		SandHotDto dto = new SandHotDto();
		dto.setSandHot(sandHot);
		dto.setActiveFileIds(this.getActiveFileIds());
		dto.setUnActiveFileIds(this.getUnActiveFileIds());
		dto.setTagIds(this.getTagIds());
		return dto;
	}

	private void update(SandHot sandHot) {
		sandHot.setTitle(this.getTitle());
		sandHot.setType(this.getType());
		sandHot.setSource(this.getSource());
		sandHot.setPublishDate(this.getPublishDate());
		sandHot.setFileId(this.getFileId());
		sandHot.setContent(this.getContent());
		sandHot.setDisclaimers(this.getDisclaimers());
	}
}
