package com.zhihaoscm.service.resource.common;

import java.util.Optional;

import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.mybatis.plus.resource.MpStringIdBaseResource;
import com.zhihaoscm.domain.bean.entity.TransportOrderDetailsShip;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.vo.TransportOrderShipVo;
import com.zhihaoscm.service.core.service.TransportOrderShipService;

/**
 * @program: zhihaoscm-server
 * @ClassName TransportOrderShipResource
 * @description: 船运单控制器
 * @author: 魏志鹏
 * @create: 2025-04-22 10 05
 * @Version 1.0
 **/
@RestController
@RequestMapping("/transport-order-rest")
public class TransportOrderShipResource extends
		MpStringIdBaseResource<TransportOrderShip, TransportOrderShipService> {

	public TransportOrderShipResource(TransportOrderShipService service) {
		super(service);
	}

	@PostMapping("/confirmation/departure")
	public void confirmationDeparture(
			@RequestBody TransportOrderDetailsShip transportOrderDetailsShip) {
		service.confirmationDeparture(transportOrderDetailsShip);
	}

	@PostMapping("/agree/unload")
	public void agreeUnload(
			@RequestBody TransportOrderDetailsShip transportOrderDetailsShip) {
		service.agreeUnload(transportOrderDetailsShip);
	}

	@GetMapping("/find/vo")
	public Optional<TransportOrderShipVo> findVoById(@RequestParam String id) {
		return service.findVoById(id);
	}

	@GetMapping("/find/vo/empty-app")
	public Optional<TransportOrderShipVo> findVoByIdAndAppIsEmpty(
			@RequestParam String id) {
		return service.findVoByIdAndAppIsEmpty(id);
	}
}
