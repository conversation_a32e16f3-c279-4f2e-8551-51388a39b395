package com.zhihaoscm.service.resource.form.information;

import java.time.LocalDate;
import java.util.List;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import com.zhihaoscm.domain.bean.dto.InformationDto;
import com.zhihaoscm.domain.bean.entity.Information;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(name = "InformationForm", title = "砂石资讯新增对象")
public class InformationForm {

	@Length(min = 1, max = 50, message = ErrorCode.CODE_30047002)
	@NotBlank(message = ErrorCode.CODE_30047002)
	@Schema(title = "标题")
	private String title;

	@NotNull(message = ErrorCode.CODE_30047003)
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@Schema(title = "发布日期")
	private LocalDate publishDate;

	@Length(min = 1, max = 10, message = ErrorCode.CODE_30047005)
	@Schema(title = "来源")
	private String source;

	@NotNull(message = ErrorCode.CODE_30047006)
	@Schema(title = "类型")
	private Integer type;

	@NotNull(message = ErrorCode.CODE_30047007)
	@Schema(title = "封面文件id")
	private Long coverFileId;

	@Schema(title = "内容富文本 和 视频文件id 二选一")
	private String content;

	@Schema(title = "视频文件id 和 内容富文本 二选一")
	private Long videoFileId;

	@Schema(title = "要激活的文件id")
	private List<Long> activeFileIds;

	@Schema(title = "要删除的文件id")
	private List<Long> unActiveFileIds;

	@Length(max = 200, message = "简要描述长度不超过200个字符")
	@Schema(title = "简要描述")
	private String description;

	@NotNull(message = ErrorCode.CODE_30047010)
	@Schema(title = "分类")
	@Range(min = 1, max = 6, message = ErrorCode.CODE_30047016)
	private Integer classify;

	@NotNull(message = ErrorCode.CODE_30047011)
	@Schema(title = "收费类型：1:免费内容、2:会员专享")
	private Integer feeType;

	@Length(max = 300, message = "免责声明长度不超过300个字符")
	@Schema(title = "免责声明")
	private String disclaimers;

	@Schema(title = "标签id集合")
	@Size(max = 10, message = ErrorCode.CODE_30047012)
	private List<Long> tagIds;

	@Schema(title = "移动端展示位置")
	@Range(min = 1, max = 5, message = ErrorCode.CODE_30047017)
	private Integer showLocation;

	@Schema(title = "PC端展示位置")
	@Range(min = 1, max = 4, message = ErrorCode.CODE_30047018)
	private Integer pcShowLocation;

	public InformationDto convertToDto(Information information) {
		update(information);
		InformationDto informationDto = new InformationDto();
		informationDto.setInformation(information);
		informationDto.setActiveFileIds(this.getActiveFileIds());
		informationDto.setUnActiveFileIds(this.getUnActiveFileIds());
		informationDto.setTagIds(this.getTagIds());
		return informationDto;
	}

	public void update(Information information) {
		information.setTitle(this.getTitle());
		information.setPublishDate(this.getPublishDate());
		information.setSource(this.getSource());
		information.setType(this.getType());
		information.setCoverFileId(this.getCoverFileId());
		information.setContent(this.getContent());
		information.setVideoFileId(this.getVideoFileId());
		information.setDescription(this.getDescription());
		information.setClassify(this.getClassify());
		information.setFeeType(this.getFeeType());
		information.setDisclaimers(this.getDisclaimers());
		information.setShowLocation(this.getShowLocation());
		information.setPcShowLocation(this.getPcShowLocation());
	}

}
