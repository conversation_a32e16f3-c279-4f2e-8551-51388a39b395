package com.zhihaoscm.service.core.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.vo.ProductConsignmentVo;
import com.zhihaoscm.domain.bean.vo.ServiceSpecialVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AssignDef;
import com.zhihaoscm.domain.meta.biz.AutoCodeDef;
import com.zhihaoscm.domain.meta.biz.ProductConsignmentDef;
import com.zhihaoscm.domain.meta.biz.SampleApplyDef;
import com.zhihaoscm.service.core.mapper.ProductConsignmentMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.UserService;

/**
 * <p>
 * 寄售商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
public class ProductConsignmentServiceImpl extends
		MpStringIdBaseServiceImpl<ProductConsignment, ProductConsignmentMapper>
		implements ProductConsignmentService {

	@Autowired
	private UserService userService;
	@Autowired
	private AssignService assignService;
	@Autowired
	private FileService fileService;
	@Autowired
	private ProductTypeService productTypeService;
	@Autowired
	private SampleApplyService sampleApplyService;

	public ProductConsignmentServiceImpl(ProductConsignmentMapper repository) {
		super(repository);
	}

	@Override
	public Page<ProductConsignmentVo> paging(Integer page, Integer size,
			String keyword, String productTypeId, Long supplierId,
			Integer state, List<Integer> publishState, String sortKey,
			String sortOrder) {
		LambdaQueryWrapper<ProductConsignment> queryWrapper = Wrappers
				.lambdaQuery(ProductConsignment.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.and(StringUtils.isNotBlank(keyword),
				wrapper -> wrapper.eq(ProductConsignment::getId, keyword).or()
						.like(ProductConsignment::getTitle, keyword));
		queryWrapper
				.eq(StringUtils.isNotBlank(productTypeId),
						ProductConsignment::getProductTypeId, productTypeId)
				.eq(Objects.nonNull(supplierId),
						ProductConsignment::getSupplierId, supplierId)
				.eq(Objects.nonNull(state), ProductConsignment::getState, state)
				.in(CollectionUtils.isNotEmpty(publishState),
						ProductConsignment::getPublishState, publishState);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 先按发布状态待审核1、未通过2、已发布3、已下架4、已关闭5在按更新时间倒序
			queryWrapper.orderByAsc(ProductConsignment::getPublishState)
					.orderByDesc(ProductConsignment::getUpdatedTime);
		}
		Page<ProductConsignment> paging = repository
				.selectPage(new Page<>(page, size), queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords(), null));
	}

	@Override
	public Page<ProductConsignmentVo> customPaging(Integer page, Integer size,
			String keyword, String productTypeId, String supplierName,
			String cityCode, Long customId) {
		LambdaQueryWrapper<ProductConsignment> queryWrapper = Wrappers
				.lambdaQuery(ProductConsignment.class);
		this.filterDeleted(queryWrapper);
		// 用户的寄售商品列表只能看到上架状态的,无论发布状态是已发布还是待审核还是未通过
		queryWrapper.eq(ProductConsignment::getState,
				ProductConsignmentDef.State.UP_SHELF.getCode());
		queryWrapper.in(ProductConsignment::getPublishState,
				List.of(ProductConsignmentDef.PublishState.PASS.getCode(),
						ProductConsignmentDef.PublishState.NOT_AUDIT.getCode(),
						ProductConsignmentDef.PublishState.FAIL.getCode()));
		// 商品标题模糊查询
		queryWrapper.like(StringUtils.isNotBlank(keyword),
				ProductConsignment::getTitle, keyword);
		// 品类id
		queryWrapper.eq(StringUtils.isNotBlank(productTypeId),
				ProductConsignment::getProductTypeId, productTypeId);
		// 二级地址编码
		queryWrapper.eq(StringUtils.isNotBlank(cityCode),
				ProductConsignment::getCityCode, cityCode);
		// 供应商名称模糊查询
		if (StringUtils.isNotBlank(supplierName)) {
			queryWrapper.and(i -> i.apply(StringUtils.isNoneBlank(supplierName),
					"(JSON_EXTRACT(supplier_enterprise, '$.name') LIKE CONCAT('%',{0},'%') ",
					supplierName));
		}
		// 先按权重倒序，再按更新时间倒序
		queryWrapper.orderByDesc(ProductConsignment::getWeight)
				.orderByDesc(ProductConsignment::getUpdatedTime);

		Page<ProductConsignment> paging = repository
				.selectPage(new Page<>(page, size), queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords(), customId));
	}

	@Override
	public Optional<ProductConsignmentVo> findVoById(String id, Long customId) {
		return super.findOne(id)
				.map(resource -> this.packVo(resource, customId));
	}

	@FileId
	@Override
	public ProductConsignment create(ProductConsignment product,
			@FileId List<Long> activeFileIds) {
		// 设置id
		product.setId(AutoCodeDef.CREATE_AUTO_CODE.apply(
				SpringUtil.getBean(StringRedisClient.class),
				AutoCodeDef.BusinessCode.PRODUCT_PREFIX.getCode()
						+ product.getProductTypeId(),
				RedisKeys.Cache.PRODUCT_CONSIGNMENT_CODE_GENERATOR,
				StringUtils.EMPTY, 5, AutoCodeDef.DATE_TYPE.yyMM));
		// 填充信息
		this.fillInfo(product);
		// 新增或编辑之后的状态是 下架+待审核
		product.setState(ProductConsignmentDef.State.DOWN_SHELF.getCode());
		product.setPublishState(
				ProductConsignmentDef.PublishState.NOT_AUDIT.getCode());
		return super.create(product);
	}

	@FileId(type = 2)
	@Override
	public ProductConsignment update(ProductConsignment product,
			@FileId List<Long> activeFileIds,
			@FileId(type = 2) List<Long> unActiveFileIds) {
		// 填充信息
		this.fillInfo(product);
		// 新增或编辑之后的状态是 下架+待审核
		product.setState(ProductConsignmentDef.State.DOWN_SHELF.getCode());
		product.setPublishState(
				ProductConsignmentDef.PublishState.NOT_AUDIT.getCode());
		return super.updateAllProperties(product);
	}

	@Override
	public void audit(ProductConsignment product) {
		super.updateAllProperties(product);
	}

	@Override
	public void downShelf(ProductConsignment product) {
		// 状态为 下架+待审核
		product.setState(ProductConsignmentDef.State.DOWN_SHELF.getCode());
		product.setPublishState(
				ProductConsignmentDef.PublishState.NOT_AUDIT.getCode());
		super.updateAllProperties(product);
	}

	@FileId(type = 3)
	@Override
	public void close(ProductConsignment product,
			@FileId(type = 2) List<Long> unActiveFileIds) {
		// 状态为 下架+关闭
		product.setState(ProductConsignmentDef.State.DOWN_SHELF.getCode());
		product.setPublishState(
				ProductConsignmentDef.PublishState.CLOSE.getCode());
		super.updateAllProperties(product);
	}

	@Override
	public void assign(String id, Long handlerId) {
		// 获取专员信息
		Optional<User> userOp = userService.findOne(handlerId);
		// 将专员回写到商品表中
		this.findOne(id).ifPresent(product -> {
			product.setHandlerId(handlerId);
			userOp.ifPresent(user -> product.setHandlerName(user.getName()));
			this.updateAllProperties(product);
			// 插入记录到指派表
			Assign assign = new Assign();
			assign.setCorrelationId(Long.valueOf(id));
			assign.setUserId(handlerId);
			assign.setType(AssignDef.Type.PRODUCT_CONSIGNMENT.getCode());
			assignService.create(assign);
		});

	}

	@Override
	public List<ServiceSpecialVo> findServiceSpecials(String name) {
		// 查询拥有指定角色的账号
		List<User> attaches = userService
				.findUsersByPermission(AdminPermissionDef.PRODUCT_DEAL, name);
		if (CollectionUtils.isEmpty(attaches)) {
			return List.of();
		}
		// 跟进就是他负责的全部商品
		LambdaQueryWrapper<ProductConsignment> wrapper = Wrappers
				.lambdaQuery(ProductConsignment.class)
				.eq(ProductConsignment::getDel, CommonDef.Symbol.NO.getCode());
		List<ProductConsignment> products = repository.selectList(wrapper);
		// 过滤出HandlerId为空值的
		List<ProductConsignment> productsList = products.stream()
				.filter(shippingRequirementPlat -> Objects
						.nonNull(shippingRequirementPlat.getHandlerId()))
				.toList();
		// 按人分组
		Map<Long, Long> productMap = productsList.stream()
				.collect(Collectors.groupingBy(ProductConsignment::getHandlerId,
						Collectors.counting()));
		// 组装vo
		return attaches.stream().map(user -> {
			ServiceSpecialVo vo = new ServiceSpecialVo();
			vo.setUser(user);
			vo.setProductCount(productMap.getOrDefault(user.getId(), 0L));
			return vo;
		}).sorted(Comparator.comparingLong(ServiceSpecialVo::getProductCount)
				.reversed()).toList();
	}

	/**
	 * 填充信息
	 */
	private void fillInfo(ProductConsignment product) {
		// todo 设置供应商信息
		// 设置品类信息
		productTypeService.findOne(product.getProductTypeId())
				.ifPresent(productType -> {
					product.setProductTypeName(productType.getName());
					product.setArea(productType.getArea());
				});
		// 设置商品专员信息
		userService.findOne(product.getHandlerId())
				.ifPresent(user -> product.setHandlerName(user.getName()));
	}

	/**
	 * 组装vo
	 */
	private ProductConsignmentVo packVo(ProductConsignment productConsignment,
			Long customId) {
		ProductConsignmentVo vo = new ProductConsignmentVo();
		vo.setProductConsignment(productConsignment);

		if (Objects.nonNull(productConsignment.getImgMainId())) {
			// 设置主图
			vo.setMainFile(fileService
					.findOne(productConsignment.getImgMainId()).orElse(null));
		}
		if (Objects.nonNull(productConsignment.getVideoId())) {
			// 设置视频
			vo.setVideoFile(fileService.findOne(productConsignment.getVideoId())
					.orElse(null));
		}
		if (CollectionUtils.isNotEmpty(productConsignment.getImgIds())) {
			// 设置非主图文件
			vo.setOtherImgFile(
					fileService.findByIds(productConsignment.getImgIds()));
		}
		if (StringUtils.isNotBlank(productConsignment.getProductTypeId())) {
			vo.setProductTypeVo(productTypeService
					.findVoById(productConsignment.getProductTypeId())
					.orElse(null));
			vo.setProductType(productTypeService
					.findOne(productConsignment.getProductTypeId())
					.orElse(null));
		}
		if (Objects.nonNull(customId)) {
			// 设置申请寄样信息
			List<SampleApply> sampleApplys = sampleApplyService
					.findByProductIdAndCustomIdAndState(
							List.of(productConsignment.getId()), customId,
							List.of(SampleApplyDef.State.PENDING.getCode(),
									SampleApplyDef.State.SHIPPED.getCode()));
			if (CollectionUtils.isNotEmpty(sampleApplys)) {
				vo.setSampleApply(sampleApplys.get(0));
			}
			return vo;
		}
		return vo;
	}

	/**
	 * 组装vos
	 */
	private List<ProductConsignmentVo> packVo(List<ProductConsignment> records,
			Long customId) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		// 主图文件id集合
		Set<Long> fileIds = new HashSet<>();
		records.forEach(record -> fileIds.add(record.getImgMainId()));
		Map<Long, File> idFileMap = fileService
				.getIdMap(new ArrayList<>(fileIds));

		// 申请寄样列表(待处理或已寄出状态的)
		Map<String, SampleApply> sampleApplyMap;
		if (Objects.nonNull(customId)) {
			sampleApplyMap = sampleApplyService
					.findByProductIdAndCustomIdAndState(null, customId,
							List.of(SampleApplyDef.State.PENDING.getCode(),
									SampleApplyDef.State.SHIPPED.getCode()))
					.stream().collect(Collectors
							.toMap(SampleApply::getProductId, c -> c));
		} else {
			sampleApplyMap = new HashMap<>();
		}
		return records.stream().map(record -> {
			ProductConsignmentVo vo = new ProductConsignmentVo();
			vo.setProductConsignment(record);
			// 设置主图
			vo.setMainFile(idFileMap.get(record.getImgMainId()));
			if (StringUtils.isNotBlank(record.getProductTypeId())) {
				productTypeService.findVoById(record.getProductTypeId())
						.ifPresent(vo::setProductTypeVo);
				productTypeService.findOne(record.getProductTypeId())
						.ifPresent(vo::setProductType);

			}
			if (Objects.nonNull(customId)) {
				vo.setSampleApply(sampleApplyMap.get(record.getId()));
			}
			return vo;
		}).toList();
	}
}
