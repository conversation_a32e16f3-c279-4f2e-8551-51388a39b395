package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.ShippingCompositeIndex;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.ShippingCompositeIndexCountVo;
import com.zhihaoscm.domain.bean.vo.ShippingCompositeIndexStatisticVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.CompositeIndexDef;
import com.zhihaoscm.domain.meta.biz.DashboardDef;
import com.zhihaoscm.domain.meta.biz.ShippingCompositeIndexDef;
import com.zhihaoscm.domain.meta.biz.WxwDef;
import com.zhihaoscm.domain.utils.DashboardDateUtils;
import com.zhihaoscm.service.core.mapper.ShippingCompositeIndexMapper;
import com.zhihaoscm.service.core.service.MessageService;
import com.zhihaoscm.service.core.service.ShippingCompositeIndexService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

/**
 * <p>
 * 运价综合指数 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-16
 */
@Service
public class ShippingCompositeIndexServiceImpl extends
		MpLongIdBaseServiceImpl<ShippingCompositeIndex, ShippingCompositeIndexMapper>
		implements ShippingCompositeIndexService {

	public ShippingCompositeIndexServiceImpl(
			ShippingCompositeIndexMapper repository) {
		super(repository);
	}

	@Autowired
	private UserService userService;

	@Autowired
	private MessageService messageService;

	@Override
	public Page<ShippingCompositeIndex> paging(Integer page, Integer size,
			LocalDateTime priceDate, Integer state, Integer publishType) {
		LambdaQueryWrapper<ShippingCompositeIndex> wrapper = Wrappers
				.lambdaQuery(ShippingCompositeIndex.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(priceDate),
				ShippingCompositeIndex::getPriceDate, priceDate);
		wrapper.eq(Objects.nonNull(state), ShippingCompositeIndex::getState,
				state);
		wrapper.eq(Objects.nonNull(publishType),
				ShippingCompositeIndex::getPublishType, publishType);
		wrapper.orderByDesc(ShippingCompositeIndex::getPriceDate);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Optional<ShippingCompositeIndex> findByPriceDate(
			LocalDateTime priceDate) {
		LambdaQueryWrapper<ShippingCompositeIndex> wrapper = Wrappers
				.lambdaQuery(ShippingCompositeIndex.class);
		this.filterDeleted(wrapper);
		wrapper.eq(ShippingCompositeIndex::getPriceDate, priceDate);
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public List<ShippingCompositeIndex> findByPriceDate(LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<ShippingCompositeIndex> wrapper = Wrappers
				.lambdaQuery(ShippingCompositeIndex.class);
		this.filterDeleted(wrapper);
		wrapper.ge(ShippingCompositeIndex::getPriceDate, beginTime);
		wrapper.le(ShippingCompositeIndex::getPriceDate, endTime);
		wrapper.eq(ShippingCompositeIndex::getState,
				ShippingCompositeIndexDef.State.PUBLISHED.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<ShippingCompositeIndex> findLatest(LocalDateTime date) {
		LambdaQueryWrapper<ShippingCompositeIndex> wrapper = Wrappers
				.lambdaQuery(ShippingCompositeIndex.class);
		this.filterDeleted(wrapper);
		wrapper.orderByDesc(ShippingCompositeIndex::getPriceDate);
		wrapper.eq(ShippingCompositeIndex::getState,
				ShippingCompositeIndexDef.State.PUBLISHED.getCode());
		wrapper.eq(Objects.nonNull(date), ShippingCompositeIndex::getPriceDate,
				date);
		wrapper.last("limit 1");
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public List<ShippingCompositeIndex> batchCreate(
			List<ShippingCompositeIndex> resources) {
		List<ShippingCompositeIndex> shippingCompositeIndexList = super.batchCreate(
				resources);
		for (ShippingCompositeIndex resource : resources) {
			this.sendPublishNotice(resource);
		}

		return shippingCompositeIndexList;
	}

	@Override
	public ShippingCompositeIndex updateAllProperties(
			ShippingCompositeIndex resource) {
		ShippingCompositeIndex shippingCompositeIndex = super.updateAllProperties(
				resource);
		this.sendPublishNotice(resource);
		return shippingCompositeIndex;
	}

	@Override
	public Optional<ShippingCompositeIndexStatisticVo> detail() {
		ShippingCompositeIndexStatisticVo shippingCompositeIndexStatisticVo = new ShippingCompositeIndexStatisticVo();
		// 计算最新综合指数
		this.findLatest(null).ifPresent(lastIndex -> {
			LocalDateTime priceDate = lastIndex.getPriceDate();
			shippingCompositeIndexStatisticVo.setNewIndexDate(priceDate);
			shippingCompositeIndexStatisticVo
					.setNewCompositeIndex(lastIndex.getCompositeIndex());

			LocalDateTime date = priceDate.minusDays(1).with(LocalTime.MIN);
			this.findLatest(date).ifPresent(lastSecondIndex -> {
				// 前一日的指数不为空且前两日的数据不能为空且不能为0
				if (Objects.nonNull(lastIndex.getCompositeIndex())
						&& Objects.nonNull(lastSecondIndex.getCompositeIndex())
						&& BigDecimal.ZERO.compareTo(
								lastSecondIndex.getCompositeIndex()) != 0) {
					BigDecimal subtract = lastIndex.getCompositeIndex()
							.subtract(lastSecondIndex.getCompositeIndex());
					shippingCompositeIndexStatisticVo
							.setCompositeIndexChangeAmount(subtract);
					shippingCompositeIndexStatisticVo
							.setCompositeIndexChangePercent(subtract
									.divide(lastSecondIndex.getCompositeIndex(),
											4, RoundingMode.HALF_UP)
									.multiply(new BigDecimal(100)));
				}
			});
		});
		return Optional.of(shippingCompositeIndexStatisticVo);
	}

	@Override
	public Optional<ShippingCompositeIndexStatisticVo> line(Integer scope) {
		ShippingCompositeIndexStatisticVo vo = new ShippingCompositeIndexStatisticVo();
		switch (CompositeIndexDef.Scope.from(scope)) {
			case LATEST_ONE_WEEK ->
				this.protract(CompositeIndexDef.Scope.LATEST_ONE_WEEK, vo);
			case LATEST_ONE_MONTH ->
				this.protract(CompositeIndexDef.Scope.LATEST_ONE_MONTH, vo);
			case LATEST_ONE_YEAR ->
				this.protract(CompositeIndexDef.Scope.LATEST_ONE_YEAR, vo);
		}
		return Optional.of(vo);
	}

	@Override
	public ShippingCompositeIndexCountVo statisticsShippingCompositeIndex(
			Boolean hasFull) {
		ShippingCompositeIndexCountVo shippingCompositeIndexCountVo = new ShippingCompositeIndexCountVo();
		shippingCompositeIndexCountVo.setUnPublished(0L);
		if (hasFull) {
			LambdaQueryWrapper<ShippingCompositeIndex> queryWrapper = Wrappers
					.lambdaQuery(ShippingCompositeIndex.class);
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(ShippingCompositeIndex::getState,
					ShippingCompositeIndexDef.State.UNPUBLISHED.getCode());
			Long unPublished = repository.selectCount(queryWrapper);
			shippingCompositeIndexCountVo.setUnPublished(unPublished);
			queryWrapper.clear();
		}
		return shippingCompositeIndexCountVo;
	}

	/**
	 * 计算月份天数
	 *
	 * @return
	 */
	private Integer calcScope(LocalDateTime latestDate,
			CompositeIndexDef.Scope scope) {
		return switch (scope) {
			case LATEST_ONE_WEEK -> 7;
			case LATEST_ONE_MONTH -> 1 + Math.toIntExact(Duration.between(
					Objects.requireNonNull(DashboardDateUtils
							.calcBeginDate(DashboardDef.Scope.MONTH.getCode())),
					latestDate).toDays());
			case LATEST_ONE_YEAR -> 1 + Math.toIntExact(Duration.between(
					Objects.requireNonNull(DashboardDateUtils
							.calcBeginDate(DashboardDef.Scope.YEAR.getCode())),
					latestDate).toDays());
		};
	}

	@Override
	public void publish(Long id) {
		LambdaUpdateWrapper<ShippingCompositeIndex> wrapper = Wrappers
				.lambdaUpdate(ShippingCompositeIndex.class);
		wrapper.eq(ShippingCompositeIndex::getId, id);
		wrapper.set(ShippingCompositeIndex::getState,
				ShippingCompositeIndexDef.State.PUBLISHED.getCode());
		wrapper.set(ShippingCompositeIndex::getPublishType,
				ShippingCompositeIndexDef.PublishType.MANUAL.getCode());
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (Objects.nonNull(context)) {
			Long userId = context.getUserId();
			wrapper.set(ShippingCompositeIndex::getUpdatedBy, userId);
		}
		repository.update(wrapper);
	}

	@Override
	public void revoke(Long id) {
		LambdaUpdateWrapper<ShippingCompositeIndex> wrapper = Wrappers
				.lambdaUpdate(ShippingCompositeIndex.class);
		wrapper.eq(ShippingCompositeIndex::getId, id);
		wrapper.set(ShippingCompositeIndex::getState,
				ShippingCompositeIndexDef.State.UNPUBLISHED.getCode());
		wrapper.set(ShippingCompositeIndex::getPublishType, null);
		wrapper.set(ShippingCompositeIndex::getVersionId, null);
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (Objects.nonNull(context)) {
			Long userId = context.getUserId();
			wrapper.set(ShippingCompositeIndex::getUpdatedBy, userId);
		}
		repository.update(wrapper);

		this.findOne(id).ifPresent(shippingCompositeIndex -> messageService
				.sendNotice(WxwMessage.builder()
						.receiptors(userService.findUsersByPermission(
								AdminPermissionDef.COMPSITE_FREIGHT_DEAL, null)
								.stream()
								.map(user -> String.valueOf(user.getId()))
								.toList())
						.url("/indexResearch/freightManage/freightCompositeIndex/detail/"
								.concat(String.valueOf(
										shippingCompositeIndex.getId())))
						.prefix(WxwDef.NoticePrefix.YOU_SUBMIT.getDesc())
						.operationModule(
								WxwDef.NoticeOperationModule.SHIPPING_COMPOSITE_INDEX
										.getDesc())
						.desc("已撤回")
						.keyword(String.valueOf(shippingCompositeIndex
								.getPriceDate().toLocalDate()))
						.content(StringUtils.EMPTY).build()));
	}

	/**
	 * 发送发布通知
	 *
	 * @param resource
	 */
	private void sendPublishNotice(ShippingCompositeIndex resource) {
		if (ShippingCompositeIndexDef.State.UNPUBLISHED
				.match(resource.getState())) {
			messageService.sendNotice(WxwMessage.builder()
					.receiptors(userService
							.findUsersByPermission(
									AdminPermissionDef.COMPSITE_FREIGHT_MANAGE,
									null)
							.stream().map(user -> String.valueOf(user.getId()))
							.toList())
					.url("/indexResearch/freightManage/freightCompositeIndex/detail/"
							.concat(String.valueOf(resource.getId())))
					.prefix(WxwDef.NoticePrefix.YOU_HAVE_NEW.getDesc())
					.operationModule(
							WxwDef.NoticeOperationModule.SHIPPING_COMPOSITE_INDEX
									.getDesc())
					.desc("待发布")
					.keyword(String
							.valueOf(resource.getPriceDate().toLocalDate()))
					.content(StringUtils.EMPTY).build());
		}
	}

	/**
	 * 绘制折线图数据
	 *
	 * @param scope
	 * @param vo
	 * @return
	 */
	private void protract(CompositeIndexDef.Scope scope,
			ShippingCompositeIndexStatisticVo vo) {
		// 获取最新的综合指数日期
		LocalDateTime latestDate = LocalDateTime.now().with(LocalTime.MIN);
		// 初始化最近一周的综合指数
		Map<LocalDateTime, ShippingCompositeIndex> lineMap = Stream
				.iterate(latestDate,
						localDate -> localDate.minusDays(1).with(LocalTime.MIN))
				.limit(this.calcScope(latestDate, scope)).collect(
						Collectors.toMap(key -> key, this::initCompositeIndex));

		List<LocalDateTime> dateTimeList = lineMap.keySet().stream().toList();

		// 获取最小时间
		LocalDateTime beginTime = Collections.min(dateTimeList);

		// 获取最大时间
		LocalDateTime endTime = Collections.max(dateTimeList);

		// 综合砂石指数
		List<ShippingCompositeIndex> compositeIndexList = this
				.findByPriceDate(beginTime, endTime);
		compositeIndexList.stream().collect(Collectors
				.toMap(ShippingCompositeIndex::getPriceDate, index -> index))
				.forEach((key, value) -> {
					if (lineMap.containsKey(key)) {
						lineMap.put(key, value);
					}
				});
		vo.setCompositeIndexLines(lineMap.values().stream()
				.sorted(Comparator
						.comparing(ShippingCompositeIndex::getPriceDate))
				.toList());

	}

	/**
	 * 初始化最近一周的综合指数
	 *
	 * @param date
	 * @return
	 */
	private ShippingCompositeIndex initCompositeIndex(LocalDateTime date) {
		ShippingCompositeIndex index = new ShippingCompositeIndex();
		index.setPriceDate(date);
		index.setCompositeIndex(BigDecimal.ZERO);
		return index;
	}
}
