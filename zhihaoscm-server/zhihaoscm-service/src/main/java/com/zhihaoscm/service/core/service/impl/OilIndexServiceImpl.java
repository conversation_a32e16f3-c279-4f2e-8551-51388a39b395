package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.OilIndex;
import com.zhihaoscm.domain.bean.entity.OilSite;
import com.zhihaoscm.domain.bean.vo.OilIndexDetailVo;
import com.zhihaoscm.domain.bean.vo.OilIndexStatisticVo;
import com.zhihaoscm.domain.bean.vo.OilIndexVo;
import com.zhihaoscm.domain.bean.vo.OilSiteVo;
import com.zhihaoscm.domain.meta.biz.CompositeIndexDef;
import com.zhihaoscm.domain.meta.biz.DashboardDef;
import com.zhihaoscm.domain.utils.DashboardDateUtils;
import com.zhihaoscm.service.core.mapper.OilIndexMapper;
import com.zhihaoscm.service.core.service.OilIndexService;
import com.zhihaoscm.service.core.service.OilSiteService;

/**
 * <p>
 * 油品指数 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class OilIndexServiceImpl
		extends MpLongIdBaseServiceImpl<OilIndex, OilIndexMapper>
		implements OilIndexService {

	public OilIndexServiceImpl(OilIndexMapper repository) {
		super(repository);
	}

	@Autowired
	private OilSiteService oilSiteService;

	@Override
	public Page<OilIndexVo> paging(Integer page, Integer size, Long versionId,
			String sortKey, String sortOrder, String keyword, String brand,
			String provinceName, LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<OilIndex> wrapper = Wrappers
				.lambdaQuery(OilIndex.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(versionId), OilIndex::getVersionId,
				versionId);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			wrapper.orderByDesc(OilIndex::getVersionDate);
			wrapper.orderByDesc(OilIndex::getUpdatedTime);
		}
		wrapper.ge(Objects.nonNull(beginTime), OilIndex::getVersionDate,
				beginTime);
		wrapper.le(Objects.nonNull(endTime), OilIndex::getVersionDate, endTime);
		if (StringUtils.isNotBlank(keyword)) {
			List<OilSite> oilSiteList = oilSiteService.findByNameOrId(keyword);
			List<Long> oliSiteIds = oilSiteList.stream().map(OilSite::getId)
					.toList();
			if (CollectionUtils.isNotEmpty(oilSiteList)) {
				wrapper.in(OilIndex::getOilSiteId, oliSiteIds);
			} else {
				// 返回空的
				return new Page<>();
			}

		}
		if (StringUtils.isNotBlank(brand)) {
			List<OilSite> oilSiteList = oilSiteService.findByBrandLike(brand);
			List<Long> oliSiteIds = oilSiteList.stream().map(OilSite::getId)
					.toList();
			if (CollectionUtils.isNotEmpty(oilSiteList)) {
				wrapper.in(OilIndex::getOilSiteId, oliSiteIds);
			} else {
				// 返回空的
				return new Page<>();
			}
		}
		if (StringUtils.isNotBlank(provinceName)) {
			List<OilSite> oilSiteList = oilSiteService
					.findByProvinceNameLike(provinceName);
			List<Long> oliSiteIds = oilSiteList.stream().map(OilSite::getId)
					.toList();
			if (CollectionUtils.isNotEmpty(oilSiteList)) {
				wrapper.in(OilIndex::getOilSiteId, oliSiteIds);
			} else {
				// 返回空的
				return new Page<>();
			}
		}
		Page<OilIndex> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));

	}

	@Override
	public Page<OilIndexVo> customPaging(Integer page, Integer size,
			String provinceCode, String brand) {
		return repository.customPaging(new Page<>(page, size), provinceCode,
				brand);
	}

	@Override
	public List<OilIndex> findByVersionId(Long versionId) {
		LambdaQueryWrapper<OilIndex> wrapper = Wrappers
				.lambdaQuery(OilIndex.class);
		this.filterDeleted(wrapper);
		wrapper.eq(OilIndex::getVersionId, versionId);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<OilIndex> findByOilSiteId(Long oilSiteId) {
		LambdaQueryWrapper<OilIndex> wrapper = Wrappers
				.lambdaQuery(OilIndex.class);
		this.filterDeleted(wrapper);
		wrapper.eq(OilIndex::getOilSiteId, oilSiteId);
		wrapper.orderByDesc(OilIndex::getVersionDate);
		wrapper.last("limit 1");
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public List<OilIndex> findByOilSiteIds(List<Long> oilSiteIds) {
		LambdaQueryWrapper<OilIndex> wrapper = Wrappers
				.lambdaQuery(OilIndex.class);
		this.filterDeleted(wrapper);
		wrapper.in(CollectionUtils.isNotEmpty(oilSiteIds),
				OilIndex::getOilSiteId, oilSiteIds);
		wrapper.orderByDesc(OilIndex::getVersionDate);
		return repository.selectList(wrapper);
	}

	@Override
	public List<OilIndex> findByVersionIds(List<Long> versionIds) {
		if (CollectionUtils.isEmpty(versionIds)) {
			return List.of();
		}
		LambdaQueryWrapper<OilIndex> wrapper = Wrappers
				.lambdaQuery(OilIndex.class);
		this.filterDeleted(wrapper);
		wrapper.in(OilIndex::getVersionId, versionIds);
		return repository.selectList(wrapper);
	}

	@Override
	public List<OilIndex> findByVersionDate(String provinceCode,
			String cityCode, String brand, LocalDateTime beginTime,
			LocalDateTime endTime) {
		return repository.findByVersionDate(provinceCode, cityCode, brand,
				beginTime, endTime);
	}

	@Override
	public List<OilIndex> findByVersionDate(LocalDateTime versionDate) {
		LambdaQueryWrapper<OilIndex> wrapper = Wrappers
				.lambdaQuery(OilIndex.class);
		this.filterDeleted(wrapper);
		wrapper.eq(OilIndex::getVersionDate, versionDate);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<OilIndexDetailVo> detail(String provinceCode,
			String cityCode, String brand, Double lon, Double lat) {
		OilIndexDetailVo oilIndexDetailVo = new OilIndexDetailVo();
		// 根据区域查询所有站点信息并获取他们最新价格且是最低的价格
		OilIndex oilIndex = repository.findByProvinceCodeAndCityCodeAndBrand(
				provinceCode, cityCode, brand);
		if (Objects.isNull(oilIndex)) {
			return Optional.empty();
		}
		// 最新版本日期
		LocalDateTime versionDate = oilIndex.getVersionDate();
		if (Objects.isNull(versionDate)) {
			return Optional.empty();
		}
		oilIndexDetailVo.setOilIndex(oilIndex);
		// 根据版本日期查询指数
		List<OilIndex> oilIndexList = this.findByVersionDate(versionDate);
		if (CollectionUtils.isNotEmpty(oilIndexList)) {
			// 站点ID集合
			List<Long> oilSetIds = oilIndexList.stream()
					.map(OilIndex::getOilSiteId).distinct().toList();
			// 查询最新日期有数据的站点
			List<OilSite> oilSiteList = oilSiteService
					.findByIdsNoDeleted(oilSetIds).stream()
					.filter(item -> CommonDef.Symbol.YES.match(item.getState()))
					.filter(item -> provinceCode.equals(item.getProvinceCode()))
					.filter(item -> cityCode.equals(item.getCityCode()))
					.filter(item -> brand.equals(item.getBrand())).toList();
			if (CollectionUtils.isNotEmpty(oilSiteList)) {
				List<OilSiteVo> oilSiteVos = this.packListVo(oilSiteList, lon,
						lat);
				oilIndexDetailVo.setOilSiteList(oilSiteVos);
			}
		}
		return Optional.of(oilIndexDetailVo);
	}

	@Override
	public List<OilIndex> findByVersionDate(Long oilSiteId,
			LocalDateTime beginTime, LocalDateTime endTime) {
		LambdaQueryWrapper<OilIndex> wrapper = Wrappers
				.lambdaQuery(OilIndex.class);
		this.filterDeleted(wrapper);
		wrapper.eq(OilIndex::getOilSiteId, oilSiteId);
		wrapper.ge(OilIndex::getVersionDate, beginTime);
		wrapper.le(OilIndex::getVersionDate, endTime);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<OilIndexStatisticVo> line(Long oilSiteId, Integer scope) {
		OilIndexStatisticVo vo = new OilIndexStatisticVo();
		switch (CompositeIndexDef.Scope.from(scope)) {
			case LATEST_ONE_WEEK -> this.protract(oilSiteId,
					CompositeIndexDef.Scope.LATEST_ONE_WEEK, vo);
			case LATEST_ONE_MONTH -> this.protract(oilSiteId,
					CompositeIndexDef.Scope.LATEST_ONE_MONTH, vo);
		}
		return Optional.of(vo);
	}

	@Override
	public Optional<OilIndexStatisticVo> areaLine(String provinceCode,
			String cityCode, String brand, Integer scope) {
		OilIndexStatisticVo vo = new OilIndexStatisticVo();
		switch (CompositeIndexDef.Scope.from(scope)) {
			case LATEST_ONE_WEEK -> this.protract(provinceCode, cityCode, brand,
					CompositeIndexDef.Scope.LATEST_ONE_WEEK, vo);
			case LATEST_ONE_MONTH -> this.protract(provinceCode, cityCode,
					brand, CompositeIndexDef.Scope.LATEST_ONE_MONTH, vo);
		}
		return Optional.of(vo);
	}

	/**
	 * 组装数据
	 *
	 * @param records
	 * @return
	 */
	private List<OilIndexVo> packVo(List<OilIndex> records) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		List<Long> oilSetIds = records.stream().map(OilIndex::getOilSiteId)
				.toList();
		Map<Long, OilSite> oilSiteMap = oilSiteService.getIdMap(oilSetIds);

		return records.stream().map(item -> {
			OilIndexVo oilIndexVo = new OilIndexVo();
			oilIndexVo.setOilIndex(item);
			if (Objects.nonNull(item.getOilSiteId())) {
				oilIndexVo.setOilSite(oilSiteMap.get(item.getOilSiteId()));
			}
			return oilIndexVo;
		}).toList();
	}

	/**
	 * 绘制折线图数据
	 *
	 * @param scope
	 * @param vo
	 * @return
	 */
	private void protract(Long oilSiteId, CompositeIndexDef.Scope scope,
			OilIndexStatisticVo vo) {
		// 获取最新的综合指数日期
		LocalDateTime latestDate = LocalDateTime.now().with(LocalTime.MIN);
		// 初始化最近一周的综合指数
		Map<LocalDateTime, OilIndex> lineMap = Stream
				.iterate(latestDate,
						localDate -> localDate.minusDays(1).with(LocalTime.MIN))
				.limit(this.calcScope(latestDate, scope)).collect(
						Collectors.toMap(key -> key, this::initCompositeIndex));

		List<LocalDateTime> dateTimeList = lineMap.keySet().stream().toList();

		// 获取最小时间
		LocalDateTime beginTime = Collections.min(dateTimeList);

		// 获取最大时间
		LocalDateTime endTime = Collections.max(dateTimeList);

		// 油品指数
		List<OilIndex> compositeIndexList = this.findByVersionDate(oilSiteId,
				beginTime, endTime);
		compositeIndexList.stream().collect(
				Collectors.toMap(OilIndex::getVersionDate, index -> index))
				.forEach((key, value) -> {
					if (lineMap.containsKey(key)) {
						lineMap.put(key, value);
					}
				});
		vo.setOilIndexLines(lineMap.values().stream()
				.sorted(Comparator.comparing(OilIndex::getVersionDate))
				.toList());

	}

	/**
	 * 绘制折线图数据
	 *
	 * @param scope
	 * @param vo
	 * @return
	 */
	private void protract(String provinceCode, String cityCode, String brand,
			CompositeIndexDef.Scope scope, OilIndexStatisticVo vo) {
		// 获取最新的综合指数日期
		LocalDateTime latestDate = LocalDateTime.now().with(LocalTime.MIN);
		// 初始化最近一周的综合指数
		Map<LocalDateTime, OilIndex> lineMap = Stream
				.iterate(latestDate,
						localDate -> localDate.minusDays(1).with(LocalTime.MIN))
				.limit(this.calcScope(latestDate, scope)).collect(
						Collectors.toMap(key -> key, this::initCompositeIndex));

		List<LocalDateTime> dateTimeList = lineMap.keySet().stream().toList();

		// 获取最小时间
		LocalDateTime beginTime = Collections.min(dateTimeList);

		// 获取最大时间
		LocalDateTime endTime = Collections.max(dateTimeList);

		// 查询这个省 市 品牌的指数
		List<OilIndex> compositeIndexList = this.findByVersionDate(provinceCode,
				cityCode, brand, beginTime, endTime);
		compositeIndexList.stream().collect(
				Collectors.toMap(OilIndex::getVersionDate, index -> index))
				.forEach((key, value) -> {
					if (lineMap.containsKey(key)) {
						lineMap.put(key, value);
					}
				});
		vo.setOilIndexLines(lineMap.values().stream()
				.sorted(Comparator.comparing(OilIndex::getVersionDate))
				.toList());

	}

	/**
	 * 初始化最近一周的综合指数
	 *
	 * @param date
	 * @return
	 */
	private OilIndex initCompositeIndex(LocalDateTime date) {
		OilIndex index = new OilIndex();
		index.setVersionDate(date);
		index.setLightFuelListingPrice(0L);
		index.setZeroDieselListingPrice(0L);
		return index;
	}

	/**
	 * 计算月份天数
	 *
	 * @return
	 */
	private Integer calcScope(LocalDateTime latestDate,
			CompositeIndexDef.Scope scope) {
		return switch (scope) {
			case LATEST_ONE_WEEK -> 7;
			case LATEST_ONE_MONTH -> 1 + Math.toIntExact(Duration.between(
					Objects.requireNonNull(DashboardDateUtils
							.calcBeginDate(DashboardDef.Scope.MONTH.getCode())),
					latestDate).toDays());
			case LATEST_ONE_YEAR -> 1 + Math.toIntExact(Duration.between(
					Objects.requireNonNull(DashboardDateUtils
							.calcBeginDate(DashboardDef.Scope.YEAR.getCode())),
					latestDate).toDays());
		};
	}

	/**
	 * 组装查询vo
	 */
	private List<OilSiteVo> packListVo(List<OilSite> records, Double lon,
			Double lat) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		List<OilSiteVo> oilSiteVos = new ArrayList<>();

		// 油站ids
		List<Long> oilSiteIds = records.stream().map(OilSite::getId).toList();
		// 根据油站ids查询油品指数
		List<OilIndex> oilIndexes = this.findByOilSiteIds(oilSiteIds);
		// 根据油站id进行分组，查出每个油站的最新的价格
		Map<Long, OilIndex> oilIndexMap = oilIndexes.stream()
				.collect(Collectors.groupingBy(OilIndex::getOilSiteId,
						Collectors.collectingAndThen(
								Collectors.maxBy(Comparator
										.comparing(OilIndex::getVersionDate)),
								optional -> optional.orElse(null))));

		// 计算每个站点距离用户的距离
		Map<Long, BigDecimal> distanceMap = oilSiteService.calcDistance(lon,
				lat, records);
		for (OilSite record : records) {
			OilSiteVo vo = new OilSiteVo();
			vo.setOilSite(record);
			vo.setDistance(Objects.nonNull(distanceMap.get(record.getId()))
					? distanceMap.get(record.getId())
					: BigDecimal.ZERO);
			OilIndex oilIndex = oilIndexMap.get(record.getId());
			if (Objects.nonNull(oilIndex)) {
				vo.setOilIndex(oilIndex);
			}
			oilSiteVos.add(vo);
		}
		// 再按距离顺序，最后按创建时间倒序
		return oilSiteVos.stream().sorted(Comparator
				.comparing(OilSiteVo::getDistance,
						Comparator.nullsLast(Comparator.naturalOrder()))
				.thenComparing(item -> item.getOilSite().getCreatedTime(),
						Comparator.nullsLast(Comparator.reverseOrder())))
				.toList();

	}
}
