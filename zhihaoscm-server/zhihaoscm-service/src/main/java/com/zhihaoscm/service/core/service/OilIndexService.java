package com.zhihaoscm.service.core.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.OilIndex;
import com.zhihaoscm.domain.bean.vo.OilIndexDetailVo;
import com.zhihaoscm.domain.bean.vo.OilIndexStatisticVo;
import com.zhihaoscm.domain.bean.vo.OilIndexVo;

/**
 * <p>
 * 油品指数 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface OilIndexService extends MpLongIdBaseService<OilIndex> {

	/**
	 *
	 * @param page
	 * @param size
	 * @param versionId
	 * @param sortKey
	 * @param sortOrder
	 * @param keyword
	 * @param brand
	 *            站点品牌
	 * @param provinceName
	 *            省份名称
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	Page<OilIndexVo> paging(Integer page, Integer size, Long versionId,
			String sortKey, String sortOrder, String keyword, String brand,
			String provinceName, LocalDateTime beginTime,
			LocalDateTime endTime);

	/**
	 * 小程序分页查询
	 * 
	 * @param page
	 * @param size
	 * @param provinceCode
	 * @param brand
	 * @return
	 */
	Page<OilIndexVo> customPaging(Integer page, Integer size,
			String provinceCode, String brand);

	/**
	 * 根据版本id查询
	 * 
	 * @param versionId
	 * @return
	 */
	List<OilIndex> findByVersionId(Long versionId);

	/**
	 * 根据版本id集合查询
	 *
	 * @param versionIds
	 * @return
	 */
	List<OilIndex> findByVersionIds(List<Long> versionIds);

	/**
	 * 根据油站id查询
	 *
	 * @return
	 */
	Optional<OilIndex> findByOilSiteId(Long oilSiteId);

	/**
	 * 根据油站ids查询
	 * 
	 * @param oilSiteIds
	 * @return
	 */
	List<OilIndex> findByOilSiteIds(List<Long> oilSiteIds);

	/**
	 * 根据日期查询 已发布数据
	 *
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<OilIndex> findByVersionDate(String provinceCode, String cityCode,
			String brand, LocalDateTime beginTime, LocalDateTime endTime);

	/**
	 * 根据日期查询 已发布数据
	 *
	 * @param versionDate
	 * @return
	 */
	List<OilIndex> findByVersionDate(LocalDateTime versionDate);

	/**
	 * 小程序端油品指数详情
	 *
	 * @param provinceCode
	 * @param cityCode
	 * @param brand
	 * @param lon
	 * @param lat
	 * @return 该区域和品牌最新指数信息 相关站点信息
	 */
	Optional<OilIndexDetailVo> detail(String provinceCode, String cityCode,
			String brand, Double lon, Double lat);

	/**
	 * 根据日期查询 已发布数据
	 *
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<OilIndex> findByVersionDate(Long oilSiteId, LocalDateTime beginTime,
			LocalDateTime endTime);

	/**
	 * 油品指数折线图
	 *
	 * @return
	 */
	Optional<OilIndexStatisticVo> line(Long oilSiteId, Integer scope);

	/**
	 * 区域油品指数折线图
	 * 
	 * @param provinceCode
	 * @param cityCode
	 * @param brand
	 * @param scope
	 * @return
	 */
	Optional<OilIndexStatisticVo> areaLine(String provinceCode, String cityCode,
			String brand, Integer scope);
}
