package com.zhihaoscm.service.resource.validator.device;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.dto.DeviceDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.vo.ShipMonitorShareVo;
import com.zhihaoscm.domain.meta.biz.DeviceDef;
import com.zhihaoscm.domain.meta.biz.ShipDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderShipDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.admin.RegexUtils;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.DeviceService;
import com.zhihaoscm.service.core.service.ShipMonitorShareService;
import com.zhihaoscm.service.core.service.TransportOrderShipService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.UserService;
import com.zhihaoscm.service.resource.form.device.DeviceBindingForm;
import com.zhihaoscm.service.resource.form.device.DeviceEditForm;
import com.zhihaoscm.service.resource.form.device.DeviceForm;
import com.zhihaoscm.service.resource.validator.ship.ShipValidator;

@Component
public class DeviceValidator {

	@Autowired
	private DeviceService service;

	@Autowired
	private ShipValidator shipValidator;

	@Autowired
	private UserService userValidator;

	@Autowired
	private TransportOrderShipService transportOrderShipService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private ShipMonitorShareService shipMonitorShareService;

	/**
	 * 新增校验
	 *
	 * @param deviceForm
	 * @return
	 */
	public void validateCreate(DeviceForm deviceForm) {
		DeviceDef.Type type = DeviceDef.Type.from(deviceForm.getType());
		if (Objects.isNull(type)) {
			throw new BadRequestException(ErrorCode.CODE_30037025);
		}
		if (DeviceDef.Type.CAMERA.getCode().equals(deviceForm.getType())) {
			// 类型位摄像头 序列号默认9位
			if (!RegexUtils.match(RegexUtils.CAMERA_SERIAL_NO_PATTERN,
					deviceForm.getSerialNo())) {
				throw new BadRequestException(ErrorCode.CODE_30037008);
			}
			this.validateBind(null, deviceForm.getMasterId(),
					deviceForm.getTransportType(), deviceForm.getPosition());
		}

		// 校验序列号唯一性
		Device serialDevice = service.findBySerialNo(deviceForm.getSerialNo())
				.orElse(null);
		// 校验数据卡号唯一性
		Device dataCardDevice = service.findByDataCard(deviceForm.getDataCard())
				.orElse(null);

		if (Objects.nonNull(serialDevice)) {
			throw new BadRequestException(ErrorCode.CODE_30037013);
		}
		if (Objects.nonNull(dataCardDevice)) {
			throw new BadRequestException(ErrorCode.CODE_30037012);
		}
		if (Objects.nonNull(deviceForm.getInstallerInfo())) {
			userValidator
					.validateIsExist(deviceForm.getInstallerInfo().getId());
		}
	}

	/**
	 * 校验绑定
	 *
	 * @param masterId
	 * @param transportType
	 * @param position
	 */
	private void validateBind(Long id, String masterId, Integer transportType,
			String position) {
		if (StringUtils.isNotBlank(masterId)) {
			if (Objects.isNull(transportType)) {
				throw new BadRequestException(ErrorCode.CODE_30037017);
			}
			if (StringUtils.isBlank(position)) {
				throw new BadRequestException(ErrorCode.CODE_30037018);
			}
			if (position.length() > 20) {
				throw new BadRequestException(ErrorCode.CODE_30037028);
			}
			Ship ship = shipValidator.validateExist(masterId);
			if (!ShipDef.State.AUTHENTICATED.match(ship.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30037029);
			}
			// 查询船舶关联的设备
			long count = service
					.findByMasterIdAndTransportType(masterId, transportType)
					.stream().filter(item -> Objects.nonNull(item)
							&& !Objects.equals(item.getId(), id))
					.count();
			if (count > 0) {
				throw new BadRequestException(ErrorCode.CODE_30037045);
			}
		}
	}

	/**
	 * 修改校验
	 *
	 * @param id
	 * @param form
	 * @return
	 */
	public DeviceDto validateEdit(Long id, DeviceEditForm form) {
		DeviceDto deviceDto = new DeviceDto();
		Device device = this.validateExist(id);
		Device dataCard = service.findByDataCard(form.getDataCard())
				.orElse(null);
		// 修改时校验
		if (Objects.nonNull(dataCard)
				&& !Objects.equals(dataCard.getId(), id)) {
			throw new BadRequestException(ErrorCode.CODE_30037012);
		}
		if (DeviceDef.Type.CAMERA.match(device.getType())) {
			this.validateBind(id, form.getMasterId(), form.getTransportType(),
					form.getPosition());
		}
		// 修改前设备绑定了运输工具，并且修改后表单没有运输工具 说明设备与运输工具解绑 需要删除船舶对应的船舶监控调阅分享记录
		if (Objects.nonNull(device.getMasterId())
				&& StringUtils.isBlank(form.getMasterId())) {
			deviceDto.setShipId(device.getMasterId());
		}
		deviceDto.setDevice(device);
		return deviceDto;
	}

	/**
	 * 验证设备是否存在
	 *
	 * @param id
	 * @return
	 */
	public Device validateExist(Long id) {
		Device device = service.findOne(id).orElse(null);
		if (Objects.isNull(device)) {
			throw new BadRequestException(ErrorCode.CODE_30037010);
		}
		return device;
	}

	/**
	 * 校验删除
	 *
	 * @param id
	 */
	public Device validateDelete(Long id) {
		Device device = this.validateExist(id);
		// 被船舶绑定不能删除
		if (Objects.nonNull(device.getMasterId())) {
			throw new BadRequestException(ErrorCode.CODE_30037024);
		}
		return device;
	}

	/**
	 * 启用或禁用设备校验
	 *
	 * @param id
	 */
	public void validateUpdateState(Long id, Integer status) {
		Device device = this.validateExist(id);
		// 启用 状态为禁用才能启用
		if (DeviceDef.OperationType.ENABLED.getCode().equals(status)) {
			if (!device.getState().equals(DeviceDef.State.DISABLED.getCode())) {
				throw new BadRequestException(ErrorCode.CODE_30037014);
			}
		} else {
			// 禁用 状态为启用才能禁用
			if (!DeviceDef.State.ENABLE.match(device.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30037015);
			}
		}
	}

	/**
	 * 校验绑定设备
	 *
	 * @param form
	 * @return
	 */
	public Device validateBindingDevice(DeviceBindingForm form) {
		Device device = service.findOne(form.getId()).orElse(null);
		if (Objects.isNull(device)) {
			throw new BadRequestException(ErrorCode.CODE_30037010);
		}
		// 船舶绑定的设备
		Device deviceBinding = service
				.findByMasterIdAndTransportType(form.getMasterId(),
						form.getTransportType())
				.stream().findFirst().orElse(null);
		// 绑定设备
		if (CommonDef.Symbol.YES.match(form.getFlag())) {
			if (Objects.isNull(form.getMasterId())) {
				throw new BadRequestException(ErrorCode.CODE_30037016);
			}
			if (Objects.isNull(form.getTransportType())) {
				throw new BadRequestException(ErrorCode.CODE_30037017);
			} else {
				// 有绑定的设备且绑定的不是当前的则抛异常
				if (Objects.nonNull(deviceBinding) && !(form.getMasterId()
						.equals(deviceBinding.getMasterId()))) {
					throw new BadRequestException(ErrorCode.CODE_30037045);
				}
			}
			device.setMasterId(form.getMasterId());
			device.setTransportType(form.getTransportType());
		} else {
			if (Objects.isNull(form.getMasterId())) {
				throw new BadRequestException(ErrorCode.CODE_30037016);
			}
			if (Objects.isNull(form.getTransportType())) {
				throw new BadRequestException(ErrorCode.CODE_30037017);
			}
			if (StringUtils.isBlank(device.getMasterId())) {
				throw new BadRequestException(ErrorCode.CODE_30037049);
			}
			if (!device.getMasterId().equals(form.getMasterId())) {
				throw new BadRequestException(ErrorCode.CODE_30037046);
			}
			// 解绑
			device.setMasterId(null);
			device.setPosition(null);
			device.setTransportType(null);
		}
		return device;
	}

	/**
	 * 校验设备序列号
	 *
	 * @param deviceSerial
	 */
	public void validatePlay(String deviceSerial) {
		Device device = service.findBySerialNo(deviceSerial).orElse(null);
		if (Objects.isNull(device)) {
			throw new BadRequestException(ErrorCode.CODE_30037010);
		}
		if (DeviceDef.State.DISABLED.match(device.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30037019);
		}
		if (DeviceDef.Online.OFFLINE.match(device.getOnline())) {
			throw new BadRequestException(ErrorCode.CODE_30037026);
		}
	}

	/**
	 * 校验设备序列号
	 *
	 * @param deviceSerial
	 */
	public void validateCustomPlay(String deviceSerial) {
		Device device = service.findBySerialNo(deviceSerial).orElse(null);
		if (Objects.isNull(device)) {
			throw new BadRequestException(ErrorCode.CODE_30037010);
		}
		if (DeviceDef.State.DISABLED.match(device.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30037026);
		}
		if (DeviceDef.Online.OFFLINE.match(device.getOnline())) {
			throw new BadRequestException(ErrorCode.CODE_30037019);
		}
		if (StringUtils.isBlank(device.getMasterId())) {
			return;
		}
		// 承运商可以查看船舶监控
		Ship ship = shipValidator.validateExist(device.getMasterId());
		if (CustomerContextHolder.getCustomerLoginVo().getProxyAccount().getId()
				.equals(ship.getCarrier())) {
			return;
		}
		// 被分享的用户也可以查看出船舶监控
		ShipMonitorShareVo shipMonitorShareVo = shipMonitorShareService
				.findVoByCustomerIdAndShipId(CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount().getId(),
						ship.getId())
				.orElse(null);
		if (Objects.nonNull(shipMonitorShareVo)) {
			return;
		}
		// 判断用户当前是否有查看监控的权限
		Customer customer = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount();
		MembershipLevel membershipLevel = customerService
				.getMembershipLevel(customer);
		if (Objects.nonNull(membershipLevel)) {
			if (CommonDef.Symbol.NO.getCode()
					.equals(membershipLevel.getFeature().getQueryMonitor())) {
				// 当前用户没有查看监控的权限
				throw new BadRequestException(ErrorCode.CODE_30099017);
			}

		}
		// 根据船舶id查询船运单
		List<TransportOrderShip> transportOrderShipList = transportOrderShipService
				.findByShipId(ship.getId(), null);
		for (TransportOrderShip transportOrderShip : transportOrderShipList) {
			// 如果是船长可以播放 货主且船运单是非完成状态才能播放
			if ((CustomerContextHolder.getCustomerLoginVo().getProxyAccount()
					.getId().equals(transportOrderShip.getOwnerId())
					&& !TransportOrderShipDef.State.COMPLETED
							.match(transportOrderShip.getState()))

					|| (CustomerContextHolder.getCustomerLoginVo()
							.getProxyAccount().getId()
							.equals(transportOrderShip.getCaptainId()))) {
				return;
			}
		}
		throw new BadRequestException(ErrorCode.CODE_404);
	}
}
