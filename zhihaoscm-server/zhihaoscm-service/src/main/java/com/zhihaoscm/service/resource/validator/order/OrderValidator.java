package com.zhihaoscm.service.resource.validator.order;

import java.math.BigDecimal;
import java.util.Objects;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerReceivingAddress;
import com.zhihaoscm.domain.bean.entity.Order;
import com.zhihaoscm.domain.bean.entity.ProductConsignment;
import com.zhihaoscm.domain.bean.json.AddressInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.ProductInfo;
import com.zhihaoscm.domain.meta.biz.OrderDef;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.OrderService;
import com.zhihaoscm.service.core.service.usercenter.CustomerReceivingAddressService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.resource.form.order.OrderForm;
import com.zhihaoscm.service.resource.validator.product.consignment.ProductConsignmentValidator;

/**
 * 订单校验器
 */
@Component
public class OrderValidator {

	@Autowired
	private OrderService service;

	@Autowired
	private CustomerReceivingAddressService customerReceivingAddressService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private ProductConsignmentValidator productConsignmentValidator;

	/**
	 * 校验是否存在
	 * 
	 * @param id
	 * @return
	 */
	public Order validateExist(String id) {
		return service.findOne(id)
				.orElseThrow(() -> new BadRequestException("订单不存在"));
	}

	/**
	 * 校验取消订单
	 * 
	 * @param id
	 * @return
	 */
	public Order validateCancel(String id) {
		Order order = this.validateHandle(id);
		if (!(OrderDef.State.OBLIGATION.match(order.getState())
				&& OrderDef.State.TO_BE_PICKED_UP.match(order.getState()))) {
			throw new BadRequestException("待付款和待提货状态才能取消");
		}
		return order;
	}

	/**
	 * 校验处理
	 * 
	 * @param id
	 * @return
	 */
	public Order validateHandle(String id) {
		Order order = this.validateExist(id);
		if (!Objects.requireNonNull(UserContextHolder.getUser()).getId()
				.equals(order.getHandlerId())) {
			throw new BadRequestException("专员才能进行处理");
		}
		return order;
	}

	/**
	 * 校验完成订单
	 * 
	 * @param id
	 * @return
	 */
	public Order validateFinish(String id) {
		Order order = this.validateExist(id);
		if (!OrderDef.State.IN_THE_PROCESS_OF_PICKING_UP_THE_GOODS
				.match(order.getState())) {
			throw new BadRequestException("提货中状态才能完成");
		}
		return order;
	}

	/**
	 * 校验删除
	 * 
	 * @param id
	 */
	public void validateDelete(String id, Integer userType) {
		Order order = this.validateExist(id);
		if (CommonDef.UserType.INNER.match(userType)) {
			if (!(OrderDef.State.COMPLETED.match(order.getState())
					&& OrderDef.State.CANCELLED.match(order.getState()))) {
				throw new BadRequestException("已完成和已取消状态才能完成");
			}
		}
	}

	/**
	 * 校验创建
	 * 
	 * @param form
	 * @return
	 */
	public Order validateCreate(OrderForm form) {
		// todo 判断吨数商品
		Order order = form.convertToEntity();
		switch (OrderDef.OrderType.from(form.getOrderType())) {
			case CONSIGNMENT_PRODUCTS -> {
				ProductConsignment productConsignment = productConsignmentValidator
						.validateExist(form.getProductId());
				// 数量大于起订减调整 小于库存 小于起订加调整
				Integer minOrderTon = productConsignment.getMinOrderTon();
				Integer minAdjustmentTon = productConsignment
						.getMinAdjustmentTon();
				Integer inventoryQuantity = productConsignment
						.getInventoryQuantity();

				int minNum = minOrderTon - minAdjustmentTon;
				int maxNum = minOrderTon + minAdjustmentTon;

				if (!(form.getTon() > minNum)) {
					throw new BadRequestException("吨数不能小于起订减调整");
				}
				if (!(form.getTon() < maxNum)) {
					throw new BadRequestException("吨数不能大于起订加调整");
				}
				if (!(form.getTon() < inventoryQuantity)) {
					throw new BadRequestException("吨数不能大于库存");
				}
				ProductInfo productInfo = new ProductInfo();
				BeanUtils.copyProperties(productConsignment, productInfo);
				order.setProductInfo(productInfo);
				order.setUnitPrice(productConsignment.getUnitPrice());

				order.setEstimatedAmount(productConsignment.getUnitPrice()
						.multiply(new BigDecimal(form.getTon())));
			}
			case GROUP_BUYING_PRODUCTS -> {
				// 单价是拼团价
			}
			case PICKING_UP_MISSING_PRODUCTS -> {
				// 单价是捡漏价
			}
		}
		CustomerReceivingAddress customerReceivingAddress = customerReceivingAddressService
				.findById(form.getAddressId());
		if (Objects.isNull(customerReceivingAddress)) {
			throw new BadRequestException("收货地址不存在");
		}

		// 地址信息
		AddressInfo addressInfo = new AddressInfo();
		BeanUtils.copyProperties(customerReceivingAddress, addressInfo);
		order.setAddressInfo(addressInfo);
		// 企业信息
		Customer customer = customerService
				.findOne(CustomerContextHolder.getCustomerLoginVo()
						.getProxyAccount().getId())
				.orElseThrow(() -> new BadRequestException("用户不存在"));
		Enterprise enterprise = new Enterprise();
		BeanUtils.copyProperties(customer, enterprise);
		order.setCustomerEnterprise(enterprise);
		order.setCustomerId(CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId());

		return order;
	}
}
