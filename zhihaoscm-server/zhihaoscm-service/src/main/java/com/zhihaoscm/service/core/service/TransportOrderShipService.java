package com.zhihaoscm.service.core.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.TransportOrderShipAIRes;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.bean.entity.TransportOrderDetailsShip;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.entity.User;
import com.zhihaoscm.domain.bean.vo.*;

/**
 * <p>
 * 船运单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface TransportOrderShipService
		extends MpStringIdBaseService<TransportOrderShip> {

	/**
	 * 分页查询
	 *
	 * @param page
	 * @param size
	 * @param sortKey
	 * @param sortOrder
	 * @param keyword
	 * @param portKeyword
	 * @param shipKeyword
	 * @param handlerName
	 * @param type
	 * @param state
	 * @param hasFull
	 * @param userId
	 * @param customerId
	 * @return
	 */
	Page<TransportOrderShipVo> paging(Integer page, Integer size,
			String sortKey, String sortOrder, String keyword,
			String portKeyword, String shipKeyword, String handlerName,
			String type, List<Integer> state, LocalDateTime beginTime,
			LocalDateTime endTime, boolean hasFull, Long userId,
			Long customerId);

	/**
	 * 小程序端分页查询
	 *
	 * @param page
	 * @param size
	 * @param keyword
	 * @param queryType
	 *            用户类型（货主，船主）
	 * @param customerId
	 *            登陆用户id
	 * @param state
	 * @param destinationPortName
	 * @param sourcePortName
	 * @param isNotCompleted
	 *            是否未完成
	 * @return
	 */
	com.zhihaoscm.common.bean.page.Page<TransportOrderShipVo> customPaging(
			Integer page, Integer size, String keyword, Integer queryType,
			Long customerId, Integer state, String sourcePortName,
			String destinationPortName, Boolean isNotCompleted);

	/**
	 * 下拉船运单
	 *
	 * @param keyword
	 * @param customerId
	 * @return
	 */
	List<TransportOrderShip> selector(String keyword, Long customerId);

	/**
	 * 船运单列表
	 *
	 */
	List<TransportOrderShip> selectOrders(Long customerId);

	/**
	 * 下拉船运单
	 *
	 * 
	 * @param keyword
	 * @param customerId
	 * @return
	 */
	List<TransportOrderShipVo> selectorVo(String keyword, Long customerId);

	/**
	 * 服务专员下拉
	 *
	 * @param keyword
	 * @return
	 */
	List<User> specialsSelector(String keyword);

	/**
	 * 查询船运单详情
	 *
	 * @param id
	 * @return
	 */
	Optional<TransportOrderShipVo> findVoById(String id);

	/**
	 * 查询服务专员
	 *
	 * @param serviceSpecialsType
	 *            服务专员类型 区分上下游
	 * @return
	 */
	List<ServiceSpecialVo> findServiceSpecials(Integer serviceSpecialsType);

	/**
	 * 根据船主id查询船运单
	 *
	 * @param shipId
	 * @param notInStates
	 *            排除的状态
	 * @return
	 */
	List<TransportOrderShip> findByShipId(String shipId,
			List<Integer> notInStates);

	/**
	 * 根据船主接单id查询船运单
	 *
	 * @param sraId
	 * @return
	 */
	List<TransportOrderShipVo> findVoBySraId(Long sraId);

	/**
	 * 根据船主接单ids查询船运单
	 * 
	 * @param sraIds
	 * @return
	 */
	List<TransportOrderShipVo> findVoBySraIds(List<Long> sraIds);

	/**
	 *
	 * @param captainId
	 * @param notInStates
	 *            排除的状态
	 * @return
	 */
	List<TransportOrderShip> findByCaptainId(Long captainId,
			List<Integer> notInStates);

	/**
	 *
	 *
	 * @param notInStates
	 *            不在其中的状态
	 * @return
	 */
	List<TransportOrderShip> findNotInStates(List<Integer> notInStates);

	/**
	 * 根据平台船运需求id查询船运单
	 *
	 * @param id
	 * @return
	 */
	List<TransportOrderShip> findBySrpId(String id);

	/**
	 * 根据平台船运需求id查询船运单
	 *
	 * @param srcId
	 * @return
	 */
	List<TransportOrderShipVo> findVoBySrpId(String srcId);

	/**
	 * 根据船舶id和状态查询船运单
	 *
	 * @param shipIds
	 * @param state
	 * @return
	 */
	List<TransportOrderShip> findByShipIdsAndState(Set<String> shipIds,
			Integer state);

	/**
	 * 查询吨数
	 *
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	Optional<Integer> findTon(LocalDateTime beginTime, LocalDateTime endTime);

	/**
	 * 查询金额
	 *
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	Optional<BigDecimal> findAmount(LocalDateTime beginTime,
			LocalDateTime endTime);

	/**
	 * 根据船舶id查询未完成的船运单信息
	 *
	 * @param shipIds
	 * @return
	 */
	List<TransportOrderShip> findIncompleteByShipIds(Set<String> shipIds);

	/**
	 * 根据创建起始时间查询船运单列表
	 *
	 * @return
	 */
	List<TransportOrderShip> findByTime(LocalDateTime startTime,
			LocalDateTime finishTime, String shipId);

	/**
	 * 根据ID查询且appID为空的数据
	 *
	 * @param id
	 * @return
	 */
	Optional<TransportOrderShipVo> findVoByIdAndAppIsEmpty(String id);

	/**
	 * 查询货主历史船运单信息
	 *
	 * @param customerId
	 * @param startPort
	 * @param endPort
	 * @param goodsType
	 * @param ton
	 * @return
	 */
	List<TransportOrderShipAIRes> findHistoryShipOrders(Long customerId,
			String startPort, String endPort, String goodsType, Integer ton);

	/**
	 * 根据船运单ID查询轨迹
	 * 
	 * @param id
	 * @return
	 */
	List<ShipTrajectoryVo> findAisTrackById(String id);

	/**
	 * 查询船运单数量
	 *
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	Long count(LocalDateTime beginTime, LocalDateTime endTime,
			Boolean isNotRelatedPlat);

	/**
	 * 创建船运单 （抢单成功调用这个)
	 *
	 */
	TransportOrderShip buildTransportOrderShip(
			TransportOrderShip transportOrderShip);

	/**
	 * 更新状态
	 *
	 * @param id
	 * @param state
	 */
	void updateState(String id, Integer state);

	/**
	 * 更新状态
	 *
	 * @param id
	 * @param state
	 */
	void updateStateByIds(List<String> id, Integer state);

	/**
	 * 货主发航确认 同时判断上传发船信息 上传则进行状态流转
	 *
	 * @param transportOrderDetailsShip
	 */
	void confirmationDeparture(
			TransportOrderDetailsShip transportOrderDetailsShip);

	/**
	 * 管理后台代替货主发航确认 同时判断上传发船信息 上传则进行状态流转
	 *
	 * @param transportOrderDetailsShip
	 */
	void adminConfirmationDeparture(
			TransportOrderDetailsShip transportOrderDetailsShip);

	/**
	 * 同意卸货 同时判断上传卸货信息 上传则进行状态流转
	 *
	 * @param transportOrderDetailsShip
	 */
	void agreeUnload(TransportOrderDetailsShip transportOrderDetailsShip);

	/**
	 * 同意卸货 同时判断上传卸货信息 上传则进行状态流转
	 *
	 * @param transportOrderDetailsShip
	 */
	void adminAgreeUnload(TransportOrderDetailsShip transportOrderDetailsShip);

	/**
	 * 到港确认
	 *
	 * @param id
	 * @param arrivalVideoId
	 */
	void adminConfirmationArrivalPort(String id, Long arrivalVideoId);

	/**
	 * 到港确认
	 *
	 * @param id
	 * @param arrivalVideoId
	 */
	void confirmationArrivalPort(String id, Long arrivalVideoId);

	/**
	 * 指派服务专员
	 *
	 * @param id
	 * @param upstreamHandlerId
	 * @param downstreamHandlerId
	 */
	void assign(String id, Long upstreamHandlerId, Long downstreamHandlerId);

	/**
	 * 统计船运单需求
	 *
	 * @param hasFull
	 *            管理权限
	 *
	 * @param hasDeal
	 *            处理权限
	 */
	TransportOrderShipCountVo staticsTransportOrderShip(Long userId,
			Boolean hasDeal, Boolean hasFull);

	/**
	 * 查询热门航线
	 *
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<HotShipRouteVo> hotShipRouteRank(LocalDateTime beginTime,
			LocalDateTime endTime);

	/**
	 * 查询热门目的港
	 *
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<HotShipDestinationPortVo> hotShipDestinationPortRank(
			LocalDateTime beginTime, LocalDateTime endTime);

	/**
	 * 船主确认定金(流转状态)
	 *
	 * @param transportOrderShip
	 */
	void confirm(TransportOrderShip transportOrderShip);

	/**
	 * 船主更新收款账户
	 *
	 * @param transportOrderShip
	 */
	void updateBank(TransportOrderShip transportOrderShip, Long bankId);

	/**
	 * 关闭船运单
	 *
	 * @param id
	 */
	@Override
	void delete(String id);

	/**
	 * 开始装货
	 *
	 * @param id
	 * @param transportOrderDetailsShip
	 */
	void startLoading(String id,
			TransportOrderDetailsShip transportOrderDetailsShip);

	/**
	 * 处理发航确认（主要逻辑是修改状态 发送通知）
	 *
	 * @param transportOrderShip
	 */
	void handleConfirmationDeparture(TransportOrderShip transportOrderShip,
			Integer drainage);

	/**
	 * 处理同意卸货（主要逻辑是修改状态 发送通知）
	 */
	void handleAgreeUnload(TransportOrderShip transportOrderShip);

	/**
	 * 黄码港回调处理
	 * 
	 * @param params
	 */
	void callback(Map<String, Object> params);

	/**
	 * 调用hmg更新运单信息接口
	 *
	 * @param transportOrderShip
	 */
	void updateWaybill(TransportOrderShip transportOrderShip);

	/**
	 * 长传图片带水印
	 * 
	 * @param file
	 * @param transportOrderShip
	 * @return
	 */
	File uploadWatermark(MultipartFile file,
			TransportOrderShip transportOrderShip) throws Exception;
}
