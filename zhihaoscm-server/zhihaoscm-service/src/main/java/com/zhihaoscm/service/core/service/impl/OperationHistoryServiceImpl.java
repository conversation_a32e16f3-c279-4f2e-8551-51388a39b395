package com.zhihaoscm.service.core.service.impl;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.OperationHistory;
import com.zhihaoscm.service.core.mapper.OperationHistoryMapper;
import com.zhihaoscm.service.core.service.OperationHistoryService;

/**
 * <p>
 * 操作历史 服务实现类
 * </p>
 *
 */
@Service
public class OperationHistoryServiceImpl extends
		MpLongIdBaseServiceImpl<OperationHistory, OperationHistoryMapper>
		implements OperationHistoryService {
	public OperationHistoryServiceImpl(OperationHistoryMapper repository) {
		super(repository);
	}

	@Override
	public List<OperationHistory> findByModule(Integer module, String bizNo,
			String sortKey, String sortOrder) {
		LambdaQueryWrapper<OperationHistory> queryWrapper = Wrappers
				.lambdaQuery(OperationHistory.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Objects.nonNull(module),
				OperationHistory::getOperationModule, module);
		queryWrapper.eq(Objects.nonNull(bizNo), OperationHistory::getBizNo,
				bizNo);
		// 排序
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			queryWrapper.orderByAsc(OperationHistory::getCreatedTime);
		}

		return repository.selectList(queryWrapper);
	}

	@Override
	public List<OperationHistory> findByModuleOfShip(Integer module,
			String bizNo) {
		LambdaQueryWrapper<OperationHistory> queryWrapper = Wrappers
				.lambdaQuery(OperationHistory.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Objects.nonNull(module),
				OperationHistory::getOperationModule, module);
		queryWrapper.eq(Objects.nonNull(bizNo), OperationHistory::getBizNo,
				bizNo);
		List<OperationHistory> operationHistories = repository
				.selectList(queryWrapper);
		return operationHistories.stream()
				.sorted(Comparator.comparing(OperationHistory::getCreatedTime))
				.toList();
	}
}
