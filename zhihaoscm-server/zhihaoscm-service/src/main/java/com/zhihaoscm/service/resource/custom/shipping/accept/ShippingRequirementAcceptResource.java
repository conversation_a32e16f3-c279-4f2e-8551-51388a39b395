package com.zhihaoscm.service.resource.custom.shipping.accept;

import java.time.LocalDate;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementAccept;
import com.zhihaoscm.domain.bean.vo.ShippingRequirementAcceptVo;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.ShippingRequirementAcceptService;
import com.zhihaoscm.service.core.service.ShippingRequirementPlatService;
import com.zhihaoscm.service.core.service.usercenter.CustomerBankService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.resource.form.shipping.accept.CustomShippingRequirementAcceptForm;
import com.zhihaoscm.service.resource.validator.shipping.accept.ShippingRequirementAcceptValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "船主接单平台船运需求记录", description = "船主接单平台船运需求记录API")
@RestController
@RequestMapping("/shipping/accept")
public class ShippingRequirementAcceptResource {

	@Autowired
	private ShippingRequirementAcceptValidator validator;

	@Autowired
	private ShippingRequirementAcceptService service;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private CustomerBankService customerBankService;

	@Autowired
	private ShippingRequirementPlatService shippingRequirementPlatService;

	@Operation(summary = "承运商接单列表分页查询")
	@GetMapping(value = "/custom/paging")
	public ApiResponse<Page<ShippingRequirementAcceptVo>> customPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_MINI_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "船运需求id") @RequestParam(required = false, name = "keyword") String keyword,
			@Parameter(description = "始发港id") @RequestParam(required = false, name = "sourcePortId") Long sourcePortId,
			@Parameter(description = "目的港id") @RequestParam(required = false, name = "destinationPortId") Long destinationPortId,
			@Parameter(description = "装载日期起") @RequestParam(required = false, name = "loadDateStart") LocalDate loadDateStart,
			@Parameter(description = "装载日期止") @RequestParam(required = false, name = "loadDateEnd") LocalDate loadDateEnd,
			@Parameter(description = "状态") @RequestParam(required = false, name = "state") Integer state) {

		return new ApiResponse<>(PageUtil.convert(service.customPaging(page,
				size, keyword, sourcePortId, destinationPortId, loadDateStart,
				loadDateEnd, state, Objects.requireNonNull(CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount().getId()))));
	}

	@Operation(summary = "查询承运商接单需求详情")
	@GetMapping(value = "/{id}")
	public ApiResponse<ShippingRequirementAccept> findById(
			@PathVariable(value = "id") Long id) {
		return new ApiResponse<>(service.findOne(id).orElse(null));
	}

	@Operation(summary = "查询承运商接单需求详情")
	@GetMapping(value = "find/vo/{id}")
	public ApiResponse<ShippingRequirementAcceptVo> findVoById(
			@PathVariable(value = "id") Long id) {
		return new ApiResponse<>(service.findVoById(id).orElse(null));
	}

	@Operation(summary = "承运商接单")
	@PostMapping(value = "/taking/orders")
	public ApiResponse<Void> takingOrders(
			@Validated @RequestBody CustomShippingRequirementAcceptForm form) {
		validator.validateCreate(form);
		service.takingOrders(form.convertToEntity(
				Objects.requireNonNull(CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount()),
				customerService, customerBankService,
				shippingRequirementPlatService));
		return new ApiResponse<>();
	}

	@Operation(summary = "修改承运商接单信息")
	@PutMapping(value = "/update-accept/{id}")
	public ApiResponse<Void> update(@PathVariable Long id,
			@Validated @RequestBody CustomShippingRequirementAcceptForm form) {
		ShippingRequirementAccept accept = validator.validateUpdate(id, form);
		service.update(form.convertToEntity(id, customerBankService, accept));
		return new ApiResponse<>();
	}

	@Operation(summary = "取消承运商接单需求")
	@PutMapping(value = "/cancel/{id}")
	public ApiResponse<Void> cancel(@PathVariable(value = "id") Long id) {
		validator.validateCancel(id);
		service.cancel(id);
		return new ApiResponse<>();
	}

}
