package com.zhihaoscm.service.resource.admin.banner;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.domain.bean.entity.Banner;
import com.zhihaoscm.domain.bean.vo.BannerVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.BannerDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.service.core.service.BannerService;
import com.zhihaoscm.service.resource.form.banner.BannerForm;
import com.zhihaoscm.service.resource.validator.banner.BannerValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "轮播图管理", description = "轮播图管理API")
@RestController
@RequestMapping("/banner")
public class BannerResource {

	@Autowired
	private BannerService service;

	@Autowired
	private BannerValidator validator;

	@Operation(summary = "轮播图列表")
	@GetMapping(value = "/findList")
	@Secured(value = AdminPermissionDef.AD_R)
	public ApiResponse<List<Banner>> findList(
			@Parameter(description = "位置 1 链云砂石 4 链云船务") @RequestParam(value = "type") Integer pos) {
		return new ApiResponse<>(service.selector(pos));
	}

	@Operation(summary = "根据id查询轮播图")
	@GetMapping(value = "/{id}")
	@Secured(value = AdminPermissionDef.AD_R)
	public ApiResponse<BannerVo> findById(@PathVariable(value = "id") Long id) {
		validator.validateExist(id);
		return new ApiResponse<>(service.findVoById(id).orElse(null));
	}

	@Operation(summary = "新增轮播图")
	@PostMapping
	@Secured(value = AdminPermissionDef.AD_W)
	public ApiResponse<Banner> create(@Validated @RequestBody BannerForm form) {
		validator.validateCreate(form);
		return new ApiResponse<>(service.create(form.convertToEntity()));
	}

	@Operation(summary = "修改轮播图")
	@PutMapping(value = "{id}")
	@Secured(value = AdminPermissionDef.AD_W)
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.APP_AD_MODIFY, type = LogDef.PLATFORM_MANAGEMENT_ADVERTISING_MANAGEMENT_APP, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#value}}") })
	public ApiResponse<Banner> update(@PathVariable(value = "id") Long id,
			@Validated @RequestBody BannerForm form) {
		Banner banner = validator.validateUpdate(id, form);
		Banner update = service.update(form.convertToEntity(banner));
		BannerDef.Pos pos = BannerDef.Pos.from(banner.getPos());
		LogRecordContext.putVariable("value", pos.getName());
		return new ApiResponse<>(update);
	}

	@Operation(summary = "删除轮播图")
	@DeleteMapping(value = "/{id}")
	@Secured(value = AdminPermissionDef.AD_W)
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.DELETE, success = LogDef.APP_AD_DELETE, type = LogDef.PLATFORM_MANAGEMENT_ADVERTISING_MANAGEMENT_APP, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#value}}") })
	public ApiResponse<Void> delete(@PathVariable(value = "id") Long id) {
		Banner banner = validator.validateExist(id);
		BannerDef.Pos pos = BannerDef.Pos.from(banner.getPos());
		LogRecordContext.putVariable("value", pos.getName());
		service.delete(id);
		return new ApiResponse<>();
	}
}
