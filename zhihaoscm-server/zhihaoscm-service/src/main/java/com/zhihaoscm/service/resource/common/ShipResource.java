package com.zhihaoscm.service.resource.common;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.GeoPoint;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.resource.MpStringIdBaseResource;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.Device;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.meta.biz.DeviceDef;
import com.zhihaoscm.domain.meta.biz.ShipDef;
import com.zhihaoscm.service.core.service.DeviceService;
import com.zhihaoscm.service.core.service.ShipService;
import com.zhihaoscm.service.resource.form.ship.ShipExtendCustomForm;
import com.zhihaoscm.service.resource.validator.ship.ShipValidator;
import com.zhihaoscm.ships66.sdk.response.ShipInfo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * <p>
 * 船舶 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@RestController
@RequestMapping("/ship")
public class ShipResource extends MpStringIdBaseResource<Ship, ShipService> {

	public ShipResource(ShipService service) {
		super(service);
	}

	@Autowired
	private DeviceService deviceService;
	@Autowired
	private ShipValidator validator;

	@PostMapping("/updateWarehouseState")
	public void updateWarehouseState() {
		service.updateWarehouseState();
	}

	@PostMapping("/testUpdateWarehouseState")
	public void testUpdateWarehouseState() {
		service.testUpdateWarehouseState();
	}

	@GetMapping(value = "/keyword")
	public List<Ship> findByKeyword(
			@Parameter(description = "船舶关键字") @RequestParam String keyword) {
		return service.findByKeyword(keyword);
	}

	@GetMapping(value = "/dynamic-info")
	public ShipInfoVo findByDynamicInfo(
			@Parameter(description = "船舶mmsi") @RequestParam String shipId) {
		validator.validateMMSI(shipId);
		ShipInfo shipInfo = service.findByAsi(shipId);
		return service
				.findByIdAndCreate(shipInfo, null,
						ShipDef.MonitoringChannels.BACKGROUND.getCode())
				.orElse(null);
	}

	@Operation(summary = "自定义查船")
	@PostMapping(value = "/extent/custom")
	public List<ShipInfoVo> findByCustomExtent(
			@RequestBody ShipExtendCustomForm from) {
		validator.validateExtentCustom(from);
		List<GeoPoint> geoPointList = from.getGeoPointList();
		return service.findByCustomExtent(geoPointList, from.getType(),
				from.getTime());
	}

	@GetMapping(value = "/ship-route-info")
	public List<ShipTrajectoryVo> findByShipRouteInfo(
			@Parameter(description = "船舶mmsi") @RequestParam(value = "shipId") String shipId,
			@Parameter(description = "开始时间戳") @RequestParam(value = "start") Long start,
			@Parameter(description = "结束时间戳") @RequestParam(value = "end") Long end) {
		return service.findByAisTrack(shipId, start, end);
	}

	@GetMapping(value = "/ship-device-info")
	public Device findByFundShipDeviceInfo(
			@Parameter(description = "船舶mmsi") @RequestParam(value = "shipId") String shipId) {
		return deviceService
				.findByMasterIdAndTransportType(shipId,
						DeviceDef.TransportType.SHIP.getCode())
				.stream().findFirst().orElse(null);
	}

	@GetMapping("/account/info")
	public AccountInfoVo findAccountInfoVo() {
		return deviceService.findAccountInfoVo().orElse(null);
	}

	@GetMapping(value = "/capture")
	public String capture(
			@Parameter(description = "船舶mmsi") @RequestParam(value = "shipId") String shipId,
			@Parameter(description = "通道号") @RequestParam(value = "channelNo", defaultValue = "1") Integer channelNo,
			@Parameter(description = "视频清晰度") @RequestParam(value = "quality", required = false) Integer quality) {
		String deviceSerial = validator.validateCustomPlay(shipId);
		return deviceService.capture(deviceSerial, channelNo, quality)
				.orElse(null);
	}

	@GetMapping(value = "/video")
	public DeviceVo video(
			@Parameter(description = "船舶mmsi") @RequestParam(value = "shipId") String shipId,
			@Parameter(description = "通道号") @RequestParam(value = "channelNo", defaultValue = "1") Integer channelNo,
			@Parameter(description = "流播放协议，2-hls、3-rtmp、4-flv") @RequestParam(value = "protocol") String protocol,
			@Parameter(description = "视频清晰度，1-高清（主码流）、2-流畅（子码流）") @RequestParam(value = "quality", required = false) String quality,
			@Parameter(description = "协议地址的类型，1-预览，2-本地录像回放，3-云存储录像回放，非必选，默认为1") @RequestParam(value = "type", required = false) String type,
			@Parameter(description = "是否要求播放视频为H265编码格式,1表示需要，0表示不要求 获取到的地址需要支持H265的播放器才可以播放。") @RequestParam(value = "supportH265", required = false) Integer supportH265,
			@Parameter(description = "过期时长，单位秒；针对hls/rtmp设置有效期，相对时间；30秒-720天") @RequestParam(value = "expireTime", required = false) String expireTime,
			@Parameter(description = "回放开始时间") @RequestParam(value = "startTime", required = false) String startTime,
			@Parameter(description = "回放停止时间") @RequestParam(value = "stopTime", required = false) String stopTime,
			@Parameter(description = "回放倍速") @RequestParam(value = "playbackSpeed", required = false) Integer playbackSpeed) {
		String deviceSerial = validator.validateCustomPlay(shipId);
		return deviceService.video(deviceSerial, channelNo, protocol, quality,
				type, supportH265, expireTime, startTime, stopTime,
				playbackSpeed).orElse(null);
	}

	@GetMapping(value = "/stop/play")
	public void stopPlay(
			@Parameter(description = "船舶mmsi") @RequestParam(value = "shipId") String shipId,
			@Parameter(description = "通道号，非必选，默认为1") @RequestParam(value = "channelNo", defaultValue = "1") Integer channelNo,
			@Parameter(description = "播放地址ID，对应获取标准流播放地址接口的id") @RequestParam(value = "liveId") String liveId) {
		String deviceSerial = validator.validateCustomPlay(shipId);
		deviceService.stopPlay(deviceSerial, channelNo, liveId);
	}

	@GetMapping(value = "/vo")
	ShipVo findVoById(@RequestParam String id) {
		return service.findVoById(id).orElse(null);
	}

	@PostMapping(value = "/vo/ids")
	List<ShipVo> findVoByIds(@RequestBody List<String> ids) {
		return service.findVoByIds(ids);
	}

	@GetMapping("/selector-vo")
	public Page<ShipVo> selectorVo(
			@RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@RequestParam(value = "state", required = false) Integer state,
			@RequestParam(value = "keyword", required = false) String keyword) {
		return PageUtil.convert(service.selectorVo(page, size, state, keyword));
	}

	@GetMapping("/find/asi")
	ShipInfo findByAsi(@RequestParam String id) {
		return service.findByAsi(id);
	}

	@PostMapping("/find/id-create")
	Optional<ShipInfoVo> findByIdAndCreate(@RequestBody ShipInfo shipInfo,
			@RequestParam(required = false) Long userId,
			@RequestParam Integer monitoringChannels) {
		return service.findByIdAndCreate(shipInfo, userId, monitoringChannels);
	}

	@GetMapping("/find/ais/track")
	List<ShipTrajectoryVo> findByAisTrack(@RequestParam String mmsi,
			@RequestParam Long start, @RequestParam Long end) {
		return service.findByAisTrack(mmsi, start, end);
	}

	@GetMapping("/online/ship-info")
	List<OnlineShipVo> findByOnlineShip() {
		return service.findByOnlineShip();
	}

	@PostMapping("/recognize/id-card")
	public void recognizeIdCard() {
		service.recognizeIdCard();
	}
}
