package com.zhihaoscm.service.core.service.impl;

import static com.zhihaoscm.domain.meta.biz.ProductTypeIndexDef.QueryMode.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.ProductType;
import com.zhihaoscm.domain.bean.entity.ProductTypeIndex;
import com.zhihaoscm.domain.bean.entity.Top;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.meta.biz.CompositeIndexDef;
import com.zhihaoscm.domain.meta.biz.DashboardDef;
import com.zhihaoscm.domain.meta.biz.ProductTypeIndexDef;
import com.zhihaoscm.domain.meta.biz.TopDef;
import com.zhihaoscm.domain.utils.DashboardDateUtils;
import com.zhihaoscm.service.core.mapper.ProductTypeIndexMapper;
import com.zhihaoscm.service.core.service.ProductTypeIndexService;
import com.zhihaoscm.service.core.service.ProductTypeIndexVersionService;
import com.zhihaoscm.service.core.service.ProductTypeService;
import com.zhihaoscm.service.core.service.TopService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 品类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Slf4j
@Service
public class ProductTypeIndexServiceImpl extends
		MpLongIdBaseServiceImpl<ProductTypeIndex, ProductTypeIndexMapper>
		implements ProductTypeIndexService {

	@Autowired
	private ProductTypeService productTypeService;

	@Autowired
	private ProductTypeIndexVersionService productTypeIndexVersionService;

	@Autowired
	private TopService topService;

	public ProductTypeIndexServiceImpl(ProductTypeIndexMapper repository) {
		super(repository);
	}

	@Override
	public Page<ProductTypeIndexVo> paging(Integer page, Integer size,
			String productTypeName, String productTypeId, LocalDate startTime,
			LocalDate endTime, Integer state, String sortKey, String sortOrder,
			LocalDate priceDate, Integer marketId, String typeId,
			Integer priceType, Long versionId) {
		Page<ProductTypeIndex> paging = repository.paging(
				new Page<>(page, size), productTypeName, productTypeId,
				startTime, endTime, state,
				Objects.nonNull(priceType) ? priceType
						: ProductTypeIndexDef.PriceType.UP_BOAT.getCode(),
				sortKey, sortOrder, CommonDef.Symbol.NO.getCode(), priceDate,
				marketId, typeId, versionId);
		List<ProductTypeIndexVo> productTypeIndexVos = this
				.packVo(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, productTypeIndexVos);
	}

	@Override
	public Page<ProductTypeIndexCustomVo> customPaging(Integer page,
			Integer size, String productTypeName, String productTypeId,
			Integer queryType, Long customerId, LocalDate date) {
		// 分页查询指数列表
		Page<ProductTypeIndexCustomVo> paging = repository.customPaging(
				new Page<>(page, size), productTypeName, productTypeId,
				CommonDef.Symbol.NO.getCode(), CommonDef.Symbol.YES.getCode(),
				queryType, customerId, date);
		return PageUtil.getRecordsInfoPage(paging,
				this.packCustomVo(paging.getRecords()));
	}

	@Override
	public Page<ProductTypeIndexCustomVo> hotPaging(Integer page, Integer size,
			Integer queryType, LocalDate date) {
		// 分页查询指数列表
		Page<ProductTypeIndexCustomVo> paging = repository.hotPaging(
				new Page<>(page, size), queryType, date,
				CommonDef.Symbol.YES.getCode(), CommonDef.Symbol.NO.getCode(),
				CommonDef.Symbol.YES.getCode());
		return PageUtil.getRecordsInfoPage(paging,
				this.packCustomVo(paging.getRecords()));
	}

	@Override
	public List<List<ProductTypeIndexCustomVo>> findHot(Integer page,
			Integer size, LocalDate date) {

		// 产地
		Page<ProductTypeIndexCustomVo> paging = repository.hotPaging(
				new Page<>(page, size),
				ProductTypeIndexDef.QueryType.UP_BOAT.getCode(), date,
				CommonDef.Symbol.YES.getCode(), CommonDef.Symbol.NO.getCode(),
				CommonDef.Symbol.NO.getCode());

		List<List<ProductTypeIndexCustomVo>> result = new ArrayList<>();
		List<ProductTypeIndexCustomVo> records = paging.getRecords();
		if (CollectionUtils.isNotEmpty(records)) {

			// 芜湖
			List<ProductTypeIndexCustomVo> wuHuProductTypeIndexList = new ArrayList<>();
			// 江阴
			List<ProductTypeIndexCustomVo> jiangYinProductTypeIndexList = new ArrayList<>();
			for (ProductTypeIndexCustomVo record : records) {
				String productTypeId = record.getProductTypeIndex()
						.getProductTypeId();
				ProductType productType = record.getProductType();

				// 江阴
				Optional<ProductTypeIndex> upBoatOptional = this
						.findByQueryType(
								ProductTypeIndexDef.QueryType.JY_TOUCH_PORT
										.getCode(),
								productTypeId, date);
				this.processPort(jiangYinProductTypeIndexList, productType,
						upBoatOptional,
						ProductTypeIndexDef.QueryType.JY_TOUCH_PORT.getCode());

				// 芜湖
				Optional<ProductTypeIndex> wuHuOptional = this.findByQueryType(
						ProductTypeIndexDef.QueryType.WH_TOUCH_PORT.getCode(),
						productTypeId, date);
				this.processPort(wuHuProductTypeIndexList, productType,
						wuHuOptional,
						ProductTypeIndexDef.QueryType.WH_TOUCH_PORT.getCode());
			}
			// 江阴
			result.add(this.packCustomVo(jiangYinProductTypeIndexList));
			// 芜湖
			result.add(this.packCustomVo(wuHuProductTypeIndexList));
			// 产地
			result.add(this.packCustomVo(records));
		}
		return result;
	}

	@Override
	public List<ProductTypeIndex> findDefaultByVersionIds(
			List<Long> versionIds) {
		if (CollectionUtils.isEmpty(versionIds)) {
			return List.of();
		}
		LambdaQueryWrapper<ProductTypeIndex> wrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class);
		this.filterDeleted(wrapper);
		wrapper.in(ProductTypeIndex::getVersionId, versionIds);
		wrapper.eq(ProductTypeIndex::getPriceType,
				ProductTypeIndexDef.PriceType.UP_BOAT.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<ProductTypeIndexVo> findVoById(Long id) {
		Optional<ProductTypeIndex> optionalProductTypeIndex = super.findOne(id);
		if (optionalProductTypeIndex.isPresent()) {
			ProductTypeIndex productTypeIndex = optionalProductTypeIndex.get();
			ProductTypeIndexVo productTypeIndexVo = new ProductTypeIndexVo();
			productTypeIndexVo.setProductTypeIndex(productTypeIndex);
			productTypeIndexVo.setProductType(productTypeService
					.findOne(productTypeIndex.getProductTypeId()).orElse(null));

			// 版本信息
			if (Objects.nonNull(productTypeIndex.getVersionId())) {
				productTypeIndexVersionService
						.findOne(productTypeIndex.getVersionId()).ifPresent(
								productTypeIndexVo::setProductTypeIndexVersion);
			}

			// 较近一次涨跌
			LambdaQueryWrapper<ProductTypeIndex> lambdaQueryWrapper = Wrappers
					.lambdaQuery(ProductTypeIndex.class);
			lambdaQueryWrapper.eq(ProductTypeIndex::getProductTypeId,
					productTypeIndex.getProductTypeId());
			lambdaQueryWrapper.eq(ProductTypeIndex::getPlacePriceType,
					productTypeIndex.getPlacePriceType());
			lambdaQueryWrapper.eq(ProductTypeIndex::getPriceType,
					productTypeIndex.getPriceType());
			lambdaQueryWrapper.eq(ProductTypeIndex::getDate,
					productTypeIndex.getDate().minusDays(1));
			lambdaQueryWrapper.eq(ProductTypeIndex::getDel,
					CommonDef.Symbol.NO.getCode());
			lambdaQueryWrapper.orderByDesc(ProductTypeIndex::getDate)
					.last("limit 1");
			ProductTypeIndex lastRecord = repository
					.selectOne(lambdaQueryWrapper);
			BigDecimal lastAvgPrice = Objects.isNull(lastRecord)
					? BigDecimal.ZERO
					: lastRecord.getAvgPrice();
			if (Objects.nonNull(lastAvgPrice)
					&& Objects.nonNull(productTypeIndex.getAvgPrice())
					&& BigDecimal.ZERO
							.compareTo(productTypeIndex.getAvgPrice()) != 0) {
				productTypeIndexVo.setLastTimeUpDown(
						productTypeIndex.getAvgPrice().subtract(lastAvgPrice));
			}

			return Optional.of(productTypeIndexVo);
		}
		return Optional.empty();
	}

	@Override
	public Optional<ProductTypeIndex> findLatest(String productTypeId) {
		LambdaQueryWrapper<ProductTypeIndex> wrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class);
		this.filterDeleted(wrapper);
		wrapper.eq(ProductTypeIndex::getState, CommonDef.Symbol.YES.getCode());
		if (StringUtils.isNotBlank(productTypeId)) {
			wrapper.eq(ProductTypeIndex::getProductTypeId, productTypeId);
		}
		wrapper.orderByDesc(ProductTypeIndex::getDate);
		wrapper.last("limit 1");
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public List<ProductTypeIndexAnalyzeVo> findPriceAnalysis(
			String productTypeId, Integer queryMode) {
		Integer state = CommonDef.Symbol.YES.getCode();
		return switch (ProductTypeIndexDef.QueryMode.from(queryMode)) {
			// 查询近七天的历史均价
			case SEVEN_DAYS ->
				this.completionDate(repository.findPriceAnalysisWithSevenDay(
						productTypeId, state), SEVEN_DAYS);
			// 查询近一月的历史均价
			case MONTH ->
				this.completionDate(repository.findPriceAnalysisWithMonth(
						productTypeId, state,
						1 + Duration.between(
								Objects.requireNonNull(DashboardDateUtils
										.calcBeginDate(DashboardDef.Scope.MONTH
												.getCode())),
								LocalDateTime.now()).toDays()),
						MONTH);
			// 查询近一年的历史均价
			case YEAR -> this.completionDate(
					repository.findPriceAnalysisWithYear(productTypeId, state),
					YEAR);
		};
	}

	@Override
	public List<ProductTypeIndexAnalyzeVo> findShipmentAnalysis(
			String productTypeId, Integer queryMode) {
		Integer state = CommonDef.Symbol.YES.getCode();
		return switch (ProductTypeIndexDef.QueryMode.from(queryMode)) {
			// 查询近七天的历史出货量
			case SEVEN_DAYS ->
				this.completionDate(repository.findShipmentAnalysisWithSevenDay(
						productTypeId, state), SEVEN_DAYS);
			// 查询近一月的历史出货量
			case MONTH ->
				this.completionDate(repository.findShipmentAnalysisWithMonth(
						productTypeId, state,
						1 + Duration.between(
								Objects.requireNonNull(DashboardDateUtils
										.calcBeginDate(DashboardDef.Scope.MONTH
												.getCode())),
								LocalDateTime.now()).toDays()),
						MONTH);
			// 查询近一年的历史出货量
			case YEAR -> this.completionDate(repository
					.findShipmentAnalysisWithYear(productTypeId, state), YEAR);
		};
	}

	@Override
	public List<ProductTypeIndexVo> findByDateAndProductTypeId(LocalDate date,
			String productTypeId) {
		List<ProductTypeIndexVo> vos = repository.findByDateAndProductTypeId(
				date, productTypeId, CommonDef.Symbol.NO.getCode());
		vos.forEach(vo -> {
			if (Objects.nonNull(vo.getProductTypeIndex().getMarketId())) {
				CompositeIndexDef.SamplingMarket samplingMarket = CompositeIndexDef.SamplingMarket
						.from(vo.getProductTypeIndex().getMarketId());
				if (Objects.nonNull(samplingMarket)) {
					vo.setMarketName(samplingMarket.getName());
				}
				if (Objects.nonNull(vo.getProductTypeIndex())) {
					ProductTypeIndex productTypeIndex = vo
							.getProductTypeIndex();
					this.resetLastTimeUpDown(vo, productTypeIndex);
				}
			}
		});
		return vos;
	}

	@Override
	public List<ProductTypeIndex> findByDateAndProductTypeIds(LocalDate date,
			Integer priceType, List<String> productTypeIds, Integer marketId) {
		LambdaQueryWrapper<ProductTypeIndex> wrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(date), ProductTypeIndex::getDate, date);
		wrapper.eq(Objects.nonNull(priceType), ProductTypeIndex::getPriceType,
				priceType);
		wrapper.in(CollectionUtils.isNotEmpty(productTypeIds),
				ProductTypeIndex::getProductTypeId, productTypeIds);
		wrapper.eq(Objects.nonNull(marketId), ProductTypeIndex::getMarketId,
				marketId);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<ProductTypeIndex> findNewestDataByProductTypeId(
			String productTypeId, LocalDate date) {
		LambdaQueryWrapper<ProductTypeIndex> queryWrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(ProductTypeIndex::getProductTypeId, productTypeId);
		queryWrapper.eq(ProductTypeIndex::getState,
				CommonDef.Symbol.YES.getCode());
		queryWrapper.eq(ProductTypeIndex::getPriceType,
				ProductTypeIndexDef.PriceType.UP_BOAT.getCode());
		productTypeService.findOne(productTypeId)
				.ifPresent(productType -> queryWrapper.eq(
						ProductTypeIndex::getPlacePriceType,
						productType.getPlacePriceType()));
		queryWrapper.eq(Objects.nonNull(date), ProductTypeIndex::getDate, date);
		queryWrapper.orderByDesc(ProductTypeIndex::getDate);
		queryWrapper.last("LIMIT 1");
		return Optional
				.ofNullable(repository.selectOne(queryWrapper, Boolean.FALSE));
	}

	@Override
	public Optional<ProductTypeIndex> findByQueryType(Integer queryType,
			String productTypeId, LocalDate date) {
		LambdaQueryWrapper<ProductTypeIndex> queryWrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class);
		this.filterDeleted(queryWrapper);
		switch (ProductTypeIndexDef.QueryType.from(queryType)) {
			case UP_BOAT -> {
				queryWrapper.eq(ProductTypeIndex::getPriceType,
						ProductTypeIndexDef.PriceType.UP_BOAT.getCode());
				productTypeService.findOne(productTypeId)
						.ifPresent(productType -> queryWrapper.eq(
								ProductTypeIndex::getPlacePriceType,
								productType.getPlacePriceType()));
			}
			case JY_TOUCH_PORT -> {
				queryWrapper.eq(ProductTypeIndex::getPriceType,
						ProductTypeIndexDef.PriceType.TOUCH_PORT.getCode());
				queryWrapper.eq(ProductTypeIndex::getMarketId,
						CompositeIndexDef.SamplingMarket.JIANG_YIN.getCode());
			}
			case WH_TOUCH_PORT -> {
				queryWrapper.eq(ProductTypeIndex::getPriceType,
						ProductTypeIndexDef.PriceType.TOUCH_PORT.getCode());
				queryWrapper.eq(ProductTypeIndex::getMarketId,
						CompositeIndexDef.SamplingMarket.WU_HU.getCode());
			}
		}
		queryWrapper.eq(ProductTypeIndex::getProductTypeId, productTypeId);
		queryWrapper.eq(ProductTypeIndex::getState,
				CommonDef.Symbol.YES.getCode());

		queryWrapper.eq(Objects.nonNull(date), ProductTypeIndex::getDate, date);
		queryWrapper.orderByDesc(ProductTypeIndex::getDate);
		queryWrapper.last("LIMIT 1");
		return Optional
				.ofNullable(repository.selectOne(queryWrapper, Boolean.FALSE));
	}

	@Override
	public List<ProductTypeIndex> findByVersionId(Long versionId) {
		LambdaQueryWrapper<ProductTypeIndex> wrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class)
				.eq(ProductTypeIndex::getVersionId, versionId);
		this.filterDeleted(wrapper);
		return repository.selectList(wrapper);
	}

	@Override
	public List<ProductTypeIndex> findByVersionIdAndProductTypeId(
			Long versionId, String productTypeId) {
		LambdaQueryWrapper<ProductTypeIndex> wrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class)
				.eq(ProductTypeIndex::getVersionId, versionId)
				.eq(ProductTypeIndex::getProductTypeId, productTypeId);
		this.filterDeleted(wrapper);
		return repository.selectList(wrapper);
	}

	@Override
	public List<ProductTypeIndex> findByVersionIdAndProductTypeIds(
			Long versionId, List<String> productTypeIds) {
		LambdaQueryWrapper<ProductTypeIndex> wrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class)
				.eq(ProductTypeIndex::getVersionId, versionId)
				.in(ProductTypeIndex::getProductTypeId, productTypeIds);
		this.filterDeleted(wrapper);
		return repository.selectList(wrapper);
	}

	@Override
	public List<ProductTypeIndex> findLastIndex(String productName,
			String area) {
		return repository.findLastIndex(productName, area);
	}

	@Override
	public List<ProductTypeIndex> findIndex(String productName, String area,
			LocalDate date) {
		return repository.findIndex(productName, area, date);
	}

	@Override
	public List<ProductTypeIndex> findSandPriceByArea(String area) {
		return repository.findSandPriceByArea(area);
	}

	@Override
	public List<ProductTypeIndex> findSandPriceByAreaAndDate(String area,
			LocalDate date) {
		return repository.findSandPriceByAreaAndDate(area, date);
	}

	/**
	 * 根据指数查询前一天指数 没有返回0
	 *
	 * @param productTypeIndex
	 * @return
	 */
	private BigDecimal findLastPrice(ProductTypeIndex productTypeIndex) {
		LocalDate date = productTypeIndex.getDate();
		LocalDate lastDate = date.minusDays(1);
		LambdaQueryWrapper<ProductTypeIndex> wrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class);
		wrapper.eq(Objects.nonNull(productTypeIndex.getPriceType()),
				ProductTypeIndex::getPriceType,
				productTypeIndex.getPriceType());
		wrapper.eq(StringUtils.isNotBlank(productTypeIndex.getPlacePriceType()),
				ProductTypeIndex::getPlacePriceType,
				productTypeIndex.getPlacePriceType());
		wrapper.eq(Objects.nonNull(productTypeIndex.getMarketId()),
				ProductTypeIndex::getMarketId, productTypeIndex.getMarketId());
		wrapper.eq(ProductTypeIndex::getProductTypeId,
				productTypeIndex.getProductTypeId());
		wrapper.eq(ProductTypeIndex::getDate, lastDate);
		wrapper.eq(ProductTypeIndex::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.orderByDesc(ProductTypeIndex::getDate).last("limit 1");
		ProductTypeIndex lastRecord = repository.selectOne(wrapper);
		return Objects.isNull(lastRecord) ? BigDecimal.ZERO
				: (Objects.isNull(lastRecord.getAvgPrice()) ? BigDecimal.ZERO
						: lastRecord.getAvgPrice());
	}

	/**
	 * 查询均价和出货量
	 *
	 * @param queryType
	 * @param productTypeIndex
	 * @param endDate
	 * @param startDate
	 * @return
	 */
	private List<ProductTypeIndex> findAvgPriceAndShipment(Integer queryType,
			ProductTypeIndex productTypeIndex, LocalDate startDate,
			LocalDate endDate) {
		LambdaQueryWrapper<ProductTypeIndex> lambdaQueryWrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class);
		lambdaQueryWrapper
				.select(ProductTypeIndex::getAvgPrice,
						ProductTypeIndex::getShipment)
				.eq(ProductTypeIndex::getProductTypeId,
						productTypeIndex.getProductTypeId())
				.eq(ProductTypeIndex::getPriceType,
						productTypeIndex.getPriceType())
				.eq(ProductTypeIndex::getDel, CommonDef.Symbol.NO.getCode())
				.between(ProductTypeIndex::getDate, startDate, endDate);
		switch (ProductTypeIndexDef.QueryType.from(queryType)) {
			case UP_BOAT ->
				lambdaQueryWrapper.eq(ProductTypeIndex::getPlacePriceType,
						productTypeIndex.getPlacePriceType());
			case JY_TOUCH_PORT, WH_TOUCH_PORT ->
				lambdaQueryWrapper.eq(ProductTypeIndex::getMarketId,
						productTypeIndex.getMarketId());
		}
		return repository.selectList(lambdaQueryWrapper);
	}

	@Override
	public List<ProductTypeIndexAIVo> findProductTypeIndexByType(Integer type,
			LocalDate date) {
		String dateStr = Objects.isNull(date) ? LocalDate.now().toString()
				: date.toString();
		return repository.findProductTypeIndexByType(type, dateStr);
	}

	@Override
	public Optional<ProductTypeIndexDetailVo> findPreAndNextProductTypeId(
			String productTypeId, Long customerId) {
		ProductTypeIndexDetailVo vo = new ProductTypeIndexDetailVo();
		List<ProductTypeIndexCustomVo> list = repository
				.findAllLatestProductType(null, null,
						CommonDef.Symbol.NO.getCode(),
						CommonDef.Symbol.YES.getCode(), null, customerId, null);

		Map<String, ProductTypeIndex> productTypeIndexMap = list.stream()
				.map(ProductTypeIndexCustomVo::getProductTypeIndex)
				.collect(Collectors.toMap(ProductTypeIndex::getProductTypeId,
						Function.identity()));

		List<String> productIdList = list.stream()
				.map(ProductTypeIndexCustomVo::getProductType)
				.map(ProductType::getId).toList();

		if (CollectionUtils.isNotEmpty(productIdList)) {

			int currentIndex = productIdList.indexOf(productTypeId);
			if (currentIndex != -1) {
				if (currentIndex > 0) {
					String pre = productIdList.get(currentIndex - 1);
					vo.setPreviousProductTypeId(pre);
					vo.setPreviousDate(productTypeIndexMap.get(pre).getDate());
				}
				if (currentIndex < productIdList.size() - 1) {
					String next = productIdList.get(currentIndex + 1);
					vo.setNextProductTypeId(next);
					vo.setNextDate(productTypeIndexMap.get(next).getDate());
				}
			}
		}
		return Optional.of(vo);
	}

	@Override
	public List<ProductTypeIndexDetailVo> detail(String productTypeId,
			Long customerId) {
		List<ProductTypeIndexDetailVo> productTypeIndexDetailVoList = new ArrayList<>();

		// 判断指数是否置顶
		boolean isTop = false;
		if (Objects.nonNull(customerId)) {
			Optional<Top> topOptional = topService
					.findByCustomerIdAndTypeAndBusinessId(customerId,
							TopDef.Type.PRODUCT_TYPE_INDEX.getCode(),
							productTypeId);
			if (topOptional.isPresent()) {
				isTop = true;
			}
		}
		// 最新指数
		Optional<ProductTypeIndex> latest = this.findLatest(productTypeId);
		if (latest.isEmpty()) {
			return productTypeIndexDetailVoList;
		}
		LocalDate date = latest.get().getDate();

		// 循环三种价格指数
		for (ProductTypeIndexDef.QueryType value : ProductTypeIndexDef.QueryType
				.values()) {
			Integer queryType = value.getCode();
			Optional<ProductTypeIndex> productTypeIndexOptional = this
					.findByQueryType(queryType, productTypeId, date);
			if (productTypeIndexOptional.isPresent()) {
				ProductTypeIndex productTypeIndex = productTypeIndexOptional
						.get();
				ProductTypeIndexDetailVo detailVo = new ProductTypeIndexDetailVo();
				// 判断是否置顶
				detailVo.setIsTop(isTop);
				detailVo.setType(queryType);
				ProductType productType = productTypeService
						.findOneWithDeleted(productTypeIndex.getProductTypeId())
						.orElse(null);
				detailVo.setProductTypeIndex(productTypeIndex);
				detailVo.setProductType(productType);
				// 日环比
				this.handleDayQoq(queryType, productTypeIndex, detailVo);
				// 周环比
				this.handleWeekQoq(queryType, productTypeIndex, detailVo);
				// 涨跌
				if (Objects.nonNull(productTypeIndex.getAvgPrice())) {
					// 前一天价格
					BigDecimal lastPrice = this.findLastPrice(productTypeIndex);
					if (BigDecimal.ZERO.compareTo(lastPrice) != 0) {
						detailVo.setLastTimeUpDown(productTypeIndex
								.getAvgPrice().subtract(lastPrice));
					}
				}
				// 只设置价格不为空和不等于0的数据
				productTypeIndexDetailVoList.add(detailVo);
			}
		}
		return productTypeIndexDetailVoList;
	}

	@Override
	public void top(Long id) {
		super.findOne(id).ifPresent(productTypeIndex -> {
			List<ProductTypeIndex> productTypeIndexList = this
					.findByVersionIdAndProductTypeId(
							productTypeIndex.getVersionId(),
							productTypeIndex.getProductTypeId());
			if (CollectionUtils.isEmpty(productTypeIndexList)) {
				return;
			}
			switch (CommonDef.Symbol.from(productTypeIndex.getTop())) {
				case YES -> {
					for (ProductTypeIndex index : productTypeIndexList) {
						index.setTop(CommonDef.Symbol.NO.getCode());
						index.setTopTime(null);
					}
				}
				case NO -> {
					for (ProductTypeIndex index : productTypeIndexList) {
						index.setTop(CommonDef.Symbol.YES.getCode());
						index.setTopTime(LocalDateTime.now());
					}
				}
			}
			super.batchUpdate(productTypeIndexList);
		});
	}

	/**
	 * 处理靠港价
	 *
	 * @param productTypeIndexCustomVoList
	 * @param productType
	 * @param productTypeIndexOptional
	 */
	private void processPort(
			List<ProductTypeIndexCustomVo> productTypeIndexCustomVoList,
			ProductType productType,
			Optional<ProductTypeIndex> productTypeIndexOptional,
			Integer queryType) {
		ProductTypeIndexCustomVo productTypeIndexCustomVo = new ProductTypeIndexCustomVo();
		if (productTypeIndexOptional.isPresent()) {
			ProductTypeIndex productTypeIndex = productTypeIndexOptional.get();
			productTypeIndexCustomVo.setProductType(productType);
			productTypeIndexCustomVo.setProductTypeIndex(productTypeIndex);
		} else {
			productTypeIndexCustomVo.setProductType(productType);
			ProductTypeIndex productTypeIndex = new ProductTypeIndex();
			productTypeIndex.setPriceType(
					ProductTypeIndexDef.PriceType.TOUCH_PORT.getCode());
			switch (ProductTypeIndexDef.QueryType.from(queryType)) {
				case JY_TOUCH_PORT -> productTypeIndex.setMarketId(
						CompositeIndexDef.SamplingMarket.JIANG_YIN.getCode());
				case WH_TOUCH_PORT -> productTypeIndex.setMarketId(
						CompositeIndexDef.SamplingMarket.WU_HU.getCode());
			}
			productTypeIndexCustomVo.setProductTypeIndex(productTypeIndex);
		}
		productTypeIndexCustomVoList.add(productTypeIndexCustomVo);
	}

	/**
	 * 补全日期数据
	 *
	 * @param productTypeIndexAnalyzeVos
	 * @param queryMode
	 * @return
	 */
	private List<ProductTypeIndexAnalyzeVo> completionDate(
			List<ProductTypeIndexAnalyzeVo> productTypeIndexAnalyzeVos,
			ProductTypeIndexDef.QueryMode queryMode) {
		if (CollectionUtils.isEmpty(productTypeIndexAnalyzeVos)) {
			return new ArrayList<>();
		}
		productTypeIndexAnalyzeVos.forEach(productTypeIndexAnalyzeVo -> {
			if (CollectionUtils.isEmpty(
					productTypeIndexAnalyzeVo.getProductTypeIndices())) {
				productTypeIndexAnalyzeVo.setProductTypeIndices(
						this.fillMissingDates(queryMode));
			} else {
				this.fillMissingDatesInExistingData(productTypeIndexAnalyzeVo,
						queryMode);
			}
		});
		return productTypeIndexAnalyzeVos;
	}

	/**
	 * 补全日期数据 - 全部
	 *
	 * @param queryMode
	 * @return
	 */
	private List<ProductTypeIndex> fillMissingDates(
			ProductTypeIndexDef.QueryMode queryMode) {
		List<ProductTypeIndex> productTypeIndices = new ArrayList<>();
		LocalDate startDate;
		int days = switch (queryMode) {
			case SEVEN_DAYS -> {
				startDate = LocalDate.now().minusDays(6);
				yield 7;
			}
			case MONTH -> {
				startDate = LocalDate.now().minusMonths(1).plusDays(1);
				yield 1 + Math.toIntExact(
						ChronoUnit.DAYS.between(startDate, LocalDate.now()));
			}
			case YEAR -> {
				startDate = LocalDate.now().minusMonths(11);
				yield 12;
			}
		};
		for (int i = 0; i < days; i++) {
			startDate = switch (queryMode) {
				case SEVEN_DAYS, MONTH -> startDate.plusDays(1);
				case YEAR -> startDate.plusMonths(1);
			};
		}
		return productTypeIndices;
	}

	/**
	 * 补全日期数据 - 已有数据
	 *
	 * @param productTypeIndexAnalyzeVo
	 * @param queryMode
	 */
	private void fillMissingDatesInExistingData(
			ProductTypeIndexAnalyzeVo productTypeIndexAnalyzeVo,
			ProductTypeIndexDef.QueryMode queryMode) {
		LocalDate startDate;
		int days = switch (queryMode) {
			case SEVEN_DAYS -> {
				startDate = LocalDate.now().minusDays(6);
				yield 7;
			}
			case MONTH -> {
				startDate = LocalDate.now().minusMonths(1).plusDays(1);
				yield 1 + Math.toIntExact(
						ChronoUnit.DAYS.between(startDate, LocalDate.now()));
			}
			case YEAR -> {
				startDate = LocalDate.now()
						.with(TemporalAdjusters.lastDayOfMonth())
						.minusMonths(11);
				yield 12;
			}
		};
		Map<LocalDate, ProductTypeIndex> productTypeIndexMap = productTypeIndexAnalyzeVo
				.getProductTypeIndices().stream()
				.collect(Collectors.toMap(ProductTypeIndex::getDate,
						Function.identity(), (k1, k2) -> k1));
		List<ProductTypeIndex> productTypeIndices = new ArrayList<>();
		for (int i = 0; i < days; i++) {
			ProductTypeIndex productTypeIndex = productTypeIndexMap
					.get(startDate);
			if (Objects.isNull(productTypeIndex)) {
				productTypeIndices.add(createProductTypeIndex(startDate));
			} else {
				if (Objects.isNull(productTypeIndex.getShipment())) {
					productTypeIndex.setShipment(0);
				}
				productTypeIndices.add(productTypeIndex);
			}
			startDate = switch (queryMode) {
				case SEVEN_DAYS, MONTH -> startDate.plusDays(1);
				case YEAR -> startDate.plusMonths(1)
						.with(TemporalAdjusters.lastDayOfMonth());
			};
		}
		productTypeIndexAnalyzeVo.setProductTypeIndices(productTypeIndices);
	}

	/**
	 * 创建ProductTypeIndex
	 *
	 * @param date
	 * @return
	 */
	private ProductTypeIndex createProductTypeIndex(LocalDate date) {
		ProductTypeIndex temp = new ProductTypeIndex();
		temp.setAvgPrice(BigDecimal.ZERO);
		temp.setShipment(0);
		temp.setDate(date);
		return temp;
	}

	/**
	 * 处理日环比
	 *
	 * @param productTypeIndex
	 * @param detailVo
	 */
	private void handleDayQoq(Integer queryType,
			ProductTypeIndex productTypeIndex,
			ProductTypeIndexDetailVo detailVo) {
		LocalDate date = productTypeIndex.getDate();
		// 获取前一天
		LocalDate lastDate = date.minusDays(1);
		// 查询近一次的数据
		LambdaQueryWrapper<ProductTypeIndex> lambdaQueryWrapper = Wrappers
				.lambdaQuery(ProductTypeIndex.class)
				.eq(ProductTypeIndex::getProductTypeId,
						productTypeIndex.getProductTypeId())
				.eq(ProductTypeIndex::getPriceType,
						productTypeIndex.getPriceType())
				.eq(ProductTypeIndex::getDel, CommonDef.Symbol.NO.getCode())
				.eq(ProductTypeIndex::getDate, lastDate)
				.orderByDesc(ProductTypeIndex::getDate).last("limit 1");

		switch (ProductTypeIndexDef.QueryType.from(queryType)) {
			case UP_BOAT ->
				lambdaQueryWrapper.eq(ProductTypeIndex::getPlacePriceType,
						productTypeIndex.getPlacePriceType());
			case JY_TOUCH_PORT, WH_TOUCH_PORT ->
				lambdaQueryWrapper.eq(ProductTypeIndex::getMarketId,
						productTypeIndex.getMarketId());
		}

		// 近一次数据 日环比计算
		ProductTypeIndex lastData = repository.selectOne(lambdaQueryWrapper);
		if (Objects.nonNull(lastData)) {
			// 均价日环比
			BigDecimal nowAvgPrice = productTypeIndex.getAvgPrice();
			BigDecimal lastAvgPrice = lastData.getAvgPrice();

			if (Objects.nonNull(lastAvgPrice) && Objects.nonNull(nowAvgPrice)
					&& lastAvgPrice.compareTo(BigDecimal.ZERO) != 0) {
				BigDecimal avgPriceDayQoq = nowAvgPrice.subtract(lastAvgPrice)
						.divide(lastAvgPrice, 4, RoundingMode.HALF_UP)
						.multiply(BigDecimal.valueOf(100L));
				detailVo.setAvgPriceDayQoq(avgPriceDayQoq);
			}

			if (ProductTypeIndexDef.QueryType.UP_BOAT.match(queryType)) {
				if (Objects.nonNull(productTypeIndex.getShipment())) {
					// 出货量日环比
					BigDecimal nowShipment = new BigDecimal(
							productTypeIndex.getShipment());
					if (Objects.nonNull(lastData.getShipment())) {
						BigDecimal lastShipment = new BigDecimal(
								lastData.getShipment());
						if (lastShipment.compareTo(BigDecimal.ZERO) != 0) {
							BigDecimal shipmentDayQoq = nowShipment
									.subtract(lastShipment)
									.divide(lastShipment, 4,
											RoundingMode.HALF_UP)
									.multiply(BigDecimal.valueOf(100L));
							detailVo.setShipmentDayQoq(shipmentDayQoq);
						}
					}
				}
			}
		}
	}

	/**
	 * 处理周环比
	 *
	 * @param productTypeIndex
	 * @param detailVo
	 */
	private void handleWeekQoq(Integer queryType,
			ProductTypeIndex productTypeIndex,
			ProductTypeIndexDetailVo detailVo) {
		// 周环比计算
		LocalDate endDate = productTypeIndex.getDate();
		LocalDate startDate = productTypeIndex.getDate().minusDays(6);

		// 当前周数据
		List<ProductTypeIndex> nowWeekData = this
				.findAvgPriceAndShipment(queryType, productTypeIndex, startDate,
						endDate)
				.stream().filter(Objects::nonNull).toList();

		// 上一周的开始和结束时间
		LocalDate lastWeekStartDate = startDate.minusDays(7);
		LocalDate lastWeekEndDate = lastWeekStartDate.plusDays(6);

		// 上一周的数据
		List<ProductTypeIndex> lastWeekData = this
				.findAvgPriceAndShipment(queryType, productTypeIndex,
						lastWeekStartDate, lastWeekEndDate)
				.stream().filter(Objects::nonNull).toList();

		if (!CollectionUtils.isEmpty(lastWeekData)) {

			// 本周数据
			BigDecimal nowWeekAvgPrice = BigDecimal.ZERO;
			if (CollectionUtils.isNotEmpty(nowWeekData)) {
				for (ProductTypeIndex nowWeekDatum : nowWeekData) {
					if (Objects.nonNull(nowWeekDatum.getAvgPrice())) {
						nowWeekAvgPrice = nowWeekAvgPrice
								.add(nowWeekDatum.getAvgPrice());
					}
				}
			}

			// 上周数据
			BigDecimal lastWeekAvgPrice = BigDecimal.ZERO;
			for (ProductTypeIndex lastWeekDatum : lastWeekData) {
				if (Objects.nonNull(lastWeekDatum.getAvgPrice())) {
					lastWeekAvgPrice = lastWeekAvgPrice
							.add(lastWeekDatum.getAvgPrice());
				}
			}

			if (lastWeekAvgPrice.compareTo(BigDecimal.ZERO) != 0) {
				BigDecimal avgPriceWeekQoq = nowWeekAvgPrice
						.subtract(lastWeekAvgPrice)
						.divide(lastWeekAvgPrice, 4, RoundingMode.HALF_UP)
						.multiply(BigDecimal.valueOf(100L));
				detailVo.setAvgPriceWeekQoq(avgPriceWeekQoq);
			}

			if (ProductTypeIndexDef.QueryType.UP_BOAT.match(queryType)) {

				long nowWeekShipment = 0L;
				for (ProductTypeIndex nowWeekDatum : nowWeekData) {
					if (Objects.nonNull(nowWeekDatum.getShipment())) {
						nowWeekShipment += nowWeekDatum.getShipment();
					}

				}

				long lastWeekShipment = 0L;
				for (ProductTypeIndex lastWeekDatum : lastWeekData) {
					if (Objects.nonNull(lastWeekDatum.getShipment())) {
						lastWeekShipment += lastWeekDatum.getShipment();
					}

				}

				// 出货量周环比
				if (lastWeekShipment != 0) {
					BigDecimal shipmentWeekQoq = new BigDecimal(nowWeekShipment)
							.subtract(new BigDecimal(lastWeekShipment))
							.divide(new BigDecimal(lastWeekShipment), 4,
									RoundingMode.HALF_UP)
							.multiply(BigDecimal.valueOf(100L));
					detailVo.setShipmentWeekQoq(shipmentWeekQoq);
				}
			}
		}
	}

	/**
	 * 组装数据
	 *
	 * @param productTypeIndices
	 * @return
	 */
	private List<ProductTypeIndexVo> packVo(
			List<ProductTypeIndex> productTypeIndices) {
		if (CollectionUtils.isEmpty(productTypeIndices)) {
			return List.of();
		}
		List<String> productTypeIds = productTypeIndices.stream()
				.map(ProductTypeIndex::getProductTypeId)
				.filter(ObjectUtils::isNotEmpty).distinct().toList();

		Map<String, ProductType> productTypeIdMap = productTypeService
				.getIdMap(productTypeIds);
		return productTypeIndices.stream().map(productTypeIndex -> {
			ProductTypeIndexVo productTypeIndexVo = new ProductTypeIndexVo();
			productTypeIndexVo.setProductTypeIndex(productTypeIndex);
			if (Objects.nonNull(productTypeIndex.getVersionId())) {
				productTypeIndexVersionService
						.findOne(productTypeIndex.getVersionId()).ifPresent(
								productTypeIndexVo::setProductTypeIndexVersion);
			}
			productTypeIndexVo.setProductType(
					productTypeIdMap.get(productTypeIndex.getProductTypeId()));
			CompositeIndexDef.SamplingMarket samplingMarket = null;
			if (Objects.nonNull(productTypeIndex.getMarketId())) {
				samplingMarket = CompositeIndexDef.SamplingMarket
						.from(productTypeIndex.getMarketId());
			}
			productTypeIndexVo.setMarketName(
					Objects.nonNull(samplingMarket) ? samplingMarket.getName()
							: StringUtils.EMPTY);
			this.resetLastTimeUpDown(productTypeIndexVo, productTypeIndex);
			return productTypeIndexVo;
		}).toList();
	}

	/**
	 * 设置最近已经涨跌
	 *
	 * @param vo
	 * @param productTypeIndex
	 */
	private void resetLastTimeUpDown(ProductTypeIndexVo vo,
			ProductTypeIndex productTypeIndex) {
		if (Objects.isNull(productTypeIndex.getAvgPrice())) {
			return;
		}
		BigDecimal avgPrice = this.findLastPrice(productTypeIndex);
		if (Objects.nonNull(avgPrice)
				&& BigDecimal.ZERO.compareTo(avgPrice) != 0
				&& Objects.nonNull(productTypeIndex.getAvgPrice())
				&& BigDecimal.ZERO
						.compareTo(productTypeIndex.getAvgPrice()) != 0) {
			vo.setLastTimeUpDown(
					productTypeIndex.getAvgPrice().subtract(avgPrice));
		}
	}

	/**
	 * 组装数据
	 *
	 * @param list
	 * @return
	 */
	private List<ProductTypeIndexCustomVo> packCustomVo(
			List<ProductTypeIndexCustomVo> list) {
		for (ProductTypeIndexCustomVo productTypeIndexCustomVo : list) {
			ProductTypeIndex productTypeIndex = productTypeIndexCustomVo
					.getProductTypeIndex();
			if (Objects.nonNull(productTypeIndex)) {
				if (Objects.nonNull(productTypeIndex.getAvgPrice())) {
					if (BigDecimal.ZERO.compareTo(
							this.findLastPrice(productTypeIndex)) != 0) {
						productTypeIndexCustomVo.setLastTimeUpDown(
								productTypeIndex.getAvgPrice().subtract(
										this.findLastPrice(productTypeIndex)));
					}
				}
			}

		}
		return list;
	}
}
