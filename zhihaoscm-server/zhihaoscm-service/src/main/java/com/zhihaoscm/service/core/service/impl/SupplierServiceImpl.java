package com.zhihaoscm.service.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Supplier;
import com.zhihaoscm.service.core.mapper.SupplierMapper;
import com.zhihaoscm.service.core.service.SupplierService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author: xiaYZ 2025/7/31
 * @version: 1.0
 */
@Slf4j
@Service
public class SupplierServiceImpl extends MpLongIdBaseServiceImpl<Supplier, SupplierMapper>
        implements SupplierService {

    public SupplierServiceImpl(SupplierMapper repository) {
        super(repository);
    }

    @Override
    public Page<Supplier> paging(Integer page, Integer size, String supplierName, String groupCompanyId, Integer type) {
        LambdaQueryWrapper<Supplier> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(Supplier::getDel, CommonDef.Symbol.NO.getCode());
        wrapper.like(StringUtils.isNotBlank(supplierName), Supplier::getSupplierName, supplierName);
        wrapper.eq(StringUtils.isNotBlank(groupCompanyId), Supplier::getGroupCompanyId, groupCompanyId);
        wrapper.eq(Objects.nonNull(type), Supplier::getType, type);

        Page<Supplier> supplierPage = super.repository.selectPage(new Page<>(page, size), wrapper);
        return supplierPage;
    }



}
