package com.zhihaoscm.service.core.service;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.entity.ProductGroupPurchase;
import com.zhihaoscm.domain.bean.vo.ProductConsignmentVo;
import com.zhihaoscm.domain.bean.vo.ServiceSpecialVo;

/**
 * <p>
 * 团购商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
public interface ProductGroupPurchaseService
		extends MpStringIdBaseService<ProductGroupPurchase> {
	/**
	 * 管理后台分页
	 */
	Page<ProductConsignmentVo> paging(Integer page, Integer size,
			String keyword, String productTypeId, Long supplierId,
			List<Integer> groupPurchaseState, Integer state,
			List<Integer> publishState, String sortKey, String sortOrder);

	/**
	 * 查询商品详情
	 */
	Optional<ProductConsignmentVo> findVoById(String id, Long customId);

	/**
	 * 新增寄售商品
	 */
	ProductGroupPurchase create(ProductGroupPurchase product,
			List<Long> activeFileIds);

	/**
	 * 编辑商品
	 */
	ProductGroupPurchase update(ProductGroupPurchase product,
			List<Long> activeFileIds, List<Long> unActiveFileIds);

	/**
	 * 审核
	 */
	void audit(ProductGroupPurchase product);

	/**
	 * 下架
	 */
	void downShelf(ProductGroupPurchase product);

	/**
	 * 关闭
	 */
	void close(ProductGroupPurchase product, List<Long> unActiveFileIds);

	/**
	 * 指派专员
	 *
	 * @param id
	 * @param handlerId
	 */
	void assign(String id, Long handlerId);

	/**
	 * 获取专员列表
	 */
	List<ServiceSpecialVo> findServiceSpecials(String name);
}
