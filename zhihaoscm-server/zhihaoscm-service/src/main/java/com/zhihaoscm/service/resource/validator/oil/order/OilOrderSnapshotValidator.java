package com.zhihaoscm.service.resource.validator.oil.order;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.domain.bean.entity.OilOrderSnapshot;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.json.PlanInfo;
import com.zhihaoscm.domain.meta.biz.OilOrderDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.OilOrderSnapshotService;
import com.zhihaoscm.service.core.service.ShipService;
import com.zhihaoscm.service.core.service.usercenter.UserService;
import com.zhihaoscm.service.resource.form.oil.order.OilOrderCheckForm;
import com.zhihaoscm.service.resource.form.oil.order.OilOrderPlanForm;

/**
 * 油品订单校验器
 */
@Component
public class OilOrderSnapshotValidator {

	@Autowired
	private OilOrderSnapshotService service;
	@Autowired
	private UserService userService;
	@Autowired
	private ShipService shipService;

	/**
	 * 校验数据是否存在
	 *
	 * @param id
	 * @return
	 */
	public OilOrderSnapshot validateExist(String id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30156001));
	}

	/**
	 * 核对加油
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public OilOrderSnapshot validateCheckRefueling(String id,
			OilOrderCheckForm form) {
		OilOrderSnapshot snapshot = this.validateExist(id);
		// 预约中状态才能核对
		if (!OilOrderDef.State.BOOKING.match(snapshot.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30156006);
		}
		// 用户同意了加油协议才能核对
		if (!CommonDef.Symbol.YES.match(snapshot.getIsAgreeProtocol())) {
			throw new BadRequestException(ErrorCode.CODE_30156034);
		}
		snapshot = form.convertToEntity(snapshot);
		// 设置船舶信息
		Optional<Ship> one = shipService.findOne(snapshot.getShipId());
		if (one.isPresent()) {
			Ship ship = one.get();
			snapshot.setShipName(ship.getName());
			snapshot.setShipCnName(ship.getCnname());
		}
		// 如果是提交，把状态设置为进行中，进行中状态设置为待报计划
		if (OilOrderDef.CheckType.SUBMIT.match(form.getCheckType())) {
			snapshot.setState(OilOrderDef.State.IN_PROGRESS.getCode());
			snapshot.setProgressState(
					OilOrderDef.ProgressState.AWAITING_PLAN.getCode());
		}
		return snapshot;
	}

	/**
	 * 报计划
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public OilOrderSnapshot validatePlan(String id, OilOrderPlanForm form) {
		OilOrderSnapshot snapshot = this.validateExist(id);
		// 业务状态为进行中+进行中状态为待报计划才能报计划
		if (!OilOrderDef.State.IN_PROGRESS.match(snapshot.getState())
				|| !OilOrderDef.ProgressState.AWAITING_PLAN
						.match(snapshot.getProgressState())) {
			throw new BadRequestException(ErrorCode.CODE_30156007);
		}
		snapshot = form.convertToEntity(snapshot);
		PlanInfo planInfo = snapshot.getPlanInfo();
		if (Objects.nonNull(planInfo)) {
			// 油品类型不能为空
			if (Objects.isNull(planInfo.getOilType())) {
				throw new BadRequestException(ErrorCode.CODE_30156018);
			}
			// 加油量不能为空
			if (Objects.isNull(planInfo.getRefuelingVolume())) {
				throw new BadRequestException(ErrorCode.CODE_30156020);
			}
			// 计划加油日期不能为空
			if (Objects.isNull(planInfo.getRefuelingTime())) {
				throw new BadRequestException(ErrorCode.CODE_30156030);
			}
			// 计划加油时间段不能为空
			if (Objects.isNull(planInfo.getRefuelingTimePeriod())) {
				throw new BadRequestException(ErrorCode.CODE_30156031);
			}
			// 油价不能为空
			if (Objects.isNull(planInfo.getRefuelingPrice())) {
				throw new BadRequestException(ErrorCode.CODE_30156024);
			}
			// 设置计划人信息
			// 获取当前登录用户id
			UserInfoContext context = UserInfoContextHolder.getContext();
			if (Objects.nonNull(context)) {
				Long userId = context.getUserId();
				planInfo.setPlanBy(userId);
				userService.findOne(userId).ifPresent(user -> {
					planInfo.setPlanBy(user.getId());

					planInfo.setPlanByName(user.getName());
				});
			}
			// 设置计划油费
			planInfo.setRefuelingCost(planInfo.getRefuelingVolume()
					.multiply(planInfo.getRefuelingPrice()));
			// 设置计划时间
			planInfo.setPlanTime(LocalDateTime.now());
			snapshot.setPlanInfo(planInfo);
			return snapshot;
		} else {
			throw new BadRequestException(ErrorCode.CODE_30156029);
		}
	}

	/**
	 * 校验完成加油
	 * 
	 * @param id
	 */
	public void validateComplete(String id) {
		OilOrderSnapshot oilOrderSnapshot = this.validateExist(id);
		// 订单业务状态是进行中+进行中状态是待完成加油才能完成加油
		if (!OilOrderDef.State.IN_PROGRESS.match(oilOrderSnapshot.getState())
				|| !OilOrderDef.ProgressState.AWAITING_COMPLETION
						.match(oilOrderSnapshot.getProgressState())) {
			throw new BadRequestException(ErrorCode.CODE_30156009);
		}
	}

	/**
	 * 校验删除订单
	 * 
	 * @param id
	 */
	public void validateDelete(String id) {
		OilOrderSnapshot oilOrderSnapshot = this.validateExist(id);
		// 业务状态为已取消才能删除
		if (!OilOrderDef.State.CANCELED.match(oilOrderSnapshot.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30156011);
		}
	}

}
