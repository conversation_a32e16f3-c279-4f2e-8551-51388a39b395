package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.zhihaoscm.domain.bean.dto.DashboardScopeDto;
import com.zhihaoscm.domain.bean.entity.ShipInfoServiceFee;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.meta.biz.DashboardDef;
import com.zhihaoscm.domain.meta.biz.ShipDef;
import com.zhihaoscm.domain.meta.biz.ShipInfoServiceFeeDef;
import com.zhihaoscm.domain.utils.DashboardDateUtils;
import com.zhihaoscm.service.core.service.*;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class LogisticsDashboardServiceImpl
		implements LogisticsDashboardService {

	@Lazy
	@Autowired
	private ShipService shipService;

	@Lazy
	@Autowired
	private ShippingRequirementPlatService shippingRequirementPlatService;

	@Lazy
	@Autowired
	private TransportOrderShipService transportOrderShipService;

	@Lazy
	@Autowired
	private ShipWaterGaugeService shipWaterGaugeService;

	@Lazy
	@Autowired
	private ShipInfoServiceFeeService shipInfoServiceFeeService;

	@Override
	public Optional<ShipStatisticVo> shipStatistic(Integer scope,
			LocalDateTime beginTime, LocalDateTime endTime) {
		ShipStatisticVo shipStatisticVo = new ShipStatisticVo();
		if (Objects.nonNull(scope) && !DashboardDef.Scope.TOTAL.match(scope)) {
			beginTime = DashboardDateUtils.calcBeginDate(scope);
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}

		// 船舶总量
		long count = shipService.countByState(null, beginTime, endTime, null,
				null);
		shipStatisticVo.setShipNum((count));

		// 认证船舶数量
		long shipApplyNum = shipService.countByState(
				ShipDef.State.AUTHENTICATED.getCode(), beginTime, endTime, null,
				null);
		shipStatisticVo.setShipApplyNum(shipApplyNum);

		// 船舶绑定设备数量
		Long shipBindDeviceNum = shipService.countByState(null, beginTime,
				endTime, ShipDef.RelatedDevice.YES.getCode(), null);

		// 水尺表维护数量
		Long shipWaterGaugeNum = (long) shipWaterGaugeService
				.findShipIdAndFirstCreatedTime(beginTime, endTime).size();

		shipStatisticVo.setShipApplyNum(shipApplyNum);
		shipStatisticVo.setShipBindDeviceNum(shipBindDeviceNum);
		shipStatisticVo.setShipWaterGaugeNum(shipWaterGaugeNum);

		// 船舶设备占有率
		if (shipApplyNum != 0 && shipBindDeviceNum != 0) {
			shipStatisticVo.setShipDeviceOccupancyRate(
					new BigDecimal(shipBindDeviceNum)
							.multiply(new BigDecimal(100))
							.divide(new BigDecimal(shipApplyNum), 2,
									RoundingMode.HALF_UP));
		} else {
			shipStatisticVo.setShipDeviceOccupancyRate(new BigDecimal(0));
		}
		return Optional.of(shipStatisticVo);
	}

	@Override
	public Optional<ShipStatisticVo> addShipStatistic(Integer scope,
			LocalDateTime beginTime, LocalDateTime endTime) {
		ShipStatisticVo shipStatisticVo = new ShipStatisticVo();
		if (Objects.nonNull(scope) && !DashboardDef.Scope.TOTAL.match(scope)) {
			beginTime = DashboardDateUtils.calcBeginDate(scope);
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}
		long addShipApplyNum = shipService.countByState(
				ShipDef.State.AUTHENTICATED.getCode(), beginTime, endTime, null,
				null);
		// 新增认证船舶数量
		shipStatisticVo.setAddShipApplyNum(addShipApplyNum);

		Long addShipBindDeviceNum = shipService.countByState(null, beginTime,
				endTime, ShipDef.RelatedDevice.YES.getCode(), null);

		// 水尺表维护数量
		Long addShipWaterGaugeNum = (long) shipWaterGaugeService
				.findShipIdAndFirstCreatedTime(beginTime, endTime).size();

		shipStatisticVo.setAddShipWaterGaugeNum(addShipWaterGaugeNum);

		// 新增船舶绑定设备数量
		shipStatisticVo.setAddShipBindDeviceNum(addShipBindDeviceNum);
		return Optional.of(shipStatisticVo);
	}

	@Override
	public Optional<ShippingDemandStatisticVo> shippingDemandStatistic(
			Integer scope, LocalDateTime beginTime, LocalDateTime endTime) {
		return this.calculateShippingDemandStatistics(scope, beginTime, endTime,
				false);
	}

	@Override
	public Optional<ShippingDemandStatisticVo> addShippingDemandStatistic(
			Integer scope, LocalDateTime beginTime, LocalDateTime endTime) {
		return this.calculateShippingDemandStatistics(scope, beginTime, endTime,
				true);
	}

	@Override
	public Optional<ShippingDemandStatisticVo> shippingDemandConvertAnalysis(
			Integer scope, LocalDateTime beginTime, LocalDateTime endTime) {
		if (Objects.nonNull(scope) && !DashboardDef.Scope.TOTAL.match(scope)) {
			beginTime = DashboardDateUtils.calcBeginDate(scope);
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}
		ShippingDemandStatisticVo shippingDemandStatisticVo = new ShippingDemandStatisticVo();

		// 新增平台船运需求
		Long addPlatShippingDemandNum = shippingRequirementPlatService
				.count(beginTime, endTime);
		shippingDemandStatisticVo
				.setAddPlatShippingDemandNum(addPlatShippingDemandNum);

		// 新增船运单总量
		Long addShipOrderNum = transportOrderShipService.count(beginTime,
				endTime, true);
		shippingDemandStatisticVo.setAddShipOrderNum(addShipOrderNum);

		// 船运单转化率
		if (addPlatShippingDemandNum != 0 && addShipOrderNum != 0) {
			BigDecimal shipOrderConvertRate = new BigDecimal(addShipOrderNum)
					.multiply(new BigDecimal(100))
					.divide(new BigDecimal(addPlatShippingDemandNum), 2,
							RoundingMode.HALF_UP);
			shippingDemandStatisticVo
					.setShipOrderConvertRate(shipOrderConvertRate);
		} else {
			shippingDemandStatisticVo
					.setShipOrderConvertRate(new BigDecimal(0));
		}
		return Optional.of(shippingDemandStatisticVo);
	}

	@Override
	public Optional<ShippingDemandStatisticVo> addShippingDemandAnalysis(
			Integer scope, LocalDateTime date) {
		ShippingDemandStatisticVo shippingDemandStatisticVo = new ShippingDemandStatisticVo();

		// 平台船运需求
		List<Long> platShippingDemandNums = new ArrayList<>();
		// 船运单总量
		List<Long> shipOrderNums = new ArrayList<>();

		List<DashboardScopeDto> dateScope = DashboardDateUtils
				.getDateScope(scope, date, null);
		if (CollectionUtils.isNotEmpty(dateScope)) {
			dateScope
					.forEach(
							x -> this
									.addShippingDemandStatistic(null,
											x.getBeginTime(), x.getEndTime())
									.ifPresent(vo -> {
										platShippingDemandNums.add(vo
												.getAddPlatShippingDemandNum());
										shipOrderNums
												.add(vo.getAddShipOrderNum());
									}));
		}

		shippingDemandStatisticVo
				.setPlatShippingDemandNums(platShippingDemandNums);
		shippingDemandStatisticVo.setShipOrderNums(shipOrderNums);
		return Optional.of(shippingDemandStatisticVo);
	}

	@Override
	public Optional<ShipInfoServiceFeeStatisticVo> shipInfoServiceFeeStatistic(
			Integer scope, LocalDateTime beginTime, LocalDateTime endTime) {
		ShipInfoServiceFeeStatisticVo shipInfoServiceFeeStatisticVo = new ShipInfoServiceFeeStatisticVo();
		if (Objects.nonNull(scope) && !DashboardDef.Scope.TOTAL.match(scope)) {
			beginTime = DashboardDateUtils.calcBeginDate(scope);
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}
		// 查询状态为已支付的船务信息服务费
		List<ShipInfoServiceFee> shipInfoServiceFeeList = shipInfoServiceFeeService
				.findByDateScope(beginTime, endTime);
		// 累计船务信息服务费（万元）
		BigDecimal totalShipInfoServiceFeeAmount = shipInfoServiceFeeList
				.stream().map(ShipInfoServiceFee::getAmount)
				.filter(Objects::nonNull)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		shipInfoServiceFeeStatisticVo.setTotalShipInfoServiceFeeNum(
				totalShipInfoServiceFeeAmount.divide(new BigDecimal(10000), 2,
						RoundingMode.HALF_UP));
		// 累计货主船务信息服务费（万元）
		BigDecimal totalOwnerShipInfoServiceFeeAmount = shipInfoServiceFeeList
				.stream()
				.filter(shipInfoServiceFee -> ShipInfoServiceFeeDef.Type.OWNER
						.match(shipInfoServiceFee.getType()))
				.map(ShipInfoServiceFee::getAmount).filter(Objects::nonNull)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		shipInfoServiceFeeStatisticVo.setTotalOwnerShipInfoServiceFeeNum(
				totalOwnerShipInfoServiceFeeAmount.divide(new BigDecimal(10000),
						2, RoundingMode.HALF_UP));
		// 累计船主船务信息服务费（万元）
		BigDecimal totalCaptainShipInfoServiceFeeAmount = shipInfoServiceFeeList
				.stream()
				.filter(shipInfoServiceFee -> ShipInfoServiceFeeDef.Type.CAPTAIN
						.match(shipInfoServiceFee.getType()))
				.map(ShipInfoServiceFee::getAmount).filter(Objects::nonNull)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		shipInfoServiceFeeStatisticVo.setTotalCaptainShipInfoServiceFeeNum(
				totalCaptainShipInfoServiceFeeAmount.divide(
						new BigDecimal(10000), 2, RoundingMode.HALF_UP));
		return Optional.of(shipInfoServiceFeeStatisticVo);
	}

	@Override
	public Optional<ShipInfoServiceFeeStatisticVo> addShipInfoServiceFeeAnalysis(
			Integer scope, LocalDateTime date) {
		ShipInfoServiceFeeStatisticVo shipInfoServiceFeeStatisticVo = new ShipInfoServiceFeeStatisticVo();
		List<BigDecimal> addOwnerShipInfoServiceFeeNums = new ArrayList<>();
		List<BigDecimal> addCaptainShipInfoServiceFeeNums = new ArrayList<>();

		List<DashboardScopeDto> dateScope = DashboardDateUtils
				.getDateScope(scope, null, date);
		if (CollectionUtils.isNotEmpty(dateScope)) {
			dateScope.forEach(x -> {
				shipInfoServiceFeeService
						.findAmountByType(
								ShipInfoServiceFeeDef.Type.OWNER.getCode(),
								x.getBeginTime(), x.getEndTime())
						.ifPresent(amount -> addOwnerShipInfoServiceFeeNums
								.add(amount.divide(new BigDecimal(10000), 2,
										RoundingMode.HALF_UP)));
				shipInfoServiceFeeService
						.findAmountByType(
								ShipInfoServiceFeeDef.Type.CAPTAIN.getCode(),
								x.getBeginTime(), x.getEndTime())
						.ifPresent(amount -> addCaptainShipInfoServiceFeeNums
								.add(amount.divide(new BigDecimal(10000), 2,
										RoundingMode.HALF_UP)));
			});
		}

		// 进行计算
		shipInfoServiceFeeStatisticVo.setAddOwnerShipInfoServiceFeeNums(
				addOwnerShipInfoServiceFeeNums);
		shipInfoServiceFeeStatisticVo.setAddCaptainShipInfoServiceFeeNums(
				addCaptainShipInfoServiceFeeNums);
		return Optional.of(shipInfoServiceFeeStatisticVo);
	}

	@Override
	public List<HotShipRouteVo> hotShipRouteRank(LocalDateTime beginTime,
			LocalDateTime endTime) {
		return transportOrderShipService.hotShipRouteRank(beginTime, endTime);
	}

	@Override
	public List<HotShipDestinationPortVo> hotShipDestinationPortRank(
			LocalDateTime beginTime, LocalDateTime endTime) {
		return transportOrderShipService.hotShipDestinationPortRank(beginTime,
				endTime);
	}

	@Override
	public Optional<ShippingDemandStatisticVo> shipOrderDataAnalysis(
			Integer scope, LocalDateTime date) {
		ShippingDemandStatisticVo shippingDemandStatisticVo = new ShippingDemandStatisticVo();
		// 计算新增服务费分析范围(图表)
		// 运货吨数
		List<BigDecimal> freightTons = new ArrayList<>();
		// 船运费
		List<BigDecimal> shipOrderCosts = new ArrayList<>();

		List<DashboardScopeDto> dateScope = DashboardDateUtils
				.getDateScope(scope, null, date);
		if (CollectionUtils.isNotEmpty(dateScope)) {
			dateScope.forEach(x -> {
				log.info("beginTime: {}, endTime: {}", x.getBeginTime(),
						x.getEndTime());
				transportOrderShipService
						.findTon(x.getBeginTime(), x.getEndTime())
						.ifPresent(ton -> freightTons.add(new BigDecimal(ton)
								.divide(new BigDecimal(10000), 2,
										RoundingMode.HALF_UP)));
				transportOrderShipService
						.findAmount(x.getBeginTime(), x.getEndTime())
						.ifPresent(amount -> shipOrderCosts
								.add(amount.divide(BigDecimal.valueOf(10000), 2,
										RoundingMode.HALF_UP)));
			});
		}

		shippingDemandStatisticVo.setFreightTons(freightTons);
		shippingDemandStatisticVo.setShipOrderCosts(shipOrderCosts);
		return Optional.of(shippingDemandStatisticVo);
	}

	/**
	 * 计算货主船运需求
	 *
	 * @param scope
	 * @param beginTime
	 * @param endTime
	 * @param isAdd
	 * @return
	 */
	private Optional<ShippingDemandStatisticVo> calculateShippingDemandStatistics(
			Integer scope, LocalDateTime beginTime, LocalDateTime endTime,
			boolean isAdd) {

		ShippingDemandStatisticVo shippingDemandStatisticVo = new ShippingDemandStatisticVo();

		// 处理时间范围
		if (Objects.nonNull(scope) && !DashboardDef.Scope.TOTAL.match(scope)) {
			beginTime = DashboardDateUtils.calcBeginDate(scope);
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}

		// 计算平台船运需求
		Long platShippingDemandNum = shippingRequirementPlatService
				.count(beginTime, endTime);
		if (isAdd) {
			shippingDemandStatisticVo
					.setAddPlatShippingDemandNum(platShippingDemandNum);
		} else {
			shippingDemandStatisticVo
					.setTotalPlatShippingDemandNum(platShippingDemandNum);
		}

		// 计算船运单总量
		Long shipOrderNum = transportOrderShipService.count(beginTime, endTime,
				null);
		if (isAdd) {
			shippingDemandStatisticVo.setAddShipOrderNum(shipOrderNum);
		} else {
			shippingDemandStatisticVo.setTotalShipOrderNum(shipOrderNum);
		}

		return Optional.of(shippingDemandStatisticVo);
	}
}
