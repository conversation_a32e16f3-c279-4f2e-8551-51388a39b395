package com.zhihaoscm.service.core.service.impl;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.PlatformBankAccount;
import com.zhihaoscm.service.core.mapper.PlatformBankAccountMapper;
import com.zhihaoscm.service.core.service.PlatformBankAccountService;

/**
 * <p>
 * 平台银行账户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class PlatformBankAccountServiceImpl extends
		MpLongIdBaseServiceImpl<PlatformBankAccount, PlatformBankAccountMapper>
		implements PlatformBankAccountService {

	public PlatformBankAccountServiceImpl(
			PlatformBankAccountMapper repository) {
		super(repository);
	}

	@Override
	public Page<PlatformBankAccount> paging(Integer page, Integer size) {
		LambdaQueryWrapper<PlatformBankAccount> queryWrapper = Wrappers
				.lambdaQuery(PlatformBankAccount.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.orderByDesc(PlatformBankAccount::getCreatedTime);
		return repository.selectPage(new Page<>(page, size), queryWrapper);
	}

	@Override
	public List<PlatformBankAccount> selector(String name, Integer state,
			List<Long> useTypes) {
		LambdaQueryWrapper<PlatformBankAccount> queryWrapper = Wrappers
				.lambdaQuery(PlatformBankAccount.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.select(PlatformBankAccount::getId,
				PlatformBankAccount::getAccount,
				PlatformBankAccount::getOpeningBank,
				PlatformBankAccount::getName);
		queryWrapper.like(StringUtils.isNotBlank(name),
				PlatformBankAccount::getName, name);
		queryWrapper.eq(Objects.nonNull(state), PlatformBankAccount::getState,
				state);
		queryWrapper.and(CollectionUtils.isNotEmpty(useTypes), x -> {
			for (Long useType : useTypes) {
				x.or().apply("JSON_CONTAINS(use_type, JSON_ARRAY({0}))",
						useType);
			}
		});
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<PlatformBankAccount> findByStateAndUseTypes(Integer state,
			List<Long> useTypes) {
		LambdaQueryWrapper<PlatformBankAccount> wrapper = Wrappers
				.lambdaQuery(PlatformBankAccount.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(state), PlatformBankAccount::getState,
				state);
		wrapper.and(CollectionUtils.isNotEmpty(useTypes), x -> {
			for (Long useType : useTypes) {
				x.or().apply("JSON_CONTAINS(use_type, JSON_ARRAY({0}))",
						useType);
			}
		});
		return repository.selectList(wrapper);
	}
}
