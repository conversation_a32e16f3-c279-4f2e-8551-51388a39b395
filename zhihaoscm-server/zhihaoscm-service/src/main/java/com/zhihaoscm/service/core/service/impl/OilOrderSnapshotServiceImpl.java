package com.zhihaoscm.service.core.service.impl;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.entity.OilOrder;
import com.zhihaoscm.domain.bean.entity.OilOrderSnapshot;
import com.zhihaoscm.domain.bean.entity.OilSite;
import com.zhihaoscm.domain.bean.json.notice.Messages;
import com.zhihaoscm.domain.bean.vo.OilOrderSnapshotCountVo;
import com.zhihaoscm.domain.bean.vo.OilOrderSnapshotVo;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.notice.AppType;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.core.mapper.OilOrderSnapshotMapper;
import com.zhihaoscm.service.core.service.MessageService;
import com.zhihaoscm.service.core.service.OilOrderService;
import com.zhihaoscm.service.core.service.OilOrderSnapshotService;
import com.zhihaoscm.service.core.service.OilSiteService;

/**
 * <p>
 * 油品订单快照 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@Service
public class OilOrderSnapshotServiceImpl extends
		MpStringIdBaseServiceImpl<OilOrderSnapshot, OilOrderSnapshotMapper>
		implements OilOrderSnapshotService {

	@Autowired
	private OilSiteService oilSiteService;
	@Autowired
	private OilOrderService oilOrderService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;

	public OilOrderSnapshotServiceImpl(OilOrderSnapshotMapper repository) {
		super(repository);
	}

	@Override
	public Page<OilOrderSnapshotVo> paging(Integer page, Integer size,
			String shipName, String customerInfo, String siteName,
			LocalDateTime beginTime, LocalDateTime endTime, Integer orderType,
			Integer state, Integer payState, String sortKey, String sortOrder) {
		LambdaQueryWrapper<OilOrderSnapshot> wrapper = Wrappers
				.lambdaQuery(OilOrderSnapshot.class);
		this.filterDeleted(wrapper);
		// 船舶中英文模糊搜索、MMSI号唯一搜索、订单编号唯一搜索
		wrapper.and(StringUtils.isNotBlank(shipName),
				w -> w.like(OilOrderSnapshot::getShipName, shipName).or()
						.like(OilOrderSnapshot::getShipCnName, shipName).or()
						.eq(OilOrderSnapshot::getShipId, shipName).or()
						.eq(OilOrderSnapshot::getId, shipName));
		// 联系人姓名/手机号模糊搜索
		wrapper.apply(StringUtils.isNotBlank(customerInfo),
				"(captain_info ->  '$.realName' LIKE CONCAT('%',{0},'%') "
						+ "OR captain_info -> '$.mobile' LIKE CONCAT('%',{0},'%')) ",
				customerInfo);
		// 站点名称模糊搜索
		if (StringUtils.isNotBlank(siteName)) {
			List<Long> siteIds = oilSiteService
					.findByNameLikeWithDeleted(siteName).stream()
					.map(OilSite::getId).distinct().toList();
			if (CollectionUtils.isNotEmpty(siteIds)) {
				wrapper.and(i -> i.in(OilOrderSnapshot::getOilSiteId, siteIds));
			} else {
				return new Page<>();
			}

		}
		// 时间过滤：根据每条记录的 state 选择时间字段
		// 预约中优先取后台核对后的时间再去船主填写加油时间；进行中优先取确认加油的实际时间再去报计划填写的加油实际；已完成取实际加油时间
		if (Objects.nonNull(beginTime) && Objects.nonNull(endTime)) {
			wrapper.apply("(CASE "
					+ "WHEN state = 1 OR (state = 4 AND before_cancel_state = 1) OR "
					+ "(state = 2 AND progress_state = 1) OR (state = 4 AND before_cancel_state = 2 AND progress_state = 1) "
					+ "THEN booking_refueling_time "
					+ "WHEN (state = 2 AND progress_state = 2) OR (state = 4 AND before_cancel_state = 2 AND progress_state = 2) "
					+ "THEN JSON_UNQUOTE(JSON_EXTRACT(plan_info, '$.refuelingTime')) "
					+ "WHEN state = 3 OR (state = 2 AND progress_state = 3) OR (state = 4 AND before_cancel_state = 2 AND progress_state = 3) "
					+ "THEN actual_refueling_time " + "ELSE NULL "
					+ "END) BETWEEN {0} AND {1}", beginTime, endTime);
		}
		// 订单方式
		wrapper.eq(Objects.nonNull(orderType), OilOrderSnapshot::getOrderType,
				orderType);
		// 业务状态
		wrapper.eq(Objects.nonNull(state), OilOrderSnapshot::getState, state);
		// 支付状态
		wrapper.eq(Objects.nonNull(payState), OilOrderSnapshot::getPayState,
				payState);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 预约中1、进行中2、已完成3、已取消4状态相同以创建时间倒叙
			wrapper.orderByAsc(OilOrderSnapshot::getState)
					.orderByDesc(OilOrderSnapshot::getCreatedTime);
		}
		Page<OilOrderSnapshot> paging = repository
				.selectPage(new Page<>(page, size), wrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Optional<OilOrderSnapshotVo> findVoById(String id) {
		return this.findOne(id).map(this::packVo);
	}

	@Override
	public List<OilOrderSnapshot> findByStates(List<Integer> states) {
		LambdaQueryWrapper<OilOrderSnapshot> wrapper = Wrappers
				.lambdaQuery(OilOrderSnapshot.class);
		this.filterDeleted(wrapper);
		wrapper.in(CollectionUtils.isNotEmpty(states),
				OilOrderSnapshot::getState, states);
		return repository.selectList(wrapper);
	}

	@FileId
	@Override
	public OilOrderSnapshot create(OilOrderSnapshot resource) {
		return super.create(resource);
	}

	@FileId(type = 2)
	@Override
	public OilOrderSnapshot updateAllProperties(OilOrderSnapshot resource) {
		return super.updateAllProperties(resource);
	}

	@Transactional(rollbackFor = Exception.class)
	@FileId(type = 3)
	@Override
	public void delete(String id) {
		super.delete(id);
		oilOrderService.delete(id);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@History(success = HistoryDef.OIL_ORDER_ADD, isSaveIdentity = true, bizNo = "{{#oilOrder.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_OIL_ORDER)
	public OilOrder adminBooking(OilOrder oilOrder) {
		return oilOrderService.booking(oilOrder);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@History(success = HistoryDef.OIL_ORDER_CHECK, isSaveChange = true, isSaveIdentity = true, bizNo = "{{#snapshot.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_OIL_ORDER)
	public void checkRefueling(OilOrderSnapshot snapshot, Integer checkType) {
		// 根据加油量和油价，相乘得到预约油费
		if (Objects.nonNull(snapshot.getBookingRefuelingVolume())
				&& Objects.nonNull(snapshot.getBookingRefuelingPrice())) {
			snapshot.setBookingRefuelingCost(
					snapshot.getBookingRefuelingVolume()
							.multiply(snapshot.getBookingRefuelingPrice()));
		}
		snapshot = this.updateAllProperties(snapshot);
		if (OilOrderDef.CheckType.SUBMIT.match(checkType)) {
			// 同步修改订单
			OilOrder oilOrder = this.toOrder(snapshot);
			oilOrderService.updateAllProperties(oilOrder);
			// 业务状态从“预约中”流转为“进行中”，需要发送企微消息
			oilOrderService.sendWxNotice(oilOrder,
					OilOrderDef.NoticeType.TO_BE_PLAN);
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void plan(OilOrderSnapshot snapshot) {
		// 进行中状态改为待确认加油
		snapshot.setProgressState(
				OilOrderDef.ProgressState.AWAITING_CONFIRMATION.getCode());
		snapshot = this.updateAllProperties(snapshot);
		// 同步修改订单
		OilOrder oilOrder = this.toOrder(snapshot);
		oilOrderService.updateAllProperties(oilOrder);

		// 此时后台已完成报计划，如果船主没有支付油费，需要给船主发送短信和站内
		if (CollectionUtils.isEmpty(snapshot.getPayFileIds())) {
			this.sendPayOilFeeNotice(snapshot);
		}
		// 此时已完成报计划，如果油费已确认，需要给管理后台的有油品订单"处理"权限的人发送企微消息
		if (OilOrderDef.PayState.SUCCESS.match(snapshot.getPayState())) {
			oilOrderService.sendWxNotice(oilOrder,
					OilOrderDef.NoticeType.TO_BE_CONFIRM);

			// 还需要给船主发送短信和站内，提醒船主去确认加油
			this.sendConfirmRefuelingNotice(snapshot);
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void confirmRefueling(OilOrder oilOrder) {
		oilOrderService.confirmRefueling(oilOrder);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void complete(String id) {
		// 业务状态改为已完成、进行中状态改为已完成
		this.findOne(id).ifPresent(oilOrderSnapshot -> {
			oilOrderSnapshot.setState(OilOrderDef.State.COMPLETED.getCode());
			oilOrderSnapshot.setProgressState(
					OilOrderDef.ProgressState.COMPLETED.getCode());
			OilOrderSnapshot snapshot = this
					.updateAllProperties(oilOrderSnapshot);
			// 同步修改订单
			OilOrder oilOrder = this.toOrder(snapshot);
			oilOrderService.updateAllProperties(oilOrder);
		});
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void cancel(String id) {
		oilOrderService.cancel(id);
	}

	@Override
	public OilOrderSnapshotCountVo statisticsOilOrderSnapshot(Boolean hasFull) {
		OilOrderSnapshotCountVo vo = new OilOrderSnapshotCountVo();
		vo.setBookingCount(0L);
		vo.setAwaitingPlan(0L);
		vo.setAwaitingConfirmation(0L);
		vo.setAwaitingCompletion(0L);

		if (hasFull) {
			List<OilOrderSnapshot> oilOrderSnapshots = this
					.findByStates(List.of(OilOrderDef.State.BOOKING.getCode(),
							OilOrderDef.State.IN_PROGRESS.getCode()));
			if (CollectionUtils.isNotEmpty(oilOrderSnapshots)) {
				// 预约中
				vo.setBookingCount(oilOrderSnapshots.stream()
						.filter(item -> Objects.nonNull(item)
								&& OilOrderDef.State.BOOKING
										.match(item.getState()))
						.count());

				// 进行中的油品订单
				List<OilOrderSnapshot> inProgressList = oilOrderSnapshots
						.stream()
						.filter(item -> Objects.nonNull(item)
								&& OilOrderDef.State.IN_PROGRESS
										.match(item.getState()))
						.toList();

				// 待计划
				vo.setAwaitingPlan(inProgressList.stream()
						.filter(item -> Objects.nonNull(item.getProgressState())
								&& OilOrderDef.ProgressState.AWAITING_PLAN
										.match(item.getProgressState()))
						.count());
				// 待确认加油
				vo.setAwaitingConfirmation(inProgressList.stream()
						.filter(item -> Objects.nonNull(item.getProgressState())
								&& OilOrderDef.ProgressState.AWAITING_CONFIRMATION
										.match(item.getProgressState()))
						.count());
				// 待完成加油
				vo.setAwaitingCompletion(inProgressList.stream()
						.filter(item -> Objects.nonNull(item.getProgressState())
								&& OilOrderDef.ProgressState.AWAITING_COMPLETION
										.match(item.getProgressState()))
						.count());
			}
		}
		return vo;
	}

	@Override
	public void sendConfirmRefuelingNotice(OilOrderSnapshot snapshot) {
		// 船主支付了油费且报计划了，需要给船主发送短信和站内，提醒船主去确认加油

		messageService.sendNotice(Messages.builder()
				.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
						SendType.USERMESSAGE.getCode()))
				.type(UserMessageDef.MessageType.TRANSFORM.getCode())
				.title(MessageFormat.format(
						UserMessageConstants.REMIND_CONFIRMATION_REFUELING_TEMPLATE,
						snapshot.getId()))
				.receiptors(List.of(String.valueOf(snapshot.getCaptainId())))
				.url(UserMessageConstants.OIL_ORDER_DETAIL_PAGE)
				.role(AppTypeDef.AppType.CHUAN_WU.getCode())
				.detailId(String.valueOf(snapshot.getId()))
				.initiator(UserMessageDef.BusinessInitiator.initiate.getCode())
				.templateCode(wxSubscriptionProperties
						.getCarrierConfirmRefuelingCode())
				.params(Map.of("index", snapshot.getId()))
				.mobile(snapshot.getCaptainInfo().getMobile()).build());

		// 发送app推送
		messageService.sendNotice(Messages.builder()
				.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
				.type(UserMessageDef.MessageType.TRANSFORM.getCode())
				.title(UserMessageConstants.TRANSPORT_TITLE)
				.content(MessageFormat.format(
						UserMessageConstants.REMIND_CONFIRMATION_REFUELING_TEMPLATE,
						snapshot.getId()))
				.appTypes(List.of(AppType.SHIP))
				.bizNo(String.valueOf(snapshot.getId()))
				.moduleType(UserMessageConstants.OIL_ORDER_DETAIL_PAGE)
				.receiptors(List.of(String.valueOf(snapshot.getCaptainId())))
				.build());
	}

	/**
	 * 给船主发送支付油费的提醒
	 *
	 * @param snapshot
	 */
	private void sendPayOilFeeNotice(OilOrderSnapshot snapshot) {
		// 后台已完成报计划，如果船主没有支付油费，需要给船主发送短信和站内，提醒船主去支付油费

		messageService.sendNotice(Messages.builder()
				.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
						SendType.USERMESSAGE.getCode()))
				.type(UserMessageDef.MessageType.TRANSFORM.getCode())
				.title(MessageFormat.format(
						UserMessageConstants.REMIND_PAY_OIL_FEE_TEMPLATE,
						snapshot.getId()))
				.receiptors(List.of(String.valueOf(snapshot.getCaptainId())))
				.url(UserMessageConstants.OIL_ORDER_DETAIL_PAGE)
				.role(AppTypeDef.AppType.CHUAN_WU.getCode())
				.detailId(String.valueOf(snapshot.getId()))
				.initiator(UserMessageDef.BusinessInitiator.initiate.getCode())
				.templateCode(
						wxSubscriptionProperties.getCarrierPayOilFeeCode())
				.params(Map.of("index", snapshot.getId()))
				.mobile(snapshot.getCaptainInfo().getMobile()).build());

		// 发送app推送
		messageService.sendNotice(Messages.builder()
				.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
				.type(UserMessageDef.MessageType.TRANSFORM.getCode())
				.title(UserMessageConstants.TRANSPORT_TITLE)
				.content(MessageFormat.format(
						UserMessageConstants.REMIND_PAY_OIL_FEE_TEMPLATE,
						snapshot.getId()))
				.appTypes(List.of(AppType.SHIP))
				.bizNo(String.valueOf(snapshot.getId()))
				.moduleType(UserMessageConstants.OIL_ORDER_DETAIL_PAGE)
				.receiptors(List.of(String.valueOf(snapshot.getCaptainId())))
				.build());
	}

	/**
	 * 组装数据
	 */
	private OilOrderSnapshotVo packVo(OilOrderSnapshot oilOrder) {
		OilOrderSnapshotVo vo = new OilOrderSnapshotVo();
		vo.setOilOrder(oilOrder);
		if (Objects.nonNull(oilOrder.getOilSiteId())) {
			vo.setOilSite(oilSiteService
					.findOneWithDeleted(oilOrder.getOilSiteId()).orElse(null));
		}
		return vo;
	}

	/**
	 * 组装数据
	 */
	private List<OilOrderSnapshotVo> packVo(
			List<OilOrderSnapshot> oilOrderList) {
		if (CollectionUtils.isEmpty(oilOrderList)) {
			return List.of();
		}
		List<Long> oilSiteIds = oilOrderList.stream()
				.map(OilOrderSnapshot::getOilSiteId).toList();
		// 油站列表（包括已删除的）
		List<OilSite> oilSiteList = oilSiteService.findByIds(oilSiteIds);
		Map<Long, OilSite> oilSiteMap = oilSiteList.stream().collect(
				Collectors.toMap(BaseEntityWithLongId::getId, item -> item));

		return oilOrderList.stream().map(item -> {
			OilOrderSnapshotVo vo = new OilOrderSnapshotVo();
			vo.setOilOrder(item);
			if (Objects.nonNull(item.getOilSiteId())) {
				vo.setOilSite(oilSiteMap.get(item.getOilSiteId()));
			}
			return vo;
		}).toList();
	}

	/**
	 * 快照转订单
	 *
	 * @param snapshot
	 * @return
	 */
	private OilOrder toOrder(OilOrderSnapshot snapshot) {
		OilOrder oilOrder = new OilOrder();
		BeanUtils.copyProperties(snapshot, oilOrder);
		return oilOrder;
	}
}
