package com.zhihaoscm.service.resource.form.business.opportunity;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.domain.bean.entity.BusinessOpportunity;
import com.zhihaoscm.domain.meta.biz.BusinessOpportunityDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "BusinessOpportunityForm", title = "商机表单")
public class BusinessOpportunityForm {

	@Schema(title = "是否关联用户")
	private Integer isRelatedOwner;

	@Schema(title = "关联用户id")
	private Long ownerId;

	@Schema(title = "标题")
	@Length(min = 1, max = 32, message = ErrorCode.CODE_30194003)
	@NotBlank(message = ErrorCode.CODE_30194002)
	private String title;

	@Schema(description = "类型")
	@NotNull(message = ErrorCode.CODE_30194004)
	@Range(min = 1, max = 4, message = ErrorCode.CODE_30194005)
	private Integer type;

	@Schema(title = "省编码")
	@NotBlank(message = ErrorCode.CODE_30194006)
	private String provinceCode;

	@Schema(title = "城市编码")
	@NotBlank(message = ErrorCode.CODE_30194007)
	private String cityCode;

	@Schema(title = "联系人(发布人)")
	@NotBlank(message = ErrorCode.CODE_30194012)
	private String contactName;

	@Schema(title = "联系电话")
	@NotBlank(message = ErrorCode.CODE_30194013)
	private String contactPhone;

	@Schema(description = "详细说明")
	@Length(max = 500, message = ErrorCode.CODE_30194014)
	private String detailedDescription;

	public BusinessOpportunity convertToEntity() {
		BusinessOpportunity businessOpportunity = new BusinessOpportunity();
		businessOpportunity.setIsRelatedOwner(this.getIsRelatedOwner());
		businessOpportunity.setOwnerId(this.getOwnerId());
		return this.updateData(businessOpportunity);
	}

	private BusinessOpportunity updateData(
			BusinessOpportunity businessOpportunity) {
		businessOpportunity.setTitle(this.getTitle());
		businessOpportunity.setType(this.getType());
		businessOpportunity.setProvinceCode(this.getProvinceCode());
		businessOpportunity.setCityCode(this.getCityCode());
		businessOpportunity.setContactName(this.getContactName());
		businessOpportunity.setContactPhone(this.getContactPhone());
		businessOpportunity
				.setDetailedDescription(this.getDetailedDescription());
		// 提交后状态都扭转成待审核
		businessOpportunity.setState(
				BusinessOpportunityDef.State.TO_BE_REVIEWED.getCode());
		return businessOpportunity;
	}

	public BusinessOpportunity convertToEntity(
			BusinessOpportunity businessOpportunity) {
		return this.updateData(businessOpportunity);
	}
}
