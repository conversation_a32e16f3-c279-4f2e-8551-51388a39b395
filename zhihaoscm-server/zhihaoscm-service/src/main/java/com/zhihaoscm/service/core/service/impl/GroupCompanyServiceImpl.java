package com.zhihaoscm.service.core.service.impl;

import java.util.*;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.bean.entity.GroupCompany;
import com.zhihaoscm.domain.bean.vo.GroupCompanyVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.service.core.mapper.GroupCompanyMapper;
import com.zhihaoscm.service.core.service.FileService;
import com.zhihaoscm.service.core.service.GroupCompanyService;

/**
 * <p>
 * 集团管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class GroupCompanyServiceImpl
		extends MpStringIdBaseServiceImpl<GroupCompany, GroupCompanyMapper>
		implements GroupCompanyService {

	@Autowired
	private FileService fileService;

	public GroupCompanyServiceImpl(GroupCompanyMapper repository) {
		super(repository);
	}

	@Override
	public Page<GroupCompanyVo> paging(Integer page, Integer size,
			String keyword, String sortKey, String sortOrder) {
		LambdaQueryWrapper<GroupCompany> queryWrapper = Wrappers
				.lambdaQuery(GroupCompany.class);
		this.filterDeleted(queryWrapper);
		// 集团名称模糊查询
		queryWrapper.like(StringUtils.isNotBlank(keyword),
				GroupCompany::getName, keyword);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 按照创建时间倒序
			queryWrapper.orderByDesc(GroupCompany::getCreatedTime);
		}
		Page<GroupCompany> paging = repository
				.selectPage(new Page<>(page, size), queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@FileId
	@Override
	public GroupCompany create(GroupCompany resource) {
		// 设置集团id
		resource.setId(this.generateId());
		// 设置是否关联供应商为未关联
		resource.setUsed(CommonDef.Symbol.NO.getCode());
		return super.create(resource);
	}

	@FileId(type = 2)
	@Override
	public GroupCompany updateAllProperties(GroupCompany resource) {
		return super.updateAllProperties(resource);
	}

	@FileId(type = 3)
	@Override
	public void delete(String id) {
		super.delete(id);
	}

	@Override
	public List<GroupCompany> findByNameAndNotInId(String name, String neId) {
		LambdaQueryWrapper<GroupCompany> wrapper = Wrappers
				.lambdaQuery(GroupCompany.class);
		this.filterDeleted(wrapper);
		wrapper.eq(StringUtils.isNotBlank(name), GroupCompany::getName, name)
				.ne(StringUtils.isNotBlank(neId), GroupCompany::getId, neId);
		return repository.selectList(wrapper);
	}

	/**
	 * 生成集团编号
	 * 
	 * @return
	 */
	private String generateId() {
		StringRedisClient client = SpringUtil.getBean(StringRedisClient.class);
		Long autoId = client.getRedisTemplate().opsForValue()
				.increment(RedisKeys.Cache.GROUP_COMPANY, 1);
		return StringUtils.leftPad(String.valueOf(autoId), 3, "0");
	}

	/**
	 * 组装vos
	 */
	private List<GroupCompanyVo> packVo(List<GroupCompany> records) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		// 主图文件id集合
		Set<Long> fileIds = new HashSet<>();
		records.forEach(record -> fileIds.add(record.getImgMainId()));
		Map<Long, File> idFileMap = fileService
				.getIdMap(new ArrayList<>(fileIds));

		return records.stream().map(record -> {
			GroupCompanyVo vo = new GroupCompanyVo();
			vo.setGroupCompany(record);
			// 设置主图
			vo.setMainFile(idFileMap.get(record.getImgMainId()));

			return vo;
		}).toList();
	}
}
