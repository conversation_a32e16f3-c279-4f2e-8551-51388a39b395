package com.zhihaoscm.service.resource.form.shipping.customer;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AutoCodeDef;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementPlatDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.RegexUtils;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

@Data
@Schema(name = "ShippingRequirementCustomerForm", title = "船运需求表单对象")
public class CustomShippingRequirementCustomerForm {

	@Schema(description = "货品类型")
	@NotBlank(message = ErrorCode.CODE_30134011)
	@Length(min = 1, max = 32, message = ErrorCode.CODE_30134012)
	private String goodsType;

	@Schema(description = "船型")
	private Integer shipType;

	@Schema(description = "排水槽")
	private Integer drainageChannel;

	@Schema(description = "始发地码头id")
	@NotNull(message = ErrorCode.CODE_30134015)
	private Long sourcePortId;

	@Schema(description = "始发地码头名称")
	@NotBlank(message = ErrorCode.CODE_30134016)
	private String sourcePortName;

	@Schema(description = "目的地码头id")
	@NotNull(message = ErrorCode.CODE_30134017)
	private Long destinationPortId;

	@Schema(description = "目的地码头名称")
	@NotBlank(message = ErrorCode.CODE_30134018)
	private String destinationPortName;

	@Schema(description = "航线id")
	private Long shipRouteId;

	@Schema(description = "航线始发地")
	private String shipRouteSource;

	@Schema(description = "航线目的地")
	private String shipRouteDestination;

	@Schema(description = "意向单价")
	@Digits(integer = 10, fraction = 2, message = ErrorCode.CODE_30134020)
	private BigDecimal unitPrice;

	@Schema(description = "意向吨位")
	@NotNull(message = ErrorCode.CODE_30134021)
	@Positive(message = ErrorCode.CODE_30134022)
	private Integer ton;

	@Schema(description = "装载日期")
	@NotNull(message = ErrorCode.CODE_30134023)
	private LocalDate loadDate;

	@Schema(description = "宽限天数")
	private Integer loadDays;

	@Schema(description = "装卸天数")
	private Integer loadUnloadDays;

	@Schema(description = "滞期费")
	@Digits(integer = 10, fraction = 2, message = ErrorCode.CODE_30134026)
	private BigDecimal demurrageFee;

	@Schema(description = "海事费用")
	private Integer maritimeAffairsFee;

	@Schema(description = "吨位不随船时最大吨位数")
	@Positive(message = ErrorCode.CODE_30134028)
	private Integer dwtMax;

	@Schema(description = "吨位不随船时最小吨位数")
	@Positive(message = ErrorCode.CODE_30134029)
	private Integer dwtMin;

	@Schema(description = "船运定金")
	private BigDecimal deposit;

	@Schema(description = "船运费用支付方式")
	private Integer depositPaymentMethod;

	@Schema(description = "联系人")
	@Length(min = 1, max = 32, message = ErrorCode.CODE_30134030)
	private String contact;

	@Schema(description = "联系电话")
	@Pattern(regexp = RegexUtils.PHONE_CARD_PATTERN, message = ErrorCode.CODE_30134032)
	private String mobile;

	@Schema(description = "补充约定")
	@Length(max = 200, message = ErrorCode.CODE_30134033)
	private String replenishAppoint;

	@Schema(description = "下单类型:0委托平台下单(待发布) 1自主发布找船需求(已发布)")
	@Range(min = 0, max = 1, message = ErrorCode.CODE_30134037)
	private Integer orderType;

	@Schema(description = "委托平台下单的操作平台:1PC端 2小程序或APP")
	@Range(min = 1, max = 2, message = ErrorCode.CODE_30134038)
	private Integer operatingPlatform;

	public ShippingRequirementPlat convertToEntity(
			StringRedisClient redisClient) {
		ShippingRequirementPlat plat = new ShippingRequirementPlat();
		plat.setId(AutoCodeDef.CREATE_AUTO_CODE.apply(redisClient,
				AutoCodeDef.BusinessCode.REQUIREMENT_PLAT_PREFIX.getCode(),
				RedisKeys.Cache.SHIPPING_REQUIREMENT_PLAT_CODE_GENERATOR, "01",
				5, AutoCodeDef.DATE_TYPE.yyMM));
		// 设置接单状态
		plat.setOrderAcceptanceState(
				ShippingRequirementPlatDef.OrderAcceptanceState.CONFIRMED
						.getCode());
		// 设置船运需求状态(处理旧版本，如果没传，默认为自主发布的状态)
		// 委托平台下单：后台船运需求状态为“待发布”0
		// 自主发布找船需求逻辑不变，即船运需求状态为“已发布”1
		if (Objects.nonNull(this.orderType)) {
			plat.setState(this.orderType);
		} else {
			plat.setState(
					ShippingRequirementPlatDef.State.TO_BE_ALLOCATED.getCode());
		}
		// 设置数据来源为平台船运需求
		plat.setDataSource(
				ShippingRequirementPlatDef.DataSource.INNER.getCode());

		// 需求等级默认为1级
		plat.setDemandLevel(
				ShippingRequirementPlatDef.DemandLevel.ONE.getCode());
		// 货主的船务信息服务费默认为0
		plat.setShipInfoServiceFee(BigDecimal.ZERO);
		// 电联类型默认为手机号
		plat.setElectricContactNumberType(
				ShippingRequirementPlatDef.ElectricContactNumberType.PHONE
						.getCode());
		// plat.setElectricContactNumber(this.mobile);
		return this.updateDate(plat);
	}

	private ShippingRequirementPlat updateDate(ShippingRequirementPlat plat) {
		// 委托平台下单只需要：船型、排水槽、始发地、目的地、期望装载日期、货物类型、运货吨数
		// 小程序和app的委托，船型和排水槽是必填。PC的委托，船型和排水槽是不填
		// 只有委托+PC的船型和排水槽是不填
		if (!ShippingRequirementPlatDef.State.TO_BE_PUBLISH
				.match(plat.getState())
				|| !ShippingRequirementPlatDef.OperatingPlatform.PC
						.match(this.operatingPlatform)) {
			// orderType默认是自主发布，operatingPlatform默认是移动端
			plat.setDrainageChannel(this.getDrainageChannel());
			plat.setShipType(this.shipType);
		}
		plat.setGoodsType(this.goodsType);
		plat.setSourcePortId(this.sourcePortId);
		plat.setSourcePortName(this.sourcePortName);
		plat.setDestinationPortId(this.destinationPortId);
		plat.setDestinationPortName(this.destinationPortName);
		plat.setFreightTons(this.ton);
		plat.setLoadDate(this.loadDate);
		if (ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
				.match(plat.getState())) {

			plat.setContact(this.contact);
			plat.setMobile(this.mobile);

			plat.setShipRouteId(this.shipRouteId);
			plat.setShipRouteSource(this.shipRouteSource);
			plat.setShipRouteDestination(this.shipRouteDestination);
			plat.setUnitPrice(this.unitPrice);

			plat.setTonMax(this.dwtMax);
			plat.setTonMin(this.dwtMin);

			plat.setLoadDays(this.loadDays);
			plat.setLoadUnloadDays(this.loadUnloadDays);
			plat.setDemurrageFee(this.demurrageFee);
			plat.setMaritimeAffairsFee(this.getMaritimeAffairsFee());
			plat.setReplenishAppoint(this.getReplenishAppoint());
			plat.setDeposit(this.deposit);
			plat.setPayType(this.getDepositPaymentMethod());

		}
		return plat;
	}

}
