package com.zhihaoscm.service.core.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.SampleApply;
import com.zhihaoscm.service.core.mapper.SampleApplyMapper;
import com.zhihaoscm.service.core.service.SampleApplyService;

/**
 * <p>
 * 申请寄样信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
public class SampleApplyServiceImpl
		extends MpLongIdBaseServiceImpl<SampleApply, SampleApplyMapper>
		implements SampleApplyService {

	public SampleApplyServiceImpl(SampleApplyMapper repository) {
		super(repository);
	}

	@Override
	public Page<SampleApply> paging(Integer page, Integer size,
			String productId, Integer state, LocalDateTime beginTime,
			LocalDateTime endTime, String sortKey, String sortOrder,
			String keyword) {
		LambdaQueryWrapper<SampleApply> wrapper = Wrappers
				.lambdaQuery(SampleApply.class);
		this.filterDeleted(wrapper);
		// 采购账号信息：实名、组织机构认证、手机号，模糊搜索
		wrapper.apply(StringUtils.isNotBlank(keyword),
				"(customer_enterprise -> '$.institutionName' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_enterprise -> '$.realName' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_enterprise -> '$.mobile' LIKE CONCAT('%',{0},'%'))",
				keyword);
		wrapper.eq(StringUtils.isNotBlank(productId), SampleApply::getProductId,
				productId);
		wrapper.eq(Objects.isNull(state), SampleApply::getState, state);
		wrapper.between(Objects.nonNull(beginTime), SampleApply::getCreatedTime,
				beginTime, endTime);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(" ORDER BY " + sortKey + " " + sortOrder);
		} else {
			wrapper.orderByAsc(SampleApply::getState);
			wrapper.orderByDesc(SampleApply::getCreatedTime);
		}
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public List<SampleApply> findByProductIdAndStates(String productId,
			List<Integer> states) {
		LambdaQueryWrapper<SampleApply> wrapper = Wrappers
				.lambdaQuery(SampleApply.class);
		this.filterDeleted(wrapper);
		wrapper.eq(SampleApply::getProductId, productId);
		wrapper.in(CollectionUtils.isNotEmpty(states), SampleApply::getState,
				states);
		return repository.selectList(wrapper);
	}

	@Override
	public List<SampleApply> findByProductIdAndCustomIdAndState(
			List<String> productIds, Long customId, List<Integer> states) {
		LambdaQueryWrapper<SampleApply> wrapper = Wrappers
				.lambdaQuery(SampleApply.class);
		this.filterDeleted(wrapper);
		wrapper.in(CollectionUtils.isNotEmpty(productIds),
				SampleApply::getProductId, productIds);
		wrapper.eq(Objects.nonNull(customId), SampleApply::getCustomerId,
				customId);
		wrapper.in(CollectionUtils.isNotEmpty(states), SampleApply::getState,
				states);
		wrapper.orderByDesc(SampleApply::getUpdatedTime);
		return repository.selectList(wrapper);
	}
}
