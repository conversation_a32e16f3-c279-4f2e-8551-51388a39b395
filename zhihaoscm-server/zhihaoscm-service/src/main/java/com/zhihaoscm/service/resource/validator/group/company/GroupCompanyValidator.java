package com.zhihaoscm.service.resource.validator.group.company;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.GroupCompany;
import com.zhihaoscm.service.core.service.GroupCompanyService;
import com.zhihaoscm.service.resource.form.group.company.GroupCompanyForm;

@Component
public class GroupCompanyValidator {
	@Autowired
	private GroupCompanyService service;

	/**
	 * 校验是否存在
	 *
	 * @param id
	 */
	public GroupCompany validateExist(String id) {
		return service.findOne(id)
				.orElseThrow(() -> new BadRequestException("数据不存在"));
	}

	/**
	 * 校验新增
	 *
	 * @param form
	 */
	public void validateCreate(GroupCompanyForm form) {
		// 校验集团名称不可重复
		this.validateName(form.getName(), null);
	}

	/**
	 * 校验修改
	 * 
	 * @param form
	 */
	public GroupCompany validateUpdate(String id, GroupCompanyForm form) {
		GroupCompany groupCompany = this.validateExist(id);
		// 校验集团名称不可重复
		this.validateName(form.getName(), id);
		return groupCompany;
	}

	/**
	 * 校验删除
	 */
	public void validateDelete(String id) {
		GroupCompany groupCompany = this.validateExist(id);
		// 是否关联供应商 为 未关联 才能删除
		if (!CommonDef.Symbol.NO.match(groupCompany.getUsed())) {
			throw new BadRequestException("该集团已关联供应商，不允许删除");
		}
	}

	/**
	 * 验证集团名称
	 *
	 * @param name
	 * @param neId
	 */
	private void validateName(String name, String neId) {
		List<GroupCompany> groupCompanyList = service.findByNameAndNotInId(name,
				neId);
		if (CollectionUtils.isNotEmpty(groupCompanyList)) {
			throw new BadRequestException("集团名称已存在");
		}
	}

}
