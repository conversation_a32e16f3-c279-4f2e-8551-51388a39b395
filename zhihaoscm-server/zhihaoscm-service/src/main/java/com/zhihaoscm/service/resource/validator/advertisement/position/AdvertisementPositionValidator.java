package com.zhihaoscm.service.resource.validator.advertisement.position;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.AdvertisementPosition;
import com.zhihaoscm.domain.meta.biz.AdvertisementPositionDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.AdvertisementPositionService;
import com.zhihaoscm.service.resource.form.advertisement.position.AdvertisementItem;
import com.zhihaoscm.service.resource.form.advertisement.position.AdvertisementPositionForm;

/**
 * 广告位校验器
 *
 */
@Component
public class AdvertisementPositionValidator {

	@Autowired
	private AdvertisementPositionService service;

	/**
	 * 校验删除
	 *
	 * @param id
	 * @return
	 */
	public AdvertisementPosition validateUpdate(Long id,
			AdvertisementPositionForm form) {
		AdvertisementPosition advertisementPosition = this.validateExist(id);
		// 校验广告位置（例如找商机广告位）与广告位（例如3号广告位）与展示形式（例如单图固定）
		this.validatePositionAndDisplayForm(form.getPositionType(),
				form.getPosition(), form.getDisplayForm());
		// 多图轮
		if (AdvertisementPositionDef.DisplayForm.MULTI_IMAGE_ROTATION
				.match(form.getDisplayForm())) {
			if (Objects.isNull(form.getTimeInterval())) {
				// 轮播间隔不能为空
				throw new BadRequestException(ErrorCode.CODE_30033004);
			}
			// 时间间隔存在
			AdvertisementPositionDef.RotationInterval rotationInterval = AdvertisementPositionDef.RotationInterval
					.from(form.getTimeInterval());
			if (Objects.isNull(rotationInterval)) {
				throw new BadRequestException(ErrorCode.CODE_30033013);
			}
			List<AdvertisementItem> content = form.getContent();
			for (AdvertisementItem advertisementItem : content) {
				if (Objects.isNull(advertisementItem.getSort())) {
					// 排序不能为空
					throw new BadRequestException(ErrorCode.CODE_30033007);
				}
				// 链接类型不为空
				if (Objects.nonNull(advertisementItem.getLinkType())) {
					AdvertisementPositionDef.LinkType linkType = AdvertisementPositionDef.LinkType
							.from(advertisementItem.getLinkType());
					if (Objects.isNull(linkType)) {
						throw new BadRequestException(ErrorCode.CODE_30033020);
					}
					switch (AdvertisementPositionDef.LinkType
							.from(advertisementItem.getLinkType())) {
						case ADVERT -> {
							if (Objects
									.isNull(advertisementItem.getAdvertId())) {
								throw new BadRequestException(
										ErrorCode.CODE_30033021);
							}
						}
						case LINK -> {
							if (StringUtils
									.isBlank(advertisementItem.getLink())) {
								throw new BadRequestException(
										ErrorCode.CODE_30033022);
							}
						}
					}
				}
			}
			// 判断存在重复值
			long count = content.stream().map(AdvertisementItem::getSort)
					.distinct().count();
			if (content.size() != count) {
				throw new BadRequestException(ErrorCode.CODE_30033011);
			}
		}
		return advertisementPosition;
	}

	/**
	 * 校验存在
	 *
	 * @param id
	 * @return
	 */
	public AdvertisementPosition validateExist(Long id) {
		AdvertisementPosition advertisementPosition = service.findOne(id)
				.orElse(null);
		if (Objects.isNull(advertisementPosition)) {
			throw new BadRequestException(ErrorCode.CODE_30033001);
		}
		return advertisementPosition;
	}

	/**
	 * 校验广告位置（例如找商机广告位）与广告位（例如3号广告位）与展示形式（例如单图固定）
	 * 
	 * @param positionType
	 * @param position
	 * @param displayForm
	 */
	private void validatePositionAndDisplayForm(Integer positionType,
			Integer position, Integer displayForm) {
		// 所有的广告位置都有 1号广告位，且能单图或多图
		// 所有的广告位置都有 2号广告位，但是找货源、找商机才能单图或多图，其余位置只能单图
		// 找货源、找商机才有 3号广告位，且只能单图
		if (AdvertisementPositionDef.Position.TWO.match(position)
				&& AdvertisementPositionDef.DisplayForm.MULTI_IMAGE_ROTATION
						.match(displayForm)) {
			// 2号广告位，且多图时
			// 不是找货源，也不是找商机，则报错
			if (!AdvertisementPositionDef.PositionType.FIND_GOODS_SOURCE_PLACEMENT
					.match(positionType)
					&& !AdvertisementPositionDef.PositionType.FIND_BUSINESS_OPPORTUNITY_PLACEMENT
							.match(positionType)) {
				throw new BadRequestException(ErrorCode.CODE_30033033);
			}
		} else if (AdvertisementPositionDef.Position.THREE.match(position)) {
			// 3号广告位时
			// 不是找货源，也不是找商机，则报错
			if (!AdvertisementPositionDef.PositionType.FIND_GOODS_SOURCE_PLACEMENT
					.match(positionType)
					&& !AdvertisementPositionDef.PositionType.FIND_BUSINESS_OPPORTUNITY_PLACEMENT
							.match(positionType)) {
				throw new BadRequestException(ErrorCode.CODE_30033034);
			}
			// 不是单图则报错
			if (!AdvertisementPositionDef.DisplayForm.FIXED_SINGLE_IMAGE
					.match(displayForm)) {
				throw new BadRequestException(ErrorCode.CODE_30033033);
			}
		}
	}
}
