package com.zhihaoscm.service.resource.validator.platform;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.PlatformBankAccount;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.PlatformBankAccountService;
import com.zhihaoscm.service.resource.form.platform.PlatformBankAccountForm;

@Component
public class PlatformValidator {

	@Autowired
	private PlatformBankAccountService platformBankAccountService;

	/**
	 * 验证存在
	 *
	 * @param id
	 * @return
	 */
	private PlatformBankAccount validateExist(Long id) {
		PlatformBankAccount platformBankAccount = platformBankAccountService
				.findOne(id).orElse(null);
		if (Objects.isNull(platformBankAccount)) {
			throw new BadRequestException(ErrorCode.CODE_30105004);
		}
		return platformBankAccount;
	}

	/**
	 * 校验新增
	 *
	 * @param form
	 */
	public void validateCreate(PlatformBankAccountForm form) {
		// 如果使用场景不为空 则校验是否存在启用状态下占用了此场景的数据
		if (CollectionUtils.isNotEmpty(form.getUseType())) {
			List<PlatformBankAccount> platformBankAccountList = platformBankAccountService
					.findByStateAndUseTypes(CommonDef.Symbol.YES.getCode(),
							form.getUseType());
			if (CollectionUtils.isNotEmpty(platformBankAccountList)) {
				throw new BadRequestException(ErrorCode.CODE_30105009);
			}
		}
	}

	/**
	 * 验证修改平台银行账户
	 *
	 * @param id
	 * @param form
	 * @return
	 */
	public PlatformBankAccount validateUpdate(Long id,
			PlatformBankAccountForm form) {
		PlatformBankAccount platformBankAccount = this.validateExist(id);
		if (CollectionUtils.isNotEmpty(form.getUseType())) {
			List<PlatformBankAccount> platformBankAccountList = platformBankAccountService
					.findByStateAndUseTypes(CommonDef.Symbol.YES.getCode(),
							form.getUseType());
			// 排除相同的id
			platformBankAccountList
					.removeIf(p -> Objects.equals(p.getId(), id));
			if (CollectionUtils.isNotEmpty(platformBankAccountList)) {
				throw new BadRequestException(ErrorCode.CODE_30105009);
			}
		}

		form.update(platformBankAccount);
		return platformBankAccount;
	}

	/**
	 * 验证更新平台银行账户状态
	 *
	 * @param id
	 * @param state
	 * @return
	 */
	public PlatformBankAccount validateUpdateState(Long id, Integer state) {
		PlatformBankAccount platformBankAccount = this.validateExist(id);
		if (CommonDef.Symbol.YES.match(state)) {
			if (CollectionUtils.isNotEmpty(platformBankAccount.getUseType())) {
				List<PlatformBankAccount> platformBankAccountList = platformBankAccountService
						.findByStateAndUseTypes(CommonDef.Symbol.YES.getCode(),
								platformBankAccount.getUseType());
				// 排除相同的id
				platformBankAccountList
						.removeIf(p -> Objects.equals(p.getId(), id));
				if (CollectionUtils.isNotEmpty(platformBankAccountList)) {
					throw new BadRequestException(ErrorCode.CODE_30105009);
				}
			}
		}
		platformBankAccount.setState(state);
		return platformBankAccount;
	}
}
