package com.zhihaoscm.service.resource.form.oil.order;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.OilOrder;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(name = "OilOrderBookingForm", title = "船主油品订单预约加油表单")
@Data
public class OilOrderBookingForm {

	@Schema(title = "订单方式:1.自行下单/2.平台代下")
	@NotNull(message = ErrorCode.CODE_30156012)
	@Range(min = 1, max = 2, message = ErrorCode.CODE_30156013)
	private Integer orderType;

	@Schema(title = "油品站点id")
	@NotNull(message = ErrorCode.CODE_30156014)
	private Long oilSiteId;

	@Schema(title = "船主信息联系人")
	@NotBlank(message = ErrorCode.CODE_30156015)
	private String captainName;

	@Schema(title = "船主信息手机号")
	@NotBlank(message = ErrorCode.CODE_30156016)
	private String captainMobile;

	@Schema(title = "船舶的MMSI")
	private String shipId;

	@Schema(title = "油品类型:1.0号柴油/2.轻质燃油")
	@Range(min = 1, max = 2, message = ErrorCode.CODE_30156019)
	private Integer oilType;

	@Schema(title = "预估加油量")
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30156027)
	@DecimalMax(value = "9999.99", message = ErrorCode.CODE_30156027)
	private BigDecimal bookingRefuelingVolume;

	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	@Schema(title = "预约加油日期")
	private LocalDateTime bookingRefuelingTime;

	@Schema(title = "预约加油时间段")
	@Range(min = 1, max = 12, message = ErrorCode.CODE_30156023)
	private Integer bookingRefuelingTimePeriod;

	@Schema(title = "油价")
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30156026)
	@DecimalMax(value = "999999.99", message = ErrorCode.CODE_30156026)
	private BigDecimal bookingRefuelingPrice;

	@Schema(title = "备注")
	@Length(max = 200, message = ErrorCode.CODE_30156025)
	private String remark;

	public OilOrder convertToEntity() {
		OilOrder oilOrder = new OilOrder();
		oilOrder.setOrderType(this.getOrderType());
		oilOrder.setOilSiteId(this.getOilSiteId());
		oilOrder.setCaptainName(this.getCaptainName());
		oilOrder.setCaptainMobile(this.getCaptainMobile());
		oilOrder.setShipId(this.getShipId());
		oilOrder.setOilType(this.getOilType());
		oilOrder.setBookingRefuelingVolume(this.getBookingRefuelingVolume());
		oilOrder.setBookingRefuelingTime(this.getBookingRefuelingTime());
		oilOrder.setBookingRefuelingTimePeriod(
				this.getBookingRefuelingTimePeriod());
		oilOrder.setBookingRefuelingPrice(this.getBookingRefuelingPrice());
		oilOrder.setRemark(this.getRemark());
		// 船主从小程序或app预约加油，设置为已同意加油协议
		oilOrder.setIsAgreeProtocol(CommonDef.Symbol.YES.getCode());
		return oilOrder;
	}
}
