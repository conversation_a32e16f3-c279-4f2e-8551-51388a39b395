package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.Port;
import com.zhihaoscm.domain.bean.entity.ShipRoute;
import com.zhihaoscm.domain.bean.entity.ShippingPriceIndex;
import com.zhihaoscm.domain.bean.entity.ShippingPriceIndexVersion;
import com.zhihaoscm.domain.bean.json.ShippingPrice;
import com.zhihaoscm.domain.bean.vo.ShippingPriceIndexDetailVo;
import com.zhihaoscm.domain.bean.vo.ShippingPriceIndexStatisticVo;
import com.zhihaoscm.domain.bean.vo.ShippingPriceIndexVo;
import com.zhihaoscm.domain.meta.biz.DashboardDef;
import com.zhihaoscm.domain.meta.biz.PortDef;
import com.zhihaoscm.domain.meta.biz.ShippingPriceIndexDef;
import com.zhihaoscm.domain.meta.biz.TopDef;
import com.zhihaoscm.domain.utils.DashboardDateUtils;
import com.zhihaoscm.service.core.mapper.ShippingPriceIndexMapper;
import com.zhihaoscm.service.core.service.*;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 运价指数 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Slf4j
@Service
public class ShippingPriceIndexServiceImpl extends
		MpLongIdBaseServiceImpl<ShippingPriceIndex, ShippingPriceIndexMapper>
		implements ShippingPriceIndexService {

	@Autowired
	private ShipRouteService shipRouteService;

	@Autowired
	private ShippingPriceIndexVersionService shippingPriceIndexVersionService;

	@Autowired
	private TopService topService;

	@Autowired
	private PortService portService;

	public ShippingPriceIndexServiceImpl(ShippingPriceIndexMapper repository) {
		super(repository);
	}

	@Override
	public Page<ShippingPriceIndexVo> paging(Integer page, Integer size,
			String sourceProvinceCode, String sourceCityCode,
			String sourceRegionCode, String destinationProvinceCode,
			String destinationCityCode, String destinationRegionCode,
			LocalDate startTime, LocalDate endTime, Integer state,
			String sortKey, String sortOrder, Long versionId) {
		Page<ShippingPriceIndex> paging = repository.paging(
				new Page<>(page, size), sourceProvinceCode, sourceCityCode,
				sourceRegionCode, destinationProvinceCode, destinationCityCode,
				destinationRegionCode, startTime, endTime, state, sortKey,
				sortOrder, CommonDef.Symbol.NO.getCode(), versionId);

		List<ShippingPriceIndexVo> list = this.packVo(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, list);
	}

	@Override
	public Page<ShippingPriceIndexVo> customPaging(Integer page, Integer size,
			String shipRouteName, Long customerId, LocalDate date) {
		List<Long> shipRouteIdList = this.findRoutesByPortName(shipRouteName);
		Page<ShippingPriceIndexVo> paging = repository.customPaging(
				new Page<>(page, size), shipRouteName, customerId,
				CommonDef.Symbol.YES.getCode(), date,
				CommonDef.Symbol.NO.getCode(), shipRouteIdList);
		List<ShippingPriceIndexVo> records = paging.getRecords();
		this.handelUpDown(records);
		return paging;
	}

	@Override
	public Page<ShippingPriceIndexVo> hotPaging(Integer page, Integer size,
			LocalDate date) {
		Page<ShippingPriceIndexVo> paging = repository.hotPaging(
				new Page<>(page, size), date, CommonDef.Symbol.YES.getCode(),
				CommonDef.Symbol.NO.getCode());
		List<ShippingPriceIndexVo> records = paging.getRecords();
		this.handelUpDown(records);
		return paging;
	}

	@Override
	public Optional<ShippingPriceIndexVo> findVoById(Long id) {
		Optional<ShippingPriceIndex> optionalShippingPriceIndex = super.findOne(
				id);
		if (optionalShippingPriceIndex.isPresent()) {
			ShippingPriceIndex shippingPriceIndex = optionalShippingPriceIndex
					.get();
			ShippingPriceIndexVo shippingPriceIndexVo = new ShippingPriceIndexVo();
			shippingPriceIndexVo
					.setShippingPriceIndex(optionalShippingPriceIndex.get());
			shippingPriceIndexVo.setShipRoute(shipRouteService
					.findOne(shippingPriceIndex.getShipRouteId()).orElse(null));
			if (Objects.nonNull(shippingPriceIndex.getVersionId())) {
				shippingPriceIndexVersionService
						.findOne(shippingPriceIndex.getVersionId()).ifPresent(
								shippingPriceIndexVo::setShippingPriceIndexVersion);
			}
			return Optional.of(shippingPriceIndexVo);
		}
		return Optional.empty();
	}

	@Override
	public List<ShippingPriceIndexVo> findAllLatestShipRoute(Long customerId) {
		return repository.findAllLatestShipRoute(null, customerId,
				CommonDef.Symbol.YES.getCode(), null,
				CommonDef.Symbol.NO.getCode());
	}

	@Override
	public List<ShippingPriceIndex> findByVersionId(Long versionId) {
		LambdaQueryWrapper<ShippingPriceIndex> queryWrapper = Wrappers
				.lambdaQuery(ShippingPriceIndex.class);
		queryWrapper.eq(ShippingPriceIndex::getVersionId, versionId);
		queryWrapper.eq(ShippingPriceIndex::getDel,
				CommonDef.Symbol.NO.getCode());
		return repository.selectList(queryWrapper);

	}

	@Override
	public List<ShippingPriceIndex> findByVersionIds(List<Long> versionIds) {
		if (CollectionUtils.isEmpty(versionIds)) {
			return List.of();
		}
		LambdaQueryWrapper<ShippingPriceIndex> queryWrapper = Wrappers
				.lambdaQuery(ShippingPriceIndex.class);
		queryWrapper.in(ShippingPriceIndex::getVersionId, versionIds);
		queryWrapper.eq(ShippingPriceIndex::getDel,
				CommonDef.Symbol.NO.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<ShippingPriceIndex> findNewestDataByShipRouteId(
			Long shipRouteId) {
		LambdaQueryWrapper<ShippingPriceIndex> queryWrapper = Wrappers
				.lambdaQuery(ShippingPriceIndex.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(ShippingPriceIndex::getState,
				CommonDef.Symbol.YES.getCode());
		queryWrapper.eq(ShippingPriceIndex::getShipRouteId, shipRouteId);
		queryWrapper.orderByDesc(ShippingPriceIndex::getDate);
		queryWrapper.last("LIMIT 1");
		return Optional
				.ofNullable(repository.selectOne(queryWrapper, Boolean.FALSE));
	}

	@Override
	public Optional<ShippingPriceIndex> findNewestDataByShipRouteName(
			String shipRouteSource, String shipRouteDestination,
			LocalDate date) {
		LambdaQueryWrapper<ShippingPriceIndex> queryWrapper = Wrappers
				.lambdaQuery(ShippingPriceIndex.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(ShippingPriceIndex::getState,
				CommonDef.Symbol.YES.getCode());
		queryWrapper.eq(ShippingPriceIndex::getShipRouteSource,
				shipRouteSource);
		queryWrapper.eq(ShippingPriceIndex::getShipRouteDestination,
				shipRouteDestination);
		queryWrapper.eq(Objects.nonNull(date), ShippingPriceIndex::getDate,
				date);
		queryWrapper.orderByDesc(ShippingPriceIndex::getDate);
		queryWrapper.last("LIMIT 1");
		return Optional
				.ofNullable(repository.selectOne(queryWrapper, Boolean.FALSE));
	}

	@Override
	public Optional<ShippingPriceIndex> findByDateAndShipRouteId(LocalDate date,
			Long shipRouteId) {
		LambdaQueryWrapper<ShippingPriceIndex> wrapper = Wrappers
				.lambdaQuery(ShippingPriceIndex.class);
		this.filterDeleted(wrapper);
		wrapper.eq(ShippingPriceIndex::getDate, date);
		wrapper.eq(ShippingPriceIndex::getShipRouteId, shipRouteId);
		ShippingPriceIndex shippingPriceIndex = repository.selectOne(wrapper,
				Boolean.FALSE);
		return Optional.ofNullable(shippingPriceIndex);
	}

	@Override
	public List<ShippingPriceIndex> findByDateAndShipRouteId(
			LocalDate beginTime, LocalDate endTime, Long shipRouteId) {
		LambdaQueryWrapper<ShippingPriceIndex> wrapper = Wrappers
				.lambdaQuery(ShippingPriceIndex.class);
		this.filterDeleted(wrapper);
		wrapper.ge(ShippingPriceIndex::getDate, beginTime);
		wrapper.le(ShippingPriceIndex::getDate, endTime);
		wrapper.eq(ShippingPriceIndex::getShipRouteId, shipRouteId);
		return repository.selectList(wrapper);
	}

	@Override
	public Map<Integer, List<ShippingPriceIndexStatisticVo>> line(
			Integer shipRouteId, Integer queryType, LocalDate endDate) {
		LocalDate startDate = null;
		endDate = LocalDate.now();
		LambdaQueryWrapper<ShippingPriceIndex> queryWrapper = Wrappers
				.lambdaQuery(ShippingPriceIndex.class);
		queryWrapper.eq(ShippingPriceIndex::getShipRouteId, shipRouteId);
		queryWrapper.eq(ShippingPriceIndex::getState,
				CommonDef.Symbol.YES.getCode());
		queryWrapper.eq(ShippingPriceIndex::getDel,
				CommonDef.Symbol.NO.getCode());
		switch (ShippingPriceIndexDef.QueryType.from(queryType)) {
			case WEEK -> startDate = endDate.minusDays(7);
			case MONTH -> startDate = endDate.minusDays(1 + Math.toIntExact(
					Duration.between(Objects.requireNonNull(DashboardDateUtils
							.calcBeginDate(DashboardDef.Scope.MONTH.getCode())),
							LocalDateTime.now()).toDays()));
		}
		queryWrapper.between(ShippingPriceIndex::getDate, startDate, endDate);
		queryWrapper.orderByAsc(ShippingPriceIndex::getDate);
		List<ShippingPriceIndex> shippingPriceIndices = repository
				.selectList(queryWrapper);
		return this.packShippingPriceIndexStatisticVo(shippingPriceIndices,
				endDate, queryType);
	}

	@Override
	public List<ShippingPriceIndex> findBySourceAndDes(String source,
			String destination, LocalDate date) {
		LambdaQueryWrapper<ShippingPriceIndex> queryWrapper = Wrappers
				.lambdaQuery(ShippingPriceIndex.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(ShippingPriceIndex::getState,
				CommonDef.Symbol.YES.getCode());
		if (date != null) {
			queryWrapper.eq(ShippingPriceIndex::getDate, date);
		}
		queryWrapper.eq(ShippingPriceIndex::getShipRouteSource, source);
		queryWrapper.eq(ShippingPriceIndex::getShipRouteDestination,
				destination);
		queryWrapper.orderByDesc(ShippingPriceIndex::getDate);
		queryWrapper.last("LIMIT 1");
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<ShippingPriceIndex> v2FindBySourceAndDes(String source,
			String destination, LocalDate date) {
		List<ShippingPriceIndex> shippingPriceIndices = this
				.findBySourceAndDes(source, destination, date);
		if (CollectionUtils.isNotEmpty(shippingPriceIndices)) {
			return shippingPriceIndices;
		}
		// 查询始发地码头信息
		Optional<Port> optionalSourcePort = portService.findOneByName(source,
				PortDef.Type.PORT.getCode());

		// 查询目的地码头信息
		Optional<Port> optionalDestinationPort = portService
				.findOneByName(destination, PortDef.Type.PORT.getCode());
		if (optionalSourcePort.isEmpty() || optionalDestinationPort.isEmpty()) {
			return List.of();
		}
		Port sourcePort = optionalSourcePort.get();
		Port destinationPort = optionalDestinationPort.get();

		// 查询航线信息
		List<ShipRoute> shipRoutes = shipRouteService
				.findByStartPortIdAndEndPortId(sourcePort.getId(),
						destinationPort.getId());
		if (shipRoutes.isEmpty()) {
			return List.of();
		}
		List<Long> shipRouteIds = shipRoutes.stream().map(ShipRoute::getId)
				.toList();

		// 查询运价信息
		return repository.v2FindBySourceAndDes(date, shipRouteIds);
	}

	@Override
	public Optional<ShippingPriceIndex> findLatest(Long shipRouteId) {
		LambdaQueryWrapper<ShippingPriceIndex> queryWrapper = Wrappers
				.lambdaQuery(ShippingPriceIndex.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(ShippingPriceIndex::getState,
				CommonDef.Symbol.YES.getCode());
		if (Objects.nonNull(shipRouteId)) {
			queryWrapper.eq(ShippingPriceIndex::getShipRouteId, shipRouteId);
		}
		queryWrapper.orderByDesc(ShippingPriceIndex::getDate);
		queryWrapper.last("LIMIT 1");
		return Optional
				.ofNullable(repository.selectOne(queryWrapper, Boolean.FALSE));
	}

	@Override
	public Optional<ShippingPriceIndexDetailVo> detail(Long shipRouteId,
			Long customerId) {
		Optional<ShippingPriceIndex> latest = this.findLatest(shipRouteId);
		if (latest.isEmpty()) {
			return Optional.empty();
		}
		ShippingPriceIndex shippingPriceIndex = latest.get();
		ShippingPriceIndexDetailVo shippingPriceIndexDetailVo = this
				.packDetailVo(shippingPriceIndex, customerId);

		if (Objects.nonNull(shippingPriceIndexDetailVo)) {
			List<ShippingPriceIndexVo> shippingPriceIndexVoList = this
					.findAllLatestShipRoute(customerId);

			Map<Long, ShippingPriceIndex> shippingPriceIndexMap = shippingPriceIndexVoList
					.stream().map(ShippingPriceIndexVo::getShippingPriceIndex)
					.collect(
							Collectors.toMap(ShippingPriceIndex::getShipRouteId,
									Function.identity()));

			List<Long> shipRouteIdList = shippingPriceIndexVoList.stream()
					.map(ShippingPriceIndexVo::getShippingPriceIndex)
					.map(ShippingPriceIndex::getShipRouteId).toList();
			if (CollectionUtils.isNotEmpty(shipRouteIdList)) {

				int currentIndex = shipRouteIdList.indexOf(shipRouteId);
				if (currentIndex != -1) {
					if (currentIndex > 0) {
						Long pre = shipRouteIdList.get(currentIndex - 1);
						shippingPriceIndexDetailVo.setPreviousShipRouteId(pre);
						shippingPriceIndexDetailVo.setPreviousDate(
								shippingPriceIndexMap.get(pre).getDate());
					}
					if (currentIndex < shipRouteIdList.size() - 1) {

						Long next = shipRouteIdList.get(currentIndex + 1);
						shippingPriceIndexDetailVo.setNextShipRouteId(next);
						shippingPriceIndexDetailVo.setNextDate(
								shippingPriceIndexMap.get(next).getDate());
					}
				}
			}
		}
		return Optional.ofNullable(shippingPriceIndexDetailVo);
	}

	@Override
	public void top(Long id) {
		super.findOne(id).ifPresent(shippingPriceIndex -> {
			switch (CommonDef.Symbol.from(shippingPriceIndex.getTop())) {
				case YES -> {
					shippingPriceIndex.setTop(CommonDef.Symbol.NO.getCode());
					shippingPriceIndex.setTopTime(null);
				}

				case NO -> {
					shippingPriceIndex.setTop(CommonDef.Symbol.YES.getCode());
					shippingPriceIndex.setTopTime(LocalDateTime.now());
				}

			}
			super.updateAllProperties(shippingPriceIndex);
		});
	}

	/**
	 * 根据码头名称得到 始发地或目的地和该码头二级或三级地址相同的航线id
	 *
	 * @param portName
	 * @return
	 */
	private List<Long> findRoutesByPortName(String portName) {
		// 如果 name 为 null 或仅为空格，返回空集合
		if (portName == null || portName.trim().isEmpty()) {
			return List.of();
		}
		// 根据码头名模糊找到port码头list
		List<Port> portList = portService.findByName(portName, null);
		// 找到portList中每个port所属的二级citCode和三级regionCode区域代码
		if (portList == null || portList.isEmpty()) {
			// 返回空集合
			return List.of();
		}
		// 使用 Set 对 (cityCode, regionCode) 进行去重
		Set<List<String>> uniqueCityRegionPairs = new HashSet<>();
		for (Port port : portList) {
			if (PortDef.Type.PORT.match(port.getType())) {
				uniqueCityRegionPairs
						.add(List.of(port.getCityCode(), port.getRegionCode()));
			} else {
				uniqueCityRegionPairs.add(List.of(port.getCityCode()));
			}
		}
		List<ShipRoute> shipRouteList = uniqueCityRegionPairs.stream()
				.flatMap(pair -> {
					String cityCode = pair.get(0);
					String regionCode = pair.size() > 1 ? pair.get(1) : null;
					// 无论航线是否被删除
					List<ShipRoute> routes = shipRouteService
							.findByCityCodeAndRegionCode(cityCode, regionCode);

					// 判空处理
					if (routes == null || routes.isEmpty()) {
						// 返回空流
						return Stream.empty();
					}
					return routes.stream();
				}).toList();
		// 判空处理
		if (shipRouteList.isEmpty()) {
			// 返回空列表
			return List.of();
		}
		return shipRouteList.stream().map(ShipRoute::getId)
				.collect(Collectors.toList());
	}

	/**
	 * 组装详情vo
	 *
	 * @param shippingPriceIndex
	 * @return
	 */
	private ShippingPriceIndexDetailVo packDetailVo(
			ShippingPriceIndex shippingPriceIndex, Long customerId) {
		ShippingPriceIndexDetailVo vo = new ShippingPriceIndexDetailVo();
		if (Objects.isNull(shippingPriceIndex)) {
			return null;
		}
		ShippingPrice todayPrices = shippingPriceIndex.getPrices();
		if (Objects.isNull(todayPrices)) {
			return null;
		}
		vo.setShippingPriceIndex(shippingPriceIndex);
		vo.setIsTop(false);
		if (Objects.nonNull(customerId)) {
			topService
					.findByCustomerIdAndTypeAndBusinessId(customerId,
							TopDef.Type.SHIPPING_PRICE_INDEX.getCode(),
							String.valueOf(shippingPriceIndex.getShipRouteId()))
					.ifPresent(top -> vo.setIsTop(true));
		}
		if (Objects.nonNull(shippingPriceIndex.getShipRouteId())) {
			shipRouteService.findOne(shippingPriceIndex.getShipRouteId())
					.ifPresent(vo::setShipRoute);
		}
		handelDayQoq(shippingPriceIndex, vo);
		handelWeekQoq(shippingPriceIndex, vo);
		handelUpDown(shippingPriceIndex, vo);

		return vo;
	}

	/**
	 * 处理涨跌
	 *
	 * @param records
	 */
	private void handelUpDown(List<ShippingPriceIndexVo> records) {
		for (ShippingPriceIndexVo shippingPriceIndexVo : records) {
			ShippingPrice todayPrices = shippingPriceIndexVo
					.getShippingPriceIndex().getPrices();
			// 处理日环比
			ShippingPriceIndex yesterday = this.findByDateAndShipRouteId(
					shippingPriceIndexVo.getShippingPriceIndex().getDate()
							.minusDays(1),
					shippingPriceIndexVo.getShippingPriceIndex()
							.getShipRouteId())
					.orElse(null);
			if (Objects.nonNull(yesterday)) {
				// 处理涨跌标识
				this.handlePriceComparison(todayPrices, yesterday.getPrices(),
						shippingPriceIndexVo);
				// 处理涨跌额
				this.handelChange(todayPrices, yesterday.getPrices(),
						shippingPriceIndexVo);
			}
		}
	}

	/**
	 * 处理价格变化
	 *
	 * @param todayPrices
	 * @param yesterdayPrices
	 * @param shippingPriceIndexVo
	 */
	private void handelChange(ShippingPrice todayPrices,
			ShippingPrice yesterdayPrices,
			ShippingPriceIndexVo shippingPriceIndexVo) {
		BigDecimal sixToNineThousandPrice = todayPrices
				.getSixToNineThousandPrice();
		BigDecimal yesterdaySixToNineThousandPrice = yesterdayPrices
				.getSixToNineThousandPrice();

		// 6-9千吨 日涨跌 日环比
		if (Objects.nonNull(sixToNineThousandPrice)
				&& Objects.nonNull(yesterdaySixToNineThousandPrice)) {
			shippingPriceIndexVo.setSixToNineThousandPriceChangeAmount(
					sixToNineThousandPrice
							.subtract(yesterdaySixToNineThousandPrice));
			shippingPriceIndexVo.setSixToNineThousandPriceChangePercent(
					sixToNineThousandPrice
							.subtract(yesterdaySixToNineThousandPrice)
							.divide(yesterdaySixToNineThousandPrice, 4,
									RoundingMode.HALF_UP)
							.multiply(new BigDecimal(100)));
		}

		// 1万吨 日涨跌 日环比
		BigDecimal tenThousandPrice = todayPrices.getTenThousandPrice();
		BigDecimal yesterdayTenThousandPrice = yesterdayPrices
				.getTenThousandPrice();

		if (Objects.nonNull(tenThousandPrice)
				&& Objects.nonNull(yesterdayTenThousandPrice)) {
			shippingPriceIndexVo.setTenThousandPriceChangeAmount(
					tenThousandPrice.subtract(yesterdayTenThousandPrice));
			shippingPriceIndexVo.setTenThousandPriceChangePercent(
					tenThousandPrice.subtract(yesterdayTenThousandPrice)
							.divide(yesterdayTenThousandPrice, 4,
									RoundingMode.HALF_UP)
							.multiply(new BigDecimal(100)));
		}

		// 1.5+万吨 日涨跌 日环比
		BigDecimal fifteenUpThousandPrice = todayPrices
				.getFifteenUpThousandPrice();
		BigDecimal yesterdayFifteenUpThousandPrice = yesterdayPrices
				.getFifteenUpThousandPrice();

		if (Objects.nonNull(fifteenUpThousandPrice)
				&& Objects.nonNull(yesterdayFifteenUpThousandPrice)) {
			shippingPriceIndexVo.setFifteenUpThousandPriceChangeAmount(
					fifteenUpThousandPrice
							.subtract(yesterdayFifteenUpThousandPrice));
			shippingPriceIndexVo.setFifteenUpThousandPriceChangePercent(
					fifteenUpThousandPrice
							.subtract(yesterdayFifteenUpThousandPrice)
							.divide(yesterdayFifteenUpThousandPrice, 4,
									RoundingMode.HALF_UP)
							.multiply(new BigDecimal(100)));
		}

	}

	/**
	 * 处理涨跌
	 *
	 * @param shippingPriceIndex
	 */
	private void handelUpDown(ShippingPriceIndex shippingPriceIndex,
			ShippingPriceIndexDetailVo vo) {
		ShippingPrice todayPrices = shippingPriceIndex.getPrices();
		// 处理日环比
		ShippingPriceIndex yesterday = this.findByDateAndShipRouteId(
				shippingPriceIndex.getDate().minusDays(1),
				shippingPriceIndex.getShipRouteId()).orElse(null);
		if (Objects.nonNull(yesterday)) {
			handlePriceComparison(todayPrices, yesterday.getPrices(), vo);
		}
	}

	/**
	 * 用于处理价格比较和设置标志的 Helper 方法。
	 *
	 * @param todayPrices
	 *            今天价格
	 * @param yesterdayPrices
	 *            昨天的价格
	 * @param priceIndexVo
	 *
	 */
	private void handlePriceComparison(ShippingPrice todayPrices,
			ShippingPrice yesterdayPrices, Object priceIndexVo) {
		if (Objects.nonNull(todayPrices) && Objects.nonNull(yesterdayPrices)) {
			compareAndSetFlag(todayPrices.getSixToNineThousandPrice(),
					yesterdayPrices.getSixToNineThousandPrice(), priceIndexVo,
					"SixToNineThousandPriceUpDownFlag");
			compareAndSetFlag(todayPrices.getTenThousandPrice(),
					yesterdayPrices.getTenThousandPrice(), priceIndexVo,
					"TenThousandPriceUpDownFlag");
			compareAndSetFlag(todayPrices.getFifteenUpThousandPrice(),
					yesterdayPrices.getFifteenUpThousandPrice(), priceIndexVo,
					"FifteenUpThousandPriceUpDownFlag");
		}
	}

	/**
	 * 比较两个价格并设置相应的 up/down 标志。
	 *
	 * @param todayPrice
	 *            今天的价格。
	 * @param yesterdayPrice
	 *            昨天的价格。
	 * @param priceIndexVo
	 * @param flagField
	 */
	private void compareAndSetFlag(BigDecimal todayPrice,
			BigDecimal yesterdayPrice, Object priceIndexVo, String flagField) {
		if (Objects.nonNull(todayPrice) && Objects.nonNull(yesterdayPrice)) {
			int comparison = todayPrice.compareTo(yesterdayPrice);
			if (priceIndexVo instanceof ShippingPriceIndexVo) {
				setFlag((ShippingPriceIndexVo) priceIndexVo, flagField,
						comparison);
			} else if (priceIndexVo instanceof ShippingPriceIndexDetailVo) {
				setFlag((ShippingPriceIndexDetailVo) priceIndexVo, flagField,
						comparison);
			}
		}
	}

	/**
	 * 在 ShippingPriceIndexVo 中设置 flag 字段。
	 *
	 * @param vo
	 * @param flagField
	 * @param value
	 */
	private void setFlag(ShippingPriceIndexVo vo, String flagField, int value) {
		switch (flagField) {
			case "SixToNineThousandPriceUpDownFlag" ->
				vo.setSixToNineThousandPriceUpDownFlag(value);
			case "TenThousandPriceUpDownFlag" ->
				vo.setTenThousandPriceUpDownFlag(value);
			case "FifteenUpThousandPriceUpDownFlag" ->
				vo.setFifteenUpThousandPriceUpDownFlag(value);

		}
	}

	/**
	 * 在 ShippingPriceIndexDetailVo 中设置 flag 字段。
	 *
	 * @param vo
	 * @param flagField
	 * @param value
	 *
	 */
	private void setFlag(ShippingPriceIndexDetailVo vo, String flagField,
			int value) {
		switch (flagField) {
			case "SixToNineThousandPriceUpDownFlag" ->
				vo.setSixToNineThousandPriceUpDownFlag(value);
			case "TenThousandPriceUpDownFlag" ->
				vo.setTenThousandPriceUpDownFlag(value);
			case "FifteenUpThousandPriceUpDownFlag" ->
				vo.setFifteenUpThousandPriceUpDownFlag(value);
		}
	}

	/**
	 * 处理周环比
	 *
	 * @param shippingPriceIndex
	 * @param vo
	 */
	private void handelWeekQoq(ShippingPriceIndex shippingPriceIndex,
			ShippingPriceIndexDetailVo vo) {
		// 周环比计算
		LocalDate startDate = shippingPriceIndex.getDate().minusDays(6);
		LocalDate endDate = shippingPriceIndex.getDate();

		// 本周数据
		List<ShippingPriceIndex> thisWeekShippingPriceIndex = this
				.findByDateAndShipRouteId(startDate, endDate,
						shippingPriceIndex.getShipRouteId());

		// 上一周的开始和结束时间
		LocalDate lastWeekStartDate = startDate.minusDays(7);
		LocalDate lastWeekEndDate = lastWeekStartDate.plusDays(6);

		// 上一周数据
		List<ShippingPriceIndex> lastWeekShippingPriceIndex = this
				.findByDateAndShipRouteId(lastWeekStartDate, lastWeekEndDate,
						shippingPriceIndex.getShipRouteId());

		if (CollectionUtils.isNotEmpty(thisWeekShippingPriceIndex)
				&& CollectionUtils.isNotEmpty(lastWeekShippingPriceIndex)) {
			// 计算本周和上一周的数据
			BigDecimal thisWeekSixToNineThousandPrice = calculateTotalPrice(
					thisWeekShippingPriceIndex,
					ShippingPrice::getSixToNineThousandPrice);
			BigDecimal thisWeekTenThousandPrice = calculateTotalPrice(
					thisWeekShippingPriceIndex,
					ShippingPrice::getTenThousandPrice);
			BigDecimal thisWeekFifteenUpThousandPrice = calculateTotalPrice(
					thisWeekShippingPriceIndex,
					ShippingPrice::getFifteenUpThousandPrice);

			BigDecimal lastWeekSixToNineThousandPrice = calculateTotalPrice(
					lastWeekShippingPriceIndex,
					ShippingPrice::getSixToNineThousandPrice);
			BigDecimal lastWeekTenThousandPrice = calculateTotalPrice(
					lastWeekShippingPriceIndex,
					ShippingPrice::getTenThousandPrice);
			BigDecimal lastWeekFifteenUpThousandPrice = calculateTotalPrice(
					lastWeekShippingPriceIndex,
					ShippingPrice::getFifteenUpThousandPrice);

			// 处理周环比
			calculateQoq(thisWeekSixToNineThousandPrice,
					lastWeekSixToNineThousandPrice,
					vo::setSixToNineThousandPriceWeekQoq);
			calculateQoq(thisWeekTenThousandPrice, lastWeekTenThousandPrice,
					vo::setTenThousandPriceWeekQoq);
			calculateQoq(thisWeekFifteenUpThousandPrice,
					lastWeekFifteenUpThousandPrice,
					vo::setFifteenUpThousandPriceWeekQoq);
		}
	}

	/**
	 * 结算总运价
	 *
	 * @param shippingPriceIndices
	 * @param priceExtractor
	 * @return
	 */
	private BigDecimal calculateTotalPrice(
			List<ShippingPriceIndex> shippingPriceIndices,
			Function<ShippingPrice, BigDecimal> priceExtractor) {
		return shippingPriceIndices.stream().map(ShippingPriceIndex::getPrices)
				.map(priceExtractor).filter(Objects::nonNull)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
	}

	/**
	 * 计算周环比
	 *
	 * @param thisWeekPrice
	 * @param lastWeekPrice
	 * @param setter
	 */
	private void calculateQoq(BigDecimal thisWeekPrice,
			BigDecimal lastWeekPrice, Consumer<BigDecimal> setter) {
		if (lastWeekPrice.compareTo(BigDecimal.ZERO) != 0
				&& thisWeekPrice.compareTo(BigDecimal.ZERO) != 0) {
			BigDecimal priceQoq = thisWeekPrice.subtract(lastWeekPrice)
					.divide(lastWeekPrice, 4, RoundingMode.HALF_UP)
					.multiply(new BigDecimal(100));
			setter.accept(priceQoq);
		}
	}

	/**
	 * 处理日环比
	 *
	 * @param shippingPriceIndex
	 * @param vo
	 */
	private void handelDayQoq(ShippingPriceIndex shippingPriceIndex,
			ShippingPriceIndexDetailVo vo) {
		// 处理日环比
		ShippingPriceIndex yesterday = this.findByDateAndShipRouteId(
				shippingPriceIndex.getDate().minusDays(1),
				shippingPriceIndex.getShipRouteId()).orElse(null);
		ShippingPrice todayPrices = shippingPriceIndex.getPrices();
		if (Objects.nonNull(yesterday)) {
			ShippingPrice prices = yesterday.getPrices();
			if (Objects.nonNull(prices)) {
				// 最新日期
				BigDecimal todaySixToNineThousandPrice = todayPrices
						.getSixToNineThousandPrice();
				BigDecimal todayTenThousandPrice = todayPrices
						.getTenThousandPrice();
				BigDecimal todayFifteenUpThousandPrice = todayPrices
						.getFifteenUpThousandPrice();

				// 最新日期前一天的数据
				BigDecimal sixToNineThousandPrice = prices
						.getSixToNineThousandPrice();
				BigDecimal tenThousandPrice = prices.getTenThousandPrice();
				BigDecimal fifteenUpThousandPrice = prices
						.getFifteenUpThousandPrice();

				// 最新和最新前一天的运价都不能为空
				if (Objects.nonNull(todaySixToNineThousandPrice)
						&& Objects.nonNull(sixToNineThousandPrice)) {
					BigDecimal sixToNineThousandPriceChange = todaySixToNineThousandPrice
							.subtract(sixToNineThousandPrice)
							.divide(sixToNineThousandPrice, 4,
									RoundingMode.HALF_UP)
							.multiply(new BigDecimal(100));
					vo.setSixToNineThousandPriceDayQoq(
							sixToNineThousandPriceChange);
				}

				// 最新和最新前一天的运价都不能为空
				if (Objects.nonNull(todayTenThousandPrice)
						&& Objects.nonNull(tenThousandPrice)) {
					BigDecimal tenThousandPriceChange = todayTenThousandPrice
							.subtract(tenThousandPrice)
							.divide(tenThousandPrice, 4, RoundingMode.HALF_UP)
							.multiply(new BigDecimal(100));
					vo.setTenThousandPriceDayQoq(tenThousandPriceChange);
				}

				// 最新和最新前一天的运价都不能为空
				if (Objects.nonNull(todayFifteenUpThousandPrice)
						&& Objects.nonNull(fifteenUpThousandPrice)) {
					BigDecimal fifteenUpThousandPriceChange = todayFifteenUpThousandPrice
							.subtract(fifteenUpThousandPrice)
							.divide(fifteenUpThousandPrice, 4,
									RoundingMode.HALF_UP)
							.multiply(new BigDecimal(100));
					vo.setFifteenUpThousandPriceDayQoq(
							fifteenUpThousandPriceChange);
				}
			}
		}
	}

	/**
	 * 组装近一周数据
	 *
	 * @param shippingPriceIndices
	 * @return
	 */
	private Map<Integer, List<ShippingPriceIndexStatisticVo>> packShippingPriceIndexStatisticVo(
			List<ShippingPriceIndex> shippingPriceIndices, LocalDate latestDate,
			Integer queryType) {

		// 初始化最近一周的综合指数
		Map<LocalDate, ShippingPriceIndex> lineMap = Stream
				.iterate(latestDate, localDate -> localDate.minusDays(1))
				.limit(this.calcScope(queryType))
				.collect(Collectors.toMap(key -> key, key -> {
					ShippingPriceIndex shippingPriceIndex = new ShippingPriceIndex();
					shippingPriceIndex.setState(CommonDef.Symbol.YES.getCode());
					shippingPriceIndex.setDate(key);
					return shippingPriceIndex;
				}));

		shippingPriceIndices.stream().collect(
				Collectors.toMap(ShippingPriceIndex::getDate, index -> index))
				.forEach((key, value) -> {
					if (lineMap.containsKey(key)) {
						lineMap.put(key, value);
					}
				});
		shippingPriceIndices = (lineMap.values().stream()
				.sorted(Comparator.comparing(ShippingPriceIndex::getDate))
				.toList());

		// Map.key 1: 6千吨到9千吨 2:1万吨 3: 1.5万吨以上 Map.value 对应吨位的近一个月数据
		Map<Integer, List<ShippingPriceIndexStatisticVo>> resutlMap = new HashMap<>();
		for (ShippingPriceIndex shippingPriceIndex : shippingPriceIndices) {
			ShippingPrice prices = shippingPriceIndex.getPrices();

			addStatToResultMap(resutlMap, shippingPriceIndex,
					Objects.nonNull(prices) ? prices.getSixToNineThousandPrice()
							: null,
					1);
			addStatToResultMap(resutlMap, shippingPriceIndex,
					Objects.nonNull(prices) ? prices.getTenThousandPrice()
							: null,
					2);
			addStatToResultMap(resutlMap, shippingPriceIndex,
					Objects.nonNull(prices) ? prices.getFifteenUpThousandPrice()
							: null,
					3);

		}
		for (List<ShippingPriceIndexStatisticVo> statVoList : resutlMap
				.values()) {
			if (CollectionUtils.isNotEmpty(statVoList)) {
				statVoList.remove(0);
			}
		}
		return resutlMap;
	}

	private void addStatToResultMap(
			Map<Integer, List<ShippingPriceIndexStatisticVo>> resultMap,
			ShippingPriceIndex shippingPriceIndex, BigDecimal price, int key) {
		ShippingPriceIndexStatisticVo stat = new ShippingPriceIndexStatisticVo();
		stat.setDate(shippingPriceIndex.getDate());
		stat.setPrice(price);
		resultMap.compute(key, (k, v) -> {
			if (v == null) {
				v = new ArrayList<>();
			} else {
				ShippingPriceIndexStatisticVo lastData = v.get(v.size() - 1);
				if (Objects.nonNull(lastData)
						&& Objects.nonNull(lastData.getPrice())
						&& Objects.nonNull(stat.getPrice())) {
					stat.setUpDownFlag(
							stat.getPrice().compareTo(lastData.getPrice()));
				}
			}
			v.add(stat);
			return v;
		});

	}

	/**
	 * 计算月份天数
	 *
	 * @return
	 */
	private Integer calcScope(Integer queryType) {
		return switch (ShippingPriceIndexDef.QueryType.from(queryType)) {
			case WEEK -> 8;
			case MONTH -> 1 + 1 + Math.toIntExact(Duration.between(
					Objects.requireNonNull(DashboardDateUtils
							.calcBeginDate(DashboardDef.Scope.MONTH.getCode())),
					LocalDateTime.now()).toDays());
		};
	}

	/**
	 * 组装vo
	 *
	 * @param records
	 * @return
	 */
	private List<ShippingPriceIndexVo> packVo(
			List<ShippingPriceIndex> records) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		List<Long> shipRouteIds = records.stream()
				.map(ShippingPriceIndex::getShipRouteId).distinct().toList();

		Map<Long, ShipRoute> shipRouteServiceIdMap = shipRouteService
				.getIdMap(shipRouteIds);

		List<Long> versionIdList = records.stream()
				.map(ShippingPriceIndex::getVersionId).toList();
		Map<Long, ShippingPriceIndexVersion> versionMap = shippingPriceIndexVersionService
				.getIdMap(versionIdList);

		return records.stream().map(e -> {
			ShippingPriceIndexVo shippingPriceIndexVo = new ShippingPriceIndexVo();
			shippingPriceIndexVo.setShippingPriceIndex(e);
			shippingPriceIndexVo.setShipRoute(
					shipRouteServiceIdMap.get(e.getShipRouteId()));
			if (Objects.nonNull(e.getVersionId())) {
				shippingPriceIndexVo.setShippingPriceIndexVersion(
						versionMap.get(e.getVersionId()));
			}
			return shippingPriceIndexVo;
		}).toList();
	}
}
