package com.zhihaoscm.service.core.service.impl;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.AdvertisementPosition;
import com.zhihaoscm.domain.bean.json.Advertisement;
import com.zhihaoscm.domain.bean.json.ArrayAdvertisement;
import com.zhihaoscm.domain.meta.biz.AdvertisementPositionDef;
import com.zhihaoscm.service.core.mapper.AdvertisementPositionMapper;
import com.zhihaoscm.service.core.service.AdvertService;
import com.zhihaoscm.service.core.service.AdvertisementPositionService;

/**
 * <p>
 * 广告位 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@Service
public class AdvertisementPositionServiceImpl extends
		MpLongIdBaseServiceImpl<AdvertisementPosition, AdvertisementPositionMapper>
		implements AdvertisementPositionService {

	public AdvertisementPositionServiceImpl(
			AdvertisementPositionMapper repository) {
		super(repository);
	}

	@Autowired
	private AdvertService advertService;

	@Override
	public List<AdvertisementPosition> findAll() {
		List<AdvertisementPosition> all = super.findAll();
		for (AdvertisementPosition advertisementPosition : all) {
			if (CollectionUtils
					.isNotEmpty(advertisementPosition.getContent())) {
				ArrayAdvertisement positionContent = advertisementPosition
						.getContent();
				// 进行排序
				List<Advertisement> sortPosition = positionContent.stream()
						.sorted(Comparator.comparing(Advertisement::getSort,
								Comparator
										.nullsLast(Comparator.reverseOrder())))
						.toList();

				ArrayAdvertisement advertisements = new ArrayAdvertisement();
				advertisements.addAll(sortPosition);

				advertisementPosition.setContent(advertisements);
			}
		}
		return all;
	}

	@Override
	public Optional<AdvertisementPosition> findByPosition(Integer positionType,
			Integer position) {
		LambdaQueryWrapper<AdvertisementPosition> wrapper = Wrappers
				.lambdaQuery(AdvertisementPosition.class)
				.eq(AdvertisementPosition::getDel,
						CommonDef.Symbol.NO.getCode())
				.eq(AdvertisementPosition::getPositionType, positionType)
				.eq(AdvertisementPosition::getPosition, position);
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@FileId(type = 2)
	@Override
	@Transactional(rollbackFor = Exception.class)
	public AdvertisementPosition update(AdvertisementPosition resource) {
		AdvertisementPosition old = this.findOne(resource.getId()).orElse(null);
		AdvertisementPosition advertisementPosition = super.updateAllProperties(
				resource);
		this.resetOldAdvert(old);
		ArrayAdvertisement positionContent = advertisementPosition.getContent();
		if (CollectionUtils.isNotEmpty(positionContent)) {
			for (Advertisement advertisement : positionContent) {
				if (AdvertisementPositionDef.LinkType.ADVERT
						.match(advertisement.getLinkType())) {
					advertService.updateUsed(advertisement.getAdvertId());
				}
			}
		}

		return advertisementPosition;
	}

	@FileId(type = 3)
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(Long id) {
		LambdaUpdateWrapper<AdvertisementPosition> wrapper = new LambdaUpdateWrapper<>();
		wrapper.eq(AdvertisementPosition::getId, id);
		wrapper.eq(AdvertisementPosition::getDel,
				CommonDef.Symbol.NO.getCode());
		wrapper.set(AdvertisementPosition::getDisplayForm, null);
		wrapper.set(AdvertisementPosition::getTimeInterval, null);
		wrapper.set(AdvertisementPosition::getBeginTime, null);
		wrapper.set(AdvertisementPosition::getEndTime, null);
		wrapper.set(AdvertisementPosition::getContent, null);
		AdvertisementPosition old = this.findOne(id).orElse(null);

		this.resetOldAdvert(old);

		repository.update(wrapper);
	}

	@Override
	public Boolean existAdvertId(Long advertId) {
		List<AdvertisementPosition> all = super.findAll();
		if (CollectionUtils.isNotEmpty(all)) {
			List<ArrayAdvertisement> advertisementList = all.stream()
					.map(AdvertisementPosition::getContent).toList();
			if (CollectionUtils.isNotEmpty(advertisementList)) {
				for (ArrayAdvertisement advertisements : advertisementList) {
					if (CollectionUtils.isNotEmpty(advertisements)) {
						for (Advertisement advertisement : advertisements) {
							if (Objects.nonNull(advertisement)) {
								if (AdvertisementPositionDef.LinkType.ADVERT
										.match(advertisement.getLinkType())) {
									if (advertisement.getAdvertId()
											.equals(advertId)) {
										return true;
									}
								}
							}
						}
					}
				}
			}
		}
		return false;
	}

	/**
	 * 重置之前的广告页是否呗使用
	 *
	 * @param old
	 */
	private void resetOldAdvert(AdvertisementPosition old) {
		if (Objects.nonNull(old)) {
			ArrayAdvertisement content = old.getContent();
			if (CollectionUtils.isNotEmpty(content)) {
				for (Advertisement advertisement : content) {
					if (AdvertisementPositionDef.LinkType.ADVERT
							.match(advertisement.getLinkType())) {
						advertService.updateUsed(advertisement.getAdvertId());
					}
				}
			}
		}
	}
}
