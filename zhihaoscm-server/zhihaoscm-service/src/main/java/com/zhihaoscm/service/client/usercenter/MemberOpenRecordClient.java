package com.zhihaoscm.service.client.usercenter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.zhihaoscm.common.util.utils.date.DateFormat;
import com.zhihaoscm.domain.bean.dto.OnlinePayOpenMemberDto;
import com.zhihaoscm.domain.bean.entity.Customer;

@FeignClient(name = "user-center", path = "/member-open-record-rest", url = "${application.config.user-center}")
public interface MemberOpenRecordClient extends MpLongIdBaseClient<Customer> {

	@PostMapping("/online/pay/open/member")
	Boolean onlinePayOpenMember(
			@RequestBody(required = false) OnlinePayOpenMemberDto onlinePayOpenMemberDto);

	@PostMapping("/apple/pay/cancel/member")
	Boolean cancelOpenMember(
			@RequestBody(required = false) OnlinePayOpenMemberDto onlinePayOpenMemberDto);

	@PostMapping("/find/amount/member/level")
	BigDecimal findAmountByMemberLevel(
			@RequestParam(required = false) Integer memberLevel,
			@DateTimeFormat(pattern = DateFormat.YYYY_MM_DD_HH_MM_SS) @RequestParam(value = "beginTime", required = false) LocalDateTime beginTime,
			@DateTimeFormat(pattern = DateFormat.YYYY_MM_DD_HH_MM_SS) @RequestParam(value = "endTime", required = false) LocalDateTime endTime);
}