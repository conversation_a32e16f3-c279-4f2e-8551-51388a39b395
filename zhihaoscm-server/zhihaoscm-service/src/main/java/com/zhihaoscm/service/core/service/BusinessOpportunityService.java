package com.zhihaoscm.service.core.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.BusinessOpportunity;
import com.zhihaoscm.domain.bean.vo.BusinessOpportunityCountVo;
import com.zhihaoscm.domain.bean.vo.BusinessOpportunityVo;

/**
 * <p>
 * 商机 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
public interface BusinessOpportunityService
		extends MpLongIdBaseService<BusinessOpportunity> {

	/**
	 * 分页
	 * 
	 * @param page
	 * @param size
	 * @param keyword
	 * @param type
	 * @param state
	 * @param sortKey
	 * @param sortOrder
	 * @return
	 */
	Page<BusinessOpportunity> paging(Integer page, Integer size, String keyword,
			List<Integer> type, List<Integer> state, String sortKey,
			String sortOrder);

	/**
	 * 用户PC分页
	 * 
	 * @param page
	 * @param size
	 * @param keyword
	 * @param type
	 * @param addressCode
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	com.zhihaoscm.common.bean.page.Page<BusinessOpportunityVo> customPaging(
			Integer page, Integer size, String keyword, List<Integer> type,
			String addressCode, LocalDateTime beginTime, LocalDateTime endTime,
			Long customId);

	/**
	 * 分页查询收藏商机列表
	 */
	com.zhihaoscm.common.bean.page.Page<BusinessOpportunityVo> customPagingCollect(
			Integer page, Integer size, String keyword, Long customId);

	/**
	 * 相关商机列表
	 * 
	 * @param businessOpportunityId
	 * @param customId
	 * @return
	 */
	List<BusinessOpportunityVo> relatedList(Long businessOpportunityId,
			Long customId);

	/**
	 * 查询vo详情
	 */
	Optional<BusinessOpportunityVo> findVoById(Long id, Long customId);

	/**
	 * 审核
	 */
	void audit(BusinessOpportunity businessOpportunity);

	/**
	 * 下架
	 */
	void downShelf(BusinessOpportunity businessOpportunity);

	/**
	 * 收藏、取消收藏商机
	 * 
	 * @param id
	 * @param state
	 *            1 收藏 0 取消收藏
	 * @param customId
	 */
	void collect(Long id, Integer state, Long customId);

	/**
	 * 待办统计-商机管理
	 *
	 * @param hasFull
	 *            审核权限
	 * @return
	 */
	BusinessOpportunityCountVo statisticsBusinessOpportunity(Boolean hasFull);
}
