package com.zhihaoscm.service.core.service;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.entity.OilOrder;
import com.zhihaoscm.domain.bean.entity.OilOrderSnapshot;
import com.zhihaoscm.domain.bean.vo.OilOrderVo;
import com.zhihaoscm.domain.meta.biz.OilOrderDef;

/**
 * <p>
 * 油品订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
public interface OilOrderService extends MpStringIdBaseService<OilOrder> {

	/**
	 * 船务分页查询油品订单
	 * 
	 * @param page
	 * @param size
	 * @param state
	 *            业务状态
	 * @param customId
	 * @return
	 */
	Page<OilOrderVo> customPaging(Integer page, Integer size, Integer state,
			Long customId);

	/**
	 * 根据id查询订单
	 * 
	 * @param id
	 * @param lon
	 *            经度
	 * @param lat
	 *            纬度
	 */
	Optional<OilOrderVo> findVoById(String id, Double lon, Double lat);

	/**
	 * 查询创建时间最新的油品订单
	 */
	Optional<OilOrderVo> findLast(Long customId, List<Integer> states);

	/**
	 * 船主确认油品订单（同意加油协议）
	 * 
	 * @param oilOrder
	 */
	void agree(OilOrder oilOrder);

	/**
	 * 预约加油
	 * 
	 * @param oilOrder
	 */
	OilOrder booking(OilOrder oilOrder);

	/**
	 * 确认加油
	 * 
	 * @param oilOrder
	 */
	void confirmRefueling(OilOrder oilOrder);

	/**
	 * 船务取消订单
	 * 
	 * @param id
	 */
	void cancel(String id);

	/**
	 * 订单转快照
	 * 
	 * @param order
	 * @return
	 */
	OilOrderSnapshot toSnapshot(OilOrder order);

	/**
	 * 发送企微消息
	 * 
	 * @param oilOrder
	 * @param noticeType
	 */
	void sendWxNotice(OilOrder oilOrder, OilOrderDef.NoticeType noticeType);
}
