package com.zhihaoscm.service.resource.custom.owner.shipping.deposit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.ModeDef;
import com.zhihaoscm.domain.bean.dto.OwnerShippingDepositDto;
import com.zhihaoscm.domain.bean.entity.OwnerShippingDeposit;
import com.zhihaoscm.service.core.service.OwnerShippingDepositService;
import com.zhihaoscm.service.resource.form.owner.shipping.deposit.OwnerShippingDepositForm;
import com.zhihaoscm.service.resource.validator.owner.shipping.deposit.OwnerShippingDepositValidator;
import com.zhihaoscm.service.resource.validator.transport.order.ship.TransportOrderShipValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "货主船运定金", description = "货主船运API")
@RestController
@RequestMapping("/owner-shipping-deposit")
public class OwnerShippingDepositResource {

	@Autowired
	private OwnerShippingDepositService ownerShippingDepositService;

	@Autowired
	private OwnerShippingDepositValidator validator;

	@Autowired
	private TransportOrderShipValidator transportOrderShipValidator;

	@Operation(summary = "查询货主船运定金详情")
	@GetMapping("/{id}")
	public ApiResponse<OwnerShippingDeposit> findById(@PathVariable String id) {
		return new ApiResponse<>(validator.validateExist(id));
	}

	@Operation(summary = "支付定金")
	@PostMapping("/pay")
	public ApiResponse<Void> pay(
			@Validated(ModeDef.Create.class) @RequestBody OwnerShippingDepositForm form) {
		// 货主船运单被三方需求中盛关联了的船运单货主端（小程序、app、pc）暂时隐藏操作按钮，只能在后台或供应链端操作
		transportOrderShipValidator
				.validateOwnerOperate(form.getRelationCode());
		OwnerShippingDepositDto depositDto = validator.validatePay(form);
		ownerShippingDepositService.pay(depositDto.getOwnerShippingDeposit(),
				depositDto.getTransportOrderShip());
		return new ApiResponse<>();
	}

	@Operation(summary = "重新支付定金")
	@PutMapping("/reset-pay/{id}")
	public ApiResponse<Void> resetPay(
			@Parameter(description = "定金id") @PathVariable String id,
			@Validated(ModeDef.Update.class) @RequestBody OwnerShippingDepositForm form) {
		// 货主船运单被三方需求中盛关联了的船运单货主端（小程序、app、pc）暂时隐藏操作按钮，只能在后台或供应链端操作
		transportOrderShipValidator
				.validateOwnerOperate(form.getRelationCode());
		OwnerShippingDepositDto depositDto = validator.validateResetPay(id,
				form);
		ownerShippingDepositService.resetPay(
				depositDto.getOwnerShippingDeposit(),
				depositDto.getTransportOrderShip());
		return new ApiResponse<>();
	}

	@Operation(summary = "船主确认定金")
	@PutMapping("/confirm-pay/{id}")
	public ApiResponse<Void> confirmPay(
			@Parameter(description = "定金id") @PathVariable String id) {
		OwnerShippingDepositDto depositDto = validator.validateConfirmPay(id);
		ownerShippingDepositService.confirmPay(
				depositDto.getOwnerShippingDeposit(),
				depositDto.getTransportOrderShip());
		return new ApiResponse<>();
	}
}
