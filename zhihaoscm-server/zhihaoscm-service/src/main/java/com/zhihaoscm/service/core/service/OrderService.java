package com.zhihaoscm.service.core.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.entity.Order;
import com.zhihaoscm.domain.bean.vo.OrderVo;
import com.zhihaoscm.domain.bean.vo.ServiceSpecialVo;

/**
 * <p>
 * 订单信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface OrderService extends MpStringIdBaseService<Order> {

	/**
	 * 分页查询
	 * 
	 * @param page
	 * @param size
	 * @param id
	 * @param productId
	 * @param orderType
	 * @param states
	 * @param keyword
	 * @param sortKey
	 * @param sortOrder
	 * @return
	 */
	Page<Order> paging(Integer page, Integer size, String id, String productId,
			Integer orderType, List<Integer> states, String keyword,
			String sortKey, String sortOrder, LocalDateTime beginTime,
			LocalDateTime endTime);

	/**
	 * 分页查询
	 * 
	 * @param page
	 * @param size
	 * @param orderType
	 * @param states
	 * @param keyword
	 * @param sortKey
	 * @param sortOrder
	 * @param beginTime
	 * @param endTime
	 * @param customerId
	 * @return
	 */
	Page<OrderVo> customPaging(Integer page, Integer size, Integer orderType,
			List<Integer> states, String keyword, String sortKey,
			String sortOrder, LocalDateTime beginTime, LocalDateTime endTime,
			Long customerId);

	/**
	 * 查询专员
	 * 
	 * @param
	 * @return
	 */
	List<ServiceSpecialVo> findServiceSpecials();

	/**
	 * 根据id查询订单信息
	 * 
	 * @param id
	 * @return
	 */
	Optional<OrderVo> findVoById(String id);

	/**
	 * 删除订单
	 * 
	 * @param id
	 * @param userType
	 */
	void delete(String id, Integer userType);

	/**
	 * 指派
	 * 
	 * @param order
	 * @param handlerId
	 */
	void assign(Order order, Long handlerId);

	/**
	 * 取消订单
	 * 
	 * @param order
	 */
	void cancel(Order order);

	/**
	 * 完成订单
	 * 
	 * @param order
	 */
	void finish(Order order);

}
