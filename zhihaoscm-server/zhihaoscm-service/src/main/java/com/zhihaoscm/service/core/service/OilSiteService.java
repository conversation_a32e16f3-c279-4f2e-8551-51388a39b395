package com.zhihaoscm.service.core.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.OilSite;
import com.zhihaoscm.domain.bean.vo.OilSiteProvinceVo;
import com.zhihaoscm.domain.bean.vo.OilSiteVo;

/**
 * <p>
 * 油品站点 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface OilSiteService extends MpLongIdBaseService<OilSite> {

	/**
	 * 分页查询列表
	 */
	Page<OilSite> paging(Integer page, Integer size, String keyword,
			String brand, Integer state, String sortKey, String sortOrder);

	/**
	 * 客户端-查询列表
	 * 
	 * @param keyword
	 *            站点名称
	 * @param provinceCode
	 *            省编码
	 * @param lon
	 *            当前经度
	 * @param lat
	 *            当前纬度
	 * @return
	 */
	List<OilSiteProvinceVo> customPaging(String keyword, String provinceCode,
			Double lon, Double lat);

	/**
	 * 客户端-搜索页-分页查询列表
	 */
	com.zhihaoscm.common.bean.page.Page<OilSiteVo> customPagingSearch(
			Integer page, Integer size, String keyword, String provinceCode,
			String sortKey, String sortOrder, Double lon, Double lat);

	/**
	 * 下拉列表
	 */
	Page<OilSite> pagingSelector(Integer page, Integer size, String name);

	/**
	 * 根据站点名称和站点id精确查询站点列表
	 */
	List<OilSite> findByNameAndNotInId(String name, Long id);

	/**
	 * 根据状态查询站点列表
	 */
	List<OilSite> findByState(Integer state);

	/**
	 * 根据站点名称模糊搜索
	 * 
	 * @param siteName
	 * @return
	 */
	List<OilSite> findByNameLikeWithDeleted(String siteName);

	/**
	 * 查询有站点的一级地址
	 */
	List<String> findSiteProvince();

	/**
	 * 根据id查找单个站点详情数据
	 * 
	 * @param id
	 * @param lon
	 *            经度
	 * @param lat
	 *            纬度
	 */
	Optional<OilSiteVo> findVoById(Long id, Double lon, Double lat);

	/**
	 * 根据关键字查询站点列表
	 * 
	 * @param keyword
	 * @return
	 */
	List<OilSite> findByNameOrId(String keyword);

	/**
	 * 根据品牌模糊查询
	 * 
	 * @param brand
	 * @return
	 */
	List<OilSite> findByBrandLike(String brand);

	/**
	 * 根据省份名称模糊查询
	 * 
	 * @param provinceName
	 * @return
	 */
	List<OilSite> findByProvinceNameLike(String provinceName);

	/**
	 * 更新状态
	 */
	void updateState(Long id, Integer state);

	/**
	 * 计算每个站点距离用户的距离
	 *
	 * @param lon
	 *            经度
	 * @param lat
	 *            纬度
	 */
	Map<Long, BigDecimal> calcDistance(Double lon, Double lat,
			List<OilSite> oilSiteList);

	/**
	 * 组装查询vo
	 * 
	 * @param records
	 * @param lon
	 * @param lat
	 * @return
	 */
	List<OilSiteVo> packListVo(List<OilSite> records, Double lon, Double lat);
}
