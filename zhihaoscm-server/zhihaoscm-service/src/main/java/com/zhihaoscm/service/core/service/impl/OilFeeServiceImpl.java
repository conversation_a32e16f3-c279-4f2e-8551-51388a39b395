package com.zhihaoscm.service.core.service.impl;

import java.time.LocalDateTime;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.OilFee;
import com.zhihaoscm.domain.bean.entity.OilOrder;
import com.zhihaoscm.domain.bean.entity.OilOrderSnapshot;
import com.zhihaoscm.domain.bean.json.CustomerJsonInfo;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.OilFeeCountVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AutoCodeDef;
import com.zhihaoscm.domain.meta.biz.OilFeeDef;
import com.zhihaoscm.domain.meta.biz.OilOrderDef;
import com.zhihaoscm.domain.meta.biz.WxwDef;
import com.zhihaoscm.service.core.mapper.OilFeeMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

/**
 * <p>
 * 油品费用 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Service
public class OilFeeServiceImpl
		extends MpStringIdBaseServiceImpl<OilFee, OilFeeMapper>
		implements OilFeeService {

	@Autowired
	private UserService userService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private ShipService shipService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private OilOrderService oilOrderService;
	@Autowired
	private OilOrderSnapshotService oilOrderSnapshotService;

	public OilFeeServiceImpl(OilFeeMapper repository) {
		super(repository);
	}

	@Override
	public Page<OilFee> paging(Integer page, Integer size, String keyword,
			String customerInfo, String shipName, Integer state, String sortKey,
			String sortOrder) {
		LambdaQueryWrapper<OilFee> queryWrapper = Wrappers
				.lambdaQuery(OilFee.class);
		this.filterDeleted(queryWrapper);
		// 油品费用编号、油品订单编号唯一搜索
		queryWrapper.and(StringUtils.isNotBlank(keyword),
				wrapper -> wrapper.eq(OilFee::getId, keyword).or()
						.eq(OilFee::getOilOrderId, keyword));
		// 个人实名/机构名称/手机号/用户ID模糊搜索
		queryWrapper.apply(StringUtils.isNotBlank(customerInfo),
				"(customer_info -> '$.institutionName' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_info -> '$.realName' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_info -> '$.mobile' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_info -> '$.code' LIKE CONCAT('%',{0},'%'))",
				customerInfo);
		// 船舶中英文模糊搜索
		queryWrapper.and(StringUtils.isNotBlank(shipName),
				wrapper -> wrapper.like(OilFee::getShipName, shipName).or()
						.like(OilFee::getShipCnName, shipName));
		// 状态筛选
		queryWrapper.eq(Objects.nonNull(state), OilFee::getState, state);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 以创建时间倒序
			queryWrapper.orderByDesc(OilFee::getCreatedTime);
		}
		return repository.selectPage(new Page<>(page, size), queryWrapper);
	}

	@FileId
	@Override
	public OilFee create(OilFee resource) {
		return super.create(resource);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void confirm(OilFee oilFee) {
		// 获取当前登录用户id
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (Objects.nonNull(context)) {
			Long userId = context.getUserId();
			oilFee.setConfirmBy(userId);
			userService.findOne(userId).ifPresent(user -> {
				oilFee.setConfirmBy(user.getId());
				oilFee.setConfirmTime(LocalDateTime.now());
				oilFee.setConfirmByName(user.getName());
			});
		}
		// 油品费用状态改为已支付
		oilFee.setState(OilFeeDef.State.SUCCESS.getCode());
		super.updateAllProperties(oilFee);
		// 油品订单的支付状态改为已支付
		oilOrderService.findOne(oilFee.getOilOrderId()).ifPresent(oilOrder -> {
			oilOrder.setPayState(OilOrderDef.PayState.SUCCESS.getCode());
			OilOrder order = oilOrderService.updateAllProperties(oilOrder);
			// 同步修改快照
			OilOrderSnapshot oilOrderSnapshot = oilOrderService
					.toSnapshot(order);
			OilOrderSnapshot snapshot = oilOrderSnapshotService
					.updateAllProperties(oilOrderSnapshot);
			// 此时油费已确认，如果已完成报计划，需要给管理后台的有油品订单"处理"权限的人发送企微消息
			if (OilOrderDef.State.IN_PROGRESS.match(order.getState())
					&& OilOrderDef.ProgressState.AWAITING_CONFIRMATION
							.match(order.getProgressState())) {
				oilOrderService.sendWxNotice(order,
						OilOrderDef.NoticeType.TO_BE_CONFIRM);

				// 还需要给船主发送短信和站内，提醒船主去确认加油
				oilOrderSnapshotService.sendConfirmRefuelingNotice(snapshot);
			}
		});
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void pay(OilFee oilFee) {
		// 设置油费id
		oilFee.setId(AutoCodeDef.CREATE_AUTO_CODE.apply(
				SpringUtil.getBean(StringRedisClient.class),
				AutoCodeDef.BusinessCode.OIL_FEE.getCode(),
				RedisKeys.Cache.OIL_FEE, "01", 5, AutoCodeDef.DATE_TYPE.yyMM));
		// 设置油费支付时间
		oilFee.setPayTime(LocalDateTime.now());
		// 设置油费状态为待确认
		oilFee.setState(OilFeeDef.State.UN_CONFIRM.getCode());
		oilOrderService.findOne(oilFee.getOilOrderId()).ifPresent(oilOrder -> {
			// 油费设置船主信息
			oilFee.setCustomerId(oilOrder.getCaptainId());
			customerService.findOne(oilOrder.getCaptainId())
					.ifPresent(customer -> {
						CustomerJsonInfo customerJsonInfo = new CustomerJsonInfo();
						customerJsonInfo.setId(customer.getId());
						customerJsonInfo.setCode(customer.getCode());
						customerJsonInfo.setMobile(customer.getMobile());
						customerJsonInfo.setRealName(customer.getRealName());
						customerJsonInfo.setInstitutionName(
								customer.getInstitutionName());
						oilFee.setCustomerInfo(customerJsonInfo);
					});
			// 油费设置船舶信息
			oilFee.setShipId(oilOrder.getShipId());
			shipService.findOne(oilOrder.getShipId()).ifPresent(ship -> {
				oilFee.setShipName(ship.getName());
				oilFee.setShipCnName(ship.getCnname());
			});
			// 油品订单的支付状态设置为待确认
			oilOrder.setPayState(OilOrderDef.PayState.UN_CONFIRM.getCode());
			// 油品订单的支付凭证
			oilOrder.setPayFileIds(oilFee.getPayFileIds());
			// 油品订单的付款金额
			oilOrder.setPayAmount(oilFee.getAmount());
			OilOrder order = oilOrderService.updateAllProperties(oilOrder);
			// 同步修改快照
			OilOrderSnapshot oilOrderSnapshot = oilOrderService
					.toSnapshot(order);
			oilOrderSnapshotService.updateAllProperties(oilOrderSnapshot);
		});

		OilFee fee = this.create(oilFee);
		// 发送企微通知
		this.sendNotice(fee);
	}

	@Override
	public OilFeeCountVo statisticsOilFee(Boolean hasFull) {
		OilFeeCountVo vo = new OilFeeCountVo();
		LambdaQueryWrapper<OilFee> queryWrapper = Wrappers
				.lambdaQuery(OilFee.class);
		if (hasFull) {
			// 统计待确认油品费用
			queryWrapper.eq(OilFee::getState,
					OilFeeDef.State.UN_CONFIRM.getCode());
			queryWrapper.eq(OilFee::getDel, CommonDef.Symbol.NO.getCode());
			Long toBeConfirmedCount = repository.selectCount(queryWrapper);
			vo.setToBeConfirmed(toBeConfirmedCount);
			queryWrapper.clear();
		} else {
			vo.setToBeConfirmed(0L);
		}
		return vo;
	}

	/**
	 * 发送企微通知
	 */
	private void sendNotice(OilFee oilFee) {
		messageService.sendNotice(WxwMessage.builder().receiptors(userService
				.findUsersByPermission(AdminPermissionDef.REFUELING_FEE_W, null)
				.stream().map(user -> String.valueOf(user.getId())).toList())
				.url("/expenseManagement/shippingDeal/oilFee/detail/"
						.concat(oilFee.getId()))
				.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
				.operationModule(WxwDef.NoticeOperationModule.OIL_FEE.getDesc())
				.desc("待确认").keyword(oilFee.getId()).content(StringUtils.EMPTY)
				.build());
	}
}
