package com.zhihaoscm.service.core.service.impl;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementAccept;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.json.ArrayCarrierShip;
import com.zhihaoscm.domain.bean.json.CarrierShip;
import com.zhihaoscm.domain.bean.json.CustomerBankInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.Application.ApplicationTransfer;
import com.zhihaoscm.domain.bean.json.notice.Messages;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.ShippingRequirementAcceptVo;
import com.zhihaoscm.domain.bean.vo.ShippingRequirementPlatVo;
import com.zhihaoscm.domain.bean.vo.TransportOrderShipVo;
import com.zhihaoscm.domain.meta.ApplicationDef;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.meta.notice.AppType;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.service.aop.history.HistoryContext;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.properties.TransportProperties;
import com.zhihaoscm.service.config.security.ApplicationInfoContextHolder;
import com.zhihaoscm.service.core.mapper.ShippingRequirementAcceptMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

/**
 * <p>
 * 船主接单平台船运需求记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Service
public class ShippingRequirementAcceptServiceImpl extends
		MpLongIdBaseServiceImpl<ShippingRequirementAccept, ShippingRequirementAcceptMapper>
		implements ShippingRequirementAcceptService {

	@Autowired
	private ShippingRequirementPlatService platService;

	@Autowired
	private ShippingPriceIndexService priceIndexService;

	@Autowired
	private TransportOrderShipService transportOrderShipService;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Autowired
	private CustomerService customService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private TransportProperties transportProperties;
	@Autowired
	private ShippingRequirementPlatService shippingRequirementPlatService;

	@Autowired
	private MqUtil mqUtil;

	public ShippingRequirementAcceptServiceImpl(
			ShippingRequirementAcceptMapper repository) {
		super(repository);
	}

	@Override
	public Page<ShippingRequirementAcceptVo> customPaging(Integer page,
			Integer size, String platId, Long sourcePortId,
			Long destinationPortId, LocalDate loadDateStart,
			LocalDate loadDateEnd, Integer state, Long carrierId) {
		LambdaQueryWrapper<ShippingRequirementAccept> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementAccept.class)
				.eq(StringUtils.isNotBlank(platId),
						ShippingRequirementAccept::getRequirementPlatId, platId)
				.eq(Objects.nonNull(sourcePortId),
						ShippingRequirementAccept::getSourcePortId,
						sourcePortId)
				.eq(Objects.nonNull(destinationPortId),
						ShippingRequirementAccept::getDestinationPortId,
						destinationPortId)
				.eq(Objects.nonNull(carrierId),
						ShippingRequirementAccept::getCaptainId, carrierId)
				.eq(Objects.nonNull(state), ShippingRequirementAccept::getState,
						state)
				.eq(ShippingRequirementAccept::getDel,
						CommonDef.Symbol.NO.getCode())
				.ge(Objects.nonNull(loadDateStart),
						ShippingRequirementAccept::getLoadDate, loadDateStart)
				.le(Objects.nonNull(loadDateEnd),
						ShippingRequirementAccept::getLoadDate, loadDateEnd)
				// 默认按照创建时间降序排列
				.last("ORDER BY FIELD(state, 4,3,1) ASC, created_time DESC");
		Page<ShippingRequirementAccept> paging = repository
				.selectPage(new Page<>(page, size), wrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Optional<ShippingRequirementAcceptVo> findVoById(Long id) {
		return this.findOne(id).map(this::packVo);
	}

	@Override
	public List<ShippingRequirementAccept> findByPlatId(String id) {
		LambdaQueryWrapper<ShippingRequirementAccept> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementAccept.class)
				.eq(ShippingRequirementAccept::getRequirementPlatId, id)
				.eq(ShippingRequirementAccept::getDel,
						CommonDef.Symbol.NO.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public List<ShippingRequirementAccept> findByPlatId(String id,
			Long captainId) {
		LambdaQueryWrapper<ShippingRequirementAccept> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementAccept.class)
				.eq(ShippingRequirementAccept::getRequirementPlatId, id)
				.eq(ShippingRequirementAccept::getDel,
						CommonDef.Symbol.NO.getCode())
				.eq(Objects.nonNull(captainId),
						ShippingRequirementAccept::getCaptainId, captainId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<ShippingRequirementAcceptVo> findVoByPlatId(String id,
			Long captainId) {
		LambdaQueryWrapper<ShippingRequirementAccept> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementAccept.class)
				.eq(ShippingRequirementAccept::getRequirementPlatId, id)
				.eq(ShippingRequirementAccept::getDel,
						CommonDef.Symbol.NO.getCode())
				.eq(Objects.nonNull(captainId),
						ShippingRequirementAccept::getCaptainId, captainId);
		return this.packVo(repository.selectList(wrapper));
	}

	@Override
	public List<ShippingRequirementAccept> findByPlatIds(List<String> platIds) {
		LambdaQueryWrapper<ShippingRequirementAccept> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementAccept.class)
				.in(ShippingRequirementAccept::getRequirementPlatId, platIds)
				.eq(ShippingRequirementAccept::getDel,
						CommonDef.Symbol.NO.getCode());
		return repository.selectList(wrapper);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public ShippingRequirementAccept update(
			ShippingRequirementAccept resource) {
		if (ShippingRequirementAcceptDef.CarrierState.PROCESSING
				.match(resource.getState())) {
			platService.findOne(resource.getRequirementPlatId())
					.ifPresent(plat -> {
						if (ShippingRequirementPlatDef.DataSource.OUTER
								.match(plat.getDataSource())) {
							// 三方需求不发信息
							// 链云抢三方的需要 推送给第三方
							if (!ShippingRequirementPlatDef.DataSource.OUTER
									.match(resource.getDataSource())) {
								// 船主接单信息修改推送给三方
								handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
										.getCode(),
										ShippingCallbackDef.PushType.UPDATE_ACCEPT
												.getCode(),
										String.valueOf(resource.getId()));
							}
						} else {
							messageService.sendNotice(WxwMessage.builder()
									.receiptors(List.of(String
											.valueOf(plat.getHandlerId())))
									.url("/logistics/shippingNeed/platformShipNeedInfo/"
											.concat(String.valueOf(resource
													.getRequirementPlatId())))
									.prefix("你" + plat.getCreatedTime()
											.format(DateTimeFormatter.ofPattern(
													"yyyy-MM-dd HH:mm:ss"))
											+ "发布由" + plat.getSourcePortName()
											+ "到"
											+ plat.getDestinationPortName()
											+ "的船运需求")
									.operationModule(StringUtils.EMPTY)
									.desc("有船舶" + resource.getShipInfos().get(0)
											.getCnname() + "报价，请及时处理")
									.keyword(String.valueOf(
											resource.getRequirementPlatId()))
									.content(StringUtils.EMPTY).build());
						}
					});
		}
		return super.updateAllProperties(resource);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@History(success = HistoryDef.SHIPPING_DEMAND_PUBLISH_CHECK_ORDER, isSaveChange = true, bizNo = "{{#resource.getRequirementPlatId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_PUBLISH)
	public void updateAccept(ShippingRequirementAccept resource) {
		if (ShippingRequirementAcceptDef.PlatformState.SUCCESSFUL_ORDER_ACCEPTANCE
				.match(resource.getState())) {
			// 如果是服务商垫付的，需要校验银行账户开户人和开户名称是否承运商实名
			platService.findOne(resource.getRequirementPlatId())
					.ifPresent(plat -> {
						if (TransportOrderShipDef.DepositPayType.PAY_ZH
								.match(plat.getPayType())) {
							CustomerBankInfo captainBankInfo = resource
									.getCaptainBankInfo();
							if (Objects.nonNull(captainBankInfo)) {
								Long captainId = resource.getCaptainId();
								Customer customer = customService
										.findOne(captainId).orElse(null);
								if (Objects.nonNull(customer)) {
									if (!captainBankInfo.getName()
											.equals(customer.getRealName())) {
										throw new BadRequestException(
												ErrorCode.CODE_30138024);
									}
								}
							}
						}
					});

			// 抢单信息不是第三方平台的，则需要生成船运单
			if (!ShippingRequirementPlatDef.DataSource.OUTER
					.match(resource.getDataSource())) {
				this.buildTransportOrderShip(resource);
			}
			// 驳回其他接单 查询出该船运需求的所有接单信息
			List<ShippingRequirementAccept> shippingRequirementAcceptList = this
					.findByPlatId(resource.getRequirementPlatId());
			// 过滤出当前抢单成功记录
			List<ShippingRequirementAccept> list = shippingRequirementAcceptList
					.stream()
					.filter(item -> !item.getId().equals(resource.getId()))
					.toList();
			this.rejectOtherAccept(resource.getRequirementPlatId(), list);
			// 更新平台船运需求状态为已结束
			platService.findOne(resource.getRequirementPlatId())
					.ifPresent(plat -> {
						plat.setState(ShippingRequirementPlatDef.State.ENDED
								.getCode());
						platService.updateAllProperties(plat);
					});
		} else {
			// 抢单信息不是第三方平台的，修改后推送给三方
			if (!ShippingRequirementPlatDef.DataSource.OUTER
					.match(resource.getDataSource())) {
				// 船主接单信息修改推送给三方
				handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
						.getCode(),
						ShippingCallbackDef.PushType.UPDATE_SHIP_ACCEPT
								.getCode(),
						String.valueOf(resource.getId()));
			}
		}
		super.updateAllProperties(resource);
	}

	@Override
	public void takingOrders(ShippingRequirementAccept shipping) {
		// 新增承运商船运需求
		this.create(shipping);
		if (Objects.nonNull(shipping.getRequirementPlatId())) {
			ShippingRequirementPlat shippingRequirementPlat = shippingRequirementPlatService
					.findOne(shipping.getRequirementPlatId()).orElse(null);
			if (Objects.nonNull(shippingRequirementPlat)) {
				// 船运需求货主是第三方平台的
				if (Objects.equals(shippingRequirementPlat.getOwnerId(),
						transportProperties.getId())) {
					// 接单的船运需求是第三方平台的则设置应用id为船运需求的应用id
					shipping.setSourceAppId(
							shippingRequirementPlat.getSourceAppId());
					// 开始发航后推送给三方
					handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
							.getCode(),
							ShippingCallbackDef.PushType.SHIP_ACCEPT.getCode(),
							String.valueOf(shipping.getId()));
					super.updateAllProperties(shipping);
				}
			}
		}
		// 更新平台船运需求的接单状态
		platService.findOne(shipping.getRequirementPlatId()).ifPresent(plat -> {

			if (ShippingRequirementPlatDef.OrderAcceptanceState.CONFIRMED
					.match(plat.getOrderAcceptanceState())) {
				plat.setOrderAcceptanceState(
						ShippingRequirementPlatDef.OrderAcceptanceState.PENDING_PROCESSING
								.getCode());
				platService.updateAllProperties(plat);
			}
			customService.findOne(plat.getOwnerId()).ifPresent(customer -> {
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.USERMESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.SHIP_DETAIL_GRAP_TEMPLATE,
								plat.getId()))
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.FIND_BOAT_DETAIL_PAGE)
						.detailId(String.valueOf(plat.getId()))
						.initiator(UserMessageDef.BusinessInitiator.initiate
								.getCode())
						.mobile(customer.getMobile()).build());

				// 发送app推送
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(UserMessageConstants.TRANSPORT_TITLE)
						.content(MessageFormat.format(
								UserMessageConstants.SHIP_DETAIL_GRAP_TEMPLATE,
								plat.getId()))
						.appTypes(List.of(AppType.LIANYUN))
						.bizNo(String.valueOf(plat.getId()))
						.moduleType(UserMessageConstants.FIND_BOAT_DETAIL_PAGE)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.build());
			});

			if (ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
					.match(plat.getState())) {
				messageService.sendNotice(WxwMessage.builder()
						.receiptors(
								List.of(String.valueOf(plat.getHandlerId())))
						.url("/logistics/shippingNeed/platformShipNeedInfo/"
								.concat(String.valueOf(
										shipping.getRequirementPlatId())))
						.prefix("你"
								+ plat.getCreatedTime()
										.format(DateTimeFormatter.ofPattern(
												"yyyy-MM-dd HH:mm:ss"))
								+ "发布由" + plat.getSourcePortName() + "到"
								+ plat.getDestinationPortName() + "的船运需求")
						.operationModule(StringUtils.EMPTY)
						.desc("有船舶" + shipping.getShipInfos().get(0).getCnname()
								+ "报价，请及时处理")
						.keyword(
								String.valueOf(shipping.getRequirementPlatId()))
						.content(StringUtils.EMPTY).build());
			}
		});
	}

	@Override
	public Long createAccept(
			ShippingRequirementAccept shippingRequirementAccept) {
		// 如果船运需求已结束，则不会接收此接单信息
		ShippingRequirementPlat plat = platService
				.findOne(shippingRequirementAccept.getRequirementPlatId())
				.orElse(null);
		if (Objects.nonNull(plat)) {
			if (ShippingRequirementPlatDef.State.ENDED.match(plat.getState())
					|| ShippingRequirementPlatDef.State.CLOSED
							.match(plat.getState())) {
				return null;
			}
		}
		// 数据来源是三方的
		if (ShippingRequirementPlatDef.DataSource.OUTER
				.match(shippingRequirementAccept.getDataSource())) {
			// 则设置船主id为冈好运的
			shippingRequirementAccept.setCaptainId(transportProperties.getId());
			// 设置来源应用id
			shippingRequirementAccept.setSourceAppId(Objects
					.requireNonNull(ApplicationInfoContextHolder.getAppId()));
		}
		ShippingRequirementAccept accept = this
				.create(shippingRequirementAccept);
		// 返回这边的船主接单id
		return accept.getId();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void rejectAccept(Long id, String rejectReason) {
		super.findOne(id).ifPresent(accept -> {
			// 更新船运需求状态
			accept.setRejectReason(rejectReason);
			accept.setState(
					ShippingRequirementAcceptDef.PlatformState.ORDER_ACCEPTANCE_FAILED
							.getCode());
			accept.setRejectOrigin(
					ShippingRequirementAcceptDef.RejectOrigin.BACKSTAGE
							.getCode());
			// 更新承运商船运需求
			accept = super.updateAllProperties(accept);
			// 更新平台船运需求的接单状态 判断是否全部是拒绝 全是的话 平台的状态改成--
			List<ShippingRequirementAccept> accepts = this
					.findByPlatId(accept.getRequirementPlatId());
			if (CollectionUtils.isNotEmpty(accepts)) {
				long count = accepts.stream()
						.filter(item -> item.getState().equals(
								ShippingRequirementAcceptDef.PlatformState.ORDER_ACCEPTANCE_FAILED
										.getCode()))
						.count();
				if (accepts.size() == count) {
					platService.findOne(accept.getRequirementPlatId())
							.ifPresent(shippingRequirementPlat -> {
								shippingRequirementPlat.setOrderAcceptanceState(
										ShippingRequirementPlatDef.OrderAcceptanceState.CONFIRMED
												.getCode());
								platService.updateAllProperties(
										shippingRequirementPlat);
							});
				}
			}
		});
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@History(success = HistoryDef.SHIPPING_DEMAND_PUBLISH_REJECT_ORDER, bizNo = "{{#accept.getRequirementPlatId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_PUBLISH)
	public void reject(Long id, String rejectReason) {
		ShippingRequirementAccept accept = super.findOne(id).orElse(null);
		if (Objects.nonNull(accept)) {
			HistoryContext.putVariable("accept", accept);
			// 船运需求id不为空时
			if (Objects.nonNull(accept.getRequirementPlatId())) {
				// 查询对应的船运需求
				platService.findOne(accept.getRequirementPlatId())
						.ifPresent(plat -> {
							// 如果是第三方的船运需求 则需要推送信息给第三方
							if (ShippingRequirementPlatDef.DataSource.OUTER
									.match(plat.getDataSource())) {
								handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
										.getCode(),
										ShippingCallbackDef.PushType.REJECT_SHIP_ACCEPT
												.getCode(),
										String.valueOf(id));
							}
						});
			}
			// 更新船运需求状态
			accept.setRejectReason(rejectReason);
			accept.setState(
					ShippingRequirementAcceptDef.PlatformState.ORDER_ACCEPTANCE_FAILED
							.getCode());
			accept.setRejectOrigin(
					ShippingRequirementAcceptDef.RejectOrigin.BACKSTAGE
							.getCode());
			// 更新承运商船运需求
			accept = super.updateAllProperties(accept);
			// 更新平台船运需求的接单状态 判断是否全部是拒绝 全是的话 平台的状态改成--
			List<ShippingRequirementAccept> accepts = this
					.findByPlatId(accept.getRequirementPlatId());
			if (CollectionUtils.isNotEmpty(accepts)) {
				long count = accepts.stream()
						.filter(item -> item.getState().equals(
								ShippingRequirementAcceptDef.PlatformState.ORDER_ACCEPTANCE_FAILED
										.getCode()))
						.count();
				if (accepts.size() == count) {
					platService.findOne(accept.getRequirementPlatId())
							.ifPresent(shippingRequirementPlat -> {
								shippingRequirementPlat.setOrderAcceptanceState(
										ShippingRequirementPlatDef.OrderAcceptanceState.CONFIRMED
												.getCode());
								platService.updateAllProperties(
										shippingRequirementPlat);
							});
				}
			}

			Optional<Customer> captain = customService
					.findById(accept.getCaptainId());
			if (captain.isPresent()) {
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
								SendType.USERMESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.UNFIT_SHIP_TEMPLATE,
								accept.getRequirementPlatId()))
						.receiptors(
								List.of(String.valueOf(accept.getCaptainId())))
						.url(UserMessageConstants.GRAP_ORDER_DETAIL_PAGE)
						.role(AppTypeDef.AppType.CHUAN_WU.getCode())
						.detailId(String.valueOf(id))
						.initiator(UserMessageDef.BusinessInitiator.initiate
								.getCode())
						.templateCode(
								wxSubscriptionProperties.getOrderFailedCode())
						.params(Map.of("index", accept.getRequirementPlatId()))
						.mobile(captain.get().getMobile()).build());
				// 发送app推送
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(UserMessageConstants.TRANSPORT_TITLE)
						.content(MessageFormat.format(
								UserMessageConstants.UNFIT_SHIP_TEMPLATE,
								accept.getRequirementPlatId()))
						.appTypes(List.of(AppType.SHIP))
						.bizNo(String.valueOf(id))
						.moduleType(UserMessageConstants.GRAP_ORDER_DETAIL_PAGE)
						.receiptors(
								List.of(String.valueOf(accept.getCaptainId())))
						.build());
			}
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void rejectOtherAccept(String platId,
			List<ShippingRequirementAccept> list) {
		if (CollectionUtils.isNotEmpty(list)) {
			// 过滤掉已经被拒绝过的 不需要再发短信
			List<ShippingRequirementAccept> smsList = list.stream()
					.filter(t -> Objects.isNull(t.getRejectOrigin())).toList();
			for (ShippingRequirementAccept shippingRequirementAccept : smsList) {
				// 如果接单信息是三方的，则跳过此条数据，不发送短信
				if (!ShippingRequirementPlatDef.DataSource.OUTER
						.match(shippingRequirementAccept.getDataSource())) {

					Optional<Customer> captain = customService
							.findById(shippingRequirementAccept.getCaptainId());
					captain.ifPresent(customer -> {
						messageService.sendNotice(Messages.builder()
								.messageTypes(
										List.of(SendType.ALIMESSAGE.getCode(),
												SendType.USERMESSAGE.getCode()))
								.type(UserMessageDef.MessageType.TRANSFORM
										.getCode())
								.title(MessageFormat.format(
										UserMessageConstants.UNFIT_SHIP_TEMPLATE,
										shippingRequirementAccept
												.getRequirementPlatId()))
								.receiptors(List.of(
										String.valueOf(shippingRequirementAccept
												.getCaptainId())))
								.url(UserMessageConstants.GRAP_ORDER_DETAIL_PAGE)
								.role(AppTypeDef.AppType.CHUAN_WU.getCode())
								.detailId(String.valueOf(
										shippingRequirementAccept.getId()))
								.initiator(
										UserMessageDef.BusinessInitiator.initiate
												.getCode())
								.templateCode(wxSubscriptionProperties
										.getOrderFailedCode())
								.params(Map.of("index",
										shippingRequirementAccept
												.getRequirementPlatId()))
								.mobile(customer.getMobile()).build());
						// 发送app推送
						messageService.sendNotice(Messages.builder()
								.messageTypes(List
										.of(SendType.PUSH_MESSAGE.getCode()))
								.type(UserMessageDef.MessageType.TRANSFORM
										.getCode())
								.title(UserMessageConstants.TRANSPORT_TITLE)
								.content(MessageFormat.format(
										UserMessageConstants.UNFIT_SHIP_TEMPLATE,
										shippingRequirementAccept
												.getRequirementPlatId()))
								.appTypes(List.of(AppType.SHIP))
								.bizNo(String.valueOf(
										shippingRequirementAccept.getId()))
								.moduleType(
										UserMessageConstants.GRAP_ORDER_DETAIL_PAGE)
								.receiptors(List.of(
										String.valueOf(shippingRequirementAccept
												.getCaptainId())))
								.build());
					});
				}
			}
			for (ShippingRequirementAccept shippingRequirementAccept : list) {
				shippingRequirementAccept.setState(
						ShippingRequirementAcceptDef.PlatformState.ORDER_ACCEPTANCE_FAILED
								.getCode());
				shippingRequirementAccept.setRejectReason("其他船主已抢单");
				shippingRequirementAccept.setRejectOrigin(
						ShippingRequirementAcceptDef.RejectOrigin.OTHERS
								.getCode());
				super.updateAllProperties(shippingRequirementAccept);
			}
		}
		// 平台船运需求状态--
		platService.findOne(platId).ifPresent(shippingRequirementPlat -> {
			shippingRequirementPlat.setOrderAcceptanceState(
					ShippingRequirementPlatDef.OrderAcceptanceState.CONFIRMED
							.getCode());
			platService.updateAllProperties(shippingRequirementPlat);
		});
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void cancel(Long id) {
		ShippingRequirementAccept accept = super.findOne(id).orElse(null);
		if (Objects.nonNull(accept)) {
			ShippingRequirementPlat shippingRequirementPlat = shippingRequirementPlatService
					.findOne(accept.getRequirementPlatId()).orElse(null);
			if (Objects.nonNull(shippingRequirementPlat)) {
				// 抢单的船运需求是三方平台
				if (ShippingRequirementPlatDef.DataSource.OUTER
						.match(shippingRequirementPlat.getDataSource())) {
					handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
							.getCode(),
							ShippingCallbackDef.PushType.CANCEL_SHIP_ACCEPT
									.getCode(),
							String.valueOf(id));
				}
			}
			// 更新平台船运需求的接单状态
			List<ShippingRequirementAccept> accepts = this
					.findByPlatId(accept.getRequirementPlatId()).stream()
					.filter(accept1 -> !id.equals(accept1.getId())).toList();
			// 计算该平台需求下是否存在待确认或待支付定金的接单记录
			this.updatePlatOrderAcceptanceState(accepts,
					accept.getRequirementPlatId());
		}
		super.delete(id);
	}

	/**
	 * 创建船运单
	 *
	 * @param accept
	 */
	private void buildTransportOrderShip(ShippingRequirementAccept accept) {
		// 新增船运单
		platService.findOne(accept.getRequirementPlatId())
				.ifPresent(shippingRequirementPlat -> {
					ArrayString orderShipIds = new ArrayString();
					// 新增船运单
					ArrayCarrierShip shipInfos = accept.getShipInfos();
					for (CarrierShip shipInfo : shipInfos) {
						// 新增船运单
						TransportOrderShip transportOrderShip = new TransportOrderShip();

						// 货物类型
						transportOrderShip.setGoodsType(
								shippingRequirementPlat.getGoodsType());
						// 关联的平台船运需求单ID
						transportOrderShip
								.setSrpId(shippingRequirementPlat.getId());
						// 关联的承运商船运需求单ID
						transportOrderShip.setSraId(accept.getId());
						// 货主id
						transportOrderShip.setOwnerId(
								shippingRequirementPlat.getOwnerId());
						// 船运需求里面的货主账号信息
						Enterprise ownerEnterprise = shippingRequirementPlat
								.getOwnerEnterprise();
						// 设置货主账号信息
						transportOrderShip.setOwnerEnterprise(ownerEnterprise);
						// 货主信息联系人 取船运需求里面的货物信息 联系人
						transportOrderShip.setOwnerName(
								shippingRequirementPlat.getContact());
						// 货主信息手机号 取船运需求里面的货物信息 手机号
						transportOrderShip.setOwnerMobile(
								shippingRequirementPlat.getMobile());
						// 船主id
						transportOrderShip.setCaptainId(accept.getCaptainId());
						// 初始化船主信息 --里面的信息有可能是错误的 姓名和手机号是船主抢单时填的联系人和手机号并不一定是账号信息
						Enterprise captainEnterprise = new Enterprise();
						BeanUtils.copyProperties(accept.getCaptainEnterprise(),
								captainEnterprise);
						// 根据船主id查询船主账号信息 设置账号信息
						customService.findVoById(accept.getCaptainId())
								.ifPresent(customerVo -> {
									captainEnterprise
											.setName(customerVo.getCustomer()
													.getInstitutionName());
									captainEnterprise.setRealName(customerVo
											.getCustomer().getRealName());
									captainEnterprise.setCode(
											customerVo.getCustomer().getCode());
									captainEnterprise.setMobile(customerVo
											.getCustomer().getMobile());
									captainEnterprise.setLegalRepresentative(
											customerVo.getCustomer()
													.getLegalRepresentative());
									captainEnterprise
											.setUnifiedSocialCreditCode(
													customerVo.getCustomer()
															.getUnifiedSocialCreditCode());

								});
						transportOrderShip
								.setCaptainEnterprise(captainEnterprise);
						// 船主信息联系人
						transportOrderShip.setCaptainName(
								accept.getCaptainEnterprise().getRealName());
						// 船主信息手机号
						transportOrderShip.setCaptainMobile(
								accept.getCaptainEnterprise().getMobile());
						// 船舶id
						transportOrderShip
								.setShipId(String.valueOf(shipInfo.getId()));

						// 运价
						transportOrderShip
								.setUnitPrice(shipInfo.getUnitPrice());
						// 吨数
						transportOrderShip.setTon(shipInfo.getTon());
						// 始发港
						transportOrderShip.setSourcePortId(
								shippingRequirementPlat.getSourcePortId());
						transportOrderShip.setSourcePortName(
								shippingRequirementPlat.getSourcePortName());
						// 目的港
						transportOrderShip.setDestinationPortId(
								shippingRequirementPlat.getDestinationPortId());
						transportOrderShip
								.setDestinationPortName(shippingRequirementPlat
										.getDestinationPortName());
						// 装载日期
						transportOrderShip.setLoadDate(accept.getLoadDate());
						// 装载日期宽限天数
						transportOrderShip.setLoadDays(accept.getLoadDays());
						// 装卸天数
						transportOrderShip
								.setLoadUnloadDays(accept.getLoadUnloadDays());
						transportOrderShip.setShipRouteId(
								shippingRequirementPlat.getShipRouteId());
						transportOrderShip
								.setShipRouteDestination(shippingRequirementPlat
										.getShipRouteDestination());
						transportOrderShip.setShipRouteSource(
								shippingRequirementPlat.getShipRouteSource());
						// 实际运费
						transportOrderShip.setActualFreightCost(
								shipInfo.getActualFreightCost());
						// 海事费用
						transportOrderShip.setMaritimeAffairsFee(
								accept.getMaritimeAffairsFee());
						// 类型 船主接单
						transportOrderShip.setType(
								TransportOrderShipDef.Type.SHIP_OWNER_TAKES_ORDERS
										.getCode());
						transportOrderShip
								.setDemurrageFee(accept.getDemurrageFee());
						// 船主信息服务费
						transportOrderShip.setCaptainShipInfoServiceFee(
								shipInfo.getShippingInformationFee());
						// 货主信息服务费
						transportOrderShip.setOwnerShipInfoServiceFee(
								shippingRequirementPlat
										.getShipInfoServiceFee());
						// 定金
						transportOrderShip.setDeposit(shipInfo.getDeposit());
						// 船运定金支付方式
						transportOrderShip.setPayType(
								shippingRequirementPlat.getPayType());

						// 船主收款银行账户id
						transportOrderShip
								.setCaptainBankId(accept.getCaptainBankId());
						// 船主收款银行账户信息--收定金使用
						transportOrderShip.setCaptainBankInfo(
								accept.getCaptainBankInfo());
						// 新增
						TransportOrderShip transportOrderShip1 = transportOrderShipService
								.buildTransportOrderShip(transportOrderShip);

						// 给承运商船运需求回填船运单信息
						orderShipIds.add(transportOrderShip1.getId());
					}
					accept.setOrderShipIds(orderShipIds);
				});
	}

	/**
	 * 更新平台船运需求的接单状态
	 *
	 * @param accepts
	 * @param platId
	 */
	private void updatePlatOrderAcceptanceState(
			List<ShippingRequirementAccept> accepts, String platId) {
		long count = accepts.stream().filter(a -> (a.getState()
				.equals(ShippingRequirementAcceptDef.PlatformState.PROCESSING
						.getCode()))
				&& a.getDel().equals(CommonDef.Symbol.NO.getCode())).count();
		// 更新平台船运需求的接单状态
		platService.findOne(platId).ifPresent(plat -> {
			if (count == 0) {
				plat.setOrderAcceptanceState(
						ShippingRequirementPlatDef.OrderAcceptanceState.CONFIRMED
								.getCode());
			} else {
				plat.setOrderAcceptanceState(
						ShippingRequirementPlatDef.OrderAcceptanceState.PENDING_PROCESSING
								.getCode());
			}
			platService.updateAllProperties(plat);
		});
	}

	/**
	 * 组装数据
	 *
	 * @param accept
	 * @return
	 */
	private ShippingRequirementAcceptVo packVo(
			ShippingRequirementAccept accept) {
		ShippingRequirementAcceptVo vo = new ShippingRequirementAcceptVo();
		platService.findOne(accept.getRequirementPlatId()).ifPresent(plat -> {
			ShippingRequirementPlatVo platVo = new ShippingRequirementPlatVo();
			platVo.setPlat(plat);
			priceIndexService.findNewestDataByShipRouteId(plat.getShipRouteId())
					.ifPresent(platVo::setPriceIndex);
			vo.setPlatVo(platVo);
		});
		// 船运单信息
		vo.setTransportOrderShipVoList(
				transportOrderShipService.findVoBySraId(accept.getId()));
		vo.setAccept(accept);
		return vo;
	}

	/**
	 * 组装数据
	 *
	 * @param acceptList
	 * @return
	 */
	private List<ShippingRequirementAcceptVo> packVo(
			List<ShippingRequirementAccept> acceptList) {
		if (CollectionUtils.isEmpty(acceptList)) {
			return List.of();
		}
		// 接单id
		List<Long> acceptIds = acceptList.stream()
				.map(ShippingRequirementAccept::getId).toList();

		// 平台船运需求id集合
		List<String> platIds = acceptList.stream()
				.map(ShippingRequirementAccept::getRequirementPlatId).toList();
		Map<String, ShippingRequirementPlat> shippingRequirementPlatMap = shippingRequirementPlatService
				.getIdMap(platIds);

		// 分组
		Map<Long, List<TransportOrderShipVo>> transportOrderShipMap = transportOrderShipService
				.findVoBySraIds(acceptIds).stream()
				.collect(Collectors.groupingBy(
						item -> item.getTransportOrderShip().getSraId()));

		List<ShippingRequirementAcceptVo> shippingRequirementAcceptVoList = new ArrayList<>();
		for (ShippingRequirementAccept accept : acceptList) {
			ShippingRequirementAcceptVo shippingRequirementAcceptVo = new ShippingRequirementAcceptVo();
			shippingRequirementAcceptVo.setAccept(accept);
			shippingRequirementAcceptVo.setTransportOrderShipVoList(
					transportOrderShipMap.get(accept.getId()));
			if (StringUtils.isNotBlank(accept.getRequirementPlatId())) {
				ShippingRequirementPlatVo vo = new ShippingRequirementPlatVo();
				ShippingRequirementPlat shippingRequirementPlat = shippingRequirementPlatMap
						.get(accept.getRequirementPlatId());
				priceIndexService
						.findNewestDataByShipRouteId(
								shippingRequirementPlat.getShipRouteId())
						.ifPresent(vo::setPriceIndex);
				vo.setPlat(shippingRequirementPlat);
				shippingRequirementAcceptVo.setPlatVo(vo);
			}
			shippingRequirementAcceptVoList.add(shippingRequirementAcceptVo);
		}
		return shippingRequirementAcceptVoList;
	}

	public void handle(Integer type, Integer action, String id) {
		if (Objects.isNull(type) || Objects.isNull(action)
				|| Objects.isNull(id)) {
			return;
		}
		ApplicationTransfer transfer = new ApplicationTransfer(type, action,
				id);
		mqUtil.asyncSend(MqMessage.builder()
				.topic(TopicDef.APPLICATION_CONNECTOR)
				.message(MessageBuilder
						.withPayload(JsonUtils.objectToJson(transfer)).build())
				.build());
	}

}
