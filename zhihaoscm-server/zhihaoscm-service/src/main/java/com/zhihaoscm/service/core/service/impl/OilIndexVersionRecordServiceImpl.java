package com.zhihaoscm.service.core.service.impl;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.mybatis.plus.util.UpdateUtil;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.bean.entity.OilIndex;
import com.zhihaoscm.domain.bean.entity.OilIndexSnapshot;
import com.zhihaoscm.domain.bean.entity.OilIndexVersionRecord;
import com.zhihaoscm.domain.bean.json.ArrayOilVersionIndex;
import com.zhihaoscm.domain.bean.json.OilVersionIndex;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.FileVo;
import com.zhihaoscm.domain.bean.vo.OilIndexVersionRecordVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.IndexVersionRecordDef;
import com.zhihaoscm.domain.meta.biz.WxwDef;
import com.zhihaoscm.service.core.mapper.OilIndexVersionRecordMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.UserService;

/**
 * <p>
 * 油品指数版本提交记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class OilIndexVersionRecordServiceImpl extends
		MpLongIdBaseServiceImpl<OilIndexVersionRecord, OilIndexVersionRecordMapper>
		implements OilIndexVersionRecordService {

	public OilIndexVersionRecordServiceImpl(
			OilIndexVersionRecordMapper repository) {
		super(repository);
	}

	@Autowired
	private UserService userService;

	@Autowired
	private OilIndexSnapshotService oilIndexSnapshotService;

	@Autowired
	private FileService fileService;

	@Autowired
	private OilIndexVersionService oilIndexVersionService;

	@Autowired
	private OilIndexService oilIndexService;

	@Autowired
	private MessageService messageService;

	@Override
	public Page<OilIndexVersionRecordVo> paging(Integer page, Integer size,
			String sortKey, String sortOrder, Integer state, Long versionId,
			Long operateBy) {
		LambdaQueryWrapper<OilIndexVersionRecord> wrapper = Wrappers
				.lambdaQuery(OilIndexVersionRecord.class);
		if (StringUtils.isAnyBlank(sortKey, sortOrder)) {
			wrapper.orderByAsc(OilIndexVersionRecord::getState);
			wrapper.orderByDesc(OilIndexVersionRecord::getUpdatedTime);
		} else {
			wrapper.last("order by " + sortKey + " " + sortOrder);
		}
		wrapper.eq(Objects.nonNull(versionId),
				OilIndexVersionRecord::getVersionId, versionId);
		wrapper.eq(Objects.nonNull(operateBy),
				OilIndexVersionRecord::getOperateBy, operateBy);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(state), OilIndexVersionRecord::getState,
				state);
		Page<OilIndexVersionRecord> pageResult = repository
				.selectPage(new Page<>(page, size), wrapper);
		return PageUtil.getRecordsInfoPage(pageResult,
				this.packVo(pageResult.getRecords()));
	}

	@Override
	public Optional<OilIndexVersionRecordVo> findVoById(Long id) {
		return this.findOne(id).map(this::packVo);
	}

	@Override
	public List<OilIndexVersionRecord> findByVersionId(Long versionId) {
		LambdaQueryWrapper<OilIndexVersionRecord> wrapper = Wrappers
				.lambdaQuery(OilIndexVersionRecord.class);
		this.filterDeleted(wrapper);
		wrapper.eq(OilIndexVersionRecord::getState,
				IndexVersionRecordDef.State.PUBLISHED.getCode());
		wrapper.eq(OilIndexVersionRecord::getVersionId, versionId);
		wrapper.orderByDesc(OilIndexVersionRecord::getUpdatedTime);
		return repository.selectList(wrapper);
	}

	@Override
	public List<OilIndexVersionRecord> findByVersionIdsAndStates(
			List<Long> versionIds, List<Integer> states) {
		if (CollectionUtils.isEmpty(versionIds)) {
			return List.of();
		}
		LambdaQueryWrapper<OilIndexVersionRecord> wrapper = Wrappers
				.lambdaQuery(OilIndexVersionRecord.class);
		this.filterDeleted(wrapper);
		wrapper.in(OilIndexVersionRecord::getVersionId, versionIds);
		wrapper.in(OilIndexVersionRecord::getState, states);
		return repository.selectList(wrapper);
	}

	@Override
	@FileId
	public OilIndexVersionRecord create(OilIndexVersionRecord resource) {
		// 获取当前登录用户id
		this.updateEdit(resource);
		return super.create(resource);
	}

	@Override
	@FileId(type = 2)
	public OilIndexVersionRecord updateAllProperties(
			OilIndexVersionRecord resource) {
		return super.updateAllProperties(resource);
	}

	@Override
	@FileId(type = 3)
	public void delete(Long id) {
		// 删除快照
		oilIndexSnapshotService.deleteByVersionRecordId(id);
		super.delete(id);
	}

	@Override
	public void save(Long id, ArrayLong fileIds) {
		// 状态变为待发布
		super.findOne(id).ifPresent(oilIndexVersionRecord -> {
			oilIndexVersionRecord.setState(
					IndexVersionRecordDef.State.TO_BE_SUBMIT.getCode());
			this.updateEdit(oilIndexVersionRecord);
			oilIndexVersionRecord.setFileIds(fileIds);
			this.updateAllProperties(oilIndexVersionRecord);
		});
	}

	@Override
	public void submit(Long id, ArrayLong fileIds) {
		// 状态变为待发布
		super.findOne(id).ifPresent(oilIndexVersionRecord -> {
			oilIndexVersionRecord.setState(
					IndexVersionRecordDef.State.TO_BE_PUBLISH.getCode());
			// 获取当前登录用户id
			updateEdit(oilIndexVersionRecord);
			oilIndexVersionRecord.setFileIds(fileIds);
			this.updateAllProperties(oilIndexVersionRecord);
			// 发送企微消息
			oilIndexVersionService.findOne(oilIndexVersionRecord.getVersionId())
					.ifPresent(oilIndexVersion -> {
						LocalDate localDate = oilIndexVersion.getVersionDate()
								.toLocalDate();
						messageService.sendNotice(WxwMessage.builder()
								.receiptors(userService.findUsersByPermission(
										AdminPermissionDef.PETROL_INDEX_MANAGE,
										null).stream()
										.map(user -> String
												.valueOf(user.getId()))
										.toList())
								.url("/indexResearch/oilIndex/oilIndexVersionRecord/"
										.concat(String.valueOf(id)))
								.prefix(WxwDef.NoticePrefix.YOU_HAVE_NEW
										.getDesc())
								.operationModule("油品指数版本").desc("提交记录待发布")
								.keyword(String.valueOf(localDate))
								.content(StringUtils.EMPTY).build());
					});
		});
	}

	@Override
	public void publish(Long id) {
		// 状态变为已发布 将指数快照数据新增到指数 且记录发布前指数数据到json
		super.findOne(id).ifPresent(oilIndexVersionRecord -> {
			oilIndexVersionRecord
					.setState(IndexVersionRecordDef.State.PUBLISHED.getCode());
			// 设置记录里面处理人
			UserInfoContext context = UserInfoContextHolder.getContext();
			if (Objects.nonNull(context)) {
				Long userId = context.getUserId();
				userService.findOne(userId).ifPresent(user -> {
					oilIndexVersionRecord.setOperateBy(userId);
					oilIndexVersionRecord.setOperateName(user.getName());
				});
			}
			// 设置版本里面的处理人
			oilIndexVersionService.findOne(oilIndexVersionRecord.getVersionId())
					.ifPresent(version -> {
						if (Objects.nonNull(context)) {
							Long userId = context.getUserId();
							userService.findOne(userId).ifPresent(user -> {
								version.setOperateBy(userId);
								version.setOperateName(user.getName());
							});
						}
						oilIndexVersionService.updateAllProperties(version);
					});

			// 当前版本已发数据
			List<OilIndex> shippingPriceIndexList = oilIndexService
					.findByVersionId(oilIndexVersionRecord.getVersionId());
			Map<String, OilIndex> stringShippingPriceIndexMap = shippingPriceIndexList
					.stream()
					.collect(Collectors.toMap(
							spi -> spi.getVersionDate() + "-"
									+ spi.getOilSiteId(),
							spi -> spi, (existing, replacement) -> existing));

			// 组装发布这个版本记录之前的指数数据 组装成json
			ArrayOilVersionIndex content = new ArrayOilVersionIndex();
			for (OilIndex priceIndex : shippingPriceIndexList) {
				OilVersionIndex oilVersionIndex = new OilVersionIndex();
				BeanUtils.copyProperties(priceIndex, oilVersionIndex);
				content.add(oilVersionIndex);
			}

			// 根据版本id和记录id查询快照数据
			List<OilIndexSnapshot> snapshotList = oilIndexSnapshotService
					.findByVersionId(oilIndexVersionRecord.getVersionId(), id);

			// 遍历快照数据
			for (OilIndexSnapshot oilIndexSnapshot : snapshotList) {
				// 声明运价指数对象
				OilIndex index = new OilIndex();
				// 将快照数据复制到指数对象
				BeanUtils.copyProperties(oilIndexSnapshot, index);

				// 校验已发布的是否存在 不在则新增 存在则修改之前的数据
				OilIndex exist = stringShippingPriceIndexMap.get(
						index.getVersionDate() + "-" + index.getOilSiteId());
				if (Objects.isNull(exist)) {
					// id置空
					index.setId(null);
					OilIndex priceIndex = oilIndexService.create(index);
					oilIndexSnapshot.setOilIndexId(priceIndex.getId());
				} else {
					// 存在则 将将存在数据修改成当前的快照指数数据
					index.setId(exist.getId());
					UpdateUtil.copyNonNullProperties(index, exist);
					exist.setLightFuelActualSellingPrice(
							index.getLightFuelActualSellingPrice());
					exist.setLightFuelListingPrice(
							index.getLightFuelListingPrice());
					exist.setZeroDieselActualSellingPrice(
							index.getZeroDieselActualSellingPrice());
					exist.setZeroDieselListingPrice(
							index.getZeroDieselListingPrice());
					oilIndexService.updateAllProperties(exist);
					oilIndexSnapshot.setOilIndexId(exist.getId());
				}
			}
			// 回填指数id
			oilIndexSnapshotService.batchUpdate(snapshotList);

			// 设置json
			oilIndexVersionRecord.setContent(content);
			this.updateAllProperties(oilIndexVersionRecord);
		});
	}

	@Override
	public void reject(Long id, String remark) {
		// 状态变为已驳回
		super.findOne(id).ifPresent(oilIndexVersionRecord -> {
			oilIndexVersionRecord
					.setState(IndexVersionRecordDef.State.REJECTED.getCode());
			// 获取当前登录用户id
			UserInfoContext context = UserInfoContextHolder.getContext();
			if (Objects.nonNull(context)) {
				Long userId = context.getUserId();
				userService.findOne(userId).ifPresent(user -> {
					oilIndexVersionRecord.setOperateBy(userId);
					oilIndexVersionRecord.setOperateName(user.getName());
				});
			}
			oilIndexVersionRecord.setRemark(remark);
			this.updateAllProperties(oilIndexVersionRecord);

			// 发送企微消息
			if (Objects.nonNull(oilIndexVersionRecord.getEditBy())) {
				messageService.sendNotice(WxwMessage.builder()
						.receiptors(List.of(String
								.valueOf(oilIndexVersionRecord.getEditBy())))
						.url("/indexResearch/oilIndex/oilIndexVersionRecord/"
								.concat(String.valueOf(id)))
						.keyword(String.valueOf(id))
						.content("你提交的油品指数版本提交记录%s已驳回").build());
			}
		});
	}

	@Override
	public void revoke(Long id) {
		// 状态为变已撤回 且指数数据退回到上个记录 把json数据和现在指数数据对比
		super.findOne(id).ifPresent(oilIndexVersionRecord -> {
			oilIndexVersionRecord
					.setState(IndexVersionRecordDef.State.REVOKED.getCode());
			// 获取当前登录用户id
			UserInfoContext context = UserInfoContextHolder.getContext();
			if (Objects.nonNull(context)) {
				Long userId = context.getUserId();
				userService.findOne(userId).ifPresent(user -> {
					oilIndexVersionRecord.setOperateBy(userId);
					oilIndexVersionRecord.setOperateName(user.getName());
				});
			}

			// 解析出上个版本指数数据进行对比
			List<OilIndex> lastShippingPriceIndexList = new ArrayList<>();
			if (CollectionUtils
					.isNotEmpty(oilIndexVersionRecord.getContent())) {
				ArrayOilVersionIndex content = oilIndexVersionRecord
						.getContent();

				for (OilVersionIndex oilVersionIndex : content) {
					OilIndex oilIndex = new OilIndex();
					BeanUtils.copyProperties(oilVersionIndex, oilIndex);
					lastShippingPriceIndexList.add(oilIndex);
				}
			}

			// 将这个版本记录中json指数数据转成map
			Map<String, OilIndex> lastShippingPriceIndexMap = lastShippingPriceIndexList
					.stream()
					.collect(Collectors.toMap(
							spi -> spi.getVersionDate() + "-"
									+ spi.getOilSiteId(),
							spi -> spi, (existing, replacement) -> existing));

			// 查询现在的数据
			List<OilIndex> oilIndexList = oilIndexService
					.findByVersionId(oilIndexVersionRecord.getVersionId());
			// 遍历当前版本运价指数
			for (OilIndex priceIndex : oilIndexList) {

				// 判断上个版本是否存在当前指数
				OilIndex exist = lastShippingPriceIndexMap
						.get(priceIndex.getVersionDate() + "-"
								+ priceIndex.getOilSiteId());
				// 存在直接更新,不存在直接删除
				if (Objects.nonNull(exist)) {
					// 将当前的数据更新成上个版本数据
					UpdateUtil.copyNonNullProperties(exist, priceIndex);
					priceIndex.setLightFuelActualSellingPrice(
							exist.getLightFuelActualSellingPrice());
					priceIndex.setLightFuelListingPrice(
							exist.getLightFuelListingPrice());
					priceIndex.setZeroDieselActualSellingPrice(
							exist.getZeroDieselActualSellingPrice());
					priceIndex.setZeroDieselListingPrice(
							exist.getZeroDieselListingPrice());
					oilIndexService.updateAllProperties(priceIndex);
				} else {
					oilIndexService.delete(priceIndex.getId());
				}
			}

			// 将快照表指数id置空
			List<OilIndexSnapshot> snapshotList = oilIndexSnapshotService
					.findByVersionId(oilIndexVersionRecord.getVersionId(), id);
			for (OilIndexSnapshot oilIndexSnapshot : snapshotList) {
				oilIndexSnapshot.setOilIndexId(null);
			}
			oilIndexSnapshotService.batchUpdate(snapshotList);

			this.updateAllProperties(oilIndexVersionRecord);
		});
	}

	@Override
	public void deleteByVersionId(Long versionId) {
		LambdaUpdateWrapper<OilIndexVersionRecord> wrapper = Wrappers
				.lambdaUpdate(OilIndexVersionRecord.class)
				.eq(OilIndexVersionRecord::getVersionId, versionId)
				.set(OilIndexVersionRecord::getDel,
						CommonDef.Symbol.YES.getCode());
		repository.update(wrapper);
	}

	/**
	 * 修改编辑人
	 *
	 * @param oilIndexVersionRecord
	 */
	private void updateEdit(OilIndexVersionRecord oilIndexVersionRecord) {
		// 获取当前登录用户id
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (Objects.nonNull(context)) {
			Long userId = context.getUserId();
			userService.findOne(userId).ifPresent(user -> {
				oilIndexVersionRecord.setEditBy(userId);
				oilIndexVersionRecord.setEditName(user.getName());
			});
		}
	}

	/**
	 * 组装数据
	 *
	 * @param oilIndexVersionRecord
	 * @return
	 */
	private OilIndexVersionRecordVo packVo(
			OilIndexVersionRecord oilIndexVersionRecord) {
		OilIndexVersionRecordVo vo = new OilIndexVersionRecordVo();
		vo.setOilIndexVersionRecord(oilIndexVersionRecord);
		ArrayLong fileIds = oilIndexVersionRecord.getFileIds();
		if (CollectionUtils.isNotEmpty(fileIds)) {
			List<FileVo> fileVoList = new ArrayList<>();
			List<File> fileList = fileService.findByIds(fileIds);
			if (CollectionUtils.isNotEmpty(fileList)) {
				for (File file : fileList) {
					FileVo fileVo = new FileVo();
					fileVo.setFile(file);
					userService.findOne(file.getCreatedBy())
							.ifPresent(fileVo::setUser);
					fileVoList.add(fileVo);
				}
			}
			vo.setFileVoList(fileVoList);
		}
		return vo;
	}

	/**
	 * 组装数据
	 *
	 * @param records
	 * @return
	 */
	private List<OilIndexVersionRecordVo> packVo(
			List<OilIndexVersionRecord> records) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		// 版本记录ID集合
		List<Long> versionRecordIds = records.stream()
				.map(OilIndexVersionRecord::getId).toList();

		Map<Long, List<OilIndexSnapshot>> snapshotListMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(versionRecordIds)) {
			List<OilIndexSnapshot> snapshotList = oilIndexSnapshotService
					.findByVersionRecordIds(versionRecordIds);

			// 根据版本记录ID分组
			snapshotListMap = snapshotList.stream().collect(Collectors
					.groupingBy(OilIndexSnapshot::getVersionRecordId));

		}

		List<OilIndexVersionRecordVo> oilIndexVersionRecordVos = new ArrayList<>();

		for (OilIndexVersionRecord record : records) {
			OilIndexVersionRecordVo versionRecordVo = new OilIndexVersionRecordVo();
			versionRecordVo.setOilIndexVersionRecord(record);
			List<OilIndexSnapshot> snapshotList = snapshotListMap
					.get(record.getId());
			if (CollectionUtils.isNotEmpty(snapshotList)) {
				versionRecordVo.setNum(snapshotList.size());
			} else {
				versionRecordVo.setNum(0);
			}
			oilIndexVersionRecordVos.add(versionRecordVo);
		}
		return oilIndexVersionRecordVos;
	}

}
