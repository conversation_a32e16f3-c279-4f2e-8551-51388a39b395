package com.zhihaoscm.service.core.service;

import java.util.List;
import java.util.Optional;

import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.BusinessOpportunityCollect;

/**
 * <p>
 * 收藏商机 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface BusinessOpportunityCollectService
		extends MpLongIdBaseService<BusinessOpportunityCollect> {

	/**
	 * 根据用户id查询收藏列表
	 * 
	 * @param customId
	 * @return
	 */
	List<BusinessOpportunityCollect> findByCustomId(Long customId);

	/**
	 * 根据用户id和商机id查询
	 * 
	 * @param customId
	 * @param businessOpportunityId
	 * @return
	 */
	Optional<BusinessOpportunityCollect> findByCustomIdAndBusinessId(
			Long customId, Long businessOpportunityId);
}
