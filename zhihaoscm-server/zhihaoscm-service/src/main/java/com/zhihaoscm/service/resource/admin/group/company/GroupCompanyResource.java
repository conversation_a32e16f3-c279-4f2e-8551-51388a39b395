package com.zhihaoscm.service.resource.admin.group.company;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.GroupCompany;
import com.zhihaoscm.domain.bean.vo.GroupCompanyVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.service.core.service.GroupCompanyService;
import com.zhihaoscm.service.resource.form.group.company.GroupCompanyForm;
import com.zhihaoscm.service.resource.validator.group.company.GroupCompanyValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import java.util.List;

/**
 * <p>
 * 集团管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@RequestMapping("/group-company")
public class GroupCompanyResource {

	@Autowired
	private GroupCompanyService service;
	@Autowired
	private GroupCompanyValidator validator;

	@Operation(summary = "分页查询集团列表")
	@GetMapping("/paging")
	@Secured({ AdminPermissionDef.SUPPLIER_R,
			AdminPermissionDef.SUPPLIER_DEAL })
	public ApiResponse<Page<GroupCompanyVo>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "集团名称") @RequestParam(required = false) String keyword,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder) {
		return new ApiResponse<>(PageUtil.convert(
				service.paging(page, size, keyword, sortKey, sortOrder)));
	}

	@Operation(summary = "查看详情")
	@GetMapping("/{id}")
	@Secured({ AdminPermissionDef.SUPPLIER_R,
			AdminPermissionDef.SUPPLIER_DEAL })
	public ApiResponse<GroupCompany> finById(@PathVariable String id) {
		return new ApiResponse<>(validator.validateExist(id));
	}

    @Operation(summary = "集团下拉框数据")
    @GetMapping("/select-box-data")
    @Secured({ AdminPermissionDef.SUPPLIER_R,
            AdminPermissionDef.SUPPLIER_DEAL })
    public ApiResponse<List<GroupCompany>> selectBoxData() {
        LambdaQueryWrapper<GroupCompany> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GroupCompany::getDel, CommonDef.Symbol.NO.getCode());
        wrapper.orderByDesc(GroupCompany::getCreatedTime);
        List<GroupCompany> list = service.getRepository().selectList(wrapper);
        return new ApiResponse<>(list);
    }

	@Operation(summary = "添加集团")
	@PostMapping
	@Secured({ AdminPermissionDef.SUPPLIER_DEAL })
	public ApiResponse<GroupCompany> create(
			@Validated @RequestBody GroupCompanyForm form) {
		validator.validateCreate(form);
		return new ApiResponse<>(service.create(form.convertToEntity()));
	}

	@Operation(summary = "修改集团")
	@PutMapping("/{id}")
	@Secured({ AdminPermissionDef.SUPPLIER_DEAL })
	public ApiResponse<GroupCompany> update(@PathVariable String id,
			@Validated @RequestBody GroupCompanyForm form) {
		GroupCompany groupCompany = validator.validateUpdate(id, form);
		return new ApiResponse<>(service
				.updateAllProperties(form.convertToEntity(groupCompany)));
	}

	@Operation(summary = "删除集团")
	@DeleteMapping("/{id}")
	@Secured({ AdminPermissionDef.SUPPLIER_DEAL })
	public ApiResponse<Void> delete(@PathVariable String id) {
		validator.validateDelete(id);
		service.delete(id);
		return new ApiResponse<>();
	}
}
