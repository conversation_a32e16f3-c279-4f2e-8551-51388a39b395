package com.zhihaoscm.service.resource.form.oil.index.snapshot;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "OilIndexSnapshotImportForm", description = "油品指数上传快照表单")
public class OilIndexSnapshotUploadForm {
	/**
	 * 站点id
	 */
	private String oilSiteId;

	/**
	 * 站点名称
	 */
	private String oilSiteName;

	/**
	 * 品牌
	 */
	private String brand;
	/**
	 * 0号柴油挂牌价
	 */
	private String zeroDieselListingPrice;

	/**
	 * 0号柴油实际零售价
	 */
	private String zeroDieselActualSellingPrice;

	/**
	 * 轻质燃油挂牌价
	 */
	private String lightFuelListingPrice;

	/**
	 * 轻质燃油实际零售价
	 */
	private String lightFuelActualSellingPrice;

}
