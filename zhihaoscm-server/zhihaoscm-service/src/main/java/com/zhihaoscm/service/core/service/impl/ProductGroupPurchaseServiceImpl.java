package com.zhihaoscm.service.core.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.Assign;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.bean.entity.ProductGroupPurchase;
import com.zhihaoscm.domain.bean.entity.User;
import com.zhihaoscm.domain.bean.vo.ProductConsignmentVo;
import com.zhihaoscm.domain.bean.vo.ServiceSpecialVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AssignDef;
import com.zhihaoscm.domain.meta.biz.AutoCodeDef;
import com.zhihaoscm.domain.meta.biz.ProductGroupPurchaseDef;
import com.zhihaoscm.service.core.mapper.ProductGroupPurchaseMapper;
import com.zhihaoscm.service.core.service.AssignService;
import com.zhihaoscm.service.core.service.FileService;
import com.zhihaoscm.service.core.service.ProductGroupPurchaseService;
import com.zhihaoscm.service.core.service.ProductTypeService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

/**
 * <p>
 * 团购商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Service
public class ProductGroupPurchaseServiceImpl extends
		MpStringIdBaseServiceImpl<ProductGroupPurchase, ProductGroupPurchaseMapper>
		implements ProductGroupPurchaseService {
	@Autowired
	private UserService userService;
	@Autowired
	private AssignService assignService;
	@Autowired
	private FileService fileService;
	@Autowired
	private ProductTypeService productTypeService;

	public ProductGroupPurchaseServiceImpl(
			ProductGroupPurchaseMapper repository) {
		super(repository);
	}

	@Override
	public Page<ProductConsignmentVo> paging(Integer page, Integer size,
			String keyword, String productTypeId, Long supplierId,
			List<Integer> groupPurchaseState, Integer state,
			List<Integer> publishState, String sortKey, String sortOrder) {
		LambdaQueryWrapper<ProductGroupPurchase> queryWrapper = Wrappers
				.lambdaQuery(ProductGroupPurchase.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.and(StringUtils.isNotBlank(keyword),
				wrapper -> wrapper.eq(ProductGroupPurchase::getId, keyword).or()
						.like(ProductGroupPurchase::getTitle, keyword));
		queryWrapper
				.eq(StringUtils.isNotBlank(productTypeId),
						ProductGroupPurchase::getProductTypeId, productTypeId)
				.eq(Objects.nonNull(supplierId),
						ProductGroupPurchase::getSupplierId, supplierId)
				.in(CollectionUtils.isNotEmpty(groupPurchaseState),
						ProductGroupPurchase::getGroupPurchaseState,
						groupPurchaseState)
				.eq(Objects.nonNull(state), ProductGroupPurchase::getState,
						state)
				.in(CollectionUtils.isNotEmpty(publishState),
						ProductGroupPurchase::getPublishState, publishState);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 先按发布状态待审核1、未通过2、已发布3、已下架4、已关闭5在按更新时间倒序
			queryWrapper.orderByAsc(ProductGroupPurchase::getPublishState)
					.orderByDesc(ProductGroupPurchase::getUpdatedTime);
		}
		Page<ProductGroupPurchase> paging = repository
				.selectPage(new Page<>(page, size), queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords(), null));
	}

	@Override
	public Optional<ProductConsignmentVo> findVoById(String id, Long customId) {
		return super.findOne(id)
				.map(resource -> this.packVo(resource, customId));
	}

	@FileId
	@Override
	public ProductGroupPurchase create(ProductGroupPurchase product,
			@FileId List<Long> activeFileIds) {
		// 设置id
		product.setId(AutoCodeDef.CREATE_AUTO_CODE.apply(
				SpringUtil.getBean(StringRedisClient.class),
				AutoCodeDef.BusinessCode.PRODUCT_GROUP_PURCHASE_PREFIX.getCode()
						+ product.getProductTypeId(),
				RedisKeys.Cache.PRODUCT_GROUP_PURCHASE_CODE_GENERATOR,
				StringUtils.EMPTY, 5, AutoCodeDef.DATE_TYPE.yyMM));
		// 填充信息
		this.fillInfo(product);
		// 新增或编辑之后的状态是 下架+待审核
		product.setState(ProductGroupPurchaseDef.State.DOWN_SHELF.getCode());
		product.setPublishState(
				ProductGroupPurchaseDef.PublishState.NOT_AUDIT.getCode());
		return super.create(product);
	}

	@FileId(type = 2)
	@Override
	public ProductGroupPurchase update(ProductGroupPurchase product,
			@FileId List<Long> activeFileIds,
			@FileId(type = 2) List<Long> unActiveFileIds) {
		// 填充信息
		this.fillInfo(product);
		// 新增或编辑之后的状态是 下架+待审核
		product.setState(ProductGroupPurchaseDef.State.DOWN_SHELF.getCode());
		product.setPublishState(
				ProductGroupPurchaseDef.PublishState.NOT_AUDIT.getCode());
		return super.updateAllProperties(product);
	}

	@Override
	public void audit(ProductGroupPurchase product) {
		if (ProductGroupPurchaseDef.State.DOWN_SHELF.match(product.getState())
				&& ProductGroupPurchaseDef.PublishState.DOWN_SHELF
						.match(product.getPublishState())) {
			// todo已发布拼团中商品可下架，下架后拼团进度重置，用户订单全部取消
		}
		super.updateAllProperties(product);
	}

	@Override
	public void downShelf(ProductGroupPurchase product) {
		// 状态为 下架+待审核
		product.setState(ProductGroupPurchaseDef.State.DOWN_SHELF.getCode());
		product.setPublishState(
				ProductGroupPurchaseDef.PublishState.NOT_AUDIT.getCode());
		super.updateAllProperties(product);
	}

	@FileId(type = 3)
	@Override
	public void close(ProductGroupPurchase product,
			@FileId(type = 2) List<Long> unActiveFileIds) {
		// 状态为 下架+关闭
		product.setState(ProductGroupPurchaseDef.State.DOWN_SHELF.getCode());
		product.setPublishState(
				ProductGroupPurchaseDef.PublishState.CLOSE.getCode());
		super.updateAllProperties(product);
	}

	@Override
	public void assign(String id, Long handlerId) {
		// 获取专员信息
		Optional<User> userOp = userService.findOne(handlerId);
		// 将专员回写到商品表中
		this.findOne(id).ifPresent(product -> {
			product.setHandlerId(handlerId);
			userOp.ifPresent(user -> product.setHandlerName(user.getName()));
			this.updateAllProperties(product);
			// 插入记录到指派表
			Assign assign = new Assign();
			assign.setCorrelationId(Long.valueOf(id));
			assign.setUserId(handlerId);
			assign.setType(AssignDef.Type.PRODUCT_GROUP_PURCHASE.getCode());
			assignService.create(assign);
		});

	}

	@Override
	public List<ServiceSpecialVo> findServiceSpecials(String name) {
		// 查询拥有指定角色的账号
		List<User> attaches = userService
				.findUsersByPermission(AdminPermissionDef.PRODUCT_DEAL, name);
		if (CollectionUtils.isEmpty(attaches)) {
			return List.of();
		}
		// 跟进就是他负责的全部商品
		LambdaQueryWrapper<ProductGroupPurchase> wrapper = Wrappers
				.lambdaQuery(ProductGroupPurchase.class)
				.eq(ProductGroupPurchase::getDel,
						CommonDef.Symbol.NO.getCode());
		List<ProductGroupPurchase> products = repository.selectList(wrapper);
		// 过滤出HandlerId为空值的
		List<ProductGroupPurchase> productsList = products.stream()
				.filter(shippingRequirementPlat -> Objects
						.nonNull(shippingRequirementPlat.getHandlerId()))
				.toList();
		// 按人分组
		Map<Long, Long> productMap = productsList.stream()
				.collect(Collectors.groupingBy(
						ProductGroupPurchase::getHandlerId,
						Collectors.counting()));
		// 组装vo
		return attaches.stream().map(user -> {
			ServiceSpecialVo vo = new ServiceSpecialVo();
			vo.setUser(user);
			vo.setProductCount(productMap.getOrDefault(user.getId(), 0L));
			return vo;
		}).sorted(Comparator.comparingLong(ServiceSpecialVo::getProductCount)
				.reversed()).toList();
	}

	/**
	 * 填充信息
	 */
	private void fillInfo(ProductGroupPurchase product) {
		// todo 设置供应商信息
		// 设置品类信息
		productTypeService.findOne(product.getProductTypeId())
				.ifPresent(productType -> {
					product.setProductTypeName(productType.getName());
					product.setArea(productType.getArea());
				});
		// 设置商品专员信息
		userService.findOne(product.getHandlerId())
				.ifPresent(user -> product.setHandlerName(user.getName()));
	}

	/**
	 * 组装vo
	 */
	private ProductConsignmentVo packVo(ProductGroupPurchase product,
			Long customId) {
		ProductConsignmentVo vo = new ProductConsignmentVo();
		vo.setProductGroupPurchase(product);

		if (Objects.nonNull(product.getImgMainId())) {
			// 设置主图
			vo.setMainFile(
					fileService.findOne(product.getImgMainId()).orElse(null));
		}
		if (Objects.nonNull(product.getVideoId())) {
			// 设置视频
			vo.setVideoFile(
					fileService.findOne(product.getVideoId()).orElse(null));
		}
		if (CollectionUtils.isNotEmpty(product.getImgIds())) {
			// 设置非主图文件
			vo.setOtherImgFile(fileService.findByIds(product.getImgIds()));
		}
		if (StringUtils.isNotBlank(product.getProductTypeId())) {
			vo.setProductTypeVo(productTypeService
					.findVoById(product.getProductTypeId()).orElse(null));
			vo.setProductType(productTypeService
					.findOne(product.getProductTypeId()).orElse(null));
		}
		if (Objects.nonNull(customId)) {
			// todo根据customId查询是否拼团和拼团吨数
			return vo;
		}
		return vo;
	}

	/**
	 * 组装vos
	 */
	private List<ProductConsignmentVo> packVo(
			List<ProductGroupPurchase> records, Long customId) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		// 主图文件id集合
		Set<Long> fileIds = new HashSet<>();
		records.forEach(record -> fileIds.add(record.getImgMainId()));
		Map<Long, File> idFileMap = fileService
				.getIdMap(new ArrayList<>(fileIds));

		// todo 根据customId查询是否拼团和拼团吨数

		return records.stream().map(record -> {
			ProductConsignmentVo vo = new ProductConsignmentVo();
			vo.setProductGroupPurchase(record);
			// 设置主图
			vo.setMainFile(idFileMap.get(record.getImgMainId()));
			if (StringUtils.isNotBlank(record.getProductTypeId())) {
				productTypeService.findVoById(record.getProductTypeId())
						.ifPresent(vo::setProductTypeVo);
				productTypeService.findOne(record.getProductTypeId())
						.ifPresent(vo::setProductType);

			}
			return vo;
		}).toList();
	}
}
