package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.ReceivePayment;
import com.zhihaoscm.domain.bean.vo.ReceivePaymentVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AutoCodeDef;
import com.zhihaoscm.domain.meta.biz.ReceivePaymentDef;
import com.zhihaoscm.service.core.mapper.ReceivePaymentMapper;
import com.zhihaoscm.service.core.service.ReceivePaymentService;

/**
 * <p>
 * 收款信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Service
public class ReceivePaymentServiceImpl
		extends MpStringIdBaseServiceImpl<ReceivePayment, ReceivePaymentMapper>
		implements ReceivePaymentService {

	public ReceivePaymentServiceImpl(ReceivePaymentMapper repository) {
		super(repository);
	}

	@Autowired
	private StringRedisClient redisClient;

	@Override
	public Page<ReceivePayment> paging(Integer page, Integer size, String id,
			String orderId, List<Integer> states, List<Integer> paymentTypes,
			LocalDateTime beginTime, LocalDateTime endTime, String sortKey,
			String sortOrder, String keyword) {
		LambdaQueryWrapper<ReceivePayment> wrapper = Wrappers
				.lambdaQuery(ReceivePayment.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(id), ReceivePayment::getId, id);
		wrapper.eq(Objects.nonNull(orderId), ReceivePayment::getOrderId,
				orderId);
		wrapper.apply(StringUtils.isNotBlank(keyword),
				"(customer_enterprise -> '$.institutionName' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_enterprise -> '$.realName' LIKE CONCAT('%',{0},'%') "
						+ "OR customer_enterprise -> '$.mobile' LIKE CONCAT('%',{0},'%'))",
				keyword);
		wrapper.in(CollectionUtils.isNotEmpty(states), ReceivePayment::getState,
				states);
		wrapper.in(CollectionUtils.isNotEmpty(paymentTypes),
				ReceivePayment::getPaymentType, paymentTypes);
		wrapper.ge(Objects.nonNull(beginTime), ReceivePayment::getCreatedTime,
				beginTime);
		wrapper.le(Objects.nonNull(endTime), ReceivePayment::getCreatedTime,
				endTime);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("ORDER BY " + sortKey + " " + sortOrder);
		} else {
			wrapper.orderByAsc(ReceivePayment::getState);
			wrapper.orderByDesc(ReceivePayment::getCreatedTime);
		}
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Optional<ReceivePaymentVo> findAmount() {
		List<ReceivePayment> receivePaymentList = super.findAll();
		BigDecimal pendingPaymentAmount = receivePaymentList.stream()
				.filter(item -> ReceivePaymentDef.State.PENDING_PAYMENT
						.match(item.getPaymentType()))
				.map(ReceivePayment::getAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		BigDecimal paidAmount = receivePaymentList.stream()
				.filter(item -> ReceivePaymentDef.State.PAID
						.match(item.getPaymentType()))
				.map(ReceivePayment::getAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		ReceivePaymentVo receivePaymentVo = new ReceivePaymentVo();
		receivePaymentVo.setPendingPaymentAmount(pendingPaymentAmount);
		receivePaymentVo.setPaidAmount(paidAmount);
		receivePaymentVo
				.setReceivePaymentAmount(pendingPaymentAmount.add(paidAmount));
		return Optional.of(receivePaymentVo);
	}

	@Override
	public List<ReceivePayment> findByOrderIds(List<String> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return List.of();
		}
		LambdaQueryWrapper<ReceivePayment> wrapper = Wrappers
				.lambdaQuery(ReceivePayment.class);
		this.filterDeleted(wrapper);
		wrapper.in(ReceivePayment::getOrderId, orderIds);
		wrapper.orderByAsc(ReceivePayment::getState);
		wrapper.orderByDesc(ReceivePayment::getCreatedTime);
		return repository.selectList(wrapper);
	}

	@Override
	public ReceivePayment create(ReceivePayment resource) {
		resource.setId(AutoCodeDef.CREATE_AUTO_CODE.apply(redisClient,
				AutoCodeDef.BusinessCode.RECEIVE_PAYMENT_PREFIX.getCode(),
				RedisKeys.Cache.RECEIVE_PAYMENT_GENERATOR,
				"0" + resource.getPaymentType(), 5,
				AutoCodeDef.DATE_TYPE.yyMM));
		// todo 发送mq消息队列
		return super.create(resource);
	}

	@Override
	public ReceivePayment updateAllProperties(ReceivePayment resource) {
		// todo 发送mq消息队列
		return super.updateAllProperties(resource);
	}

	@Override
	public void cancel(ReceivePayment receivePayment) {
		receivePayment.setState(ReceivePaymentDef.State.UNPAID.getCode());
		super.updateAllProperties(receivePayment);
	}
}
