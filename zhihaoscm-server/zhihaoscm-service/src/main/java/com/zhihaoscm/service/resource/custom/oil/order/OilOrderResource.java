package com.zhihaoscm.service.resource.custom.oil.order;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.entity.OilOrder;
import com.zhihaoscm.domain.bean.vo.OilOrderVo;
import com.zhihaoscm.domain.meta.biz.HistoryDef;
import com.zhihaoscm.domain.meta.biz.OilOrderDef;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.OilOrderService;
import com.zhihaoscm.service.resource.form.oil.order.OilOrderBookingForm;
import com.zhihaoscm.service.resource.form.oil.order.OilOrderConfirmForm;
import com.zhihaoscm.service.resource.validator.oil.order.OilOrderValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 油品订单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Tag(name = "油品订单", description = "油品订单API")
@RestController
@RequestMapping("/oil-order")
public class OilOrderResource {

	@Autowired
	private OilOrderService service;
	@Autowired
	private OilOrderValidator validator;

	@Operation(summary = "分页查询油品订单列表")
	@GetMapping(value = "/custom-paging")
	public ApiResponse<Page<OilOrderVo>> customPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "状态:1.预约中、2.进行中、3.已完成、4.已取消") @RequestParam(required = false) Integer state) {

		return new ApiResponse<>(PageUtil.convert(
				service.customPaging(page, size, state, CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount().getId())));
	}

	@Operation(summary = "查询油品订单详情")
	@GetMapping("/vo/{id}")
	public ApiResponse<OilOrderVo> findVoById(@PathVariable String id,
			@Parameter(description = "当前经度") @RequestParam(required = false) Double lon,
			@Parameter(description = "当前纬度") @RequestParam(required = false) Double lat) {
		return new ApiResponse<>(service.findVoById(id, lon, lat).orElse(null));
	}

	@Operation(summary = "最新的预约中、进行中油品订单")
	@GetMapping("/last")
	public ApiResponse<OilOrderVo> findLast() {
		return new ApiResponse<>(service
				.findLast(
						CustomerContextHolder.getCustomerLoginVo()
								.getProxyAccount().getId(),
						List.of(OilOrderDef.State.BOOKING.getCode(),
								OilOrderDef.State.IN_PROGRESS.getCode()))
				.orElse(null));
	}

	@Operation(summary = "确认油品订单（同意加油协议）")
	@PutMapping(value = "/agree-refueling/{id}")
	public ApiResponse<Void> agreeRefueling(@PathVariable String id) {
		OilOrder oilOrder = validator.validateAgree(id);
		service.agree(oilOrder);
		return new ApiResponse<>();
	}

	@Operation(summary = "预约加油")
	@PostMapping("/booking-refueling")
	public ApiResponse<Void> bookingRefueling(
			@Validated @RequestBody OilOrderBookingForm form) {
		OilOrder oilOrder = validator.validateBooking(form);
		service.booking(oilOrder);
		return new ApiResponse<>();
	}

	@Operation(summary = "确认加油")
	@PutMapping(value = "/confirm-refueling/{id}")
	@History(success = HistoryDef.OIL_ORDER_CONFIRM, isSaveIdentity = true, identity = HistoryDef.CUSTOM, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_OIL_ORDER)
	public ApiResponse<Void> confirmRefueling(@PathVariable String id,
			@Validated @RequestBody OilOrderConfirmForm form) {
		OilOrder oilOrder = validator.validateConfirmRefueling(id,
				CommonDef.Symbol.NO.getCode());
		service.confirmRefueling(form.convertToEntity(oilOrder));
		return new ApiResponse<>();
	}

	@Operation(summary = "取消订单")
	@PutMapping(value = "/cancel/{id}")
	@History(success = HistoryDef.OIL_ORDER_CANCEL, isSaveIdentity = true, identity = HistoryDef.CUSTOM, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_OIL_ORDER)
	public ApiResponse<Void> cancel(@PathVariable String id) {
		validator.validateCancel(id);
		service.cancel(id);
		return new ApiResponse<>();
	}
}
