package com.zhihaoscm.service.resource.validator.shipping.plat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementAcceptDef;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementCustomerDef;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementPlatDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderShipDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.UserService;
import com.zhihaoscm.service.resource.form.shipping.customer.CustomShippingRequirementCustomerForm;
import com.zhihaoscm.service.resource.form.shipping.plat.ShippingRequirementPlatDemandLevelForm;
import com.zhihaoscm.service.resource.form.shipping.plat.ShippingRequirementPlatForm;
import com.zhihaoscm.service.resource.form.shipping.plat.ShippingRequirementPlatInviteForm;

@Component
public class ShippingRequirementPlatValidator {

	@Autowired
	private TransportOrderShipService transportOrderShipService;
	@Autowired
	private ShippingRequirementPlatService shippingRequirementPlatService;
	@Autowired
	private ShippingRequirementAcceptService shippingRequirementAcceptService;
	@Autowired
	private ShipRecommendService shipRecommendService;
	@Autowired
	private ShipService shipService;

	@Autowired
	private UserService userValidator;

	/**
	 * 验证是否存在
	 *
	 * @param id
	 * @return
	 */
	public ShippingRequirementPlat validateExist(String id) {
		ShippingRequirementPlat plat = shippingRequirementPlatService
				.findOne(id).orElse(null);
		Assert.notNull(plat, ErrorCode.CODE_30128088);
		return plat;
	}

	/**
	 * 验证新增
	 *
	 * @param form
	 */
	public void validateCreate(ShippingRequirementPlatForm form) {
		// 验证是否包海事字段格式正确性
		ShippingRequirementCustomerDef.MaritimeAffairsFee
				.from(form.getMaritimeAffairsFee());
		// 校验船运定金支付方式是否合法
		TransportOrderShipDef.DepositPayType.from(form.getPayType());
		switch (ShippingRequirementPlatDef.ElectricContactNumberType
				.from(form.getElectricContactNumberType())) {
			case PHONE -> {
				String regex = "^1[3-9]\\d{9}$";
				if (!form.getElectricContactNumber().matches(regex)) {
					throw new BadRequestException(ErrorCode.CODE_30128101);
				}
			}
			case LANDLINE -> {
				String regex = "^(0\\d{2,3}-)?\\d{7,8}$";
				if (!form.getElectricContactNumber().matches(regex)) {
					throw new BadRequestException(ErrorCode.CODE_30128101);
				}
			}
		}
	}

	/**
	 * 验证修改
	 */
	public ShippingRequirementPlat validateUpdate(String id,
			ShippingRequirementPlatForm form) {
		ShippingRequirementPlat plat = this.validateExist(id);
		// 需求状态已发布+没有报价信息（无论报价成功还是失败）
		// 或者需求状态为待发布
		if (!ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
				.match(plat.getState())
				&& !ShippingRequirementPlatDef.State.TO_BE_PUBLISH
						.match(plat.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30128109);
		}
		List<ShippingRequirementAccept> byPlatId = shippingRequirementAcceptService
				.findByPlatId(plat.getId());
		if (CollectionUtils.isNotEmpty(byPlatId)) {
			throw new BadRequestException(ErrorCode.CODE_30128109);
		}
		this.validateCreate(form);
		// 修改需要为此需求的船务专员
		User user = Objects.requireNonNull(UserContextHolder.getUser());
		Long currentUserId = user.getId();
		if (!Objects.equals(currentUserId, plat.getHandlerId())) {
			throw new BadRequestException(ErrorCode.CODE_30128110);
		}
		return plat;
	}

	/**
	 * 验证货主新增船运需求
	 *
	 * @param form
	 */
	public void validateOwnerCreate(
			CustomShippingRequirementCustomerForm form) {
		if (Objects.nonNull(form.getOrderType())
				&& ShippingRequirementPlatDef.State.TO_BE_PUBLISH
						.match(form.getOrderType())) {
			// 委托平台下单只需要：船型、排水槽、始发地、目的地、期望装载日期、货物类型、运货吨数
			// PC的委托，船型和排水槽是不填
			if (ShippingRequirementPlatDef.OperatingPlatform.PC
					.match(form.getOperatingPlatform())) {
				return;
			} else {
				// operatingPlatform为空，默认为移动端
				// 小程序和app的委托，船型和排水槽是必填。
				// 船型
				if (Objects.isNull(form.getShipType())) {
					throw new BadRequestException(ErrorCode.CODE_30134013);
				}
				// 排水槽
				if (Objects.isNull(form.getDrainageChannel())) {
					throw new BadRequestException(ErrorCode.CODE_30134014);
				}
				return;
			}
		}
		// 如果是自主需要校验字段必填与正确性
		// 船型
		if (Objects.isNull(form.getShipType())) {
			throw new BadRequestException(ErrorCode.CODE_30134013);
		}
		// 排水槽
		if (Objects.isNull(form.getDrainageChannel())) {
			throw new BadRequestException(ErrorCode.CODE_30134014);
		}
		// 意向单价
		if (Objects.isNull(form.getUnitPrice())) {
			throw new BadRequestException(ErrorCode.CODE_30134019);
		}
		// 宽限天数
		if (Objects.isNull(form.getLoadDays())) {
			throw new BadRequestException(ErrorCode.CODE_30134024);
		}
		// 装卸天数
		if (Objects.isNull(form.getLoadUnloadDays())) {
			throw new BadRequestException(ErrorCode.CODE_30134025);
		}
		// 海事费用
		if (Objects.isNull(form.getMaritimeAffairsFee())) {
			throw new BadRequestException(ErrorCode.CODE_30134027);
		}
		// 船运定金
		if (Objects.isNull(form.getDeposit())) {
			throw new BadRequestException(ErrorCode.CODE_30134009);
		}
		// 联系人
		if (Objects.isNull(form.getContact())) {
			throw new BadRequestException(ErrorCode.CODE_30128035);
		}
		// 联系电话
		if (Objects.isNull(form.getMobile())) {
			throw new BadRequestException(ErrorCode.CODE_30134031);
		}
		// 验证海事费用字段格式正确性
		ShippingRequirementCustomerDef.MaritimeAffairsFee
				.from(form.getMaritimeAffairsFee());
		if (Objects.nonNull(form.getDwtMin())
				&& Objects.nonNull(form.getDwtMax())) {
			// 验证最大吨位必须大于等于最小吨位
			if (form.getDwtMax().compareTo(form.getDwtMin()) < 0) {
				throw new BadRequestException(ErrorCode.CODE_30128113);
			}
		}
		// 验证装载日期宽限天数不能为负数
		if (form.getLoadDays() < 0) {
			throw new BadRequestException(ErrorCode.CODE_30128114);
		}
		// 验证装卸天数不能为负数
		if (form.getLoadUnloadDays() < 0) {
			throw new BadRequestException(ErrorCode.CODE_30128115);
		}
		// 校验船运定金
		if (Objects.nonNull(form.getDeposit())
				&& (form.getDeposit().compareTo(BigDecimal.ZERO) < 0
						|| form.getDeposit()
								.compareTo(new BigDecimal("**********")) > 0)) {
			throw new BadRequestException(ErrorCode.CODE_30128116);
		}

		if (ShippingRequirementCustomerDef.DepositPaymentMethod.PAY_ZH
				.match(form.getDepositPaymentMethod())) {
			Customer customer = CustomerContextHolder.getCustomerLoginVo()
					.getProxyAccount();
			if (StringUtils.isBlank(customer.getRealName())) {
				throw new BadRequestException(ErrorCode.CODE_30128117);
			}
		}
		// 校验船舶类型
		if (Objects.isNull(ShippingRequirementCustomerDef.ShipType
				.from(form.getShipType()))) {
			throw new BadRequestException(ErrorCode.CODE_30134034);
		}

	}

	/**
	 * 验证指派
	 *
	 * @param id
	 * @param handlerId
	 */
	public void validateAssign(String id, Long handlerId) {
		// 验证平台船运需求是否存在
		ShippingRequirementPlat plat = this.validateExist(id);
		// 验证操作专员是否存在
		userValidator.validateIsExist(handlerId);
		// 验证当前状态是否允许指派
		if (!ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
				.match(plat.getState())
				&& !ShippingRequirementPlatDef.State.TO_BE_PUBLISH
						.match(plat.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30128086);
		}
	}

	/**
	 * 验证冈好运删除船运需求
	 *
	 * @param id
	 */
	public void validateComplete(String id) {
		// 验证平台船运需求是否存在
		ShippingRequirementPlat plat = this.validateExist(id);
		// 验证当前状态是否允许结束
		if (!ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
				.match(plat.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30128086);
		}
	}

	/**
	 * 验证关闭
	 *
	 * @param id
	 */
	public void validateClose(String id) {
		// 验证平台船运需求是否存在
		ShippingRequirementPlat plat = this.validateExist(id);
		// 验证当前状态是否允许关闭
		if (!ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
				.match(plat.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30128087);
		}
		// 有关联船运单的已发布船运需求不能关闭；
		List<TransportOrderShip> orderShips = transportOrderShipService
				.findBySrpId(plat.getId());
		if (!orderShips.isEmpty()) {
			throw new BadRequestException(ErrorCode.CODE_30128089);
		}
	}

	/**
	 * 验证邀请船舶
	 *
	 * @param form
	 * @return
	 */
	public List<String> validateInvite(ShippingRequirementPlatInviteForm form) {
		List<String> result = new ArrayList<>();
		ShippingRequirementPlat shippingRequirementPlat = this
				.validateExist(form.getPlatId());
		if (!ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
				.match(shippingRequirementPlat.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30128096);
		}
		List<String> shipIds = form.getShipIds().stream().distinct().toList();
		// 过滤掉已经邀请过的船舶(当天)
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime beginTime = now.with(LocalTime.MIN);
		LocalDateTime endTime = now.with(LocalTime.MAX).withNano(0);
		for (String shipId : shipIds) {
			if (shipService.findOne(shipId).isEmpty()) {
				throw new BadRequestException(ErrorCode.CODE_30120024);
			}
			if (shipRecommendService
					.findByPlatIdAndShipId(null, shipId, beginTime, endTime)
					.isEmpty()) {
				result.add(shipId);
			}
		}

		return result;
	}

	/**
	 * 验证修改船运需求的需求等级
	 *
	 * @param form
	 * @return
	 */
	public List<String> validateUpdateDemandLevel(
			ShippingRequirementPlatDemandLevelForm form) {
		List<String> result = new ArrayList<>();

		List<ShippingRequirementPlat> platList = shippingRequirementPlatService
				.findByIdsNoDeleted(form.getPlatIds());

		if (CollectionUtils.isNotEmpty(platList)) {
			// 修改需求等级，跟报价状态没关系，就是已发布都可以
			// 修改等级可以修改三方需求的这个不影响她们冈好运
			result = platList.stream().filter(
					p -> ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
							.match(p.getState()))
					.map(ShippingRequirementPlat::getId).distinct().toList();
		}

		if (CollectionUtils.isEmpty(result)) {
			throw new BadRequestException(ErrorCode.CODE_30128108);
		}
		return result;
	}

	/**
	 * 验证货主取消船运需求的请求是否合法
	 *
	 * @param id
	 */
	public void validateOwnerCancel(String id) {
		// 判断该船运需求是否已经有抢单成功的 有抢单成功的不能取消
		ShippingRequirementPlat plat = this.validateExist(id);

		List<ShippingRequirementAccept> acceptList = shippingRequirementAcceptService
				.findByPlatId(plat.getId());
		if (CollectionUtils.isNotEmpty(acceptList)) {
			for (ShippingRequirementAccept shippingRequirementAccept : acceptList) {
				if (ShippingRequirementAcceptDef.CarrierState.SUCCESSFUL_ORDER_ACCEPTANCE
						.match(shippingRequirementAccept.getState())) {
					throw new BadRequestException(ErrorCode.CODE_30128111);
				}
			}
		}

		// 判断当前状态是否是找船中(管理后台已发布的船运需求) 否则不允许取消
		// 处理中可以取消找船（管理后台待发布的船运需求）
		if (!ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
				.match(plat.getState())
				&& !ShippingRequirementPlatDef.State.TO_BE_PUBLISH
						.match(plat.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30128112);
		}
	}

	/**
	 * 验证发布
	 *
	 * @param id
	 */
	public ShippingRequirementPlat validatePublish(String id) {
		// 验证平台船运需求是否存在
		ShippingRequirementPlat plat = this.validateExist(id);
		// 验证当前状态是否允许发布
		if (!ShippingRequirementPlatDef.State.TO_BE_PUBLISH
				.match(plat.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30128102);
		}
		return plat;
	}
}
