package com.zhihaoscm.service.resource.validator.ocr;

import java.time.LocalDate;
import java.util.Objects;

import org.springframework.stereotype.Component;

import com.zhihaoscm.aliyun.ocr.sdk.res.IdCardBackInfo;
import com.zhihaoscm.aliyun.ocr.sdk.res.IdCardFaceInfo;
import com.zhihaoscm.aliyun.ocr.sdk.res.IdCardInfo;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.date.DateUtils;
import com.zhihaoscm.domain.meta.biz.OcrRecognizeDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;

@Component
public class OCRValidator {

	/**
	 * 校验身份证信息
	 * 
	 * @param info
	 * @param type
	 *            身份证正反面:1（正面国徽）2（反面人像）
	 */
	public void validateIdCard(IdCardInfo info, Integer type) {
		if (Objects.isNull(info)) {
			throw new BadRequestException(ErrorCode.CODE_30120060);
		}
		// 正面（国徽）
		IdCardBackInfo idCardBackInfo = info.getIdCardBackInfo();
		// 反面（人像）
		IdCardFaceInfo idCardFaceInfo = info.getIdCardFaceInfo();
		// 识别结果为空，则识别失败
		if (Objects.isNull(idCardBackInfo) && Objects.isNull(idCardFaceInfo)) {
			throw new BadRequestException(ErrorCode.CODE_30120060);
		}
		if (OcrRecognizeDef.Type.ID_CARD_FACE.match(type)) {
			// 1（正面国徽）
			if (Objects.isNull(idCardBackInfo)) {
				throw new BadRequestException(ErrorCode.CODE_30120062);
			}
		} else {
			// 2（反面人像）
			if (Objects.isNull(idCardFaceInfo)) {
				throw new BadRequestException(ErrorCode.CODE_30120063);
			}
		}
		// 国徽页不为空，校验有效期
		if (Objects.nonNull(idCardBackInfo)) {
			String validPeriod = idCardBackInfo.getValidPeriod();
			if (StringUtils.isNotBlank(validPeriod)) {
				String[] parts = validPeriod.split("-");
				if (parts.length >= 2) {
					// 第二个部分
					String secondPart = parts[1];
					if (!"长期".equals(secondPart)) {
						LocalDate endDate = DateUtils.convertDate(secondPart,
								"yyyy.M.d");
						if (endDate.isBefore(LocalDate.now())) {
							throw new BadRequestException(
									ErrorCode.CODE_30120059);
						}
					}
				}
			}
		}
	}
}
