package com.zhihaoscm.service.core.service.usercenter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zhihaoscm.domain.bean.entity.CustomerReceivingAddress;
import com.zhihaoscm.service.client.usercenter.CustomerReceivingAddressClient;

@Service
public class CustomerReceivingAddressService {

	@Autowired
	private CustomerReceivingAddressClient client;

	public CustomerReceivingAddress findById(Long id) {
		return client.findById(id);
	}
}
