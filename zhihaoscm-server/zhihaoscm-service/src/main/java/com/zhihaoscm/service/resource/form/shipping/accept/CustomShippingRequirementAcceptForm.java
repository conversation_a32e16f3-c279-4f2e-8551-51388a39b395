package com.zhihaoscm.service.resource.form.shipping.accept;

import java.util.Objects;

import org.springframework.beans.BeanUtils;

import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerBank;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementAccept;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.json.ArrayCarrierShip;
import com.zhihaoscm.domain.bean.json.CaptainEnterprise;
import com.zhihaoscm.domain.bean.json.CustomerBankInfo;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementAcceptDef;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementPlatDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.ShippingRequirementPlatService;
import com.zhihaoscm.service.core.service.usercenter.CustomerBankService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "ShippingRequirementAcceptForm", title = "承运商船运需求新增表单对象")
public class CustomShippingRequirementAcceptForm {

	@Schema(title = "平台船运需求ID")
	@NotNull(message = ErrorCode.CODE_30128083)
	private String requirementPlatId;

	@Schema(title = "承运船相关信息：包括船舶id，船舶名称，运输单价，运输吨数")
	@NotEmpty(message = ErrorCode.CODE_30128084)
	private ArrayCarrierShip shipInfos;

	@Schema(title = "补充约定")
	private String replenishAppoint;

	@Schema(title = "联系人")
	@NotBlank(message = ErrorCode.CODE_30128035)
	private String realName;

	@Schema(title = "手机号")
	@NotBlank(message = ErrorCode.CODE_30128037)
	private String mobile;

	@Schema(title = "收款银行账户id")
	private Long captainBankId;

	public ShippingRequirementAccept convertToEntity(Customer customer,
			CustomerService customerService,
			CustomerBankService customerBankClient,
			ShippingRequirementPlatService platClient) {
		ShippingRequirementAccept shippingAccept = new ShippingRequirementAccept();
		ShippingRequirementAccept shipping = this.convertToEntity(null,
				customerBankClient, shippingAccept);
		// 设置承运商的id及公司信息
		shipping.setCaptainId(customer.getId());
		shipping.setCaptainEnterprise(
				this.assembleEnterprise(customer, customerService));
		ShippingRequirementPlat plat = platClient
				.findOne(this.requirementPlatId).orElse(null);
		if (Objects.nonNull(plat)) {
			shipping.setSourcePortId(plat.getSourcePortId());
			shipping.setSourcePortName(plat.getSourcePortName());
			shipping.setDestinationPortId(plat.getDestinationPortId());
			shipping.setDestinationPortName(plat.getDestinationPortName());
			shipping.setShipRouteId(plat.getShipRouteId());
			shipping.setShipRouteSource(plat.getShipRouteSource());
			shipping.setShipRouteDestination(plat.getShipRouteDestination());
		}
		return shipping;
	}

	public ShippingRequirementAccept convertToEntity(@Nullable Long id,
			CustomerBankService customerBankClient,
			ShippingRequirementAccept shipping) {
		shipping.setId(id);
		shipping.setRequirementPlatId(this.requirementPlatId);
		shipping.setShipInfos(this.shipInfos);
		shipping.setCaptainBankId(this.captainBankId);
		// 银行信息
		if (Objects.nonNull(this.captainBankId)) {
			CustomerBank customerBank = customerBankClient
					.findOne(captainBankId).orElse(null);
			CustomerBankInfo customerBankInfo = new CustomerBankInfo();
			if (Objects.nonNull(customerBank)) {
				BeanUtils.copyProperties(customerBank, customerBankInfo);
			}
			shipping.setCaptainBankInfo(customerBankInfo);
		}
		shipping.setReplenishAppoint(this.replenishAppoint);
		shipping.setState(
				ShippingRequirementAcceptDef.CarrierState.PROCESSING.getCode());
		shipping.setRejectOrigin(null);
		shipping.setRejectReason(null);
		CaptainEnterprise enterprise = shipping.getCaptainEnterprise();
		if (Objects.nonNull(enterprise)) {
			enterprise.setRealName(this.realName);
			enterprise.setMobile(this.mobile);
		} else {
			CaptainEnterprise enterprise1 = new CaptainEnterprise();
			enterprise1.setRealName(this.realName);
			enterprise1.setMobile(this.mobile);
			shipping.setCaptainEnterprise(enterprise1);
		}
		// 设置数据来源为平台
		shipping.setDataSource(
				ShippingRequirementPlatDef.DataSource.INNER.getCode());
		return shipping;
	}

	/**
	 * 组装承运商企业信息
	 *
	 * @param customer
	 * @param customerService
	 * @return
	 */
	private CaptainEnterprise assembleEnterprise(Customer customer,
			CustomerService customerService) {
		CaptainEnterprise enterprise = new CaptainEnterprise();
		if (Objects.nonNull(customer)) {
			customerService.findOne(customer.getId()).ifPresent(c -> {
				enterprise.setName(c.getInstitutionName());
				enterprise.setUnifiedSocialCreditCode(
						c.getUnifiedSocialCreditCode());
				enterprise.setLegalRepresentative(c.getLegalRepresentative());
			});
		}
		// 前端填写的联系人和手机号
		if (Objects.nonNull(this.getRealName())) {
			enterprise.setRealName(this.realName);
		}
		if (Objects.nonNull(this.getMobile())) {
			enterprise.setMobile(this.mobile);
		}
		return enterprise;
	}

}
