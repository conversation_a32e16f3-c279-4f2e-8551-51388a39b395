package com.zhihaoscm.service.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@Configuration
@ConfigurationProperties(prefix = SMSProperties.PREFIX)
@Data
public class SMSProperties {

	final static String PREFIX = "aliyun.sms";

	// 等待用户确认 模板code
	private String waitUserConfirmCode;

	// 余额不足提醒 模板code
	private String waitPaymentCode;

	// 确认收款提醒 模板code
	private String collectionConfirmationCode;

	// 收款驳回提醒 模板code
	private String collectionRejectCode;

	// 提货新增提醒 模板code
	private String createPurchaseGoodsCode;

	// 货主收到船运单提醒 模板code
	private String receiveShippingOrderCode;

	// 船主收到船运单提醒 模板code
	private String captainReceiveShippingOrderCode;

	// 收到船运单提醒 生成船运单的时候，如果关联了货主船运需求 模板code 模板code
	private String receiveShippingOrderCode2;

	// 接单失败提醒 模板code
	private String orderFailedCode;

	// 会员续费提醒
	private String memberRenewalReminder;

	// 货主支付船务信息服务费提醒
	private String ownerPayShippingServiceFeeCode;
	// 船主支付船务信息服务费提醒
	private String captainPayShippingServiceFeeCode;
	// 被添加子账号提醒
	private String subAccountConfirmCode;
	// 付款作废确认提醒
	private String paymentAbortConfirmCode;
	// 平台邀请承运商抢单提醒(有船主)
	private String platformTransferShipRequireCode;
	// 平台邀请承运商抢单提醒(无船主)
	private String platformTransferShipRequireCode2;
	// 后台已完成报计划，如果船主没有支付油费，提醒船主去支付油费
	private String carrierPayOilFeeCode;
	// 船主支付了油费且报计划了，提醒船主去确认加油
	private String carrierConfirmRefuelingCode;
	/** 意向详情 */
	private String intendDetailPage;
	/** 付款详情 */
	private String paymentDetailPage;
	/** 收款详情 */
	private String receiptDetailPage;
	/** 找船需求详情 */
	private String shipDetailPage;
	/** 船运单详情 */
	private String transportOrderPage;
	/** 承运商船运单详情 */
	private String transferTransportOrderPage;
	/** 承运商抢单详情 */
	private String transferShipRequirePage;
	/** 会员页 */
	private String membershipPage;
	/** 承运商会员页 */
	private String transferMembershipPage;
	/** 受邀请记录详情 */
	private String invitationRecordPage;
	/** 提货单详情 */
	private String purchaseGoodPage;
	/** 项目详情 */
	private String projectPage;
	/** 承运商船运需求详情 */
	private String transferShipRequireDetailPage;
	/** 短信链接小程序 URL Link 失效类型，失效时间：0，失效间隔天数：1 */
	private Integer expireType;
	/** 短信链接失效时间数，默认最长时限30天 */
	private Integer expireNum;
	/** app短信链接 */
	private String appLinkUrl;

}
