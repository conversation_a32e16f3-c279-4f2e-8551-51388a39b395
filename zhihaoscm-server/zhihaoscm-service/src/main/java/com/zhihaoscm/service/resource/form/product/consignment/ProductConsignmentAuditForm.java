package com.zhihaoscm.service.resource.form.product.consignment;

import java.time.LocalDateTime;

import com.zhihaoscm.domain.bean.entity.ProductConsignment;
import com.zhihaoscm.domain.meta.biz.ProductConsignmentDef;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "ProductConsignmentAuditForm", description = "商品审核表单")
public class ProductConsignmentAuditForm {

	@Schema(title = "审核状态 true 同意  false 拒绝")
	private Boolean state;

	@Schema(title = "审核意见")
	private String auditDescn;

	public void update(ProductConsignment product) {

		if (this.getState()) {
			if (ProductConsignmentDef.State.DOWN_SHELF
					.match(product.getState())) {
				product.setState(
						ProductConsignmentDef.State.UP_SHELF.getCode());
				product.setPublishState(
						ProductConsignmentDef.PublishState.PASS.getCode());
			} else {
				product.setState(
						ProductConsignmentDef.State.DOWN_SHELF.getCode());
				product.setPublishState(
						ProductConsignmentDef.PublishState.DOWN_SHELF
								.getCode());
			}
		} else {
			product.setPublishState(
					ProductConsignmentDef.PublishState.FAIL.getCode());
		}
		product.setAuditDescn(this.getAuditDescn());
		product.setAuditTime(LocalDateTime.now());
	}
}
