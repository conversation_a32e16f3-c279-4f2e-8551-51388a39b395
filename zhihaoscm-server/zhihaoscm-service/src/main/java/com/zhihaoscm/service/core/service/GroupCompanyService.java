package com.zhihaoscm.service.core.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.entity.GroupCompany;
import com.zhihaoscm.domain.bean.vo.GroupCompanyVo;

/**
 * <p>
 * 集团管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface GroupCompanyService
		extends MpStringIdBaseService<GroupCompany> {

	/**
	 * 管理后台分页
	 */
	Page<GroupCompanyVo> paging(Integer page, Integer size, String keyword,
			String sortKey, String sortOrder);

	/**
	 * 根据名称查询，排除指定id
	 * 
	 * @param name
	 * @param neId
	 * @return
	 */
	List<GroupCompany> findByNameAndNotInId(String name, String neId);
}
