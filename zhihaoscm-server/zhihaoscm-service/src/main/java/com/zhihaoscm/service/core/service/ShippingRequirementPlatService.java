package com.zhihaoscm.service.core.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.json.GeoPoint;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.vo.*;

/**
 * <p>
 * 平台船运需求 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
public interface ShippingRequirementPlatService
		extends MpStringIdBaseService<ShippingRequirementPlat> {

	/**
	 * 分页查询
	 *
	 * @param destinationPortName
	 * @param sourcePortName
	 * @param page
	 * @param size
	 * @param handlerName
	 * @param state
	 * @param id
	 * @param isFull
	 * @return
	 */
	com.zhihaoscm.common.bean.page.Page<ShippingRequirementPlatVo> paging(
			String destinationPortName, String sourcePortName, Integer page,
			Integer size, String handlerName, Integer demandLevel,
			LocalDate loadDateStart, LocalDate loadDateEnd, List<Integer> state,
			String id, boolean isFull, List<Integer> orderAcceptanceState,
			Integer dataSource, Long sourceAppId, String sortKey,
			String sortOrder);

	/**
	 * 下拉列表
	 *
	 * @param page
	 * @param size
	 * @param platId
	 * @param isFull
	 * @return
	 */
	Page<ShippingRequirementPlat> selector(Integer page, Integer size,
			String platId, boolean isFull);

	/**
	 * 小程序分页查询
	 *
	 * @param page
	 * @param size
	 * @param sourcePortId
	 * @param destinationPortId
	 * @param loadDateStart
	 * @return
	 */
	com.zhihaoscm.common.bean.page.Page<ShippingRequirementPlatVo> customPaging(
			Integer page, Integer size, String platId, Long sourcePortId,
			Long destinationPortId, LocalDate loadDateStart,
			LocalDate loadDateEnd);

	/**
	 * 小程序端 pc端货主运单分页查询
	 *
	 * @param page
	 * @param size
	 * @param states
	 * @param title
	 * @param sourcePortName
	 * @param destinationPortName
	 * @return
	 */
	com.zhihaoscm.common.bean.page.Page<ShippingRequirementCustomerVo> orderPaging(
			Integer page, Integer size, String keyword, List<Integer> states,
			String title, String shipKey, String sourcePortName,
			String destinationPortName);

	/**
	 * 根据ID查询平台船运需求详情
	 *
	 * @param id
	 * @return
	 */
	Optional<ShippingRequirementPlatVo> findVoById(String id);

	/**
	 * 客户查询船运需求详情
	 * 
	 * @return
	 */
	Optional<ShippingRequirementCustomerVo> findOwnerVoById(String platId);

	/**
	 * 根据船运需求id查询船运需求及船运单详情
	 *
	 * @return
	 */
	Optional<ShippingPlatAndTransportVo> findShippingPlatAndTransportVoById(
			String platId);

	/**
	 * 查询货主运单详情
	 */
	Optional<ShippingRequirementCustomerVo> orderDetail(String platId,
			String orderId);

	/**
	 * 获取服务专员列表
	 *
	 * @return
	 */
	List<ServiceSpecialVo> findServiceSpecials(String name);

	/**
	 * 根据状态查询平台船运需求列表
	 *
	 * @param state
	 * @return
	 */
	List<ShippingRequirementPlat> findByState(Integer state);

	/**
	 * 根据处理人ids 以及状态查找
	 */
	List<ShippingRequirementPlat> findByHandlerIdsAndState(
			List<Long> handlerIds, List<Integer> states);

	/**
	 * 查询货主上一条找船数据
	 *
	 * @return
	 */
	Optional<ShippingRequirementPlat> findLast(Long customerId);

	/**
	 * 根据平台船运需求找出符合智能推荐的船舶
	 * 
	 * @param platId
	 * @param portId
	 * @param radius
	 * @return
	 */
	List<ShipSmartVo> findByShipPlat(String platId, Long portId, Integer radius,
			String shipId, List<Integer> status, Long tonCapacityMin,
			Long tonCapacityMax);

	/**
	 * 自定义邀请的查船
	 *
	 * @return
	 */
	List<ShipSmartVo> findByExtentInvite(List<GeoPoint> geoPointList,
			String type, Long time, String platId);

	/**
	 * 查询平台船运需求数量
	 *
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	Long count(LocalDateTime beginTime, LocalDateTime endTime);

	/**
	 * 新增船运需求
	 *
	 * @param shippingRequirementPlat
	 *
	 * @return
	 */
	ShippingRequirementPlat createRequirementPlat(
			ShippingRequirementPlat shippingRequirementPlat);

	/**
	 * 货主新增船运需求
	 *
	 * @return
	 */
	Optional<ShippingRequirementPlat> ownerCreate(ShippingRequirementPlat plat);

	/**
	 * 修改船运需求
	 *
	 * @return
	 */
	ShippingRequirementPlat updateRequirementPlat(
			ShippingRequirementPlat shippingRequirementPlat);

	/**
	 *
	 * 将冈好运推过来的船运需求进行新增
	 * 
	 * @param shippingRequirementPlat
	 *
	 * @return
	 */
	String createPlat(ShippingRequirementPlat shippingRequirementPlat);

	/**
	 * 指派专员
	 *
	 * @param id
	 * @param handlerId
	 */
	void assign(String id, Long handlerId);

	/**
	 * 结束需求
	 *
	 * @param id
	 */
	void complete(String id);

	/**
	 * 关闭需求
	 *
	 * @param id
	 */
	void close(String id);

	/**
	 * 统计平台船运需求数量
	 *
	 * @return
	 */
	ShippingRequirementPlatCountVo statiscShippingRequirementPlat(Long userId,
			Boolean hasDeal);

	/**
	 * 平台邀请智能推荐船舶抢单
	 *
	 * @param platId
	 * @param shipIds
	 */
	void invite(String platId, List<String> shipIds);

	/**
	 * 修改平台船运需求的需求等级
	 *
	 * @param demandLevel
	 * @param platIds
	 */
	void updateDemandLevel(Integer demandLevel, List<String> platIds);

	/**
	 * 发布平台船运需求
	 *
	 * @param plat
	 */
	void publish(ShippingRequirementPlat plat);

	/**
	 * expiration定时任务用到的：三方的船运需求被刷成已结束时，推送给三方
	 * 
	 * @param plat
	 */
	void handleEndThirdParty(ShippingRequirementPlat plat);
}
