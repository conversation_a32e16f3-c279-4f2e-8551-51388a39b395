package com.zhihaoscm.service.resource.admin.sand.hot;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.domain.bean.dto.SandHotDto;
import com.zhihaoscm.domain.bean.entity.SandHot;
import com.zhihaoscm.domain.bean.vo.SandHotVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.domain.meta.error.ErrorCodeDef;
import com.zhihaoscm.service.core.service.SandHotService;
import com.zhihaoscm.service.resource.form.sand.hot.SandHotDeleteForm;
import com.zhihaoscm.service.resource.form.sand.hot.SandHotForm;
import com.zhihaoscm.service.resource.validator.sand.hot.SandHotValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 砂石热点 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Tag(name = "砂石热点", description = "砂石热点API")
@RestController
@RequestMapping("/sand-hot")
public class SandHotResource {
	@Autowired
	private SandHotService service;
	@Autowired
	private SandHotValidator validator;

	@GetMapping("/paging")
	@Operation(summary = "分页查询砂石热点")
	@Secured({ AdminPermissionDef.HPTSPOT_R, AdminPermissionDef.HPTSPOT_W })
	public ApiResponse<Page<SandHotVo>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "标题") @RequestParam(required = false) String title,
			@Parameter(description = "分类") @RequestParam(required = false) List<Integer> type,
			@Parameter(description = "上架状态：1：已上架 0：已下架") @RequestParam(required = false) Integer state,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder) {

		return new ApiResponse<>(service.paging(page, size, title, type, state,
				sortKey, sortOrder));
	}

	@Operation(summary = "查找单个砂石热点数据")
	@GetMapping("/vo/{id}")
	@Secured({ AdminPermissionDef.HPTSPOT_R, AdminPermissionDef.HPTSPOT_W })
	public ApiResponse<SandHotVo> findVoById(@PathVariable Long id) {
		SandHotVo vo = service.findVoById(id).orElse(null);
		if (Objects.isNull(vo)) {
			throw new BadRequestException(ErrorCodeDef.CODE_30192001.getCode());
		}
		return new ApiResponse<>(vo);
	}

	@Operation(summary = "新增砂石热点")
	@PostMapping
	@Secured({ AdminPermissionDef.HPTSPOT_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = LogDef.INDEX_MANAGEMENT_SAND_HOT_ADD, type = LogDef.INDEX_MANAGEMENT_SAND_HOT, bizNo = "{{#_ret.getData().getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getTitle()}}") })
	public ApiResponse<SandHot> create(
			@Validated @RequestBody SandHotForm form) {
		validator.validateCreate(form);
		SandHotDto dto = form.convertToDto(new SandHot());
		return new ApiResponse<>(service.create(dto.getSandHot(),
				dto.getActiveFileIds(), dto.getTagIds()));
	}

	@Operation(summary = "编辑砂石热点")
	@PutMapping(value = "/{id}")
	@Secured({ AdminPermissionDef.HPTSPOT_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.INDEX_MANAGEMENT_SAND_HOT_EDIT, type = LogDef.INDEX_MANAGEMENT_SAND_HOT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#form.getTitle()}}") })
	public ApiResponse<SandHot> update(@PathVariable(value = "id") Long id,
			@Validated @RequestBody SandHotForm form) {
		SandHot sandHot = validator.validateUpdate(id, form);
		SandHotDto dto = form.convertToDto(sandHot);
		return new ApiResponse<>(
				service.update(dto.getSandHot(), dto.getActiveFileIds(),
						dto.getUnActiveFileIds(), dto.getTagIds()));
	}

	@Operation(summary = "删除砂石热点")
	@DeleteMapping(value = "/{id}")
	@Secured({ AdminPermissionDef.HPTSPOT_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.DELETE, success = LogDef.INDEX_MANAGEMENT_SAND_HOT_DELETE, type = LogDef.INDEX_MANAGEMENT_SAND_HOT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#title}}") })
	public ApiResponse<Void> delete(@PathVariable Long id,
			@RequestBody SandHotDeleteForm deleteForm) {
		validator.validateDelete(id);
		SandHot sandHot = service.findOne(id).orElse(new SandHot());
		LogRecordContext.putVariable("title", sandHot.getTitle());
		service.delete(id, deleteForm.getUnActiveFileIds());
		return new ApiResponse<>();
	}

	@Operation(summary = "更新状态")
	@PostMapping("/update/{id}/{state}")
	@Secured({ AdminPermissionDef.HPTSPOT_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = "{{#success}}", type = LogDef.INDEX_MANAGEMENT_SAND_HOT, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#title}}") })
	public ApiResponse<Void> updateState(@PathVariable Long id,
			@Parameter(description = "1 上架 0 下架") @PathVariable Integer state) {
		validator.validateUpdateState(id, state);
		service.updateState(id, state);
		SandHot sandHot = service.findOne(id).orElse(new SandHot());
		LogRecordContext.putVariable("title", sandHot.getTitle());
		if (CommonDef.Symbol.YES.match(state)) {
			LogRecordContext.putVariable("success",
					LogDef.INDEX_MANAGEMENT_SAND_HOT_PUT_ON_SHELF);
		} else {
			LogRecordContext.putVariable("success",
					LogDef.INDEX_MANAGEMENT_SAND_HOT_PUT_OFF_SHELF);
		}
		return new ApiResponse<>();
	}
}
