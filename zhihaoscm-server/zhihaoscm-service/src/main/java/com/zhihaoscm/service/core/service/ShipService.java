package com.zhihaoscm.service.core.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.json.GeoPoint;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.entity.DeviceCapturePicRec;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.hmg.sdk.response.callback.HmgShipInfo;
import com.zhihaoscm.ships66.sdk.response.ShipInfo;

/**
 * <p>
 * 船舶 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
public interface ShipService extends MpStringIdBaseService<Ship> {

	/**
	 * 船舶列表分页查询
	 *
	 * @param page
	 * @param size
	 * @param sortKey
	 * @param sortOrder
	 * @param param
	 *            查询字段(船舶名关键字或MMSI)
	 * @param carrier
	 *            承运商
	 * @param state
	 * @param relatedDevice
	 *            是否关联设备
	 * @param warehouseState
	 * @return
	 */
	Page<ShipVo> paging(Integer page, Integer size, String sortKey,
			String sortOrder, String param, String contactMaintainer,
			Long carrier, List<Integer> state, Integer relatedDevice,
			Integer warehouseState, Integer existWaterGauge,
			Integer existMobile);

	/**
	 * 小程序分页查询
	 *
	 * @param page
	 * @param size
	 * @param carrier
	 * @return
	 */
	Page<ShipVo> customPaging(Integer page, Integer size, Long carrier);

	/**
	 * 下拉框
	 *
	 * @param page
	 * @param size
	 * @param state
	 * @param keyword
	 * @return
	 */
	Page<Ship> selector(Integer page, Integer size, Integer state,
			String keyword);

	/**
	 * 下拉框
	 *
	 * @param page
	 * @param size
	 * @param state
	 * @param keyword
	 * @return
	 */
	Page<ShipVo> selectorVo(Integer page, Integer size, Integer state,
			String keyword);

	/**
	 * 安装推广船舶下拉列表
	 *
	 * @param page
	 * @param size
	 * @param searchParam
	 * @return
	 */
	Page<ShipVo> installSelector(Integer page, Integer size,
			String searchParam);

	/**
	 * 承运商船舶下拉
	 *
	 * @param id
	 * @return
	 */
	Optional<Ship> carrierSelector(String id);

	/**
	 * 根据id查询船舶详情
	 *
	 * @param id
	 *            船舶id
	 * @return
	 */
	Optional<ShipVo> findVoById(String id);

	/**
	 * 根据关键字查询船舶 后台管理查船
	 *
	 * @param keyword
	 * @return
	 */
	List<ShipVo> findVoByKeyword(String keyword);

	/**
	 * 根据船舶中文名查询船舶
	 *
	 * @param cname
	 * @return
	 */
	List<Ship> findByCname(String cname);

	/**
	 * 根据ID列表查询 返回未删除的对象
	 *
	 * @param ids
	 * @return
	 */
	List<Ship> findByIdsNoDeleted(List<String> ids);

	/**
	 * 区域查船
	 *
	 * @param shipInfoList
	 * @return
	 */
	List<ShipInfoVo> findByExtent(List<ShipInfo> shipInfoList);

	/**
	 * 自定义查船
	 *
	 * @param geoPointList
	 * @param type
	 * @param time
	 * @return
	 */
	List<ShipInfoVo> findByCustomExtent(List<GeoPoint> geoPointList,
			String type, Long time);

	/**
	 * 区域查船存在分数的 后台管理区域查船
	 *
	 * @return
	 */
	List<ShipInfoVo> findByExtentAndScope(List<ShipInfo> shipInfoList);

	/**
	 * 根据mmsi查询船舶轨迹
	 *
	 * @param mmsi
	 * @param start
	 * @param end
	 * @return
	 */
	List<ShipTrajectoryVo> findByAisTrack(String mmsi, Long start, Long end);

	/**
	 * 根据关键字查询船舶-传顺
	 *
	 * @param keyword
	 *            关键字
	 * @param limit
	 *            限制 默认 10 条
	 * @param type
	 *            type type (船舶SHIP 航标 VMN 网位仪 NETSONDE)
	 * @return
	 */
	List<Ship> findByKeyword(String keyword, Integer limit, String type);

	/**
	 * 根据关键字查询船舶-链云数据库
	 *
	 * @param keyword
	 * @return
	 */
	List<Ship> findByKeyword(String keyword);

	/**
	 * 根据id查询船舶并验证
	 *
	 * @param id
	 * @return
	 */
	Optional<ShipVo> findByIdAndValidate(String id);

	/**
	 * 根据承运商查询船舶
	 *
	 * @param carrier
	 *            承运商id
	 * @param keyword
	 * @return
	 */
	List<Ship> findByCarrier(Long carrier, String keyword);

	/**
	 * 查询当前承运商已认证船舶列表(未上传内河船舶检验报告)
	 * 
	 * @param carrierId
	 * @return
	 */
	List<Ship> findByCarrierAndNoReportFile(Long carrierId);

	/**
	 * 查询承运商已认证的船舶
	 *
	 * @param carrier
	 *            承运商id
	 *
	 * @return
	 */
	List<Ship> findByCarrier(Long carrier);

	/**
	 * 查询承运商已认证且绑定了设备的船舶
	 *
	 * @param carrier
	 *            承运商id
	 *
	 * @return
	 */
	List<Ship> findByCarrierRelatedDevice(Long carrier);

	/**
	 * 查找并新增
	 *
	 * @param shipInfo
	 * @param userId
	 * @param monitoringChannels
	 *            1管理 2小程序
	 * @return
	 */
	Optional<ShipInfoVo> findByIdAndCreate(ShipInfo shipInfo, Long userId,
			Integer monitoringChannels);

	/**
	 * 传顺根据mmsi查询
	 *
	 * @param id
	 * @return
	 */
	ShipInfo findByAsi(String id);

	/**
	 * 查询历史
	 *
	 * @param customerId
	 * @param monitoringChannels
	 *            哪个端
	 *            {@link com.zhihaoscm.domain.meta.biz.ShipDef.MonitoringChannels}
	 * @return
	 */
	List<ShipHistoryVo> findHistory(Long customerId,
			Integer monitoringChannels);

	/**
	 * 查询船舶、码头搜索历史
	 *
	 * @param customerId
	 * @param monitoringChannels
	 * @return
	 */
	List<ShipPortHistoryVo> findShipPortHistory(Long customerId,
			Integer monitoringChannels);

	/**
	 * 砂石交易概览
	 *
	 * @param scope
	 * @param beginTime
	 * @param endTime
	 */
	com.zhihaoscm.common.bean.page.Page<TransportOrderShip> findOrderShip(
			Integer scope, LocalDateTime beginTime, LocalDateTime endTime,
			String shipId, Integer page, Integer size);

	/**
	 * 根据船舶中文名查询船舶信息
	 *
	 * @param shipCnName
	 * @return
	 */
	List<Ship> findByShipCnName(String shipCnName);

	/**
	 * 调用船顺网区域查船
	 *
	 * @param leftTopPoint
	 * @param rightTopPoint
	 * @param rightBottomPoint
	 * @param leftBottomPoint
	 * @param type
	 * @param time
	 * @return
	 */
	List<ShipInfo> findShipInfoByExtent(GeoPoint leftTopPoint,
			GeoPoint rightTopPoint, GeoPoint rightBottomPoint,
			GeoPoint leftBottomPoint, String type, Long time);

	/**
	 * 根据船舶ID查询船舶和设备信息（中盛供应链使用）
	 *
	 * @param ids
	 * @return
	 */
	List<ShipVo> findVoByIds(List<String> ids);

	/**
	 * 查询联系方式不为空的数据
	 *
	 * @param carrier
	 */
	List<Ship> selectShips(Long carrier, String shipId, Long tonCapacityMin,
			Long tonCapacityMax);

	/**
	 * 查询有承运商的船舶数据
	 *
	 */
	List<Ship> selectCarrierExists();

	/**
	 * 根据货主找船需求找出符合智能推荐的船舶
	 *
	 * @param id
	 * @param monitoringChannels
	 *            哪个端
	 *            {@link com.zhihaoscm.domain.meta.biz.ShipDef.MonitoringChannels}
	 *
	 */
	List<ShipSmartVo> findByShipCustom(Long id, Integer monitoringChannels);

	/**
	 * 根据状态查询数量
	 *
	 * @param state
	 * @param beginTime
	 * @param endTime
	 * @param relatedDevice
	 * @param existWaterGauge
	 * @return
	 */
	Long countByState(Integer state, LocalDateTime beginTime,
			LocalDateTime endTime, Integer relatedDevice,
			Integer existWaterGauge);

	/**
	 * 根据状态查询
	 * 
	 * @param state
	 * @return
	 */
	List<Ship> findByState(Integer state);

	/**
	 * 根据船舶id查询 id逗号分隔 最多40个
	 *
	 * @param ids
	 * @return
	 */
	List<ShipInfo> findByBatchAsi(String ids);

	/**
	 * 根据港口id和区域范围查询船舶
	 *
	 * @param portId
	 * @param area
	 *            {@link com.zhihaoscm.domain.meta.biz.ShipDef.Area}
	 * @return
	 */
	List<ShipInfo> findShipInfoByPortId(Long portId, Integer area);

	/**
	 * 查询当日运价信息
	 *
	 * @param localDate
	 * @return
	 */
	List<ShippingPriceIndexAIVo> findTodayShippingPriceIndex(
			LocalDate localDate);

	/**
	 * 查询在线船舶
	 *
	 * @return
	 */
	List<OnlineShipVo> findByOnlineShip();

	/**
	 * 识别内河船舶检验报告
	 *
	 * @param fileIds
	 *            内河船舶检验报告 file id
	 */
	ShipInspectionReportVo recognizeShipInspectionReport(List<Long> fileIds);

	/**
	 * 船舶营业运输证提取
	 *
	 * @param fileId
	 * @return
	 */
	Optional<ShipBusinessTransportVo> getShipBusinessTransport(Long fileId);

	/**
	 * 识别船舶船员适任证书
	 *
	 * @param fileId
	 * @return
	 */
	Optional<String> recognizeCrewCertificate(Long fileId);

	/**
	 * 绑定船舶
	 *
	 * @param id
	 * @param customerId
	 */
	void bind(String id, Long customerId);

	/**
	 * 绑定船舶
	 *
	 * @param ship
	 */
	void bind(Ship ship);

	/**
	 * 解绑船舶
	 *
	 * @param id
	 */
	void unbind(String id);

	/**
	 * 异步批量创建船舶
	 *
	 * @param idList
	 *            船舶idList
	 */
	void asyncBathCreate(List<String> idList);

	/**
	 * 插入数据忽略主键冲突
	 *
	 * @param ship
	 */
	void insertIgnoreUk(Ship ship);

	/**
	 * 创建查询历史
	 *
	 * @param customerId
	 * @param monitoringChannels
	 *            哪个端
	 *            {@link com.zhihaoscm.domain.meta.biz.ShipDef.MonitoringChannels}
	 * @param id
	 * @param name
	 * @param cnname
	 * @param type
	 */
	void createHistory(Long customerId, Integer monitoringChannels, String id,
			String name, String cnname, Integer type);

	/**
	 * 创建船舶、码头搜索历史
	 *
	 * @param customerId
	 * @param monitoringChannels
	 * @param shipPortHistoryVo
	 */
	void createShipPortHistory(Long customerId, Integer monitoringChannels,
			ShipPortHistoryVo shipPortHistoryVo);

	/**
	 * 更新货仓状态
	 */
	void updateWarehouseState();

	/**
	 * 批量更新船舶是否存在水尺
	 *
	 * @param shipIds
	 */
	void batchUpdateExistWaterGauge(Collection<String> shipIds,
			Integer existWaterGauge);

	/**
	 * 批量更新船舶是否存在设备
	 *
	 * @param shipIds
	 */
	void batchUpdateExistDevice(Collection<String> shipIds,
			Integer existDevice);

	/**
	 * 批量更新船舶是否维护联系方式
	 *
	 * @param shipIds
	 */
	void batchUpdateExistMobile(Collection<String> shipIds,
			Integer existMobile);

	/**
	 * 删除查询历史
	 *
	 * @param customerId
	 * @param monitoringChannels
	 *            哪个端
	 *            {@link com.zhihaoscm.domain.meta.biz.ShipDef.MonitoringChannels}
	 * @param id
	 * @param name
	 * @param cnname
	 * @param type
	 */
	void deleteHistory(Long customerId, Integer monitoringChannels, String id,
			String name, String cnname, Integer type);

	/**
	 * 清空历史记录
	 *
	 * @param customerId
	 * @param monitoringChannels
	 *            哪个端
	 *            {@link com.zhihaoscm.domain.meta.biz.ShipDef.MonitoringChannels}
	 */
	void deleteAllHistory(Long customerId, Integer monitoringChannels);

	/**
	 * 删除船舶、码头搜索历史
	 *
	 * @param customerId
	 * @param monitoringChannels
	 * @param shipPortHistoryVo
	 */
	void deleteShipPortHistory(Long customerId, Integer monitoringChannels,
			ShipPortHistoryVo shipPortHistoryVo);

	/**
	 * 删除全部查询船舶、码头搜索历史
	 *
	 * @param customerId
	 * @param monitoringChannels
	 */
	void deleteAllShipPortHistory(Long customerId, Integer monitoringChannels);

	/**
	 * 处理船舶异常告警
	 *
	 * @param shipIdDeviceCapturePicRecMap
	 */
	void handleShipReport(
			Map<String, DeviceCapturePicRec> shipIdDeviceCapturePicRecMap);

	/**
	 * 处理船舶分数
	 */
	void handelShipScope();

	void testUpdateWarehouseState();

	/**
	 * 处理黄码港船舶信息回调
	 * 
	 * @param hmgShipInfo
	 */
	void handleShipInfoCallback(HmgShipInfo hmgShipInfo);

	/**
	 * 转换船舶信息 在后台船舶关注使用
	 *
	 * @param shipInfoList
	 * @return
	 */
	List<ShipInfoVo> handleShipInfo(List<ShipInfo> shipInfoList);

	/**
	 * 定时任务识别身份证信息
	 */
	void recognizeIdCard();
}
