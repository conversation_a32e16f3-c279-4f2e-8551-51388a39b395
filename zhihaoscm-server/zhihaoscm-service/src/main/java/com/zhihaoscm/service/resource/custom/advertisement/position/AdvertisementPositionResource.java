package com.zhihaoscm.service.resource.custom.advertisement.position;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.entity.AdvertisementPosition;
import com.zhihaoscm.service.core.service.AdvertisementPositionService;
import com.zhihaoscm.service.resource.validator.advertisement.position.AdvertisementPositionValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "广告位管理", description = "广告位管理API")
@RequestMapping("/advertisement-position")
@RestController
public class AdvertisementPositionResource {

	@Autowired
	private AdvertisementPositionService service;
	@Autowired
	private AdvertisementPositionValidator validator;

	@Operation(summary = "全部广告位")
	@GetMapping("/all")
	public ApiResponse<List<AdvertisementPosition>> findAll() {
		return new ApiResponse<>(service.findAll());
	}

	@Operation(summary = "根据广告位和广告位号查询查询")
	@GetMapping("/position")
	public ApiResponse<AdvertisementPosition> findByPosition(
			@RequestParam(value = "positionType") Integer positionType,
			@RequestParam(value = "position") Integer position) {
		return new ApiResponse<>(
				service.findByPosition(positionType, position).orElse(null));
	}

	@Operation(summary = "根据id查询")
	@GetMapping("/{id}")
	public ApiResponse<AdvertisementPosition> findById(@PathVariable Long id) {
		return new ApiResponse<>(validator.validateExist(id));
	}

}
