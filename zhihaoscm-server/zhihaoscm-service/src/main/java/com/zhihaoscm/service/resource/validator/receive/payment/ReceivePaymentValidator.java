package com.zhihaoscm.service.resource.validator.receive.payment;

import java.time.LocalDateTime;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.domain.bean.entity.ReceivePayment;
import com.zhihaoscm.domain.meta.biz.ReceivePaymentDef;
import com.zhihaoscm.service.core.service.ReceivePaymentService;
import com.zhihaoscm.service.resource.form.receive.payment.ReceivePaymentForm;

/**
 * 收款校验器
 */
@Component
public class ReceivePaymentValidator {

	@Autowired
	private ReceivePaymentService service;

	/**
	 * 校验是否存在
	 * 
	 * @param id
	 * @return
	 */
	public ReceivePayment validateExist(String id) {
		return service.findOne(id)
				.orElseThrow(() -> new BadRequestException("收款信息不存在"));
	}

	/**
	 * 校验新增 支持手动新增发航货款和开仓货款
	 * 
	 * @param form
	 * @return
	 */
	public ReceivePayment validateCreate(ReceivePaymentForm form) {
		// todo 根据排期id查询订单 再根据订单ID查询商品的支付模式
		if (Objects.isNull(form.getPaymentType())) {
			throw new BadRequestException("收款类型不能为空");
		}
		if (!(ReceivePaymentDef.PaymentType.SHIPPING_PAYMENT
				.match(form.getPaymentType())
				|| ReceivePaymentDef.PaymentType.WAREHOUSE_OPENING_PAYMENT
						.match(form.getPaymentType()))) {
			throw new BadRequestException("不支持新增");
		}
		// 设置订单结束时间
		ReceivePayment receivePayment = form
				.convertToEntity(new ReceivePayment());
		Integer hour = ReceivePaymentDef.PaymentType.from(form.getPaymentType())
				.getHour();
		receivePayment.setPayEndTime(LocalDateTime.now().plusHours(hour));
		return receivePayment;
	}

	/**
	 * 校验修改 待支付状态才能修改
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public ReceivePayment validateUpdate(String id, ReceivePaymentForm form) {
		ReceivePayment exist = this.validateExist(id);
		if (ReceivePaymentDef.State.PENDING_PAYMENT.match(exist.getState())) {
			throw new BadRequestException("待支付状态才能修改");
		}
		// 设置订单结束时间
		ReceivePayment receivePayment = form.convertToEntity(exist);
		Integer hour = ReceivePaymentDef.PaymentType.from(form.getPaymentType())
				.getHour();
		receivePayment.setPayEndTime(LocalDateTime.now().plusHours(hour));
		return receivePayment;
	}

	/**
	 * 校验取消 订单定金和订单货款不能取消 待支付状态才能取消
	 * 
	 * @param id
	 * @return
	 */
	public ReceivePayment validateCancel(String id) {
		ReceivePayment receivePayment = this.validateExist(id);
		if (ReceivePaymentDef.PaymentType.ORDER_DEPOSIT
				.match(receivePayment.getPaymentType())
				|| ReceivePaymentDef.PaymentType.ORDER_PAYMENT
						.match(receivePayment.getPaymentType())) {
			throw new BadRequestException("订单定金和订单货款不能取消");
		}

		if (ReceivePaymentDef.State.PENDING_PAYMENT
				.match(receivePayment.getState())) {
			throw new BadRequestException("待支付状态才能取消");
		}
		return receivePayment;
	}
}
