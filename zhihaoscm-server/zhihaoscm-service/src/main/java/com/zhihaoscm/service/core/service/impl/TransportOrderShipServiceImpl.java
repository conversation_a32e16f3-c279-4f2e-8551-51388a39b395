package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.date.DateFormat;
import com.zhihaoscm.common.util.utils.date.DateUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.TransportOrderShipAIRes;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.*;
import com.zhihaoscm.domain.bean.json.Application.ApplicationTransfer;
import com.zhihaoscm.domain.bean.json.notice.Messages;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.exception.DynamicsBadRequestException;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.ApplicationDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.meta.notice.AppType;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.domain.utils.GeoUtils;
import com.zhihaoscm.domain.utils.PaginationUtils;
import com.zhihaoscm.domain.utils.ThreadPoolUtil;
import com.zhihaoscm.hmg.sdk.api.HmgClient;
import com.zhihaoscm.hmg.sdk.enums.HmgDef;
import com.zhihaoscm.hmg.sdk.request.*;
import com.zhihaoscm.hmg.sdk.response.CreateWaybillResponse;
import com.zhihaoscm.hmg.sdk.response.HmgBaseResponse;
import com.zhihaoscm.hmg.sdk.response.UpdateWaybillResponse;
import com.zhihaoscm.hmg.sdk.response.callback.CarrierInfo;
import com.zhihaoscm.hmg.sdk.response.callback.HmgShipInfo;
import com.zhihaoscm.hmg.sdk.response.callback.WaybillInfo;
import com.zhihaoscm.hmg.sdk.response.callback.WaybillPaymentInfo;
import com.zhihaoscm.service.aop.history.HistoryContext;
import com.zhihaoscm.service.config.properties.HmgProperties;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.core.mapper.TransportOrderShipMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerBankService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 船运单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Slf4j
@Service
public class TransportOrderShipServiceImpl extends
		MpStringIdBaseServiceImpl<TransportOrderShip, TransportOrderShipMapper>
		implements TransportOrderShipService {

	@Autowired
	private ShippingRequirementPlatService shippingRequirementPlatService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private TransportOrderDetailsShipService transportOrderDetailsShipService;
	@Autowired
	private UserService userService;
	@Autowired
	private ShipService shipService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private AssignService assignService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private PortService portService;
	@Lazy
	@Autowired
	private OwnerShippingDepositService ownerShippingDepositService;
	@Lazy
	@Autowired
	private ShippingRequirementAcceptService shippingRequirementAcceptService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private CustomerBankService customerBankService;
	@Autowired
	private ShipInfoServiceFeeService shipInfoServiceFeeService;
	@Autowired
	private TrajectoryService trajectoryService;
	@Autowired
	private MqUtil mqUtil;
	@Autowired
	private HmgClient hmgClient;
	@Autowired
	private FileService fileService;
	@Autowired
	private HmgProperties hmgProperties;
	@Autowired
	private AreasService areasService;
	@Autowired
	private HmgFileService hmgFileService;

	public TransportOrderShipServiceImpl(TransportOrderShipMapper repository) {
		super(repository);
	}

	@Override
	public Page<TransportOrderShipVo> paging(Integer page, Integer size,
			String sortKey, String sortOrder, String keyword,
			String portKeyword, String shipKeyword, String handlerName,
			String type, List<Integer> state, LocalDateTime beginTime,
			LocalDateTime endTime, boolean hasFull, Long userId,
			Long customerId) {
		LambdaQueryWrapper<TransportOrderShip> queryWrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		if (Objects.nonNull(customerId)) {
			queryWrapper.and(
					x -> x.eq(TransportOrderShip::getOwnerId, customerId).or()
							.eq(TransportOrderShip::getCaptainId, customerId));
			hasFull = Boolean.TRUE;
		}
		// 有管理权限可以看全部数据
		if (!hasFull) {
			// 没有管理权限 有处理权限 只能看自己处理的
			if (Objects.nonNull(userId)) {
				queryWrapper.and(i -> i
						.eq(TransportOrderShip::getUpstreamHandlerId, userId)
						.or().eq(TransportOrderShip::getDownstreamHandlerId,
								userId));
			}
		}

		if (StringUtils.isNotBlank(keyword)) {
			// 单位查询
			queryWrapper.and(i -> i.apply(StringUtils.isNoneBlank(keyword),
					"(JSON_EXTRACT(owner_enterprise, '$.name') LIKE CONCAT('%',{0},'%') "
							+ "OR JSON_EXTRACT(owner_enterprise, '$.realName') LIKE CONCAT('%',{0},'%'))"
							+ "OR JSON_EXTRACT(owner_enterprise, '$.mobile') ={0}"
							+ "OR (id = {0})",
					keyword));
		}
		if (StringUtils.isNotBlank(portKeyword)) {
			queryWrapper.and(i -> i
					.like(TransportOrderShip::getSourcePortName, portKeyword)
					.or().eq(TransportOrderShip::getDestinationPortName,
							portKeyword));
		}
		if (StringUtils.isNotBlank(shipKeyword)) {
			queryWrapper.like(TransportOrderShip::getShipName, shipKeyword);
		}
		if (StringUtils.isNotBlank(handlerName)) {
			queryWrapper.and(i -> i
					.like(TransportOrderShip::getUpstreamHandlerName,
							handlerName)
					.or().like(TransportOrderShip::getDownstreamHandlerName,
							handlerName));
		}
		queryWrapper.eq(Objects.nonNull(type), TransportOrderShip::getType,
				type);
		queryWrapper.in(CollectionUtils.isNotEmpty(state),
				TransportOrderShip::getState, state);
		queryWrapper.ge(Objects.nonNull(beginTime),
				TransportOrderShip::getCreatedTime, beginTime);
		queryWrapper.le(Objects.nonNull(endTime),
				TransportOrderShip::getCreatedTime, endTime);
		// 排序
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照创建时间降序排列
			queryWrapper.last(
					"ORDER BY FIELD(state, 10,9,1,2,3,4,5,6,7,8) ASC, created_time DESC");
		}
		Page<TransportOrderShip> transportOrderShipPage = repository
				.selectPage(new Page<>(page, size), queryWrapper);
		return PageUtil.getRecordsInfoPage(transportOrderShipPage,
				this.packVo(transportOrderShipPage.getRecords()));
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<TransportOrderShipVo> customPaging(
			Integer page, Integer size, String keyword, Integer queryType,
			Long customerId, Integer state, String sourcePortName,
			String destinationPortName, Boolean isNotCompleted) {
		LambdaQueryWrapper<TransportOrderShip> queryWrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		if (StringUtils.isNotBlank(keyword)) {
			queryWrapper
					.and(i -> i.like(TransportOrderShip::getGoodsType, keyword)
							.or().eq(TransportOrderShip::getId, keyword));
		}
		queryWrapper.like(StringUtils.isNotBlank(sourcePortName),
				TransportOrderShip::getSourcePortName, sourcePortName);
		queryWrapper.like(StringUtils.isNotBlank(destinationPortName),
				TransportOrderShip::getDestinationPortName,
				destinationPortName);
		if (Objects.nonNull(isNotCompleted) && isNotCompleted) {
			queryWrapper.ne(TransportOrderShip::getState,
					TransportOrderShipDef.State.COMPLETED.getCode());
		}
		switch (TransportOrderShipDef.QueryType.from(queryType)) {
			case SHIPPER ->
				// 船主
				addServiceFeeConditions(queryWrapper, state, true, customerId);
			case SHIPOWNER -> {
				// 货主
				addServiceFeeConditions(queryWrapper, state, false, customerId);
				queryWrapper.eq(TransportOrderShip::getOwnerId, customerId);
			}
		}
		List<TransportOrderShipVo> transportOrderShipVoList = this
				.packCustomerVo(repository.selectList(queryWrapper), queryType);
		return PaginationUtils.handelPage(page, size, transportOrderShipVoList);

	}

	@Override
	public List<TransportOrderShip> selector(String keyword, Long customerId) {
		LambdaQueryWrapper<TransportOrderShip> queryWrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.and(i -> i.like(TransportOrderShip::getGoodsType, keyword)
				.or().eq(TransportOrderShip::getId, keyword));
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<TransportOrderShip> selectOrders(Long customerId) {
		LambdaQueryWrapper<TransportOrderShip> queryWrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class);
		queryWrapper.eq(TransportOrderShip::getOwnerId, customerId);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<TransportOrderShipVo> selectorVo(String keyword,
			Long customerId) {
		List<TransportOrderShip> transportOrderShipList = this.selector(keyword,
				customerId);
		return this.packVo(transportOrderShipList);
	}

	@Override
	public List<User> specialsSelector(String keyword) {
		// 查询拥有服务专员角色的账号
		return userService.findUsersByPermission(
				AdminPermissionDef.SHIP_ORDER_DEAL, keyword);
	}

	@Override
	public Optional<TransportOrderShipVo> findVoById(String id) {
		return super.findOne(id).map(this::packVo);
	}

	@Override
	public List<ServiceSpecialVo> findServiceSpecials(
			Integer serviceSpecialsType) {
		// 查询拥有服务专员角色的账号
		List<User> attaches = userService.findUsersByPermission(
				AdminPermissionDef.SHIP_ORDER_DEAL, null);
		if (CollectionUtils.isEmpty(attaches)) {
			return List.of();
		}

		LambdaQueryWrapper<TransportOrderShip> wrapper;
		Function<TransportOrderShip, Long> handlerIdExtractor;

		if (TransportOrderShipDef.ServiceSpecialsType.UPSTREAM_HANDLER
				.match(serviceSpecialsType)) {
			wrapper = Wrappers.lambdaQuery(TransportOrderShip.class)
					.eq(TransportOrderShip::getDel,
							CommonDef.Symbol.NO.getCode())
					.isNotNull(TransportOrderShip::getUpstreamHandlerId);
			handlerIdExtractor = TransportOrderShip::getUpstreamHandlerId;
		} else {
			wrapper = Wrappers.lambdaQuery(TransportOrderShip.class)
					.eq(TransportOrderShip::getDel,
							CommonDef.Symbol.NO.getCode())
					.isNotNull(TransportOrderShip::getDownstreamHandlerId);
			handlerIdExtractor = TransportOrderShip::getDownstreamHandlerId;
		}

		return packServiceSpecialVo(attaches, wrapper, handlerIdExtractor);
	}

	@Override
	public List<TransportOrderShip> findByShipId(String shipId,
			List<Integer> notInStates) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.eq(TransportOrderShip::getShipId, shipId)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode())
				.notIn(CollectionUtils.isNotEmpty(notInStates),
						TransportOrderShip::getState, notInStates);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<Integer> findTon(LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.query(TransportOrderShip.class).select("sum(ton) ton").lambda()
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.ge(Objects.nonNull(beginTime),
				TransportOrderShip::getCreatedTime, beginTime);
		wrapper.le(Objects.nonNull(endTime), TransportOrderShip::getCreatedTime,
				endTime);
		TransportOrderShip transportOrderShip = repository.selectOne(wrapper);

		return Objects.isNull(transportOrderShip) ? Optional.of(0)
				: Optional.of(Objects.nonNull(transportOrderShip.getTon())
						? transportOrderShip.getTon()
						: 0);
	}

	@Override
	public Optional<BigDecimal> findAmount(LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.query(TransportOrderShip.class)
				.select("sum(ton*unit_price) unit_price").lambda()
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.ge(Objects.nonNull(beginTime),
				TransportOrderShip::getCreatedTime, beginTime);
		wrapper.le(Objects.nonNull(endTime), TransportOrderShip::getCreatedTime,
				endTime);
		TransportOrderShip transportOrderShip = repository.selectOne(wrapper);
		return Objects.isNull(transportOrderShip)
				? Optional.of(new BigDecimal(0))
				: Optional.of(transportOrderShip.getUnitPrice());
	}

	@Override
	public List<TransportOrderShip> findIncompleteByShipIds(
			Set<String> shipIds) {
		LambdaQueryWrapper<TransportOrderShip> queryWrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.in(TransportOrderShip::getShipId, shipIds);
		queryWrapper.ne(TransportOrderShip::getState,
				TransportOrderShipDef.State.COMPLETED.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<TransportOrderShip> findByTime(LocalDateTime startTime,
			LocalDateTime finishTime, String shipId) {
		LambdaQueryWrapper<TransportOrderShip> queryWrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class);
		queryWrapper.eq(TransportOrderShip::getDel,
				CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(TransportOrderShip::getShipId, shipId);
		queryWrapper.ge(Objects.nonNull(startTime),
				TransportOrderShip::getCreatedTime, startTime);
		queryWrapper.le(Objects.nonNull(finishTime),
				TransportOrderShip::getCreatedTime, finishTime);
		// 默认按照创建时间降序排列
		queryWrapper.last(
				"ORDER BY FIELD(state, 10,9,1,2,3,4,5,6,7,8) ASC, created_time DESC");
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<TransportOrderShipVo> findVoByIdAndAppIsEmpty(String id) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class);
		wrapper.eq(TransportOrderShip::getId, id);
		wrapper.isNull(TransportOrderShip::getAppId);
		this.filterDeleted(wrapper);
		TransportOrderShip transportOrderShip = repository.selectOne(wrapper);
		return Optional.ofNullable(transportOrderShip).map(this::packVo);
	}

	@Override
	public List<TransportOrderShipAIRes> findHistoryShipOrders(Long customerId,
			String startPort, String endPort, String goodsType, Integer ton) {
		LambdaQueryWrapper<TransportOrderShip> queryWrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(TransportOrderShip::getOwnerId, customerId);
		queryWrapper.like(StringUtils.isNotBlank(startPort),
				TransportOrderShip::getSourcePortName, startPort);
		queryWrapper.like(StringUtils.isNotBlank(endPort),
				TransportOrderShip::getDestinationPortName, endPort);
		queryWrapper.eq(Objects.nonNull(ton), TransportOrderShip::getTon, ton);
		queryWrapper.orderByDesc(TransportOrderShip::getCreatedTime);
		queryWrapper.last("LIMIT 3");
		return this.packToAIRes(this.repository.selectList(queryWrapper));
	}

	@Override
	public List<TransportOrderShipVo> findVoBySraId(Long sraId) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.eq(TransportOrderShip::getSraId, sraId)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		return this.packVo(repository.selectList(wrapper));
	}

	@Override
	public List<TransportOrderShipVo> findVoBySraIds(List<Long> sraIds) {
		if (CollectionUtils.isEmpty(sraIds)) {
			return List.of();
		}
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.in(TransportOrderShip::getSraId, sraIds)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		return this.packVo(repository.selectList(wrapper));
	}

	@Override
	public List<TransportOrderShip> findBySrpId(String id) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.eq(TransportOrderShip::getSrpId, id)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public List<TransportOrderShipVo> findVoBySrpId(String SrpId) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.eq(TransportOrderShip::getSrpId, SrpId)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		return this.packVo(repository.selectList(wrapper));
	}

	@Override
	public List<TransportOrderShip> findByShipIdsAndState(Set<String> shipIds,
			Integer state) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.in(TransportOrderShip::getShipId, shipIds)
				.eq(TransportOrderShip::getState, state)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public List<TransportOrderShip> findByCaptainId(Long captainId,
			List<Integer> notInStates) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.eq(TransportOrderShip::getCaptainId, captainId)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode())
				.notIn(CollectionUtils.isNotEmpty(notInStates),
						TransportOrderShip::getState, notInStates);
		return repository.selectList(wrapper);
	}

	@Override
	public List<TransportOrderShip> findNotInStates(List<Integer> notInStates) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode())
				.notIn(CollectionUtils.isNotEmpty(notInStates),
						TransportOrderShip::getState, notInStates);
		return repository.selectList(wrapper);
	}

	@Override
	public List<ShipTrajectoryVo> findAisTrackById(String id) {
		TransportOrderShip transportOrderShip = this.findOne(id).orElse(null);
		if (Objects.isNull(transportOrderShip)) {
			return List.of();
		}
		if (Objects.isNull(transportOrderShip.getShipId())) {
			return List.of();
		}
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
				.findOne(id).orElse(null);
		if (Objects.isNull(transportOrderDetailsShip)) {
			return List.of();
		}

		DeliveryInfo deliveryInfo = transportOrderDetailsShip.getDeliveryInfo();

		DeparturePreparationInfo lazbInfo = transportOrderDetailsShip
				.getLazbInfo();

		SetSailInfo setSailInfo = transportOrderDetailsShip.getSetSailInfo();

		Long beginTime = null;
		Long endTime = null;
		if (Objects.nonNull(deliveryInfo)
				&& Objects.nonNull(deliveryInfo.getLoadingDate())) {
			// 装货时间
			LocalDateTime loadingDate = deliveryInfo.getLoadingDate();
			beginTime = this.getTimestamp(loadingDate);
		} else {
			if (Objects.nonNull(lazbInfo)
					&& Objects.nonNull(lazbInfo.getLoadingCompletionTime())) {
				// 装载完成时间
				LocalDateTime loadingCompletionTime = lazbInfo
						.getLoadingCompletionTime();
				beginTime = this.getTimestamp(loadingCompletionTime);
			} else {
				if (Objects.nonNull(setSailInfo)
						&& Objects.nonNull(setSailInfo.getDepartureTime())) {
					// 发航时间
					LocalDateTime departureTime = setSailInfo
							.getDepartureTime();
					beginTime = this.getTimestamp(departureTime);
				}
			}
		}

		// 到港时间
		LocalDateTime captainArrivalTime = transportOrderDetailsShip
				.getCaptainArrivalTime();
		if (Objects.nonNull(captainArrivalTime)) {
			endTime = this.getTimestamp(captainArrivalTime);
		}

		if (Objects.isNull(beginTime) || Objects.isNull(endTime)) {
			log.error("船运单历史轨迹，时间参数为空:{}", id);
		}

		List<ShipTrajectoryVo> data;
		try {
			// 获取历史轨迹
			data = shipService.findByAisTrack(transportOrderShip.getShipId(),
					beginTime, endTime);
		} catch (Exception e) {
			log.error("船运单历史轨迹，查询轨迹异常:{}", e.getMessage(), e);
			data = new ArrayList<>();
		}
		return data;
	}

	@Override
	public Long count(LocalDateTime beginTime, LocalDateTime endTime,
			Boolean isRelatedPlat) {
		LambdaQueryWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class)
				.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.ge(Objects.nonNull(beginTime),
				TransportOrderShip::getCreatedTime, beginTime);
		wrapper.le(Objects.nonNull(endTime), TransportOrderShip::getCreatedTime,
				endTime);
		wrapper.isNotNull(Objects.nonNull(isRelatedPlat) && isRelatedPlat,
				TransportOrderShip::getSrpId);
		return repository.selectCount(wrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public TransportOrderShip buildTransportOrderShip(
			TransportOrderShip transportOrderShip) {
		return this.create(transportOrderShip);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@History(success = HistoryDef.SHIPPING_ORDER_ADD, bizNo = "{{#resource.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER)
	public TransportOrderShip create(TransportOrderShip resource) {
		// 生成id
		resource.setId(AutoCodeDef.CREATE_AUTO_CODE.apply(redisClient,
				AutoCodeDef.BusinessCode.TRANSPORT_ORDER_SHIP_PREFIX.getCode(),
				RedisKeys.Cache.TRANSPORT_ORDER_SHIP_CODE_GENERATOR,
				"0" + resource.getType(), 5, AutoCodeDef.DATE_TYPE.yyMM));
		this.packData(resource);
		// 判断支付类型是不是垫付
		if (TransportOrderShipDef.DepositPayType.PAY_ZH
				.match(resource.getPayType())) {
			// 调用hmg新增或变更承运人和船舶接口
			this.saveCarrierAndShip(resource);
			// 调用hmg新增或变更货源接口
			this.saveGoodsSource(resource);
			// 调用hmg新增运单信息接口
			String waybill = this.createWaybill(resource);
			if (StringUtils.isNotBlank(waybill)) {
				resource.setHmgWaybillNo(waybill);
			}
		}
		TransportOrderShip transportOrderShip = super.create(resource);

		// 创建船运单明细
		TransportOrderDetailsShip transportOrderDetailsShip = new TransportOrderDetailsShip();
		transportOrderDetailsShip.setId(transportOrderShip.getId());
		transportOrderDetailsShipService.create(transportOrderDetailsShip);
		if (StringUtils.isNotBlank(resource.getSrpId())
				&& TransportOrderShipDef.Type.PLATFORM_CREATION
						.match(resource.getType())) {
			// 其他接单失败
			shippingRequirementAcceptService.rejectOtherAccept(
					resource.getSrpId(), shippingRequirementAcceptService
							.findByPlatId(resource.getSrpId()));
		}

		// 发生相关通知
		this.sendTransportOrderShipNotice(resource);
		// 修改货主找船需求为已完成
		if (StringUtils.isNotBlank(resource.getSrpId())) {
			ShippingRequirementPlat shippingRequirementPlat = shippingRequirementPlatService
					.findOne(transportOrderShip.getSrpId()).orElse(null);
			if (Objects.nonNull(shippingRequirementPlat)) {
				// 修改船运需求为已结束
				shippingRequirementPlat.setState(
						ShippingRequirementPlatDef.State.ENDED.getCode());
				shippingRequirementPlatService
						.updateAllProperties(shippingRequirementPlat);

				// 新增船运单时选择了船运需求 并且船运需求是三方需求时需要推送给对方
				if (ShippingRequirementPlatDef.DataSource.OUTER
						.match(shippingRequirementPlat.getDataSource())) {
					// 船运单设置应用id
					resource.setAppId(shippingRequirementPlat.getSourceAppId());
					// 处理核对船主抢单信息为成功状态
					handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
							.getCode(),
							ShippingCallbackDef.PushType.UPDATE_SHIP_ACCEPT_CREATE_ORDER
									.getCode(),
							resource.getId());
					super.update(resource);
				}
			}
		}
		return transportOrderShip;
	}

	@FileId(type = 2)
	@Override
	public TransportOrderShip updateAllProperties(TransportOrderShip resource) {
		return super.updateAllProperties(resource);
	}

	@FileId(type = 3)
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void delete(String id) {
		TransportOrderShip transportOrderShip = super.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			// 清除接单里面的船运单id
			shippingRequirementAcceptService
					.findOne(transportOrderShip.getSraId())
					.ifPresent(shippingRequirementAccept -> {
						ArrayString orderShipIds = shippingRequirementAccept
								.getOrderShipIds();
						orderShipIds.remove(id);
						shippingRequirementAccept.setOrderShipIds(orderShipIds);
						shippingRequirementAcceptService
								.updateAllProperties(shippingRequirementAccept);
					});
			if (Objects.nonNull(transportOrderShip.getAppId())) {
				// 关闭船运单推送给第三方
				handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
						.getCode(),
						ShippingCallbackDef.PushType.CLOSE_ORDER.getCode(), id);
			}
		}
		transportOrderDetailsShipService.delete(id);
		super.delete(id);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@History(success = "{{#success}}", bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER)
	public void updateState(String id, Integer state) {
		super.findOne(id).ifPresent(transportOrderShip -> {

			if (TransportOrderShipDef.State.AWAITING_DEPARTURE.match(state)) {
				HistoryContext.putVariable("success",
						HistoryDef.SHIPPING_ORDER_APPLY_SAILING);
				// 船运单货主是第三方平台的
				if (Objects.nonNull(transportOrderShip.getAppId())) {
					// 开始发航后推送给三方
					handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
							.getCode(),
							ShippingCallbackDef.PushType.APPLY_SAILING
									.getCode(),
							transportOrderShip.getId());
				}
				// 船运单货主不是第三方平台的 需要给货主发信息
				else {
					// 平台发航申请后
					messageService.sendNotice(Messages.builder()
							.messageTypes(
									List.of(SendType.USERMESSAGE.getCode()))
							.type(UserMessageDef.MessageType.TRANSFORM
									.getCode())
							.title(MessageFormat.format(
									UserMessageConstants.REQUEST_DEPARTURE_TEMPLATE,
									transportOrderShip.getShipName(),
									transportOrderShip.getId()))
							.receiptors(List.of(String
									.valueOf(transportOrderShip.getOwnerId())))
							.url(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
							.detailId(
									String.valueOf(transportOrderShip.getId()))
							.initiator(UserMessageDef.BusinessInitiator.initiate
									.getCode())
							.build());

					// 发送app推送
					messageService.sendNotice(Messages.builder()
							.messageTypes(
									List.of(SendType.PUSH_MESSAGE.getCode()))
							.type(UserMessageDef.MessageType.TRANSFORM
									.getCode())
							.title(UserMessageConstants.TRANSPORT_TITLE)
							.content(MessageFormat.format(
									UserMessageConstants.REQUEST_DEPARTURE_TEMPLATE,
									transportOrderShip.getShipName(),
									transportOrderShip.getId()))
							.appTypes(List.of(AppType.LIANYUN))
							.bizNo(String.valueOf(transportOrderShip.getId()))
							.moduleType(
									UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
							.receiptors(List.of(String
									.valueOf(transportOrderShip.getOwnerId())))
							.build());
				}
			} else if (TransportOrderShipDef.State.DISCHARGED.match(state)) {
				HistoryContext.putVariable("success",
						HistoryDef.SHIPPING_ORDER_COMPLETE_UNLOADING);
				// 船运单货主是第三方平台的
				if (Objects.nonNull(transportOrderShip.getAppId())) {
					// 确认装货后推送给三方
					handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
							.getCode(),
							ShippingCallbackDef.PushType.CONFIRM_UNLOAD
									.getCode(),
							transportOrderShip.getId());
				}
				// 船运单货主不是第三方平台的 则需要给货主发信息
				else {
					// 平台点确认卸货后
					messageService.sendNotice(Messages.builder()
							.messageTypes(
									List.of(SendType.USERMESSAGE.getCode()))
							.type(UserMessageDef.MessageType.TRANSFORM
									.getCode())
							.title(MessageFormat.format(
									UserMessageConstants.CLEAN_GOODS_TEMPLATE,
									transportOrderShip.getShipName()))
							.receiptors(List.of(String
									.valueOf(transportOrderShip.getOwnerId())))
							.url(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
							.detailId(
									String.valueOf(transportOrderShip.getId()))
							.initiator(UserMessageDef.BusinessInitiator.initiate
									.getCode())
							.build());

					// 发送app推送
					messageService.sendNotice(Messages.builder()
							.messageTypes(
									List.of(SendType.PUSH_MESSAGE.getCode()))
							.type(UserMessageDef.MessageType.TRANSFORM
									.getCode())
							.title(UserMessageConstants.TRANSPORT_TITLE)
							.content(MessageFormat.format(
									UserMessageConstants.CLEAN_GOODS_TEMPLATE,
									transportOrderShip.getShipName()))
							.appTypes(List.of(AppType.LIANYUN))
							.bizNo(String.valueOf(transportOrderShip.getId()))
							.moduleType(
									UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
							.receiptors(List.of(String
									.valueOf(transportOrderShip.getOwnerId())))
							.build());
				}
				// 企微消息
				messageService.sendNotice(WxwMessage.builder()
						.receiptors(userService
								.findUsersByPermission(
										AdminPermissionDef.SHIP_DEMAND_MANAGE,
										null)
								.stream()
								.map(user -> String.valueOf(user.getId()))
								.toList())
						.url("/logistics/shippingNeed/waybillManagementInfo/"
								.concat(id))
						.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
						.operationModule(
								WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
										.getDesc())
						.desc("待完成").keyword(id).content(StringUtils.EMPTY)
						.build());
			} else if (TransportOrderShipDef.State.DURING_LOADING
					.match(state)) {
				HistoryContext.putVariable("success",
						HistoryDef.SHIPPING_ORDER_START_LOADING);
				transportOrderShip.setStartLoadingTime(LocalDateTime.now());
				if (Objects
						.nonNull(transportOrderShip.getUpstreamHandlerId())) {
					messageService.sendNotice(WxwMessage.builder()
							.receiptors(List.of(String.valueOf(
									transportOrderShip.getUpstreamHandlerId())))
							.url("/logistics/shippingNeed/waybillManagementInfo/"
									.concat(transportOrderShip.getId()))
							.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
							.operationModule(
									WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
											.getDesc())
							.desc("需发航申请").keyword(transportOrderShip.getId())
							.content(StringUtils.EMPTY).build());
					// 船运单货主是第三方平台的
					if (Objects.nonNull(transportOrderShip.getAppId())) {
						// 开始装货后推送给三方
						handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
								.getCode(),
								ShippingCallbackDef.PushType.START_LOADING
										.getCode(),
								transportOrderShip.getId());
					}
				}

			} else if (TransportOrderShipDef.State.COMPLETED.match(state)) {
				HistoryContext.putVariable("success",
						HistoryDef.SHIPPING_ORDER_COMPLETE);

				// 船运单货主是第三方平台的
				if (Objects.nonNull(transportOrderShip.getAppId())) {
					// 完成船运单后推送给三方
					handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
							.getCode(),
							ShippingCallbackDef.PushType.COMPLETE_ORDER
									.getCode(),
							transportOrderShip.getId());
				}

				// 保存历史轨迹
				ThreadPoolUtil.submitTask(
						() -> this.handleAisTrack(transportOrderShip.getId()),
						ThreadPoolUtil.getShipExecutor());
			}
			transportOrderShip.setState(state);
			super.updateAllProperties(transportOrderShip);
		});
	}

	@Override
	public void updateStateByIds(List<String> ids, Integer state) {
		if (CollectionUtils.isEmpty(ids)) {
			return;
		}
		LambdaUpdateWrapper<TransportOrderShip> updateWrapper = Wrappers
				.lambdaUpdate(TransportOrderShip.class)
				.set(TransportOrderShip::getState, state)
				.in(TransportOrderShip::getId, ids);
		repository.update(updateWrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@History(success = HistoryDef.SHIPPING_ORDER_CONFIRM_DEPARTURE, bizNo = "{{#transportOrderDetailsShip.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER)
	public void adminConfirmationDeparture(
			TransportOrderDetailsShip transportOrderDetailsShip) {
		this.confirmationDeparture(transportOrderDetailsShip);

	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void confirmationDeparture(
			TransportOrderDetailsShip transportOrderDetailsShip) {
		// 修改发航信息
		transportOrderDetailsShip.setOwnerSetSailTime(LocalDateTime.now());
		transportOrderDetailsShip.setDrainageTime(LocalDateTime.now());
		transportOrderDetailsShipService
				.updateAllProperties(transportOrderDetailsShip);

		DeparturePreparationInfo lazbInfo = transportOrderDetailsShip
				.getLazbInfo();

		// 是否排水
		Integer drainage = transportOrderDetailsShip.getDrainage();
		super.findOne(transportOrderDetailsShip.getId())
				.ifPresent(transportOrderShip -> {
					// 判断是否上传了发船信息
					if (Objects.isNull(lazbInfo)
							|| Objects.isNull(lazbInfo.getLoadVideoId())) {
						transportOrderShip.setIsSailConfirm(
								CommonDef.Symbol.YES.getCode());
						super.updateAllProperties(transportOrderShip);
					} else {
						transportOrderShip.setIsSailConfirm(
								CommonDef.Symbol.YES.getCode());
						this.handleConfirmationDeparture(transportOrderShip,
								drainage);
					}
				});

		TransportOrderShip transportOrderShip = super.findOne(
				transportOrderDetailsShip.getId()).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			if (Objects.nonNull(transportOrderShip.getAppId())) {
				// 管理后台代替货主发航确认推送给第三方
				handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
						.getCode(),
						ShippingCallbackDef.PushType.CONFIRM_DEPARTURE
								.getCode(),
						transportOrderShip.getId());
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@History(success = HistoryDef.SHIPPING_ORDER_AGREE_UNLOAD, bizNo = "{{#transportOrderDetailsShip.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER)
	public void adminAgreeUnload(
			TransportOrderDetailsShip transportOrderDetailsShip) {
		this.agreeUnload(transportOrderDetailsShip);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void agreeUnload(
			TransportOrderDetailsShip transportOrderDetailsShip) {
		transportOrderDetailsShip.setOwnerUnloadingTime(LocalDateTime.now());
		transportOrderDetailsShipService
				.updateAllProperties(transportOrderDetailsShip);

		// 卸货信息
		UnloadingInfo unloadingInfo = transportOrderDetailsShip
				.getUnloadingInfo();

		super.findOne(transportOrderDetailsShip.getId())
				.ifPresent(transportOrderShip -> {
					// 判断是否上传了卸货信息
					// 卸货图片或视频为空
					if (Objects.isNull(unloadingInfo) || Objects
							.isNull(unloadingInfo.getUnloadingVideoId())) {
						transportOrderShip.setIsAgreeUnloading(
								CommonDef.Symbol.YES.getCode());
						super.updateAllProperties(transportOrderShip);
					} else {
						transportOrderShip.setIsAgreeUnloading(
								CommonDef.Symbol.YES.getCode());
						this.handleAgreeUnload(transportOrderShip);
					}
				});
		TransportOrderShip transportOrderShip = super.findOne(
				transportOrderDetailsShip.getId()).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			if (Objects.nonNull(transportOrderShip.getAppId())) {
				// 管理后台代替货主同意卸货推送给第三方
				handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
						.getCode(),
						ShippingCallbackDef.PushType.AGREE_UNLOAD.getCode(),
						transportOrderShip.getId());
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@History(success = HistoryDef.SHIPPING_ORDER_CONFIRM_ARRIVAL_PORT, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER)
	public void adminConfirmationArrivalPort(String id, Long arrivalVideoId) {
		this.confirmationArrivalPort(id, arrivalVideoId);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void confirmationArrivalPort(String id, Long arrivalVideoId) {
		TransportOrderDetailsShip transportOrderDetailsShip = new TransportOrderDetailsShip();
		transportOrderDetailsShip.setId(id);
		transportOrderDetailsShip.setArrivalVideoId(arrivalVideoId);
		transportOrderDetailsShip.setCaptainArrivalTime(LocalDateTime.now());
		transportOrderDetailsShipService.update(transportOrderDetailsShip);
		TransportOrderShip transportOrderShip = super.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			transportOrderShip.setState(
					TransportOrderShipDef.State.TO_BE_UNLOADED.getCode());
			super.updateAllProperties(transportOrderShip);

			// 船运单货主是第三方平台的
			if (Objects.nonNull(transportOrderShip.getAppId())) {
				// 到港确认后推送船运单信息给第三方
				handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
						.getCode(),
						ShippingCallbackDef.PushType.CONFIRM_ARRIVAL_PORT
								.getCode(),
						transportOrderShip.getId());

			}

			// 船运单货主不是第三方平台的
			if (Objects.isNull(transportOrderShip.getAppId())) {
				// 船主到港确认后
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.USERMESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.ARRIVE_AT_PORT_TEMPLATE,
								transportOrderShip.getShipName(),
								transportOrderShip.getId()))
						.receiptors(List.of(String
								.valueOf(transportOrderShip.getOwnerId())))
						.url(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
						.detailId(String.valueOf(transportOrderShip.getId()))
						.initiator(UserMessageDef.BusinessInitiator.initiate
								.getCode())
						.build());
				// 发送app推送
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(UserMessageConstants.TRANSPORT_TITLE)
						.content(MessageFormat.format(
								UserMessageConstants.ARRIVE_AT_PORT_TEMPLATE,
								transportOrderShip.getShipName(),
								transportOrderShip.getId()))
						.appTypes(List.of(AppType.LIANYUN))
						.bizNo(String.valueOf(transportOrderShip.getId()))
						.moduleType(
								UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
						.receiptors(List.of(String
								.valueOf(transportOrderShip.getOwnerId())))
						.build());
			}

			if (Objects.nonNull(transportOrderShip.getDownstreamHandlerId())) {
				messageService.sendNotice(WxwMessage.builder()
						.receiptors(List.of(String.valueOf(
								transportOrderShip.getDownstreamHandlerId())))
						.url("/logistics/shippingNeed/waybillManagementInfo/"
								.concat(id))
						.prefix(WxwDef.NoticePrefix.YOU_FOLLOW_UP.getDesc())
						.operationModule(
								WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
										.getDesc())
						.desc("已到港").keyword(id).content(StringUtils.EMPTY)
						.build());
			}
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@History(success = "{{#success}}", bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER, kvParam = {
			@History.KeyValuePair(key = "#upHandlerName#", value = "{{#upHandlerName!=null?#upHandlerName:''}}"),
			@History.KeyValuePair(key = "#downHandlerName#", value = "{{#downHandlerName!=null?#downHandlerName:''}}") })
	public void assign(String id, Long upstreamHandlerId,
			Long downstreamHandlerId) {
		String upHandlerName = "";
		String downHandlerName = "";
		Set<Long> handelIdSet = new HashSet<>();
		if (Objects.nonNull(upstreamHandlerId)) {
			handelIdSet.add(upstreamHandlerId);
		}
		if (Objects.nonNull(downstreamHandlerId)) {
			handelIdSet.add(downstreamHandlerId);
		}
		this.findOne(id).ifPresent(transportOrderShip -> {
			Set<Long> oldHandelIdSet = new HashSet<>();
			Long up = transportOrderShip.getUpstreamHandlerId();
			if (Objects.nonNull(up)) {
				oldHandelIdSet.add(up);
			}
			Long down = transportOrderShip.getDownstreamHandlerId();
			if (Objects.nonNull(down)) {
				oldHandelIdSet.add(down);
			}
			handelIdSet.removeAll(oldHandelIdSet);
			if (CollectionUtils.isNotEmpty(handelIdSet)) {
				messageService.sendNotice(WxwMessage.builder()
						.receiptors(handelIdSet.stream().map(String::valueOf)
								.toList())
						.url("/logistics/shippingNeed/waybillManagementInfo/"
								.concat(id))
						.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
						.operationModule(
								WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
										.getDesc())
						.desc("待跟进").keyword(id).content(StringUtils.EMPTY)
						.build());
			}
		});
		if (Objects.nonNull(upstreamHandlerId)) {
			// 获取上游专员信息
			Optional<User> userOp = userService.findOne(upstreamHandlerId);
			if (userOp.isPresent()) {
				upHandlerName = userOp.get().getName();
				HistoryContext.putVariable("upHandlerName",
						userOp.get().getName());
			}
			this.findOne(id).ifPresent(transportOrderShip -> {
				if (upstreamHandlerId
						.equals(transportOrderShip.getUpstreamHandlerId())) {
					return;
				}
				transportOrderShip.setUpstreamHandlerId(upstreamHandlerId);
				userOp.ifPresent(user -> transportOrderShip
						.setUpstreamHandlerName(user.getName()));
				super.updateAllProperties(transportOrderShip);
				// 插入记录到指派表
				Assign assign = new Assign();
				assign.setCorrelationId(Long.valueOf(id));
				assign.setUserId(upstreamHandlerId);
				assign.setType(
						AssignDef.Type.TRANSPORT_ORDER_SHIP_UPSTREAM.getCode());
				assignService.create(assign);
			});
		}
		if (Objects.nonNull(downstreamHandlerId)) {
			// 获取下游专员信息
			Optional<User> userOp = userService.findOne(downstreamHandlerId);
			if (userOp.isPresent()) {
				downHandlerName = userOp.get().getName();
				HistoryContext.putVariable("downHandlerName",
						userOp.get().getName());
			}
			userOp.ifPresent(user -> HistoryContext
					.putVariable("downHandlerName", user.getName()));
			this.findOne(id).ifPresent(transportOrderShip -> {
				if (downstreamHandlerId
						.equals(transportOrderShip.getDownstreamHandlerId())) {
					return;
				}

				transportOrderShip.setDownstreamHandlerId(downstreamHandlerId);
				userOp.ifPresent(user -> transportOrderShip
						.setDownstreamHandlerName(user.getName()));
				super.updateAllProperties(transportOrderShip);
				// 插入记录到指派表
				Assign assign = new Assign();
				assign.setCorrelationId(Long.valueOf(id));
				assign.setUserId(downstreamHandlerId);
				assign.setType(AssignDef.Type.TRANSPORT_ORDER_SHIP_DOWNSTREAM
						.getCode());
				assignService.create(assign);
			});
		}
		if (StringUtils.isNotBlank(upHandlerName)
				&& StringUtils.isNotBlank(downHandlerName)) {
			HistoryContext.putVariable("success",
					HistoryDef.SHIPPING_ORDER_ASSIGN);
		}
		if (StringUtils.isNotBlank(upHandlerName)
				&& StringUtils.isBlank(downHandlerName)) {
			HistoryContext.putVariable("success",
					HistoryDef.SHIPPING_ORDER_ASSIGN1);
		}
		if (StringUtils.isBlank(upHandlerName)
				&& StringUtils.isNotBlank(downHandlerName)) {
			HistoryContext.putVariable("success",
					HistoryDef.SHIPPING_ORDER_ASSIGN2);
		}
	}

	@Override
	public TransportOrderShipCountVo staticsTransportOrderShip(Long userId,
			Boolean hasDeal, Boolean hasFull) {

		TransportOrderShipCountVo transportOrderShipCountVo = new TransportOrderShipCountVo();
		transportOrderShipCountVo.setWaitFinishedCount(0L);
		transportOrderShipCountVo.setToBeLoadedCount(0L);
		transportOrderShipCountVo.setAwaitingDepartureCount(0L);
		transportOrderShipCountVo.setToBeUnloadedCount(0L);
		transportOrderShipCountVo.setWaitFinishedCount(0L);

		LambdaQueryWrapper<TransportOrderShip> queryWrapper = Wrappers
				.lambdaQuery(TransportOrderShip.class);

		if (hasDeal) {
			queryWrapper.clear();
			// 1. 统计 TO_BE_LOADED(1, "待装货")
			queryWrapper
					.eq(TransportOrderShip::getState,
							TransportOrderShipDef.State.TO_BE_LOADED.getCode())
					.eq(Objects.nonNull(userId),
							TransportOrderShip::getUpstreamHandlerId, userId)
					.eq(TransportOrderShip::getDel,
							CommonDef.Symbol.NO.getCode());
			Long toBeLoadedCount = repository.selectCount(queryWrapper);
			transportOrderShipCountVo.setToBeLoadedCount(toBeLoadedCount);
			queryWrapper.clear();

			// 2. 统计 AWAITING_DEPARTURE(3,"待发航")
			queryWrapper
					.eq(TransportOrderShip::getState,
							TransportOrderShipDef.State.AWAITING_DEPARTURE
									.getCode())
					.eq(Objects.nonNull(userId),
							TransportOrderShip::getUpstreamHandlerId, userId)
					.eq(TransportOrderShip::getDel,
							CommonDef.Symbol.NO.getCode());
			Long awaitingDepartureCount = repository.selectCount(queryWrapper);
			transportOrderShipCountVo
					.setAwaitingDepartureCount(awaitingDepartureCount);
			queryWrapper.clear();

			// 3. 统计 TO_BE_UNLOADED(5, "待卸货")
			queryWrapper
					.eq(TransportOrderShip::getState,
							TransportOrderShipDef.State.TO_BE_UNLOADED
									.getCode())
					.eq(Objects.nonNull(userId),
							TransportOrderShip::getDownstreamHandlerId, userId)
					.eq(TransportOrderShip::getDel,
							CommonDef.Symbol.NO.getCode());
			Long toBeUnLoadedCount = repository.selectCount(queryWrapper);
			transportOrderShipCountVo.setToBeUnloadedCount(toBeUnLoadedCount);
			queryWrapper.clear();
		}

		if (hasFull) {
			// 4. 统计 DISCHARGED(6, "已经卸货")
			queryWrapper
					.eq(TransportOrderShip::getState,
							TransportOrderShipDef.State.DISCHARGED.getCode())
					.eq(TransportOrderShip::getDel,
							CommonDef.Symbol.NO.getCode());
			Long waitFinishedCount = repository.selectCount(queryWrapper);
			transportOrderShipCountVo.setWaitFinishedCount(waitFinishedCount);
			queryWrapper.clear();
		}

		return transportOrderShipCountVo;
	}

	@Override
	public List<HotShipRouteVo> hotShipRouteRank(LocalDateTime beginTime,
			LocalDateTime endTime) {
		return repository.hotShipRouteRank(beginTime, endTime);
	}

	@Override
	public List<HotShipDestinationPortVo> hotShipDestinationPortRank(
			LocalDateTime beginTime, LocalDateTime endTime) {
		return repository.hotShipDestinationPortRank(beginTime, endTime);
	}

	/**
	 * 船主确认定金
	 *
	 * @param transportOrderShip
	 */
	@Override
	public void confirm(TransportOrderShip transportOrderShip) {
		// 判断货主和船主服务费是否为0 都为零则直接到待装货状态 否则到待支付船务信息服务费状态
		if (transportOrderShip.getCaptainShipInfoServiceFee()
				.compareTo(BigDecimal.ZERO) == 0) {
			transportOrderShip.setCaptainShipInfoServiceFeeState(
					ShipInfoServiceFeeDef.State.SUCCESS.getCode());
		} else {
			transportOrderShip.setCaptainShipInfoServiceFeeState(
					ShipInfoServiceFeeDef.State.UNPAID.getCode());
		}
		if (transportOrderShip.getOwnerShipInfoServiceFee()
				.compareTo(BigDecimal.ZERO) == 0) {
			transportOrderShip.setOwnerShipInfoServiceFeeState(
					ShipInfoServiceFeeDef.State.SUCCESS.getCode());
		} else {
			transportOrderShip.setOwnerShipInfoServiceFeeState(
					ShipInfoServiceFeeDef.State.UNPAID.getCode());
		}
		if (transportOrderShip.getOwnerShipInfoServiceFee()
				.compareTo(BigDecimal.ZERO) == 0
				&& transportOrderShip.getCaptainShipInfoServiceFee()
						.compareTo(BigDecimal.ZERO) == 0) {
			transportOrderShip.setState(
					TransportOrderShipDef.State.TO_BE_LOADED.getCode());
			// 船主与货主的信息服务费均为0，船主确认收到定金后 上游专员接收信息
			if (Objects.nonNull(transportOrderShip.getUpstreamHandlerId())) {
				messageService.sendNotice(WxwMessage.builder()
						.receiptors(List.of(String.valueOf(
								transportOrderShip.getUpstreamHandlerId())))
						.url("/logistics/shippingNeed/waybillManagementInfo/"
								.concat(transportOrderShip.getId()))
						.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
						.operationModule(
								WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
										.getDesc())
						.desc("待装货").keyword(transportOrderShip.getId())
						.content(StringUtils.EMPTY).build());
			}
		} else {
			transportOrderShip.setState(
					TransportOrderShipDef.State.SHIP_INFO_FEE_TO_BE_PAID
							.getCode());

			// 发生相关通知--待支付服务信息费短信
			if (TransportOrderShipDef.State.SHIP_INFO_FEE_TO_BE_PAID
					.match(transportOrderShip.getState())) {
				// 货主不是三方需求的情况下需要发信息
				if (Objects.isNull(transportOrderShip.getAppId())) {
					// 船运单状态为待支付信息服务费且货主未支付
					if (ShipInfoServiceFeeDef.State.UNPAID
							.match(transportOrderShip
									.getOwnerShipInfoServiceFeeState())) {
						messageService.sendNotice(Messages.builder()
								.messageTypes(
										List.of(SendType.ALIMESSAGE.getCode(),
												SendType.USERMESSAGE.getCode()))
								.type(UserMessageDef.MessageType.TRANSFORM
										.getCode())
								.title(MessageFormat.format(
										UserMessageConstants.NEED_PAY_SERVICE_FEE_OWNER_TEMPLATE,
										transportOrderShip.getId()))
								.receiptors(List.of(String.valueOf(
										transportOrderShip.getOwnerId())))
								.url(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
								.detailId(String
										.valueOf(transportOrderShip.getId()))
								.initiator(
										UserMessageDef.BusinessInitiator.initiate
												.getCode())
								.templateCode(wxSubscriptionProperties
										.getOwnerPayShippingServiceFeeCode())
								.params(Map.of("index",
										transportOrderShip.getId()))
								.build());

						// 发送app推送
						messageService.sendNotice(Messages.builder()
								.messageTypes(List
										.of(SendType.PUSH_MESSAGE.getCode()))
								.type(UserMessageDef.MessageType.TRANSFORM
										.getCode())
								.title(UserMessageConstants.TRANSPORT_TITLE)
								.content(MessageFormat.format(
										UserMessageConstants.NEED_PAY_SERVICE_FEE_OWNER_TEMPLATE,
										transportOrderShip.getId()))
								.appTypes(List.of(AppType.LIANYUN))
								.bizNo(String
										.valueOf(transportOrderShip.getId()))
								.moduleType(
										UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
								.receiptors(List.of(String.valueOf(
										transportOrderShip.getOwnerId())))
								.build());
					}
				}
				// 船运单状态为待支付信息服务费且船主未支付
				if (ShipInfoServiceFeeDef.State.UNPAID.match(transportOrderShip
						.getCaptainShipInfoServiceFeeState())) {
					messageService.sendNotice(Messages.builder()
							.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
									SendType.USERMESSAGE.getCode()))
							.type(UserMessageDef.MessageType.TRANSFORM
									.getCode())
							.title(MessageFormat.format(
									UserMessageConstants.NEED_PAY_SERVICE_FEE_TEMPLATE,
									transportOrderShip.getId()))
							.receiptors(List.of(String.valueOf(
									transportOrderShip.getCaptainId())))
							.url(UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
							.role(AppTypeDef.AppType.CHUAN_WU.getCode())
							.detailId(
									String.valueOf(transportOrderShip.getId()))
							.initiator(UserMessageDef.BusinessInitiator.initiate
									.getCode())
							.templateCode(wxSubscriptionProperties
									.getCaptainPayShippingServiceFeeCode())
							.params(Map.of("index", transportOrderShip.getId()))
							.mobile(transportOrderShip.getCaptainEnterprise()
									.getMobile())
							.build());

					// 发送app推送
					messageService.sendNotice(Messages.builder()
							.messageTypes(
									List.of(SendType.PUSH_MESSAGE.getCode()))
							.type(UserMessageDef.MessageType.TRANSFORM
									.getCode())
							.title(UserMessageConstants.TRANSPORT_TITLE)
							.content(MessageFormat.format(
									UserMessageConstants.NEED_PAY_SERVICE_FEE_TEMPLATE,
									transportOrderShip.getId()))
							.appTypes(List.of(AppType.SHIP))
							.bizNo(String.valueOf(transportOrderShip.getId()))
							.moduleType(
									UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
							.receiptors(List.of(String.valueOf(
									transportOrderShip.getCaptainId())))
							.build());
				}
			}
		}

		super.update(transportOrderShip);
	}

	// 船运单 船主选择 船运定金 收款账户
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateBank(TransportOrderShip transportOrderShip, Long bankId) {
		// 查询出客户银行账户信息
		CustomerBank customerBank = customerBankService.findOne(bankId)
				.orElse(null);
		CustomerBankInfo customerBankInfo = new CustomerBankInfo();
		if (Objects.nonNull(customerBank)) {
			BeanUtils.copyProperties(customerBank, customerBankInfo);
		}
		// 船运单更新船主收款银行账户id，船主收款银行账户信息
		transportOrderShip.setCaptainBankId(bankId);
		transportOrderShip.setCaptainBankInfo(customerBankInfo);
		super.updateAllProperties(transportOrderShip);

		if (Objects.nonNull(transportOrderShip.getAppId())) {
			handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
					.getCode(),
					ShippingCallbackDef.PushType.UPLOAD_BANK_INFO.getCode(),
					transportOrderShip.getId());
		}

	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@History(success = HistoryDef.SHIPPING_ORDER_START_LOADING, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_ORDER)
	public void startLoading(String id,
			TransportOrderDetailsShip transportOrderDetailsShip) {
		this.updateState(id,
				TransportOrderShipDef.State.DURING_LOADING.getCode());
		transportOrderDetailsShipService
				.updateAllProperties(transportOrderDetailsShip);
	}

	@Override
	public void handleConfirmationDeparture(
			TransportOrderShip transportOrderShip, Integer drainage) {
		transportOrderShip.setState(
				TransportOrderShipDef.State.DURING_TRANSPORTATION.getCode());

		switch (CommonDef.Symbol.from(drainage)) {
			case YES -> {
				// 货主确认发航后，选择了排水
				// 货主确认发航后
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.USERMESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.DRAINAGE_TEMPLATE,
								transportOrderShip.getShipName()))
						.receiptors(List.of(String
								.valueOf(transportOrderShip.getCaptainId())))
						.url(UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
						.role(AppTypeDef.AppType.CHUAN_WU.getCode())
						.detailId(String.valueOf(transportOrderShip.getId()))
						.initiator(UserMessageDef.BusinessInitiator.initiate
								.getCode())
						.mobile(transportOrderShip.getCaptainEnterprise()
								.getMobile())
						.build());

				// 发送app推送
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(UserMessageConstants.TRANSPORT_TITLE)
						.content(MessageFormat.format(
								UserMessageConstants.DRAINAGE_TEMPLATE,
								transportOrderShip.getShipName()))
						.appTypes(List.of(AppType.SHIP))
						.bizNo(String.valueOf(transportOrderShip.getId()))
						.moduleType(
								UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
						.receiptors(List.of(String
								.valueOf(transportOrderShip.getCaptainId())))
						.build());
			}
			case NO -> {
				// 运费结算节点不是待发航，货主确认发航后，未选择排水
				// 货主确认发航后
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.USERMESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.SHIP_CAN_DEPARTURE_TEMPLATE,
								transportOrderShip.getShipName()))
						.receiptors(List.of(String
								.valueOf(transportOrderShip.getCaptainId())))
						.url(UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
						.role(AppTypeDef.AppType.CHUAN_WU.getCode())
						.detailId(String.valueOf(transportOrderShip.getId()))
						.initiator(UserMessageDef.BusinessInitiator.initiate
								.getCode())
						.mobile(transportOrderShip.getCaptainEnterprise()
								.getMobile())
						.build());

				// 发送app推送
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(UserMessageConstants.TRANSPORT_TITLE)
						.content(MessageFormat.format(
								UserMessageConstants.SHIP_CAN_DEPARTURE_TEMPLATE,
								transportOrderShip.getShipName()))
						.appTypes(List.of(AppType.SHIP))
						.bizNo(String.valueOf(transportOrderShip.getId()))
						.moduleType(
								UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
						.receiptors(List.of(String
								.valueOf(transportOrderShip.getCaptainId())))
						.build());
			}
		}

		super.updateAllProperties(transportOrderShip);

		if (Objects.nonNull(transportOrderShip.getUpstreamHandlerId())) {
			messageService.sendNotice(WxwMessage.builder()
					.receiptors(List.of(String.valueOf(
							transportOrderShip.getUpstreamHandlerId())))
					.url("/logistics/shippingNeed/waybillManagementInfo/"
							.concat(transportOrderShip.getId()))
					.prefix(WxwDef.NoticePrefix.YOU_FOLLOW_UP.getDesc())
					.operationModule(
							WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
									.getDesc())
					.desc("货主已同意发航").keyword(transportOrderShip.getId())
					.content(StringUtils.EMPTY).build());
		}

	}

	@Override
	public void handleAgreeUnload(TransportOrderShip transportOrderShip) {

		transportOrderShip.setState(
				TransportOrderShipDef.State.DURING_UNLOADING.getCode());

		super.updateAllProperties(transportOrderShip);

		// 货主确认开仓后
		messageService.sendNotice(Messages.builder()
				.messageTypes(List.of(SendType.USERMESSAGE.getCode()))
				.type(UserMessageDef.MessageType.TRANSFORM.getCode())
				.title(MessageFormat.format(
						UserMessageConstants.UNLOAD_GOODS_TEMPLATE,
						transportOrderShip.getShipName(),
						transportOrderShip.getId()))
				.receiptors(List
						.of(String.valueOf(transportOrderShip.getCaptainId())))
				.url(UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
				.role(AppTypeDef.AppType.CHUAN_WU.getCode())
				.detailId(String.valueOf(transportOrderShip.getId()))
				.initiator(UserMessageDef.BusinessInitiator.initiate.getCode())
				.mobile(transportOrderShip.getCaptainEnterprise().getMobile())
				.build());

		// 发送app推送
		messageService.sendNotice(Messages.builder()
				.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
				.type(UserMessageDef.MessageType.TRANSFORM.getCode())
				.title(UserMessageConstants.TRANSPORT_TITLE)
				.content(MessageFormat.format(
						UserMessageConstants.UNLOAD_GOODS_TEMPLATE,
						transportOrderShip.getShipName(),
						transportOrderShip.getId()))
				.appTypes(List.of(AppType.SHIP))
				.bizNo(String.valueOf(transportOrderShip.getId()))
				.moduleType(
						UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
				.receiptors(List
						.of(String.valueOf(transportOrderShip.getCaptainId())))
				.build());

		if (Objects.nonNull(transportOrderShip.getDownstreamHandlerId())) {
			messageService.sendNotice(WxwMessage.builder()
					.receiptors(List.of(String.valueOf(
							transportOrderShip.getDownstreamHandlerId())))
					.url("/logistics/shippingNeed/waybillManagementInfo/"
							.concat(transportOrderShip.getId()))
					.prefix(WxwDef.NoticePrefix.YOU_FOLLOW_UP.getDesc())
					.operationModule(
							WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
									.getDesc())
					.desc("货主已同意卸货").keyword(transportOrderShip.getId())
					.content(StringUtils.EMPTY).build());
		}
	}

	/**
	 * 组装vo
	 *
	 * @param transportOrderShipList
	 * @return
	 */
	private List<TransportOrderShipVo> packVo(
			List<TransportOrderShip> transportOrderShipList) {
		if (CollectionUtils.isEmpty(transportOrderShipList)) {
			return List.of();
		}
		List<String> idList = transportOrderShipList.stream()
				.map(TransportOrderShip::getId).toList();

		// 船主ID
		List<Long> captainIds = transportOrderShipList.stream()
				.map(TransportOrderShip::getCaptainId).distinct().toList();
		// 黄码港代开协议文件
		Map<Long, HmgFile> hmgFileMap = hmgFileService.getIdMap(captainIds);

		// 货主定金
		List<OwnerShippingDeposit> ownerShippingDepositList = ownerShippingDepositService
				.findByRelationCodes(idList);
		Map<String, List<OwnerShippingDeposit>> ownerShippingDepositMap = ownerShippingDepositList
				.stream().collect(Collectors
						.groupingBy(OwnerShippingDeposit::getRelationCode));

		Map<String, TransportOrderDetailsShip> detailsShipServiceIdMap = transportOrderDetailsShipService
				.getIdMap(idList);

		// 船舶id集合
		List<String> shipIds = transportOrderShipList.stream()
				.map(TransportOrderShip::getShipId).distinct().toList();

		// 将ShipVo列表转换为Map
		Map<String, ShipVo> shipVoMap = shipService.findVoByIds(shipIds)
				.stream()
				.filter(item -> StringUtils.isNotBlank(item.getShip().getId()))
				.collect(Collectors.toMap(shipVo -> shipVo.getShip().getId(),
						shipVo -> shipVo));

		List<TransportOrderShipVo> transportOrderShipVoList = new ArrayList<>();
		for (TransportOrderShip transportOrderShip : transportOrderShipList) {
			TransportOrderShipVo transportOrderShipVo = new TransportOrderShipVo();
			transportOrderShipVo.setTransportOrderShip(transportOrderShip);
			transportOrderShipVo.setTransportOrderDetailsShip(
					detailsShipServiceIdMap.get(transportOrderShip.getId()));

			if (Objects.nonNull(transportOrderShip.getShipId())) {
				ShipVo shipVo = shipVoMap.get(transportOrderShip.getShipId());
				transportOrderShipVo.setShipVo(shipVo);
			}
			if (Objects.nonNull(transportOrderShip.getCaptainId())) {
				transportOrderShipVo.setHmgFile(
						hmgFileMap.get(transportOrderShip.getCaptainId()));
			}
			// 货主定金
			transportOrderShipVo.setOwnerShippingDeposits(
					ownerShippingDepositMap.get(transportOrderShip.getId()));
			transportOrderShipVoList.add(transportOrderShipVo);
		}
		return transportOrderShipVoList;
	}

	/**
	 * 组装vo
	 *
	 * @param transportOrderShipList
	 * @return
	 */
	private List<TransportOrderShipVo> packCustomerVo(
			List<TransportOrderShip> transportOrderShipList,
			Integer queryType) {
		List<TransportOrderShipVo> transportOrderShipVoList = this
				.packVo(transportOrderShipList);
		for (TransportOrderShipVo transportOrderShipVo : transportOrderShipVoList) {
			switch (TransportOrderShipDef.QueryType.from(queryType)) {
				case SHIPPER -> this.setCaptainOrderState(
						transportOrderShipVo.getTransportOrderShip(),
						transportOrderShipVo);
				case SHIPOWNER -> this.setOwnerOrderState(
						transportOrderShipVo.getTransportOrderShip(),
						transportOrderShipVo);
			}
		}
		return transportOrderShipVoList
				.stream().sorted(
						Comparator
								.comparing(TransportOrderShipVo::getOrderState,
										Comparator.nullsLast(
												Comparator.naturalOrder()))
								.thenComparing(
										item -> item.getTransportOrderShip()
												.getCreatedTime(),
										Comparator.nullsLast(
												Comparator.reverseOrder())))
				.toList();
	}

	/**
	 * 设置货主状态
	 *
	 * @param transportOrderShip
	 */
	private void setOwnerOrderState(TransportOrderShip transportOrderShip,
			TransportOrderShipVo transportOrderShipVo) {
		// 设置排序状态货主
		switch (TransportOrderShipDef.State
				.from(transportOrderShip.getState())) {
			// 待支付信息服务费
			case SHIP_INFO_FEE_TO_BE_PAID -> {
				// 待支付信息服务费 状态是待支付信息服务费 且货主未支付
				if (ShipInfoServiceFeeDef.State.UNPAID.match(
						transportOrderShip.getOwnerShipInfoServiceFeeState())) {
					transportOrderShipVo.setOrderState(
							TransportOrderShipDef.OwnerOrderState.SHIP_INFO_FEE_TO_BE_PAID
									.getCode());
				}
				// 服务费确认中 状态是待支付信息服务费 且货主已支付且货主待支付或待确认
				if (ShipInfoServiceFeeDef.State.SUCCESS.match(
						transportOrderShip.getOwnerShipInfoServiceFeeState())
						&& List.of(ShipInfoServiceFeeDef.State.UNPAID.getCode(),
								ShipInfoServiceFeeDef.State.UN_CONFIRM
										.getCode())
								.contains(transportOrderShip
										.getCaptainShipInfoServiceFeeState())) {
					transportOrderShipVo.setOrderState(
							TransportOrderShipDef.OwnerOrderState.SHIP_INFO_FEE_CONFIRMING
									.getCode());
				}
				// 服务费确认中 状态是待支付信息服务费 货主支付确认中
				if (ShipInfoServiceFeeDef.State.UN_CONFIRM.match(
						transportOrderShip.getOwnerShipInfoServiceFeeState())) {
					transportOrderShipVo.setOrderState(
							TransportOrderShipDef.OwnerOrderState.SHIP_INFO_FEE_CONFIRMING
									.getCode());
				}
			}
			case DEPOSIT_TO_BE_PAID -> {
				// 定金确认中
				switch (TransportOrderShipDef.DepositPayType
						.from(transportOrderShip.getPayType())) {
					case PAY_ONESELF ->
						// 定金确认中 自行支付
						transportOrderShipVo.setOrderState(
								TransportOrderShipDef.OwnerOrderState.DEPOSIT_CONFIRMING_PAY_ONESELF
										.getCode());
					// 定金确认中 服务商支付
					case PAY_ZH -> transportOrderShipVo.setOrderState(
							TransportOrderShipDef.OwnerOrderState.DEPOSIT_CONFIRMING_PAY_ZH
									.getCode());
				}
			}
			case TO_BE_LOADED -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.OwnerOrderState.TO_BE_LOADED
							.getCode());
			case DURING_LOADING -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.OwnerOrderState.DURING_LOADING
							.getCode());
			case AWAITING_DEPARTURE -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.OwnerOrderState.AWAITING_DEPARTURE
							.getCode());
			case DURING_TRANSPORTATION -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.OwnerOrderState.DURING_TRANSPORTATION
							.getCode());
			case TO_BE_UNLOADED -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.OwnerOrderState.TO_BE_UNLOADED
							.getCode());
			case DURING_UNLOADING -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.OwnerOrderState.DURING_UNLOADING
							.getCode());
			case DISCHARGED -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.OwnerOrderState.DISCHARGED.getCode());
			case COMPLETED -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.OwnerOrderState.COMPLETED.getCode());
		}
	}

	/**
	 * 设置船主排序状态
	 *
	 * @param transportOrderShip
	 */
	private void setCaptainOrderState(TransportOrderShip transportOrderShip,
			TransportOrderShipVo transportOrderShipVo) {
		switch (TransportOrderShipDef.State
				.from(transportOrderShip.getState())) {
			// 待支付信息服务费
			case SHIP_INFO_FEE_TO_BE_PAID -> {
				// 待支付信息服务费 状态是待支付信息服务费 且船主未支付
				if (ShipInfoServiceFeeDef.State.UNPAID.match(transportOrderShip
						.getCaptainShipInfoServiceFeeState())) {
					transportOrderShipVo.setOrderState(
							TransportOrderShipDef.CaptainOrderState.SHIP_INFO_FEE_TO_BE_PAID
									.getCode());
				}
				// 服务费确认中 状态是待支付信息服务费 且船主已支付且货主待支付或待确认
				if (ShipInfoServiceFeeDef.State.SUCCESS.match(
						transportOrderShip.getCaptainShipInfoServiceFeeState())
						&& List.of(ShipInfoServiceFeeDef.State.UNPAID.getCode(),
								ShipInfoServiceFeeDef.State.UN_CONFIRM
										.getCode())
								.contains(transportOrderShip
										.getOwnerShipInfoServiceFeeState())) {
					transportOrderShipVo.setOrderState(
							TransportOrderShipDef.CaptainOrderState.SHIP_INFO_FEE_CONFIRMING
									.getCode());
				}
				// 服务费确认中 状态是待支付信息服务费 船主支付确认中
				if (ShipInfoServiceFeeDef.State.UN_CONFIRM
						.match(transportOrderShip
								.getCaptainShipInfoServiceFeeState())) {
					transportOrderShipVo.setOrderState(
							TransportOrderShipDef.CaptainOrderState.SHIP_INFO_FEE_CONFIRMING
									.getCode());
				}
			}
			case DEPOSIT_TO_BE_PAID ->
				// 待收定金
				transportOrderShipVo.setOrderState(
						TransportOrderShipDef.CaptainOrderState.DEPOSIT_TO_BE_PAID
								.getCode());
			case TO_BE_LOADED -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.CaptainOrderState.TO_BE_LOADED
							.getCode());
			case DURING_LOADING -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.CaptainOrderState.DURING_LOADING
							.getCode());
			case AWAITING_DEPARTURE -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.CaptainOrderState.AWAITING_DEPARTURE
							.getCode());
			case DURING_TRANSPORTATION -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.CaptainOrderState.DURING_TRANSPORTATION
							.getCode());
			case TO_BE_UNLOADED -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.CaptainOrderState.TO_BE_UNLOADED
							.getCode());
			case DURING_UNLOADING -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.CaptainOrderState.DURING_UNLOADING
							.getCode());
			case DISCHARGED -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.CaptainOrderState.DISCHARGED
							.getCode());
			case COMPLETED -> transportOrderShipVo.setOrderState(
					TransportOrderShipDef.CaptainOrderState.COMPLETED
							.getCode());
		}
	}

	/**
	 * 组装数据
	 *
	 * @param transportOrderShip
	 * @return
	 */
	private TransportOrderShipVo packVo(TransportOrderShip transportOrderShip) {
		TransportOrderShipVo transportOrderShipVo = new TransportOrderShipVo();
		transportOrderShipVo.setTransportOrderShip(transportOrderShip);
		transportOrderDetailsShipService.findOne(transportOrderShip.getId())
				.ifPresent(transportOrderShipVo::setTransportOrderDetailsShip);

		if (Objects.nonNull(transportOrderShip.getShipId())) {
			shipService.findVoById(transportOrderShip.getShipId())
					.ifPresent(transportOrderShipVo::setShipVo);
		}
		// 设置黄码港代开
		if (Objects.nonNull(transportOrderShip.getCaptainId())) {
			hmgFileService.findOne(transportOrderShip.getCaptainId())
					.ifPresent(transportOrderShipVo::setHmgFile);
		}
		transportOrderShipVo
				.setOwnerShippingDeposits(ownerShippingDepositService
						.findByRelationCode(transportOrderShip.getId()));
		shipInfoServiceFeeService
				.findByRelationCodeAndType(transportOrderShip.getId(),
						ShipInfoServiceFeeDef.Type.OWNER.getCode())
				.ifPresent(transportOrderShipVo::setOwmerShipInfoServiceFee);
		shipInfoServiceFeeService
				.findByRelationCodeAndType(transportOrderShip.getId(),
						ShipInfoServiceFeeDef.Type.CAPTAIN.getCode())
				.ifPresent(transportOrderShipVo::setCaptainShipInfoServiceFee);
		return transportOrderShipVo;
	}

	/**
	 * 组装数据
	 *
	 * @param resource
	 */
	private void packData(TransportOrderShip resource) {
		// 船舶信息
		shipService.findOne(resource.getShipId()).ifPresent(ship -> {
			if (StringUtils.isNotBlank(ship.getCnname())) {
				resource.setShipName(ship.getCnname() + "/" + ship.getName());
			} else {
				resource.setShipName(ship.getName());
			}
			resource.setShipType(ship.getType());
		});
		// 货主信息-设置 企业名称 法人代表，统一社会信用代码
		customerService.findVoById(resource.getOwnerId())
				.ifPresent(customerVo -> {
					Enterprise ownerEnterprise = resource.getOwnerEnterprise();
					if (Objects.isNull(ownerEnterprise.getName())) {
						ownerEnterprise.setName(
								customerVo.getCustomer().getInstitutionName());
					}
					ownerEnterprise.setCode(customerVo.getCustomer().getCode());
					ownerEnterprise.setUnifiedSocialCreditCode(customerVo
							.getCustomer().getUnifiedSocialCreditCode());
					ownerEnterprise.setLegalRepresentative(
							customerVo.getCustomer().getLegalRepresentative());
					resource.setOwnerEnterprise(ownerEnterprise);
				});
		// 船主信息-设置 -企业名称 法人代表，统一社会信用代码
		customerService.findVoById(resource.getCaptainId())
				.ifPresent(customerVo -> {
					Enterprise captainEnterprise = resource
							.getCaptainEnterprise();
					captainEnterprise.setName(
							customerVo.getCustomer().getInstitutionName());
					captainEnterprise.setUnifiedSocialCreditCode(customerVo
							.getCustomer().getUnifiedSocialCreditCode());
					captainEnterprise.setLegalRepresentative(
							customerVo.getCustomer().getLegalRepresentative());
					if (Objects.isNull(captainEnterprise.getMobile())) {
						captainEnterprise.setMobile(
								customerVo.getCustomer().getMobile());
					}
					resource.setCaptainEnterprise(captainEnterprise);
				});
		if (Objects.nonNull(resource.getSourcePortId())) {
			portService.findOne(resource.getSourcePortId()).ifPresent(
					port -> resource.setSourcePortName(port.getShortName()));
		}
		if (Objects.nonNull(resource.getDestinationPortId())) {
			portService.findOne(resource.getDestinationPortId())
					.ifPresent(port -> resource
							.setDestinationPortName(port.getShortName()));
		}
		// 平台创建 设置航线信息
		if (TransportOrderShipDef.Type.PLATFORM_CREATION.match(
				resource.getType()) && Objects.nonNull(resource.getSrpId())) {
			shippingRequirementPlatService.findOne(resource.getSrpId())
					.ifPresent(shippingRequirementPlat -> {
						resource.setShipRouteId(
								shippingRequirementPlat.getShipRouteId());
						resource.setShipRouteSource(
								shippingRequirementPlat.getShipRouteSource());
						resource.setShipRouteDestination(shippingRequirementPlat
								.getShipRouteDestination());
					});
		}

		// 待支付定金状态
		resource.setState(
				TransportOrderShipDef.State.DEPOSIT_TO_BE_PAID.getCode());

		// 设置处理人
		if (Objects.nonNull(resource.getSrpId())) {
			shippingRequirementPlatService.findOne(resource.getSrpId())
					.ifPresent(shippingRequirementPlat -> {
						// 查询拥有服务专员角色的账号
						List<User> attaches = userService.findUsersByPermission(
								AdminPermissionDef.SHIP_ORDER_DEAL, null);
						if (CollectionUtils.isNotEmpty(attaches)) {
							List<Long> userIdList = attaches.stream()
									.map(User::getId).toList();
							if (userIdList.contains(
									shippingRequirementPlat.getHandlerId())) {
								Long handlerId = shippingRequirementPlat
										.getHandlerId();
								String handlerName = shippingRequirementPlat
										.getHandlerName();
								// 设置处理人
								if (Objects.nonNull(handlerId)) {
									resource.setUpstreamHandlerId(handlerId);
									resource.setDownstreamHandlerId(handlerId);
								}
								if (StringUtils.isNotBlank(handlerName)) {
									resource.setUpstreamHandlerName(
											handlerName);
									resource.setDownstreamHandlerName(
											handlerName);
								}
							}
						}
					});
		}
	}

	/**
	 * 封装服务专员Vo
	 *
	 * @param attaches
	 * @param wrapper
	 * @param handlerIdExtractor
	 * @return
	 */
	private List<ServiceSpecialVo> packServiceSpecialVo(List<User> attaches,
			LambdaQueryWrapper<TransportOrderShip> wrapper,
			Function<TransportOrderShip, Long> handlerIdExtractor) {
		List<TransportOrderShip> transportOrderShipList = repository
				.selectList(wrapper);
		Map<Long, Long> transportOrderShipMap = transportOrderShipList.stream()
				.collect(Collectors.groupingBy(handlerIdExtractor,
						Collectors.counting()));

		return attaches.stream().map(user -> {
			ServiceSpecialVo vo = new ServiceSpecialVo();
			vo.setUser(user);
			vo.setShipOrderCount(
					transportOrderShipMap.getOrDefault(user.getId(), 0L));
			return vo;
		}).sorted(Comparator.comparingLong(ServiceSpecialVo::getShipOrderCount)
				.reversed()).toList();
	}

	/**
	 * 增加服务费查询条件
	 *
	 * @param queryWrapper
	 * @param state
	 *            查询的状态
	 * @param isShipper
	 *            是否船主
	 * @param customerId
	 *            客户id
	 */
	private void addServiceFeeConditions(
			LambdaQueryWrapper<TransportOrderShip> queryWrapper, Integer state,
			boolean isShipper, Long customerId) {
		if (Objects.nonNull(state)) {
			switch (TransportOrderShipDef.State.from(state)) {
				// 待支付服务费
				case SHIP_INFO_FEE_TO_BE_PAID -> {
					queryWrapper.eq(TransportOrderShip::getState,
							TransportOrderShipDef.State.SHIP_INFO_FEE_TO_BE_PAID
									.getCode());
					if (isShipper) {
						// 船主未支付
						queryWrapper.eq(
								TransportOrderShip::getCaptainShipInfoServiceFeeState,
								ShipInfoServiceFeeDef.State.UNPAID.getCode());
					} else {
						// 货主未支付
						queryWrapper.eq(
								TransportOrderShip::getOwnerShipInfoServiceFeeState,
								ShipInfoServiceFeeDef.State.UNPAID.getCode());
					}
				}
				// 服务费确认中
				case SHIP_INFO_FEE_CONFIRMING -> {
					queryWrapper.eq(TransportOrderShip::getState,
							TransportOrderShipDef.State.SHIP_INFO_FEE_TO_BE_PAID
									.getCode());
					if (isShipper) {
						// 船主已支付且货主待支付或船主确认中
						queryWrapper.and(x -> x.nested(y -> y.eq(
								TransportOrderShip::getCaptainShipInfoServiceFeeState,
								ShipInfoServiceFeeDef.State.SUCCESS.getCode())
								.in(TransportOrderShip::getOwnerShipInfoServiceFee,
										List.of(ShipInfoServiceFeeDef.State.UNPAID
												.getCode(),
												ShipInfoServiceFeeDef.State.UN_CONFIRM
														.getCode()))
								.or()
								.nested(z -> z.eq(
										TransportOrderShip::getCaptainShipInfoServiceFeeState,
										ShipInfoServiceFeeDef.State.UN_CONFIRM
												.getCode()))));
					} else {
						// 货主已支付且船主待支付或货主确认中
						queryWrapper.and(x -> x.nested(y -> y.eq(
								TransportOrderShip::getOwnerShipInfoServiceFeeState,
								ShipInfoServiceFeeDef.State.SUCCESS.getCode())
								.in(TransportOrderShip::getCaptainShipInfoServiceFeeState,
										List.of(ShipInfoServiceFeeDef.State.UNPAID
												.getCode(),
												ShipInfoServiceFeeDef.State.UN_CONFIRM
														.getCode())))
								.or()
								.nested(z -> z.eq(
										TransportOrderShip::getOwnerShipInfoServiceFeeState,
										ShipInfoServiceFeeDef.State.UN_CONFIRM
												.getCode())));
					}
				}
				// 定金确认中
				case DEPOSIT_CONFIRMING -> {
					if (!isShipper) {
						queryWrapper.eq(TransportOrderShip::getState,
								TransportOrderShipDef.State.DEPOSIT_TO_BE_PAID
										.getCode());
					}
				}
				default -> queryWrapper.eq(TransportOrderShip::getState, state);
			}
			// 根据状态查询 创建时间倒序
			queryWrapper.last("ORDER BY created_time DESC");
		}

		if (isShipper) {
			queryWrapper.eq(TransportOrderShip::getCaptainId, customerId);
		}
	}

	/**
	 * 发生船运单通知
	 *
	 * @param resource
	 */
	private void sendTransportOrderShipNotice(TransportOrderShip resource) {

		// 收到船运单短信通知
		if (Objects.nonNull(resource.getSrpId())) {
			// 船主收到船运单提醒
			this.sendCommonNotice(resource);

			// 船运需求匹配成功生成船运单
			// 货主收到船运单提醒，船运单详情
			messageService.sendNotice(Messages.builder()
					.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
							SendType.USERMESSAGE.getCode()))
					.type(UserMessageDef.MessageType.TRANSFORM.getCode())
					.title(MessageFormat.format(
							UserMessageConstants.SHIP_DETAIL_MATCH_TEMPLATE,
							resource.getSrpId()))
					.receiptors(List.of(String.valueOf(resource.getOwnerId())))
					.url(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
					.detailId(String.valueOf(resource.getId()))
					.initiator(
							UserMessageDef.BusinessInitiator.initiate.getCode())
					.templateCode(wxSubscriptionProperties
							.getReceiveShippingOrderCode2())
					.params(Map.of("index", resource.getSrpId(), "num",
							resource.getId()))
					.build());

			// 发送app推送
			messageService.sendNotice(Messages.builder()
					.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
					.type(UserMessageDef.MessageType.TRANSFORM.getCode())
					.title(UserMessageConstants.TRANSPORT_TITLE)
					.content(MessageFormat.format(
							UserMessageConstants.SHIP_DETAIL_MATCH_TEMPLATE,
							resource.getSrpId()))
					.appTypes(List.of(AppType.LIANYUN))
					.bizNo(String.valueOf(resource.getId()))
					.moduleType(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
					.receiptors(List.of(String.valueOf(resource.getOwnerId())))
					.build());

		} else {
			// 发送短信
			this.sendCommonNotice(resource);

			// 新增船运单关联货主
			// 货主收到船运单提醒
			messageService.sendNotice(Messages.builder()
					.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
							SendType.USERMESSAGE.getCode()))
					.type(UserMessageDef.MessageType.TRANSFORM.getCode())
					.title(MessageFormat.format(
							UserMessageConstants.NEW_TRANSORDER_BILL_TEMPLATE,
							resource.getId()))
					.receiptors(List.of(String.valueOf(resource.getOwnerId())))
					.url(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
					.detailId(String.valueOf(resource.getId()))
					.initiator(
							UserMessageDef.BusinessInitiator.initiate.getCode())
					.templateCode(wxSubscriptionProperties
							.getReceiveShippingOrderCode())
					.params(Map.of("index", resource.getId())).build());

			// 发送app推送
			messageService.sendNotice(Messages.builder()
					.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
					.type(UserMessageDef.MessageType.TRANSFORM.getCode())
					.title(UserMessageConstants.TRANSPORT_TITLE)
					.content(MessageFormat.format(
							UserMessageConstants.NEW_TRANSORDER_BILL_TEMPLATE,
							resource.getId()))
					.appTypes(List.of(AppType.SHIP))
					.bizNo(String.valueOf(resource.getId()))
					.moduleType(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
					.receiptors(List.of(String.valueOf(resource.getOwnerId())))
					.build());

		}

		if (TransportOrderShipDef.State.SHIP_INFO_FEE_TO_BE_PAID
				.match(resource.getState())) {
			// 船运单状态为待支付信息服务费且货主未支付
			if (ShipInfoServiceFeeDef.State.UNPAID
					.match(resource.getOwnerShipInfoServiceFeeState())) {
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
								SendType.USERMESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.NEED_PAY_SERVICE_FEE_OWNER_TEMPLATE,
								resource.getId()))
						.receiptors(
								List.of(String.valueOf(resource.getOwnerId())))
						.url(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
						.detailId(String.valueOf(resource.getId()))
						.initiator(UserMessageDef.BusinessInitiator.initiate
								.getCode())
						.templateCode(wxSubscriptionProperties
								.getOwnerPayShippingServiceFeeCode())
						.params(Map.of("index", resource.getId())).build());

				// 发送app推送
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(UserMessageConstants.TRANSPORT_TITLE)
						.content(MessageFormat.format(
								UserMessageConstants.NEED_PAY_SERVICE_FEE_OWNER_TEMPLATE,
								resource.getId()))
						.appTypes(List.of(AppType.LIANYUN))
						.bizNo(String.valueOf(resource.getId()))
						.moduleType(
								UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
						.receiptors(
								List.of(String.valueOf(resource.getOwnerId())))
						.build());

			}
			// 船运单状态为待支付信息服务费且船主未支付
			if (ShipInfoServiceFeeDef.State.UNPAID
					.match(resource.getCaptainShipInfoServiceFeeState())) {
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
								SendType.USERMESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.NEED_PAY_SERVICE_FEE_TEMPLATE,
								resource.getId()))
						.receiptors(List
								.of(String.valueOf(resource.getCaptainId())))
						.url(UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
						.role(AppTypeDef.AppType.CHUAN_WU.getCode())
						.detailId(String.valueOf(resource.getId()))
						.initiator(UserMessageDef.BusinessInitiator.initiate
								.getCode())
						.templateCode(wxSubscriptionProperties
								.getCaptainPayShippingServiceFeeCode())
						.params(Map.of("index", resource.getId()))
						.mobile(resource.getCaptainEnterprise().getMobile())
						.build());

				// 发送app推送
				messageService.sendNotice(Messages.builder()
						.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
						.type(UserMessageDef.MessageType.TRANSFORM.getCode())
						.title(UserMessageConstants.TRANSPORT_TITLE)
						.content(MessageFormat.format(
								UserMessageConstants.NEED_PAY_SERVICE_FEE_TEMPLATE,
								resource.getId()))
						.appTypes(List.of(AppType.SHIP))
						.bizNo(String.valueOf(resource.getId()))
						.moduleType(
								UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
						.receiptors(List
								.of(String.valueOf(resource.getCaptainId())))
						.build());

			}
		}

		if (TransportOrderShipDef.State.DEPOSIT_TO_BE_PAID
				.match(resource.getState())
				&& TransportOrderShipDef.DepositPayType.PAY_ONESELF
						.match(resource.getPayType())) {
			// 船运单状态进入待支付定金且支付方式为自行支付
			messageService.sendNotice(Messages.builder()
					.messageTypes(List.of(SendType.USERMESSAGE.getCode()))
					.type(UserMessageDef.MessageType.TRANSFORM.getCode())
					.title(MessageFormat.format(
							UserMessageConstants.NEED_PAY_DEPOSIT_TEMPLATE,
							resource.getId()))
					.receiptors(List.of(String.valueOf(resource.getOwnerId())))
					.url(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
					.detailId(String.valueOf(resource.getId()))
					.initiator(
							UserMessageDef.BusinessInitiator.initiate.getCode())
					.build());

			// 发送app推送
			messageService.sendNotice(Messages.builder()
					.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
					.type(UserMessageDef.MessageType.TRANSFORM.getCode())
					.title(UserMessageConstants.TRANSPORT_TITLE)
					.content(MessageFormat.format(
							UserMessageConstants.NEED_PAY_DEPOSIT_TEMPLATE,
							resource.getId()))
					.appTypes(List.of(AppType.LIANYUN))
					.bizNo(String.valueOf(resource.getId()))
					.moduleType(UserMessageConstants.TRANSFORM_BILL_DETAIL_PAGE)
					.receiptors(List.of(String.valueOf(resource.getOwnerId())))
					.build());
		}

		// 判断是否指派了专员
		if (Objects.isNull(resource.getUpstreamHandlerId())
				|| Objects.isNull(resource.getDownstreamHandlerId())) {
			messageService.sendNotice(WxwMessage.builder()
					.receiptors(userService
							.findUsersByPermission(
									AdminPermissionDef.SHIP_DEMAND_MANAGE, null)
							.stream().map(user -> String.valueOf(user.getId()))
							.toList())
					.url("/logistics/shippingNeed/waybillManagementInfo/"
							.concat(String.valueOf(resource.getId())))
					.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
					.operationModule(
							WxwDef.NoticeOperationModule.TRANSPORT_ORDER_SHIP
									.getDesc())
					.desc("待指派").keyword(String.valueOf(resource.getId()))
					.content(StringUtils.EMPTY).build());
		}

	}

	/**
	 * 发送通知给船主
	 *
	 * @param resource
	 */
	private void sendCommonNotice(TransportOrderShip resource) {
		messageService.sendNotice(Messages.builder()
				.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
						SendType.USERMESSAGE.getCode()))
				.type(UserMessageDef.MessageType.TRANSFORM.getCode())
				.title(MessageFormat.format(
						UserMessageConstants.NEW_TRANSFORM_BILL_TEMPLATE,
						resource.getId()))
				.receiptors(List.of(String.valueOf(resource.getCaptainId())))
				.url(UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
				.role(AppTypeDef.AppType.CHUAN_WU.getCode())
				.detailId(String.valueOf(resource.getId()))
				.initiator(UserMessageDef.BusinessInitiator.initiate.getCode())
				.templateCode(wxSubscriptionProperties
						.getCaptainReceiveShippingOrderCode())
				.params(Map.of("index", resource.getId()))
				.mobile(resource.getCaptainEnterprise().getMobile()).build());

		// 发送app推送
		messageService.sendNotice(Messages.builder()
				.messageTypes(List.of(SendType.PUSH_MESSAGE.getCode()))
				.type(UserMessageDef.MessageType.TRANSFORM.getCode())
				.title(UserMessageConstants.TRANSPORT_TITLE)
				.content(MessageFormat.format(
						UserMessageConstants.NEW_TRANSFORM_BILL_TEMPLATE,
						resource.getId()))
				.appTypes(List.of(AppType.LIANYUN))
				.bizNo(String.valueOf(resource.getId()))
				.moduleType(
						UserMessageConstants.TRANSFER_TRANSFORM_BILL_DETAIL_PAGE)
				.receiptors(List.of(String.valueOf(resource.getCaptainId())))
				.build());
	}

	@Override
	public void callback(Map<String, Object> params) {
		String type = (String) params.get("type");
		Object data = params.get("data");
		switch (HmgDef.CallBackType.from(type)) {
			// 船舶
			case SHIP -> {
				HmgShipInfo hmgShipInfo = JsonUtils.convertClass(data,
						HmgShipInfo.class);
				log.info("船舶信息回调:{}", JsonUtils.objectToJson(hmgShipInfo));
				if (Objects.nonNull(hmgShipInfo)) {
					shipService.handleShipInfoCallback(hmgShipInfo);
				}
			}
			// 承运商
			case CARRIER -> {
				CarrierInfo carrierInfo = JsonUtils.convertClass(data,
						CarrierInfo.class);
				log.info("承运商信息回调:{}", JsonUtils.objectToJson(carrierInfo));
			}
			// 运单
			case WAYBILL -> {
				WaybillInfo waybillInfo = JsonUtils.convertClass(data,
						WaybillInfo.class);
				log.info("运单信息回调:{}", JsonUtils.objectToJson(waybillInfo));
				if (Objects.nonNull(waybillInfo)) {
					this.handleWaybillCallback(waybillInfo);
				}
			}
			// 支付
			case CHARGE -> {
				WaybillPaymentInfo waybillPaymentInfo = JsonUtils
						.convertClass(data, WaybillPaymentInfo.class);
				log.info("支付信息回调:{}",
						JsonUtils.objectToJson(waybillPaymentInfo));
				if (Objects.nonNull(waybillPaymentInfo)) {
					ownerShippingDepositService
							.handleWaybillPaymentInfoCallback(
									waybillPaymentInfo);
				}

			}
		}
	}

	/**
	 * 拼接时间
	 * 
	 * @return
	 */
	public String getDate() {

		// 获取当前日期
		LocalDate dataNow = LocalDate.now();

		// 自定义格式：2025年06月13日：10时45分30秒
		DateTimeFormatter dateFormatter = DateTimeFormatter
				.ofPattern("yyyy-MM-dd");

		String dataStr = dataNow.format(dateFormatter);

		// 获取当前时间
		LocalTime timeNow = LocalTime.now();

		DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");

		String timeStr = timeNow.format(timeFormatter);

		// 获取星期几（英文）
		String dayOfWeek = LocalDate.now().getDayOfWeek()
				.getDisplayName(TextStyle.FULL, Locale.CHINA);

		return "时间: " + dataStr + " " + dayOfWeek + " " + timeStr + "\n";
	}

	@Override
	public File uploadWatermark(MultipartFile file,
			TransportOrderShip transportOrderShip) throws Exception {
		Ship ship = shipService.findOne(transportOrderShip.getShipId())
				.orElse(new Ship());

		String shipName = StringUtils.isNotBlank(ship.getCnname())
				? ship.getCnname()
				: (StringUtils.isNotBlank(ship.getName()) ? ship.getName()
						: ship.getId());

		// 船名
		String shipStr = "船名: ";
		String mmsiStr = "MMSI号: "
				+ (StringUtils.isNotBlank(ship.getId()) ? ship.getId() : "")
				+ "\n";
		if (StringUtils.isNotBlank(shipName)) {
			shipStr = shipStr + shipName + "\n";
		} else {
			shipStr = shipStr + "--" + "\n";
		}
		// 时间
		String dateStr = this.getDate();
		com.zhihaoscm.ships66.sdk.response.ShipInfo shipInfo = shipService
				.findByAsi(transportOrderShip.getShipId());

		// 位置
		String location = "";
		// 经纬度
		String lonLat = "";
		if (Objects.nonNull(shipInfo)) {
			location = shipInfo.getLocation();
			// 纬度
			Double lat = shipInfo.getLat();
			// 经度
			Double lon = shipInfo.getLon();

			if (Objects.nonNull(lat) && Objects.nonNull(lon)) {
				String latDMS = GeoUtils.convertToDMS(lat, true);
				String lonDMS = GeoUtils.convertToDMS(lon, false);
				lonLat = lonDMS + ", " + latDMS;
			}

		}
		String locationStr = "地点: ";
		if (StringUtils.isNotBlank(location)) {
			locationStr = locationStr + location + "\n";
		} else {
			locationStr = locationStr + "--" + "\n";
		}
		String lonLatStr = "经纬度: ";
		if (StringUtils.isNotBlank(lonLat)) {
			lonLatStr = lonLatStr + lonLat + "\n";
		} else {
			lonLatStr = lonLatStr + "--" + "\n";
		}

		String watermark = shipStr + mmsiStr + dateStr + locationStr
				+ lonLatStr;

		return fileService.uploadWatermark(file, StringUtils.EMPTY, watermark)
				.orElse(null);
	}

	/**
	 * 处理运单装船/发船/到达/签收/审核成功/失败回调
	 * 
	 * @param waybillInfo
	 */
	private void handleWaybillCallback(WaybillInfo waybillInfo) {
		// 运单ID
		String tpWaybillNo = waybillInfo.getTpWaybillNo();
		// 运单审核状态
		Integer checkStatus = waybillInfo.getCheckStatus();
		Integer hmgCheckState = null;
		switch (HmgDef.WaybillReviewStatus.from(checkStatus)) {
			case APPROVED ->
				hmgCheckState = TransportOrderShipDef.HmgCheckState.APPROVED
						.getCode();
			case REJECTED ->
				hmgCheckState = TransportOrderShipDef.HmgCheckState.REJECTED
						.getCode();
			case PENDING_REVIEW ->
				hmgCheckState = TransportOrderShipDef.HmgCheckState.PENDING_REVIEW
						.getCode();
		}
		// 更新运单审核状态
		LambdaUpdateWrapper<TransportOrderShip> wrapper = Wrappers
				.lambdaUpdate(TransportOrderShip.class);
		wrapper.eq(TransportOrderShip::getId, tpWaybillNo);
		wrapper.eq(TransportOrderShip::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.set(TransportOrderShip::getHmgCheckState, hmgCheckState);
		repository.update(wrapper);
	}

	/**
	 * 保存历史轨迹信息
	 *
	 * @param id
	 */
	private void handleAisTrack(String id) {
		List<ShipTrajectoryVo> data = this.findAisTrackById(id);
		String routerPathResponseStr = JsonUtils.objectToJson(data);
		Trajectory trajectory = new Trajectory();
		trajectory.setBusinessId(id);
		trajectory.setType(TrajectoryDef.Type.SHIP.getCode());
		trajectory.setTrajectoryInfo(routerPathResponseStr);
		trajectoryService.create(trajectory);

	}

	/**
	 * 转换时间戳
	 *
	 * @param time
	 * @return
	 */
	private long getTimestamp(LocalDateTime time) {
		// 将 LocalDateTime 转换为 ZonedDateTime
		ZonedDateTime zonedDateTime = time.atZone(ZoneId.systemDefault());
		// 将 ZonedDateTime 转换为 Instant
		Instant instant = zonedDateTime.toInstant();
		// 获取时间戳
		return instant.toEpochMilli();
	}

	/**
	 * 调用hmg新增或变更承运人和船舶接口-只传shipInfo
	 * 
	 * @param transportOrderShip
	 */
	private void saveCarrierAndShip(TransportOrderShip transportOrderShip) {
		// 调用新增或变更承运人和船舶接口
		ShipInfo shipInfo = new ShipInfo();
		DriverInfo driverInfo = new DriverInfo();

		Ship ship = shipService.findOne(transportOrderShip.getShipId())
				.orElse(null);
		Customer customer = customerService
				.findOne(transportOrderShip.getCaptainId()).orElse(null);
		if (Objects.isNull(ship)) {
			throw new BadRequestException(ErrorCode.CODE_30191010);
		}
		if (Objects.isNull(customer)) {
			throw new BadRequestException(ErrorCode.CODE_30191011);
		}
		if (Objects.isNull(customer.getMobile())) {
			throw new BadRequestException(ErrorCode.CODE_30191012);
		}
		if (Objects.isNull(customer.getIdNo())) {
			throw new BadRequestException(ErrorCode.CODE_30191013);
		}
		if (Objects.isNull(customer.getRealName())) {
			throw new BadRequestException(ErrorCode.CODE_30191018);
		}
		driverInfo.setNotifyUrl(hmgProperties.getNotifyUrl());
		driverInfo.setUsername(customer.getMobile());
		driverInfo.setMobilePhone(customer.getMobile());
		driverInfo.setRealname(customer.getRealName());
		driverInfo.setIdCardNo(customer.getIdNo());
		CertificateFiles certificateFiles = ship.getCertificateFiles();
		if (Objects.isNull(certificateFiles)) {
			throw new BadRequestException(ErrorCode.CODE_30191019);
		}

		// 身份证正面（国徽）不为空的校验
		if (Objects.isNull(
				ship.getCertificateFiles().getCaptainsIdCardFrontFile())) {
			throw new BadRequestException(ErrorCode.CODE_30191014);
		}
		// 身份证反面（人像）不为空的校验
		if (Objects.isNull(
				ship.getCertificateFiles().getCaptainsIdCardBackFile())) {
			throw new BadRequestException(ErrorCode.CODE_30191015);
		}
		// 身份证信息不为空的校验
		if (Objects.isNull(ship.getIdCardInfo())) {
			throw new BadRequestException(ErrorCode.CODE_30191033);
		}
		ArrayLong competencyCertificateFile = certificateFiles
				.getCompetencyCertificateFile();
		// 船员适任证书不为空的校验
		if (CollectionUtils.isEmpty(competencyCertificateFile)) {
			throw new BadRequestException(ErrorCode.CODE_30191030);
		}
		// 船员适任证书识别的职务资格
		if (StringUtils.isBlank(ship.getJobQualification())) {
			throw new BadRequestException(ErrorCode.CODE_30191026);
		} else {
			driverInfo.setWaterLicenseQual(ship.getJobQualification());
		}

		IdCardInfo idCardInfo = ship.getIdCardInfo();
		// 身份证正面-国徽
		fileService.findOne(
				ship.getCertificateFiles().getCaptainsIdCardFrontFile(), true)
				.ifPresent(file -> {
					// 正面
					driverInfo.setIdCardFrontPhoto(file.getPath());
				});
		// 身份证反面-人像
		fileService
				.findOne(ship.getCertificateFiles().getCaptainsIdCardBackFile(),
						true)
				.ifPresent(file -> {
					// 反面
					driverInfo.setIdCardBackPhoto(file.getPath());
				});
		// 设置信息
		// 识别身份证-身份证有效期开始时间、身份证有效期结束时间
		LocalDateTime idCardStartDate = idCardInfo.getIdCardStartDate();
		if (Objects.isNull(idCardStartDate)) {
			throw new BadRequestException(ErrorCode.CODE_30191034);
		}
		LocalDateTime idCardEndDate = idCardInfo.getIdCardEndDate();
		if (Objects.isNull(idCardEndDate)) {
			throw new BadRequestException(ErrorCode.CODE_30191035);
		}
		driverInfo.setIdCardStartDate(idCardStartDate
				.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
		driverInfo.setIdCardEndDate(idCardEndDate
				.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

		// 识别身份证-三级地址编码、三级地址、住址、性别、出生日期
		String idNumber = idCardInfo.getIdNumber();
		if (StringUtils.isBlank(idNumber)) {
			throw new BadRequestException(ErrorCode.CODE_30191036);
		}
		String address = idCardInfo.getAddress();
		if (StringUtils.isBlank(address)) {
			throw new BadRequestException(ErrorCode.CODE_30191037);
		}
		String sex = idCardInfo.getSex();
		if (StringUtils.isBlank(sex)) {
			throw new BadRequestException(ErrorCode.CODE_30191038);
		}
		LocalDateTime birthDate = idCardInfo.getBirthDate();
		if (Objects.isNull(birthDate)) {
			throw new BadRequestException(ErrorCode.CODE_30191039);
		}

		String areaCode = idNumber.substring(0, 6);
		driverInfo.setAreaCode(areaCode);
		// 三级地址
		areasService.findOne(Long.valueOf(areaCode)).ifPresent(area -> {
			String province = area.getProvince();
			String city = area.getCity();
			String district = area.getDistrict();
			driverInfo.setAreaName(province + " " + city + " " + district);
		});
		driverInfo.setAddress(address);
		// 男1，女2
		driverInfo.setSex("男".equals(sex) ? "1" : "2");
		driverInfo.setBirthDate(
				birthDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

		// 船员适任证书
		if (Objects.nonNull(
				ship.getCertificateFiles().getCompetencyCertificateFile())) {
			fileService
					.findOne(
							ship.getCertificateFiles()
									.getCompetencyCertificateFile().get(0),
							true)
					.ifPresent(file -> driverInfo
							.setWaterLicensePhoto(file.getPath()));
		}

		// 设置shipInfo
		shipInfo.setShipOwner(ship.getCaptain());
		shipInfo.setShipCertEndTime(
				this.formatToDate(ship.getOperatingLicenseExpirationDate()));
		shipInfo.setShipName(
				StringUtils.isNotBlank(ship.getCnname()) ? ship.getCnname()
						: (StringUtils.isNotBlank(ship.getName())
								? ship.getName()
								: ship.getId()));
		shipInfo.setShipType(ShipDef.Type.from(ship.getType()).getName());
		shipInfo.setShipMmsi(ship.getId());
		shipInfo.setOwnName(ship.getShipOperatorName());
		shipInfo.setShipOwnMobile(ship.getMobile());
		if (Objects.nonNull(ship.getTonCapacity())) {
			shipInfo.setActualWeight(String.valueOf(ship.getTonCapacity()));
		}
		shipInfo.setNotifyUrl(hmgProperties.getNotifyUrl());
		if (Objects.nonNull(ship.getCertificateFiles()
				.getShipBusinessTransportCertificateFile())) {
			fileService
					.findOne(
							ship.getCertificateFiles()
									.getShipBusinessTransportCertificateFile(),
							true)
					.ifPresent(file -> shipInfo
							.setShipCertOwnerPhoto(file.getPath()));
		} else {
			throw new BadRequestException(ErrorCode.CODE_30191027);
		}
		if (Objects.nonNull(ship.getCertificateFiles()
				.getShipAutomaticIdentifyCertificateFile())) {
			fileService
					.findOne(
							ship.getCertificateFiles()
									.getShipAutomaticIdentifyCertificateFile(),
							true)
					.ifPresent(file -> shipInfo
							.setShipAisCertPhoto(file.getPath()));
		} else {
			throw new BadRequestException(ErrorCode.CODE_30191032);
		}
		if (Objects.nonNull(ship.getCertificateFiles()
				.getShipBusinessTonnagCertificateFile())) {
			fileService
					.findOne(
							ship.getCertificateFiles()
									.getShipBusinessTonnagCertificateFile(),
							true)
					.ifPresent(file -> shipInfo
							.setShipCertWeightPhoto(file.getPath()));
		} else {
			throw new BadRequestException(ErrorCode.CODE_30191029);
		}
		if (Objects.nonNull(
				ship.getCertificateFiles().getShipInspectionReportFile())) {
			fileService
					.findOne(ship.getCertificateFiles()
							.getShipInspectionReportFile(), true)
					.ifPresent(file -> shipInfo
							.setShipSurveyCertPhoto(file.getPath()));
		} else {
			throw new BadRequestException(ErrorCode.CODE_30191031);
		}
		if (Objects.nonNull(ship.getCertificateFiles()
				.getShipNationalityCertificateFile())) {
			fileService
					.findOne(ship.getCertificateFiles()
							.getShipNationalityCertificateFile(), true)
					.ifPresent(
							file -> shipInfo.setShipNatiPhoto(file.getPath()));
		} else {
			throw new BadRequestException(ErrorCode.CODE_30191028);
		}

		HmgCarrierAndShipRequest req = new HmgCarrierAndShipRequest();
		req.setShipInfo(shipInfo);
		req.setDriverInfo(driverInfo);
		log.info("新增或变更承运人和船舶参数:{}", JsonUtils.objectToJson(req));
		try {
			HmgBaseResponse<Object> resp = hmgClient.saveCarrierAndShip(req);
			log.info("新增或变更承运人和船舶结果:{}", JsonUtils.objectToJson(resp));
			if (!CommonDef.Symbol.NO.match(resp.getCode())) {
				if (110 != resp.getCode()) {
					throw new RuntimeException(resp.getMsg());
				}
			}
			// 修改船舶状态
			ship.setHmgCheckState(
					ShipDef.HmgCheckState.PENDING_REVIEW.getCode());
			shipService.updateAllProperties(ship);
		} catch (Exception e) {
			log.error("新增或变更承运人和船舶失败:{}", e.getMessage());
			throw new DynamicsBadRequestException(ErrorCode.CODE_30191004,
					e.getMessage());
		}

	}

	/**
	 * 调用hmg新增或变更货源接口
	 * 
	 * @param transportOrderShip
	 */
	private void saveGoodsSource(TransportOrderShip transportOrderShip) {
		try {
			// 调用新增或变更货源接口
			HmgGoodsSourceRequest req = new HmgGoodsSourceRequest();
			req.setTpOrderNo(transportOrderShip.getId());
			customerService.findVoById(transportOrderShip.getOwnerId())
					.ifPresent(customerVo -> {
						req.setShipperUserName(
								customerVo.getCustomer().getRealName());
						req.setConsigneeUser(
								customerVo.getCustomer().getRealName());
					});
			req.setShipperName(transportOrderShip.getOwnerName());
			req.setShipperPhone(transportOrderShip.getOwnerMobile());
			portService.findOne(transportOrderShip.getSourcePortId())
					.ifPresent(port -> {
						if (PortDef.Type.PORT.match(port.getType())) {
							// 码头取三级地址编码，城市取二级地址编码
							req.setStartAreaCode(port.getRegionCode());
							req.setStartAddress(port.getAddress());
						} else {
							String cityCode = port.getCityCode();
							if (port.getCityCode().length() == 4) {
								cityCode = cityCode + "00";
							}
							req.setStartAreaCode(cityCode);
							req.setStartAddress(port.getName());
						}

						String location = port.getLatLon();
						if (StringUtils.isNotBlank(location)) {
							String[] parts = location.split(",");
							if (parts.length == 2) {
								// 经度
								String lng = parts[0];
								// 纬度
								String lat = parts[1];
								req.setStartLng(lng);
								req.setStartLat(lat);
							}
						}
						req.setStartPort(port.getName());
					});
			req.setConsigneeName(transportOrderShip.getOwnerName());
			req.setConsigneePhone(transportOrderShip.getOwnerMobile());
			portService.findOne(transportOrderShip.getDestinationPortId())
					.ifPresent(port -> {
						if (PortDef.Type.PORT.match(port.getType())) {
							// 码头取三级地址编码，城市取二级地址编码
							req.setEndAreaCode(port.getRegionCode());
							req.setEndAddress(port.getAddress());
						} else {
							req.setEndAddress(port.getName());
							String cityCode = port.getCityCode();
							if (port.getCityCode().length() == 4) {
								cityCode = cityCode + "00";
							}
							req.setEndAreaCode(cityCode);
						}

						String location = port.getLatLon();
						if (StringUtils.isNotBlank(location)) {
							String[] parts = location.split(",");
							if (parts.length == 2) {
								// 经度
								String lng = parts[0];
								// 纬度
								String lat = parts[1];
								req.setEndLng(lng);
								req.setEndLat(lat);
							}
						}
						req.setEndPort(port.getName());
					});

			req.setChargeWay("10");
			req.setUnitPrice(String.valueOf(transportOrderShip.getUnitPrice()));
			req.setGoodsWeight(String.valueOf(transportOrderShip.getTon()));
			req.setGoodsName(transportOrderShip.getGoodsType());
			// 默认传其他1700
			req.setGoodsType("1700");
			req.setShipperCharge(
					String.valueOf(transportOrderShip.getUnitPrice().multiply(
							BigDecimal.valueOf(transportOrderShip.getTon()))));
			req.setGoodsNumber("0");
			log.info("新增或变更货源参数:{}", JsonUtils.objectToJson(req));
			HmgBaseResponse<Object> resp = hmgClient.saveGoodsSource(req);
			if (!CommonDef.Symbol.NO.match(resp.getCode())) {
				throw new RuntimeException(resp.getMsg());
			}
		} catch (Exception e) {
			log.error("新增或变更货源失败:{}", e.getMessage());
			throw new DynamicsBadRequestException(ErrorCode.CODE_30191005,
					e.getMessage());
		}
	}

	/**
	 * 调用hmg新增运单信息接口
	 * 
	 * @param transportOrderShip
	 * @return 黄码港运单id
	 */
	private String createWaybill(TransportOrderShip transportOrderShip) {
		try {
			// 调用新增运单信息接口
			HmgWaybillRequest req = new HmgWaybillRequest();
			req.setTpWaybillNo(transportOrderShip.getId());
			req.setTpOrderNo(transportOrderShip.getId());
			req.setStatus(10);
			req.setNotifyUrl(hmgProperties.getNotifyUrl());

			shipService.findOne(transportOrderShip.getShipId())
					.ifPresent(ship -> req.setShipName(
							StringUtils.isNotBlank(ship.getCnname())
									? ship.getCnname()
									: (StringUtils.isNotBlank(ship.getName())
											? ship.getName()
											: ship.getId())));
			req.setShipMmsi(transportOrderShip.getShipId());
			req.setUnitPrice(String.valueOf(transportOrderShip.getUnitPrice()));
			req.setGoodsWeight(String.valueOf(transportOrderShip.getTon()));

			customerService.findVoById(transportOrderShip.getCaptainId())
					.ifPresent(customerVo -> {
						req.setCarrierIdCardNo(
								customerVo.getCustomer().getIdNo());
						req.setDriverIdCardNo(
								customerVo.getCustomer().getIdNo());
					});
			log.info("新增运单信息参数:{}", JsonUtils.objectToJson(req));
			HmgBaseResponse<CreateWaybillResponse> resp = hmgClient
					.createWaybill(req);
			log.info("新增运单信息结果:{}", JsonUtils.objectToJson(resp));
			if (!CommonDef.Symbol.NO.match(resp.getCode())) {
				throw new RuntimeException(resp.getMsg());
			}
			return resp.getData().getWaybillNo();
		} catch (Exception e) {
			log.error("新增运单信息失败:{}", e.getMessage());
			throw new DynamicsBadRequestException(ErrorCode.CODE_30191006,
					e.getMessage());
		}
	}

	/**
	 * 调用hmg更新运单信息接口
	 *
	 * @param transportOrderShip
	 */
	@Override
	public void updateWaybill(TransportOrderShip transportOrderShip) {
		// 装货和发船信息（船主身份证号（识别船舶信息内身份证信息）、发航时间、船运单编号、MMSI、注册身份证号、装货时间、满载吨位、船名、满载视频（图片和视频））
		// 卸货和签收信息（船运单编号、卸货完成时间、卸货吨位、到港时间、卸货视频（图片和视频））

		// 如果卸货和签收信息都不为空，则传状态50 以及 装货和发船 以及 卸货和签收信息
		// 否则如果装货和发船信息都不为空，则传状态30 以及 装货和发船信息
		try {
			// 调用更新运单信息接口
			HmgWaybillRequest req = new HmgWaybillRequest();
			req.setTpWaybillNo(transportOrderShip.getId());
			shipService.findOne(transportOrderShip.getShipId())
					.ifPresent(ship -> req.setShipName(
							StringUtils.isNotBlank(ship.getCnname())
									? ship.getCnname()
									: (StringUtils.isNotBlank(ship.getName())
											? ship.getName()
											: ship.getId())));
			req.setShipMmsi(transportOrderShip.getShipId());
			req.setUnitPrice(String.valueOf(transportOrderShip.getUnitPrice()));
			req.setGoodsWeight(String.valueOf(transportOrderShip.getTon()));

			customerService.findVoById(transportOrderShip.getCaptainId())
					.ifPresent(customerVo -> {
						req.setCarrierIdCardNo(
								customerVo.getCustomer().getIdNo());
						req.setDriverIdCardNo(
								customerVo.getCustomer().getIdNo());
					});

			TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
					.findOne(transportOrderShip.getId()).orElse(null);
			if (Objects.isNull(transportOrderDetailsShip)) {
				log.error("船运单明细不存在:{}", transportOrderShip.getId());
				throw new BadRequestException(ErrorCode.CODE_30137001);
			}
			UnloadingInfo unloadingInfo = transportOrderDetailsShip
					.getUnloadingInfo();
			SetSailInfo setSailInfo = transportOrderDetailsShip
					.getSetSailInfo();
			DeliveryInfo deliveryInfo = transportOrderDetailsShip
					.getDeliveryInfo();
			DeparturePreparationInfo lazbInfo = transportOrderDetailsShip
					.getLazbInfo();
			if (this.checkUnloadingAndSign(transportOrderShip)) {
				// 卸货和签收信息都不为空:传状态50 以及 装货和发船 以及 卸货和签收信息
				req.setStatus(50);
				if (Objects.nonNull(unloadingInfo)) {
					// 卸货完成时间
					req.setSignTime(unloadingInfo.getUnloadingCompletionTime());
					// 卸货吨位
					req.setSignWeight(String
							.valueOf(unloadingInfo.getUnloadingTonnage()));
					// 到港时间
					req.setArrivedTime(
							transportOrderDetailsShip.getCaptainArrivalTime());
					// 卸货视频（图片和视频）
					fileService
							.findOne(unloadingInfo.getUnloadingVideoId(), true)
							.ifPresent(
									file -> req.setSignPhoto(file.getPath()));
				}

				// 发航时间
				if (Objects.nonNull(setSailInfo)
						&& Objects.nonNull(setSailInfo.getDepartureTime())) {
					req.setDepartTime(setSailInfo.getDepartureTime());
				}
				// 装货时间
				if (Objects.nonNull(deliveryInfo)
						&& Objects.nonNull(deliveryInfo.getLoadingDate())) {
					req.setLoadingTime(deliveryInfo.getLoadingDate());
				}
				// 满载吨位
				if (Objects.nonNull(lazbInfo)
						&& Objects.nonNull(lazbInfo.getLoadingTonnage())) {
					req.setLoadingWeight(
							String.valueOf(lazbInfo.getLoadingTonnage()));
				}
				// 满载视频（图片和视频）
				if (Objects.nonNull(lazbInfo)
						&& Objects.nonNull(lazbInfo.getLoadVideoId())) {

					fileService.findOne(lazbInfo.getLoadVideoId(), true)
							.ifPresent(file -> req
									.setLoadingPhoto(file.getPath()));
				}

			} else if (this.checkLoadingAndSail(transportOrderShip)) {
				// 装货和发船信息都不为空:传状态30 以及 装货和发船信息
				req.setStatus(30);
				// 发航时间
				req.setDepartTime(setSailInfo.getDepartureTime());
				// 装货时间
				req.setLoadingTime(deliveryInfo.getLoadingDate());
				// 满载吨位
				req.setLoadingWeight(
						String.valueOf(lazbInfo.getLoadingTonnage()));
				// 满载视频（图片和视频）
				fileService.findOne(lazbInfo.getLoadVideoId(), true)
						.ifPresent(file -> req.setLoadingPhoto(file.getPath()));

			}

			log.info("更新运单信息参数:{}", JsonUtils.objectToJson(req));
			HmgBaseResponse<UpdateWaybillResponse> resp = hmgClient
					.updateWaybill(req);
			log.info("更新运单信息结果:{}", JsonUtils.objectToJson(resp));
			if (!CommonDef.Symbol.NO.match(resp.getCode())) {
				throw new RuntimeException(resp.getMsg());
			}
		} catch (Exception e) {
			log.error("更新运单信息失败:{}", e.getMessage());
			throw new DynamicsBadRequestException(ErrorCode.CODE_30191009,
					e.getMessage());
		}
	}

	/**
	 * 将 LocalDateTime 转换为 yyyy-MM-dd 格式的字符串
	 * 
	 * @param dateTime
	 *            LocalDateTime对象
	 * @return 格式化后的字符串，例如 "2019-03-22"
	 */
	private String formatToDate(LocalDateTime dateTime) {
		if (dateTime == null) {
			return null;
		}
		DateTimeFormatter DATE_FORMATTER = DateTimeFormatter
				.ofPattern("yyyy-MM-dd");
		return dateTime.format(DATE_FORMATTER);
	}

	/**
	 * 处理推送
	 * 
	 * @param type
	 * @param action
	 * @param id
	 */
	public void handle(Integer type, Integer action, String id) {
		if (Objects.isNull(type) || Objects.isNull(action)
				|| Objects.isNull(id)) {
			return;
		}
		ApplicationTransfer transfer = new ApplicationTransfer(type, action,
				id);
		mqUtil.asyncSend(MqMessage.builder()
				.topic(TopicDef.APPLICATION_CONNECTOR)
				.message(MessageBuilder
						.withPayload(JsonUtils.objectToJson(transfer)).build())
				.build());
	}

	/**
	 * 组装ai返回结果
	 * 
	 * @param transportOrderShips
	 * @return
	 */
	private List<TransportOrderShipAIRes> packToAIRes(
			List<TransportOrderShip> transportOrderShips) {
		if (CollectionUtils.isEmpty(transportOrderShips)) {
			return List.of();
		}
		return transportOrderShips.stream().map(e -> {
			TransportOrderShipAIRes aiRes = new TransportOrderShipAIRes();
			aiRes.setId(e.getId());
			aiRes.setSourcePortName(e.getSourcePortName());
			aiRes.setDestinationPortName(e.getDestinationPortName());
			if (Objects.nonNull(e.getLoadDate())) {
				aiRes.setLoadDate(DateUtils.convertDate(e.getLoadDate(),
						DateFormat.YYYY_MM_DD_CN));
			}
			aiRes.setLoadDays(e.getLoadDays());
			aiRes.setLoadUnloadDays(e.getLoadUnloadDays());
			aiRes.setGoodsType(e.getGoodsType());

			for (ShipDef.Type value : ShipDef.Type.values()) {
				if (value.getCode().equals(e.getShipType())) {
					aiRes.setShipType(value.getName());
				}
			}
			aiRes.setUnitPrice(e.getUnitPrice());
			aiRes.setTon(e.getTon());
			return aiRes;
		}

		).collect(Collectors.toList());
	}

	/**
	 * 判断卸货和签收信息都不为空
	 */
	private boolean checkUnloadingAndSign(
			TransportOrderShip transportOrderShip) {
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
				.findOne(transportOrderShip.getId()).orElse(null);
		if (Objects.isNull(transportOrderDetailsShip)) {
			return false;
		}
		UnloadingInfo unloadingInfo = transportOrderDetailsShip
				.getUnloadingInfo();
		if (Objects.isNull(unloadingInfo)) {
			return false;
		}
		// 卸货完成时间、卸货吨位、到港时间、卸货视频（图片和视频）
		// 卸货完成时间
		LocalDateTime unloadingCompletionTime = unloadingInfo
				.getUnloadingCompletionTime();
		// 卸货吨位
		BigDecimal unloadingTonnage = unloadingInfo.getUnloadingTonnage();
		// 到港时间
		LocalDateTime captainArrivalTime = transportOrderDetailsShip
				.getCaptainArrivalTime();
		// 卸货视频（图片和视频）
		Long unloadingVideoId = unloadingInfo.getUnloadingVideoId();
		return Objects.nonNull(unloadingCompletionTime)
				&& Objects.nonNull(unloadingTonnage)
				&& Objects.nonNull(captainArrivalTime)
				&& Objects.nonNull(unloadingVideoId);
	}

	/**
	 * 判断装货和发船信息都不为空
	 */
	private boolean checkLoadingAndSail(TransportOrderShip transportOrderShip) {
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
				.findOne(transportOrderShip.getId()).orElse(null);
		if (Objects.isNull(transportOrderDetailsShip)) {
			return false;
		}
		SetSailInfo setSailInfo = transportOrderDetailsShip.getSetSailInfo();
		DeliveryInfo deliveryInfo = transportOrderDetailsShip.getDeliveryInfo();
		DeparturePreparationInfo lazbInfo = transportOrderDetailsShip
				.getLazbInfo();
		if (Objects.isNull(setSailInfo) || Objects.isNull(deliveryInfo)
				|| Objects.isNull(lazbInfo)) {
			return false;
		}
		// 发航时间、装货时间、满载吨位、满载视频（图片和视频）
		// 发航时间
		LocalDateTime departureTime = setSailInfo.getDepartureTime();
		// 装货时间
		LocalDateTime loadingDate = deliveryInfo.getLoadingDate();
		// 满载吨位
		BigDecimal loadingTonnage = lazbInfo.getLoadingTonnage();
		// 满载视频（图片和视频）
		Long loadVideoId = lazbInfo.getLoadVideoId();
		return Objects.nonNull(departureTime) && Objects.nonNull(loadingDate)
				&& Objects.nonNull(loadingTonnage)
				&& Objects.nonNull(loadVideoId);
	}

}
