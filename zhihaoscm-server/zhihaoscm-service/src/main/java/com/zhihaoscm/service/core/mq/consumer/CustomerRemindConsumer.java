package com.zhihaoscm.service.core.mq.consumer;

import java.util.List;
import java.util.Objects;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.json.CustomerMinder;
import com.zhihaoscm.domain.bean.json.notice.Messages;
import com.zhihaoscm.domain.meta.biz.TopicDef;
import com.zhihaoscm.domain.meta.biz.UserMessageConstants;
import com.zhihaoscm.domain.meta.biz.UserMessageDef;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.core.service.MessageService;

import lombok.extern.slf4j.Slf4j;

/**
 * 用户相关消息消费者
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = TopicDef.CUSTOMER_ACCOUNT_MIND, consumerGroup = TopicDef.CUSTOMER_ACCOUNT_MIND_GROUP)
public class CustomerRemindConsumer
		implements RocketMQListener<CustomerMinder> {

	@Autowired
	private SMSProperties smsProperties;

	@Autowired
	private MessageService messageService;

	@Override
	public void onMessage(CustomerMinder remind) {
		log.info("执行消息{}", remind);

		if (Objects.isNull(remind) || Objects.isNull(remind.getReceiptor())) {
			return;
		}

		Customer receiptor = remind.getReceiptor();
		Messages messages = null;
		if (CustomerMinder.RemindType.memberRenewalRemind
				.equals(remind.getRemindType())) {
			// 会员过期提醒
			messages = Messages.builder()
					.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
							SendType.USERMESSAGE.getCode()))
					.type(UserMessageDef.MessageType.OTHER.getCode())
					.title(remind.getTitle())
					.receiptors(List.of(String.valueOf(receiptor.getId())))
					.url(UserMessageConstants.MEMBER_DETAIL_PAGE)
					.initiator(
							UserMessageDef.BusinessInitiator.initiate.getCode())
					.templateCode(smsProperties.getMemberRenewalReminder())
					.params(remind.getSmsParams()).mobile(receiptor.getMobile())
					.build();

		} else if (CustomerMinder.RemindType.subAccountCreate
				.equals(remind.getRemindType())) {
			// 子账号创建提醒
			messages = Messages.builder()
					.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
							SendType.USERMESSAGE.getCode()))
					.type(UserMessageDef.MessageType.OTHER.getCode())
					.title(remind.getTitle())
					.receiptors(List.of(String.valueOf(receiptor.getId())))
					.url(UserMessageConstants.INVITE_DETAIL_PAGE)
					.detailId(String.valueOf(remind.getDetailId()))
					.initiator(
							UserMessageDef.BusinessInitiator.initiate.getCode())
					.templateCode(smsProperties.getSubAccountConfirmCode())
					.params(remind.getSmsParams()).mobile(receiptor.getMobile())
					.build();
		}
		messageService.sendNotice(messages);
	}
}
