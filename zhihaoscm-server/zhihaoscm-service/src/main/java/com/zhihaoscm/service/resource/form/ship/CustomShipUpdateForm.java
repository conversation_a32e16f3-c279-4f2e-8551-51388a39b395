package com.zhihaoscm.service.resource.form.ship;

import java.time.LocalDateTime;

import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.json.CertificateFiles;
import com.zhihaoscm.domain.bean.json.IdCardInfo;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "CustomShipUpdateForm", title = "船舶更新表单")
public class CustomShipUpdateForm {

	@Schema(description = "船舶负责人")
	private String captain;

	@Schema(description = "船舶负责人联系方式")
	private String mobile;

	@Schema(description = "总吨数")
	private Long tonTotal;

	@Schema(description = "载重吨数")
	private Long tonCapacity;

	@Schema(description = "净吨数")
	private Long tonPure;

	@Schema(description = "营运证截止时间")
	@NotNull(message = ErrorCode.CODE_30120055)
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	private LocalDateTime operatingLicenseExpirationDate;

	@Schema(description = "船舶经营人名称")
	@NotBlank(message = ErrorCode.CODE_30120056)
	@Length(max = 32, message = ErrorCode.CODE_30120057)
	private String shipOperatorName;

	@Schema(description = "国籍截止时间")
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	private LocalDateTime nationalityExpirationDate;

	@Schema(description = "船检证截止时间")
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	private LocalDateTime shipInspectionExpirationDate;

	@Schema(description = "建造日期")
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	private LocalDateTime constructionDate;

	@Schema(description = "船籍港-城市编码")
	private String homePortCityCode;

	@Schema(description = "船舶营运证号")
	@Length(max = 32, message = ErrorCode.CODE_30120058)
	private String operatingLicenseNumber;

	@Schema(description = "资料完善进度")
	private Integer dataProgress;

	@Schema(description = "船舶照片文件")
	private ArrayLong shipPhotoFiles;

	@Schema(description = "船舶视频文件")
	private ArrayLong shipVideoFiles;

	@Schema(description = "船只证件文件")
	private CertificateFiles certificateFiles;

	@Schema(description = "备注")
	@Length(max = 200, message = ErrorCode.CODE_30120053)
	private String remark;

	@Schema(description = "职务资格")
	@NotBlank(message = ErrorCode.CODE_30120061)
	private String jobQualification;

	@Schema(description = "船舶身份证图片识别的字段")
	private IdCardInfo idCardInfo;

	public Ship convertToEntity(Ship ship) {
		ship.setCaptain(this.captain);
		ship.setMobile(this.mobile);
		ship.setTonTotal(this.tonTotal);
		ship.setTonCapacity(this.tonCapacity);
		ship.setTonPure(this.tonPure);
		ship.setOperatingLicenseExpirationDate(
				this.operatingLicenseExpirationDate);
		ship.setShipOperatorName(this.shipOperatorName);
		ship.setNationalityExpirationDate(this.nationalityExpirationDate);
		ship.setShipInspectionExpirationDate(this.shipInspectionExpirationDate);
		ship.setConstructionDate(this.constructionDate);
		ship.setHomePortCityCode(this.homePortCityCode);
		ship.setOperatingLicenseNumber(this.operatingLicenseNumber);
		ship.setDataProgress(this.dataProgress);
		ship.setShipPhotoFiles(this.shipPhotoFiles);
		ship.setShipVideoFiles(this.shipVideoFiles);
		ship.setCertificateFiles(this.certificateFiles);
		ship.setRemark(this.getRemark());
		ship.setJobQualification(this.jobQualification);
		ship.setIdCardInfo(this.idCardInfo);
		return ship;
	}
}
