package com.zhihaoscm.service.core.service;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.SampleApply;

/**
 * <p>
 * 申请寄样信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface SampleApplyService extends MpLongIdBaseService<SampleApply> {

	/**
	 * 分页查询
	 * 
	 * @param page
	 * @param size
	 * @param productId
	 * @param state
	 * @param beginTime
	 * @param endTime
	 * @param sortKey
	 * @param sortOrder
	 * @param keyword
	 * @return
	 */
	Page<SampleApply> paging(Integer page, Integer size, String productId,
			Integer state, LocalDateTime beginTime, LocalDateTime endTime,
			String sortKey, String sortOrder, String keyword);

	/**
	 * 根据产品id和状态查询
	 * 
	 * @param productId
	 * @param states
	 */
	List<SampleApply> findByProductIdAndStates(String productId,
			List<Integer> states);

	/**
	 * 根据商品id和客户id和状态查询
	 * 
	 * @return
	 */
	List<SampleApply> findByProductIdAndCustomIdAndState(
			List<String> productIds, Long customId, List<Integer> states);
}
