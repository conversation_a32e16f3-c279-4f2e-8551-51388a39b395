package com.zhihaoscm.service.core.service.impl;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.common.bean.json.GeoPoint;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.mq.MqMessage;
import com.zhihaoscm.common.mq.MqUtil;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.CarrierShip;
import com.zhihaoscm.domain.bean.json.CustomerBankInfo;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.json.Application.ApplicationTransfer;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.Messages;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.ApplicationDef;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.notice.AppType;
import com.zhihaoscm.domain.meta.notice.SendType;
import com.zhihaoscm.domain.utils.PaginationUtils;
import com.zhihaoscm.service.aop.history.HistoryContext;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.properties.ShippingHandlerProperties;
import com.zhihaoscm.service.config.properties.TransportProperties;
import com.zhihaoscm.service.config.security.ApplicationInfoContextHolder;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.ShippingRequirementPlatMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.UserService;
import com.zhihaoscm.ships66.sdk.enums.ShipInfoDef;
import com.zhihaoscm.ships66.sdk.response.ShipInfo;
import com.zhihaoscm.tianditu.client.TianDiTuClient;
import com.zhihaoscm.tianditu.request.GeocodeRequest;
import com.zhihaoscm.tianditu.response.AddressComponent;
import com.zhihaoscm.tianditu.response.Geocoder;
import com.zhihaoscm.tianditu.response.Response;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 平台船运需求 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Service
@Slf4j
public class ShippingRequirementPlatServiceImpl extends
		MpStringIdBaseServiceImpl<ShippingRequirementPlat, ShippingRequirementPlatMapper>
		implements ShippingRequirementPlatService {

	@Autowired
	private UserService userService;

	@Autowired
	private AssignService assignService;

	@Autowired
	private ShippingRequirementAcceptService acceptService;

	@Autowired
	@Lazy
	private ShippingRequirementPlatService self;

	@Autowired
	private ShippingPriceIndexService priceIndexService;

	@Autowired
	private TransportOrderShipService transportOrderShipService;

	@Autowired
	private TransportOrderDetailsShipService transportOrderDetailsShipService;

	@Lazy
	@Autowired
	private OwnerShippingDepositService ownerShippingDepositService;

	@Autowired
	private CustomerService service;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Autowired
	private FrequentShipRouteService frequentShipRouteService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private PortService portService;

	@Autowired
	private ShipService shipService;

	@Autowired
	private ShipRecommendService shipRecommendService;

	@Autowired
	private CustomerService usercustomerService;

	@Autowired
	private ShipFollowCustomService shipFollowCustomService;

	@Autowired
	private TransportProperties transportProperties;

	@Autowired
	private ApplicationService applicationService;

	@Autowired
	private MqUtil mqUtil;

	@Autowired
	private ShippingHandlerProperties shippingHandlerProperties;

	@Autowired
	private TianDiTuClient tianDiTuClient;

	public ShippingRequirementPlatServiceImpl(
			ShippingRequirementPlatMapper repository) {
		super(repository);
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<ShippingRequirementPlatVo> paging(
			String destinationPortName, String sourcePortName, Integer page,
			Integer size, String handlerName, Integer demandLevel,
			LocalDate loadDateStart, LocalDate loadDateEnd, List<Integer> state,
			String id, boolean isFull, List<Integer> orderAcceptanceState,
			Integer dataSource, Long sourceAppId, String sortKey,
			String sortOrder) {
		LambdaQueryWrapper<ShippingRequirementPlat> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class)
				.like(StringUtils.isNotBlank(sourcePortName),
						ShippingRequirementPlat::getSourcePortName,
						sourcePortName)
				.like(StringUtils.isNotBlank(destinationPortName),
						ShippingRequirementPlat::getDestinationPortName,
						destinationPortName)
				.like(StringUtils.isNotBlank(handlerName),
						ShippingRequirementPlat::getHandlerName, handlerName)
				.eq(Objects.nonNull(demandLevel),
						ShippingRequirementPlat::getDemandLevel, demandLevel)
				.ge(Objects.nonNull(loadDateStart),
						ShippingRequirementPlat::getLoadDate, loadDateStart)
				.le(Objects.nonNull(loadDateEnd),
						ShippingRequirementPlat::getLoadDate, loadDateEnd)
				.eq(ShippingRequirementPlat::getDel,
						CommonDef.Symbol.NO.getCode())
				// 已关闭的数据不在页面上显示
				.ne(ShippingRequirementPlat::getState,
						ShippingRequirementPlatDef.State.CLOSED.getCode())
				.eq(!isFull, ShippingRequirementPlat::getHandlerId,
						UserInfoContextHolder.getContext().getUserId());
		if (CollectionUtils.isNotEmpty(state)) {
			wrapper.in(ShippingRequirementPlat::getState, state);
		}
		// 货主名称或编号
		if (StringUtils.isNotBlank(id)) {
			wrapper.and(x -> x.or(y -> {
				// 第一部分：直接匹配 ID
				y.like(ShippingRequirementPlat::getId, id);
				// 第二部分：处理 JSON 字段的模糊匹配逻辑
				//// 检查 name 字段是否非空，并模糊匹配
				y.or(z -> z.apply(
						"JSON_UNQUOTE(JSON_EXTRACT(owner_enterprise, '$.name')) IS NOT NULL "
								+ "AND JSON_UNQUOTE(JSON_EXTRACT(owner_enterprise, '$.name')) != '' "
								+ "AND JSON_UNQUOTE(JSON_EXTRACT(owner_enterprise, '$.name')) != 'null' "
								+ "AND JSON_UNQUOTE(JSON_EXTRACT(owner_enterprise, '$.name')) LIKE CONCAT('%', {0}, '%')",
						id)).or(z -> {
							// 如果 name 为空，则匹配 realName
							z.apply("(JSON_UNQUOTE(JSON_EXTRACT(owner_enterprise, '$.name')) IS NULL "
									+ "OR JSON_UNQUOTE(JSON_EXTRACT(owner_enterprise, '$.name')) = '' "
									+ "OR JSON_UNQUOTE(JSON_EXTRACT(owner_enterprise, '$.name')) = 'null')"
									+ "AND JSON_UNQUOTE(JSON_EXTRACT(owner_enterprise, '$.realName')) LIKE CONCAT('%', {0}, '%')",
									id);
						});
			}));
		}
		if (CollectionUtils.isNotEmpty(orderAcceptanceState)) {
			wrapper.in(ShippingRequirementPlat::getOrderAcceptanceState,
					orderAcceptanceState);
		}
		if (Objects.nonNull(dataSource)) {
			wrapper.eq(ShippingRequirementPlat::getDataSource, dataSource);
		}
		if (Objects.nonNull(sourceAppId)) {
			wrapper.eq(ShippingRequirementPlat::getSourceAppId, sourceAppId);
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 优先需求来源为平台船运需求1、三方需求2顺序排序
			// 再按已发布1、已结束2顺序排序
			// 相同状态按需求等级排序，等级越高越在前面，都相同按创建时间倒序
			wrapper.orderByAsc(ShippingRequirementPlat::getDataSource);
			wrapper.orderBy(Boolean.TRUE, Boolean.TRUE,
					ShippingRequirementPlat::getState);
			wrapper.orderByDesc(ShippingRequirementPlat::getDemandLevel);
			wrapper.orderByDesc(ShippingRequirementPlat::getCreatedTime);
		}
		return PaginationUtils.handelPage(page, size,
				this.packAcceptVo(repository.selectList(wrapper)));
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<ShippingRequirementPlatVo> customPaging(
			Integer page, Integer size, String platId, Long sourcePortId,
			Long destinationPortId, LocalDate loadDateStart,
			LocalDate loadDateEnd) {
		LambdaQueryWrapper<ShippingRequirementPlat> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class)
				.eq(StringUtils.isNotBlank(platId),
						ShippingRequirementPlat::getId, platId)
				.eq(Objects.nonNull(sourcePortId),
						ShippingRequirementPlat::getSourcePortId, sourcePortId)
				.eq(Objects.nonNull(destinationPortId),
						ShippingRequirementPlat::getDestinationPortId,
						destinationPortId)
				.ge(Objects.nonNull(loadDateStart),
						ShippingRequirementPlat::getLoadDate, loadDateStart)
				.le(Objects.nonNull(loadDateEnd),
						ShippingRequirementPlat::getLoadDate, loadDateEnd)
				.eq(ShippingRequirementPlat::getState,
						ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
								.getCode())
				.eq(ShippingRequirementPlat::getDel,
						CommonDef.Symbol.NO.getCode());
		wrapper.orderByDesc(ShippingRequirementPlat::getCreatedTime);
		// 查询出所有已发布并且未删除的船运需求
		List<ShippingRequirementPlat> platList = repository.selectList(wrapper);

		List<ShippingRequirementPlatVo> shippingRequirementPlatList = this
				.packVo(platList);
		if (CollectionUtils.isNotEmpty(shippingRequirementPlatList)) {
			// 对列表进行排序
			// 优先按后台设置的需求等级排序，等级越高越在前面，相同等级按标签规则排序，如果都相同按创建时间倒序
			shippingRequirementPlatList.sort(Comparator
					.comparing(
							(ShippingRequirementPlatVo vo) -> vo.getPlat()
									.getDemandLevel(),
							Comparator.reverseOrder())
					.thenComparing(ShippingRequirementPlatVo::getScore,
							Comparator.reverseOrder())
					.thenComparing(vo -> vo.getPlat().getCreatedTime(),
							Comparator.reverseOrder()));
		}
		return this.handelPage(page, size, shippingRequirementPlatList);
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<ShippingRequirementCustomerVo> orderPaging(
			Integer page, Integer size, String keyword, List<Integer> states,
			String title, String shipKey, String sourcePortName,
			String destinationPortName) {
		LambdaQueryWrapper<ShippingRequirementPlat> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class)
				.eq(ShippingRequirementPlat::getOwnerId,
						UserInfoContextHolder.getContext().getUserId())
				.eq(ShippingRequirementPlat::getDel,
						CommonDef.Symbol.NO.getCode());
		// 货主所有的找船需求数据
		List<ShippingRequirementPlat> shippingRequirementPlatList = repository
				.selectList(wrapper);
		// 货主的所有船运单数据（包括被删除的的数据)
		List<TransportOrderShip> transportOrderShipList = transportOrderShipService
				.selectOrders(CustomerContextHolder.getCustomerLoginVo()
						.getProxyAccount().getId());
		// 从船运单中找出 srpId非空的对象(包括已经删除的数据）
		Set<String> nonNullSrpIdSet = transportOrderShipList.stream()
				.map(TransportOrderShip::getSrpId).filter(Objects::nonNull)
				.collect(Collectors.toSet());
		// 货主找船需求里面去除在船运单存在的数据，再筛出找船中的船运需求（管理后台已发布的船运需求），处理中的船运需求（管理后台待发布的船运需求）
		List<ShippingRequirementPlat> filteredList = shippingRequirementPlatList
				.stream()
				.filter(plat -> !nonNullSrpIdSet.contains(plat.getId()))
				.filter(plat -> ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
						.match(plat.getState())
						|| ShippingRequirementPlatDef.State.TO_BE_PUBLISH
								.match(plat.getState()))
				.toList();
		// 货主船运单（未删除的数据）
		List<TransportOrderShip> orderShipList = transportOrderShipList
				.stream().filter(transportOrderShip -> CommonDef.Symbol.NO
						.getCode().equals(transportOrderShip.getDel()))
				.toList();
		// 货主找船需求 船运单 状态映射 运单状态并封装数据
		List<ShippingRequirementCustomerVo> shippingRequirementCustomerVoList = this
				.packCustomerVo(filteredList, orderShipList, states, keyword,
						title, shipKey, sourcePortName, destinationPortName);
		return this.handelOrderPage(page, size,
				shippingRequirementCustomerVoList);
	}

	@Override
	public Page<ShippingRequirementPlat> selector(Integer page, Integer size,
			String platId, boolean isFull) {
		List<String> srpIdList = transportOrderShipService.findAll().stream()
				.map(TransportOrderShip::getSrpId)
				.filter(StringUtils::isNotBlank).distinct().toList();
		LambdaQueryWrapper<ShippingRequirementPlat> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class)
				.eq(StringUtils.isNotBlank(platId),
						ShippingRequirementPlat::getId, platId)
				.notIn(CollectionUtils.isNotEmpty(srpIdList),
						ShippingRequirementPlat::getId, srpIdList)
				.ne(ShippingRequirementPlat::getDataSource,
						ShippingRequirementPlatDef.DataSource.OUTER.getCode());
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Optional<ShippingRequirementPlatVo> findVoById(String id) {
		return this.findOne(id).map(plat -> {
			ShippingRequirementPlatVo vo = new ShippingRequirementPlatVo();
			vo.setPlat(plat);
			// 数据来源不是三方需求的或者不是待发布的船运需求 需要查询报价信息
			if (!(ShippingRequirementPlatDef.DataSource.OUTER
					.match(plat.getDataSource())
					&& ShippingRequirementPlatDef.State.TO_BE_PUBLISH
							.match(plat.getState()))) {
				List<ShippingRequirementAccept> accepts = acceptService
						.findByPlatId(plat.getId());
				vo.setAccepts(accepts);
				// 设置报价信息+报价里船舶的信息
				if (CollectionUtils.isNotEmpty(accepts)) {
					List<String> shipIds = accepts.stream()
							.map(ShippingRequirementAccept::getShipInfos)
							.filter(Objects::nonNull)
							.filter(list -> !list.isEmpty())
							.map(list -> list.get(0)).map(CarrierShip::getId)
							.filter(Objects::nonNull).map(String::valueOf)
							.toList();
					List<Ship> ships = shipService.findByIds(shipIds);
					Map<String, Ship> mmsiToShipMap = ships.stream()
							.filter(ship -> ship.getId() != null)
							.collect(Collectors.toMap(Ship::getId, ship -> ship,
									(a, b) -> a));
					Map<Long, Ship> acceptIdToShipMap = accepts.stream()
							.filter(accept -> accept.getShipInfos() != null
									&& !accept.getShipInfos().isEmpty())
							.filter(accept -> {
								CarrierShip firstShip = accept.getShipInfos()
										.get(0);
								return firstShip.getId() != null
										&& mmsiToShipMap.get(String.valueOf(
												firstShip.getId())) != null;
							})
							.collect(Collectors.toMap(
									ShippingRequirementAccept::getId,
									accept -> {
										CarrierShip firstShip = accept
												.getShipInfos().get(0);
										Long mmsi = firstShip.getId();
										return mmsiToShipMap
												.get(String.valueOf(mmsi));
									}, (existing, replacement) -> existing));
					List<ShippingRequirementAcceptShipVo> acceptShipVoList = new ArrayList<>();

					for (ShippingRequirementAccept accept : accepts) {
						ShippingRequirementAcceptShipVo acceptShipVo = new ShippingRequirementAcceptShipVo();
						acceptShipVo.setAccept(accept);
						acceptShipVo
								.setShip(acceptIdToShipMap.get(accept.getId()));
						// 银行账户开户人和开户名称是否承运商实名
						acceptShipVo.setBankNameIsRealName(
								this.checkBankName(accept));
						acceptShipVoList.add(acceptShipVo);
					}
					vo.setAcceptShips(acceptShipVoList);
				}
				// 只查询船主的接单信息
				UserInfoContext context = UserInfoContextHolder.getContext();
				if (Objects.nonNull(context)) {
					Long userId = context.getUserId();
					List<ShippingRequirementAcceptVo> acceptVoList = acceptService
							.findVoByPlatId(plat.getId(), userId);
					vo.setAcceptVoList(acceptVoList);
				}
			}
			priceIndexService.findNewestDataByShipRouteId(plat.getShipRouteId())
					.ifPresent(vo::setPriceIndex);
			List<TransportOrderShip> transportOrderShips = transportOrderShipService
					.findBySrpId(id);
			vo.setTransportOrderShipList(transportOrderShips);
			if (Objects.nonNull(plat.getSourceAppId())) {
				applicationService.findOne(plat.getSourceAppId())
						.ifPresent(vo::setApplication);
			}
			return vo;
		});
	}

	@Override
	public Optional<ShippingRequirementCustomerVo> findOwnerVoById(String id) {
		return this.findOne(id).map(this::packOwnerVo);
	}

	@Override
	public Optional<ShippingPlatAndTransportVo> findShippingPlatAndTransportVoById(
			String platId) {
		// 根据船运需求id查询船运需求及船运单详情
		return this.findOne(platId).map(plat -> {
			ShippingPlatAndTransportVo vo = new ShippingPlatAndTransportVo();
			vo.setShippingRequirementPlat(plat);
			List<TransportOrderShip> transportOrderShips = transportOrderShipService
					.findBySrpId(platId);
			if (CollectionUtils.isNotEmpty(transportOrderShips)) {
				TransportOrderShip transportOrderShip = transportOrderShips
						.get(0);
				vo.setTransportOrderShip(transportOrderShip);
				transportOrderDetailsShipService
						.findOne(transportOrderShip.getId())
						.ifPresent(vo::setTransportOrderDetailsShip);
			}
			return vo;
		});
	}

	@Override
	public Optional<ShippingRequirementCustomerVo> orderDetail(String platId,
			String orderId) {
		// 货主运单详情
		ShippingRequirementCustomerVo shippingRequirementCustomerVo = new ShippingRequirementCustomerVo();
		if (Objects.nonNull(platId)) {
			// 根据传入的船运需求id查询 信息
			ShippingRequirementPlat shippingRequirementPlat = this
					.findOne(platId).orElse(null);
			// 船运需求信息不为空时
			if (Objects.nonNull(shippingRequirementPlat)) {
				// 需求是否生成船运单数据
				List<TransportOrderShip> transportOrderShipList = transportOrderShipService
						.findBySrpId(shippingRequirementPlat.getId());
				// 过滤出自己是货主的船运单
				List<TransportOrderShip> orderShipList = transportOrderShipList
						.stream()
						.filter(ship -> ship.getOwnerId() != null
								&& ship.getOwnerId()
										.equals(CustomerContextHolder
												.getCustomerLoginVo()
												.getProxyAccount().getId()))
						.toList();
				if (CollectionUtils.isNotEmpty(orderShipList)) {
					// 该找船需求生成了自己是货主的船运单
					TransportOrderShip transportOrderShip = orderShipList
							.get(0);
					// 返回船运单数据
					shippingRequirementCustomerVo = this
							.packCustomerVoByOrderShip(transportOrderShip);

				} else {
					// 该找船需求没有生成自己是货主的船运单，则返回需求信息
					shippingRequirementCustomerVo = this
							.setCustomerOrderState(shippingRequirementPlat);
					shippingRequirementCustomerVo.setPublishTime(
							shippingRequirementPlat.getCreatedTime());
					usercustomerService
							.findOne(shippingRequirementPlat.getCreatedBy())
							.ifPresent(
									shippingRequirementCustomerVo::setOwener);
					// 编号取船运需求id或船运单id
					shippingRequirementCustomerVo
							.setTitle(shippingRequirementPlat.getId());
					shippingRequirementCustomerVo.setSourcePortName(
							shippingRequirementPlat.getSourcePortName());
					shippingRequirementCustomerVo.setDestinationPortName(
							shippingRequirementPlat.getDestinationPortName());
					shippingRequirementCustomerVo.setOrderShips(
							transportOrderShipService.findBySrpId(
									shippingRequirementPlat.getId()));
					shippingRequirementCustomerVo.setOrderShipVos(
							transportOrderShipService.findVoBySrpId(
									shippingRequirementPlat.getId()));
					shippingRequirementCustomerVo
							.setPlat(shippingRequirementPlat);
				}
			}

		} else {
			// 返回船运单数据
			TransportOrderShip transportOrderShip = transportOrderShipService
					.findOne(orderId).orElse(null);
			if (Objects.nonNull(transportOrderShip)) {
				shippingRequirementCustomerVo = this
						.packCustomerVoByOrderShip(transportOrderShip);
			}
		}
		return Optional.of(shippingRequirementCustomerVo);
	}

	@Override
	public List<ServiceSpecialVo> findServiceSpecials(String name) {
		// 查询拥有服务专员角色的账号
		List<User> attaches = userService.findUsersByPermission(
				AdminPermissionDef.SHIP_DEMAND_DEAL, name);
		if (CollectionUtils.isEmpty(attaches)) {
			return List.of();
		}
		// 查询所有已指派的意向
		LambdaQueryWrapper<ShippingRequirementPlat> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class)
				.eq(ShippingRequirementPlat::getDel,
						CommonDef.Symbol.NO.getCode())
				.in(ShippingRequirementPlat::getState,
						ShippingRequirementPlatDef.State.ENDED.getCode(),
						ShippingRequirementPlatDef.State.CLOSED.getCode());
		List<ShippingRequirementPlat> plats = repository.selectList(wrapper);
		// 过滤出HandlerId为空值的
		List<ShippingRequirementPlat> platsList = plats.stream()
				.filter(shippingRequirementPlat -> Objects
						.nonNull(shippingRequirementPlat.getHandlerId()))
				.toList();
		// 平台需求按处理人分组
		Map<Long, Long> platMap = platsList.stream()
				.collect(Collectors.groupingBy(
						ShippingRequirementPlat::getHandlerId,
						Collectors.counting()));
		// 组装vo
		return attaches.stream().map(user -> {
			ServiceSpecialVo vo = new ServiceSpecialVo();
			vo.setUser(user);
			vo.setDemandCount(platMap.getOrDefault(user.getId(), 0L));
			return vo;
		}).sorted(Comparator.comparingLong(ServiceSpecialVo::getDemandCount)
				.reversed()).toList();
	}

	@Override
	public List<ShippingRequirementPlat> findByState(Integer state) {
		LambdaQueryWrapper<ShippingRequirementPlat> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class)
				.eq(ShippingRequirementPlat::getState, state)
				.eq(ShippingRequirementPlat::getDel,
						CommonDef.Symbol.NO.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public List<ShippingRequirementPlat> findByHandlerIdsAndState(
			List<Long> handlerIds, List<Integer> states) {
		LambdaQueryWrapper<ShippingRequirementPlat> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class)
				.eq(ShippingRequirementPlat::getDel,
						CommonDef.Symbol.NO.getCode());
		wrapper.in(CollectionUtils.isNotEmpty(handlerIds),
				ShippingRequirementPlat::getHandlerId, handlerIds);
		wrapper.in(CollectionUtils.isNotEmpty(states),
				ShippingRequirementPlat::getState, states);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<ShippingRequirementPlat> findLast(Long customerId) {
		LambdaQueryWrapper<ShippingRequirementPlat> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class);
		this.filterDeleted(wrapper);
		wrapper.eq(ShippingRequirementPlat::getOwnerId, customerId);
		wrapper.orderByDesc(ShippingRequirementPlat::getCreatedTime);
		wrapper.last("limit 1");
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public List<ShipSmartVo> findByShipPlat(String platId, Long portId,
			Integer radius, String shipId, List<Integer> status,
			Long tonCapacityMin, Long tonCapacityMax) {
		List<ShipSmartVo> shipSmartVos = new ArrayList<>();
		List<ShipSmartVo> finalShipSmartVos;
		ShippingRequirementPlat shippingRequirementPlat = this.findOne(platId)
				.orElse(null);
		if (Objects.isNull(shippingRequirementPlat)
				|| !ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
						.match(shippingRequirementPlat.getState())) {
			return List.of();
		}

		// 查询出所有船舶中联系方式不为空的数据
		// 过滤查询条件中的船舶名称或mmsi号、最小载重吨、最大载重吨
		List<Ship> shipList = shipService.selectShips(null, shipId,
				tonCapacityMin, tonCapacityMax);
		// 获取有联系方式，没有船运单，如有船运单只在在卸货中、已卸货、已清仓、已完成状态的船舶
		List<Ship> combinedList = this.getCombinedList(shipList);
		if (CollectionUtils.isEmpty(combinedList)) {
			return List.of();
		}
		// 查询出所有常跑航线中始发地城市编码和目的地二级地址不为空的数据
		List<FrequentShipRoute> frequentShipRouteList = frequentShipRouteService
				.findAll().stream()
				.filter(route -> Objects.nonNull(route.getSourceCityCode())
						&& Objects.nonNull(route.getDestinationCityCode()))
				.toList();
		// 查询出所有码头信息，将码头id为key,port对象为value存入map
		Map<Long, Port> portMap = portService.findAll().stream()
				.collect(Collectors.toMap(Port::getId, port -> port,
						(existing, replacement) -> existing));
		// 查询出所有船运单信息，将船舶id为key,船运单对象为value存入map
		// 查询出所有船运单信息，根据船舶id进行分组,然后按时间倒序排序 船运单对象为value存入map
		Map<String, List<TransportOrderShip>> transportOrderShipMap = transportOrderShipService
				.findAll().stream()
				.filter(transportOrderShip -> StringUtils
						.isNotBlank(transportOrderShip.getShipId()))
				.collect(Collectors.groupingBy(TransportOrderShip::getShipId,
						Collectors.collectingAndThen(Collectors.toList(),
								list -> list.stream()
										.sorted(Comparator.comparing(
												TransportOrderShip::getCreatedTime)
												.reversed())
										.collect(Collectors.toList()))));

		// 查找当天已经被邀请过的船舶id
		Set<String> invitedShipIds = this.getInvitedShipIds();

		// 获取货主关注船舶信息 根据船舶id分组
		Map<String, ShipFollowCustom> shipFollowCustomMap = this
				.getShipFollowCustomMap(shippingRequirementPlat);

		for (Ship ship : combinedList) {
			ShipSmartVo shipSmartVo = new ShipSmartVo();
			// 初始化非必要条件的分数 认证船舶136分，当前位置68分，载重吨34分，货仓状态17分，常跑航线7分，历史运单5分 船主关注船舶3
			// 历史轨迹1分（暂无）
			shipSmartVo.setCount(0);
			// 初始化非必要条件的数量
			shipSmartVo.setScore(0);
			shipSmartVo.setId(ship.getId());
			shipSmartVo.setShip(ship);

			// 1判断是否满足认证船舶条件 满足认证船舶 加136分 非必要条件数量增加
			if (ShipDef.State.AUTHENTICATED.match(ship.getState())) {
				shipSmartVo.setCount(shipSmartVo.getCount() + 1);
				shipSmartVo.setScore(shipSmartVo.getScore() + 136);
			}

			// 3判断是否满足载重吨条件 载重吨34分
			this.handleTonCapacity(ship, shipSmartVo, shippingRequirementPlat);

			// 船主关注船舶3分
			this.handelOwnerFollowShip(ship, shipSmartVo, shipFollowCustomMap);

			// 4判断是否满足货仓状态条件 17分
			if (Objects.nonNull(ship.getWarehouseState())) {
				// 满足货仓状态条件 加17分 非必要条件数量增加
				if (CommonDef.Symbol.YES.match(ship.getExistDevice())
						&& DeviceCapturePicDef.RecognizeResult.IDLE
								.match(ship.getWarehouseState())) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 17);
				}
			}

			// 5判断常跑航线 7分
			this.handleFrequentShipRoutes(ship, shipSmartVo,
					frequentShipRouteList, portMap, shippingRequirementPlat);

			// 如果船主没有填写常跑航线信息 则需要调用方法取船舶最新一条的船运单始发港到目的港的航线）
			List<TransportOrderShip> transportOrderShips = this
					.handelTransportOrderShips(
							transportOrderShipMap.get(ship.getId()),
							shipSmartVo);
			if (CollectionUtils.isNotEmpty(transportOrderShips)) {
				// 6判断历史运单 查询出船舶关联的船运单且始发港到目的港不为空的数据
				List<TransportOrderShip> orderShipList = transportOrderShips
						.stream()
						.filter(orderShip -> Objects
								.nonNull(orderShip.getSourcePortId())
								&& Objects.nonNull(
										orderShip.getDestinationPortId()))
						.toList();

				// 取所有船运单始发港到目的港的航线，需要至少有一条与找船需求填写的始发港目的港的航线一致
				this.handleOrderShipList(orderShipList, shipSmartVo,
						shippingRequirementPlat);
			}
			// 设置邀请状态
			if (CollectionUtils.isNotEmpty(invitedShipIds)
					&& invitedShipIds.contains(ship.getId())) {
				shipSmartVo.setAcceptStatus(CommonDef.Symbol.YES.getCode());
			} else {
				shipSmartVo.setAcceptStatus(CommonDef.Symbol.NO.getCode());
			}
			shipSmartVos.add(shipSmartVo);

		}
		// 过滤出满足至少两个非必要条件的数据
		List<ShipSmartVo> filterShip = shipSmartVos.stream()
				.filter(vo -> vo.getCount() >= 2).toList();

		// 是否把当前位置作为前提条件
		boolean b = Objects.nonNull(portId) && Objects.nonNull(radius);
		// 根据港口id和区域范围查询船舶 满足当前位置的船舶
		List<ShipInfo> shipInfos = shipService.findShipInfoByPortId(
				shippingRequirementPlat.getSourcePortId(),
				ShipDef.Area.TWENTY.getName());
		if (b) {
			shipInfos = shipService.findShipInfoByPortId(portId, radius);
		}
		Set<String> shipIds = shipInfos.stream().map(ShipInfo::getId)
				.collect(Collectors.toSet());
		// 当前位置68分
		if (b) {
			// 过滤出id在shipIds中的ShipSmartVo，并对筛选出的ShipSmartVo进行count和score的修改
			filterShip = filterShip.stream().filter(
					shipSmartVo -> shipIds.contains(shipSmartVo.getId()))
					.peek(shipSmartVo -> {
						shipSmartVo.setCount(shipSmartVo.getCount() + 1);
						shipSmartVo.setScore(shipSmartVo.getScore() + 68);
					}).toList();

		} else {
			// 找出满足至少两个非必要条件的数据和找出满足当前位置的船舶 对这些数据进行count和score更新
			for (ShipSmartVo shipSmartVo : filterShip) {
				if (shipIds.contains(shipSmartVo.getId())) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 68);
				}
			}
		}

		// 找出满足必要条件和至少满足三个非必要条件的数据 然后调船顺查询信息
		List<ShipSmartVo> filteredShipSmartVos = new ArrayList<>(
				filterShip.stream().filter(vo -> vo.getCount() >= 3).toList());
		// 先按count降序排序， count相同时，按score降序排序
		filteredShipSmartVos.sort(Comparator.comparingInt(ShipSmartVo::getCount)
				.reversed().thenComparing(Comparator
						.comparingInt(ShipSmartVo::getScore).reversed()));

		// 填充船舶信息，比如船舶类型以及船舶状态等
		String idsString = filteredShipSmartVos.stream().map(ShipSmartVo::getId)
				.sorted().collect(Collectors.joining(","));
		List<ShipInfo> shipInfosList = SpringUtil.getBean(ShipService.class)
				.findByBatchAsi(idsString);
		Map<String, ShipInfo> shipInfoMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(shipInfosList)) {
			shipInfoMap = shipInfosList.stream().collect(
					Collectors.toMap(ShipInfo::getId, shipInfo -> shipInfo));
		}
		for (ShipSmartVo shipSmartVo : filteredShipSmartVos) {
			ShipInfo shipInfo = shipInfoMap.get(shipSmartVo.getId());
			if (Objects.nonNull(shipInfo)) {
				this.handelShipType(shipInfo, shipSmartVo);
				// 调用方法处理船舶信息里面的当前位置和船名，以及设置船舶状态
				this.handleShipSmart(shipSmartVo, shipInfo);
			}
		}

		// 过滤符合查询的状态的船舶
		if (CollectionUtils.isNotEmpty(filteredShipSmartVos)
				&& CollectionUtils.isNotEmpty(status)) {
			filteredShipSmartVos = filteredShipSmartVos.stream()
					.filter(shipSmartVo -> {
						String s = shipSmartVo.getStatus();
						if (StringUtils.isBlank(s)) {
							return false;
						}
						try {
							Integer statusInt = Integer.parseInt(s);
							return status.contains(statusInt);
						} catch (NumberFormatException e) {
							// 如果不能转换成数字，直接跳过
							return false;
						}
					}).toList();
		}

		// 限制最大数量为 40
		if (filteredShipSmartVos.size() > 40) {
			filteredShipSmartVos = filteredShipSmartVos.subList(0, 40);
		}

		finalShipSmartVos = filteredShipSmartVos;
		return finalShipSmartVos;
	}

	@Override
	public List<ShipSmartVo> findByExtentInvite(List<GeoPoint> geoPointList,
			String type, Long time, String platId) {
		List<ShipSmartVo> shipSmartVos = new ArrayList<>();
		List<ShipSmartVo> finalShipSmartVos;
		if (StringUtils.isBlank(platId)) {
			return List.of();
		}
		ShippingRequirementPlat shippingRequirementPlat = this.findOne(platId)
				.orElse(null);
		if (Objects.isNull(shippingRequirementPlat)
				|| !ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
						.match(shippingRequirementPlat.getState())) {
			return List.of();
		}

		// 自定义查船的列表
		List<ShipInfoVo> byCustomExtent = shipService
				.findByCustomExtent(geoPointList, type, time);
		if (CollectionUtils.isEmpty(byCustomExtent)) {
			return List.of();
		}
		// 过滤出有联系方式的船舶
		List<ShipInfoVo> filterShipList = byCustomExtent.stream()
				.filter(Objects::nonNull)
				.filter(ship -> StringUtils.isNotBlank(ship.getMobile()))
				.toList();
		if (CollectionUtils.isEmpty(filterShipList)) {
			return List.of();
		}
		Map<String, ShipInfoVo> shipInfoMap = filterShipList.stream().collect(
				Collectors.toMap(ShipInfoVo::getId, shipInfoVo -> shipInfoVo));
		// 有联系方式的船舶的id
		List<String> shipIds = filterShipList.stream().map(ShipInfoVo::getId)
				.filter(Objects::nonNull).toList();
		if (CollectionUtils.isEmpty(shipIds)) {
			return List.of();
		}
		// 根据船舶id得到需要返回的船舶
		List<Ship> shipList = shipService.findByIdsNoDeleted(shipIds);

		// 查找与船运需求的始发港到目的港的航线一致的所有的常跑航线
		List<FrequentShipRoute> frequentShipRouteList = this
				.getFrequentShipRouteList(shippingRequirementPlat);
		// 查询出所有常跑航线中始发地城市编码和目的地二级地址不为空的数据
		List<FrequentShipRoute> allFrequentShipRouteList = frequentShipRouteService
				.findAll().stream()
				.filter(route -> Objects.nonNull(route.getSourceCityCode())
						&& Objects.nonNull(route.getDestinationCityCode()))
				.toList();

		// 查找与船运需求的始发港到目的港的航线一致的所有的船运单
		List<TransportOrderShip> transportOrderShipList = this
				.getTransportOrderShipList(shippingRequirementPlat);

		// 获取货主关注船舶信息 根据船舶id分组
		Map<String, ShipFollowCustom> shipFollowCustomMap = this
				.getShipFollowCustomMap(shippingRequirementPlat);

		// 查找当天已经被邀请过的船舶id
		Set<String> invitedShipIds = this.getInvitedShipIds();

		for (Ship ship : shipList) {
			// 初始化满足条件的数量和得分
			// 认证船舶68、载重吨34、货仓状态17、常跑航线7、历史运单5、链云用户关注的船3
			// 历史轨迹1分（暂无）
			ShipSmartVo shipSmartVo = new ShipSmartVo();
			shipSmartVo.setCount(0);
			shipSmartVo.setScore(0);
			shipSmartVo.setId(ship.getId());
			shipSmartVo.setShip(ship);

			// 判断认证船舶
			if (ShipDef.State.AUTHENTICATED.match(ship.getState())) {
				shipSmartVo.setCount(shipSmartVo.getCount() + 1);
				shipSmartVo.setScore(shipSmartVo.getScore() + 68);
			}

			// 判断载重吨
			if (Objects.nonNull(ship.getTonCapacity())) {
				Integer dwtMax = shippingRequirementPlat.getTonMax();
				Integer dwtMin = shippingRequirementPlat.getTonMin();
				if (Objects.nonNull(dwtMax) && Objects.nonNull(dwtMin)) {
					// 填写了船舶吨位要求,船舶信息内维护的载重吨 需要满足于这个范围
					if (ship.getTonCapacity() >= dwtMin
							&& ship.getTonCapacity() <= dwtMax) {
						shipSmartVo.setCount(shipSmartVo.getCount() + 1);
						shipSmartVo.setScore(shipSmartVo.getScore() + 34);
					}
				} else if (Objects
						.nonNull(shippingRequirementPlat.getFreightTons())) {
					// 未填写吨位要求，载重吨需大于运货吨数才算满足
					if (ship.getTonCapacity() > shippingRequirementPlat
							.getFreightTons()) {
						shipSmartVo.setCount(shipSmartVo.getCount() + 1);
						shipSmartVo.setScore(shipSmartVo.getScore() + 34);
					}
				}
			}

			// 判断货仓状态 17分
			if (Objects.nonNull(ship.getWarehouseState())) {
				// 满足货仓状态条件 加17分 非必要条件数量增加
				if (CommonDef.Symbol.YES.match(ship.getExistDevice())
						&& DeviceCapturePicDef.RecognizeResult.IDLE
								.match(ship.getWarehouseState())) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 17);
				}
			}

			// 判断常跑航线
			if (Objects.nonNull(ship.getCarrier())) {
				// 根据船舶承运商id查询符合的常跑航线
				List<FrequentShipRoute> frequentShipRoutes = frequentShipRouteList
						.stream()
						.filter(route -> Objects.equals(route.getCaptainId(),
								ship.getCarrier()))
						.toList();
				if (CollectionUtils.isNotEmpty(frequentShipRoutes)) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 7);
					// 如果满足常跑航线条件，则将满足条件的常跑航线赋值给 智能匹配船舶的常跑航线
					shipSmartVo.setFrequentShipRoutes(frequentShipRoutes);
				} else if (CollectionUtils
						.isNotEmpty(allFrequentShipRouteList)) {
					// 如果没有满足常跑航线条件，则将将船主填写的常跑航线信息赋值进去
					List<FrequentShipRoute> routeList = allFrequentShipRouteList
							.stream()
							.filter(route -> Objects.equals(
									route.getCaptainId(), ship.getCarrier()))
							.toList();
					shipSmartVo.setFrequentShipRoutes(routeList);
				}
			}

			// 判断历史运单
			if (Objects.nonNull(ship.getCarrier())) {
				// 根据船舶承运商id查询符合的船运单
				List<TransportOrderShip> transportOrderShips = transportOrderShipList
						.stream()
						.filter(orderShip -> Objects.equals(
								orderShip.getCaptainId(), ship.getCarrier()))
						.toList();
				if (CollectionUtils.isNotEmpty(transportOrderShips)) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 5);
				}
			}

			// 判断链云用户关注的船
			if (Objects.nonNull(shipFollowCustomMap.get(ship.getId()))) {
				// 满足货主关注条件 非必要条件数量增加
				shipSmartVo.setCount(shipSmartVo.getCount() + 1);
				shipSmartVo.setScore(shipSmartVo.getScore() + 3);
			}

			// 设置邀请状态
			if (CollectionUtils.isNotEmpty(invitedShipIds)
					&& invitedShipIds.contains(ship.getId())) {
				shipSmartVo.setAcceptStatus(CommonDef.Symbol.YES.getCode());
			} else {
				shipSmartVo.setAcceptStatus(CommonDef.Symbol.NO.getCode());
			}

			shipSmartVos.add(shipSmartVo);
		}

		// 填充船舶信息，比如船舶类型以及船舶状态等
		for (ShipSmartVo shipSmartVo : shipSmartVos) {
			ShipInfoVo shipInfoVo = shipInfoMap.get(shipSmartVo.getId());
			if (Objects.nonNull(shipInfoVo)) {
				shipSmartVo.setType(shipInfoVo.getType());

				shipSmartVo.setCnname(shipInfoVo.getCnname());
				shipSmartVo.setName(shipInfoVo.getName());
				if (Objects.nonNull(shipSmartVo.getShip())) {
					String cname = shipSmartVo.getShip().getCnname();
					String name = shipSmartVo.getShip().getName();
					if (StringUtils.isNotBlank(cname)
							|| StringUtils.isNotBlank(name)) {
						shipSmartVo.setShipName(cname + "/" + name);
					} else {
						String shipInfoCname = shipInfoVo.getCnname();
						String shipInfoName = shipInfoVo.getName();
						shipSmartVo.setShipName(
								shipInfoCname + "/" + shipInfoName);
					}
				}
				shipSmartVo.setLat(shipInfoVo.getLat());
				shipSmartVo.setLon(shipInfoVo.getLon());
				shipSmartVo.setLocation(this.handelLocation(shipInfoVo.getLon(),
						shipInfoVo.getLat()));
				shipSmartVo.setStatus(shipInfoVo.getStatus());
			}
		}

		// 先按count降序排序， count相同时，按score降序排序
		shipSmartVos.sort(Comparator.comparingInt(ShipSmartVo::getCount)
				.reversed().thenComparing(Comparator
						.comparingInt(ShipSmartVo::getScore).reversed()));
		finalShipSmartVos = shipSmartVos;
		return finalShipSmartVos;
	}

	@Override
	public Long count(LocalDateTime beginTime, LocalDateTime endTime) {
		LambdaQueryWrapper<ShippingRequirementPlat> wrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class)
				.eq(ShippingRequirementPlat::getDel,
						CommonDef.Symbol.NO.getCode());
		wrapper.ne(ShippingRequirementPlat::getState,
				ShippingRequirementPlatDef.State.CLOSED.getCode());
		wrapper.apply(Objects.nonNull(beginTime), "created_time >= {0}",
				beginTime);
		wrapper.apply(Objects.nonNull(endTime), "created_time <= {0}", endTime);
		return repository.selectCount(wrapper);
	}

	@Override
	@History(success = HistoryDef.SHIPPING_DEMAND_PUBLISH_ADD, bizNo = "{{#plat.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_PUBLISH)
	public ShippingRequirementPlat createRequirementPlat(
			ShippingRequirementPlat shippingRequirementPlat) {
		// 管理后台新增
		// 谁新增的 船务专员就是谁
		User user = UserContextHolder.getUser();
		if (Objects.nonNull(user)) {
			shippingRequirementPlat.setHandlerId(user.getId());
			shippingRequirementPlat.setHandlerName(user.getName());
		}
		ShippingRequirementPlat plat = this.create(shippingRequirementPlat);
		HistoryContext.putVariable("plat", plat);
		this.sendNotice(plat);
		return plat;
	}

	@Override
	public Optional<ShippingRequirementPlat> ownerCreate(
			ShippingRequirementPlat shippingRequirementPlat) {
		// 设置货主信息
		Customer customer = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount();
		if (Objects.nonNull(customer)) {
			shippingRequirementPlat.setOwnerId(customer.getId());
			Enterprise ownerEnterprise = new Enterprise();
			ownerEnterprise.setUnifiedSocialCreditCode(
					customer.getUnifiedSocialCreditCode());
			ownerEnterprise.setName(customer.getInstitutionName());
			ownerEnterprise
					.setLegalRepresentative(customer.getLegalRepresentative());
			ownerEnterprise.setRealName(customer.getRealName());
			ownerEnterprise.setMobile(customer.getMobile());
			ownerEnterprise.setCode(customer.getCode());
			shippingRequirementPlat.setOwnerEnterprise(ownerEnterprise);
		}
		// 如果是委托的话，后台是“待发布”，货主端是“处理中”，等管理后台发布后，再分配船务专员
		if (ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
				.match(shippingRequirementPlat.getState())) {
			// 自动平均分配给指定的船务专员 shippingHandlerProperties 电联类型为电话号，电联号码是专员的
			this.assignHandlerToRequirement(shippingRequirementPlat);
		}
		// 新增
		ShippingRequirementPlat plat = this.create(shippingRequirementPlat);
		// 委托平台下单(待发布)，要等管理后台修改提交了，才会给货主发站内；自主发布找船需求(已发布)就是之前的发布流程
		if (ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
				.match(plat.getState())) {
			// 给货主发站内
			this.sendNotice(plat);
			// 给船务专员发送企微消息
			this.sendWxNotice(plat.getId());
		}
		return Optional.of(plat);
	}

	// 冈好运新增的船运需求
	@Override
	public String createPlat(ShippingRequirementPlat shippingRequirementPlat) {
		// 数据来源是三方的
		if (ShippingRequirementPlatDef.DataSource.OUTER
				.match(shippingRequirementPlat.getDataSource())) {
			// 则设置货主id为冈好运的
			shippingRequirementPlat.setOwnerId(transportProperties.getId());
			// 设置来源应用id
			shippingRequirementPlat.setSourceAppId(Objects
					.requireNonNull(ApplicationInfoContextHolder.getAppId()));
		}
		// 自动平均分配给指定的船务专员 shippingHandlerProperties 电联类型为电话号，电联号码是专员的
		this.assignHandlerToRequirement(shippingRequirementPlat);
		// 新增
		ShippingRequirementPlat plat = this.create(shippingRequirementPlat);
		// 给船务专员发送企微消息
		this.sendWxNotice(plat.getId());
		// 返回这边的船运需求id
		return plat.getId();
	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.SHIPPING_DEMAND_PUBLISH_UPDATE, type = LogDef.LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_PUBLISH, bizNo = "{{#plat.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#plat.getId()}}") })
	@History(success = HistoryDef.SHIPPING_DEMAND_PUBLISH_UPDATE, isSaveChange = true, bizNo = "{{#plat.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_PUBLISH)
	public ShippingRequirementPlat updateRequirementPlat(
			ShippingRequirementPlat plat) {
		// 如果是待发布的状态，流转为已发布，并且给货主发送短信和站内
		if (ShippingRequirementPlatDef.State.TO_BE_PUBLISH
				.match(plat.getState())) {
			plat.setState(
					ShippingRequirementPlatDef.State.TO_BE_ALLOCATED.getCode());
			if (ShippingRequirementPlatDef.DataSource.INNER
					.match(plat.getDataSource())) {
				// 委托平台下单，要等管理后台修改提交了，才会给货主发站内
				// 给货主发站内
				this.sendNotice(plat);
			}
		}
		ShippingRequirementPlat plat1 = this.updateAllProperties(plat);
		// 如果是三方的，需要把修改后的船运需求推给三方
		if (ShippingRequirementPlatDef.DataSource.OUTER
				.match(plat1.getDataSource())) {
			// 船运需求修改推送给三方
			handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
					.getCode(),
					ShippingCallbackDef.PushType.UPDATE_SHIP_PLAT.getCode(),
					String.valueOf(plat1.getId()));
		}
		return plat1;
	}

	@Override
	@History(success = HistoryDef.SHIPPING_DEMAND_PUBLISH_CHANGE_ASSIGN, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_PUBLISH, kvParam = {
			@History.KeyValuePair(key = "#userName#", value = "{{#userName!=null?#userName:''}}") })
	public void assign(String id, Long handlerId) {
		// 获取专员信息
		Optional<User> userOp = userService.findOne(handlerId);
		// 将被指派的专员名称存入操作历史
		userOp.ifPresent(
				user -> HistoryContext.putVariable("userName", user.getName()));
		// 将专员回写到平台需求表中
		this.findOne(id).ifPresent(plat -> {
			plat.setHandlerId(handlerId);
			userOp.ifPresent(user -> {
				plat.setHandlerName(user.getName());
				// 变更指派，变更后的电联号码改为新的指派人的
				plat.setElectricContactNumber(user.getMobile());
			});
			this.updateAllProperties(plat);
			// 插入记录到指派表
			Assign assign = new Assign();
			assign.setCorrelationId(Long.valueOf(id));
			assign.setUserId(handlerId);
			assign.setType(AssignDef.Type.REQUIREMENT_PLAT.getCode());
			assignService.create(assign);

			if (ShippingRequirementPlatDef.State.TO_BE_ALLOCATED.match(
					plat.getState()) && Objects.nonNull(plat.getHandlerId())) {
				messageService.sendNotice(WxwMessage.builder()
						.receiptors(
								List.of(String.valueOf(plat.getHandlerId())))
						.url("/logistics/shippingNeed/platformShipNeedInfo/"
								.concat(id))
						.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
						.operationModule(
								WxwDef.NoticeOperationModule.SHIPPING_REQUIREMENT_PLAT
										.getDesc())
						.desc("待修改").keyword(id).content(StringUtils.EMPTY)
						.build());
			}
		});

	}

	@Override
	public ShippingRequirementPlatCountVo statiscShippingRequirementPlat(
			Long userId, Boolean hasDeal) {
		ShippingRequirementPlatCountVo shippingRequirementPlatCountVo = new ShippingRequirementPlatCountVo();
		LambdaQueryWrapper<ShippingRequirementPlat> queryWrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class);
		if (hasDeal) {
			// 统计 待审核接单 的数量 平台船运需求状态已发布，接单状态为待确认状态
			queryWrapper.eq(ShippingRequirementPlat::getOrderAcceptanceState,
					ShippingRequirementPlatDef.OrderAcceptanceState.PENDING_PROCESSING
							.getCode())
					.eq(Objects.nonNull(userId),
							ShippingRequirementPlat::getHandlerId, userId)
					.eq(ShippingRequirementPlat::getState,
							ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
									.getCode())
					.eq(ShippingRequirementPlat::getDel,
							CommonDef.Symbol.NO.getCode());
			Long acceptanceStateCount = repository.selectCount(queryWrapper);
			shippingRequirementPlatCountVo
					.setWaitAcceptanceStateCount(acceptanceStateCount);
		} else {
			shippingRequirementPlatCountVo.setWaitAcceptanceStateCount(0L);
		}

		queryWrapper.clear();
		return shippingRequirementPlatCountVo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@History(success = HistoryDef.SHIPPING_DEMAND_PUBLISH_END, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIPPING_DEMAND_PUBLISH)
	public void complete(String id) {
		ShippingRequirementPlat plat = this.findOne(id).orElseThrow();
		plat.setState(ShippingRequirementPlatDef.State.ENDED.getCode());
		plat.setOrderAcceptanceState(
				ShippingRequirementPlatDef.OrderAcceptanceState.CONFIRMED
						.getCode());
		this.updateAllProperties(plat);
		// 根据platId找出船运需求 且不是三方需求的数据
		List<ShippingRequirementAccept> accepts = acceptService.findByPlatId(id)
				.stream()
				.filter(accept -> ShippingRequirementPlatDef.DataSource.INNER
						.match(accept.getDataSource()))
				.toList();
		// 查询没有抢单成功的船运需求且不是三方需求的数据
		List<ShippingRequirementAccept> acceptsList = accepts.stream().filter(
				shippingRequirementAccept -> !ShippingRequirementAcceptDef.PlatformState.SUCCESSFUL_ORDER_ACCEPTANCE
						.getCode().equals(shippingRequirementAccept.getState())
						&& ShippingRequirementPlatDef.DataSource.INNER.match(
								shippingRequirementAccept.getDataSource()))
				.toList();
		// 查询接单成功的 船运需求ID
		List<String> platIds = accepts.stream().filter(
				shippingRequirementAccept -> ShippingRequirementAcceptDef.PlatformState.SUCCESSFUL_ORDER_ACCEPTANCE
						.getCode().equals(shippingRequirementAccept.getState()))
				.map(ShippingRequirementAccept::getRequirementPlatId).distinct()
				.toList();
		// 在抢单成功的情况下平台结束船运需求，其他抢单的承运商算抢单失败，默认填写拒绝理由：其他船主已抢单
		List<ShippingRequirementAccept> list = new ArrayList<>();
		if (platIds.contains(id)) {
			// 过滤出已经抢单成功的 填写拒绝理由其他船主已抢单
			for (ShippingRequirementAccept shippingRequirementAccept : acceptsList) {
				shippingRequirementAccept.setState(
						ShippingRequirementAcceptDef.PlatformState.ORDER_ACCEPTANCE_FAILED
								.getCode());
				shippingRequirementAccept.setRejectReason("其他船主已抢单");
				shippingRequirementAccept.setRejectOrigin(
						ShippingRequirementAcceptDef.RejectOrigin.OTHERS
								.getCode());
				list.add(shippingRequirementAccept);
			}
			acceptService.batchUpdate(list);
		} else {
			// 在没有抢单成功的情况下平台结束船运需求，正在抢单的承运商算抢单失败，默认填写拒绝理由：此船运需求已结束
			for (ShippingRequirementAccept shippingRequirementAccept : accepts) {
				shippingRequirementAccept.setState(
						ShippingRequirementAcceptDef.PlatformState.ORDER_ACCEPTANCE_FAILED
								.getCode());
				shippingRequirementAccept.setRejectReason("此船运需求已结束");
				shippingRequirementAccept.setRejectOrigin(
						ShippingRequirementAcceptDef.RejectOrigin.OTHERS
								.getCode());
				list.add(shippingRequirementAccept);

				Optional<Customer> captain = service
						.findById(shippingRequirementAccept.getCaptainId());
				captain.ifPresent(customer -> {
					messageService.sendNotice(Messages.builder()
							.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
									SendType.USERMESSAGE.getCode()))
							.type(UserMessageDef.MessageType.TRANSFORM
									.getCode())
							.title(MessageFormat.format(
									UserMessageConstants.UNFIT_SHIP_TEMPLATE,
									shippingRequirementAccept
											.getRequirementPlatId()))
							.receiptors(List.of(String.valueOf(
									shippingRequirementAccept.getCaptainId())))
							.url(UserMessageConstants.GRAP_ORDER_DETAIL_PAGE)
							.role(AppTypeDef.AppType.CHUAN_WU.getCode())
							.detailId(String
									.valueOf(shippingRequirementAccept.getId()))
							.initiator(UserMessageDef.BusinessInitiator.initiate
									.getCode())
							.templateCode(wxSubscriptionProperties
									.getOrderFailedCode())
							.params(Map.of("index",
									shippingRequirementAccept
											.getRequirementPlatId()))
							.mobile(customer.getMobile()).build());

					// 发送app推送
					messageService.sendNotice(Messages.builder()
							.messageTypes(
									List.of(SendType.PUSH_MESSAGE.getCode()))
							.type(UserMessageDef.MessageType.TRANSFORM
									.getCode())
							.title(UserMessageConstants.TRANSPORT_TITLE)
							.content(MessageFormat.format(
									UserMessageConstants.UNFIT_SHIP_TEMPLATE,
									shippingRequirementAccept
											.getRequirementPlatId()))
							.appTypes(List.of(AppType.SHIP))
							.bizNo(String
									.valueOf(shippingRequirementAccept.getId()))
							.moduleType(
									UserMessageConstants.GRAP_ORDER_DETAIL_PAGE)
							.receiptors(List.of(String.valueOf(String.valueOf(
									shippingRequirementAccept.getCaptainId()))))
							.build());
				});
			}
			if (Objects.nonNull(plat.getSourceAppId())) {
				// 船主接单信息修改推送给三方
				handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
						.getCode(),
						ShippingCallbackDef.PushType.COMPLETE_PLAT.getCode(),
						String.valueOf(plat.getId()));
			}
			acceptService.batchUpdate(list);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void close(String id) {
		ShippingRequirementPlat plat = this.findOne(id).orElseThrow();
		plat.setId(id);
		plat.setState(ShippingRequirementPlatDef.State.CLOSED.getCode());
		plat.setOrderAcceptanceState(
				ShippingRequirementPlatDef.OrderAcceptanceState.CONFIRMED
						.getCode());
		// 根据platId找出船运需求 且不是三方需求的数据
		List<ShippingRequirementAccept> accepts = acceptService.findByPlatId(id)
				.stream()
				.filter(accept -> ShippingRequirementPlatDef.DataSource.INNER
						.match(accept.getDataSource()))
				.toList();
		List<ShippingRequirementAccept> list = new ArrayList<>();
		// 正在抢单的承运商算抢单失败，默认填写拒绝理由：此船运需求已结束
		for (ShippingRequirementAccept shippingRequirementAccept : accepts) {
			shippingRequirementAccept.setState(
					ShippingRequirementAcceptDef.PlatformState.ORDER_ACCEPTANCE_FAILED
							.getCode());
			shippingRequirementAccept.setRejectReason("此船运需求已结束");
			shippingRequirementAccept.setRejectOrigin(
					ShippingRequirementAcceptDef.RejectOrigin.OTHERS.getCode());
			list.add(shippingRequirementAccept);
		}
		acceptService.batchUpdate(list);
		if (Objects.nonNull(plat.getSourceAppId())) {
			// 船主接单信息修改推送给三方
			handle(ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT
					.getCode(),
					ShippingCallbackDef.PushType.CLOSE_PLAT.getCode(),
					String.valueOf(plat.getId()));
		}
		this.updateAllProperties(plat);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void invite(String platId, List<String> shipIds) {
		// 管理后台点邀请的那个人的电话
		User user = UserContextHolder.getUser();
		String phone = Objects.nonNull(user) ? user.getMobile() : "";
		for (String shipId : shipIds) {
			// 发短信
			shipService.findOne(shipId).ifPresent(ship -> {
				Optional<Customer> captain;
				if (Objects.isNull(ship.getCarrier())) {
					captain = Optional.empty();
				} else {
					captain = usercustomerService.findById(ship.getCarrier());
				}
				String shipName = "";
				String cname = ship.getCnname();
				String name = ship.getName();
				if (StringUtils.isNotBlank(cname)
						&& (StringUtils.isNotBlank(name))) {
					shipName = cname + "/" + name;
				} else if (StringUtils.isNotBlank(cname)) {
					shipName = cname;
				} else if ((StringUtils.isNotBlank(name))) {
					shipName = name;
				}
				// 有船主，发短信和站内
				if (captain.isPresent()) {

					messageService.sendNotice(Messages.builder()
							.messageTypes(List.of(SendType.ALIMESSAGE.getCode(),
									SendType.USERMESSAGE.getCode()))
							.type(UserMessageDef.MessageType.TRANSFORM
									.getCode())
							.title(MessageFormat.format(
									UserMessageConstants.ORDER_REMINDER_TEMPLATE,
									shipName, phone))
							.receiptors(
									List.of(String.valueOf(ship.getCarrier())))
							.url(UserMessageConstants.TRANSFER_SHIP_REQUIRE_DETAIL_PAGE)
							.role(AppTypeDef.AppType.CHUAN_WU.getCode())
							.detailId(String.valueOf(platId))
							.initiator(UserMessageDef.BusinessInitiator.initiate
									.getCode())
							.templateCode(wxSubscriptionProperties
									.getPlatformTransferShipRequireCode())
							.params(Map.of("ship_name", shipName))
							.mobile(captain.get().getMobile()).build());

					// 发送app推送
					messageService.sendNotice(Messages.builder()
							.messageTypes(
									List.of(SendType.PUSH_MESSAGE.getCode()))
							.type(UserMessageDef.MessageType.TRANSFORM
									.getCode())
							.title(UserMessageConstants.TRANSPORT_TITLE)
							.content(MessageFormat.format(
									UserMessageConstants.ORDER_REMINDER_TEMPLATE,
									shipName, phone))
							.appTypes(List.of(AppType.SHIP))
							.bizNo(String.valueOf(platId))
							.moduleType(
									UserMessageConstants.TRANSFER_SHIP_REQUIRE_DETAIL_PAGE)
							.receiptors(List.of(String.valueOf(
									String.valueOf(ship.getCarrier()))))
							.build());
				} else {
					// 没有船主，给联系方式发短信
					messageService.sendNotice(AliMessage.builder()
							.templateCode(wxSubscriptionProperties
									.getPlatformTransferShipRequireCode2())
							.mobile(ship.getMobile()).build());
				}
				ShipRecommend shipRecommend = new ShipRecommend();
				shipRecommend.setPlatId(platId);
				shipRecommend.setShipId(shipId);
				shipRecommendService.create(shipRecommend);
			});
		}

	}

	@Override
	public void updateDemandLevel(Integer demandLevel, List<String> platIds) {
		LambdaQueryWrapper<ShippingRequirementPlat> queryWrapper = Wrappers
				.lambdaQuery(ShippingRequirementPlat.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.in(ShippingRequirementPlat::getId, platIds);
		List<ShippingRequirementPlat> platList = repository
				.selectList(queryWrapper);
		if (CollectionUtils.isNotEmpty(platList)) {
			for (ShippingRequirementPlat plat : platList) {
				plat.setDemandLevel(demandLevel);
				self.updateRequirementPlat(plat);
			}
		}
	}

	// 冈好运的船运需求 需要
	@Override
	public void publish(ShippingRequirementPlat plat) {
		// 状态变为已发布
		plat.setState(
				ShippingRequirementPlatDef.State.TO_BE_ALLOCATED.getCode());
		// 查询该船运需求是否已报价
		List<ShippingRequirementAccept> accepts = acceptService
				.findByPlatId(plat.getId());
		if (CollectionUtils.isNotEmpty(accepts)) {
			// 已经有人抢单了 报价状态变为 处理中
			plat.setOrderAcceptanceState(
					ShippingRequirementPlatDef.OrderAcceptanceState.PENDING_PROCESSING
							.getCode());
		}
		// 管理后台发布
		// 谁发布的 船务专员就是谁
		User user = UserContextHolder.getUser();
		if (Objects.nonNull(user)) {
			plat.setHandlerId(user.getId());
			plat.setHandlerName(user.getName());
			plat.setElectricContactNumber(user.getMobile());
			plat.setElectricContactNumberType(
					ShippingRequirementPlatDef.ElectricContactNumberType.PHONE
							.getCode());
		}
		ShippingRequirementPlat result = this.updateAllProperties(plat);

		if (ShippingRequirementPlatDef.DataSource.INNER
				.match(result.getDataSource())) {
			// 委托平台下单，要等管理后台点击确认发布了，才会给货主发站内
			// 给货主发站内
			this.sendNotice(result);
		}
	}

	/**
	 * expiration定时任务用到的：三方的船运需求被刷成已结束时，推送给三方
	 * 
	 * @param plat
	 */
	@Override
	public void handleEndThirdParty(ShippingRequirementPlat plat) {
		this.handle(
				ApplicationDef.ApplicationType.SHIPPING_REQUIREMENT.getCode(),
				ShippingCallbackDef.PushType.COMPLETE_PLAT.getCode(),
				String.valueOf(plat.getId()));
	}

	/**
	 * 后台分页组装接单信息
	 *
	 * @param records
	 * @return
	 */
	private List<ShippingRequirementPlatVo> packAcceptVo(
			List<ShippingRequirementPlat> records) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		List<ShippingRequirementPlatVo> vos = new ArrayList<>();
		// 船运需求ids
		List<String> platIds = records.stream()
				.map(ShippingRequirementPlat::getId).filter(Objects::nonNull)
				.toList();
		// 接单信息的map，按platId分组
		Map<String, List<ShippingRequirementAccept>> acceptMap = new HashMap<>();
		List<ShippingRequirementAccept> acceptsByPlatIds = acceptService
				.findByPlatIds(platIds);
		if (CollectionUtils.isNotEmpty(acceptsByPlatIds)) {
			// 按船运需求id分组
			acceptMap = acceptsByPlatIds.stream()
					.filter(accept -> accept.getRequirementPlatId() != null)
					.collect(Collectors.groupingBy(
							ShippingRequirementAccept::getRequirementPlatId));
		}
		for (ShippingRequirementPlat record : records) {
			ShippingRequirementPlatVo vo = new ShippingRequirementPlatVo();
			vo.setPlat(record);
			if (StringUtils.isNotBlank(record.getId()) && CollectionUtils
					.isNotEmpty(acceptMap.get(record.getId()))) {
				vo.setAccepts(acceptMap.get(record.getId()));
			}
			vos.add(vo);
		}
		return vos;
	}

	/**
	 * 批量封装vo小程序分页
	 *
	 * @param records
	 * @return
	 */
	private List<ShippingRequirementPlatVo> packVo(
			List<ShippingRequirementPlat> records) {
		List<ShippingRequirementPlatVo> shippingRequirementPlatVoList = new ArrayList<>();
		List<FrequentShipRoute> frequentShipRoutes = new ArrayList<>();
		List<TransportOrderShip> orderShipList = new ArrayList<>();
		List<Ship> shipList = new ArrayList<>();
		Map<Long, Port> portMap = new HashMap<>();
		Set<String> shipIdNotInStatesSet = new HashSet<>();
		// 登录船务小程序后
		if (Objects.nonNull(UserInfoContextHolder.getContext())
				&& (UserInfoContextHolder.getContext().getUserId()) != null) {
			// 常跑航线和历史运单
			// 根据船主id查询常跑航线中始发地和目的地不为空的数据
			frequentShipRoutes = frequentShipRouteService
					.findByCustomerId(CustomerContextHolder
							.getCustomerLoginVo().getProxyAccount().getId())
					.stream()
					.filter(route -> Objects.nonNull(route.getSourceCityCode())
							&& Objects.nonNull(route.getDestinationCityCode()))
					.toList();
			// 根据船主id查询所有船运单数据
			orderShipList = transportOrderShipService
					.findByCaptainId(CustomerContextHolder.getCustomerLoginVo()
							.getProxyAccount().getId(), null)
					.stream()
					.filter(orderShip -> Objects
							.nonNull(orderShip.getSourcePortId())
							&& Objects
									.nonNull(orderShip.getDestinationPortId()))
					.toList();
			// 根据船主查询联系方式不能为空的船舶
			shipList = shipService
					.findByCarrier(CustomerContextHolder.getCustomerLoginVo()
							.getProxyAccount().getId())
					.stream()
					.filter(ship -> StringUtils.isNotBlank(ship.getMobile()))
					.toList();
			// 查询出所有码头信息，将码头id为key,port对象为value存入map
			portMap = portService.findAll().stream()
					.collect(Collectors.toMap(Port::getId, port -> port,
							(existing, replacement) -> existing));
			// 查询出船运单不在卸货中、已卸货、已清仓、已完成状态的船舶id
			List<Integer> states = List.of(
					TransportOrderShipDef.State.DURING_UNLOADING.getCode(),
					TransportOrderShipDef.State.DISCHARGED.getCode(),
					TransportOrderShipDef.State.COMPLETED.getCode());
			shipIdNotInStatesSet = transportOrderShipService
					.findNotInStates(states).stream()
					.map(TransportOrderShip::getShipId)
					.collect(Collectors.toSet());
		}

		for (ShippingRequirementPlat plat : records) {
			ShippingRequirementPlatVo shippingRequirementPlatVo = new ShippingRequirementPlatVo();
			// 只查询船主的接单信息
			UserInfoContext context = UserInfoContextHolder.getContext();
			if (Objects.nonNull(context)) {
				Long userId = context.getUserId();
				List<ShippingRequirementAcceptVo> acceptVoList = acceptService
						.findVoByPlatId(plat.getId(), userId);
				shippingRequirementPlatVo.setAcceptVoList(acceptVoList);
			}
			shippingRequirementPlatVo
					.setAccepts(acceptService.findByPlatId(plat.getId()));
			shippingRequirementPlatVo.setPlat(plat);
			// 设置标签
			List<List<Integer>> lists = this.handelRouteTags(plat,
					frequentShipRoutes, orderShipList, shipList, portMap,
					shipIdNotInStatesSet);
			shippingRequirementPlatVo.setRouteTags(lists.get(0));
			// 设置标签得分
			shippingRequirementPlatVo.setScore(lists.get(1).get(0));
			shippingRequirementPlatVoList.add(shippingRequirementPlatVo);
		}
		return shippingRequirementPlatVoList;
	}

	/**
	 * 处理标签和标签得分
	 *
	 * @param plat
	 * @return
	 */
	private List<List<Integer>> handelRouteTags(ShippingRequirementPlat plat,
			List<FrequentShipRoute> frequentShipRoutes,
			List<TransportOrderShip> orderShipList, List<Ship> shipList,
			Map<Long, Port> portMap, Set<String> shipIdNotInStatesSet) {
		List<List<Integer>> tagsAndScore = new ArrayList<>();
		List<Integer> routeTags = new ArrayList<>();
		// 标签得分分别为4 3 2
		int score = 0;
		// 登录船务小程序后
		if (Objects.nonNull(UserInfoContextHolder.getContext())
				&& (UserInfoContextHolder.getContext().getUserId()) != null) {
			int trueCount = 0;
			// 判断常跑航线
			// 船主填写的常跑航线中最少有一条 与此船运需求的始发港到目的港(二级地址编码)的一致
			Port sourcePort = portMap.get(plat.getSourcePortId());
			Port destinationPort = portMap.get(plat.getDestinationPortId());
			if (CollectionUtils.isNotEmpty(frequentShipRoutes)
					&& Objects.nonNull(sourcePort)
					&& Objects.nonNull(destinationPort)) {
				String platSourcePortCityCode = sourcePort.getCityCode();
				String platDestinationPortCityCode = destinationPort
						.getCityCode();
				for (FrequentShipRoute route : frequentShipRoutes) {
					if (Objects.equals(platSourcePortCityCode + "00",
							route.getSourceCityCode())
							&& Objects.equals(
									platDestinationPortCityCode + "00",
									route.getDestinationCityCode())) {
						trueCount += 1;
						routeTags.add(
								ShippingRequirementPlatDef.RouteTag.FREQUENT_ROUTE
										.getCode());
						score += 3;
						break;
					}
				}
			}

			// 判断历史航线
			// 船主所有船运单始发港到目的港的航线，需要至少有一条与此船运需求的始发港目的港的航线 一致
			if (CollectionUtils.isNotEmpty(orderShipList)) {
				for (TransportOrderShip orderShip : orderShipList) {
					if (Objects.equals(plat.getSourcePortId(),
							orderShip.getSourcePortId())
							&& Objects.equals(plat.getDestinationPortId(),
									orderShip.getDestinationPortId())) {
						trueCount += 1;
						routeTags.add(
								ShippingRequirementPlatDef.RouteTag.HISTORICAL_ROUTE
										.getCode());
						score += 2;
						break;
					}
				}
			}

			// 货主关注船舶
			Map<String, ShipFollowCustom> shipFollowCustomMap = this
					.getShipFollowCustomMap(plat);

			// 判断智能标签
			List<ShipSmartVo> shipSmartVoList = new ArrayList<>();
			for (Ship ship : shipList) {
				// 查询出船运单不在卸货中、已卸货、已清仓、已完成状态的船舶id
				if (!shipIdNotInStatesSet.contains(ship.getId())) {
					// 满足前提条件，判断非必要条件：
					ShipSmartVo shipSmartVo = new ShipSmartVo();
					shipSmartVo.setId(ship.getId());
					shipSmartVo.setShip(ship);
					shipSmartVo.setCount(trueCount);
					shipSmartVo.setScore(0);

					// 满足认证船舶 非必要条件数量增加
					if (ShipDef.State.AUTHENTICATED.match(ship.getState())) {
						shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					}

					// 判断是否满足载重吨条件
					this.handleTonCapacity(ship, shipSmartVo, plat);

					// 判断是否满货主关注条件
					// 船主关注船舶3分
					this.handelOwnerFollowShip(ship, shipSmartVo,
							shipFollowCustomMap);

					// 判断是否满足货仓状态条件
					if (Objects.nonNull(ship.getWarehouseState())) {
						// 满足货仓状态为空载条件 非必要条件数量增加
						if (CommonDef.Symbol.YES.match(ship.getExistDevice())
								&& DeviceCapturePicDef.RecognizeResult.IDLE
										.match(ship.getWarehouseState())) {
							shipSmartVo.setCount(shipSmartVo.getCount() + 1);
						}
					}

					shipSmartVoList.add(shipSmartVo);

					if (shipSmartVo.getCount() >= 3) {
						break;
					}

				}
			}
			// 分别筛选 满足3个以上条件的、2个条件的
			List<ShipSmartVo> filterList1 = shipSmartVoList.stream()
					.filter(vo -> vo.getCount() >= 3).toList();
			List<ShipSmartVo> filterList2 = shipSmartVoList.stream()
					.filter(vo -> vo.getCount() == 2).toList();
			if (CollectionUtils.isNotEmpty(filterList1)) {
				routeTags.add(0, ShippingRequirementPlatDef.RouteTag.SMART_MATCH
						.getCode());
				score += 4;
			} else if (CollectionUtils.isNotEmpty(filterList2)) {
				// 对这些船判断当前位置
				// 根据港口id和区域范围查询船舶 满足当前位置的船舶
				List<ShipInfo> shipInfos = shipService.findShipInfoByPortId(
						plat.getSourcePortId(), ShipDef.Area.TWENTY.getName());
				Set<String> shipIds = new HashSet<>();
				if (CollectionUtils.isNotEmpty(shipInfos)) {
					shipIds = shipInfos.stream().map(ShipInfo::getId)
							.collect(Collectors.toSet());
				}
				for (ShipSmartVo vo : filterList2) {
					if (shipIds.contains(vo.getId())) {
						routeTags.add(0,
								ShippingRequirementPlatDef.RouteTag.SMART_MATCH
										.getCode());
						score += 4;
						break;
					}
				}
			}
		}
		tagsAndScore.add(routeTags);
		tagsAndScore.add(Collections.singletonList(score));
		return tagsAndScore;
	}

	/**
	 * 根据货主id查询关注信息并根据
	 *
	 * @param plat
	 * @return
	 */
	private Map<String, ShipFollowCustom> getShipFollowCustomMap(
			ShippingRequirementPlat plat) {
		if (Objects.nonNull(plat.getOwnerId())) {
			return shipFollowCustomService.findByCreatedBy(plat.getOwnerId())
					.stream()
					.collect(Collectors.toMap(ShipFollowCustom::getShipId,
							shipFollowCustom -> shipFollowCustom));
		}
		return Map.of();
	}

	/**
	 * 查找当前已经被邀请过的船舶ids
	 */
	private Set<String> getInvitedShipIds() {
		// 查找当天已经被邀请过的船舶id
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime beginTime = now.with(LocalTime.MIN);
		LocalDateTime endTime = now.with(LocalTime.MAX).withNano(0);
		List<ShipRecommend> shipRecommendList = shipRecommendService
				.findByCreatedTime(beginTime, endTime);
		return shipRecommendList.stream().map(ShipRecommend::getShipId)
				.collect(Collectors.toSet());
	}

	/**
	 * 查找与船运需求的始发港到目的港的航线一致的所有的常跑航线
	 */
	private List<FrequentShipRoute> getFrequentShipRouteList(
			ShippingRequirementPlat plat) {
		List<FrequentShipRoute> matchedRoutes = new ArrayList<>();
		// 船运需求的码头
		Port sourcePort = portService.findOne(plat.getSourcePortId())
				.orElse(null);
		Port destinationPort = portService.findOne(plat.getDestinationPortId())
				.orElse(null);
		// 所有常跑航线中始发地城市编码和目的地二级地址不为空的数据
		List<FrequentShipRoute> frequentShipRouteList = frequentShipRouteService
				.findAll().stream()
				.filter(route -> StringUtils
						.isNotBlank(route.getSourceCityCode())
						&& StringUtils
								.isNotBlank(route.getDestinationCityCode()))
				.toList();

		if (CollectionUtils.isNotEmpty(frequentShipRouteList)
				&& Objects.nonNull(sourcePort)
				&& Objects.nonNull(destinationPort)) {
			// 货主找船需求中始发港和目的港的二级地址
			String sourcePortCityCode = sourcePort.getCityCode();
			String destinationPortCityCode = destinationPort.getCityCode();

			matchedRoutes = frequentShipRouteList.stream().filter(route -> {
				String routeSourceCityCode = route.getSourceCityCode();
				String routeDestinationCityCode = route
						.getDestinationCityCode();

				// 截取前4位（去掉最后两位），因为码头对应的二级地址只有两位
				String routeSourceCode = routeSourceCityCode.length() >= 4
						? routeSourceCityCode.substring(0, 4)
						: routeSourceCityCode;

				String routeDestinationCode = routeDestinationCityCode
						.length() >= 4
								? routeDestinationCityCode.substring(0, 4)
								: routeDestinationCityCode;

				return Objects.equals(sourcePortCityCode, routeSourceCode)
						&& Objects.equals(destinationPortCityCode,
								routeDestinationCode);
			}).toList();
		}
		return matchedRoutes;
	}

	/**
	 * 查找与船运需求的始发港到目的港的航线一致的所有的船运单
	 */
	private List<TransportOrderShip> getTransportOrderShipList(
			ShippingRequirementPlat plat) {
		List<TransportOrderShip> matchedOrderShips = new ArrayList<>();
		// 船运需求的码头
		Long sourcePortId = plat.getSourcePortId();
		Long destinationPortId = plat.getDestinationPortId();
		// 所有船运单中始发地城市编码和目的地二级地址不为空的数据
		List<TransportOrderShip> transportOrderShipList = transportOrderShipService
				.findAll().stream()
				.filter(orderShip -> Objects
						.nonNull(orderShip.getSourcePortId())
						&& Objects.nonNull(orderShip.getDestinationPortId()))
				.filter(transportOrderShip -> StringUtils
						.isNotBlank(transportOrderShip.getShipId()))
				.toList();

		if (CollectionUtils.isNotEmpty(transportOrderShipList)
				&& Objects.nonNull(sourcePortId)
				&& Objects.nonNull(destinationPortId)) {

			matchedOrderShips = transportOrderShipList.stream()
					.filter(orderShip -> Objects.equals(sourcePortId,
							orderShip.getSourcePortId())
							&& Objects.equals(destinationPortId,
									orderShip.getDestinationPortId()))
					.toList();
		}
		return matchedOrderShips;
	}

	/**
	 * 处理货主关注船舶
	 *
	 * @param ship
	 * @param shipSmartVo
	 */
	private void handelOwnerFollowShip(Ship ship, ShipSmartVo shipSmartVo,
			Map<String, ShipFollowCustom> shipFollowCustomMap) {
		if (Objects.nonNull(shipFollowCustomMap.get(ship.getId()))) {
			// 满足货主关注条件 非必要条件数量增加
			shipSmartVo.setCount(shipSmartVo.getCount() + 1);
			shipSmartVo.setScore(shipSmartVo.getScore() + 3);
		}
	}

	/**
	 * 处理手动分页
	 *
	 * @param page
	 * @param size
	 * @param shippingRequirementPlatVoList
	 * @return
	 */
	private com.zhihaoscm.common.bean.page.Page<ShippingRequirementPlatVo> handelPage(
			Integer page, Integer size,
			List<ShippingRequirementPlatVo> shippingRequirementPlatVoList) {
		// 计算总数据量和总页数
		int total = shippingRequirementPlatVoList.size();
		int totalPage = total == 0 ? 1 : (total + size - 1) / size;

		// 确保页码在有效范围内
		if (page < 1) {
			page = 1;
		} else if (page > totalPage) {
			page = totalPage;
		}

		// 计算需要跳过的数据量
		int skip = (page - 1) * size;

		// 如果总数据为 0，直接返回一个空的分页对象
		if (total == 0) {
			return new com.zhihaoscm.common.bean.page.Page<>(List.of(), page,
					size, total, totalPage);
		}

		// 使用 stream 流进行分页
		List<ShippingRequirementPlatVo> pagedList = shippingRequirementPlatVoList
				.stream().skip(skip).limit(size).toList();

		return new com.zhihaoscm.common.bean.page.Page<>(pagedList, page, size,
				total, totalPage);
	}

	/**
	 * 通用处理船舶类型的方法
	 *
	 * @param shipInfo
	 *            船舶信息
	 * @param setter
	 *            设置类型的函数式接口
	 */
	private void handleShipTypeCommon(ShipInfo shipInfo,
			Consumer<Integer> setter) {
		if (Objects.nonNull(shipInfo)) {
			if (Objects.nonNull(shipInfo.getType())) {
				// 获取船舶类型code
				String type = shipInfo.getType();
				// 拿到类型名称
				String name = ShipInfoDef.SHIP_TYPE.get(type);
				// 通过名称拿到自定义类型
				if (StringUtils.isNotBlank(name)) {
					setter.accept(ShipDef.Type.getCode(name));
				} else {
					setter.accept(ShipDef.Type.UNKNOWN.getCode());
				}
			} else {
				setter.accept(ShipDef.Type.UNKNOWN.getCode());
			}
		}
	}

	/**
	 * 处理船舶类型（用于 ShipSmartVo）
	 *
	 * @param info
	 * @param shipSmartVo
	 */
	private void handelShipType(ShipInfo info, ShipSmartVo shipSmartVo) {
		this.handleShipTypeCommon(info, shipSmartVo::setType);
	}

	/**
	 * 处理智能推荐船舶信息
	 *
	 * @param shipSmartVo
	 * @param shipInfo
	 */
	private void handleShipSmart(ShipSmartVo shipSmartVo, ShipInfo shipInfo) {
		shipSmartVo.setCnname(shipInfo.getCnname());
		shipSmartVo.setName(shipInfo.getName());
		if (Objects.nonNull(shipSmartVo.getShip())) {
			String cname = shipSmartVo.getShip().getCnname();
			String name = shipSmartVo.getShip().getName();
			if (StringUtils.isNotBlank(cname) || StringUtils.isNotBlank(name)) {
				shipSmartVo.setShipName(cname + "/" + name);
			} else {
				String shipInfoCname = shipInfo.getCnname();
				String shipInfoName = shipInfo.getName();
				shipSmartVo.setShipName(shipInfoCname + "/" + shipInfoName);
			}
		}
		shipSmartVo.setLat(shipInfo.getLat());
		shipSmartVo.setLon(shipInfo.getLon());
		shipSmartVo.setLocation(shipInfo.getLocation());
		shipSmartVo.setStatus(shipInfo.getStatus());
	}

	/**
	 * 处理船舶对应的船运单 如果船主没有填写常跑航线信息 取船舶最新一条的船运单始发港到目的港的航线
	 *
	 * @param transportOrderShips
	 * @param shipSmartVo
	 * @return
	 */
	private List<TransportOrderShip> handelTransportOrderShips(
			List<TransportOrderShip> transportOrderShips,
			ShipSmartVo shipSmartVo) {
		// 如果此船舶承运商选择的常跑航线没有填写 取船舶最新一条的船运单始发港到目的港
		if (CollectionUtils.isEmpty(shipSmartVo.getFrequentShipRoutes())) {
			if (CollectionUtils.isNotEmpty(transportOrderShips)) {
				TransportOrderShip transportOrderShip = transportOrderShips
						.stream().findFirst().orElse(null);
				if (Objects.nonNull(transportOrderShip)) {
					shipSmartVo.setSourcePortId(
							transportOrderShip.getSourcePortId());
					shipSmartVo.setSourcePortName(
							transportOrderShip.getSourcePortName());
					shipSmartVo.setDestinationPortId(
							transportOrderShip.getDestinationPortId());
					shipSmartVo.setDestinationPortName(
							transportOrderShip.getDestinationPortName());
				}
			}
		}
		return transportOrderShips;
	}

	/**
	 * 判断是否满足历史运单条件 取所有船运单始发港到目的港的航线，需要至少有一条与需求填写的始发港目的港的航线一致
	 */
	private void handleOrderShipList(List<TransportOrderShip> orderShipList,
			ShipSmartVo shipSmartVo,
			ShippingRequirementPlat shippingRequirementPlat) {
		if (CollectionUtils.isNotEmpty(orderShipList)) {
			for (TransportOrderShip orderShip : orderShipList) {
				if (Objects.equals(shippingRequirementPlat.getSourcePortId(),
						orderShip.getSourcePortId())
						&& Objects.equals(
								shippingRequirementPlat.getDestinationPortId(),
								orderShip.getDestinationPortId())) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 5);
					break;
				}
			}
		}
	}

	/**
	 * 处理所有前提的船
	 *
	 * @param shipList
	 * @return
	 */
	private List<Ship> getCombinedList(List<Ship> shipList) {
		// 查询出船运单中所有船舶id并去重，去重后存入Set
		Set<String> shipIdsSet = transportOrderShipService.findAll().stream()
				.map(TransportOrderShip::getShipId).collect(Collectors.toSet());
		// 查询出船运单不在卸货中、已卸货、已清仓、已完成状态的船舶id
		List<Integer> states = List.of(
				TransportOrderShipDef.State.DURING_UNLOADING.getCode(),
				TransportOrderShipDef.State.DISCHARGED.getCode(),
				TransportOrderShipDef.State.COMPLETED.getCode());
		Set<String> shipIdNotInStatesSet = transportOrderShipService
				.findNotInStates(states).stream()
				.map(TransportOrderShip::getShipId).collect(Collectors.toSet());
		// 查询出所有船舶中联系方式不为空的数据有船运单且船运单只在卸货中、已卸货、已清仓、已完成状态的船舶
		Set<String> commonShipIds = shipIdsSet.stream()
				.filter(shipId -> !shipIdNotInStatesSet.contains(shipId))
				.collect(Collectors.toSet());
		// 查询出所有船舶中联系方式不为空的数据且没有船运单的数据
		List<Ship> matchedList1 = shipList.stream()
				.filter(ship -> !shipIdsSet.contains(ship.getId())).toList();
		// 查询出所有船舶中联系方式不为空的数据有船运单且船运单只在卸货中、已卸货、已清仓、已完成状态的船舶
		List<Ship> matchedList2 = shipList.stream()
				.filter(ship -> commonShipIds.contains(ship.getId())).toList();
		// 使用Stream.concat()将两个列表合并为一个流，然后收集为列表
		return Stream.concat(matchedList1.stream(), matchedList2.stream())
				.toList();
	}

	/**
	 * 处理载重吨是否满足
	 *
	 * @param ship
	 * @param shipSmartVo
	 * @param shippingRequirementPlat
	 */
	private void handleTonCapacity(Ship ship, ShipSmartVo shipSmartVo,
			ShippingRequirementPlat shippingRequirementPlat) {
		if (Objects.nonNull(ship.getTonCapacity())) {
			Integer dwtMax = shippingRequirementPlat.getTonMax();
			Integer dwtMin = shippingRequirementPlat.getTonMin();
			if (Objects.nonNull(dwtMax) && Objects.nonNull(dwtMin)) {
				// 填写了船舶吨位要求,船舶信息内维护的载重吨 需要满足于这个范围
				if (ship.getTonCapacity() >= dwtMin
						&& ship.getTonCapacity() <= dwtMax) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 34);
				}
			} else if (Objects
					.nonNull(shippingRequirementPlat.getFreightTons())) {
				// 未填写吨位要求，载重吨需大于运货吨数才算满足
				if (ship.getTonCapacity() > shippingRequirementPlat
						.getFreightTons()) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 34);
				}
			}
		}
	}

	/**
	 * 处理常跑航线是否满足
	 *
	 * @param ship
	 * @param shipSmartVo
	 * @param frequentShipRouteList
	 * @param portMap
	 * @param shippingRequirementPlat
	 */
	private void handleFrequentShipRoutes(Ship ship, ShipSmartVo shipSmartVo,
			List<FrequentShipRoute> frequentShipRouteList,
			Map<Long, Port> portMap,
			ShippingRequirementPlat shippingRequirementPlat) {
		if (Objects.nonNull(ship.getCarrier())) {
			// 有满足常跑航线非必要条件的数据放这个列表里面
			List<FrequentShipRoute> frequentShipRoutesList = new ArrayList<>();
			// 根据船舶承运商id查询常跑航线中始发地和目的地不为空的数据
			List<FrequentShipRoute> frequentShipRoutes = frequentShipRouteList
					.stream().filter(route -> Objects
							.equals(route.getCaptainId(), ship.getCarrier()))
					.toList();
			Port sourcePort = portMap
					.get(shippingRequirementPlat.getSourcePortId());
			Port destinationPort = portMap
					.get(shippingRequirementPlat.getDestinationPortId());
			if (CollectionUtils.isNotEmpty(frequentShipRoutes)
					&& Objects.nonNull(sourcePort)
					&& Objects.nonNull(destinationPort)) {
				// 货主找船需求中始发港和目的港的二级地址
				String sourcePortCityCode = sourcePort.getCityCode();
				String destinationPortCityCode = destinationPort.getCityCode();
				for (FrequentShipRoute route : frequentShipRoutes) {
					// 获取常跑航线的二级地址
					String routeSourceCityCode = route.getSourceCityCode();
					String routeDestinationCityCode = route
							.getDestinationCityCode();
					// 去除常跑航线中二级地址最后两位，因为码头对应的二级地址只有四位
					String routeSourceCode = routeSourceCityCode.substring(0,
							routeSourceCityCode.length() - 2);
					String routeDestinationCode = routeDestinationCityCode
							.substring(0,
									routeDestinationCityCode.length() - 2);
					if (Objects.equals(sourcePortCityCode, routeSourceCode)
							&& Objects.equals(destinationPortCityCode,
									routeDestinationCode)) {
						frequentShipRoutesList.add(route);
						// 满足常跑航线条件 加3分 非必要条件数量增加
						shipSmartVo.setCount(shipSmartVo.getCount() + 1);
						shipSmartVo.setScore(shipSmartVo.getScore() + 7);
						break;
					}
				}
			}
			if (CollectionUtils.isNotEmpty(frequentShipRoutesList)) {
				// 如果满足常跑航线条件，则将满足条件的常跑航线赋值给 智能匹配船舶的常跑航线
				shipSmartVo.setFrequentShipRoutes(frequentShipRoutesList);
			} else {
				// 如果没有满足常跑航线条件，则将船主填写的常跑航线信息赋值进去
				shipSmartVo.setFrequentShipRoutes(frequentShipRoutes);
			}
		}
	}

	/**
	 * 根据经纬度处理船舶位置
	 *
	 * @param lon
	 *            经度
	 * @param lat
	 *            纬度
	 */
	private String handelLocation(Double lon, Double lat) {
		// 船舶的经度 初始化为NaN表示未设置
		double lon1 = Double.NaN;
		// 船舶的纬度
		double lat1 = Double.NaN;
		if (Objects.nonNull(lon)) {
			lon1 = lon;
		}
		if (Objects.nonNull(lat)) {
			lat1 = lat;
		}
		Response<Geocoder> response = null;
		if (!(Double.isNaN(lat1) || Double.isNaN(lon1))) {
			try {
				response = tianDiTuClient.geocode(
						new GeocodeRequest(lon, lat, 1).toString(), "geocode");

			} catch (Exception e) {
				log.error("调用天地图接口异常: {}", e.getMessage(), e);
			}
		}
		if (Objects.nonNull(response) && ("0").equals(response.getStatus())) {
			Geocoder geocoder = response.getResult();
			if (Objects.nonNull(geocoder)) {
				AddressComponent addressComponent = geocoder
						.getAddressComponent();
				if (Objects.nonNull(addressComponent)) {
					return addressComponent.getProvince()
							+ addressComponent.getCity()
							+ addressComponent.getCounty();
				}
			}
		}
		return null;
	}

	public void handle(Integer type, Integer action, String id) {
		if (Objects.isNull(type) || Objects.isNull(action)
				|| Objects.isNull(id)) {
			return;
		}
		ApplicationTransfer transfer = new ApplicationTransfer(type, action,
				id);
		mqUtil.asyncSend(MqMessage.builder()
				.topic(TopicDef.APPLICATION_CONNECTOR)
				.message(MessageBuilder
						.withPayload(JsonUtils.objectToJson(transfer)).build())
				.build());
	}

	/**
	 * 船运需求发布提醒-船运需求发布后
	 *
	 * @param plat
	 */
	private void sendNotice(ShippingRequirementPlat plat) {
		// 船运需求发布后，给货主发站内
		if (Objects.nonNull(plat.getOwnerId())) {
			usercustomerService.findOne(plat.getOwnerId())
					.ifPresent(customer -> {

						messageService.sendNotice(Messages.builder()
								.messageTypes(
										List.of(SendType.USERMESSAGE.getCode()))
								.type(UserMessageDef.MessageType.TRANSFORM
										.getCode())
								.title(MessageFormat.format(
										UserMessageConstants.SHIPPING_DEMAND_POSTED_TEMPLATE,
										(plat.getId())))
								.receiptors(List
										.of(String.valueOf(customer.getId())))
								.url(UserMessageConstants.FIND_BOAT_DETAIL_PAGE)
								.detailId(String.valueOf(plat.getId()))
								.initiator(
										UserMessageDef.BusinessInitiator.initiate
												.getCode())
								.mobile(customer.getMobile()).build());
						// 发送app推送
						messageService.sendNotice(Messages.builder()
								.messageTypes(List
										.of(SendType.PUSH_MESSAGE.getCode()))
								.type(UserMessageDef.MessageType.TRANSFORM
										.getCode())
								.title(UserMessageConstants.TRANSPORT_TITLE)
								.content(MessageFormat.format(
										UserMessageConstants.SHIPPING_DEMAND_POSTED_TEMPLATE,
										(plat.getId())))
								.appTypes(List.of(AppType.LIANYUN))
								.bizNo(String.valueOf(plat.getId()))
								.moduleType(
										UserMessageConstants.FIND_BOAT_DETAIL_PAGE)
								.receiptors(List.of(String.valueOf(
										String.valueOf(customer.getId()))))
								.build());
					});
		}
	}

	/**
	 * 货主发布找船需求后-给船务专员发送企微消息
	 */
	private void sendWxNotice(String platId) {
		super.findOne(platId).ifPresent(
				plat -> messageService.sendNotice(WxwMessage.builder()
						.receiptors(
								List.of(String.valueOf(plat.getHandlerId())))
						.url("/logistics/shippingNeed/platformShipNeedInfo/"
								.concat(String.valueOf(plat.getId())))
						.prefix("有货主在"
								+ plat.getCreatedTime()
										.format(DateTimeFormatter.ofPattern(
												"yyyy-MM-dd HH:mm:ss"))
								+ "发布了的船运需求")
						.operationModule(StringUtils.EMPTY).desc("请跟进")
						.keyword(String.valueOf(plat.getId()))
						.content(StringUtils.EMPTY).build()));

	}

	/**
	 * 组装vo
	 * 
	 * @return
	 */
	private List<ShippingRequirementCustomerVo> packCustomerVo(
			List<ShippingRequirementPlat> shippingRequirementPlatList,
			List<TransportOrderShip> transportOrderShipList,
			List<Integer> states, String keyword, String title, String shipKey,
			String sourcePortName, String destinationPortName) {
		// 货主找船需求，船运单合并后的列表
		List<ShippingRequirementCustomerVo> shippingRequirementCustomerVoList = new ArrayList<>();
		// 获取货主的客户id
		List<Long> ownerIds = shippingRequirementPlatList.stream()
				.map(ShippingRequirementPlat::getCreatedBy).distinct().toList();
		// 获取货主的客户信息
		List<Customer> customers = usercustomerService.findByIds(ownerIds);
		// 根据货主的客户id进行分组
		Map<Long, Customer> customerMap = customers.stream().collect(
				Collectors.toMap(Customer::getId, Function.identity()));
		for (ShippingRequirementPlat shippingRequirementPlat : shippingRequirementPlatList) {
			ShippingRequirementCustomerVo shippingRequirementCustomerVo = this
					.setCustomerOrderState(shippingRequirementPlat);
			shippingRequirementCustomerVo
					.setPublishTime(shippingRequirementPlat.getCreatedTime());
			shippingRequirementCustomerVo.setOwener(
					customerMap.get(shippingRequirementPlat.getCreatedBy()));
			shippingRequirementCustomerVo.setPlat(shippingRequirementPlat);
			// 编号取船运需求id或船运单id
			shippingRequirementCustomerVo
					.setTitle(shippingRequirementPlat.getId());
			shippingRequirementCustomerVo.setSourcePortName(
					shippingRequirementPlat.getSourcePortName());
			shippingRequirementCustomerVo.setDestinationPortName(
					shippingRequirementPlat.getDestinationPortName());
			shippingRequirementCustomerVo
					.setOrderShips(transportOrderShipService
							.findBySrpId(shippingRequirementPlat.getId()));
			shippingRequirementCustomerVo
					.setOrderShipVos(transportOrderShipService
							.findVoBySrpId(shippingRequirementPlat.getId()));
			shippingRequirementCustomerVoList
					.add(shippingRequirementCustomerVo);
		}
		List<String> idList = transportOrderShipList.stream()
				.map(BaseEntityWithStringId::getId).toList();
		List<OwnerShippingDeposit> ownerShippingDepositList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(idList)) {
			// 货主定金
			ownerShippingDepositList = ownerShippingDepositService
					.findByRelationCodes(idList);
		}
		Map<String, List<OwnerShippingDeposit>> ownerShippingDepositMap = ownerShippingDepositList
				.stream().collect(Collectors
						.groupingBy(OwnerShippingDeposit::getRelationCode));
		for (TransportOrderShip transportOrderShip : transportOrderShipList) {
			ShippingRequirementCustomerVo shippingRequirementCustomerVo = this
					.setTransPortOrderState(transportOrderShip);
			shippingRequirementCustomerVo
					.setSourcePortName(transportOrderShip.getSourcePortName());
			shippingRequirementCustomerVo.setDestinationPortName(
					transportOrderShip.getDestinationPortName());
			shippingRequirementCustomerVo
					.setShipName(transportOrderShip.getShipName());
			List<TransportOrderShip> orderShips = new ArrayList<>();
			orderShips.add(transportOrderShip);
			shippingRequirementCustomerVo.setOrderShips(orderShips);
			List<TransportOrderShipVo> orderShipVos = new ArrayList<>();
			TransportOrderShipVo transportOrderShipVo = new TransportOrderShipVo();
			transportOrderShipVo.setTransportOrderShip(transportOrderShip);
			transportOrderDetailsShipService.findOne(transportOrderShip.getId())
					.ifPresent(
							transportOrderShipVo::setTransportOrderDetailsShip);

			if (Objects.nonNull(transportOrderShip.getShipId())) {
				shipService.findVoById(transportOrderShip.getShipId())
						.ifPresent(transportOrderShipVo::setShipVo);
			}
			orderShipVos.add(transportOrderShipVo);
			shippingRequirementCustomerVo.setOrderShipVos(orderShipVos);
			// 货主定金
			transportOrderShipVo.setOwnerShippingDeposits(
					ownerShippingDepositMap.get(transportOrderShip.getId()));
			if (Objects.nonNull(transportOrderShip.getOwnerId())) {
				usercustomerService.findById(transportOrderShip.getOwnerId())
						.ifPresent(shippingRequirementCustomerVo::setOwener);
			}
			// 编号取船运需求id或船运单id
			shippingRequirementCustomerVo.setTitle(transportOrderShip.getId());
			// 船运单关联了船运需求的且需求未被删除的
			if (Objects.nonNull(transportOrderShip.getSrpId())) {
				ShippingRequirementPlat shipping = super.findOne(
						transportOrderShip.getSrpId()).orElse(null);
				if (Objects.nonNull(shipping)) {
					shippingRequirementCustomerVo.setPlat(shipping);
				}
			}
			// 发布时间 船主抢单并且货主找船需求没有被删除的情况取货主找船需求的创建时间 其他情况取船运单的创建时间
			if (Objects.nonNull(shippingRequirementCustomerVo.getPlat())
					&& TransportOrderShipDef.Type.SHIP_OWNER_TAKES_ORDERS
							.match(transportOrderShip.getType())) {
				shippingRequirementCustomerVo
						.setPublishTime(shippingRequirementCustomerVo.getPlat()
								.getCreatedTime());
			} else {
				shippingRequirementCustomerVo
						.setPublishTime(transportOrderShip.getCreatedTime());
			}
			shippingRequirementCustomerVoList
					.add(shippingRequirementCustomerVo);
		}

		// 组装完 shippingRequirementCustomerVoList 后，根据查询条件进行过滤
		List<ShippingRequirementCustomerVo> filteredList;
		filteredList = shippingRequirementCustomerVoList.stream().filter(vo -> {
			// 当keyword不为空时，模糊搜索船舶名称（唯一搜索MMSI号）和唯一搜索编号
			if (StringUtils.isNotBlank(keyword)) {
				List<String> shipIds = new ArrayList<>();
				if (CollectionUtils.isNotEmpty(vo.getOrderShips())) {
					shipIds = vo.getOrderShips().stream()
							.map(TransportOrderShip::getShipId).distinct()
							.toList();
				}
				if (!(vo.getTitle().equals(keyword)
						|| (StringUtils.isNotBlank(vo.getShipName())
								&& vo.getShipName().toLowerCase()
										.contains(keyword.toLowerCase()))
						|| shipIds.contains(keyword))) {
					return false;
				}
			}
			// 当 title 不为空时，进行 唯一匹配
			if (StringUtils.isNotBlank(title) && !vo.getTitle().equals(title)) {
				return false;
			}
			// 当 shipKey 不为空时，进行 LIKE 匹配，注意 shipName 可能为 null
			// 船舶名称或船舶MMSI
			if (StringUtils.isNotBlank(shipKey)) {
				List<String> shipIds = new ArrayList<>();
				if (CollectionUtils.isNotEmpty(vo.getOrderShips())) {
					shipIds = vo.getOrderShips().stream()
							.map(TransportOrderShip::getShipId).distinct()
							.toList();
				}
				if (!((StringUtils.isNotBlank(vo.getShipName())
						&& vo.getShipName().toLowerCase()
								.contains(shipKey.toLowerCase()))
						|| shipIds.contains(shipKey))) {
					return false;
				}
			}
			// 当 sourcePortName 不为空时，进行 LIKE 匹配
			if (sourcePortName != null && !sourcePortName.isEmpty()
					&& !vo.getSourcePortName().toLowerCase()
							.contains(sourcePortName.toLowerCase())) {
				return false;
			}
			// 当 destinationPortName 不为空时，进行 LIKE 匹配
			if (destinationPortName != null && !destinationPortName.isEmpty()
					&& !vo.getDestinationPortName().toLowerCase()
							.contains(destinationPortName.toLowerCase())) {
				return false;
			}
			// 当 states 不为空时，进行状态匹配
			if (CollectionUtils.isNotEmpty(states) && !states.contains(
					ShippingRequirementCustomerDef.TransportOrderState.UNCOMPLETED
							.getCode())
					&& !states.contains(vo.getTarnsPortOrderState())) {
				return false;
			}
			// 传入的状态为未完成时 则已完成的数据去掉
			return !CollectionUtils.isNotEmpty(states) || !states.contains(
					ShippingRequirementCustomerDef.TransportOrderState.UNCOMPLETED
							.getCode())
					|| !ShippingRequirementCustomerDef.TransportOrderState.COMPLETED
							.match(vo.getTarnsPortOrderState());
		})
				// 根据 tarnsPortOrderState 进行排序，空值放在最后
				.sorted(Comparator.comparing(
						ShippingRequirementCustomerVo::getTarnsPortOrderState,
						Comparator.nullsLast(Comparator.naturalOrder()))
						.thenComparing(
								ShippingRequirementCustomerVo::getPublishTime,
								Comparator
										.nullsLast(Comparator.reverseOrder())))
				.collect(Collectors.toList());

		return filteredList;
	}

	/**
	 * 处理手动分页
	 *
	 * @param page
	 * @param size
	 * @param shippingRequirementCustomerVoList
	 * @return
	 */
	private com.zhihaoscm.common.bean.page.Page<ShippingRequirementCustomerVo> handelOrderPage(
			Integer page, Integer size,
			List<ShippingRequirementCustomerVo> shippingRequirementCustomerVoList) {
		// 计算总数据量和总页数
		int total = shippingRequirementCustomerVoList.size();
		int totalPage = total == 0 ? 1 : (total + size - 1) / size;

		// 确保页码在有效范围内
		if (page < 1) {
			page = 1;
		} else if (page > totalPage) {
			page = totalPage;
		}

		// 计算需要跳过的数据量
		int skip = (page - 1) * size;

		// 如果总数据为 0，直接返回一个空的分页对象
		if (total == 0) {
			return new com.zhihaoscm.common.bean.page.Page<>(List.of(), page,
					size, total, totalPage);
		}

		// 使用 stream 流进行分页
		List<ShippingRequirementCustomerVo> pagedList = shippingRequirementCustomerVoList
				.stream().skip(skip).limit(size).toList();

		return new com.zhihaoscm.common.bean.page.Page<>(pagedList, page, size,
				total, totalPage);
	}

	/**
	 * 封装船运需求Vo
	 */
	private ShippingRequirementCustomerVo packOwnerVo(
			ShippingRequirementPlat shipping) {
		if (Objects.isNull(shipping)) {
			return null;
		}
		ShippingRequirementCustomerVo vo = new ShippingRequirementCustomerVo();
		// 设置运价指数
		Optional<ShippingPriceIndex> optional = priceIndexService
				.findNewestDataByShipRouteId(shipping.getShipRouteId());
		optional.ifPresent(vo::setPriceIndex);
		// 设置货主船运需求信息
		vo.setPlat(shipping);
		// 设置客户信息
		usercustomerService.findOne(shipping.getCreatedBy())
				.ifPresent(vo::setOwener);
		// 设置关联船运单
		vo.setOrderShips(
				transportOrderShipService.findBySrpId(shipping.getId()));
		vo.setOrderShipVos(
				transportOrderShipService.findVoBySrpId(shipping.getId()));
		return vo;
	}

	/**
	 * 货主的详情vo返回船运单数据
	 * 
	 * @param transportOrderShip
	 * @return
	 */
	private ShippingRequirementCustomerVo packCustomerVoByOrderShip(
			TransportOrderShip transportOrderShip) {
		ShippingRequirementCustomerVo shippingRequirementCustomerVo = this
				.setTransPortOrderState(transportOrderShip);
		shippingRequirementCustomerVo
				.setSourcePortName(transportOrderShip.getSourcePortName());
		shippingRequirementCustomerVo.setDestinationPortName(
				transportOrderShip.getDestinationPortName());
		shippingRequirementCustomerVo
				.setShipName(transportOrderShip.getShipName());
		List<TransportOrderShip> orderShips = new ArrayList<>();
		orderShips.add(transportOrderShip);
		shippingRequirementCustomerVo.setOrderShips(orderShips);
		List<TransportOrderShipVo> orderShipVos = new ArrayList<>();
		TransportOrderShipVo transportOrderShipVo = new TransportOrderShipVo();
		transportOrderShipVo.setTransportOrderShip(transportOrderShip);
		transportOrderDetailsShipService.findOne(transportOrderShip.getId())
				.ifPresent(transportOrderShipVo::setTransportOrderDetailsShip);
		if (Objects.nonNull(transportOrderShip.getShipId())) {
			shipService.findVoById(transportOrderShip.getShipId())
					.ifPresent(transportOrderShipVo::setShipVo);
		}
		orderShipVos.add(transportOrderShipVo);
		shippingRequirementCustomerVo.setOrderShipVos(orderShipVos);
		// 货主定金
		transportOrderShipVo
				.setOwnerShippingDeposits(ownerShippingDepositService
						.findByRelationCode(transportOrderShip.getId()));
		if (Objects.nonNull(transportOrderShip.getOwnerId())) {
			usercustomerService.findById(transportOrderShip.getOwnerId())
					.ifPresent(shippingRequirementCustomerVo::setOwener);
		}
		// 编号取船运需求id或船运单id
		shippingRequirementCustomerVo.setTitle(transportOrderShip.getId());
		// 船运单关联了船运需求的且需求未被删除的
		if (Objects.nonNull(transportOrderShip.getSrpId())) {
			ShippingRequirementPlat shipping = super.findOne(
					transportOrderShip.getSrpId()).orElse(null);
			if (Objects.nonNull(shipping)) {
				shippingRequirementCustomerVo.setPlat(shipping);
			}
		}
		// 发布时间 船主抢单并且货主找船需求没有被删除的情况取货主找船需求的创建时间 其他情况取船运单的创建时间
		if (Objects.nonNull(shippingRequirementCustomerVo.getPlat())
				&& TransportOrderShipDef.Type.SHIP_OWNER_TAKES_ORDERS
						.match(transportOrderShip.getType())) {
			shippingRequirementCustomerVo.setPublishTime(
					shippingRequirementCustomerVo.getPlat().getCreatedTime());
		} else {
			shippingRequirementCustomerVo
					.setPublishTime(transportOrderShip.getCreatedTime());
		}

		return shippingRequirementCustomerVo;
	}

	/**
	 * 船运单状态--设置货主运单状态
	 *
	 * @param transportOrderShip
	 */
	private ShippingRequirementCustomerVo setTransPortOrderState(
			TransportOrderShip transportOrderShip) {
		ShippingRequirementCustomerVo shippingRequirementCustomerVo = new ShippingRequirementCustomerVo();
		switch (TransportOrderShipDef.State
				.from(transportOrderShip.getState())) {
			// 后台管理状态 待付定金--设置货主运单状态 定金确认中
			case DEPOSIT_TO_BE_PAID -> {
				// 船运定金支付方式为服务商垫付 --设置货主运单状态 定金确认中（服务商支付）
				if (TransportOrderShipDef.DepositPayType.PAY_ZH
						.match(transportOrderShip.getPayType())) {
					shippingRequirementCustomerVo.setTarnsPortOrderState(
							ShippingRequirementCustomerDef.TransportOrderState.DEPOSIT_CONFIRMING_PAY_ZH
									.getCode());
				}
				// 船运定金支付方式为自行支付 --设置货主运单状态 定金确认中（自行支付）
				else if (TransportOrderShipDef.DepositPayType.PAY_ONESELF
						.match(transportOrderShip.getPayType())) {
					shippingRequirementCustomerVo.setTarnsPortOrderState(
							ShippingRequirementCustomerDef.TransportOrderState.DEPOSIT_CONFIRMING_PAY_ONESELF
									.getCode());
				}
			}
			// 后台管理状态 待付信息服务费
			case SHIP_INFO_FEE_TO_BE_PAID -> {
				// 待支付信息服务费 且货主未支付 --设置货主运单状态 待付信息服务费
				if (ShipInfoServiceFeeDef.State.UNPAID.match(
						transportOrderShip.getOwnerShipInfoServiceFeeState())) {
					shippingRequirementCustomerVo.setTarnsPortOrderState(
							ShippingRequirementCustomerDef.TransportOrderState.SHIP_INFO_FEE_TO_BE_PAID
									.getCode());
				}
				// 服务费确认中 状态是待支付信息服务费 且货主已支付且船主待支付或待确认--设置货主运单状态 信息服务费确认中
				if (ShipInfoServiceFeeDef.State.SUCCESS.match(
						transportOrderShip.getOwnerShipInfoServiceFeeState())
						&& List.of(ShipInfoServiceFeeDef.State.UNPAID.getCode(),
								ShipInfoServiceFeeDef.State.UN_CONFIRM
										.getCode())
								.contains(transportOrderShip
										.getCaptainShipInfoServiceFeeState())) {
					shippingRequirementCustomerVo.setTarnsPortOrderState(
							ShippingRequirementCustomerDef.TransportOrderState.SHIP_INFO_FEE_CONFIRMING
									.getCode());
				}
				// 服务费确认中 状态是待支付信息服务费 货主支付确认中--设置货主运单状态 信息服务费确认中
				if (ShipInfoServiceFeeDef.State.UN_CONFIRM.match(
						transportOrderShip.getOwnerShipInfoServiceFeeState())) {
					shippingRequirementCustomerVo.setTarnsPortOrderState(
							ShippingRequirementCustomerDef.TransportOrderState.SHIP_INFO_FEE_CONFIRMING
									.getCode());
				}
			}
			// 后台管理状态 待装货--设置货主运单状态 装货中
			case DURING_LOADING ->
				shippingRequirementCustomerVo.setTarnsPortOrderState(
						ShippingRequirementCustomerDef.TransportOrderState.DURING_LOADING
								.getCode());

			// 后台管理状态 装货中--设置货主运单状态 装货中
			case TO_BE_LOADED ->
				shippingRequirementCustomerVo.setTarnsPortOrderState(
						ShippingRequirementCustomerDef.TransportOrderState.DURING_LOADING
								.getCode());

			// 后台管理状态 待发航--设置货主运单状态 待发航
			case AWAITING_DEPARTURE ->
				shippingRequirementCustomerVo.setTarnsPortOrderState(
						ShippingRequirementCustomerDef.TransportOrderState.AWAITING_DEPARTURE
								.getCode());

			// 后台管理状态 运输中--设置货主运单状态 运输中
			case DURING_TRANSPORTATION ->
				shippingRequirementCustomerVo.setTarnsPortOrderState(
						ShippingRequirementCustomerDef.TransportOrderState.DURING_TRANSPORTATION
								.getCode());

			// 后台管理状态 待卸货--设置货主运单状态 待卸货
			case TO_BE_UNLOADED ->
				shippingRequirementCustomerVo.setTarnsPortOrderState(
						ShippingRequirementCustomerDef.TransportOrderState.TO_BE_UNLOADED
								.getCode());

			// 后台管理状态 卸货中--设置货主运单状态 卸货中
			case DURING_UNLOADING ->
				shippingRequirementCustomerVo.setTarnsPortOrderState(
						ShippingRequirementCustomerDef.TransportOrderState.DURING_UNLOADING
								.getCode());

			// 后台管理状态 已清仓--设置货主运单状态 已完成
			case DISCHARGED ->
				shippingRequirementCustomerVo.setTarnsPortOrderState(
						ShippingRequirementCustomerDef.TransportOrderState.COMPLETED
								.getCode());

			// 后台管理状态 已完成--设置货主运单状态 已完成
			case COMPLETED ->
				shippingRequirementCustomerVo.setTarnsPortOrderState(
						ShippingRequirementCustomerDef.TransportOrderState.COMPLETED
								.getCode());

		}
		return shippingRequirementCustomerVo;
	}

	/**
	 * 货主找船需求状态--设置货主运单状态
	 *
	 * @param shippingRequirementPlat
	 */
	private ShippingRequirementCustomerVo setCustomerOrderState(
			ShippingRequirementPlat shippingRequirementPlat) {
		ShippingRequirementCustomerVo shippingRequirementCustomerVo = new ShippingRequirementCustomerVo();
		// 货主的找船中的船运需求 就是管理后台已发布的船运需求
		if (ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
				.match(shippingRequirementPlat.getState())) {
			shippingRequirementCustomerVo.setTarnsPortOrderState(
					ShippingRequirementCustomerDef.TransportOrderState.FIND_THE_SHIP
							.getCode());
		} else if (ShippingRequirementPlatDef.State.TO_BE_PUBLISH
				.match(shippingRequirementPlat.getState())) {
			// 货主的处理中的船运需求 就是管理后台待发布的船运需求
			shippingRequirementCustomerVo.setTarnsPortOrderState(
					ShippingRequirementCustomerDef.TransportOrderState.PENDING_PROCESSING
							.getCode());
		}
		return shippingRequirementCustomerVo;
	}

	/**
	 * 将船运需求自动平均分配给指定的船务专员
	 *
	 * @param shippingRequirementPlat
	 */
	private void assignHandlerToRequirement(
			ShippingRequirementPlat shippingRequirementPlat) {
		List<Long> handlerIds = shippingHandlerProperties.getHandlerIds();
		if (CollectionUtils.isEmpty(handlerIds)) {
			return;
		}
		// 查询当前待分配的需求
		List<ShippingRequirementPlat> platList = this.findByHandlerIdsAndState(
				handlerIds,
				List.of(ShippingRequirementPlatDef.State.TO_BE_ALLOCATED
						.getCode()));

		// 统计任务数量（可为空）
		Map<Long, Long> handlerCountMap = Optional.ofNullable(platList)
				.orElse(List.of()).stream()
				.filter(p -> p.getHandlerId() != null
						&& handlerIds.contains(p.getHandlerId()))
				.collect(Collectors.groupingBy(
						ShippingRequirementPlat::getHandlerId,
						Collectors.counting()));

		// 保证所有专员都在 Map 中（即使是 0）
		for (Long handlerId : handlerIds) {
			handlerCountMap.putIfAbsent(handlerId, 0L);
		}

		// 选出任务最少的专员，设置给需求
		handlerCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue())
				.map(Map.Entry::getKey).findFirst()
				.flatMap(userService::findOne).ifPresent(user -> {
					shippingRequirementPlat.setHandlerId(user.getId());
					shippingRequirementPlat.setHandlerName(user.getName());
					shippingRequirementPlat
							.setElectricContactNumber(user.getMobile());
					shippingRequirementPlat.setElectricContactNumberType(
							ShippingRequirementPlatDef.ElectricContactNumberType.PHONE
									.getCode());
					// 插入记录到指派表
					Assign assign = new Assign();
					assign.setCorrelationId(
							Long.valueOf(shippingRequirementPlat.getId()));
					assign.setUserId(shippingRequirementPlat.getHandlerId());
					assign.setType(AssignDef.Type.REQUIREMENT_PLAT.getCode());
					assignService.create(assign);
				});
	}

	/**
	 * 判断接单信息的银行账户开户人和开户名称是否承运商实名
	 */
	private boolean checkBankName(ShippingRequirementAccept accept) {
		CustomerBankInfo captainBankInfo = accept.getCaptainBankInfo();
		if (Objects.nonNull(captainBankInfo)) {
			Long captainId = accept.getCaptainId();
			Customer customer = usercustomerService.findOne(captainId)
					.orElse(null);
			if (Objects.nonNull(customer)) {
				return captainBankInfo.getName().equals(customer.getRealName());
			} else {
				return false;
			}
		} else {
			return false;
		}
	}
}
