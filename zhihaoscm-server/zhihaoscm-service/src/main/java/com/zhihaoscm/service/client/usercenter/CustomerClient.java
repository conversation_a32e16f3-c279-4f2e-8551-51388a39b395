package com.zhihaoscm.service.client.usercenter;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.MembershipLevel;
import com.zhihaoscm.domain.bean.json.Enterprise;
import com.zhihaoscm.domain.bean.vo.CustomerLoginVo;
import com.zhihaoscm.domain.bean.vo.CustomerVo;

@FeignClient(name = "user-center", path = "/customer-rest", url = "${application.config.user-center}")
public interface CustomerClient extends MpLongIdBaseClient<Customer> {

	@GetMapping("/count/app-type")
	Long countByAppType(@RequestParam(required = false) Integer role,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime);

	@GetMapping("/find/all")
	List<Customer> findAll(
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime);

	@GetMapping("/count/by/level")
	Long countByMemberLevel(@RequestParam Integer memberLevel,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime);

	@PostMapping("/get/membership/level")
	MembershipLevel getMembershipLevel(@RequestBody Customer customer);

	@GetMapping("/find/mobile")
	Customer findByMobile(@RequestParam String mobile);

	@GetMapping("/find/vo/{id}")
	CustomerVo findVoById(@PathVariable Long id);

	@GetMapping("/selector")
	List<Customer> selector(@RequestParam(required = false) String searchParam,
			@RequestParam(required = false) Integer personalAuth,
			@RequestParam(required = false) Integer enterpriseAuth);

	@GetMapping("/find/enterprise")
	Enterprise findEnterpriseByCustomerId(@RequestParam Long customerId);

	@PostMapping({ "/batch/update" })
	void batchUpdate(@RequestBody List<Customer> datas);

	@Override
	@GetMapping("/find/customer/all")
	List<Customer> findAll();

	@GetMapping("/find/code")
	Customer findByCode(@RequestParam String code);

	@GetMapping("/supplier/selector")
	List<Customer> supplierSelector(
			@RequestParam(required = false) String name);

	@RequestMapping("/parse/token")
	@ResponseBody
	LoginUser<CustomerLoginVo> parseToken(@RequestParam String token);
}
