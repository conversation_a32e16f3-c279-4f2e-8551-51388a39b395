package com.zhihaoscm.service.resource.form.sample.apply;

import org.hibernate.validator.constraints.Length;

import com.zhihaoscm.domain.bean.entity.SampleApply;
import com.zhihaoscm.domain.meta.biz.SampleApplyDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SampleApplyForm {

	@Schema(title = "商品ID")
	@NotBlank(message = ErrorCode.CODE_30197002)
	private String productId;

	@Schema(title = "地址ID")
	@NotNull(message = ErrorCode.CODE_30197003)
	private Long addressId;

	@Schema(title = "备注")
	@Length(max = 300, message = ErrorCode.CODE_30197004)
	private String remark;

	public SampleApply convertToEntity() {
		SampleApply sampleApply = new SampleApply();
		sampleApply.setCustomerId(CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId());
		sampleApply.setAddressId(this.addressId);
		sampleApply.setRemark(this.remark);
		sampleApply.setProductId(this.productId);
		sampleApply.setState(SampleApplyDef.State.PENDING.getCode());
		return sampleApply;
	}
}
