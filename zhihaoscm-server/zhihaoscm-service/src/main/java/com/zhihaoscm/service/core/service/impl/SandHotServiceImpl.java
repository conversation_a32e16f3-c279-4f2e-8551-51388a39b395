package com.zhihaoscm.service.core.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.bean.entity.ReadQuantity;
import com.zhihaoscm.domain.bean.entity.SandHot;
import com.zhihaoscm.domain.bean.entity.TagBusiness;
import com.zhihaoscm.domain.bean.vo.SandHotVo;
import com.zhihaoscm.domain.meta.biz.ReadQuantityDef;
import com.zhihaoscm.domain.meta.biz.SandHotDef;
import com.zhihaoscm.domain.meta.biz.TagBusinessDef;
import com.zhihaoscm.domain.utils.PaginationUtils;
import com.zhihaoscm.service.core.mapper.SandHotMapper;
import com.zhihaoscm.service.core.service.*;

/**
 * <p>
 * 砂石热点 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
public class SandHotServiceImpl
		extends MpLongIdBaseServiceImpl<SandHot, SandHotMapper>
		implements SandHotService {

	public SandHotServiceImpl(SandHotMapper repository) {
		super(repository);
	}

	@Autowired
	private FileService fileService;
	@Autowired
	private TagBusinessService tagBusinessService;
	@Autowired
	private TagService tagService;
	@Autowired
	private ReadQuantityService readQuantityService;

	@Override
	public com.zhihaoscm.common.bean.page.Page<SandHotVo> paging(Integer page,
			Integer size, String title, List<Integer> type, Integer state,
			String sortKey, String sortOrder) {
		LambdaQueryWrapper<SandHot> queryWrapper = Wrappers
				.lambdaQuery(SandHot.class);
		this.filterDeleted(queryWrapper);
		queryWrapper
				.like(StringUtils.isNotBlank(title), SandHot::getTitle, title)
				.in(CollectionUtils.isNotEmpty(type), SandHot::getType, type)
				.eq(Objects.nonNull(state), SandHot::getState, state);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 列表以状态和创建时间排序，上架中1、已下架0，相同状态按创建时间倒序
			queryWrapper.orderByDesc(SandHot::getState)
					.orderByDesc(SandHot::getCreatedTime);
		}
		List<SandHot> sandHotList = repository.selectList(queryWrapper);
		List<SandHotVo> vos = this.packVo(sandHotList);
		return PaginationUtils.handelPage(page, size, vos);
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<SandHotVo> customPaging(
			Integer page, Integer size, String title, Integer type) {
		LambdaQueryWrapper<SandHot> queryWrapper = Wrappers
				.lambdaQuery(SandHot.class);
		this.filterDeleted(queryWrapper);
		queryWrapper
				.like(StringUtils.isNotBlank(title), SandHot::getTitle, title)
				.eq(Objects.nonNull(type), SandHot::getType, type);
		// 已启用的
		queryWrapper.eq(SandHot::getState, CommonDef.Symbol.YES.getCode());
		// 列表按创建时间倒序
		queryWrapper.orderByDesc(SandHot::getCreatedTime);

		List<SandHot> sandHotList = repository.selectList(queryWrapper);
		List<SandHotVo> vos = this.packVo(sandHotList);
		return PaginationUtils.handelPage(page, size, vos);
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<SandHotVo> relatedPaging(
			Integer page, Integer size, Long sandHotId) {
		// 相关砂石热点id
		List<Long> relatedIds = this.getRelatedIds(sandHotId);
		if (CollectionUtils.isEmpty(relatedIds)) {
			return new Page<>();
		}

		LambdaQueryWrapper<SandHot> queryWrapper = Wrappers
				.lambdaQuery(SandHot.class);
		this.filterDeleted(queryWrapper);
		queryWrapper
				.in(CollectionUtils.isNotEmpty(relatedIds), SandHot::getId,
						relatedIds)
				.ne(Objects.nonNull(sandHotId), SandHot::getId, sandHotId)
				.eq(SandHot::getState, CommonDef.Symbol.YES.getCode());
		// 列表按发布日期倒序
		queryWrapper.orderByDesc(SandHot::getPublishDate);

		List<SandHot> sandHotList = repository.selectList(queryWrapper);
		List<SandHotVo> vos = this.packVo(sandHotList);
		return PaginationUtils.handelPage(page, size, vos);
	}

	@Override
	public Optional<SandHotVo> findVoById(Long id) {
		return super.findOne(id).map(this::packVo);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@FileId
	public SandHot create(SandHot sandHot, @FileId List<Long> activeFileIds,
			List<Long> tagIds) {
		SandHot result = super.create(sandHot);
		this.handelTag(result, tagIds);
		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@FileId(type = 2)
	public SandHot update(SandHot sandHot, @FileId List<Long> activeFileIds,
			@FileId(type = 2) List<Long> unActiveFileIds, List<Long> tagIds) {
		SandHot result = super.updateAllProperties(sandHot);
		// 先删除标签，再进行重新新增
		tagBusinessService.deleteByBusinessIdAndType(sandHot.getId().toString(),
				TagBusinessDef.Type.SAND_HOT.getCode());
		this.handelTag(result, tagIds);
		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	@FileId(type = 3)
	public void delete(Long id, @FileId(type = 2) List<Long> unActiveFileIds) {
		super.delete(id);
		tagBusinessService.deleteByBusinessIdAndType(String.valueOf(id),
				TagBusinessDef.Type.SAND_HOT.getCode());
	}

	@Override
	public void updateState(Long id, Integer state) {
		LambdaUpdateWrapper<SandHot> updateWrapper = Wrappers
				.lambdaUpdate(SandHot.class).eq(SandHot::getId, id)
				.set(SandHot::getState, state);
		repository.update(updateWrapper);
	}

	/**
	 * 获取相关砂石热点id集合
	 * 
	 * @return
	 */
	public List<Long> getRelatedIds(Long sandHotId) {
		// 查询出当前砂石热点的标签
		List<TagBusiness> tagBusinessList = tagBusinessService
				.findByBusinessIdAndType(String.valueOf(sandHotId),
						TagBusinessDef.Type.SAND_HOT.getCode());
		if (CollectionUtils.isEmpty(tagBusinessList)) {
			return List.of();
		}
		// 过滤出标签id
		List<Long> tagIdList = tagBusinessList.stream()
				.map(TagBusiness::getTagId).distinct().toList();
		if (CollectionUtils.isEmpty(tagIdList)) {
			return List.of();
		}
		// 通过品类id查询出品类id集合
		List<TagBusiness> businessList = tagBusinessService.findByTagIdsAndType(
				tagIdList, TagBusinessDef.Type.SAND_HOT.getCode());
		if (CollectionUtils.isEmpty(businessList)) {
			return List.of();
		}
		return businessList.stream()
				.map(business -> Long.parseLong(business.getBusinessId()))
				.distinct().toList();
	}

	/**
	 * 封装vo
	 *
	 * @param sandHot
	 * @return
	 */
	private SandHotVo packVo(SandHot sandHot) {
		SandHotVo vo = new SandHotVo();
		vo.setSandHot(sandHot);

		// 分类是近期展会，且文件id不为空
		if (Objects.nonNull(sandHot.getFileId())
				&& SandHotDef.Type.RECENT_EXHIBITION.match(sandHot.getType())) {
			// 设置封面
			vo.setCover(fileService.findOne(sandHot.getFileId()).orElse(null));
		}
		// 设置阅读量
		List<ReadQuantity> readQuantities = readQuantityService
				.findByUniIdsAndType(Set.of(sandHot.getId()),
						ReadQuantityDef.Type.SAND_HOT.getCode());
		if (!CollectionUtils.isEmpty(readQuantities)) {
			vo.setReadQuantity(readQuantities.get(0));
		} else {
			ReadQuantity readQuantity = new ReadQuantity();
			readQuantity.setQuantity(0L);
			vo.setReadQuantity(readQuantity);
		}

		// 设置标签
		return this.handelTag(sandHot, vo);
	}

	/**
	 * 批量封装vo
	 *
	 * @param sandHotList
	 * @return
	 */
	private List<SandHotVo> packVo(List<SandHot> sandHotList) {
		if (CollectionUtils.isEmpty(sandHotList)) {
			return List.of();
		}

		// 文件id集合
		Set<Long> fileIds = new HashSet<>();
		// 砂石热点id集合
		Set<Long> ids = new HashSet<>();
		sandHotList.forEach(sandHot -> {
			fileIds.add(sandHot.getFileId());
			ids.add(sandHot.getId());
		});

		Map<Long, File> fileMap = fileService
				.getIdMap(new ArrayList<>(fileIds));
		Map<Long, ReadQuantity> readQuantityMap = readQuantityService
				.findByUniIdsAndType(ids,
						ReadQuantityDef.Type.SAND_HOT.getCode())
				.stream().collect(Collectors.toMap(ReadQuantity::getUniId,
						Function.identity()));

		return sandHotList.stream().map(sandHot -> {
			SandHotVo vo = new SandHotVo();
			vo.setSandHot(sandHot);

			// 设置封面
			vo.setCover(fileMap.get(sandHot.getFileId()));
			// 设置阅读量
			ReadQuantity readQuantity = readQuantityMap.get(sandHot.getId());
			if (Objects.isNull(readQuantity)) {
				readQuantity = new ReadQuantity();
				readQuantity.setQuantity(0L);
			}
			vo.setReadQuantity(readQuantity);
			// 设置标签
			return this.handelTag(sandHot, vo);
		}).toList();
	}

	/**
	 * 处理vo标签
	 *
	 * @param sandHot
	 * @param vo
	 * @return
	 */
	private SandHotVo handelTag(SandHot sandHot, SandHotVo vo) {
		List<TagBusiness> tagBusinessList = tagBusinessService
				.findByBusinessIdAndType(sandHot.getId().toString(),
						TagBusinessDef.Type.SAND_HOT.getCode());
		List<Long> tagIdList = tagBusinessList.stream()
				.map(TagBusiness::getTagId).distinct().toList();
		if (CollectionUtils.isNotEmpty(tagIdList)) {
			vo.setTags(tagService.findByIds(tagIdList));
		}
		return vo;
	}

	/**
	 * 处理标签
	 *
	 * @param sandHot
	 * @param tagIds
	 */
	private void handelTag(SandHot sandHot, List<Long> tagIds) {
		if (CollectionUtils.isNotEmpty(tagIds)) {
			for (Long tagId : tagIds) {
				TagBusiness tagBusiness = new TagBusiness();
				tagBusiness.setBusinessId(sandHot.getId().toString());
				tagBusiness.setTagId(tagId);
				tagBusiness.setType(TagBusinessDef.Type.SAND_HOT.getCode());
				tagBusiness.setName(sandHot.getTitle());
				tagBusinessService.create(tagBusiness);
			}
		}
	}
}
