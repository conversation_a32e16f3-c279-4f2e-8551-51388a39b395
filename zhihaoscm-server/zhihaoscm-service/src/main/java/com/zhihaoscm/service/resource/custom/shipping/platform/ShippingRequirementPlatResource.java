package com.zhihaoscm.service.resource.custom.shipping.platform;

import java.time.LocalDate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.domain.bean.vo.ShippingRequirementPlatVo;
import com.zhihaoscm.service.core.service.ShippingRequirementPlatService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "平台船运需求记录", description = "平台船运需求记录API")
@RestController
@RequestMapping(value = "/shipping/plat")
public class ShippingRequirementPlatResource {

	@Autowired
	private ShippingRequirementPlatService service;

	@Operation(summary = "查询平台船运需求列表")
	@GetMapping(value = "custom/paging")
	public ApiResponse<Page<ShippingRequirementPlatVo>> customPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_MINI_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "船运需求id") @RequestParam(required = false, name = "keyword") String keyword,
			@Parameter(description = "始发港id") @RequestParam(required = false, name = "sourcePortId") Long sourcePortId,
			@Parameter(description = "目的港id") @RequestParam(required = false, name = "destinationPortId") Long destinationPortId,
			@Parameter(description = "装载日期起") @RequestParam(required = false, name = "LocalDateStart") LocalDate localDateStart,
			@Parameter(description = "装载日期止") @RequestParam(required = false, name = "LocalDateEnd") LocalDate localDateEnd) {
		return new ApiResponse<>(service.customPaging(page, size, keyword,
				sourcePortId, destinationPortId, localDateStart, localDateEnd));
	}

	@Operation(summary = "查询平台船运需求详情")
	@GetMapping(value = "/find/vo/{id}")
	public ApiResponse<ShippingRequirementPlatVo> findVoById(
			@PathVariable(value = "id") String id) {
		return new ApiResponse<>(service.findVoById(id).orElse(null));
	}

}
