package com.zhihaoscm.service.core.service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpLongIdBaseService;
import com.zhihaoscm.domain.bean.entity.Information;
import com.zhihaoscm.domain.bean.vo.InformationCountVo;
import com.zhihaoscm.domain.bean.vo.InformationVo;

/**
 * <p>
 * 砂石资讯 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
public interface InformationService extends MpLongIdBaseService<Information> {

	/**
	 * 分页查询
	 *
	 * @param page
	 * @param size
	 * @param title
	 * @param startTime
	 * @param endTime
	 * @param sortKey
	 * @param sortOrder
	 * @param classify
	 * @param feeType
	 * @param showLocation
	 * @param state
	 * @return
	 */
	Page<InformationVo> paging(Integer page, Integer size, String title,
			LocalDate startTime, LocalDate endTime, String sortKey,
			String sortOrder, Integer classify, Integer feeType,
			Integer showLocation, Integer pcShowLocation, Integer state);

	/**
	 * 相关资讯
	 *
	 * @param page
	 * @param size
	 * @param productTypeId
	 * @return
	 */
	Page<InformationVo> relatedPaging(Integer page, Integer size,
			String productTypeId);

	/**
	 * 资讯下拉框
	 *
	 * @param title
	 * @return
	 */
	List<Information> selector(String title);

	/**
	 * 小程序端分页查询
	 *
	 * @param page
	 * @param size
	 * @param keyword
	 * @param tenantId
	 * @param state
	 * @param publishDateStart
	 * @param publishDateFinish
	 * @param category
	 * @return
	 */
	Page<InformationVo> customPaging(Integer page, Integer size, String keyword,
			String tenantId, Integer state, String publishDateStart,
			String publishDateFinish, Integer category, List<Integer> classify,
			Integer showLocation, String sortKey, String sortOrder);

	/**
	 * 资讯详情里的相关资讯列表
	 * 
	 * @param informationId
	 * @return
	 */
	List<InformationVo> detailRelated(Long informationId);

	/**
	 * 根据展示位置查询出最新的一条数据
	 * 
	 * @param showLocations
	 * @param classify
	 * @return
	 */
	List<InformationVo> findByShowLocation(List<Integer> showLocations,
			List<Integer> classify);

	/**
	 * 根据PC端展示位置查询
	 */
	List<InformationVo> findByPcShowLocation();

	/**
	 * 根据分类查询最新数据
	 */
	List<InformationVo> findByClassify(Integer classify);

	/**
	 * 查找单个砂石资讯数据
	 *
	 * @param id
	 * @param tenantId
	 * @return
	 */
	Optional<InformationVo> detail(Long id, String tenantId);

	/**
	 * 砂石资讯统计-未上架
	 * 
	 * @param hasFull
	 * @return
	 */
	InformationCountVo statisticsInformationUnlisted(Boolean hasFull);

	/**
	 * 自定义新增 增加批量激活文件的逻辑
	 *
	 * @param information
	 * @param activeFileIds
	 */
	Information create(Information information, List<Long> activeFileIds,
			List<Long> tagIds);

	/**
	 * 自定义修改 增加批量激活和批量取消激活文件的逻辑
	 *
	 * @param information
	 * @param activeFileIds
	 * @param unActiveFileIds
	 */
	void update(Information information, List<Long> activeFileIds,
			List<Long> unActiveFileIds, List<Long> tagIds);

	/**
	 * 自定义删除 批量取消激活文件
	 *
	 * @param id
	 * @param unActiveFileIds
	 */
	void delete(Long id, List<Long> unActiveFileIds);

	/**
	 * 更新状态
	 *
	 * @param id
	 * @param state
	 */
	void updateState(Long id, Integer state);

	/**
	 * 判断当日是否存在每日评述的资讯，存在就不调用工作流生成资讯
	 *
	 * @return
	 */
	boolean validateDailyReviewExist();

	/**
	 * 生成水位和航道报告的资讯
	 */
	void generateWaterLevelAndReport();

}
