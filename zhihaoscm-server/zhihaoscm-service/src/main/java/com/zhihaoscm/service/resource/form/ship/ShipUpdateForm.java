package com.zhihaoscm.service.resource.form.ship;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.json.CertificateFiles;
import com.zhihaoscm.domain.bean.json.IdCardInfo;
import com.zhihaoscm.domain.meta.biz.ShipDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ShipUpdateForm {

	@Schema(description = "船类型")
	@Range(min = 1, max = 20, message = ErrorCode.CODE_30120052)
	private Integer type;

	@Schema(description = "散货船类型")
	@Range(min = 1, max = 4, message = ErrorCode.CODE_30120051)
	private Integer bulkCargoShipType;

	@Schema(description = "船长")
	private BigDecimal length;

	@Schema(description = "船宽")
	private BigDecimal width;

	@Schema(description = "型深")
	private BigDecimal deep;

	@Schema(description = "船舶负责人")
	@Length(max = 10)
	private String captain;

	@Schema(description = "船舶负责人联系方式")
	private String mobile;

	@Schema(description = "总吨数")
	private Long tonTotal;

	@Schema(description = "船舶中文名称")
	private String cnname;

	@Schema(description = "船舶名称")
	private String name;

	@Schema(description = "承运方客户id")
	private Long carrier;

	@Schema(description = "载重吨数")
	private Long tonCapacity;

	@Schema(description = "净吨数")
	private Long tonPure;

	@Schema(description = "营运证截止时间")
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	private LocalDateTime operatingLicenseExpirationDate;

	@Schema(description = "船舶经营人名称")
	@Length(max = 32, message = ErrorCode.CODE_30120057)
	private String shipOperatorName;

	@Schema(description = "国籍截止时间")
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	private LocalDateTime nationalityExpirationDate;

	@Schema(description = "船检证截止时间")
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	private LocalDateTime shipInspectionExpirationDate;

	@Schema(description = "建造日期")
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	private LocalDateTime constructionDate;

	@Schema(description = "船籍港-城市编码")
	private String homePortCityCode;

	@Schema(description = "船舶营运证号")
	@Length(max = 32, message = ErrorCode.CODE_30120058)
	private String operatingLicenseNumber;

	@Schema(description = "资料完善进度")
	private Integer dataProgress;

	@Schema(description = "船舶照片文件")
	private ArrayLong shipPhotoFiles;

	@Schema(description = "船舶视频文件")
	private ArrayLong shipVideoFiles;

	@Schema(description = "船只证件文件")
	private CertificateFiles certificateFiles;

	@Schema(description = "备注")
	@Length(max = 200, message = ErrorCode.CODE_30120053)
	private String remark;

	@Schema(description = "联系方式维护人id")
	private Long contactMaintainerId;

	@Schema(description = "联系方式维护人")
	private String contactMaintainer;

	@Schema(description = "职务资格")
	private String jobQualification;

	@Schema(description = "船舶身份证图片识别的字段")
	private IdCardInfo idCardInfo;

	public Ship convertToEntity(Ship ship) {
		ship.setType(this.type);
		if (Objects.nonNull(this.type)
				&& ShipDef.Type.CARGO_SHIPS.match(this.type)) {
			ship.setBulkCargoShipType(this.bulkCargoShipType);
		}
		ship.setLength(this.length);
		ship.setWidth(this.width);
		ship.setDeep(this.deep);
		ship.setCaptain(this.captain);
		ship.setMobile(this.mobile);
		ship.setTonTotal(this.tonTotal);
		ship.setTonCapacity(this.tonCapacity);
		ship.setTonPure(this.tonPure);
		ship.setOperatingLicenseExpirationDate(
				this.operatingLicenseExpirationDate);
		ship.setShipOperatorName(this.shipOperatorName);
		ship.setNationalityExpirationDate(this.nationalityExpirationDate);
		ship.setShipInspectionExpirationDate(this.shipInspectionExpirationDate);
		ship.setConstructionDate(this.constructionDate);
		ship.setHomePortCityCode(this.homePortCityCode);
		ship.setOperatingLicenseNumber(this.operatingLicenseNumber);
		ship.setCarrier(this.carrier);
		if (Objects.nonNull(this.carrier)) {
			ship.setState(ShipDef.State.AUTHENTICATED.getCode());
			ship.setAuthenticationTime(LocalDateTime.now());
		}
		ship.setCnname(this.cnname);
		ship.setName(this.name);
		ship.setDataProgress(this.dataProgress);
		ship.setShipPhotoFiles(this.shipPhotoFiles);
		ship.setShipVideoFiles(this.shipVideoFiles);
		ship.setCertificateFiles(this.certificateFiles);
		ship.setRemark(this.remark);
		ship.setContactMaintainerId(this.contactMaintainerId);
		ship.setContactMaintainer(this.contactMaintainer);
		if (StringUtils.isBlank(ship.getMobile())) {
			ship.setExistMobile(CommonDef.Symbol.NO.getCode());
		} else {
			ship.setExistMobile(CommonDef.Symbol.YES.getCode());
		}
		ship.setJobQualification(this.jobQualification);
		ship.setIdCardInfo(this.idCardInfo);
		return ship;
	}
}
