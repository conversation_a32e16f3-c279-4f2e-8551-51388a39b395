package com.zhihaoscm.service.resource.validator.oil.index.snapshot;

import java.time.LocalDateTime;
import java.util.*;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.OilIndexSnapshot;
import com.zhihaoscm.domain.bean.entity.OilIndexVersion;
import com.zhihaoscm.domain.bean.entity.OilIndexVersionRecord;
import com.zhihaoscm.domain.bean.entity.OilSite;
import com.zhihaoscm.domain.bean.vo.UploadError;
import com.zhihaoscm.domain.meta.biz.IndexVersionRecordDef;
import com.zhihaoscm.domain.meta.biz.ProductTypeIndexDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.core.service.OilIndexSnapshotService;
import com.zhihaoscm.service.core.service.OilSiteService;
import com.zhihaoscm.service.resource.form.oil.index.snapshot.OilIndexSnapshotForm;
import com.zhihaoscm.service.resource.form.oil.index.snapshot.OilIndexSnapshotUploadForm;
import com.zhihaoscm.service.resource.validator.oil.index.version.OilIndexVersionValidator;
import com.zhihaoscm.service.resource.validator.oil.index.version.record.OilIndexVersionRecordValidator;
import com.zhihaoscm.service.resource.validator.oil.site.OilSiteValidator;

import lombok.Data;

/**
 * 油品指数快照校验器
 */
@Component
public class OilIndexSnapshotValidator {

	@Autowired
	private OilIndexSnapshotService service;

	@Autowired
	private OilIndexVersionValidator oilIndexVersionValidator;

	@Autowired
	private OilIndexVersionRecordValidator oilIndexVersionRecordValidator;

	@Autowired
	private OilSiteValidator oilSiteValidator;

	@Autowired
	private OilSiteService oilSiteService;

	/**
	 * 校验是否存在
	 * 
	 * @param id
	 * @return
	 */
	public OilIndexSnapshot validateExist(Long id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30163007));
	}

	/**
	 * 校验权限
	 *
	 * @param record
	 */
	public void validateAuth(OilIndexVersionRecord record) {
		if (!(IndexVersionRecordDef.State.TO_BE_SUBMIT.match(record.getState())
				|| IndexVersionRecordDef.State.REJECTED
						.match(record.getState()))) {
			throw new BadRequestException(ErrorCode.CODE_30162003);
		}
		if (!Objects.equals(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				record.getEditBy())) {
			throw new BadRequestException(ErrorCode.CODE_403);
		}
	}

	/**
	 * 校验新增
	 * 
	 * @param form
	 * @return
	 */
	public OilIndexSnapshot validateCreate(OilIndexSnapshotForm form) {
		this.validatePrice(form);
		// 校验版本
		OilIndexVersion oilIndexVersion = oilIndexVersionValidator
				.validateExist(form.getVersionId());
		// 校验指数版本记录
		OilIndexVersionRecord oilIndexVersionRecord = oilIndexVersionRecordValidator
				.validateExist(form.getVersionRecordId());
		this.validateAuth(oilIndexVersionRecord);
		oilSiteValidator.validateExist(form.getOilSiteId());

		List<OilIndexSnapshot> priceIndexSnapshotList = service
				.findByVersionDateAndOilSiteId(oilIndexVersion.getVersionDate(),
						form.getOilSiteId(), form.getVersionRecordId());
		if (CollectionUtils.isNotEmpty(priceIndexSnapshotList)) {
			throw new BadRequestException(ErrorCode.CODE_30163006);
		}
		OilIndexSnapshot oilIndexSnapshot = form.convertToEntity();
		oilIndexSnapshot.setVersionDate(oilIndexVersion.getVersionDate());
		return oilIndexSnapshot;
	}

	/**
	 * 校验更新
	 * 
	 * @param id
	 * @param updateForm
	 * @return
	 */
	public OilIndexSnapshot validateUpdate(Long id,
			OilIndexSnapshotForm updateForm) {
		OilIndexSnapshot oilIndexSnapshot = this.validateExist(id);
		this.validatePrice(updateForm);
		OilIndexVersionRecord oilIndexVersionRecord = oilIndexVersionRecordValidator
				.validateExist(oilIndexSnapshot.getVersionRecordId());
		this.validateAuth(oilIndexVersionRecord);
		List<OilIndexSnapshot> priceIndexSnapshotList = service
				.findByVersionDateAndOilSiteId(
						oilIndexSnapshot.getVersionDate(),
						updateForm.getOilSiteId(),
						oilIndexSnapshot.getVersionRecordId());
		if (CollectionUtils
				.isNotEmpty(priceIndexSnapshotList.stream()
						.filter(item -> !item.getOilSiteId()
								.equals(oilIndexSnapshot.getOilSiteId()))
						.toList())) {
			throw new BadRequestException(ErrorCode.CODE_30163006);
		}
		oilSiteValidator.validateExist(updateForm.getOilSiteId());
		return oilIndexSnapshot;
	}

	/**
	 * 校验删除
	 * 
	 * @param id
	 */
	public void validateDelete(Long id) {
		OilIndexSnapshot oilIndexSnapshot = this.validateExist(id);
		OilIndexVersionRecord oilIndexVersionRecord = oilIndexVersionRecordValidator
				.validateExist(oilIndexSnapshot.getVersionRecordId());
		this.validateAuth(oilIndexVersionRecord);
	}

	/**
	 * 校验批量删除
	 * 
	 * @param versionRecordId
	 */
	public void validateBatchDelete(Long versionRecordId) {
		OilIndexVersionRecord oilIndexVersionRecord = oilIndexVersionRecordValidator
				.validateExist(versionRecordId);
		this.validateAuth(oilIndexVersionRecord);
	}

	/**
	 * 校验价格
	 * 
	 * @param form
	 */
	public void validatePrice(OilIndexSnapshotForm form) {
		if (Objects.isNull(form.getZeroDieselActualSellingPrice())
				&& Objects.isNull(form.getZeroDieselListingPrice())
				&& Objects.isNull(form.getLightFuelListingPrice())
				&& Objects.isNull(form.getLightFuelActualSellingPrice())) {
			// 不能全部为空
			throw new BadRequestException(ErrorCode.CODE_30163005);
		}
	}

	@Data
	public static class ValidateUploadContext {
		/**
		 * 校验成功的数据
		 */
		private List<OilIndexSnapshot> oilIndexSnapshotList;
		/**
		 * 上传的表单数据
		 */
		private List<OilIndexSnapshotUploadForm> forms;
		/**
		 * 上传失败的错误信息
		 */
		private List<UploadError> uploadErrors;

		/**
		 * 是否验证通过
		 */
		private boolean ok;

		public ValidateUploadContext(List<OilIndexSnapshotUploadForm> forms) {
			this.forms = forms;
		}
	}

	/**
	 * 校验上传
	 *
	 * @param context
	 * @return
	 */
	public void validateUpload(
			OilIndexSnapshotValidator.ValidateUploadContext context,
			Long versionId, Long versionRecordId) {
		OilIndexVersion oilIndexVersion = oilIndexVersionValidator
				.validateExist(versionId);
		OilIndexVersionRecord oilIndexVersionRecord = oilIndexVersionRecordValidator
				.validateExist(versionRecordId);
		this.validateAuth(oilIndexVersionRecord);

		if (CollectionUtils.isEmpty(context.getForms())) {
			throw new BadRequestException(ErrorCode.CODE_30090014);
		}
		if (context.getForms().size() > ProductTypeIndexDef.UPLOAD_MAX_SIZE) {
			throw new BadRequestException(ErrorCode.CODE_30090015);
		}

		// 校验数据格式
		this.verifyDataFormat(context, versionId, versionRecordId,
				oilIndexVersion.getVersionDate());
		if (!context.isOk()) {
			return;
		}
		// 校验数据是否已存在
		this.verifyExist(context, versionRecordId);
	}

	/**
	 * 校验上传的数据格式
	 *
	 * @param context
	 * @param versionId
	 */
	private void verifyDataFormat(
			OilIndexSnapshotValidator.ValidateUploadContext context,
			Long versionId, Long versionRecordId, LocalDateTime versionDate) {

		List<OilIndexSnapshotUploadForm> forms = context.getForms();
		List<UploadError> uploadErrors = new ArrayList<>();
		List<OilIndexSnapshot> oilIndexSnapshots = new ArrayList<>();
		context.setUploadErrors(uploadErrors);
		context.setOilIndexSnapshotList(oilIndexSnapshots);
		context.setOk(Boolean.TRUE);

		List<Long> oilSetIdList = new ArrayList<>();
		for (OilIndexSnapshotUploadForm form : forms) {
			String oilSiteId = form.getOilSiteId();
			if (StringUtils.isNotBlank(oilSiteId)) {
				Long newOilSiteId;
				try {
					newOilSiteId = Long.parseLong(oilSiteId);
				} catch (Exception e) {
					newOilSiteId = null;
				}
				if (Objects.nonNull(newOilSiteId)) {
					oilSetIdList.add(newOilSiteId);
				}
			}
		}
		Map<Long, OilSite> oilSiteMap = oilSiteService.getIdMap(oilSetIdList);

		OilIndexSnapshot oilIndexSnapshot;
		UploadError msgVo;
		Set<String> oilSiteIds = new HashSet<>();
		for (int i = 0; i < forms.size(); i++) {
			StringJoiner joiner = new StringJoiner("<br />");
			oilIndexSnapshot = new OilIndexSnapshot();
			OilIndexSnapshotUploadForm uploadForm = forms.get(i);
			if (StringUtils.isBlank(uploadForm.getOilSiteId())) {
				joiner.add("站点编号不能为空");
			} else {
				if (!oilSiteIds.add(uploadForm.getOilSiteId())) {
					joiner.add("站点编号不能重复");
				}
				try {
					if (Objects.isNull(oilSiteMap
							.get(Long.valueOf(uploadForm.getOilSiteId())))) {
						joiner.add("该站点不存在");
					}
				} catch (Exception e) {
					joiner.add("站点编号格式错误");
				}

			}
			if (StringUtils.isBlank(uploadForm.getOilSiteName())) {
				joiner.add("站点名称不能为空");
			}
			if (StringUtils.isBlank(uploadForm.getBrand())) {
				joiner.add("品牌不能为空");
			}
			OilSite oilSite = oilSiteMap
					.get(Long.valueOf(uploadForm.getOilSiteId()));
			if (Objects.nonNull(oilSite)) {
				if (CommonDef.Symbol.YES.match(oilSite.getDel())) {
					joiner.add("该站点不存在");
				} else {
					// del不为0
					if (CommonDef.Symbol.NO.match(oilSite.getState())) {
						joiner.add("该站点未启用");
					} else {
						// 已启用了
						if (StringUtils.isNotBlank(uploadForm.getOilSiteName())
								&& !uploadForm.getOilSiteName()
										.equals(oilSite.getName())) {
							joiner.add("站点名称和编号不匹配");
						}
						// 校验品牌和编号是否匹配
						if (StringUtils.isNotBlank(uploadForm.getBrand())
								&& !uploadForm.getBrand()
										.equals(oilSite.getBrand())) {
							joiner.add("品牌和编号不匹配");
						}
					}
				}
			}
			if (StringUtils.isBlank(uploadForm.getZeroDieselListingPrice())
					&& StringUtils.isBlank(
							uploadForm.getZeroDieselActualSellingPrice())
					&& StringUtils
							.isBlank(uploadForm.getLightFuelListingPrice())
					&& StringUtils.isBlank(
							uploadForm.getLightFuelActualSellingPrice())) {
				joiner.add("价格不能全部为空");
			}
			if (StringUtils
					.isNotBlank(uploadForm.getZeroDieselListingPrice())) {
				if (!this.validatePrice(
						uploadForm.getZeroDieselListingPrice())) {
					joiner.add("0号柴油挂牌价格式错误");
				}
			}

			if (StringUtils
					.isNotBlank(uploadForm.getZeroDieselActualSellingPrice())) {
				if (!this.validatePrice(
						uploadForm.getZeroDieselActualSellingPrice())) {
					joiner.add("0号柴油实际售卖价格式错误");
				}
			}

			if (StringUtils.isNotBlank(uploadForm.getLightFuelListingPrice())) {
				if (!this
						.validatePrice(uploadForm.getLightFuelListingPrice())) {
					joiner.add("轻质燃油挂牌价格式错误");
				}
			}

			if (StringUtils
					.isNotBlank(uploadForm.getLightFuelActualSellingPrice())) {
				if (!this.validatePrice(
						uploadForm.getLightFuelActualSellingPrice())) {
					joiner.add("轻质燃油挂牌价格式错误");
				}
			}

			if (joiner.length() > 0) {
				// 格式错误 设置标志位
				context.setOk(Boolean.FALSE);
				msgVo = new UploadError();
				msgVo.setIndex(i + 1);
				msgVo.setErrorMsg(joiner.toString());
				uploadErrors.add(msgVo);
				continue;
			}

			oilIndexSnapshot.setVersionDate(versionDate);
			oilIndexSnapshot.setVersionId(versionId);
			oilIndexSnapshot.setVersionRecordId(versionRecordId);
			oilIndexSnapshot
					.setOilSiteId(Long.valueOf(uploadForm.getOilSiteId()));
			if (StringUtils
					.isNotBlank(uploadForm.getZeroDieselListingPrice())) {
				oilIndexSnapshot.setZeroDieselListingPrice(
						Long.valueOf(uploadForm.getZeroDieselListingPrice()));
			}
			if (StringUtils
					.isNotBlank(uploadForm.getZeroDieselActualSellingPrice())) {
				oilIndexSnapshot.setZeroDieselActualSellingPrice(Long
						.valueOf(uploadForm.getZeroDieselActualSellingPrice()));
			}
			if (StringUtils.isNotBlank(uploadForm.getLightFuelListingPrice())) {
				oilIndexSnapshot.setLightFuelListingPrice(
						Long.valueOf(uploadForm.getLightFuelListingPrice()));
			}
			if (StringUtils
					.isNotBlank(uploadForm.getLightFuelActualSellingPrice())) {
				oilIndexSnapshot.setLightFuelActualSellingPrice(Long
						.valueOf(uploadForm.getLightFuelActualSellingPrice()));
			}
			oilIndexSnapshots.add(oilIndexSnapshot);
		}
	}

	/**
	 * 校验数据是否已存在
	 *
	 * @param context
	 */
	private void verifyExist(
			OilIndexSnapshotValidator.ValidateUploadContext context,
			Long versionRecordId) {
		List<OilIndexSnapshot> oilIndexSnapshots = context
				.getOilIndexSnapshotList();
		List<UploadError> uploadErrors = context.getUploadErrors();

		for (int i = 0; i < oilIndexSnapshots.size(); i++) {
			OilIndexSnapshot oilIndexSnapshot = oilIndexSnapshots.get(i);

			List<OilIndexSnapshot> oilIndexSnapshotList = service
					.findByOilSiteIdAndVersionRecordId(
							oilIndexSnapshot.getOilSiteId(), versionRecordId);
			if (!oilIndexSnapshotList.isEmpty()) {
				context.setOk(Boolean.FALSE);
				UploadError msgVo = new UploadError();
				msgVo.setIndex(i + 1);
				msgVo.setErrorMsg("该版本记录已存在该指数，请勿重复添加");
				uploadErrors.add(msgVo);
			}
		}
	}

	/**
	 * 校验价格
	 * 
	 * @param priceStr
	 * @return 通过返回true
	 */
	private Boolean validatePrice(String priceStr) {
		long price;
		try {
			price = Long.parseLong(priceStr);
		} catch (Exception e) {
			return Boolean.FALSE;
		}

		if (!(price > 0 && price < 100000L)) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}
}
