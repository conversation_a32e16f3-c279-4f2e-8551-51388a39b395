package com.zhihaoscm.service.resource.custom.transport.order.ship;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.entity.File;
import com.zhihaoscm.domain.bean.entity.TransportOrderDetailsShip;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.vo.TransportOrderShipVo;
import com.zhihaoscm.domain.meta.biz.HistoryDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderShipDef;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.TransportOrderShipService;
import com.zhihaoscm.service.resource.form.transport.order.ship.ConfirmationDepartureForm;
import com.zhihaoscm.service.resource.validator.file.FileValidator;
import com.zhihaoscm.service.resource.validator.transport.order.details.ship.TransportOrderDetailsShipValidator;
import com.zhihaoscm.service.resource.validator.transport.order.ship.TransportOrderShipValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "船运单管理", description = "船运单管理API")
@RestController
@RequestMapping("/transport/order/ship")
public class TransportOrderShipResource {

	@Autowired
	private TransportOrderShipService service;

	@Autowired
	private TransportOrderDetailsShipValidator transportOrderDetailsShipValidator;

	@Autowired
	private TransportOrderShipValidator transportOrderShipValidator;

	@Autowired
	private FileValidator fileValidator;

	@Operation(summary = "查询船运单列表")
	@GetMapping(value = "/custom-paging")
	public ApiResponse<Page<TransportOrderShipVo>> customPaging(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "货物类型称或编号") @RequestParam(value = "keyword", required = false) String keyword,
			@Parameter(description = "1船主 2货主") @RequestParam(value = "queryType") Integer queryType,
			@Parameter(description = "状态 9待支付船务信息服务费 11服务费确认中") @RequestParam(value = "state", required = false) Integer state,
			@Parameter(description = "始发地码头名称") @RequestParam(value = "sourcePortName", required = false) String sourcePortName,
			@Parameter(description = "目的地码头名称") @RequestParam(value = "destinationPortName", required = false) String destinationPortName,
			@Parameter(description = "是否未完成状态 true:查询的是未完成状态") @RequestParam(value = "isNotCompleted", required = false) Boolean isNotCompleted) {

		return new ApiResponse<>(
				service.customPaging(page, size, keyword, queryType,
						CustomerContextHolder.getCustomerLoginVo()
								.getProxyAccount().getId(),
						state, sourcePortName, destinationPortName,
						isNotCompleted));
	}

	@Operation(summary = "船运单下拉")
	@GetMapping("/selector")
	public ApiResponse<List<TransportOrderShip>> selector(
			@RequestParam(value = "keyword", required = false) String keyword) {
		return new ApiResponse<>(service.selector(keyword, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId()));
	}

	@Operation(summary = "船运单下拉vo")
	@GetMapping("/selector-vo")
	public ApiResponse<List<TransportOrderShipVo>> selectorVo(
			@RequestParam(value = "keyword", required = false) String keyword) {
		return new ApiResponse<>(
				service.selectorVo(keyword, CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount().getId()));
	}

	@Operation(summary = "查询船运单详情")
	@GetMapping("/vo/{id}")
	public ApiResponse<TransportOrderShipVo> findVoById(
			@PathVariable String id) {
		transportOrderShipValidator.validateFindVoById(id);
		return new ApiResponse<>(service.findVoById(id).orElse(null));
	}

	@Operation(summary = "船务信息服务费进去船运单详情")
	@GetMapping("/detail/{id}")
	public ApiResponse<TransportOrderShipVo> findDetail(
			@PathVariable String id) {
		transportOrderShipValidator.validateDetail(id);
		return new ApiResponse<>(service.findVoById(id).orElse(null));
	}

	@Operation(summary = "查询船主船运单不是卸货中、已卸货或已完成状态")
	@GetMapping("/captain-id")
	public ApiResponse<List<TransportOrderShip>> findByCaptainId() {
		return new ApiResponse<>(service.findByCaptainId(
				CustomerContextHolder.getCustomerLoginVo().getProxyAccount()
						.getId(),
				List.of(TransportOrderShipDef.State.DURING_UNLOADING.getCode(),
						TransportOrderShipDef.State.DISCHARGED.getCode(),
						TransportOrderShipDef.State.COMPLETED.getCode())));
	}

	@History(success = HistoryDef.FLIGHT_CONFIRMATION, bizNo = "{{#id}}", module = HistoryDef.SHIPPING_DOCUMENT_OPERATOR, origin = HistoryDef.CUSTOM)
	@Operation(summary = "发航确认")
	@PutMapping(value = "/confirmation-departure/{id}")
	public ApiResponse<Void> confirmationDeparture(
			@PathVariable(value = "id") String id,
			@Validated @RequestBody ConfirmationDepartureForm form) {
		transportOrderDetailsShipValidator.validateConfirmationDeparture(id);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipValidator
				.validateExist(id);
		// 货主船运单被三方需求中盛关联了的船运单货主端（小程序、app、pc）暂时隐藏操作按钮，只能在后台或供应链端操作
		transportOrderShipValidator.validateOwnerOperate(id);
		service.confirmationDeparture(
				form.convertToEntity(transportOrderDetailsShip));
		return new ApiResponse<>();
	}

	@Operation(summary = "到港确认")
	@PutMapping("/confirmation-arrival-port/{id}")
	public ApiResponse<Void> confirmationArrivalPort(@PathVariable String id,
			@Parameter(description = "到港视频ID") @RequestParam(value = "arrivalVideoId", required = false) Long arrivalVideoId) {
		transportOrderDetailsShipValidator.validateConfirmationArrivalPort(id);
		service.confirmationArrivalPort(id, arrivalVideoId);
		return new ApiResponse<>();
	}

	@History(success = HistoryDef.AGREE_TO_UNLOAD, bizNo = "{{#id}}", module = HistoryDef.SHIPPING_DOCUMENT_OPERATOR, origin = HistoryDef.CUSTOM)
	@Operation(summary = "同意卸货")
	@PutMapping(value = "/agree-unload/{id}")
	public ApiResponse<Void> agreeUnload(
			@PathVariable(value = "id") String id) {
		transportOrderDetailsShipValidator.validateAgreeUnload(id);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipValidator
				.validateExist(id);
		// 货主船运单被三方需求中盛关联了的船运单货主端（小程序、app、pc）暂时隐藏操作按钮，只能在后台或供应链端操作
		transportOrderShipValidator.validateOwnerOperate(id);
		service.agreeUnload(transportOrderDetailsShip);
		return new ApiResponse<>();
	}

	@Operation(summary = "船主选择定金收款账户")
	@PutMapping("/{id}/{bankId}")
	public ApiResponse<Void> selectBankId(@PathVariable String id,
			@PathVariable Long bankId) {
		TransportOrderShip transportOrderShip = transportOrderShipValidator
				.validateBankId(id, bankId);
		service.updateBank(transportOrderShip, bankId);
		return new ApiResponse<>();
	}

	@Operation(summary = "上传文件（水印）")
	@PostMapping(value = "/upload/watermark/{id}", consumes = "multipart/form-data")
	public ApiResponse<File> uploadWatermark(@RequestPart MultipartFile file,
			@Parameter(description = "运单id") @PathVariable String id)
			throws Exception {
		fileValidator.validateFile(file);
		TransportOrderShip transportOrderShip = transportOrderShipValidator
				.validateExist(id);
		File dbFile = service.uploadWatermark(file, transportOrderShip);
		return new ApiResponse<>(dbFile);
	}

}
