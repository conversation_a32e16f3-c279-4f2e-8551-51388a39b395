package com.zhihaoscm.service.resource.custom.banner;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.entity.Banner;
import com.zhihaoscm.service.core.service.BannerService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "轮播图管理", description = "轮播图管理API")
@RestController
@RequestMapping("/banner")
public class BannerResource {

	@Autowired
	private BannerService service;

	@Operation(summary = "轮播图列表")
	@GetMapping(value = "/findList")
	public ApiResponse<List<Banner>> findList(
			@Parameter(description = "位置 1 链云砂石 4 链云船务") @RequestParam(value = "type") Integer pos) {
		return new ApiResponse<>(service.selector(pos));
	}

}
