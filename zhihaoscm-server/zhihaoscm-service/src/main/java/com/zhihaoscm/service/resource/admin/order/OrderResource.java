package com.zhihaoscm.service.resource.admin.order;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.Order;
import com.zhihaoscm.domain.bean.vo.OrderVo;
import com.zhihaoscm.domain.bean.vo.ServiceSpecialVo;
import com.zhihaoscm.service.core.service.OrderService;
import com.zhihaoscm.service.resource.validator.order.OrderValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 订单信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Tag(name = "订单", description = "订单API")
@RestController
@RequestMapping("/order")
public class OrderResource {

	@Autowired
	private OrderService service;

	@Autowired
	private OrderValidator validator;

	@Operation(summary = "分页查询")
	@GetMapping("/paging")
	public ApiResponse<Page<Order>> paging(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "订单编号") @RequestParam(required = false) String id,
			@Parameter(description = "商品编号") @RequestParam(required = false) String productId,
			@Parameter(description = "订单类型 1寄售 2团购 3捡漏") @RequestParam(required = false) Integer orderType,
			@Parameter(description = "支付状态 1待付款 2待提货 3提货中 4已完成 5已取消") @RequestParam(required = false) List<Integer> states,
			@Parameter(description = "采购账号信息：实名、组织机构认证、手机号，模糊搜索") @RequestParam(required = false) String keyword,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder,
			@Parameter(description = "下单开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@Parameter(description = "下单开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
		return new ApiResponse<>(PageUtil.convert(
				service.paging(page, size, id, productId, orderType, states,
						keyword, sortKey, sortOrder, beginTime, endTime)));
	}

	@Operation(summary = "详情")
	@GetMapping("/vo/{id}")
	public ApiResponse<OrderVo> findVoById(@PathVariable String id) {
		return new ApiResponse<>(service.findVoById(id).orElse(null));
	}

	@Operation(summary = "查询专员")
	@GetMapping(value = "/find/specials")
	public ApiResponse<List<ServiceSpecialVo>> findServiceSpecials() {
		return new ApiResponse<>(service
				.findServiceSpecials());
	}

	@Operation(summary = "指派")
	@PutMapping("/assign/{id}/{handlerId}")
	public ApiResponse<Void> assign(@PathVariable String id,
			@PathVariable Long handlerId) {
		Order order = validator.validateExist(id);
		service.assign(order, handlerId);
		return new ApiResponse<>();
	}

	@Operation(summary = "取消订单")
	@PutMapping("/cancel/{id}")
	public ApiResponse<Void> cancel(@PathVariable String id) {
		Order order = validator.validateCancel(id);
		service.cancel(order);
		return new ApiResponse<>();
	}

	@Operation(summary = "完成订单")
	@PutMapping("/finish/{id}")
	public ApiResponse<Void> finish(@PathVariable String id) {
		Order order = validator.validateFinish(id);
		service.finish(order);
		return new ApiResponse<>();
	}

	@Operation(summary = "删除订单")
	@DeleteMapping("/{id}")
	public ApiResponse<Void> delete(@PathVariable String id) {
		validator.validateDelete(id, CommonDef.UserType.INNER.getCode());
		service.delete(id, CommonDef.UserType.INNER.getCode());
		return new ApiResponse<>();
	}

}
