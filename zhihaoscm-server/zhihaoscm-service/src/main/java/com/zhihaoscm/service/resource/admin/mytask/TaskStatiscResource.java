package com.zhihaoscm.service.resource.admin.mytask;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.InstitutionApplyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Tag(name = "待办工作统计", description = "待办统计API")
@RestController
@RequestMapping("/mytask/statisc")
@Slf4j
public class TaskStatiscResource {

	@Autowired
	private ProductService productService;

	@Autowired
	private ShippingRequirementPlatService shippingRequirementPlatService;

	@Autowired
	private ShippingRequirementCustomerService shippingRequirementCustomerService;

	@Autowired
	private TransportOrderShipService transportOrderShipService;

	@Autowired
	private DeviceService deviceService;

	@Autowired
	private ClaimService claimService;

	@Autowired
	private ShippingOwnerCluesService shippingOwnerCluesService;

	@Autowired
	private ShipApplyService shipApplyService;

	@Autowired
	private MonitorEarlyWarnRecordService monitorEarlyWarnRecordService;

	@Autowired
	private DeviceCapturePicDatasetService deviceCapturePicDatasetService;

	@Autowired
	private ShipInfoServiceFeeService shipInfoServiceFeeService;

	@Autowired
	private ProductTypeIndexVersionService productTypeIndexVersionService;

	@Autowired
	private CompositeIndexService compositeIndexService;

	@Autowired
	private ShippingPriceIndexVersionService shippingPriceIndexVersionService;

	@Autowired
	private ShippingCompositeIndexService shippingCompositeIndexService;

	@Autowired
	private OilIndexVersionService oilIndexVersionService;

	@Autowired
	private InformationService informationService;

	@Autowired
	private SandAcademyService sandAcademyService;

	@Autowired
	private InstitutionApplyService institutionApplyService;

	@Autowired
	private MessageFeedbackService messageFeedbackService;

	@Autowired
	private HelpCenterService helpCenterService;

	@Autowired
	private FeaturesFeedbackService featuresFeedbackService;

	@Autowired
	private CorpusLibraryService corpusLibraryService;

	@Autowired
	private OilFeeService oilFeeService;

	@Autowired
	private OilOrderSnapshotService oilOrderSnapshotService;

	@Autowired
	private BusinessOpportunityService businessOpportunityService;

	/**
	 * 商品管理统计
	 *
	 * @return
	 */
	@Operation(summary = "统计商品需求")
	@GetMapping("/product")
	public ApiResponse<ProductCountVo> statiscProduct() {
		// 判断是否有管理权限 有则查询所有数据
		boolean isFull = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.PRODUCT_REVIEW
						.getPermission());
		if (!isFull) {
			ProductCountVo productCountVo = new ProductCountVo();
			productCountVo.setStatiscCount(0L);
			return new ApiResponse<>(productCountVo);
		}

		return new ApiResponse<>(productService.statiscProduct());
	}

	/**
	 * 货主船运需求统计
	 *
	 * @return
	 */
	@Operation(summary = "统计货主船运需求")
	@GetMapping("/shipping-requirement-customer")
	public ApiResponse<ShippingRequirementCustomerCountVo> statiscShippingRequirementCustomer() {
		boolean hasFull = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.SHIP_DEMAND_MANAGE
						.getPermission());
		boolean hasDeal = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.SHIP_DEMAND_DEAL
						.getPermission());
		ShippingRequirementCustomerCountVo shippingRequirementCustomerCountVo = shippingRequirementCustomerService
				.statiscShippingRequirementCustomer(Objects
						.requireNonNull(UserContextHolder.getUser()).getId(),
						hasFull, hasDeal);
		return new ApiResponse<>(shippingRequirementCustomerCountVo);
	}

	/**
	 * 平台船运需求统计
	 *
	 * @return
	 */
	@Operation(summary = "统计平台船运需求")
	@GetMapping("/shipping-requirement-plat")
	public ApiResponse<ShippingRequirementPlatCountVo> statiscShippingRequirementPlat() {
		boolean hasDeal = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.SHIP_DEMAND_DEAL
						.getPermission());
		ShippingRequirementPlatCountVo shippingRequirementPlatCountVo = shippingRequirementPlatService
				.statiscShippingRequirementPlat(Objects
						.requireNonNull(UserContextHolder.getUser()).getId(),
						hasDeal);
		return new ApiResponse<>(shippingRequirementPlatCountVo);
	}

	/**
	 * 船运单需求统计
	 *
	 * @return
	 */
	@Operation(summary = "统计船运单需求")
	@GetMapping("/transport-order-ship")
	public ApiResponse<TransportOrderShipCountVo> staticsTransportOrderShip() {
		boolean hasDeal = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.SHIP_ORDER_DEAL
						.getPermission());
		boolean hasFull = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.SHIP_DEMAND_MANAGE
						.getPermission());
		TransportOrderShipCountVo transportOrderShipCountVo = transportOrderShipService
				.staticsTransportOrderShip(Objects
						.requireNonNull(UserContextHolder.getUser()).getId(),
						hasDeal, hasFull);
		return new ApiResponse<>(transportOrderShipCountVo);
	}

	@Operation(summary = "统计油品订单")
	@GetMapping("/oil-order-snapshot")
	public ApiResponse<OilOrderSnapshotCountVo> statisticsOilOrderSnapshot() {
		// 是否有 油品订单处理 权限
		boolean hasFull = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.GAS_ORDER_DEAL
						.getPermission());
		OilOrderSnapshotCountVo oilOrderSnapshotCountVo = oilOrderSnapshotService
				.statisticsOilOrderSnapshot(hasFull);
		return new ApiResponse<>(oilOrderSnapshotCountVo);
	}

	/**
	 * 设备管理统计
	 *
	 * @return
	 */
	@Operation(summary = "统计设备管理")
	@GetMapping("/device-manage")
	public ApiResponse<DeviceManageCountVo> staticsDeviceManage() {
		// 是否有设备管理 管理权限
		boolean hasManage = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.DEVICE_W.getPermission());
		DeviceManageCountVo deviceManageCountVo = deviceService
				.staticsDeviceManage(Objects
						.requireNonNull(UserContextHolder.getUser()).getId(),
						hasManage);
		return new ApiResponse<>(deviceManageCountVo);
	}

	/**
	 * 设备申领统计
	 * 
	 * @return
	 */
	@Operation(summary = "统计设备申领")
	@GetMapping("/claim")
	public ApiResponse<ClaimCountVo> staticsClaim() {
		// 是否有设备申领 处理权限
		boolean hasDeal = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.DEVICE_BIND_W
						.getPermission());
		ClaimCountVo claimCountVo = claimService.staticsClaim(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				hasDeal);
		return new ApiResponse<>(claimCountVo);
	}

	/**
	 * 船主线索统计
	 * 
	 * @return
	 */
	@Operation(summary = "统计船主线索")
	@GetMapping("/shipping-owner-clues")
	public ApiResponse<ShippingOwnerCluesCountVo> staticsShippingOwnerClues() {
		// 是否有船主线索 管理权限
		boolean hasManage = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.SHIP_CLUE_W.getPermission());
		ShippingOwnerCluesCountVo shippingOwnerCluesCountVo = shippingOwnerCluesService
				.staticsShippingOwnerClues(Objects
						.requireNonNull(UserContextHolder.getUser()).getId(),
						hasManage);
		return new ApiResponse<>(shippingOwnerCluesCountVo);
	}

	/**
	 * 船主认证审核统计
	 * 
	 * @return
	 */
	@Operation(summary = "统计认证审核")
	@GetMapping("/ship-apply")
	public ApiResponse<ShipApplyAuditCountVo> staticsShipApply() {
		// 是否有认证管理 审核权限
		boolean hasAudit = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.SHIP_REVIEW.getPermission());
		ShipApplyAuditCountVo shipApplyAuditCountVo = shipApplyService
				.staticsShipApply(Objects
						.requireNonNull(UserContextHolder.getUser()).getId(),
						hasAudit)
				.orElse(null);
		return new ApiResponse<>(shipApplyAuditCountVo);
	}

	/**
	 * 监控预警统计
	 * 
	 * @return
	 */
	@Operation(summary = "统计监控预警")
	@GetMapping("/monitor-early-warn-record")
	public ApiResponse<MonitorEarlyWarnRecordCountVo> staticsMonitorEarlyWarnRecord() {
		// 是否有监控预警 管理权限
		boolean hasManage = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.WARNING_MANAGE
						.getPermission());
		// 是否有监控预警 处理权限
		boolean hasDeal = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.WARING_DEAL.getPermission());
		MonitorEarlyWarnRecordCountVo monitorEarlyWarnRecordCountVo = monitorEarlyWarnRecordService
				.staticsMonitorEarlyWarnRecord(Objects
						.requireNonNull(UserContextHolder.getUser()).getId(),
						hasManage, hasDeal);
		return new ApiResponse<>(monitorEarlyWarnRecordCountVo);
	}

	/**
	 * 模型数据统计
	 * 
	 * @return
	 */
	@Operation(summary = "统计模型数据")
	@GetMapping("/device-capture-pic-dataset")
	public ApiResponse<DeviceCapturePicCountVo> staticsDeviceCapturePicDataset() {
		// 是否有模型数据 管理权限
		boolean hasManage = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.MODEL_DATA_W
						.getPermission());
		DeviceCapturePicCountVo deviceCapturePicCountVo = deviceCapturePicDatasetService
				.staticsDeviceCapturePicDataset(hasManage);
		return new ApiResponse<>(deviceCapturePicCountVo);
	}

	/**
	 * 船务交易统计-待确认船务信息服务费
	 *
	 * @return
	 */
	@Operation(summary = "统计船务交易-待确认船务信息服务费")
	@GetMapping("/ship-info-service-fee")
	public ApiResponse<ShipInfoServiceFeeCountVo> staticsShipInfoServiceFee() {
		boolean hasFull = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.SHIP_ORDER_SERVICE_FEE_W
						.getPermission());
		ShipInfoServiceFeeCountVo shipInfoServiceFeeCountVo = shipInfoServiceFeeService
				.staticsShipInfoServiceFee(hasFull);
		return new ApiResponse<>(shipInfoServiceFeeCountVo);
	}

	@Operation(summary = "统计船务交易-待确认油品费用")
	@GetMapping("/oil-fee")
	public ApiResponse<OilFeeCountVo> statisticsOilFee() {
		// 是否有 油品费用管理 权限
		boolean hasFull = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.REFUELING_FEE_W
						.getPermission());
		OilFeeCountVo oilFeeCountVo = oilFeeService.statisticsOilFee(hasFull);
		return new ApiResponse<>(oilFeeCountVo);
	}

	@Operation(summary = "统计砂石指数-待处理、待发布")
	@GetMapping("/product-type-index-version")
	public ApiResponse<ProductTypeIndexVersionCountVo> statisticsProductTypeIndexVersion() {
		// 是否有 砂石指数处理 权限
		boolean hasDeal = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.CATEGORY_INDEX_DEAL
						.getPermission());
		// 是否有 砂石指数管理 权限
		boolean hasManage = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.CATEGORY_INDEX_MANAGE
						.getPermission());
		ProductTypeIndexVersionCountVo productTypeIndexVersionCountVo = productTypeIndexVersionService
				.statisticsProductTypeIndexVersion(hasDeal, hasManage);
		return new ApiResponse<>(productTypeIndexVersionCountVo);
	}

	@Operation(summary = "统计砂石综合指数-未发布")
	@GetMapping("/composite-index")
	public ApiResponse<CompositeIndexCountVo> statisticsCompositeIndex() {
		// 是否有 砂石综合指数管理 权限
		boolean hasFull = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.COMPSITE_INDEX_MANAGE
						.getPermission());
		CompositeIndexCountVo compositeIndexCountVo = compositeIndexService
				.statisticsCompositeIndex(hasFull);
		return new ApiResponse<>(compositeIndexCountVo);
	}

	@Operation(summary = "统计运价指数-待处理、待发布")
	@GetMapping("/shipping-price-index-version")
	public ApiResponse<ShippingPriceIndexVersionCountVo> statisticsShippingPriceIndexVersion() {
		// 是否有 运价指数处理 权限
		boolean hasDeal = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.FREIGHT_RATE_DEAL
						.getPermission());
		// 是否有 运价指数管理 权限
		boolean hasManage = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.FREIGHT_RATE_MANAGE
						.getPermission());
		ShippingPriceIndexVersionCountVo shippingPriceIndexVersionCountVo = shippingPriceIndexVersionService
				.statisticsShippingPriceIndexVersion(hasDeal, hasManage);
		return new ApiResponse<>(shippingPriceIndexVersionCountVo);
	}

	@Operation(summary = "统计运价综合指数-未发布")
	@GetMapping("/shipping-composite-index")
	public ApiResponse<ShippingCompositeIndexCountVo> statisticsShippingCompositeIndex() {
		// 是否有 运价综合指数管理 权限
		boolean hasFull = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.COMPSITE_FREIGHT_MANAGE
						.getPermission());
		ShippingCompositeIndexCountVo shippingCompositeIndexCountVo = shippingCompositeIndexService
				.statisticsShippingCompositeIndex(hasFull);
		return new ApiResponse<>(shippingCompositeIndexCountVo);
	}

	@Operation(summary = "统计油品指数-待处理、待发布")
	@GetMapping("/oil-index-version")
	public ApiResponse<OilIndexVersionCountVo> statisticsOilIndexVersion() {
		// 是否有 油品指数处理 权限
		boolean hasDeal = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.PETROL_INDEX_DEAL
						.getPermission());
		// 是否有 油品指数管理 权限
		boolean hasManage = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.PETROL_INDEX_MANAGE
						.getPermission());
		OilIndexVersionCountVo vo = oilIndexVersionService
				.statisticsOilIndexVersion(hasDeal, hasManage);
		return new ApiResponse<>(vo);
	}

	@Operation(summary = "统计资讯-待上架")
	@GetMapping("/information")
	public ApiResponse<InformationCountVo> statisticsInformationUnlisted() {
		// 是否有 砂石资讯管理 权限
		boolean hasFull = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.INFO_W.getPermission());
		InformationCountVo informationCountVo = informationService
				.statisticsInformationUnlisted(hasFull);
		return new ApiResponse<>(informationCountVo);
	}

	@Operation(summary = "统计砂石学院-待上架（已下架）")
	@GetMapping("/sand-academy")
	public ApiResponse<SandAcademyCountVo> statisticsSandAcademyUnlisted() {
		// 是否有 砂石学院管理 权限
		boolean hasFull = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.SAND_ACADEMY_W
						.getPermission());
		return new ApiResponse<>(sandAcademyService
				.statisticsSandAcademyUnlisted(hasFull).orElse(null));
	}

	/**
	 * 机构认证审核
	 * 
	 * @return
	 */
	@Operation(summary = "机构认证审核-待审核")
	@GetMapping("/institution-apply")
	public ApiResponse<InstitutionApplyCountVo> staticsInstitutionApply() {
		// 是否有 机构认证管理 审核权限
		boolean hasFull = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.ORG_REVIEW.getPermission());
		return new ApiResponse<>(institutionApplyService
				.staticsInstitutionApply(hasFull).orElse(null));
	}

	@Operation(summary = "统计留言反馈-待回复（未回复）")
	@GetMapping("/message-feedback")
	public ApiResponse<MessageFeedbackCountVo> statisticsMessageFeedback() {
		// 是否有 留言反馈管理 权限
		boolean hasFull = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.FEEDBACK_W.getPermission());
		return new ApiResponse<>(messageFeedbackService
				.statisticsMessageFeedback(hasFull).orElse(null));
	}

	@Operation(summary = "统计帮助中心-待上架（已下架）")
	@GetMapping("/help-center")
	public ApiResponse<HelpCenterCountVo> statisticsHelpCenter() {
		// 是否有 帮助中心管理 权限
		boolean hasFull = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.HELP_W.getPermission());
		return new ApiResponse<>(
				helpCenterService.statisticsHelpCenter(hasFull).orElse(null));
	}

	@Operation(summary = "统计功能反馈-待处理")
	@GetMapping("/features-feedback")
	public ApiResponse<FeaturesFeedbackCountVo> statisticsFeaturesFeedback() {
		// 是否有 功能反馈管理 权限
		boolean hasFull = UserContextHolder.getPermissions().contains(
				AdminPermissionDef.AdminPermission.FUNCTION_W.getPermission());
		return new ApiResponse<>(featuresFeedbackService
				.statisticsFeaturesFeedback(hasFull).orElse(null));
	}

	@Operation(summary = "统计功能反馈-待处理")
	@GetMapping("/corpus-unmark")
	public ApiResponse<UnMarkCorpusLibraryVo> statisticsCorpusUnMark() {
		// 是否有 功能反馈管理 权限
		boolean hasFull = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.ASSISTANTS_CORPUS_W
						.getPermission());
		return new ApiResponse<>(
				corpusLibraryService.findUnMark(hasFull).orElse(null));
	}

	@Operation(summary = "统计商机管理")
	@GetMapping("/business-opportunity")
	public ApiResponse<BusinessOpportunityCountVo> statisticsBusinessOpportunity() {
		// 是否有 商机管理审核 权限
		boolean hasFull = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.BUSINESS_REVIEW
						.getPermission());
		BusinessOpportunityCountVo countVo = businessOpportunityService
				.statisticsBusinessOpportunity(hasFull);
		return new ApiResponse<>(countVo);
	}
}
