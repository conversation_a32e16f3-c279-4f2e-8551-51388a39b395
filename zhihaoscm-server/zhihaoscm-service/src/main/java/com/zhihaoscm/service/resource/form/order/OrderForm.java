package com.zhihaoscm.service.resource.form.order;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.domain.bean.entity.Order;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class OrderForm {

	/**
	 * 订单类型(1寄售,2团购,3捡漏)
	 * {@link com.zhihaoscm.domain.meta.biz.OrderDef.OrderType}
	 */
	@Schema(title = "订单类型(1寄售,2团购,3捡漏)")
	@NotNull(message = "订单类型不能为空")
	@Range(min = 1, max = 3, message = "订单类型不合法")
	private Integer orderType;

	/**
	 * 商品id
	 */
	@Schema(title = "商品id")
	@NotBlank(message = "商品id不能为空")
	private String productId;

	/**
	 * 地址id
	 */
	@Schema(title = "地址id")
	@NotNull(message = "地址id不能为空")
	private Long addressId;

	/**
	 * 采购数量(吨数)
	 */
	@Schema(title = "采购数量(吨数)")
	@NotNull(message = "采购数量不能为空")
	private Long ton;

	/**
	 * 采购说明
	 */
	@Schema(title = "采购说明")
	@Length(max = 300, message = "采购说明不能超过300个字符")
	private String remark;

	public Order convertToEntity() {
		Order order = new Order();
		order.setOrderType(this.orderType);
		order.setProductId(this.productId);
		order.setAddressId(this.addressId);
		order.setTon(this.ton);
		order.setRemark(this.remark);
		return order;
	}

}
