package com.zhihaoscm.service.resource.validator.transport.order.ship;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.CustomerBank;
import com.zhihaoscm.domain.bean.entity.TransportOrderDetailsShip;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.json.DeparturePreparationInfo;
import com.zhihaoscm.domain.bean.json.SetSailInfo;
import com.zhihaoscm.domain.bean.json.UnloadingInfo;
import com.zhihaoscm.domain.meta.biz.TransportOrderShipDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.TransportOrderShipService;
import com.zhihaoscm.service.core.service.usercenter.CustomerBankService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.resource.form.transport.order.details.ship.DeliveryInfoForm;
import com.zhihaoscm.service.resource.form.transport.order.ship.TransportOrderShipForm;
import com.zhihaoscm.service.resource.validator.transport.order.details.ship.TransportOrderDetailsShipValidator;

/**
 * 船运单校验器
 *
 */
@Component
public class TransportOrderShipValidator {

	@Autowired
	private TransportOrderShipService service;

	@Autowired
	private TransportOrderDetailsShipValidator transportOrderDetailsShipValidator;

	@Autowired
	private CustomerBankService customerBankService;

	@Autowired
	private CustomerService customerService;

	/**
	 * 校验是否存在
	 *
	 * @param id
	 * @return
	 */
	public TransportOrderShip validateExist(String id) {
		TransportOrderShip transportOrderShip = service.findOne(id)
				.orElse(null);
		if (Objects.isNull(transportOrderShip)) {
			throw new BadRequestException(ErrorCode.CODE_30137001);
		}
		return transportOrderShip;
	}

	/**
	 * 校验开始装货
	 *
	 * @param id
	 * @return
	 */
	public TransportOrderDetailsShip validateStartLoading(String id,
			DeliveryInfoForm form) {
		TransportOrderShip transportOrderShip = this
				.validateUpstreamHandler(id);
		if (!TransportOrderShipDef.State.TO_BE_LOADED
				.match(transportOrderShip.getState())) {
			// 待装货才能进行开始装货
			throw new BadRequestException(ErrorCode.CODE_30137023);
		}
		// 装货时间必须大于创建时间且小于等于当前时间
		if (TransportOrderShipDef.DepositPayType.PAY_ZH
				.match(transportOrderShip.getPayType())) {
			if (!form.getLoadingDate()
					.isAfter(transportOrderShip.getCreatedTime())
					|| !LocalDateTime.now().isAfter(form.getLoadingDate())) {
				throw new BadRequestException(ErrorCode.CODE_30137060);
			}
		}
		return transportOrderDetailsShipValidator.validateExist(id);
	}

	/**
	 * 校验发航申请
	 *
	 * @param id
	 * @return
	 */
	public void validateFlightApplication(String id) {
		TransportOrderShip transportOrderShip = validateExist(id);
		if (!TransportOrderShipDef.State.DURING_LOADING
				.match(transportOrderShip.getState())) {
			// 装货中才能进行发航申请
			throw new BadRequestException(ErrorCode.CODE_30137024);
		}
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipValidator
				.validateExist(id);
		if (TransportOrderShipDef.DepositPayType.PAY_ZH
				.match(transportOrderShip.getPayType())) {
			// 装载吨位不能空
			DeparturePreparationInfo lazbInfo = transportOrderDetailsShip
					.getLazbInfo();
			if (Objects.isNull(lazbInfo)
					|| Objects.isNull(lazbInfo.getLoadingTonnage())) {
				throw new BadRequestException(ErrorCode.CODE_30137066);
			}
		}
		this.validateUpstreamHandler(id);
	}

	/**
	 * 校验确认卸货
	 *
	 * @param id
	 * @return
	 */
	public void validateConfirmUnloading(String id) {
		TransportOrderShip transportOrderShip = this
				.validateDownstreamHandler(id);
		if (!TransportOrderShipDef.State.DURING_UNLOADING
				.match(transportOrderShip.getState())) {
			// 卸货中才能进行确认卸货
			throw new BadRequestException(ErrorCode.CODE_30137025);
		}
	}

	/**
	 * 校验上游专员
	 *
	 * @param id
	 * @return
	 */
	public TransportOrderShip validateUpstreamHandler(String id) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
		if (Objects.isNull(transportOrderShip.getUpstreamHandlerId())) {
			throw new BadRequestException(ErrorCode.CODE_30137017);
		}
		if (!Objects.equals(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				transportOrderShip.getUpstreamHandlerId())) {
			throw new BadRequestException(ErrorCode.CODE_30137016);
		}
		return transportOrderShip;
	}

	/**
	 * 校验下游专员
	 *
	 * @param id
	 * @return
	 */
	public TransportOrderShip validateDownstreamHandler(String id) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
		if (Objects.isNull(transportOrderShip.getDownstreamHandlerId())) {
			throw new BadRequestException(ErrorCode.CODE_30137018);
		}
		if (!Objects.equals(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				transportOrderShip.getDownstreamHandlerId())) {
			throw new BadRequestException(ErrorCode.CODE_30137016);
		}
		return transportOrderShip;
	}

	/**
	 * 校验上下游专员
	 * 
	 * @param id
	 */
	public void validateHandler(String id) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
		if (Objects.equals(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				transportOrderShip.getDownstreamHandlerId())) {
			return;
		}
		if (Objects.equals(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				transportOrderShip.getUpstreamHandlerId())) {
			return;
		}
		throw new BadRequestException(ErrorCode.CODE_30137016);
	}

	/**
	 * 校验删除
	 *
	 * @param id
	 */
	public void validateDelete(String id) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
		if (!(TransportOrderShipDef.State.DEPOSIT_TO_BE_PAID
				.match(transportOrderShip.getState()))
				&& !(TransportOrderShipDef.State.SHIP_INFO_FEE_TO_BE_PAID
						.match(transportOrderShip.getState()))
				&& !(TransportOrderShipDef.State.TO_BE_LOADED
						.match(transportOrderShip.getState()))
				&& !(TransportOrderShipDef.State.DURING_LOADING
						.match(transportOrderShip.getState()))) {
			// 待发航前的状态才能进行关闭（待付定金-待付信息服务费-待装货-装货中 这些状态可以关闭）
			throw new BadRequestException(ErrorCode.CODE_30137022);
		}
	}

	/**
	 * 校验完成
	 *
	 * @param id
	 */
	public void validateComplete(String id) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipValidator
				.validateExist(id);
		if (TransportOrderShipDef.DepositPayType.PAY_ZH
				.match(transportOrderShip.getPayType())) {
			// 校验卸货完成时间 和发航时间
			SetSailInfo setSailInfo = transportOrderDetailsShip
					.getSetSailInfo();
			if (Objects.isNull(setSailInfo)
					|| Objects.isNull(setSailInfo.getDepartureTime())) {
				throw new BadRequestException(ErrorCode.CODE_30137067);
			}

			UnloadingInfo unloadingInfo = transportOrderDetailsShip
					.getUnloadingInfo();
			if (Objects.isNull(unloadingInfo) || Objects
					.isNull(unloadingInfo.getUnloadingCompletionTime())) {
				throw new BadRequestException(ErrorCode.CODE_30137067);
			}
		} else {
			// 自行支付的船运单点击完成需要校验填写卸货完成时间和卸货吨位两个字段，点击完成toast：请填写卸货信息
			UnloadingInfo unloadingInfo = transportOrderDetailsShip
					.getUnloadingInfo();
			if (Objects.isNull(unloadingInfo)
					|| Objects
							.isNull(unloadingInfo.getUnloadingCompletionTime())
					|| Objects.isNull(unloadingInfo.getUnloadingTonnage())) {
				throw new BadRequestException(ErrorCode.CODE_30137068);
			}
		}
		if (!TransportOrderShipDef.State.DISCHARGED
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30137039);
		}
	}

	/**
	 * 校验链云用户是否重复
	 *
	 * @param form
	 */
	public void validateCustomerIsRepeat(TransportOrderShipForm form) {
		if (form.getOwnerId().equals(form.getCaptainId())) {
			throw new BadRequestException(ErrorCode.CODE_30137054);
		}
		// 校验平台船运需求是否已经被关联
		if (StringUtils.isNotBlank(form.getSrpId())) {
			List<TransportOrderShip> transportOrderShips = service
					.findBySrpId(form.getSrpId());
			if (CollectionUtils.isNotEmpty(transportOrderShips)) {
				throw new BadRequestException(ErrorCode.CODE_30137056);
			}
		}
	}

	/**
	 * 校验船运单是详情
	 *
	 * @param id
	 */
	public void validateFindVoById(String id) {
		TransportOrderShip transportOrderShip = validateExist(id);
		Set<Long> userIds = new HashSet<>();
		userIds.add(transportOrderShip.getCaptainId());
		userIds.add(transportOrderShip.getOwnerId());
		if (!userIds.contains(CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId())) {
			throw new BadRequestException(ErrorCode.CODE_404);
		}
	}

	/**
	 * 校验查看船运单详情
	 *
	 * @param id
	 */
	public void validateDetail(String id) {
		TransportOrderShip transportOrderShip = service.findOne(id)
				.orElse(null);
		if (Objects.isNull(transportOrderShip)) {
			throw new BadRequestException(ErrorCode.CODE_30138019);
		}
	}

	/**
	 * 校验船主选择的收款账户
	 *
	 * @param
	 * @return
	 */
	public TransportOrderShip validateBankId(String id, Long bankId) {
		// 必须是第一次付定金之前
		TransportOrderShip transportOrderShip = this.validateExist(id);
		// 银行账户信息不为空不能修改
		Long captainBankId = transportOrderShip.getCaptainBankId();
		if (Objects.nonNull(captainBankId)) {
			throw new BadRequestException(ErrorCode.CODE_30138023);
		}
		if (!TransportOrderShipDef.State.DEPOSIT_TO_BE_PAID
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30138022);
		}
		if (!CustomerContextHolder.getCustomerLoginVo().getProxyAccount()
				.getId().equals(transportOrderShip.getCaptainId())) {
			throw new BadRequestException(ErrorCode.CODE_403);
		}
		// 验证银行账户是否存在
		if (Objects.nonNull(bankId)) {
			CustomerBank customerBank = this.validateExist(bankId);
			// 如果是服务商垫付需要校验银行账户开户人和开户名称是否承运商实名
			if (TransportOrderShipDef.DepositPayType.PAY_ZH
					.match(transportOrderShip.getPayType())) {
				Long captainId = transportOrderShip.getCaptainId();
				Customer customer = customerService.findOne(captainId)
						.orElse(null);
				if (Objects.nonNull(customer)) {
					if (!customerBank.getName()
							.equals(customer.getRealName())) {
						throw new BadRequestException(ErrorCode.CODE_30138024);
					}
				}
			}
		}
		return transportOrderShip;
	}

	/**
	 * 验证是否存在
	 *
	 * @param id
	 * @return
	 */
	public CustomerBank validateExist(Long id) {
		CustomerBank customerBank = customerBankService.findOne(id)
				.orElse(null);
		if (Objects.isNull(customerBank)) {
			throw new BadRequestException(ErrorCode.CODE_30138020);
		}
		return customerBank;
	}

	/**
	 * 货主船运单被三方需求中盛关联了的船运单货主端（小程序、app、pc）暂时隐藏操作按钮，只能在后台或供应链端操作
	 * 
	 * @param id
	 */
	public void validateOwnerOperate(String id) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
		// 如果被三方关联了，就不能操作，需要提示
		if (Objects.nonNull(transportOrderShip.getAppId())) {
			throw new BadRequestException(ErrorCode.CODE_30138025);
		}
	}
}
