package com.zhihaoscm.service.resource.form.group.company;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.zhihaoscm.domain.bean.entity.GroupCompany;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "GroupCompanyForm", description = "集团管理表单")
public class GroupCompanyForm {

	@Schema(title = "集团名称")
	@Length(min = 1, max = 32, message = "集团名称不超过32个字符")
	@NotBlank(message = "集团名称不能为空")
	private String name;

	@Schema(title = "联系人")
	private String contactName;

	@Schema(title = "联系电话")
	private String contactPhone;

	@Schema(title = "集团logo文件id")
	@NotNull(message = "集团文件不能为空")
	private Long imgMainId;

	@Schema(title = "展示位置")
	@Range(min = 1, max = 8, message = "展示位置不存在")
	private Integer showLocation;

	public GroupCompany convertToEntity() {
		GroupCompany groupCompany = new GroupCompany();
		return this.updateData(groupCompany);
	}

	private GroupCompany updateData(GroupCompany groupCompany) {
		groupCompany.setName(this.getName());
		groupCompany.setContactName(this.getContactName());
		groupCompany.setContactPhone(this.getContactPhone());
		groupCompany.setImgMainId(this.getImgMainId());
		groupCompany.setShowLocation(this.getShowLocation());
		return groupCompany;
	}

	public GroupCompany convertToEntity(GroupCompany groupCompany) {
		return this.updateData(groupCompany);
	}

}
