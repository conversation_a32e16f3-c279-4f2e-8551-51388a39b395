package com.zhihaoscm.service.core.service.impl;

import java.util.*;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.entity.ShipFollow;
import com.zhihaoscm.domain.bean.entity.ShipGroup;
import com.zhihaoscm.domain.bean.vo.ShipFollowVo;
import com.zhihaoscm.domain.meta.biz.ShipGroupDef;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.core.mapper.ShipFollowMapper;
import com.zhihaoscm.service.core.service.ShipFollowService;
import com.zhihaoscm.service.core.service.ShipGroupService;
import com.zhihaoscm.service.core.service.ShipService;

/**
 * <p>
 * 船舶关注信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Service
public class ShipFollowServiceImpl
		extends MpLongIdBaseServiceImpl<ShipFollow, ShipFollowMapper>
		implements ShipFollowService {

	public ShipFollowServiceImpl(ShipFollowMapper repository) {
		super(repository);
	}

	@Autowired
	private ShipService shipService;

	@Autowired
	private ShipGroupService shipGroupService;

	@Override
	public List<ShipFollow> findByGroupIds(List<Long> groupIds, String shipId) {
		LambdaQueryWrapper<ShipFollow> wrapper = Wrappers
				.lambdaQuery(ShipFollow.class);
		this.filterDeleted(wrapper);
		wrapper.in(ShipFollow::getGroupId, groupIds);
		wrapper.eq(Objects.nonNull(shipId), ShipFollow::getShipId, shipId);
		wrapper.orderByDesc(ShipFollow::getCreatedTime);
		return this.repository.selectList(wrapper);
	}

	@Override
	public List<ShipFollowVo> findVoByGroupIds(List<Long> shipGroupIds) {
		if (CollectionUtils.isEmpty(shipGroupIds)) {
			return List.of();
		}
		List<ShipFollow> shipFollows = this.findByGroupIds(shipGroupIds, null);

		// 船舶ID集合
		List<String> idList = shipFollows.stream().map(ShipFollow::getShipId)
				.filter(StringUtils::isNotBlank).distinct().sorted().toList();

		Map<String, Ship> shipMap = shipService.getIdMap(idList);

		return shipFollows.stream().map(shipFollow -> {
			ShipFollowVo shipFollowVo = new ShipFollowVo();
			shipFollowVo.setShipFollow(shipFollow);
			if (StringUtils.isNotBlank(shipFollow.getShipId())) {
				Ship ship = shipMap.get(shipFollow.getShipId());
				if (Objects.nonNull(ship)) {
					shipFollowVo.setShip(ship);
				}
			}
			return shipFollowVo;
		}).toList();
	}

	@Override
	public Optional<ShipFollow> findByGroupIdsAndShipId(List<Long> groupIds,
			String shipId) {
		LambdaQueryWrapper<ShipFollow> wrapper = Wrappers
				.lambdaQuery(ShipFollow.class);
		this.filterDeleted(wrapper);
		wrapper.in(ShipFollow::getGroupId, groupIds);
		wrapper.eq(ShipFollow::getShipId, shipId);
		wrapper.orderByDesc(ShipFollow::getCreatedTime);
		return Optional.ofNullable(this.repository.selectOne(wrapper, false));
	}

	@Override
	public Optional<ShipFollow> findByDefaultGroupAndShipId(String shipId) {
		LambdaQueryWrapper<ShipFollow> wrapper = Wrappers
				.lambdaQuery(ShipFollow.class);
		this.filterDeleted(wrapper);
		wrapper.in(ShipFollow::getGroupId,
				Arrays.stream(ShipGroupDef.Id.values())
						.map(ShipGroupDef.Id::getCode).toList());
		wrapper.eq(ShipFollow::getShipId, shipId);
		wrapper.orderByDesc(ShipFollow::getCreatedTime);
		return Optional.ofNullable(this.repository.selectOne(wrapper, false));
	}

	@Override
	public long countByGroupId(Long groupId, Long neId) {
		LambdaQueryWrapper<ShipFollow> wrapper = Wrappers
				.lambdaQuery(ShipFollow.class);
		this.filterDeleted(wrapper);
		wrapper.eq(ShipFollow::getGroupId, groupId);
		wrapper.ne(Objects.nonNull(neId), ShipFollow::getId, neId);
		return repository.selectCount(wrapper);
	}

	@Override
	public void deleteByGroupId(Long groupId) {
		LambdaUpdateWrapper<ShipFollow> wrapper = Wrappers
				.lambdaUpdate(ShipFollow.class);
		wrapper.eq(ShipFollow::getGroupId, groupId);
		wrapper.set(ShipFollow::getDel, CommonDef.Symbol.YES.getCode());
		repository.update(wrapper);
	}

	@Override
	public void deleteByShipId(String shipId) {
		List<Long> shipGroupIds = shipGroupService
				.findByCreateBy(Objects
						.requireNonNull(UserContextHolder.getUser()).getId())
				.stream().map(ShipGroup::getId).toList();
		if (CollectionUtils.isNotEmpty(shipGroupIds)) {
			LambdaUpdateWrapper<ShipFollow> wrapper = Wrappers
					.lambdaUpdate(ShipFollow.class);
			wrapper.eq(ShipFollow::getShipId, shipId);
			wrapper.in(ShipFollow::getGroupId, shipGroupIds);
			wrapper.set(ShipFollow::getDel, CommonDef.Symbol.YES.getCode());
			repository.update(wrapper);
		}
	}
}
