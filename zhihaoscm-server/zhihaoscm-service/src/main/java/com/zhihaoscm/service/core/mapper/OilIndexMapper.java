package com.zhihaoscm.service.core.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.mapper.MpLongIdBaseMapper;
import com.zhihaoscm.domain.bean.entity.OilIndex;
import com.zhihaoscm.domain.bean.vo.OilIndexVo;

/**
 * <p>
 * 油品指数 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface OilIndexMapper extends MpLongIdBaseMapper<OilIndex> {

	/**
	 * 分页查询
	 * 
	 * @param provinceCode
	 * @param brand
	 * @return
	 */
	Page<OilIndexVo> customPaging(Page<OilIndex> page,
			@Param("provinceCode") String provinceCode,
			@Param("brand") String brand);

	/**
	 * 根据省、市、品牌查询指数
	 * 
	 * @param provinceCode
	 * @param cityCode
	 * @param brand
	 * @return
	 */
	OilIndex findByProvinceCodeAndCityCodeAndBrand(
			@Param("provinceCode") String provinceCode,
			@Param("cityCode") String cityCode, @Param("brand") String brand);

	/**
	 * 根据省市+品牌查询每天最低的价格
	 * 
	 * @param provinceCode
	 * @param cityCode
	 * @param brand
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<OilIndex> findByVersionDate(@Param("provinceCode") String provinceCode,
			@Param("cityCode") String cityCode, @Param("brand") String brand,
			@Param("beginTime") LocalDateTime beginTime,
			@Param("endTime") LocalDateTime endTime);
}
