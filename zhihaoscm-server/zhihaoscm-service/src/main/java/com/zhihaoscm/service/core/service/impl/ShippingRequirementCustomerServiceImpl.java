package com.zhihaoscm.service.core.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementCustomer;
import com.zhihaoscm.domain.bean.vo.ShippingRequirementCustomerCountVo;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementCustomerDef;
import com.zhihaoscm.service.core.mapper.ShippingRequirementCustomerMapper;
import com.zhihaoscm.service.core.service.ShippingRequirementCustomerService;

/**
 * <p>
 * 货主船运需求 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Service
public class ShippingRequirementCustomerServiceImpl extends
		MpLongIdBaseServiceImpl<ShippingRequirementCustomer, ShippingRequirementCustomerMapper>
		implements ShippingRequirementCustomerService {

	public ShippingRequirementCustomerServiceImpl(
			ShippingRequirementCustomerMapper repository) {
		super(repository);
	}

	@Override
	public ShippingRequirementCustomerCountVo statiscShippingRequirementCustomer(
			Long userId, Boolean hasFull, Boolean hasDeal) {

		ShippingRequirementCustomerCountVo shippingRequirementCustomerCountVo = new ShippingRequirementCustomerCountVo();
		LambdaQueryWrapper<ShippingRequirementCustomer> queryWrapper = Wrappers
				.lambdaQuery(ShippingRequirementCustomer.class);

		if (hasFull) {
			// 1. 统计 待指派专员统计 的数量 操作人员编号为空
			queryWrapper.eq(ShippingRequirementCustomer::getState,
					ShippingRequirementCustomerDef.PlatformState.TO_BE_ASSIGNED
							.getCode())
					.eq(ShippingRequirementCustomer::getDel,
							CommonDef.Symbol.NO.getCode())
					.isNull(ShippingRequirementCustomer::getHandlerId);
			Long assignCount = repository.selectCount(queryWrapper);
			shippingRequirementCustomerCountVo.setAssignedCount(assignCount);
		} else {
			shippingRequirementCustomerCountVo.setAssignedCount(0L);
		}

		if (hasDeal) {
			// 2. 统计 待跟进 的数量
			LambdaQueryWrapper<ShippingRequirementCustomer> countInFollowWrapper = Wrappers
					.lambdaQuery(ShippingRequirementCustomer.class);
			this.filterDeleted(countInFollowWrapper);
			List<Integer> list = Arrays.asList(
					ShippingRequirementCustomerDef.PlatformState.PENDING_PROCESSING
							.getCode(),
					ShippingRequirementCustomerDef.PlatformState.PROCESSING
							.getCode(),
					ShippingRequirementCustomerDef.PlatformState.OWNER_TO_BE_CONFIRMED
							.getCode());
			countInFollowWrapper.in(ShippingRequirementCustomer::getState,
					list);
			countInFollowWrapper.eq(Objects.nonNull(userId),
					ShippingRequirementCustomer::getHandlerId, userId);
			Long followInCustomer = repository
					.selectCount(countInFollowWrapper);
			shippingRequirementCustomerCountVo
					.setWaitFollowCount(followInCustomer);

			// 3. 统计处理中状态且关联了平台船运需求的数量
			LambdaQueryWrapper<ShippingRequirementCustomer> countInCustomerWrapper = Wrappers
					.lambdaQuery(ShippingRequirementCustomer.class);
			this.filterDeleted(countInCustomerWrapper);
			countInCustomerWrapper.eq(ShippingRequirementCustomer::getState,
					ShippingRequirementCustomerDef.PlatformState.OWNER_CONFIRMED
							.getCode());
			countInCustomerWrapper.eq(Objects.nonNull(userId),
					ShippingRequirementCustomer::getHandlerId, userId);
			Long countInCustomer = repository
					.selectCount(countInCustomerWrapper);
			shippingRequirementCustomerCountVo
					.setWaitFinishedCount(countInCustomer);

		} else {
			shippingRequirementCustomerCountVo.setWaitFollowCount(0L);
			shippingRequirementCustomerCountVo.setWaitFinishedCount(0L);
		}

		return shippingRequirementCustomerCountVo;
	}
}
