package com.zhihaoscm.service.core.service.impl;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coze.openapi.client.workflows.run.RunWorkflowResp;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.zhihaoscm.aliyun.ocr.sdk.res.IdCardBackInfo;
import com.zhihaoscm.aliyun.ocr.sdk.res.IdCardFaceInfo;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.GeoPoint;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.ExceptionUtil;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.date.DateUtils;
import com.zhihaoscm.coze.config.CozeProperties;
import com.zhihaoscm.coze.core.CozeSDK;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.annotation.MethodCallCount;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.CertificateFiles;
import com.zhihaoscm.domain.bean.json.IdCardInfo;
import com.zhihaoscm.domain.bean.json.ShippingPrice;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.AssistantsPluginDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.GeoUtils;
import com.zhihaoscm.domain.utils.ThreadPoolUtil;
import com.zhihaoscm.hkws.sdk.api.HkwsClient;
import com.zhihaoscm.hkws.sdk.entity.request.CaptureRequest;
import com.zhihaoscm.hkws.sdk.entity.response.BaseResponse;
import com.zhihaoscm.hkws.sdk.entity.response.CaptureResponse;
import com.zhihaoscm.hkws.sdk.enums.HkwsDef;
import com.zhihaoscm.hmg.sdk.enums.HmgDef;
import com.zhihaoscm.hmg.sdk.response.callback.HmgShipInfo;
import com.zhihaoscm.service.config.LocalDateTimeAdapter;
import com.zhihaoscm.service.core.mapper.ShipMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.UserService;
import com.zhihaoscm.ships66.sdk.api.ais.AisClient;
import com.zhihaoscm.ships66.sdk.enums.ShipInfoDef;
import com.zhihaoscm.ships66.sdk.response.AisInfoResponse;
import com.zhihaoscm.ships66.sdk.response.AisTrackResponse;
import com.zhihaoscm.ships66.sdk.response.SearchShipResponse;
import com.zhihaoscm.ships66.sdk.response.ShipInfo;
import com.zhihaoscm.tianditu.client.TianDiTuClient;
import com.zhihaoscm.tianditu.request.GeocodeRequest;
import com.zhihaoscm.tianditu.response.AddressComponent;
import com.zhihaoscm.tianditu.response.Geocoder;
import com.zhihaoscm.tianditu.response.Response;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 船舶 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Slf4j
@Service
public class ShipServiceImpl extends MpStringIdBaseServiceImpl<Ship, ShipMapper>
		implements ShipService {

	@Autowired
	private DeviceService deviceService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private AisClient aisClient;
	@Lazy
	@Autowired
	private ShipApplyService shipApplyService;
	@Autowired
	@Lazy
	private TransportOrderShipService transportOrderShipService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private MonitorEarlyWarnRecordService monitorEarlyWarnRecordService;
	@Autowired
	private InstallPromotionRecordService promotionRecordService;
	@Autowired
	private UserService userService;
	@Autowired
	private FileService fileService;
	@Autowired
	private ShipWaterGaugeService shipWaterGaugeService;
	@Autowired
	private HkwsClient hkwsClient;
	@Autowired
	private TianDiTuClient tianDiTuClient;
	@Lazy
	@Autowired
	private ShipService shipService;
	@Autowired
	private DeviceCapturePicRecService deviceCapturePicRecService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private ShipFollowService shipFollowService;
	@Autowired
	private ShipGroupService shipGroupService;
	@Autowired
	private ShipMonitorShareService shipMonitorShareService;
	@Autowired
	private ShippingRequirementCustomerService requirementCustomerService;
	@Autowired
	private FrequentShipRouteService frequentShipRouteService;
	@Autowired
	private PortService portService;
	@Autowired
	private ShipFollowCustomService shipFollowCustomService;
	@Autowired
	private CozeSDK cozeSDK;
	@Autowired
	private CozeProperties cozeProperties;
	@Autowired
	private OCRService ocrService;

	public ShipServiceImpl(ShipMapper repository) {
		super(repository);
	}

	/**
	 * 处理船舶名称
	 *
	 * @param input
	 *            将船舶名称字符串中除了首字和尾字之外的其他字符替换为星号（***）
	 */
	private static String maskString(String input) {
		if (StringUtils.isNotBlank(input)) {
			// 如果字符串只有一个字符，直接返回该字符加"***"
			if (input.length() == 1) {
				return input + "***";
			} else {
				// 保留首尾字符，中间用"***"替换
				char firstChar = input.charAt(0);
				char lastChar = input.charAt(input.length() - 1);
				return firstChar + "***" + lastChar;
			}
		} else {
			return input;
		}
	}

	@Override
	public Page<ShipVo> paging(Integer page, Integer size, String sortKey,
			String sortOrder, String param, String contactMaintainer,
			Long carrier, List<Integer> state, Integer relatedDevice,
			Integer warehouseState, Integer existWaterGauge,
			Integer existMobile) {

		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode());

		if (Objects.nonNull(warehouseState)) {
			queryWrapper.eq(Ship::getExistDevice,
					CommonDef.Symbol.YES.getCode());
			queryWrapper.eq(Ship::getWarehouseState, warehouseState);
		}

		if (StringUtils.isNotBlank(param)) {
			queryWrapper.and(i -> i.eq(Ship::getId, param).or()
					.like(Ship::getName, param).or()
					.like(Ship::getCnname, param));
		}

		// 是否关联设备
		if (Objects.nonNull(relatedDevice)) {
			switch (ShipDef.RelatedDevice.from(relatedDevice)) {
				case YES -> queryWrapper.eq(Ship::getExistDevice,
						CommonDef.Symbol.YES.getCode());

				case NO -> queryWrapper.eq(Ship::getExistDevice,
						CommonDef.Symbol.NO.getCode());
			}
		}
		queryWrapper.eq(Objects.nonNull(carrier), Ship::getCarrier, carrier);
		queryWrapper.like(StringUtils.isNotBlank(contactMaintainer),
				Ship::getContactMaintainer, contactMaintainer);
		queryWrapper.in(CollectionUtils.isNotEmpty(state), Ship::getState,
				state);
		queryWrapper.eq(Objects.nonNull(existWaterGauge),
				Ship::getExistWaterGauge, existWaterGauge);
		queryWrapper.eq(Objects.nonNull(existMobile), Ship::getExistMobile,
				existMobile);

		queryWrapper.orderBy(Boolean.TRUE, Boolean.FALSE, Ship::getExistDevice);
		queryWrapper.orderBy(Boolean.TRUE, Boolean.FALSE, Ship::getExistMobile);
		queryWrapper.orderBy(Boolean.TRUE, Boolean.FALSE,
				Ship::getExistWaterGauge);
		queryWrapper.orderBy(Boolean.TRUE, Boolean.FALSE, Ship::getState);
		queryWrapper.orderBy(Boolean.TRUE, Boolean.TRUE,
				Ship::getWarehouseState);
		queryWrapper.orderBy(Boolean.TRUE, Boolean.FALSE, Ship::getCreatedTime);

		Page<Ship> shipPage = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		List<Ship> ships = shipPage.getRecords();
		if (CollectionUtils.isEmpty(ships)) {
			return PageUtil.getRecordsInfoPage(shipPage, List.of());
		} else {
			// 获取船舶的id列表
			List<String> ids = ships.stream().map(Ship::getId).toList();
			// 根据船舶id列表查询船舶设备列表
			List<Device> devices = deviceService.findByMasterIds(ids,
					DeviceDef.TransportType.SHIP.getCode());
			// 根据船舶id将船舶设备列表分组
			Map<String, List<Device>> deviceMap = devices.stream()
					.collect(Collectors.groupingBy(Device::getMasterId));
			// 根据客户id查询客户信息
			List<Long> customerIds = ships.stream().map(Ship::getCarrier)
					.distinct().toList();
			Map<Long, Customer> customerMap = customerService
					.findByIds(customerIds).stream().collect(Collectors
							.toMap(Customer::getId, Function.identity()));
			// 封装船舶Vo列表
			return PageUtil.getRecordsInfoPage(shipPage,
					ships.stream().map(ship -> {
						ShipVo vo = new ShipVo();
						vo.setShip(ship);
						vo.setDevices(deviceMap.get(ship.getId()));
						vo.setExistDevice(ship.getExistDevice());
						vo.setExistMobile(ship.getExistMobile());
						vo.setCustomer(customerMap.get(ship.getCarrier()));
						vo.setExistWaterGauge(ship.getExistWaterGauge());

						if (Objects.isNull(ship.getExistDevice())
								|| CommonDef.Symbol.NO
										.match(ship.getExistDevice())) {
							ship.setWarehouseState(null);
						}
						return vo;
					}).toList());
		}
	}

	@Override
	public Page<ShipVo> customPaging(Integer page, Integer size, Long carrier) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode());

		queryWrapper.eq(Objects.nonNull(carrier), Ship::getCarrier, carrier);

		// 默认按照创建时间降序排列
		queryWrapper.orderByDesc(Ship::getCreatedTime);

		Page<Ship> shipPage = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(shipPage,
				this.packVo(shipPage.getRecords(), List.of()));
	}

	@Override
	public Page<Ship> selector(Integer page, Integer size, Integer state,
			String keyword) {

		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Objects.nonNull(state), Ship::getState, state);
		if (StringUtils.isNotBlank(keyword)) {
			queryWrapper.and(i -> i.eq(Ship::getId, keyword).or()
					.like(Ship::getName, keyword).or()
					.like(Ship::getCnname, keyword).or()
					.eq(Ship::getId, keyword));
		}
		queryWrapper.eq(Ship::getDel, CommonDef.Symbol.NO.getCode());
		return repository.selectPage(new Page<>(page, size), queryWrapper);
	}

	@Override
	public Page<ShipVo> selectorVo(Integer page, Integer size, Integer state,
			String keyword) {

		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Objects.nonNull(state), Ship::getState, state);
		if (StringUtils.isNotBlank(keyword)) {
			queryWrapper.and(i -> i.eq(Ship::getId, keyword).or()
					.like(Ship::getName, keyword).or()
					.like(Ship::getCnname, keyword));
		}
		Page<Ship> shipPage = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(shipPage,
				this.packVo(shipPage.getRecords(), List.of()));
	}

	@Override
	public Page<ShipVo> installSelector(Integer page, Integer size,
			String searchParam) {
		// 获取所有已绑定设备的船舶id
		List<Device> devices = deviceService.findByMasterIdAndTransportType(
				StringUtils.EMPTY, DeviceDef.TransportType.SHIP.getCode());
		List<String> shipIds = new ArrayList<>(
				devices.stream().map(Device::getMasterId)
						.filter(StringUtils::isNotBlank).toList());
		// 过滤已推广的船舶
		List<InstallPromotionRecord> records = promotionRecordService
				.findByShipIds(shipIds,
						InstallPromotionRecordDef.State.ACTIVATED.getCode());
		List<String> installShipIds = records.stream()
				.map(InstallPromotionRecord::getShipId).distinct().toList();
		shipIds.removeIf(installShipIds::contains);
		// 根据已过滤完毕的船舶id查询
		if (CollectionUtils.isEmpty(shipIds)) {
			return new Page<>();
		}
		LambdaQueryWrapper<Ship> wrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getState, ShipDef.State.AUTHENTICATED.getCode())
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode())
				.in(Ship::getId, shipIds)
				.and(StringUtils.isNotBlank(searchParam),
						w -> w.like(Ship::getName, searchParam).or()
								.like(Ship::getCnname, searchParam));
		Page<Ship> shipPage = repository.selectPage(new Page<>(page, size),
				wrapper);
		return PageUtil.getRecordsInfoPage(shipPage,
				this.packVo(shipPage.getRecords(), List.of()));
	}

	@Override
	public Optional<Ship> carrierSelector(String id) {
		Optional<Ship> optional = this.findOne(id);
		if (optional.isPresent()) {
			Ship ship = optional.get();
			if (ShipDef.State.UNAUTHENTICATED.match(ship.getState())) {
				List<ShipApply> shipApplyList = shipApplyService
						.findByShipId(id);
				long count = shipApplyList.stream()
						.filter(item -> !item.getState()
								.equals(ShipApplyDef.State.REJECTED.getCode()))
						.count();
				if (count > 0) {
					ship.setState(ShipDef.State.AUTHENTICATING.getCode());
				}
			}
			return Optional.of(ship);
		}
		Ship ship = getAisInfo(id);
		super.create(ship);
		return Optional.of(ship);
	}

	@Override
	public Optional<ShipVo> findVoById(String id) {
		return this.findOne(id).map(this::packVo);
	}

	@Override
	public List<ShipVo> findVoByKeyword(String keyword) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers
				.lambdaQuery(Ship.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.and(x -> x.like(Ship::getName, keyword).or()
				.like(Ship::getCnname, keyword).or().eq(Ship::getId, keyword));
		return this.packVo(repository.selectList(queryWrapper));
	}

	@Override
	public List<Ship> findByCname(String cname) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers
				.lambdaQuery(Ship.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.and(x -> x.like(Ship::getCnname, cname));
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Ship> findByIdsNoDeleted(List<String> ids) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers
				.lambdaQuery(Ship.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.in(Ship::getId, ids);
		return repository.selectList(queryWrapper);
	}

	@Override
	public Long countByState(Integer state, LocalDateTime beginTime,
			LocalDateTime endTime, Integer relatedDevice,
			Integer existWaterGauge) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Objects.nonNull(state), Ship::getState, state);
		queryWrapper.ge(Objects.nonNull(beginTime), Ship::getCreatedTime,
				beginTime);
		queryWrapper.le(Objects.nonNull(endTime), Ship::getCreatedTime,
				endTime);

		// 是否关联设备
		if (Objects.nonNull(relatedDevice)) {
			// 获取绑定设备的船舶的id列表
			List<String> shipIdList = deviceService
					.findMasterIds(DeviceDef.TransportType.SHIP.getCode());

			switch (ShipDef.RelatedDevice.from(relatedDevice)) {
				case YES ->
					queryWrapper.in(CollectionUtils.isNotEmpty(shipIdList),
							Ship::getId, shipIdList);
				case NO ->
					queryWrapper.notIn(CollectionUtils.isNotEmpty(shipIdList),
							Ship::getId, shipIdList);
			}
		}

		if (Objects.nonNull(existWaterGauge)) {
			List<String> shipIdList = shipWaterGaugeService.findAll().stream()
					.map(ShipWaterGauge::getShipId)
					.filter(StringUtils::isNotBlank).toList();
			switch (CommonDef.Symbol.from(existWaterGauge)) {
				case YES ->
					queryWrapper.in(CollectionUtils.isNotEmpty(shipIdList),
							Ship::getId, shipIdList);
				case NO ->
					queryWrapper.notIn(CollectionUtils.isNotEmpty(shipIdList),
							Ship::getId, shipIdList);
			}
		}
		return repository.selectCount(queryWrapper);
	}

	@Override
	public List<Ship> findByState(Integer state) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Objects.nonNull(state), Ship::getState, state);
		return repository.selectList(queryWrapper);
	}

	@Override
	@MethodCallCount
	@Cacheable(cacheNames = RedisKeys.Cache.SHIP_ASI_BATCH
			+ "&", cacheManager = "SHIP_REDIS_CACHE_MANAGER_NAME", key = "@hashUtils.md5(#ids)", unless = "#result == null or #result.isEmpty()")
	public List<ShipInfo> findByBatchAsi(String ids) {
		AisInfoResponse aisInfo;
		try {
			aisInfo = aisClient.getAisInfo(ids, "SHIP");
		} catch (Exception e) {
			log.error("查询船舶信息异常: {}", e.getMessage(), e);
			throw new BadRequestException(ErrorCode.CODE_30002016);
		}
		if (Objects.isNull(aisInfo) || Objects.isNull(aisInfo.getData())) {
			return null;
		}
		List<ShipInfo> data = aisInfo.getData();
		if (CollectionUtils.isEmpty(data)) {
			return null;
		}
		for (ShipInfo datum : data) {
			this.handelLocation(datum);
		}
		return data;
	}

	@Override
	public List<ShipInfo> findShipInfoByPortId(Long portId, Integer area) {
		Port port = portService.findOne(portId).orElse(null);
		if (Objects.nonNull(port)) {
			// 码头的经纬度
			String latLon = port.getLatLon();
			if (StringUtils.isNotBlank(latLon)) {
				List<GeoPoint> geoPoints = GeoUtils
						.calculateRectangleCorners(latLon, area);
				if (CollectionUtils.isNotEmpty(geoPoints)
						&& geoPoints.size() >= 4) {
					return SpringUtil.getBean(ShipService.class)
							.findShipInfoByExtent(geoPoints.get(0),
									geoPoints.get(1), geoPoints.get(2),
									geoPoints.get(3), "SHIP", null);
				}
			}
			return List.of();
		}
		return List.of();
	}

	@Override
	public List<OnlineShipVo> findByOnlineShip() {
		// 在线设备
		List<Device> onlineDevice = deviceService
				.findByOnline(DeviceDef.Online.ONLINE.getCode());
		if (CollectionUtils.isEmpty(onlineDevice)) {
			return List.of();
		}
		// 查询船舶
		List<String> shipIdList = onlineDevice.stream()
				.filter(item -> DeviceDef.TransportType.SHIP
						.match(item.getTransportType()))
				.map(Device::getMasterId).filter(StringUtils::isNotBlank)
				.toList();
		Map<String, Device> deviceMap = onlineDevice.stream()
				.filter(item -> StringUtils.isNotBlank(item.getMasterId()))
				.collect(Collectors.toMap(Device::getMasterId, t -> t));
		return super.findByIdsNoDeleted(shipIdList).stream().map(item -> {
			OnlineShipVo onlineShipVo = new OnlineShipVo();
			BeanUtils.copyProperties(item, onlineShipVo);
			Device device = deviceMap.get(item.getId());
			if (Objects.nonNull(device)) {
				onlineShipVo.setDeviceSerial(device.getSerialNo());
			}
			return onlineShipVo;
		}).toList();
	}

	@Override
	public List<ShipInfoVo> findByExtent(List<ShipInfo> data) {
		Gson GSON = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();

		if (CollectionUtils.isEmpty(data)) {
			return List.of();
		}

		List<ShipInfoVo> shipInfoVoList = this.handelShipScope(data, GSON);

		// 传顺船舶ID集合
		Set<String> shipIds = data.stream().map(ShipInfo::getId)
				.collect(Collectors.toSet());

		// 查询数据库数据
		List<Ship> shipList = super.findByIds(shipIds);
		Set<String> shipIdList = shipList.stream().map(Ship::getId)
				.collect(Collectors.toSet());
		if (!data.isEmpty()) {
			List<String> idList = new ArrayList<>(
					data.stream().map(ShipInfo::getId).toList());
			// 过滤掉已存在的船舶
			idList.removeAll(shipIdList);
			if (!idList.isEmpty()) {
				// 异步进行船舶入库
				ThreadPoolUtil.submitTask(
						() -> SpringUtil.getBean(ShipService.class)
								.asyncBathCreate(idList),
						ThreadPoolUtil.getShipExecutor());
			}

		}

		// 使用数据库中数据
		return this.handelShipData(shipInfoVoList, shipList);

	}

	@MethodCallCount(method = MethodCallCountDef.EXTENT2)
	@Override
	public List<ShipInfoVo> findByCustomExtent(List<GeoPoint> geoPointList,
			String type, Long time) {
		Gson GSON = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
		StringBuilder extent = new StringBuilder("MULTIPOLYGON(((");
		int currentIndex = 0;
		for (GeoPoint geoPoint : geoPointList) {
			extent.append(geoPoint.getLng()).append(" ")
					.append(geoPoint.getLat());
			// 只有在当前点不是最后一个点时，才拼接 ","
			if (currentIndex < geoPointList.size() - 1) {
				extent.append(",");
			}
			currentIndex++;
		}
		extent.append(")))");
		String extentString = extent.toString();

		if (StringUtils.isBlank(type)) {
			type = "SHIP";
		}
		AisInfoResponse response;
		try {
			response = aisClient.getExtent2(extentString, type, time);
		} catch (Exception e) {
			log.error("自定义查船异常: {}", e.getMessage(), e);
			return List.of();
		}
		if (response == null || Objects.isNull(response.getData())) {
			return List.of();
		}
		List<ShipInfo> data = response.getData();
		if (CollectionUtils.isEmpty(data)) {
			return List.of();
		}

		List<ShipInfoVo> shipInfoVoList = this.handelShipScope(data, GSON);

		List<Ship> shipList = super.findAll();
		// 使用数据库中数据
		return this.handelShipData(shipInfoVoList, shipList);
	}

	@Override
	public List<ShipInfoVo> findByExtentAndScope(List<ShipInfo> data) {

		if (CollectionUtils.isEmpty(data)) {
			return List.of();
		}
		List<ShipInfoVo> shipInfoVoList = new ArrayList<>();

		return this.handleScopeShip(data, shipInfoVoList);
	}

	@MethodCallCount(method = MethodCallCountDef.AIS_TRACK)
	@Override
	@Cacheable(cacheNames = RedisKeys.Cache.SHIP_ASI_TRACK
			+ "&", cacheManager = "SHIP_REDIS_CACHE_MANAGER_NAME", key = "#mmsi +':'+ #start +':'+ #end", unless = "#result == null or #result.isEmpty()")
	public List<ShipTrajectoryVo> findByAisTrack(String mmsi, Long start,
			Long end) {
		AisTrackResponse response;
		try {
			response = aisClient.getAisTrack(mmsi, start, end);
		} catch (Exception e) {
			log.error("查询船舶轨迹异常: {}", e.getMessage(), e);
			return List.of();
		}
		if (response.getData() == null || response.getData().isEmpty()) {
			return List.of();
		}
		return new ArrayList<>(response.getData().stream()
				.flatMap(data -> data.getTrajectory().stream()).map(x -> {
					ShipTrajectoryVo shipTrajectoryVo = new ShipTrajectoryVo();
					BeanUtils.copyProperties(x, shipTrajectoryVo);
					return shipTrajectoryVo;
				}).toList());
	}

	@MethodCallCount(method = MethodCallCountDef.SEARCH_SHIP)
	@Override
	public List<Ship> findByKeyword(String keyword, Integer limit,
			String shipType) {
		SearchShipResponse response;
		try {
			response = aisClient.searchShip(keyword, limit, shipType);
		} catch (Exception e) {
			log.error("根据关键字查询船舶信息异常: {}", e.getMessage(), e);
			throw new BadRequestException(ErrorCode.CODE_30002018);
		}
		if (Objects.isNull(response) || Objects.isNull(response.getData())) {
			return List.of();
		}
		List<ShipInfo> data = response.getData();
		// 查询数据库是否存在船舶
		List<String> shipIds = data.stream().map(ShipInfo::getId).distinct()
				.toList();
		// 根据id进行分组
		Map<String, Ship> shipMap = super.buildIdMap(super.findByIds(shipIds));
		List<Ship> shipList = new ArrayList<>();
		for (ShipInfo info : data) {
			Ship ship = new Ship();
			ship.setId(info.getId());
			ship.setName(info.getName());
			ship.setCnname(info.getCnname());
			this.handelShipType(info, ship);
			// 数据存在 则取数据的船舶类型
			Ship existShip = shipMap.get(info.getId());
			if (Objects.nonNull(existShip)) {
				ship.setType(existShip.getType());
				ship.setTonPure(existShip.getTonPure());
				ship.setTonCapacity(existShip.getTonCapacity());
				ship.setBulkCargoShipType(existShip.getBulkCargoShipType());
				ship.setType(existShip.getType());
				ship.setName(existShip.getName());
				ship.setCnname(existShip.getCnname());
				if (Objects.nonNull(existShip.getLength())) {
					ship.setLength(existShip.getLength());
				}
				if (Objects.nonNull(existShip.getWidth())) {
					ship.setWidth(existShip.getWidth());
				}
			}
			shipList.add(ship);
		}
		return shipList;

	}

	@Override
	public List<Ship> findByKeyword(String keyword) {
		LambdaQueryWrapper<Ship> wrapper = Wrappers.lambdaQuery(Ship.class);
		this.filterDeleted(wrapper);
		wrapper.and(x -> x.like(Ship::getName, keyword).or()
				.like(Ship::getCnname, keyword).or().eq(Ship::getId, keyword));
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<ShipVo> findByIdAndValidate(String id) {
		Optional<Ship> optional = this.findOne(id);
		if (optional.isPresent()) {
			Ship existShip = optional.get();
			ShipVo vo = new ShipVo();
			vo.setShip(existShip);
			vo.setExist(true);
			return Optional.of(vo);
		}
		Ship ship = getAisInfo(id);
		ShipVo vo = new ShipVo();
		vo.setShip(ship);
		vo.setExist(false);
		return Optional.of(vo);
	}

	@Override
	public List<Ship> findByCarrier(Long carrier, String keyword) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Ship::getCarrier, carrier);
		queryWrapper.eq(Ship::getState, ShipDef.State.AUTHENTICATED.getCode());
		if (StringUtils.isNotBlank(keyword)) {
			queryWrapper.and(i -> i.eq(Ship::getId, keyword).or()
					.like(Ship::getName, keyword).or()
					.like(Ship::getCnname, keyword));
		}
		// 默认按照创建时间降序排列
		queryWrapper.orderByDesc(Ship::getAuthenticationTime);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Ship> findByCarrierAndNoReportFile(Long carrierId) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Ship::getCarrier, carrierId);
		// 已认证
		queryWrapper.eq(Ship::getState, ShipDef.State.AUTHENTICATED.getCode());
		// 没有上传内河船舶检验报告
		queryWrapper.apply("(certificate_files IS NULL "
				+ "OR JSON_UNQUOTE(JSON_EXTRACT(certificate_files, '$.shipInspectionReportFile')) IS NULL "
				+ "OR JSON_UNQUOTE(JSON_EXTRACT(certificate_files, '$.shipInspectionReportFile')) = 'null')");
		// 认证倒序
		// 默认按照创建时间降序排列
		queryWrapper.orderByDesc(Ship::getAuthenticationTime);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Ship> findByCarrier(Long carrier) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Ship::getCarrier, carrier);
		queryWrapper.eq(Ship::getState, ShipDef.State.AUTHENTICATED.getCode());
		// 默认按照船舶表的创建时间降序排列
		queryWrapper.orderByDesc(Ship::getCreatedTime);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Ship> findByCarrierRelatedDevice(Long carrier) {
		// 需要返回的结果
		List<Ship> shipList = new ArrayList<>();
		// 查询出自己已认证的船舶列表
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Ship::getCarrier, carrier);
		queryWrapper.eq(Ship::getState, ShipDef.State.AUTHENTICATED.getCode());
		List<Ship> ships = repository.selectList(queryWrapper);
		// 将自己已认证的船舶id放入map
		Map<String, Ship> shipMap = new LinkedHashMap<>();
		for (Ship ship : ships) {
			shipMap.put(ship.getId(), ship);
		}
		// 获取已绑定设备的船舶id列表(按设备表的创建时间倒序)
		List<String> shipIds = deviceService
				.findByTransportType(DeviceDef.TransportType.SHIP.getCode())
				.stream().map(Device::getMasterId)
				.filter(StringUtils::isNotBlank).distinct().toList();
		if (CollectionUtils.isNotEmpty(shipIds)) {
			// 循环已绑定设备的船舶id列表（因为需要根据设备表的创建时间倒序）
			for (String shipId : shipIds) {
				// 直接通过 ID 从 Map 中获取 ship 对象
				Ship ship = shipMap.get(shipId);
				if (ship != null) {
					shipList.add(ship);
				}
			}
			return shipList;
		} else {
			return Collections.emptyList();
		}
	}

	@Override
	public Optional<ShipInfoVo> findByIdAndCreate(ShipInfo shipInfo,
			Long userId, Integer monitoringChannels) {
		if (Objects.isNull(shipInfo)) {
			return Optional.empty();
		}
		ShipInfoVo shipInfoVo = new ShipInfoVo();
		BeanUtils.copyProperties(shipInfo, shipInfoVo);
		// 类型处理
		this.handelShipType(shipInfo, shipInfoVo);
		super.findOne(shipInfoVo.getId())
				.ifPresent(ship -> this.handelShipData(shipInfoVo, ship));

		// 有分数的船舶信息
		String shipScope = redisClient.get(RedisKeys.Cache.SHIP_SCOPE);
		if (StringUtils.isNotBlank(shipScope)) {
			// 有分数的船舶集合
			Gson GSON = new GsonBuilder().registerTypeAdapter(
					LocalDateTime.class, new LocalDateTimeAdapter()).create();
			Type shipScopeListType = new TypeToken<List<ShipScope>>() {
			}.getType();
			List<ShipScope> shipScopeList = GSON.fromJson(shipScope,
					shipScopeListType);
			if (CollectionUtils.isNotEmpty(shipScopeList)) {
				// 转map
				Map<String, ShipScope> shipScopeMap = shipScopeList.stream()
						.collect(Collectors.toMap(ShipScope::getId, t -> t));
				if (Objects.nonNull(shipScopeMap.get(shipInfo.getId()))) {
					ShipScope scope = shipScopeMap.get(shipInfo.getId());
					BeanUtils.copyProperties(scope, shipInfoVo);
				} else {
					shipInfoVo.setScope(0);
					shipInfoVo.setExistDevice(CommonDef.Symbol.NO.getCode());
					shipInfoVo.setExistMobile(CommonDef.Symbol.NO.getCode());
					shipInfoVo
							.setExistWaterGauge(CommonDef.Symbol.NO.getCode());
				}
			}
		}

		// 查询当前登录人是否存在关注的船
		if (Objects.nonNull(userId)) {
			switch (ShipDef.MonitoringChannels.from(monitoringChannels)) {
				// 管理后台
				case BACKGROUND -> {
					List<ShipGroup> shipGroupList = shipGroupService
							.findByCreateBy(userId);
					List<Long> shipGroupIds = shipGroupList.stream()
							.map(ShipGroup::getId).distinct().toList();
					if (CollectionUtils.isNotEmpty(shipGroupIds)) {
						List<ShipFollow> shipFollows = shipFollowService
								.findByGroupIds(shipGroupIds, shipInfo.getId());
						if (CollectionUtils.isNotEmpty(shipFollows)) {
							shipInfoVo.setIsFollowed(
									CommonDef.Symbol.YES.getCode());
						} else {
							shipInfoVo.setIsFollowed(
									CommonDef.Symbol.NO.getCode());
						}
					}
				}
				// 小程序
				case MINI_PROGRAM -> {
					Optional<ShipFollowCustom> optional = shipFollowCustomService
							.findByCreatedByAndShipId(userId, shipInfo.getId());
					if (optional.isEmpty()) {
						shipInfoVo.setIsFollowed(CommonDef.Symbol.NO.getCode());
					} else {
						shipInfoVo
								.setIsFollowed(CommonDef.Symbol.YES.getCode());

					}
				}

			}

		}
		// 异步进行船舶入库
		this.asyncCreate(shipInfo);
		return Optional.of(shipInfoVo);
	}

	@MethodCallCount
	@Override
	@Cacheable(cacheNames = RedisKeys.Cache.SHIP_ASI
			+ "&", cacheManager = "SHIP_REDIS_CACHE_MANAGER_NAME", key = "#id", unless = "#result == null")
	public ShipInfo findByAsi(String id) {
		AisInfoResponse aisInfo;
		try {
			aisInfo = aisClient.getAisInfo(id, "SHIP");
		} catch (Exception e) {
			log.error("查询船舶信息异常: {}", e.getMessage(), e);
			throw new BadRequestException(ErrorCode.CODE_30002016);
		}
		if (Objects.isNull(aisInfo) || Objects.isNull(aisInfo.getData())) {
			return null;
		}
		List<ShipInfo> data = aisInfo.getData();
		if (CollectionUtils.isEmpty(data)) {
			return null;
		}
		ShipInfo shipInfo = data.stream().findFirst().orElse(null);
		if (Objects.nonNull(shipInfo)) {
			this.handelLocation(shipInfo);
		}
		return shipInfo;
	}

	@Override
	public List<ShipHistoryVo> findHistory(Long customerId,
			Integer monitoringChannels) {
		Set<ZSetOperations.TypedTuple<String>> tuples = redisClient
				.zReverseRangeWithScores(RedisKeys.Cache.SHIP_HISTORY
						+ monitoringChannels + ":" + customerId, 0, -1);
		List<String> values = tuples.stream()
				.map(ZSetOperations.TypedTuple::getValue).toList();
		List<ShipHistoryVo> shipList = new ArrayList<>();
		for (String value : values) {
			ShipHistoryVo ship = JsonUtils.jsonToObject(value,
					ShipHistoryVo.class);
			shipList.add(ship);
		}
		return shipList;
	}

	@Override
	public List<ShipPortHistoryVo> findShipPortHistory(Long customerId,
			Integer monitoringChannels) {
		Set<ZSetOperations.TypedTuple<String>> tuples = redisClient
				.zReverseRangeWithScores(RedisKeys.Cache.SHIP_PORT_HISTORY
						+ monitoringChannels + ":" + customerId, 0, -1);
		List<String> values = tuples.stream()
				.map(ZSetOperations.TypedTuple::getValue).toList();
		List<ShipPortHistoryVo> shipList = new ArrayList<>();
		for (String value : values) {
			ShipPortHistoryVo ship = JsonUtils.jsonToObject(value,
					ShipPortHistoryVo.class);
			shipList.add(ship);
		}
		return shipList;
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<TransportOrderShip> findOrderShip(
			Integer scope, LocalDateTime beginTime, LocalDateTime endTime,
			String shipId, Integer page, Integer size) {
		// 计算查询范围
		if (Objects.isNull(beginTime) || Objects.isNull(endTime)) {
			if (TransportOrderShipDef.Scope.LATEST_ONE_MONTH.match(scope)) {
				beginTime = LocalDateTime.now().with(LocalTime.MIN)
						.minusMonths(1).plusDays(1);
			} else if (TransportOrderShipDef.Scope.LATEST_THREE_MONTH
					.match(scope)) {
				beginTime = LocalDateTime.now().with(LocalTime.MIN)
						.minusMonths(3).plusDays(1);
			} else if (TransportOrderShipDef.Scope.LATEST_HALF_YEAR
					.match(scope)) {
				beginTime = LocalDateTime.now().with(LocalTime.MIN)
						.minusMonths(6).plusDays(1);
			} else if (TransportOrderShipDef.Scope.LATEST_ONE_YEAR
					.match(scope)) {
				beginTime = LocalDateTime.now().with(LocalTime.MIN)
						.minusYears(1).plusDays(1);
			}
			endTime = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
		}
		List<TransportOrderShip> allShips = transportOrderShipService
				.findByTime(beginTime, endTime, shipId);
		return this.handelPage(page, size, allShips);
	}

	@Override
	public List<Ship> findByShipCnName(String shipCnName) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers
				.lambdaQuery(Ship.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Ship::getCnname, shipCnName);
		return repository.selectList(queryWrapper);
	}

	/**
	 * 调用区域查船
	 *
	 * @param leftTopPoint
	 * @param rightTopPoint
	 * @param rightBottomPoint
	 * @param leftBottomPoint
	 * @param type
	 * @param time
	 * @return
	 */
	@MethodCallCount(method = MethodCallCountDef.EXTENT2)
	@Override
	@Cacheable(cacheNames = RedisKeys.Cache.SHIP_EXTENT
			+ "&", cacheManager = "SHIP_REDIS_CACHE_MANAGER_NAME", key = "#leftTopPoint.toString() +':'+ #rightTopPoint.toString() +':'+#rightBottomPoint.toString() +':'+ #leftBottomPoint.toString()", unless = "#result == null or #result.isEmpty()")
	public List<ShipInfo> findShipInfoByExtent(GeoPoint leftTopPoint,
			GeoPoint rightTopPoint, GeoPoint rightBottomPoint,
			GeoPoint leftBottomPoint, String type, Long time) {
		String extent = "MULTIPOLYGON(((" + leftTopPoint.getLng() + " "
				+ leftTopPoint.getLat() + "," + rightTopPoint.getLng() + " "
				+ rightTopPoint.getLat() + "," + rightBottomPoint.getLng() + " "
				+ rightBottomPoint.getLat() + "," + leftBottomPoint.getLng()
				+ " " + leftBottomPoint.getLat() + "," + leftTopPoint.getLng()
				+ " " + leftTopPoint.getLat() + ")))";
		if (StringUtils.isBlank(type)) {
			type = "SHIP";
		}
		AisInfoResponse response;
		try {
			response = aisClient.getExtent2(extent, type, time);
		} catch (Exception e) {
			log.error("区域查船异常: {}", e.getMessage(), e);
			return null;
		}
		if (response == null || Objects.isNull(response.getData())) {
			return List.of();
		}
		return response.getData();
	}

	@Override
	public List<ShipVo> findVoByIds(List<String> ids) {
		List<Ship> shipList = super.findByIds(ids);
		if (CollectionUtils.isEmpty(shipList)) {
			return List.of();
		}
		List<Device> deviceList = deviceService.findByMasterIds(ids,
				DeviceDef.TransportType.SHIP.getCode());
		Map<String, Device> deviceMap = deviceList.stream()
				.collect(Collectors.toMap(Device::getMasterId, d -> d));

		Map<String, List<Device>> devicesMap = deviceList.stream()
				.collect(Collectors.groupingBy(Device::getMasterId));

		// 客户ID集合
		List<Long> customerIds = shipList.stream().map(Ship::getCarrier)
				.filter(Objects::nonNull).distinct().toList();
		// 客户信息
		Map<Long, Customer> customerMap = customerService.findByIds(customerIds)
				.stream().collect(Collectors.toMap(Customer::getId, c -> c));
		List<ShipVo> shipVos = new ArrayList<>();
		for (Ship ship : shipList) {
			ShipVo shipVo = new ShipVo();
			shipVo.setShip(ship);
			Device device = deviceMap.get(ship.getId());
			if (Objects.nonNull(ship.getCarrier())) {
				Customer customer = customerMap.get(ship.getCarrier());
				shipVo.setCustomer(customer);
			}
			if (CollectionUtils.isNotEmpty(devicesMap.get(ship.getId()))) {
				shipVo.setDevices(devicesMap.get(ship.getId()));
			} else {
				shipVo.setDevices(List.of());
			}
			shipVo.setDevice(device);
			shipVos.add(shipVo);
		}
		return shipVos;
	}

	@Override
	public List<Ship> selectShips(Long carrier, String shipId,
			Long tonCapacityMin, Long tonCapacityMax) {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode());
		if (Objects.nonNull(carrier)) {
			queryWrapper.eq(Ship::getCarrier, carrier);
		}
		// 船舶中英文模糊搜索、MMSI号唯一搜索
		if (StringUtils.isNotBlank(shipId)) {
			queryWrapper.and(i -> i.eq(Ship::getId, shipId).or()
					.like(Ship::getName, shipId).or()
					.like(Ship::getCnname, shipId));
		}
		queryWrapper.ge(Objects.nonNull(tonCapacityMin), Ship::getTonCapacity,
				tonCapacityMin);
		queryWrapper.le(Objects.nonNull(tonCapacityMax), Ship::getTonCapacity,
				tonCapacityMax);
		queryWrapper.and(wrapper -> wrapper.isNotNull(Ship::getMobile)
				.ne(Ship::getMobile, ""));
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Ship> selectCarrierExists() {
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.and(wrapper -> wrapper.isNotNull(Ship::getCarrier));
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<ShipSmartVo> findByShipCustom(Long id,
			Integer monitoringChannels) {
		List<ShipSmartVo> shipSmartVos = new ArrayList<>();
		List<ShipSmartVo> finalShipSmartVos = new ArrayList<>();
		ShippingRequirementCustomer shippingRequirementCustomer = requirementCustomerService
				.findOne(id).orElse(null);
		if (Objects.nonNull(shippingRequirementCustomer)) {
			// 查询出所有船舶中联系方式不为空的数据
			List<Ship> shipList = this.selectShips(null, null, null, null);
			// 获取有联系方式，没有船运单，如有船运单只在在卸货中、已卸货、已清仓、已完成状态的船舶
			List<Ship> combinedList = this.getCombinedList(shipList);
			// 查询出所有常跑航线中始发地城市编码和目的地二级地址不为空的数据
			List<FrequentShipRoute> frequentShipRouteList = frequentShipRouteService
					.findAll().stream()
					.filter(route -> Objects.nonNull(route.getSourceCityCode())
							&& Objects.nonNull(route.getDestinationCityCode()))
					.toList();
			// 查询出所有码头信息，将码头id为key,port对象为value存入map
			Map<Long, Port> portMap = portService.findAll().stream()
					.collect(Collectors.toMap(Port::getId, port -> port,
							(existing, replacement) -> existing));
			// 查询出所有船运单信息，将船舶id为key,船运单对象为value存入map
			// 查询出所有船运单信息，根据船舶id进行分组,然后按时间倒序排序 船运单对象为value存入map
			Map<String, List<TransportOrderShip>> transportOrderShipMap = transportOrderShipService
					.findAll().stream()
					.filter(transportOrderShip -> StringUtils
							.isNotBlank(transportOrderShip.getShipId()))
					.collect(Collectors.groupingBy(
							TransportOrderShip::getShipId,
							Collectors.collectingAndThen(Collectors.toList(),
									list -> list.stream()
											.sorted(Comparator.comparing(
													TransportOrderShip::getCreatedTime)
													.reversed())
											.collect(Collectors.toList()))));
			for (Ship ship : combinedList) {
				ShipSmartVo shipSmartVo = new ShipSmartVo();
				// 初始化非必要条件的分数 认证船舶34分，当前位置17分，载重吨7分，货仓状态5分，常跑航线3分，历史运单1分
				shipSmartVo.setCount(0);
				// 初始化非必要条件的数量
				shipSmartVo.setScore(0);
				shipSmartVo.setId(ship.getId());
				shipSmartVo.setShip(ship);
				// 1判断是否满足认证船舶条件 满足认证船舶 加34分 非必要条件数量增加
				if (ShipDef.State.AUTHENTICATED.match(ship.getState())) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 34);
				}
				// 3判断是否满足载重吨条件
				this.handleTonCapacity(ship, shipSmartVo,
						shippingRequirementCustomer);
				// 4判断是否满足货仓状态条件
				if (Objects.nonNull(ship.getWarehouseState())) {
					// 满足货仓状态条件 加5分 非必要条件数量增加
					if (DeviceCapturePicDef.RecognizeResult.IDLE
							.match(ship.getWarehouseState())) {
						shipSmartVo.setCount(shipSmartVo.getCount() + 1);
						shipSmartVo.setScore(shipSmartVo.getScore() + 5);
					}
				}
				// 5判断常跑航线
				this.handleFrequentShipRoutes(ship, shipSmartVo,
						frequentShipRouteList, portMap,
						shippingRequirementCustomer);
				// 如果船主没有填写常跑航线信息 则需要调用方法取船舶最新一条的船运单始发港到目的港的航线）
				List<TransportOrderShip> transportOrderShips = this
						.handelTransportOrderShips(
								transportOrderShipMap.get(ship.getId()),
								shipSmartVo);
				if (CollectionUtils.isNotEmpty(transportOrderShips)) {
					// 6判断历史运单 查询出船舶关联的船运单且始发港到目的港不为空的数据
					List<TransportOrderShip> orderShipList = transportOrderShips
							.stream()
							.filter(orderShip -> Objects
									.nonNull(orderShip.getSourcePortId())
									&& Objects.nonNull(
											orderShip.getDestinationPortId()))
							.toList();
					// 取所有船运单始发港到目的港的航线，需要至少有一条与找船需求填写的始发港目的港的航线一致
					this.handleOrderShipList(orderShipList, shipSmartVo,
							shippingRequirementCustomer);
					shipSmartVos.add(shipSmartVo);
				}
			}
			// 过滤出满足至少两个非必要条件的数据
			List<ShipSmartVo> filterShip = shipSmartVos.stream()
					.filter(vo -> vo.getCount() >= 2).toList();
			// 根据港口id和区域范围查询船舶 满足当前位置的船舶
			List<ShipInfo> shipInfos = this.findShipInfoByPortId(
					shippingRequirementCustomer.getSourcePortId(),
					ShipDef.Area.TWENTY.getName());
			Set<String> shipIds = shipInfos.stream().map(ShipInfo::getId)
					.collect(Collectors.toSet());
			// 找出满足至少两个非必要条件的数据和找出满足当前位置的船舶 对这些数据进行count和score更新
			for (ShipSmartVo shipSmartVo : filterShip) {
				if (shipIds.contains(shipSmartVo.getId())) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 17);
				}
			}
			// 筛选出满足至少三个非必要条件的数据 然后调船顺查询信息
			List<ShipSmartVo> filterAndSort = this.filterAndSort(filterShip);
			// 如果数据多于5条 则取前5条数据
			if (filterAndSort.size() > 5) {
				filterAndSort = filterAndSort.subList(0, 5);
			}
			String idsString = filterAndSort.stream().limit(40)
					.map(ShipSmartVo::getId).sorted()
					.collect(Collectors.joining(","));
			List<ShipInfo> shipInfosList = SpringUtil.getBean(ShipService.class)
					.findByBatchAsi(idsString);
			Map<String, ShipInfo> shipInfoMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(shipInfosList)) {
				shipInfoMap = shipInfosList.stream().collect(Collectors
						.toMap(ShipInfo::getId, shipInfo -> shipInfo));
			}
			for (ShipSmartVo shipSmartVo : filterAndSort) {
				ShipInfo shipInfo = shipInfoMap.get(shipSmartVo.getId());
				if (Objects.nonNull(shipInfo)) {
					this.handelShipType(shipInfo, shipSmartVo);
					// 调用方法处理船舶信息里面的当前位置和船名，以及设置船舶状态
					this.handleShipSmart(shipSmartVo, shipInfo,
							monitoringChannels);
				}

			}
			finalShipSmartVos = filterAndSort;
		}
		// 返回最后的数据
		return finalShipSmartVos;
	}

	@Override
	public ShipInspectionReportVo recognizeShipInspectionReport(
			List<Long> fileIds) {
		List<File> files = fileService.findByIds(fileIds);
		if (CollectionUtils.isEmpty(fileIds)) {
			throw new BadRequestException(ErrorCode.CODE_30002019);
		}

		for (File file : files) {
			Map<String, Object> paramsMap = new HashMap<>();
			paramsMap.put("img", file.getPath());
			RunWorkflowResp res;
			try {
				res = cozeSDK.executeWorkflow(
						cozeProperties.getOcrShipEnvProtCertWorkflowId(),
						Boolean.FALSE, paramsMap);
			} catch (Exception e) {
				log.error("recognizeShipInspectionReport error: {}",
						ExceptionUtil.buildErrorMessage(e));
				return new ShipInspectionReportVo();
			}
			log.info("recognizeShipInspectionReport result:{}", res);
			if (CommonDef.Symbol.NO.match(res.getCode())
					&& StringUtils.isNotBlank(res.getData())) {
				return JSONObject.parseObject(res.getData(),
						ShipInspectionReportVo.class);
			}
		}

		return new ShipInspectionReportVo();
	}

	@Override
	public Optional<ShipBusinessTransportVo> getShipBusinessTransport(
			Long fileId) {
		Optional<File> optionalFile = fileService.findOne(fileId);
		if (optionalFile.isEmpty()) {
			return Optional.empty();
		}
		File file = optionalFile.get();
		// 将文件转存到公共桶--数据资产
		CompletableFuture.runAsync(
				() -> fileService.transferToPublic(file.getPath(),
						"ship-business-transport-card"),
				ThreadPoolUtil.getShipExecutor()).exceptionally(e -> {
					log.error("转存失败,失败原因:{}",
							ExceptionUtil.buildErrorMessage((Exception) e));
					return null;
				});
		Map<String, Object> paramsMap = new HashMap<>();
		paramsMap.put("img", file.getPath());
		RunWorkflowResp res;
		try {
			res = cozeSDK.executeWorkflow(
					cozeProperties.getOcrShipBusinessWorkflowId(),
					Boolean.FALSE, paramsMap);
		} catch (Exception e) {
			log.error("getShipBusinessTransport error: {}",
					ExceptionUtil.buildErrorMessage(e));
			return Optional.empty();
		}
		log.info("getShipBusinessTransport result:{}", res);
		if (CommonDef.Symbol.NO.match(res.getCode())
				&& StringUtils.isNotBlank(res.getData())) {
			return Optional.of(JSON.parseObject(res.getData(),
					ShipBusinessTransportVo.class));
		}
		return Optional.empty();
	}

	@Override
	public Optional<String> recognizeCrewCertificate(Long fileId) {
		Optional<File> optionalFile = fileService.findOne(fileId);
		if (optionalFile.isEmpty()) {
			return Optional.empty();
		}
		File file = optionalFile.get();
		// 将文件转存到公共桶--数据资产
		CompletableFuture
				.runAsync(
						() -> fileService.transferToPublic(file.getPath(),
								"crew_certificate"),
						ThreadPoolUtil.getShipExecutor())
				.exceptionally(e -> {
					log.error("转存失败,失败原因:{}",
							ExceptionUtil.buildErrorMessage((Exception) e));
					return null;
				});
		Map<String, Object> paramsMap = new HashMap<>();
		paramsMap.put("img", file.getPath());
		RunWorkflowResp res;
		try {
			res = cozeSDK.executeWorkflow(
					cozeProperties.getOcrCrewCertificateWorkflowId(),
					Boolean.FALSE, paramsMap);
		} catch (Exception e) {
			log.error("recognizeCrewCertificate error: {}",
					ExceptionUtil.buildErrorMessage(e));
			return Optional.empty();
		}
		log.info("recognizeCrewCertificate result:{}", res);
		if (CommonDef.Symbol.NO.match(res.getCode())
				&& StringUtils.isNotBlank(res.getData())) {
			JSONObject data = JSONObject.parse(res.getData());
			String job = data.getString("job");
			return Optional.ofNullable(job);
		}
		return Optional.empty();
	}

	@Transactional(rollbackFor = Exception.class)
	@FileId
	@Override
	@History(success = HistoryDef.SHIP_ADD, bizNo = "{{#ship.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO)
	public Ship create(Ship ship) {
		Ship aisInfo = getAisInfo(ship.getId());
		return super.create(aisInfo);
	}

	@FileId(type = 2)
	@Override
	public Ship update(Ship resource) {
		Ship ship = super.update(resource);
		this.handelShipScope();
		return ship;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchUpdate(List<Ship> resources) {
		for (Ship resource : resources) {
			super.updateAllProperties(resource);
		}
	}

	@FileId(type = 2)
	@Transactional(rollbackFor = Exception.class)
	@Override
	@History(success = HistoryDef.SHIP_MODIFY, isSaveChange = true, bizNo = "{{#resource.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO)
	public Ship updateAllProperties(Ship resource) {
		if (ShipDef.State.AUTHENTICATED.match(resource.getState())) {
			// 查询当前船舶认证记录状态为待审核 存在直接驳回
			List<ShipApply> shipApplyList = shipApplyService
					.findByShipId(resource.getId());
			for (ShipApply shipApply : shipApplyList) {
				if (ShipApplyDef.State.PENDING_APPROVAL.getCode()
						.equals(shipApply.getState())) {
					shipApplyService.review(shipApply.getId(),
							ShipApplyDef.State.REJECTED.getCode(),
							"船舶已绑定其他承运商");
				}
			}
		}
		if (Objects.nonNull(resource.getCarrier())) {
			this.updateCustomerIdentity(resource.getCarrier());
		}
		Ship ship = super.updateAllProperties(resource);
		this.handelShipScope();
		return ship;
	}

	@Override
	public void bind(String id, Long customerId) {
		super.findOne(id).ifPresent(ship -> {
			ship.setState(ShipDef.State.AUTHENTICATED.getCode());
			ship.setAuthenticationTime(LocalDateTime.now());
			ship.setCarrier(customerId);
			super.updateAllProperties(ship);

			this.updateCustomerIdentity(ship.getCarrier());
			this.handelShipScope();
		});
	}

	@FileId(type = 2)
	@Transactional(rollbackFor = Exception.class)
	@Override
	@History(success = HistoryDef.SHIP_BIND, bizNo = "{{#ship.getId()}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO)
	public void bind(Ship ship) {
		// 查询当前船舶认证记录状态为待审核 存在直接驳回
		List<ShipApply> shipApplyList = shipApplyService
				.findByShipId(ship.getId());
		for (ShipApply shipApply : shipApplyList) {
			if (ShipApplyDef.State.PENDING_APPROVAL.getCode()
					.equals(shipApply.getState())) {
				shipApplyService.review(shipApply.getId(),
						ShipApplyDef.State.REJECTED.getCode(), "船舶已绑定其他承运商");
			}
		}
		super.updateAllProperties(ship);
		// 更新用户拥有的身份
		this.updateCustomerIdentity(ship.getCarrier());
		this.handelShipScope();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@History(success = HistoryDef.SHIP_UNBIND, bizNo = "{{#id}}", module = HistoryDef.LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO)
	public void unbind(String id) {
		super.findOne(id).ifPresent(ship -> {
			// 删除认证记录
			shipApplyService.deleteByShipIdAndCustomerId(id, ship.getCarrier());
			ship.setState(ShipDef.State.UNAUTHENTICATED.getCode());
			ship.setAuthenticationTime(null);
			ship.setCertificateFiles(null);
			ship.setCarrier(null);
			super.updateAllProperties(ship);
			this.handelShipScope();
		});
		// 船主解绑船舶时 删除船舶对应的船舶监控调阅分享记录
		shipMonitorShareService.deleteByShipId(id);
	}

	@Override
	public void createHistory(Long customerId, Integer monitoringChannels,
			String id, String name, String cnname, Integer type) {
		ShipHistoryVo ship = new ShipHistoryVo();
		ship.setCnname(cnname);
		ship.setName(name);
		ship.setType(type);
		ship.setId(id);

		String key = RedisKeys.Cache.SHIP_HISTORY + monitoringChannels + ":"
				+ customerId;
		// 判断是否存在
		redisClient.zAdd(key, JsonUtils.objectToJson(ship),
				System.currentTimeMillis());

		this.trimZSetSize(key);
	}

	@Override
	public void createShipPortHistory(Long customerId,
			Integer monitoringChannels, ShipPortHistoryVo shipPortHistoryVo) {
		String key = RedisKeys.Cache.SHIP_PORT_HISTORY + monitoringChannels
				+ ":" + customerId;
		// 判断是否存在
		redisClient.zAdd(
				RedisKeys.Cache.SHIP_PORT_HISTORY + monitoringChannels + ":"
						+ customerId,
				JsonUtils.objectToJson(shipPortHistoryVo),
				System.currentTimeMillis());

		this.trimZSetSize(key);
	}

	@Override
	public void updateWarehouseState() {
		// 查询绑定在船舶上的摄像头信息
		LambdaQueryWrapper<Device> queryWrapper = Wrappers
				.lambdaQuery(Device.class);
		deviceService.filterDeleted(queryWrapper);
		queryWrapper.select(Device::getSerialNo, Device::getMasterId,
				Device::getOnline);
		queryWrapper.eq(Device::getState, DeviceDef.State.ENABLE.getCode());
		queryWrapper.eq(Device::getTransportType,
				DeviceDef.TransportType.SHIP.getCode());
		queryWrapper.eq(Device::getType, DeviceDef.Type.CAMERA.getCode());
		queryWrapper.isNotNull(Device::getMasterId);
		List<Device> devices = deviceService.findList(queryWrapper);
		if (CollectionUtils.isEmpty(devices)) {
			return;
		}
		log.info("要执行船舶状态识别的设备：{}", devices.stream().map(Device::getSerialNo)
				.collect(Collectors.joining("、")));

		int size = devices.size();
		Integer n = getSegmentSize(size);

		// 每段数量
		int segmentSize = size / n;
		if (size % n > 0) {
			int timeLength = 60 / (n + 1);
			log.info("timeLength:{}", timeLength);
			for (int i = 0; i < n + 1; i++) {
				List<Device> subDevices;
				if (i == n) {
					// 最后一节数组 数量不满segmentSize
					subDevices = devices.subList(segmentSize * i, size);
				} else {
					subDevices = devices.subList(segmentSize * i,
							segmentSize * (i + 1));
				}
				ThreadPoolUtil.scheduleTask(new CaptureAndUpdate(subDevices),
						timeLength, TimeUnit.MINUTES,
						ThreadPoolUtil.getCaptureExecutor());
			}
		} else {
			int timeLength = 60 / n;
			log.info("timeLength:{}", timeLength);
			for (int i = 0; i < n; i++) {
				List<Device> subDevices = devices.subList(segmentSize * i,
						segmentSize * (i + 1));
				ThreadPoolUtil.scheduleTask(new CaptureAndUpdate(subDevices),
						timeLength, TimeUnit.MINUTES,
						ThreadPoolUtil.getCaptureExecutor());
			}
		}
	}

	@Override
	public void batchUpdateExistWaterGauge(Collection<String> shipIds,
			Integer existWaterGauge) {
		LambdaUpdateWrapper<Ship> updateWrapper = Wrappers
				.lambdaUpdate(Ship.class);
		updateWrapper.in(Ship::getId, shipIds);
		updateWrapper.set(Ship::getExistWaterGauge, existWaterGauge);
		repository.update(updateWrapper);
	}

	@Override
	public void batchUpdateExistDevice(Collection<String> shipIds,
			Integer existDevice) {
		LambdaUpdateWrapper<Ship> updateWrapper = Wrappers
				.lambdaUpdate(Ship.class);
		updateWrapper.in(Ship::getId, shipIds);
		updateWrapper.set(Ship::getExistDevice, existDevice);
		repository.update(updateWrapper);
	}

	@Override
	public void batchUpdateExistMobile(Collection<String> shipIds,
			Integer existMobile) {
		LambdaUpdateWrapper<Ship> updateWrapper = Wrappers
				.lambdaUpdate(Ship.class);
		updateWrapper.in(Ship::getId, shipIds);
		updateWrapper.set(Ship::getExistMobile, existMobile);
		repository.update(updateWrapper);
	}

	/**
	 * 获取一个小时内最合适的分片数量
	 *
	 * @param size
	 * @return
	 */
	private Integer getSegmentSize(int size) {
		int max = size / 20;
		int[] arr = { 1 };
		if (max > 0) {
			arr = new int[max];
			for (int i = 0; i < max; i++) {
				arr[i] = i + 2;
			}
		}
		int n = 1;
		for (int i : arr) {
			if (size / i > 15 && size / i < 20) {
				n = i;
				break;
			}
		}
		return n;
	}

	@Override
	public void deleteHistory(Long customerId, Integer monitoringChannels,
			String id, String name, String cnname, Integer type) {
		ShipHistoryVo ship = new ShipHistoryVo();
		ship.setCnname(cnname);
		ship.setName(name);
		ship.setType(type);
		ship.setId(id);
		redisClient.zRemove(RedisKeys.Cache.SHIP_HISTORY + monitoringChannels
				+ ":" + customerId, JsonUtils.objectToJson(ship));
	}

	@Override
	public void deleteAllHistory(Long customerId, Integer monitoringChannels) {
		redisClient.delete(RedisKeys.Cache.SHIP_HISTORY + monitoringChannels
				+ ":" + customerId);
	}

	@Override
	public void deleteShipPortHistory(Long customerId,
			Integer monitoringChannels, ShipPortHistoryVo shipPortHistoryVo) {
		redisClient.zRemove(
				RedisKeys.Cache.SHIP_PORT_HISTORY + monitoringChannels + ":"
						+ customerId,
				JsonUtils.objectToJson(shipPortHistoryVo));
	}

	@Override
	public void deleteAllShipPortHistory(Long customerId,
			Integer monitoringChannels) {
		redisClient.delete(RedisKeys.Cache.SHIP_PORT_HISTORY
				+ monitoringChannels + ":" + customerId);
	}

	@Override
	public void handleShipReport(
			Map<String, DeviceCapturePicRec> shipIdDeviceCapturePicRecMap) {
		if (shipIdDeviceCapturePicRecMap.isEmpty()) {
			// 都为离线的设备 不发送告警信息
			return;
		}
		// 根据船舶id查询未完成的船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findIncompleteByShipIds(shipIdDeviceCapturePicRecMap.keySet());
		if (CollectionUtils.isEmpty(transportOrderShips)) {
			// 未关联船运单 不预警
			return;
		}
		Map<String, Ship> idShipMap = super.getIdMap(
				new ArrayList<>(shipIdDeviceCapturePicRecMap.keySet()));
		List<MonitorEarlyWarnRecord> monitorEarlyWarnRecords = new ArrayList<>();
		for (TransportOrderShip transportOrderShip : transportOrderShips) {
			Integer expectedWarehouseStatus = this
					.getExpectedWarehouseStatus(transportOrderShip.getState());
			if (Objects.isNull(expectedWarehouseStatus)) {
				// 未知状态 跳过处理
				continue;
			}
			// 比较货仓状态
			DeviceCapturePicRec deviceCapturePicRec = shipIdDeviceCapturePicRecMap
					.get(transportOrderShip.getShipId());
			// 预期货仓状态和实际货仓状态不匹配 触发预警
			if (!expectedWarehouseStatus
					.equals(deviceCapturePicRec.getRecognizeResult())) {
				// 判断该船舶异常上次是否已触发过(同样的运单状态，实际货仓状态是否和上一次一致)，如已触发过，不再生成预警记录
				if (monitorEarlyWarnRecordService.isRepeatShipException(
						transportOrderShip,
						deviceCapturePicRec.getRecognizeResult())) {
					continue;
				}
				// 生成预警记录
				monitorEarlyWarnRecords.add(this.buildMonitorEarlyWarnRecord(
						transportOrderShip, deviceCapturePicRec,
						expectedWarehouseStatus, idShipMap));
			}
		}
		if (CollectionUtils.isNotEmpty(monitorEarlyWarnRecords)) {
			monitorEarlyWarnRecordService.batchCreate(monitorEarlyWarnRecords);

			// 发送企业微信消息
			try {
				for (MonitorEarlyWarnRecord e : monitorEarlyWarnRecords) {
					String shipName = StringUtils.isBlank(e.getShipCnName())
							? e.getShipName()
							: e.getShipCnName();
					// 船舶异常待处理消息
					messageService.sendNotice(WxwMessage.builder()
							.receiptors(userService
									.findUsersByPermission(
											AdminPermissionDef.WARNING_MANAGE,
											null)
									.stream()
									.map(user -> String.valueOf(user.getId()))
									.toList())
							.url("/logistics/monitor/monitorWarn/"
									.concat(String.valueOf(e.getId())))
							.prefix(StringUtils.EMPTY)
							.operationModule(
									WxwDef.NoticeOperationModule.SHIP.getDesc())
							.desc("监控预警,请指派").keyword(shipName)
							.content(StringUtils.EMPTY).build());
				}
			} catch (Exception e) {
				log.error("船舶异常发送企业微信消息失败, 失败原因:{}",
						ExceptionUtil.getStackTraceString(e));
			}
		}
	}

	@MethodCallCount
	@Override
	public void asyncBathCreate(List<String> idList) {

		// 指定每个子列表的大小
		int chunkSize = 40;

		// 将原始列表分割成多个子列表
		List<List<String>> subLists = new ArrayList<>();
		for (int i = 0; i < idList.size(); i += chunkSize) {
			subLists.add(
					idList.subList(i, Math.min(i + chunkSize, idList.size())));
		}

		// 打印分割后的子列表
		for (List<String> sublist : subLists) {
			StringJoiner joiner = new StringJoiner(",");
			for (String id : sublist) {
				joiner.add(id);
			}
			String ids = joiner.toString();
			try {
				AisInfoResponse response = aisClient.getAisInfo(ids, "SHIP");
				if (Objects.isNull(response)
						|| Objects.isNull(response.getData())) {
					return;
				}
				List<ShipInfo> shipInfoList = response.getData();
				for (ShipInfo info : shipInfoList) {
					Ship ship = new Ship();
					ship.setId(info.getId());
					// 组装船舶信息
					packShip(info, ship);
					this.insertIgnoreUk(ship);
					Thread.sleep(100);
				}
			} catch (Exception e) {
				log.error("查船异常: {}", e.getMessage(), e);
				throw new BadRequestException(ErrorCode.CODE_30002016);
			}
		}
	}

	@FileId
	@Override
	public void insertIgnoreUk(Ship ship) {
		repository.insertIgnoreUk(ship);
	}

	@PostConstruct
	@Override
	public void handelShipScope() {
		// 查询出包含摄像头或包含水尺或拥有船主和联系方式的
		LambdaQueryWrapper<Ship> wrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode());

		// 获取绑定设备的船舶的id列表
		List<String> shipIdList = deviceService
				.findMasterIds(DeviceDef.TransportType.SHIP.getCode());

		// 获取水尺上的船舶id
		List<ShipWaterGauge> shipWaterGauges = shipWaterGaugeService
				.findAllDistinct();
		List<String> shipIds = shipWaterGauges.stream()
				.map(ShipWaterGauge::getShipId).distinct().toList();

		wrapper.and(x -> x
				.in(CollectionUtils.isNotEmpty(shipIdList), Ship::getId,
						shipIdList)
				.or()
				.in(CollectionUtils.isNotEmpty(shipIds), Ship::getId, shipIds)
				.or().isNotNull(Ship::getMobile));

		List<Ship> shipList = repository.selectList(wrapper);

		if (CollectionUtils.isEmpty(shipList)) {
			return;
		}

		List<ShipScope> shipScopeList = new ArrayList<>();
		// 进行评分 设备4 手机3 水尺2
		for (Ship ship : shipList) {
			ShipScope shipScope = new ShipScope();
			int scope = 0;
			shipScope.setId(ship.getId());
			if (StringUtils.isNotBlank(ship.getMobile())) {
				shipScope.setMobile(ship.getMobile());
				shipScope.setExistMobile(CommonDef.Symbol.YES.getCode());
				scope += 3;
			} else {
				shipScope.setExistMobile(CommonDef.Symbol.NO.getCode());
			}
			shipScope.setCaptain(ship.getCaptain());

			// 是否存在设备
			if (CollectionUtils.isNotEmpty(shipIdList)
					&& shipIdList.contains(ship.getId())) {
				shipScope.setExistDevice(CommonDef.Symbol.YES.getCode());
				scope += 4;
			} else {
				shipScope.setExistDevice(CommonDef.Symbol.NO.getCode());
			}
			// 是否存在水尺
			if (CollectionUtils.isNotEmpty(shipIds)
					&& shipIds.contains(ship.getId())) {
				shipScope.setExistWaterGauge(CommonDef.Symbol.YES.getCode());
				scope += 2;
			} else {
				shipScope.setExistWaterGauge(CommonDef.Symbol.NO.getCode());
			}
			shipScope.setScope(scope);
			shipScope.setCreateTime(ship.getCreatedTime());
			// 吃水
			if (StringUtils.isNotBlank(ship.getDraft())) {
				try {
					shipScope.setDraft(Double.parseDouble(ship.getDraft()));
				} catch (Exception e) {
					shipScope.setDraft(null);
				}
			}
			// 载重吨和净吨
			shipScope.setTonPure(ship.getTonPure());
			shipScope.setTonCapacity(ship.getTonCapacity());
			shipScope.setWarehouseState(ship.getWarehouseState());
			shipScopeList.add(shipScope);
		}

		// 保存到redis中
		redisClient.set(RedisKeys.Cache.SHIP_SCOPE,
				JsonUtils.objectToJson(shipScopeList));
	}

	/**
	 * 1.每日评述增加内容【船运价格】,读取当日运价指数中的数据。规则：默认展示和前一天相比运价有变化的航线；如果所有航线都无变化，则展示几条热门航线（岳阳-江阴、岳阳-芜湖、黄冈-江阴、黄冈-芜湖、重庆-江阴）
	 * 2.每日评述标题需要根据砂石指数、运价指数总结。 3.增加时间节点，每天17:00、17：30、18:00、18:30、19:00更新
	 * 没有到有，某个吨位有涨跌都算 如果三个吨位和前一天比都是持平就不算有变化
	 *
	 * @param localDate
	 */
	@Override
	public List<ShippingPriceIndexAIVo> findTodayShippingPriceIndex(
			LocalDate localDate) {
		if (Objects.isNull(localDate)) {
			localDate = LocalDate.now();
		}
		LocalDate yesterday = localDate.minusDays(1);
		// 查询当日发布的所有运价信息
		List<ShippingPriceIndexAIVo> shippingPriceIndexAIVos = repository
				.findTodayShippingPriceIndex(yesterday, localDate);
		if (shippingPriceIndexAIVos.isEmpty()) {
			return List.of();
		}
		// 运价是否有变化
		boolean isChange = Boolean.FALSE;
		// 热门航线运价
		List<ShippingPriceIndexAIVo> topShipRouteList = new ArrayList<>();
		// 运价相比于上一天有变化的集合
		List<ShippingPriceIndexAIVo> isChangeList = new ArrayList<>();

		for (ShippingPriceIndexAIVo shippingPriceIndexAIVo : shippingPriceIndexAIVos) {
			if (this.checkIsTopShipRoute(
					shippingPriceIndexAIVo.getShipRouteSource(),
					shippingPriceIndexAIVo.getShipRouteDestination())) {
				topShipRouteList.add(shippingPriceIndexAIVo);
			}
			// 判断运价相对于上一天是是否有变化
			if (Objects.isNull(shippingPriceIndexAIVo.getYesterdayPrices())) {
				isChange = Boolean.TRUE;
				isChangeList.add(shippingPriceIndexAIVo);
				continue;
			}
			if (!this.equalShippingPrice(shippingPriceIndexAIVo.getPrices(),
					shippingPriceIndexAIVo.getYesterdayPrices())) {
				isChange = Boolean.TRUE;
				isChangeList.add(shippingPriceIndexAIVo);
			}
		}

		if (isChange) {
			return isChangeList;
		} else {
			return topShipRouteList;
		}
	}

	@Override
	public void testUpdateWarehouseState() {
		// 查询绑定在船舶上的摄像头信息
		LambdaQueryWrapper<Device> queryWrapper = Wrappers
				.lambdaQuery(Device.class);
		deviceService.filterDeleted(queryWrapper);
		queryWrapper.select(Device::getSerialNo, Device::getMasterId,
				Device::getOnline);
		queryWrapper.eq(Device::getState, DeviceDef.State.ENABLE.getCode());
		queryWrapper.eq(Device::getTransportType,
				DeviceDef.TransportType.SHIP.getCode());
		queryWrapper.eq(Device::getType, DeviceDef.Type.CAMERA.getCode());
		queryWrapper.isNotNull(Device::getMasterId);
		List<Device> devices = deviceService.findList(queryWrapper);
		if (CollectionUtils.isEmpty(devices)) {
			return;
		}
		log.info("要执行船舶状态识别的设备：{}", devices.stream().map(Device::getSerialNo)
				.collect(Collectors.joining("、")));

		new CaptureAndUpdate(devices).run();
	}

	@Override
	public void handleShipInfoCallback(HmgShipInfo hmgShipInfo) {
		Integer hmgCheckSate = null;
		switch (HmgDef.HmgShipCheckState.from(hmgShipInfo.getCheckFlag())) {
			case APPROVED ->
				hmgCheckSate = ShipDef.HmgCheckState.APPROVED.getCode();
			case PENDING_REVIEW ->
				hmgCheckSate = ShipDef.HmgCheckState.PENDING_REVIEW.getCode();
			case REJECTED ->
				hmgCheckSate = ShipDef.HmgCheckState.REJECTED.getCode();
			case TO_BE_SUBMIT ->
				hmgCheckSate = ShipDef.HmgCheckState.TO_BE_SUBMIT.getCode();
		}
		Ship ship = super.findOne(hmgShipInfo.getShipMmsi()).orElse(null);
		if (Objects.nonNull(ship)) {
			ship.setHmgCheckState(hmgCheckSate);
			super.updateAllProperties(ship);
		}

	}

	/**
	 * 比较今天和昨天的运价信息是否有变化
	 *
	 * @param a
	 * @param b
	 * @return
	 */
	private boolean equalShippingPrice(ShippingPrice a, ShippingPrice b) {
		if (this.areEqual(a.getSixToNineThousandPrice(),
				b.getSixToNineThousandPrice())) {
			return Boolean.FALSE;
		}
		if (this.areEqual(a.getTenThousandPrice(), b.getTenThousandPrice())) {
			return Boolean.FALSE;
		}
		if (this.areEqual(a.getFifteenUpThousandPrice(),
				b.getFifteenUpThousandPrice())) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

	/**
	 * 判断相等
	 *
	 * @param a
	 * @param b
	 * @return
	 */
	private boolean areEqual(BigDecimal a, BigDecimal b) {
		if (a == null || b == null) {
			return !Objects.equals(a, b);
		}
		return a.compareTo(b) != 0;
	}

	/**
	 * 校验是否是热门航线 岳阳-江阴、岳阳-芜湖、黄冈-江阴、黄冈-芜湖、重庆-江阴
	 *
	 * @param shipRouteSource
	 * @param shipRouteDestination
	 */
	private boolean checkIsTopShipRoute(String shipRouteSource,
			String shipRouteDestination) {
		if (AssistantsPluginDef.YUE_YANG.equals(shipRouteSource)
				|| AssistantsPluginDef.HUANG_GANG.equals(shipRouteSource)) {
			return AssistantsPluginDef.JIANG_YIN.equals(shipRouteDestination)
					|| AssistantsPluginDef.WU_HU.equals(shipRouteDestination);
		}
		if (AssistantsPluginDef.CHONG_QIN.equals(shipRouteSource)
				&& AssistantsPluginDef.JIANG_YIN.equals(shipRouteDestination)) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * 获取船顺网船舶信息并组装数据
	 *
	 * @param id
	 * @return
	 */
	@MethodCallCount
	private Ship getAisInfo(String id) {
		AisInfoResponse aisInfo;
		try {
			aisInfo = aisClient.getAisInfo(id, "SHIP");
		} catch (Exception e) {
			log.error("查询船舶信息异常: {}", e.getMessage(), e);
			throw new BadRequestException(ErrorCode.CODE_30002016);
		}
		if (Objects.isNull(aisInfo) || Objects.isNull(aisInfo.getData())) {
			throw new BadRequestException(ErrorCode.CODE_30002015);
		}
		List<ShipInfo> data = aisInfo.getData();
		if (CollectionUtils.isEmpty(data)) {
			throw new BadRequestException(ErrorCode.CODE_30002015);
		}
		ShipInfo shipInfo = data.get(0);
		Ship ship = new Ship();
		ship.setId(id);
		// 组装船舶信息
		packShip(shipInfo, ship);
		return ship;
	}

	/**
	 * 组装船舶类型并入库
	 *
	 * @param shipInfo
	 */
	private void asyncCreate(ShipInfo shipInfo) {
		Optional<Ship> optional = super.findOne(shipInfo.getId());
		if (optional.isPresent()) {
			return;
		}
		Ship ship = new Ship();
		ship.setId(shipInfo.getId());
		// 组装船舶信息
		packShip(shipInfo, ship);
		repository.insertIgnoreUk(ship);
	}

	/**
	 * 生成预警记录
	 *
	 * @param transportOrderShip
	 * @param deviceCapturePicRec
	 * @param expectedWarehouseStatus
	 * @param idShipMap
	 * @return
	 */
	@MethodCallCount
	private MonitorEarlyWarnRecord buildMonitorEarlyWarnRecord(
			TransportOrderShip transportOrderShip,
			DeviceCapturePicRec deviceCapturePicRec,
			Integer expectedWarehouseStatus, Map<String, Ship> idShipMap) {
		Ship ship = idShipMap.get(transportOrderShip.getShipId());

		MonitorEarlyWarnRecord monitorEarlyWarnRecord = new MonitorEarlyWarnRecord();
		monitorEarlyWarnRecord
				.setDeviceSerialNo(deviceCapturePicRec.getDeviceSerialNo());
		monitorEarlyWarnRecord
				.setType(MonitorEarlyWarnRecordDef.Type.SHIP.getCode());
		monitorEarlyWarnRecord.setTriggerTime(LocalDateTime.now());
		monitorEarlyWarnRecord.setShipId(transportOrderShip.getShipId());
		if (Objects.nonNull(ship)) {
			monitorEarlyWarnRecord.setShipName(ship.getName());
			monitorEarlyWarnRecord.setShipCnName(ship.getCnname());
		}
		monitorEarlyWarnRecord.setHandleState(
				MonitorEarlyWarnRecordDef.HandleState.UNHANDLED.getCode());
		monitorEarlyWarnRecord
				.setTransportOrderShipId(transportOrderShip.getId());
		monitorEarlyWarnRecord
				.setTransportOrderShipState(transportOrderShip.getState());
		monitorEarlyWarnRecord.setExpectWarehouseState(expectedWarehouseStatus);
		monitorEarlyWarnRecord.setActualWarehouseState(
				deviceCapturePicRec.getRecognizeResult());
		monitorEarlyWarnRecord
				.setRemainingLayTime(deviceCapturePicRec.getRemainingLayTime());
		monitorEarlyWarnRecord.setFileId(deviceCapturePicRec.getPicFileId());

		AisInfoResponse aisInfo = null;
		try {
			aisInfo = aisClient.getAisInfo(transportOrderShip.getShipId(),
					"SHIP");
		} catch (Exception e) {
			log.error("查询船舶信息异常: {}", e.getMessage(), e);
		}
		// 经纬度查询
		if (Objects.nonNull(aisInfo)
				&& CollectionUtils.isNotEmpty(aisInfo.getData())) {
			ShipInfo shipInfo = aisInfo.getData().get(0);
			monitorEarlyWarnRecord
					.setGeo(new GeoPoint(shipInfo.getLon(), shipInfo.getLat()));
		}
		return monitorEarlyWarnRecord;
	}

	/**
	 * 根据船运单状态获取预期货仓状态
	 *
	 * @param state
	 * @return
	 */
	private Integer getExpectedWarehouseStatus(Integer state) {
		switch (TransportOrderShipDef.State.from(state)) {
			case DISCHARGED -> {
				// 船运单状态 待装货 预期货仓状态 空载
				// 船运单状态 已卸货 预期货仓状态 空载
				return DeviceCapturePicDef.RecognizeResult.IDLE.getCode();
			}
			case DURING_LOADING -> {
				// 船运单状态 装货中 预期货仓状态 装载中
				return DeviceCapturePicDef.RecognizeResult.LOADING.getCode();
			}
			case DURING_UNLOADING -> {
				// 船运单状态 卸货中 预期货仓状态 卸货中
				return DeviceCapturePicDef.RecognizeResult.UNLOADING.getCode();
			}
			case AWAITING_DEPARTURE, TO_BE_UNLOADED, DURING_TRANSPORTATION -> {
				// 船运单状态 待发航 预期货仓状态 装载
				// 船运单状态 运输中 预期货仓状态 装载
				// 船运单状态 待卸货 预期货仓状态 装载
				return DeviceCapturePicDef.RecognizeResult.LOADED.getCode();
			}
			default -> {
				return null;
			}
		}
	}

	/**
	 * 只保留20条记录
	 *
	 * @param key
	 */
	private void trimZSetSize(String key) {
		long size = redisClient.zZCard(key);
		if (size > 20) {
			redisClient.zRemoveRange(key, 0, size - 20 - 1);
		}
	}

	/**
	 * 根据船舶封装船舶Vo
	 *
	 * @param ship
	 * @return
	 */
	private ShipVo packVo(Ship ship) {
		ShipVo vo = new ShipVo();
		vo.setShip(ship);
		if (Objects.nonNull(ship.getCarrier())) {
			// 获取账号信息
			vo.setCustomer(
					customerService.findOne(ship.getCarrier()).orElse(null));
		}
		List<Device> devices = deviceService.findByMasterIdAndTransportType(
				ship.getId(), DeviceDef.TransportType.SHIP.getCode());
		vo.setDevice(devices.stream().findFirst().orElse(null));
		// 获取设备信息
		vo.setDevices(devices);
		return vo;
	}

	/**
	 * 根据船舶列表封装船舶Vo列表
	 *
	 * @param ships
	 *            船舶列表
	 * @return Vo列表
	 */
	private List<ShipVo> packVo(List<Ship> ships, List<String> shipIds) {
		if (CollectionUtils.isEmpty(ships)) {
			return List.of();
		}
		// 获取船舶的id列表
		List<String> ids = ships.stream().map(Ship::getId).toList();
		// 根据船舶id列表查询船舶设备列表
		List<Device> devices = deviceService.findByMasterIds(ids,
				DeviceDef.TransportType.SHIP.getCode());
		// 根据船舶id将船舶设备列表分组
		Map<String, List<Device>> deviceMap = devices.stream()
				.collect(Collectors.groupingBy(Device::getMasterId));
		// 根据客户id查询客户信息
		List<Long> customerIds = ships.stream().map(Ship::getCarrier).distinct()
				.toList();
		Map<Long, Customer> customerMap = customerService.findByIds(customerIds)
				.stream().collect(
						Collectors.toMap(Customer::getId, Function.identity()));
		// 封装船舶Vo列表
		return ships.stream().map(ship -> {
			ShipVo vo = new ShipVo();
			vo.setShip(ship);
			vo.setDevices(deviceMap.get(ship.getId()));
			if (CollectionUtils.isNotEmpty(deviceMap.get(ship.getId()))) {
				vo.setDevice(deviceMap.get(ship.getId()).stream().findFirst()
						.orElse(null));
			}
			if (Objects.nonNull(deviceMap.get(ship.getId()))) {
				vo.setExistDevice(CommonDef.Symbol.YES.getCode());
			} else {
				vo.setExistDevice(CommonDef.Symbol.NO.getCode());
			}
			if (StringUtils.isNotBlank(ship.getMobile())) {
				vo.setExistMobile(CommonDef.Symbol.YES.getCode());
			} else {
				vo.setExistMobile(CommonDef.Symbol.NO.getCode());
			}
			vo.setCustomer(customerMap.get(ship.getCarrier()));
			if (CollectionUtils.isNotEmpty(ships)) {
				vo.setExistWaterGauge(shipIds.contains(ship.getId())
						? CommonDef.Symbol.YES.getCode()
						: CommonDef.Symbol.NO.getCode());
			} else {
				vo.setExistWaterGauge(CommonDef.Symbol.NO.getCode());
			}
			return vo;
		}).toList();
	}

	/**
	 * 通用处理船舶类型的方法
	 *
	 * @param shipInfo
	 *            船舶信息
	 * @param setter
	 *            设置类型的函数式接口
	 */
	private void handleShipTypeCommon(ShipInfo shipInfo,
			Consumer<Integer> setter) {
		if (Objects.nonNull(shipInfo)) {
			if (Objects.nonNull(shipInfo.getType())) {
				// 获取船舶类型code
				String type = shipInfo.getType();
				// 拿到类型名称
				String name = ShipInfoDef.SHIP_TYPE.get(type);
				// 通过名称拿到自定义类型
				if (StringUtils.isNotBlank(name)) {
					setter.accept(ShipDef.Type.getCode(name));
				} else {
					setter.accept(ShipDef.Type.UNKNOWN.getCode());
				}
			} else {
				setter.accept(ShipDef.Type.UNKNOWN.getCode());
			}
		}
	}

	/**
	 * 处理船舶类型（用于 ShipInfoVo）
	 *
	 * @param shipInfo
	 * @param shipInfoVo
	 */
	private void handelShipType(ShipInfo shipInfo, ShipInfoVo shipInfoVo) {
		this.handleShipTypeCommon(shipInfo, shipInfoVo::setType);
	}

	/**
	 * 处理船舶类型（用于 Ship）
	 *
	 * @param info
	 * @param ship
	 */
	private void handelShipType(ShipInfo info, Ship ship) {
		this.handleShipTypeCommon(info, ship::setType);
	}

	/**
	 * 处理船舶类型（用于 ShipSmartVo）
	 *
	 * @param info
	 * @param shipSmartVo
	 */
	private void handelShipType(ShipInfo info, ShipSmartVo shipSmartVo) {
		this.handleShipTypeCommon(info, shipSmartVo::setType);
	}

	/**
	 * 组装船舶信息
	 *
	 * @param shipInfo
	 * @param ship
	 */
	private void packShip(ShipInfo shipInfo, Ship ship) {
		ship.setName(shipInfo.getName());
		ship.setCnname(shipInfo.getCnname());

		// 处理类型
		this.handelShipType(shipInfo, ship);

		ship.setImo(shipInfo.getImo());
		ship.setCallSign(shipInfo.getCallsign());
		if (Objects.nonNull(shipInfo.getLen())) {
			ship.setLength(BigDecimal.valueOf(shipInfo.getLen()));
		}
		if (Objects.nonNull(shipInfo.getWid())) {
			ship.setWidth(BigDecimal.valueOf(shipInfo.getWid()));
		}
		if (Objects.nonNull(shipInfo.getDpth())) {
			ship.setDraft(String.valueOf(shipInfo.getDpth()));
		}
		ship.setState(ShipDef.State.UNAUTHENTICATED.getCode());
		ship.setAuthenticationTime(null);
	}

	/**
	 * 更新用户拥有的身份
	 *
	 * @param carrierId
	 */
	private void updateCustomerIdentity(Long carrierId) {
		customerService.findOne(carrierId).ifPresent(customer -> {
			if ((customer.getIdentity() & 8) != 8) {
				customer.setIdentity(customer.getIdentity() | 8);
				customerService.updateAllProperties(customer);
			}
		});
	}

	/**
	 * 组装vo
	 *
	 * @param ships
	 * @return
	 */
	private List<ShipVo> packVo(List<Ship> ships) {
		if (CollectionUtils.isEmpty(ships)) {
			return List.of();
		}
		// 获取绑定设备的船舶的id列表
		List<String> shipIdList = deviceService
				.findMasterIds(DeviceDef.TransportType.SHIP.getCode());

		List<ShipWaterGauge> shipWaterGauges = shipWaterGaugeService
				.findAllDistinct();
		List<String> shipIds = shipWaterGauges.stream()
				.map(ShipWaterGauge::getShipId).toList();
		// 封装船舶Vo列表
		return ships.stream().map(ship -> {
			ShipVo vo = new ShipVo();
			vo.setShip(ship);
			int scope = 0;
			if (shipIdList.contains(ship.getId())) {
				vo.setExistDevice(CommonDef.Symbol.YES.getCode());
				scope += 4;
			} else {
				vo.setExistDevice(CommonDef.Symbol.NO.getCode());
			}
			if (StringUtils.isNotBlank(ship.getMobile())) {
				vo.setExistMobile(CommonDef.Symbol.YES.getCode());
				scope += 3;
			} else {
				vo.setExistMobile(CommonDef.Symbol.NO.getCode());
			}
			if (CollectionUtils.isNotEmpty(ships)
					&& shipIds.contains(ship.getId())) {
				vo.setExistWaterGauge(CommonDef.Symbol.YES.getCode());
				scope += 2;
			} else {
				vo.setExistWaterGauge(CommonDef.Symbol.NO.getCode());
			}
			vo.setScope(scope);
			return vo;
		}).sorted(Comparator.comparing(ShipVo::getScope).reversed()
				.thenComparing(item -> item.getShip().getCreatedTime(),
						Comparator.nullsLast(Comparator.reverseOrder())))
				.limit(10).toList();
	}

	/**
	 * 处理分数船舶
	 *
	 * @param data
	 * @param GSON
	 * @return
	 */
	private List<ShipInfoVo> handelShipScope(List<ShipInfo> data, Gson GSON) {
		List<ShipInfoVo> shipInfoVoList = new ArrayList<>();

		// 有分数的船舶信息
		Map<String, ShipScope> shipScopeMap = new HashMap<>();

		String shipScope = redisClient.get(RedisKeys.Cache.SHIP_SCOPE);
		if (StringUtils.isNotBlank(shipScope)) {
			// 有分数的船舶集合
			Type shipScopeListType = new TypeToken<List<ShipScope>>() {
			}.getType();
			List<ShipScope> shipScopeList = GSON.fromJson(shipScope,
					shipScopeListType);
			if (CollectionUtils.isNotEmpty(shipScopeList)) {
				// 转map
				shipScopeMap = shipScopeList.stream()
						.collect(Collectors.toMap(ShipScope::getId, t -> t));
			}
		}

		for (ShipInfo shipInfo : data) {
			ShipInfoVo shipInfoVo = new ShipInfoVo();
			BeanUtils.copyProperties(shipInfo, shipInfoVo);
			this.handelShipType(shipInfo, shipInfoVo);
			ShipScope scope = shipScopeMap.get(shipInfo.getId());
			if (Objects.nonNull(scope)) {
				BeanUtils.copyProperties(scope, shipInfoVo);
				shipInfoVo.setDpth(scope.getDraft());
			}
			shipInfoVoList.add(shipInfoVo);
		}
		return shipInfoVoList;
	}

	/**
	 * 处理分数数据
	 *
	 * @param data
	 * @param shipInfoVoList
	 * @return
	 */
	private List<ShipInfoVo> handleScopeShip(List<ShipInfo> data,
			List<ShipInfoVo> shipInfoVoList) {
		// 有分数的船舶信息
		String shipScope = redisClient.get(RedisKeys.Cache.SHIP_SCOPE);
		if (StringUtils.isBlank(shipScope)) {
			return List.of();
		}
		// 有分数的船舶集合
		Gson GSON = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();

		Type shipScopeListType = new TypeToken<List<ShipScope>>() {
		}.getType();
		List<ShipScope> shipScopeList = GSON.fromJson(shipScope,
				shipScopeListType);

		if (CollectionUtils.isEmpty(shipScopeList)) {
			return List.of();
		}

		// 转map
		Map<String, ShipScope> shipScopeMap = shipScopeList.stream()
				.collect(Collectors.toMap(ShipScope::getId, t -> t));

		List<String> shipScopeIdList = shipScopeList.stream()
				.map(ShipScope::getId).toList();

		// data过滤出和shipScopeList 中船舶id一样的数据
		data = data.stream()
				.filter(item -> shipScopeIdList.contains(item.getId()))
				.toList();

		// 过滤出有分数的船舶
		for (ShipInfo shipInfo : data) {
			ShipInfoVo shipInfoVo = new ShipInfoVo();
			BeanUtils.copyProperties(shipInfo, shipInfoVo);
			if (Objects.nonNull(shipScopeMap.get(shipInfo.getId()))) {
				ShipScope scope = shipScopeMap.get(shipInfo.getId());
				BeanUtils.copyProperties(scope, shipInfoVo);
				shipInfoVo.setDpth(scope.getDraft());
			}
			this.handelShipType(shipInfo, shipInfoVo);
			shipInfoVoList.add(shipInfoVo);
		}
		// 有分数的id集合
		List<String> shipScopeIds = shipInfoVoList.stream()
				.map(ShipInfoVo::getId).distinct().toList();
		// 获取有分数的船舶信息的船舶类型
		List<Ship> shipList = super.findByIds(shipScopeIds);
		Map<String, Ship> shipMap = super.buildIdMap(shipList);
		if (CollectionUtils.isNotEmpty(shipInfoVoList)) {
			for (ShipInfoVo shipInfoVo : shipInfoVoList) {
				Ship ship = shipMap.get(shipInfoVo.getId());
				if (Objects.nonNull(ship)) {
					this.handelShipData(shipInfoVo, ship);
				}
			}
		}
		return new ArrayList<>(shipInfoVoList.stream()
				.sorted(Comparator.comparing(ShipInfoVo::getScope).reversed()
						.thenComparing(ShipInfoVo::getCreateTime,
								Comparator
										.nullsLast(Comparator.reverseOrder())))
				.toList());
	}

	/**
	 * 处理船舶信息
	 *
	 * @param shipInfoVo
	 * @param ship
	 */
	private void handelShipData(ShipInfoVo shipInfoVo, Ship ship) {
		shipInfoVo.setTonPure(ship.getTonPure());
		shipInfoVo.setTonCapacity(ship.getTonCapacity());
		shipInfoVo.setBulkCargoShipType(ship.getBulkCargoShipType());
		shipInfoVo.setType(ship.getType());
		shipInfoVo.setName(ship.getName());
		shipInfoVo.setCnname(ship.getCnname());
		if (Objects.nonNull(ship.getLength())) {
			shipInfoVo.setLen(ship.getLength().doubleValue());
		}
		if (Objects.nonNull(ship.getWidth())) {
			shipInfoVo.setWid(ship.getWidth().doubleValue());
		}
	}

	/**
	 * 处理船舶信息
	 *
	 * @param shipInfoVoList
	 * @param shipList
	 */
	private List<ShipInfoVo> handelShipData(List<ShipInfoVo> shipInfoVoList,
			List<Ship> shipList) {
		if (CollectionUtils.isNotEmpty(shipList)) {
			Map<String, Ship> shipMap = super.buildIdMap(shipList);
			if (CollectionUtils.isNotEmpty(shipInfoVoList)) {
				shipInfoVoList.forEach(shipInfoVo -> {
					Ship ship = shipMap.get(shipInfoVo.getId());
					if (Objects.nonNull(ship)) {
						this.handelShipData(shipInfoVo, ship);
					}
				});
			}
		}
		return shipInfoVoList;
	}

	/**
	 * 处理智能推荐船舶信息 当前位置，船名，以及设置船舶状态
	 *
	 * @param shipSmartVo
	 * @param shipInfo
	 */
	private void handleShipSmart(ShipSmartVo shipSmartVo, ShipInfo shipInfo,
			Integer monitoringChannels) {
		shipSmartVo.setCnname(shipInfo.getCnname());
		shipSmartVo.setName(shipInfo.getName());
		if (Objects.nonNull(shipSmartVo.getShip())) {
			String cname = shipSmartVo.getShip().getCnname();
			String name = shipSmartVo.getShip().getName();
			switch (ShipDef.MonitoringChannels.from(monitoringChannels)) {
				// 管理后台船名不加星号
				case BACKGROUND -> {
					if (StringUtils.isNotBlank(cname)
							&& StringUtils.isNotBlank(name)) {
						shipSmartVo.setShipName(cname + "/" + name);
					} else {
						String shipInfoCname = shipInfo.getCnname();
						String shipInfoName = shipInfo.getName();
						if (StringUtils.isNotBlank(shipInfoCname)
								&& StringUtils.isNotBlank(shipInfoName)) {
							shipSmartVo.setShipName(
									shipInfoCname + "/" + shipInfoName);
						}
					}
				}
				// 小程序和PC端船名加星号
				case MINI_PROGRAM -> {
					String cnameResult = maskString(cname);
					String nameResult = maskString(name);
					if (StringUtils.isNotBlank(cnameResult)
							&& StringUtils.isNotBlank(nameResult)) {
						shipSmartVo.setShipName(cnameResult + "/" + nameResult);
					} else {
						String shipInfoCname = maskString(shipInfo.getCnname());
						String shipInfoName = maskString(shipInfo.getName());
						if (StringUtils.isNotBlank(shipInfoCname)
								&& StringUtils.isNotBlank(shipInfoName)) {
							shipSmartVo.setShipName(
									shipInfoCname + "/" + shipInfoName);
						}
					}
				}
			}
		}
		shipSmartVo.setLat(shipInfo.getLat());
		shipSmartVo.setLon(shipInfo.getLon());
		shipSmartVo.setLocation(shipInfo.getLocation());
		shipSmartVo.setStatus(shipInfo.getStatus());
	}

	/**
	 * 查询出有联系方式，没有船运单，如有船运单只在卸货中、已卸货、已清仓、已完成状态的船舶数据
	 *
	 * @param shipList
	 * @return
	 */
	private List<Ship> getCombinedList(List<Ship> shipList) {
		// 查询出船运单中所有船舶id并去重，去重后存入Set
		Set<String> shipIdsSet = transportOrderShipService.findAll().stream()
				.map(TransportOrderShip::getShipId).collect(Collectors.toSet());
		// 查询出船运单不在卸货中、已卸货、已清仓、已完成状态的船舶id
		List<Integer> states = List.of(
				TransportOrderShipDef.State.DURING_UNLOADING.getCode(),
				TransportOrderShipDef.State.DISCHARGED.getCode(),
				TransportOrderShipDef.State.COMPLETED.getCode());
		Set<String> shipIdNotInStatesSet = transportOrderShipService
				.findNotInStates(states).stream()
				.map(TransportOrderShip::getShipId).collect(Collectors.toSet());
		// 查询出所有船舶中联系方式不为空的数据有船运单且船运单只在卸货中、已卸货、已清仓、已完成状态的船舶
		Set<String> commonShipIds = shipIdsSet.stream()
				.filter(shipId -> !shipIdNotInStatesSet.contains(shipId))
				.collect(Collectors.toSet());
		// 查询出所有船舶中联系方式不为空的数据且没有船运单的数据
		List<Ship> matchedList1 = shipList.stream()
				.filter(ship -> !shipIdsSet.contains(ship.getId())).toList();
		// 查询出所有船舶中联系方式不为空的数据有船运单且船运单只在卸货中、已卸货、已清仓、已完成状态的船舶
		List<Ship> matchedList2 = shipList.stream()
				.filter(ship -> commonShipIds.contains(ship.getId())).toList();
		// 使用Stream.concat()将两个列表合并为一个流，然后收集为列表
		return Stream.concat(matchedList1.stream(), matchedList2.stream())
				.toList();
	}

	/**
	 * 处理船舶位置
	 *
	 * @param shipInfo
	 */
	private void handelLocation(ShipInfo shipInfo) {
		// 船舶的经度 初始化为NaN表示未设置
		double lon1 = Double.NaN;
		// 船舶的纬度
		double lat1 = Double.NaN;
		if (Objects.nonNull(shipInfo.getLon())) {
			lon1 = shipInfo.getLon();
		}
		if (Objects.nonNull(shipInfo.getLat())) {
			lat1 = shipInfo.getLat();
		}
		Response<Geocoder> response = null;
		if (!(Double.isNaN(lat1) || Double.isNaN(lon1))) {
			try {
				response = tianDiTuClient
						.geocode(
								new GeocodeRequest(shipInfo.getLon(),
										shipInfo.getLat(), 1).toString(),
								"geocode");

			} catch (Exception e) {
				log.error("调用天地图接口异常: {}", e.getMessage(), e);
			}
		}
		if (Objects.nonNull(response) && ("0").equals(response.getStatus())) {
			Geocoder geocoder = response.getResult();
			if (Objects.nonNull(geocoder)) {
				AddressComponent addressComponent = geocoder
						.getAddressComponent();
				if (Objects.nonNull(addressComponent)) {
					shipInfo.setLocation(addressComponent.getProvince()
							+ addressComponent.getCity()
							+ addressComponent.getCounty());
				}
			}
		}
	}

	/**
	 * 根据船舶对应的船运单处理智能推荐船舶的常跑航线 如果船主没有填写常跑航线信息 取船舶最新一条的船运单始发港到目的港的航线
	 *
	 * @param
	 */
	private List<TransportOrderShip> handelTransportOrderShips(
			List<TransportOrderShip> transportOrderShips,
			ShipSmartVo shipSmartVo) {
		// 如果此船舶承运商选择的常跑航线没有填写 取船舶最新一条的船运单始发港到目的港
		if (CollectionUtils.isEmpty(shipSmartVo.getFrequentShipRoutes())) {
			if (CollectionUtils.isNotEmpty(transportOrderShips)) {
				TransportOrderShip transportOrderShip = transportOrderShips
						.stream().findFirst().orElse(null);
				if (Objects.nonNull(transportOrderShip)) {
					shipSmartVo.setSourcePortId(
							transportOrderShip.getSourcePortId());
					shipSmartVo.setSourcePortName(
							transportOrderShip.getSourcePortName());
					shipSmartVo.setDestinationPortId(
							transportOrderShip.getDestinationPortId());
					shipSmartVo.setDestinationPortName(
							transportOrderShip.getDestinationPortName());
				}
			}
		}
		return transportOrderShips;
	}

	/**
	 * 判断船舶是否满足载重吨条件
	 *
	 * @param ship
	 * @param shipSmartVo
	 * @param shippingRequirementCustomer
	 */
	private void handleTonCapacity(Ship ship, ShipSmartVo shipSmartVo,
			ShippingRequirementCustomer shippingRequirementCustomer) {
		if (Objects.nonNull(ship.getTonCapacity())) {
			Integer dwtMax = shippingRequirementCustomer.getDwtMax();
			Integer dwtMin = shippingRequirementCustomer.getDwtMin();
			if (Objects.nonNull(dwtMax) && Objects.nonNull(dwtMin)) {
				// 填写了船舶吨位要求,船舶信息内维护的载重吨 需要满足于这个范围
				if (ship.getTonCapacity() >= dwtMin
						&& ship.getTonCapacity() <= dwtMax) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 7);
				}
			} else if (Objects.nonNull(shippingRequirementCustomer.getTon())) {
				// 未填写吨位要求，载重吨需大于运货吨数才算满足
				if (ship.getTonCapacity() > shippingRequirementCustomer
						.getTon()) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 7);
				}
			}
		}
	}

	/**
	 * 处理智能推荐的船舶的 常跑航线
	 *
	 * @param ship
	 * @param shipSmartVo
	 * @param frequentShipRouteList
	 * @param portMap
	 * @param shippingRequirementCustomer
	 */
	private void handleFrequentShipRoutes(Ship ship, ShipSmartVo shipSmartVo,
			List<FrequentShipRoute> frequentShipRouteList,
			Map<Long, Port> portMap,
			ShippingRequirementCustomer shippingRequirementCustomer) {
		if (Objects.nonNull(ship.getCarrier())) {
			// 有满足常跑航线非必要条件的数据放这个列表里面
			List<FrequentShipRoute> frequentShipRoutesList = new ArrayList<>();
			// 根据船舶承运商id查询常跑航线中始发地和目的地不为空的数据
			List<FrequentShipRoute> frequentShipRoutes = frequentShipRouteList
					.stream().filter(route -> Objects
							.equals(route.getCaptainId(), ship.getCarrier()))
					.toList();
			Port sourcePort = portMap
					.get(shippingRequirementCustomer.getSourcePortId());
			Port destinationPort = portMap
					.get(shippingRequirementCustomer.getDestinationPortId());
			if (CollectionUtils.isNotEmpty(frequentShipRoutes)
					&& Objects.nonNull(sourcePort)
					&& Objects.nonNull(destinationPort)) {
				// 货主找船需求中始发港和目的港的二级地址
				String sourcePortCityCode = sourcePort.getCityCode();
				String destinationPortCityCode = destinationPort.getCityCode();
				for (FrequentShipRoute route : frequentShipRoutes) {
					// 获取常跑航线的二级地址
					String routeSourceCityCode = route.getSourceCityCode();
					String routeDestinationCityCode = route
							.getDestinationCityCode();
					// 去除常跑航线中二级地址最后两位，因为码头对应的二级地址只有四位
					String routeSourceCode = routeSourceCityCode.substring(0,
							routeSourceCityCode.length() - 2);
					String routeDestinationCode = routeDestinationCityCode
							.substring(0,
									routeDestinationCityCode.length() - 2);
					if (Objects.equals(sourcePortCityCode, routeSourceCode)
							&& Objects.equals(destinationPortCityCode,
									routeDestinationCode)) {
						frequentShipRoutesList.add(route);
						// 满足常跑航线条件 加3分 非必要条件数量增加
						shipSmartVo.setCount(shipSmartVo.getCount() + 1);
						shipSmartVo.setScore(shipSmartVo.getScore() + 3);
						break;
					}
				}
			}
			if (CollectionUtils.isNotEmpty(frequentShipRoutesList)) {
				// 如果满足常跑航线条件，则将满足条件的常跑航线赋值给 智能匹配船舶的常跑航线
				shipSmartVo.setFrequentShipRoutes(frequentShipRoutesList);
			} else {
				// 如果没有满足常跑航线条件，则将船主填写的常跑航线信息赋值进去
				shipSmartVo.setFrequentShipRoutes(frequentShipRoutes);
			}
		}
	}

	/**
	 * 判断是否满足历史运单条件 取所有船运单始发港到目的港的航线，需要至少有一条与找船需求填写的始发港目的港的航线一致
	 */
	private void handleOrderShipList(List<TransportOrderShip> orderShipList,
			ShipSmartVo shipSmartVo,
			ShippingRequirementCustomer shippingRequirementCustomer) {
		if (CollectionUtils.isNotEmpty(orderShipList)) {
			for (TransportOrderShip orderShip : orderShipList) {
				if (Objects.equals(
						shippingRequirementCustomer.getSourcePortId(),
						orderShip.getSourcePortId())
						&& Objects.equals(
								shippingRequirementCustomer
										.getDestinationPortId(),
								orderShip.getDestinationPortId())) {
					shipSmartVo.setCount(shipSmartVo.getCount() + 1);
					shipSmartVo.setScore(shipSmartVo.getScore() + 1);
					break;
				}
			}
		}
	}

	/**
	 * 处理最终筛选出来的 智能推荐的船舶 数据
	 *
	 * @param shipSmartVos
	 * @return
	 */
	private List<ShipSmartVo> filterAndSort(List<ShipSmartVo> shipSmartVos) {
		// 找出满足必要条件和至少满足三个非必要条件的数据
		List<ShipSmartVo> filteredShipSmartVos = new ArrayList<>(shipSmartVos
				.stream().filter(vo -> vo.getCount() >= 3).toList());
		// 对列表进行排序 先按count降序排序， count相同时，按score降序排序
		filteredShipSmartVos.sort(Comparator
				.comparing(ShipSmartVo::getCount, Comparator.reverseOrder())
				.thenComparing(ShipSmartVo::getScore,
						Comparator.reverseOrder()));
		return filteredShipSmartVos;
	}

	/**
	 * 处理船运单状态 船舶的监控的货仓状态为装载中，运单状态是待装货，运单状态变成装货中
	 *
	 * @param shipIdDeviceCapturePicRecMap
	 */
	private void handelTransportOrderShipState(
			Map<String, DeviceCapturePicRec> shipIdDeviceCapturePicRecMap) {
		// 船舶的监控的货仓状态为装载中，运单状态是待装货，运单状态变成装货中
		if (shipIdDeviceCapturePicRecMap.isEmpty()) {
			return;
		}
		Set<String> shipIds = shipIdDeviceCapturePicRecMap.keySet();

		List<String> orderShipIds = new ArrayList<>();

		// 根据船舶id查询待装货的运单，根据船舶id进行分组
		Map<String, List<String>> transportOrderShipMap = transportOrderShipService
				.findByShipIdsAndState(shipIds,
						TransportOrderShipDef.State.TO_BE_LOADED.getCode())
				.stream()
				.collect(Collectors.groupingBy(TransportOrderShip::getShipId,
						Collectors.mapping(TransportOrderShip::getId,
								Collectors.toList())));

		if (transportOrderShipMap.isEmpty()) {
			return;
		}

		shipIdDeviceCapturePicRecMap.forEach((shipId, recognize) -> {
			// 识别结果是装载中
			if (DeviceCapturePicDef.RecognizeResult.LOADING.getCode()
					.equals(recognize.getRecognizeResult())) {
				List<String> transportOrderShipId = transportOrderShipMap
						.get(shipId);
				if (CollectionUtils.isNotEmpty(transportOrderShipId)) {
					orderShipIds.addAll(transportOrderShipId);
				}
			}
		});
		transportOrderShipService.updateStateByIds(orderShipIds,
				TransportOrderShipDef.State.DURING_LOADING.getCode());
	}

	/**
	 * 批量更新船舶状态
	 *
	 * @param ships
	 */
	private void batchUpdateState(List<Ship> ships) {
		// 批量更新货仓状态
		ships.forEach(e -> {
			LambdaUpdateWrapper<Ship> updateWrapper = Wrappers
					.lambdaUpdate(Ship.class);
			updateWrapper.set(Ship::getWarehouseState, e.getWarehouseState());
			updateWrapper.set(Ship::getRemainingLayTime,
					e.getRemainingLayTime());
			updateWrapper.eq(Ship::getId, e.getId());
			repository.update(updateWrapper);
		});
	}

	/**
	 * 处理手动分页
	 *
	 * @param page
	 * @param size
	 * @param transportOrderShipList
	 * @return
	 */
	private com.zhihaoscm.common.bean.page.Page<TransportOrderShip> handelPage(
			Integer page, Integer size,
			List<TransportOrderShip> transportOrderShipList) {
		// 计算总数据量和总页数
		int total = transportOrderShipList.size();
		int totalPage = total == 0 ? 1 : (total + size - 1) / size;

		// 确保页码在有效范围内
		if (page < 1) {
			page = 1;
		} else if (page > totalPage) {
			page = totalPage;
		}

		// 计算需要跳过的数据量
		int skip = (page - 1) * size;

		// 如果总数据为 0，直接返回一个空的分页对象
		if (total == 0) {
			return new com.zhihaoscm.common.bean.page.Page<>(List.of(), page,
					size, total, totalPage);
		}

		// 使用 stream 流进行分页
		List<TransportOrderShip> pagedList = transportOrderShipList.stream()
				.skip(skip).limit(size).toList();

		return new com.zhihaoscm.common.bean.page.Page<>(pagedList, page, size,
				total, totalPage);
	}

	class CaptureAndUpdate implements Runnable {
		List<Device> devices;

		CaptureAndUpdate(List<Device> devices) {
			this.devices = devices;
		}

		@Override
		public void run() {
			// 船舶id和货仓状态的映射
			Map<String, DeviceCapturePicRec> shipIdDeviceCapturePicRecMap = new HashMap<>();
			Map<String, String> deivceIdAndShipIdMap = devices.stream()
					.collect(Collectors.toMap(Device::getSerialNo,
							Device::getMasterId, (k1, k2) -> k1));
			List<Ship> ships = devices.stream().map(d -> {
				log.info("设备{}识别处理开始", d.getSerialNo());
				// 是否发生抓拍异常
				boolean happenException = false;
				// 识别结果
				DeviceCapturePicRec recognize = new DeviceCapturePicRec();
				recognize.setRecognizeResult(
						DeviceCapturePicDef.RecognizeResult.UNKNOWN.getCode());
				// 构造设备的抓拍请求参数
				CaptureRequest captureRequest = new CaptureRequest(
						d.getSerialNo(), 1);
				Ship ship = new Ship();
				ship.setId(deivceIdAndShipIdMap.get(d.getSerialNo()));
				BaseResponse<CaptureResponse> response = null;
				try {
					// 设备抓拍
					response = hkwsClient.capture(captureRequest);
				} catch (Exception e) {
					log.error("序列号为{}的设备抓拍异常 -> {}", d.getSerialNo(),
							e.getMessage(), e);
					ship.setWarehouseState(
							DeviceCapturePicDef.RecognizeResult.UNKNOWN
									.getCode());
					happenException = true;
				}
				if (!happenException) {
					if (HkwsDef.BackCode.OK.match(response.getCode())
							|| HkwsDef.BackCode.SUCCESS
									.match(response.getCode())) {
						// 调用 AI 算法识别货仓状态
						recognize = deviceCapturePicRecService.recognize(
								d.getSerialNo(),
								response.getData().getPicUrl());
						ship.setWarehouseState(recognize.getRecognizeResult());
						ship.setRemainingLayTime(
								recognize.getRemainingLayTime());
					} else {
						log.error("序列号为{}的设备AI推理异常 -> {}", d.getSerialNo(),
								response.getMessage());
						ship.setWarehouseState(
								DeviceCapturePicDef.RecognizeResult.UNKNOWN
										.getCode());
					}
				}
				if (CommonDef.Symbol.YES.match(d.getOnline())) {
					// 离线的设备不发送告警信息
					shipIdDeviceCapturePicRecMap.put(ship.getId(), recognize);
				}
				log.info("设备{}识别处理结束，识别信息：{}", d.getSerialNo(), recognize);
				return ship;
			}).toList();
			// 处理船舶异常告警
			shipService.handleShipReport(shipIdDeviceCapturePicRecMap);

			batchUpdateState(ships);

			// 处理船运单状态
			handelTransportOrderShipState(shipIdDeviceCapturePicRecMap);

		}
	}

	/**
	 * 转换船舶信息 在后台船舶关注使用
	 * 
	 * @param shipInfoList
	 * @return
	 */
	@Override
	public List<ShipInfoVo> handleShipInfo(List<ShipInfo> shipInfoList) {
		if (Objects.isNull(shipInfoList)) {
			return List.of();
		}
		// 有分数的船舶信息
		String shipScope = redisClient.get(RedisKeys.Cache.SHIP_SCOPE);

		List<ShipScope> shipScopeList = new ArrayList<>();
		if (StringUtils.isNotBlank(shipScope)) {
			// 有分数的船舶集合
			Gson GSON = new GsonBuilder().registerTypeAdapter(
					LocalDateTime.class, new LocalDateTimeAdapter()).create();
			Type shipScopeListType = new TypeToken<List<ShipScope>>() {
			}.getType();
			shipScopeList = GSON.fromJson(shipScope, shipScopeListType);
		}

		List<ShipInfoVo> result = new ArrayList<>();
		for (ShipInfo shipInfo : shipInfoList) {
			ShipInfoVo shipInfoVo = new ShipInfoVo();
			BeanUtils.copyProperties(shipInfo, shipInfoVo);
			// 类型处理
			this.handelShipType(shipInfo, shipInfoVo);

			if (CollectionUtils.isNotEmpty(shipScopeList)) {
				// 转map
				Map<String, ShipScope> shipScopeMap = shipScopeList.stream()
						.collect(Collectors.toMap(ShipScope::getId, t -> t));
				if (Objects.nonNull(shipScopeMap.get(shipInfo.getId()))) {
					ShipScope scope = shipScopeMap.get(shipInfo.getId());
					BeanUtils.copyProperties(scope, shipInfoVo);
				} else {
					shipInfoVo.setScope(0);
					shipInfoVo.setExistDevice(CommonDef.Symbol.NO.getCode());
					shipInfoVo.setExistMobile(CommonDef.Symbol.NO.getCode());
					shipInfoVo
							.setExistWaterGauge(CommonDef.Symbol.NO.getCode());
				}
			}
			// 目前在船舶关注使用到了 默认设置关注
			shipInfoVo.setIsFollowed(CommonDef.Symbol.YES.getCode());
			result.add(shipInfoVo);
		}
		List<Ship> shipList = findByIds(
				shipInfoList.stream().map(ShipInfo::getId).distinct().toList());
		// 设置船舶信息
		this.handelShipData(result, shipList);
		return result;
	}

	@Override
	public void recognizeIdCard() {
		// 找出有身份证正面或身份证反面的船舶
		LambdaQueryWrapper<Ship> queryWrapper = Wrappers.lambdaQuery(Ship.class)
				.eq(Ship::getDel, CommonDef.Symbol.NO.getCode())
				.apply("(" + "certificate_files IS NOT NULL "
						+ "AND certificate_files != '' " + "AND ("
						+ "(JSON_EXTRACT(certificate_files, '$.captainsIdCardFrontFile') IS NOT NULL "
						+ "AND JSON_UNQUOTE(JSON_EXTRACT(certificate_files, '$.captainsIdCardFrontFile')) != 'null') "
						+ "OR "
						+ "(JSON_EXTRACT(certificate_files, '$.captainsIdCardBackFile') IS NOT NULL "
						+ "AND JSON_UNQUOTE(JSON_EXTRACT(certificate_files, '$.captainsIdCardBackFile')) != 'null')"
						+ ")" + ")");
		List<Ship> ships = repository.selectList(queryWrapper);

		if (CollectionUtils.isNotEmpty(ships)) {
			for (Ship ship : ships) {
				CertificateFiles certificateFiles = ship.getCertificateFiles();
				if (Objects.nonNull(certificateFiles)) {
					// 识别身份证
					IdCardInfo idCardInfo = new IdCardInfo();
					// 人像
					Long captainsIdCardBackFile = certificateFiles
							.getCaptainsIdCardBackFile();
					if (Objects.nonNull(captainsIdCardBackFile)) {
						fileService.findOne(captainsIdCardBackFile)
								.ifPresent(file -> {
									com.zhihaoscm.aliyun.ocr.sdk.res.IdCardInfo info = ocrService
											.recognizeIdCardBackAndFace(file)
											.orElse(null);
									if (Objects.nonNull(info)) {
										IdCardFaceInfo idCardFaceInfo = info
												.getIdCardFaceInfo();
										if (Objects.nonNull(idCardFaceInfo)) {
											idCardInfo.setName(
													idCardFaceInfo.getName());
											idCardInfo
													.setIdNumber(idCardFaceInfo
															.getIdNumber());
											idCardInfo.setSex(
													idCardFaceInfo.getSex());
											idCardInfo.setAddress(idCardFaceInfo
													.getAddress());
											String birthDate = DateUtils
													.convertDate(DateUtils
															.convertDate(
																	idCardFaceInfo
																			.getBirthDate(),
																	"yyyy年M月d日"),
															"yyyy-MM-dd");
											idCardInfo.setBirthDate(LocalDate
													.parse(birthDate,
															DateTimeFormatter
																	.ofPattern(
																			"yyyy-MM-dd"))
													.atStartOfDay());

										}
										IdCardBackInfo idCardBackInfo = info
												.getIdCardBackInfo();
										if (Objects.nonNull(idCardBackInfo)) {
											String validPeriod = idCardBackInfo
													.getValidPeriod();
											String[] parts = validPeriod
													.split("-");
											if (parts.length >= 2) {
												// 第一个部分
												String firstPart = parts[0];
												idCardInfo.setIdCardStartDate(
														DateUtils.convertDate(
																firstPart,
																"yyyy.M.d")
																.atStartOfDay());
												// 第二个部分
												String secondPart = parts[1];
												if (!"长期".equals(secondPart)) {
													idCardInfo.setIdCardEndDate(
															DateUtils
																	.convertDate(
																			secondPart,
																			"yyyy.M.d")
																	.atStartOfDay());
												} else {
													idCardInfo.setIdCardEndDate(
															DateUtils
																	.convertDate(
																			"2099.12.31",
																			"yyyy.M.d")
																	.atStartOfDay());
												}
											}
										}

									}
								});
					}
					// 国徽
					Long captainsIdCardFrontFile = certificateFiles
							.getCaptainsIdCardFrontFile();
					if (Objects.nonNull(captainsIdCardFrontFile)) {
						fileService.findOne(captainsIdCardFrontFile)
								.ifPresent(file -> {
									com.zhihaoscm.aliyun.ocr.sdk.res.IdCardInfo info = ocrService
											.recognizeIdCardBackAndFace(file)
											.orElse(null);
									if (Objects.nonNull(info)) {
										IdCardFaceInfo idCardFaceInfo = info
												.getIdCardFaceInfo();
										if (Objects.nonNull(idCardFaceInfo)) {
											idCardInfo.setName(
													idCardFaceInfo.getName());
											idCardInfo
													.setIdNumber(idCardFaceInfo
															.getIdNumber());
											idCardInfo.setSex(
													idCardFaceInfo.getSex());
											idCardInfo.setAddress(idCardFaceInfo
													.getAddress());
											String birthDate = DateUtils
													.convertDate(DateUtils
															.convertDate(
																	idCardFaceInfo
																			.getBirthDate(),
																	"yyyy年M月d日"),
															"yyyy-MM-dd");
											idCardInfo.setBirthDate(LocalDate
													.parse(birthDate,
															DateTimeFormatter
																	.ofPattern(
																			"yyyy-MM-dd"))
													.atStartOfDay());

										}
										IdCardBackInfo idCardBackInfo = info
												.getIdCardBackInfo();
										if (Objects.nonNull(idCardBackInfo)) {
											String validPeriod = idCardBackInfo
													.getValidPeriod();
											String[] parts = validPeriod
													.split("-");
											if (parts.length >= 2) {
												// 第一个部分
												String firstPart = parts[0];
												idCardInfo.setIdCardStartDate(
														DateUtils.convertDate(
																firstPart,
																"yyyy.M.d")
																.atStartOfDay());
												// 第二个部分
												String secondPart = parts[1];
												if (!"长期".equals(secondPart)) {
													idCardInfo.setIdCardEndDate(
															DateUtils
																	.convertDate(
																			secondPart,
																			"yyyy.M.d")
																	.atStartOfDay());
												} else {
													idCardInfo.setIdCardEndDate(
															DateUtils
																	.convertDate(
																			"2099.12.31",
																			"yyyy.M.d")
																	.atStartOfDay());
												}
											}
										}

									}
								});
					}

					ship.setIdCardInfo(idCardInfo);
					super.updateAllProperties(ship);

				}
			}
		}
	}
}
