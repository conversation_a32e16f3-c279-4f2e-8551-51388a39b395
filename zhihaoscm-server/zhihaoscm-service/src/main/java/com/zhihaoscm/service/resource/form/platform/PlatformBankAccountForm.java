package com.zhihaoscm.service.resource.form.platform;

import org.hibernate.validator.constraints.Length;

import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.domain.bean.entity.PlatformBankAccount;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "平台银行账户表单对象")
public class PlatformBankAccountForm {

	@Schema(title = "开户名称")
	@NotNull(message = ErrorCode.CODE_30105001)
	@Length(min = 1, max = 25, message = ErrorCode.CODE_30105001)
	private String name;

	@Schema(title = "银行账户")
	@NotNull(message = ErrorCode.CODE_30105002)
	@Pattern(regexp = "\\d{8,30}", message = ErrorCode.CODE_30105002)
	private String account;

	@Schema(title = "开户银行")
	@NotNull(message = ErrorCode.CODE_30105003)
	@Length(min = 1, max = 128, message = ErrorCode.CODE_30105003)
	private String openingBank;

	@Schema(title = "使用场景")
	private ArrayLong useType;

	public PlatformBankAccount convertToEntity(
			PlatformBankAccount platformBankAccount) {
		this.update(platformBankAccount);
		return platformBankAccount;
	}

	public void update(PlatformBankAccount platformBankAccount) {
		platformBankAccount.setName(this.name);
		platformBankAccount.setAccount(this.account);
		platformBankAccount.setOpeningBank(this.openingBank);
		platformBankAccount.setUseType(this.useType);
	}
}
