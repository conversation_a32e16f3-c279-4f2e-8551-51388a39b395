package com.zhihaoscm.service.resource.admin.ship;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.GeoPoint;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.dto.ShipDto;
import com.zhihaoscm.domain.bean.dto.ShipHistoryDto;
import com.zhihaoscm.domain.bean.entity.Device;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.DeviceDef;
import com.zhihaoscm.domain.meta.biz.LogDef;
import com.zhihaoscm.domain.meta.biz.ShipDef;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.core.service.DeviceService;
import com.zhihaoscm.service.core.service.ShipService;
import com.zhihaoscm.service.resource.form.ship.*;
import com.zhihaoscm.service.resource.validator.ship.ShipValidator;
import com.zhihaoscm.ships66.sdk.response.ShipInfo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "船舶管理", description = "船舶管理API")
@RestController
@RequestMapping(value = "/ship")
public class ShipResource {

	@Autowired
	private ShipService shipService;

	@Autowired
	private ShipValidator validator;

	@Autowired
	private DeviceService deviceService;

	@Operation(summary = "查询船舶列表")
	@GetMapping(value = "/paging")
	@Secured({ AdminPermissionDef.SHIP_R, AdminPermissionDef.SHIP_W })
	public ApiResponse<Page<ShipVo>> paging(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "排序字段") @RequestParam(value = "sortKey", required = false) String sortKey,
			@Parameter(description = "排序顺序, ASC升序,DESC倒序") @RequestParam(value = "sortOrder", required = false) String sortOrder,
			@Parameter(description = "查询参数") @RequestParam(value = "param", required = false) String param,
			@Parameter(description = "联系方式维护人") @RequestParam(value = "contactMaintainer", required = false) String contactMaintainer,
			@Parameter(description = "承运商用户id") @RequestParam(value = "carrier", required = false) Long carrier,
			@Parameter(description = "状态") @RequestParam(value = "state", required = false) List<Integer> state,
			@Parameter(description = "是否关联设备 1:全部 2:是 3:否") @RequestParam(value = "relatedDevice", required = false) Integer relatedDevice,
			@Parameter(description = "货仓状态") @RequestParam(required = false) Integer warehouseState,
			@Parameter(description = "是否维护水尺 1:是 0:否") @RequestParam(required = false) Integer existWaterGauge,
			@Parameter(description = "是否有联系方式 1:是 0:否") @RequestParam(required = false) Integer existMobile) {
		return new ApiResponse<>(PageUtil.convert(shipService.paging(page, size,
				sortKey, sortOrder, param, contactMaintainer, carrier, state,
				relatedDevice, warehouseState, existWaterGauge, existMobile)));
	}

	@Operation(summary = "船舶下拉列表")
	@GetMapping("/selector")
	public ApiResponse<Page<Ship>> selector(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "状态 0未认证 1已认证") @RequestParam(value = "state", required = false) Integer state,
			@Parameter(description = "船舶名或者mmsi") @RequestParam(value = "keyword", required = false) String keyword) {
		return new ApiResponse<>(PageUtil
				.convert(shipService.selector(page, size, state, keyword)));
	}

	@Operation(summary = "船舶下拉列表")
	@GetMapping("/selector-vo")
	public ApiResponse<Page<ShipVo>> selectorVo(
			@RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@RequestParam(value = "state", required = false) Integer state,
			@RequestParam(value = "keyword", required = false) String keyword) {
		return new ApiResponse<>(PageUtil
				.convert(shipService.selectorVo(page, size, state, keyword)));
	}

	@Operation(summary = "安装推广船舶下拉列表")
	@GetMapping("/install/selector")
	public ApiResponse<Page<ShipVo>> installSelector(
			@RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@RequestParam(value = "searchParam", required = false) String searchParam) {
		return new ApiResponse<>(PageUtil
				.convert(shipService.installSelector(page, size, searchParam)));
	}

	@Operation(summary = "查询船舶详情")
	@GetMapping(value = "/vo/{id}")
	@Secured({ AdminPermissionDef.SHIP_R, AdminPermissionDef.SHIP_W })
	public ApiResponse<ShipVo> findVoById(
			@PathVariable(value = "id") String id) {
		validator.validateExist(id);
		return new ApiResponse<>(shipService.findVoById(id).orElse(null));
	}

	@Operation(summary = "关键字查询")
	@GetMapping(value = "/vo/keyword")
	@Secured({ AdminPermissionDef.LF_SHIP })
	public ApiResponse<List<ShipVo>> findVoByKeyword(
			@RequestParam String keyword) {
		return new ApiResponse<>(shipService.findVoByKeyword(keyword));
	}

	@Operation(summary = "区域查船")
	@PostMapping(value = "/extent")
	@Secured({ AdminPermissionDef.LF_SHIP, AdminPermissionDef.LF_SHIP_REGION })
	public ApiResponse<List<ShipInfoVo>> findByExtent(
			@RequestBody ShipExtendForm from) {
		validator.validateExtent(from);
		ShipDto dto = from.convertToDto();
		List<ShipInfo> shipInfoByExtent = shipService.findShipInfoByExtent(
				dto.getLeftTopPoint(), dto.getRightTopPoint(),
				dto.getRightBottomPoint(), dto.getLeftBottomPoint(),
				dto.getType(), dto.getTime());
		return new ApiResponse<>(shipService.findByExtent(shipInfoByExtent));
	}

	@Operation(summary = "区域查船带分数")
	@PostMapping(value = "/extent/scope")
	@Secured({ AdminPermissionDef.LF_SHIP_REGION })
	public ApiResponse<List<ShipInfoVo>> findByExtentAndScope(
			@RequestBody ShipExtendForm from) {
		validator.validateExtent(from);
		ShipDto dto = from.convertToDto();
		List<ShipInfo> shipInfoByExtent = shipService.findShipInfoByExtent(
				dto.getLeftTopPoint(), dto.getRightTopPoint(),
				dto.getRightBottomPoint(), dto.getLeftBottomPoint(),
				dto.getType(), dto.getTime());
		List<ShipInfoVo> list = shipService
				.findByExtentAndScope(shipInfoByExtent);
		return new ApiResponse<>(list);
	}

	@Operation(summary = "自定义查船")
	@PostMapping(value = "/extent/custom")
	@Secured({ AdminPermissionDef.LF_SHIP_CUSTOMIZE })
	public ApiResponse<List<ShipInfoVo>> findByCustomExtent(
			@RequestBody ShipExtendCustomForm from) {
		validator.validateExtentCustom(from);
		List<GeoPoint> geoPointList = from.getGeoPointList();
		return new ApiResponse<>(shipService.findByCustomExtent(geoPointList,
				from.getType(), from.getTime()));
	}

	@Operation(summary = "查询船舶轨迹")
	@GetMapping(value = "/aisTrack")
	@Secured({ AdminPermissionDef.LF_SHIP, AdminPermissionDef.LF_SHIP_REGION })
	public ApiResponse<List<ShipTrajectoryVo>> findByAisTrack(
			@Parameter(description = "mmsi") @RequestParam(value = "mmsi") String mmsi,
			@Parameter(description = "开始时间戳") @RequestParam(value = "start") Long start,
			@Parameter(description = "结束时间戳") @RequestParam(value = "end") Long end) {
		return new ApiResponse<>(shipService.findByAisTrack(mmsi, start, end));
	}

	@Operation(summary = "根据关键字查询")
	@GetMapping(value = "/keyword")
	@Secured({ AdminPermissionDef.LF_SHIP })
	public ApiResponse<List<Ship>> findByKeyword(
			@Parameter(description = "关键字") @RequestParam String keyword,
			@Parameter(description = "限制 默认 10 条") @RequestParam(value = "limit", required = false) Integer limit,
			@Parameter(description = "类型 （船舶 SHIP 航标 VMN 网位仪 NETSONDE)") @RequestParam(value = "type", required = false, defaultValue = "SHIP") String type) {
		return new ApiResponse<>(
				shipService.findByKeyword(keyword, limit, type));
	}

	@Operation(summary = "查询并校验船舶")
	@GetMapping("/find-validate/{id}")
	@Secured({ AdminPermissionDef.SHIP_R, AdminPermissionDef.SHIP_W })
	public ApiResponse<ShipVo> findByIdAndValidate(
			@PathVariable(value = "id") String id) {
		validator.validateMMSI(id);
		return new ApiResponse<>(
				shipService.findByIdAndValidate(id).orElse(null));
	}

	@Operation(summary = "查询并创建船舶")
	@GetMapping("/find-create/{id}")
	@Secured({ AdminPermissionDef.LF_SHIP, AdminPermissionDef.SHIP_R,
			AdminPermissionDef.LF_SHIP_REGION })
	public ApiResponse<ShipInfoVo> findByIdAndCreate(
			@PathVariable(value = "id") String id) {
		validator.validateMMSI(id);
		ShipInfo shipInfo = shipService.findByAsi(id);
		return new ApiResponse<>(
				shipService
						.findByIdAndCreate(shipInfo,
								Objects.requireNonNull(
										UserContextHolder.getUser()).getId(),
								ShipDef.MonitoringChannels.BACKGROUND.getCode())
						.orElse(null));
	}

	@Operation(summary = "查询船舶下绑定的设备列表")
	@GetMapping(value = "/find/devices/{id}")
	@Secured({ AdminPermissionDef.SHIP_R, AdminPermissionDef.SHIP_W })
	public ApiResponse<List<Device>> findDevicesById(
			@PathVariable(value = "id") String id) {
		validator.validateExist(id);
		return new ApiResponse<>(deviceService.findByMasterIdAndTransportType(
				id, DeviceDef.TransportType.SHIP.getCode()));
	}

	@Operation(summary = "识别内河船舶检验报告")
	@GetMapping(value = "/recognize-ship-inspection-report")
	@Secured({ AdminPermissionDef.SHIP_R, AdminPermissionDef.SHIP_W })
	public ApiResponse<ShipInspectionReportVo> recognizeShipInspectionReport(
			@Parameter(description = "识别内河船舶检验报告fileIds") @RequestParam(value = "fileIds") List<Long> fileIds) {
		return new ApiResponse<>(
				shipService.recognizeShipInspectionReport(fileIds));
	}

	@Operation(summary = "船舶营业运输证提取")
	@PostMapping("/get-ship-business-transport")
	@Secured({ AdminPermissionDef.SHIP_R, AdminPermissionDef.SHIP_W })
	public ApiResponse<ShipBusinessTransportVo> getShipBusinessTransport(
			@RequestParam Long fileId) {
		return new ApiResponse<>(
				shipService.getShipBusinessTransport(fileId).orElse(null));
	}

	@Operation(summary = "识别船舶船员适任证书")
	@PostMapping("/recognize-crew-certificate")
	@Secured({ AdminPermissionDef.SHIP_R, AdminPermissionDef.SHIP_W })
	public ApiResponse<String> recognizeCrewCertificate(
			@RequestParam Long fileId) {
		return new ApiResponse<>(
				shipService.recognizeCrewCertificate(fileId).orElse(null));
	}

	@Operation(summary = "查询承运商已认证船舶列表")
	@GetMapping("/carrier")
	public ApiResponse<List<Ship>> findByCarrier(
			@RequestParam(value = "customerId") Long customerId,
			@RequestParam(value = "keyword", required = false) String keyword) {
		return new ApiResponse<>(
				shipService.findByCarrier(customerId, keyword));
	}

	@Operation(summary = "新增船舶")
	@PostMapping
	@Secured({ AdminPermissionDef.SHIP_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = LogDef.SHIP_ADD, type = LogDef.LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO, bizNo = "{{#_ret.getData().getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getCnname()}}") })
	public ApiResponse<Ship> create(@Validated @RequestBody ShipForm form) {
		validator.validateCreate(form);
		return new ApiResponse<>(shipService.create(form.convertToEntity()));
	}

	@Operation(summary = "更新船舶")
	@PutMapping(value = "/{id}")
	@Secured({ AdminPermissionDef.SHIP_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.SHIP_MODIFY, type = LogDef.LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#_ret.getData().getCnname()}}") })
	public ApiResponse<Ship> update(@PathVariable(value = "id") String id,
			@Validated @RequestBody ShipUpdateForm form) {
		Ship ship = validator.validateUpdate(id);
		return new ApiResponse<>(
				shipService.updateAllProperties(form.convertToEntity(ship)));
	}

	@Operation(summary = "绑定船舶")
	@PutMapping(value = "/bind/{id}")
	@Secured({ AdminPermissionDef.SHIP_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.SHIP_BIND, type = LogDef.LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#ship.getCnname()}}") })
	public ApiResponse<Void> bind(@PathVariable String id,
			@RequestBody ShipBindForm form) {
		Ship ship = validator.validateBind(id);
		LogRecordContext.putVariable("ship", ship);
		shipService.bind(form.convertToEntity(ship));
		return new ApiResponse<>();
	}

	@Operation(summary = "解绑船舶")
	@PutMapping(value = "/unbind/{id}")
	@Secured({ AdminPermissionDef.SHIP_W })
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.UPDATE, success = LogDef.SHIP_UNBIND, type = LogDef.LOGISTICS_MANAGEMENT_SHIP_MANAGEMENT_INFO, bizNo = "{{#id}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#ship.getCnname()}}") })
	public ApiResponse<Void> unbind(@PathVariable(value = "id") String id) {
		validator.validateUnbind(id);
		LogRecordContext.putVariable("ship",
				shipService.findOne(id).orElse(new Ship()));
		shipService.unbind(id);
		return new ApiResponse<>();
	}

	@Operation(summary = "查询历史记录")
	@GetMapping("/history")
	public ApiResponse<List<ShipHistoryVo>> findHistory() {
		return new ApiResponse<>(shipService.findHistory(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				ShipDef.MonitoringChannels.BACKGROUND.getCode()));
	}

	@Operation(summary = "查船-运单信息")
	@GetMapping(value = "/transport")
	public ApiResponse<Page<TransportOrderShip>> findOrderShip(
			@RequestParam(value = "shipId") String shipId,
			@Parameter(description = "查询范围") @RequestParam(required = false) Integer scope,
			@Parameter(description = "查询起始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@Parameter(description = "查询截止时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size) {
		return new ApiResponse<>(shipService.findOrderShip(scope, beginTime,
				endTime, shipId, page, size));
	}

	@Operation(summary = "新增历史记录")
	@PostMapping("/history")
	public ApiResponse<Void> createHistory(@RequestBody ShipHistoryForm form) {
		ShipHistoryDto dto = form.convertToDto();

		shipService.createHistory(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				ShipDef.MonitoringChannels.BACKGROUND.getCode(), dto.getId(),
				dto.getName(), dto.getCnname(), dto.getType());
		return new ApiResponse<>();
	}

	@Operation(summary = "删除历史记录")
	@DeleteMapping("/history")
	public ApiResponse<Void> deleteHistory(@RequestBody ShipHistoryForm form) {
		ShipHistoryDto dto = form.convertToDto();
		shipService.deleteHistory(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				ShipDef.MonitoringChannels.BACKGROUND.getCode(), dto.getId(),
				dto.getName(), dto.getCnname(), dto.getType());
		return new ApiResponse<>();
	}

	@Operation(summary = "清空历史记录")
	@DeleteMapping("/all-history")
	public ApiResponse<Void> deleteAllHistory() {
		shipService.deleteAllHistory(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				ShipDef.MonitoringChannels.BACKGROUND.getCode());
		return new ApiResponse<>();
	}

}
