package com.zhihaoscm.service.resource.validator.sand.hot;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.SandHot;
import com.zhihaoscm.domain.meta.biz.SandAcademyDef;
import com.zhihaoscm.domain.meta.biz.SandHotDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.SandHotService;
import com.zhihaoscm.service.resource.form.sand.hot.SandHotForm;
import com.zhihaoscm.service.resource.validator.tag.TagValidator;

@Component
public class SandHotValidator {

	@Autowired
	private SandHotService service;
	@Autowired
	private TagValidator tagValidator;

	/**
	 * 验证存在
	 *
	 * @param id
	 * @return
	 */
	public SandHot validateExist(Long id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30192001));
	}

	public void validateCreate(SandHotForm form) {
		// 校验类型-只有选择近期展会才需要上传图片
		if (SandHotDef.Type.RECENT_EXHIBITION.match(form.getType())) {
			if (Objects.isNull(form.getFileId())) {
				throw new BadRequestException(ErrorCode.CODE_30192009);
			}
		} else {
			// 其他类型则不需要上传图片
			form.setFileId(null);
		}
		// 校验标签
		this.validateTag(form);
	}

	public SandHot validateUpdate(Long id, SandHotForm form) {
		SandHot sandHot = this.validateExist(id);
		// 下架状态才能编辑
		if (!CommonDef.Symbol.NO.match(sandHot.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30192013);
		}
		// 校验类型-只有选择近期展会才需要上传图片
		if (SandHotDef.Type.RECENT_EXHIBITION.match(form.getType())) {
			if (Objects.isNull(form.getFileId())) {
				throw new BadRequestException(ErrorCode.CODE_30192009);
			}
		} else {
			// 其他类型则不需要上传图片
			form.setFileId(null);
		}
		// 校验标签
		this.validateTag(form);
		return sandHot;
	}

	public void validateDelete(Long id) {
		SandHot sandHot = this.validateExist(id);
		// 下架状态才能删除
		if (!CommonDef.Symbol.NO.match(sandHot.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30192014);
		}
	}

	/**
	 * 校验更新状态
	 * 
	 * @param id
	 * @param state
	 */
	public void validateUpdateState(Long id, Integer state) {
		SandHot sandHot = this.validateExist(id);
		if (Objects.isNull(SandAcademyDef.State.from(state))
				|| Objects.equals(sandHot.getState(), state)) {
			throw new BadRequestException(ErrorCode.CODE_30192016);
		}
	}

	/**
	 * 校验标签
	 *
	 * @param form
	 */
	private void validateTag(SandHotForm form) {
		List<Long> tagIds = form.getTagIds();
		Set<Long> tagSet = new HashSet<>();
		if (CollectionUtils.isNotEmpty(tagIds)) {
			for (Long tagId : tagIds) {
				tagValidator.validateExist(tagId);
				if (tagSet.contains(tagId)) {
					throw new BadRequestException(ErrorCode.CODE_30192015);
				}
				tagSet.add(tagId);
			}
		}
	}

}
