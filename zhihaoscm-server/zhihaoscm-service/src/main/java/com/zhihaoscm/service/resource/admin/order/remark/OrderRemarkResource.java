package com.zhihaoscm.service.resource.admin.order.remark;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.entity.OrderRemark;
import com.zhihaoscm.service.core.service.OrderRemarkService;
import com.zhihaoscm.service.resource.form.order.remark.OrderRemarkForm;
import com.zhihaoscm.service.resource.validator.order.OrderValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 订单备注信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Tag(name = "订单备注信息", description = "订单备注信息API")
@RestController
@RequestMapping("/order-remark")
public class OrderRemarkResource {

	@Autowired
	private OrderRemarkService service;
	@Autowired
	private OrderValidator orderValidator;

	@Operation(summary = "新增订单备注")
	@PostMapping
	public ApiResponse<OrderRemark> create(
			@Validated @RequestBody OrderRemarkForm form) {
		orderValidator.validateHandle(form.getOrderId());
		return new ApiResponse<>(service.create(form.convertToEntity()));
	}

}
