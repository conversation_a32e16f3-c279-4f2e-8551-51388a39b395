package com.zhihaoscm.service.core.processor.contract.processor.contract;

import java.util.Map;

import com.zhihaoscm.domain.processor.Processor;
import com.zhihaoscm.qiyuesuo.sdk.request.Signatory;

/**
 * 合同处理器定义
 */
public interface ContractProcessor extends Processor<Integer> {

	/**
	 * 签署完成
	 */
	void signComplete(String tableId);

	/**
	 * 签署驳回
	 */
	void signReject(String tableId, String contact);

	/**
	 * 签署中
	 *
	 * @param correlationId
	 * @param name
	 * @param callbackType
	 */
	void signing(String correlationId, String name, String callbackType);

	/**
	 * 初始化创建合同草稿参数
	 *
	 * @param tableId
	 * @param signatoryMap
	 *            签署方: 签署合同的公司/个人
	 * @param customerMap
	 */
	void initDraftParams(String tableId, Map<Long, Signatory> signatoryMap,
			Map<Long, Long> customerMap);


	/**
	 * 发送作废
	 * @param tableId
	 * @param name
	 */
	void sendInvalid(String tableId, String name);

	/**
	 *  签署作废
	 * @param tableId
	 * @param name
	 */
	void invaliding(String tableId, String name);

	/**
	 * 作废完成
	 * @param tableId
	 * @param name
	 */
	void invalided(String tableId, String name);

	void rejectInvalid(String tableId, String name);
}
