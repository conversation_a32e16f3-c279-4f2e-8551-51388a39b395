package com.zhihaoscm.service.resource.custom.information;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.common.util.utils.date.DateFormat;
import com.zhihaoscm.domain.bean.dto.InformationDto;
import com.zhihaoscm.domain.bean.entity.Information;
import com.zhihaoscm.domain.bean.vo.InformationVo;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.meta.error.ErrorCodeDef;
import com.zhihaoscm.service.core.service.InformationService;
import com.zhihaoscm.service.resource.form.information.InformationCustomForm;
import com.zhihaoscm.service.resource.form.information.InformationDelCustomForm;
import com.zhihaoscm.service.resource.validator.information.InformationValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

@Tag(name = "砂石资讯", description = "砂石资讯API")
@RestController
@RequestMapping("/information")
public class InformationResource {

	@Autowired
	private InformationService informationService;

	@Autowired
	private InformationValidator validator;

	@GetMapping("/custom-paging")
	@Operation(summary = "分页查询砂石资讯")
	public ApiResponse<Page<InformationVo>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "标题关键字或砂石品类名") @RequestParam(required = false) String keyword,
			@Parameter(description = "状态") @RequestParam(required = false) Integer state,
			@Parameter(description = "发布日期起") @DateTimeFormat(pattern = DateFormat.YYYY_MM_DD_HH_MM_SS) @RequestParam(required = false) String publishDateStart,
			@Parameter(description = "发布日期止") @DateTimeFormat(pattern = DateFormat.YYYY_MM_DD_HH_MM_SS) @RequestParam(required = false) String publishDateFinish,
			@Parameter(description = "查询公告/资讯") @RequestParam(required = false) Integer category,
			@Parameter(description = "资讯分类 志豪链云：(1. 行业动态 2. 分析报告 3.每日评述)  链云船务：(4. 水位信息 5. 航道通告 )") @RequestParam(required = false) List<Integer> classify,
			@Parameter(description = "展示位置") @RequestParam(required = false) Integer showLocation,
			@Parameter(description = "排序key") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序方向") @RequestParam(required = false) String sortOrder,
			HttpServletRequest request) {
		return new ApiResponse<>(PageUtil
				.convert(informationService.customPaging(page, size, keyword,
						StringUtils.isBlank(request.getHeader("tenantId"))
								? null
								: request.getHeader("tenantId"),
						state, publishDateStart, publishDateFinish, category,
						classify, showLocation, sortKey, sortOrder)));
	}

	@Operation(summary = "分页查询相关砂石资讯 不针对分身版")
	@GetMapping("/related-paging")
	public ApiResponse<Page<InformationVo>> relatedPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "砂石品类ID") @RequestParam String productTypeId) {
		return new ApiResponse<>(PageUtil.convert(
				informationService.relatedPaging(page, size, productTypeId)));
	}

	@Operation(summary = "资讯详情里的相关资讯列表")
	@GetMapping("/detail-related")
	public ApiResponse<List<InformationVo>> detailRelated(
			@Parameter(description = "资讯ID") @RequestParam Long informationId) {
		return new ApiResponse<>(
				informationService.detailRelated(informationId));
	}

	@Operation(summary = "根据展示位置查询最新数据")
	@GetMapping("/findByShowLocation")
	public ApiResponse<List<InformationVo>> findByShowLocation(
			@Parameter(description = "展示位置list") @RequestParam(required = false) List<Integer> showLocations,
			@Parameter(description = "资讯分类 志豪链云：(1. 行业动态 2. 分析报告 3.每日评述)  链云船务：(4. 水位信息 5. 航道通告 )") @RequestParam(required = false) List<Integer> classify) {
		return new ApiResponse<>(
				informationService.findByShowLocation(showLocations, classify));
	}

	@Operation(summary = "根据PC端展示位置查询")
	@GetMapping("/find-by-pc-show-location")
	public ApiResponse<List<InformationVo>> findByPcShowLocation() {
		return new ApiResponse<>(informationService.findByPcShowLocation());
	}

	@Operation(summary = "根据分类查询最新数据")
	@GetMapping("/find-by-classify")
	public ApiResponse<List<InformationVo>> findByClassify(
			@Parameter(description = "分类") @RequestParam(required = false) Integer classify) {
		return new ApiResponse<>(informationService.findByClassify(classify));
	}

	@Operation(summary = "查找单个砂石资讯数据")
	@GetMapping("/detail/{id}")
	public ApiResponse<InformationVo> detail(@PathVariable Long id,
			HttpServletRequest request) {
		InformationVo informationVo = informationService.detail(id,
				StringUtils.isBlank(request.getHeader("tenantId")) ? null
						: request.getHeader("tenantId"))
				.orElse(null);
		if (Objects.isNull(informationVo)) {
			throw new BadRequestException(ErrorCode.CODE_30106001);
		}
		return new ApiResponse<>(informationVo);
	}

	@Operation(summary = "新增分身版公告/资讯")
	@PostMapping
	public ApiResponse<Information> create(
			@Validated @RequestBody InformationCustomForm informationForm) {
		validator.validateCategory(informationForm);
		InformationDto dto = informationForm.convertToDto(new Information());
		Information information = dto.getInformation();
		information.setState(CommonDef.Symbol.NO.getCode());
		return new ApiResponse<>(informationService.create(dto.getInformation(),
				dto.getActiveFileIds(), dto.getTagIds()));
	}

	@Operation(summary = "修改分身版公告/资讯")
	@PutMapping("/{id}")
	public ApiResponse<Void> update(@PathVariable Long id,
			@Validated @RequestBody InformationCustomForm informationForm) {
		validator.validateCategory(informationForm);
		Information information = informationService.findOne(id).orElse(null);
		if (Objects.isNull(information)) {
			throw new BadRequestException(ErrorCodeDef.CODE_30047001.getCode());
		}
		InformationDto informationDto = informationForm
				.convertToDto(information);
		informationService.update(informationDto.getInformation(),
				informationDto.getActiveFileIds(),
				informationDto.getUnActiveFileIds(),
				informationDto.getTagIds());
		return new ApiResponse<>();
	}

	@Operation(summary = "删除")
	@DeleteMapping("/delete")
	public ApiResponse<Void> delete(
			@Validated @RequestBody InformationDelCustomForm informationDelForm) {
		validator.validateDelete(informationDelForm);
		InformationDto informationDto = informationDelForm.convertToDto();
		informationService.delete(informationDto.getInformation().getId(),
				informationDto.getUnActiveFileIds());
		return new ApiResponse<>();
	}

	@Operation(summary = "更新状态")
	@PostMapping("/update/{id}/state/{state}")
	public ApiResponse<Void> updateState(@PathVariable Long id,
			@Parameter(description = "1 上架 0 下架") @PathVariable Integer state) {
		informationService.updateState(id, state);
		return new ApiResponse<>();
	}

}
