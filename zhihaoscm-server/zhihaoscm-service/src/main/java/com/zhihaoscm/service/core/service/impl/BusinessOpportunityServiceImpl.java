package com.zhihaoscm.service.core.service.impl;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.BusinessOpportunity;
import com.zhihaoscm.domain.bean.entity.BusinessOpportunityCollect;
import com.zhihaoscm.domain.bean.json.AuditInfo;
import com.zhihaoscm.domain.bean.json.TakedownInfo;
import com.zhihaoscm.domain.bean.json.notice.WxwMessage;
import com.zhihaoscm.domain.bean.vo.BusinessOpportunityCountVo;
import com.zhihaoscm.domain.bean.vo.BusinessOpportunityVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.BusinessOpportunityDef;
import com.zhihaoscm.domain.meta.biz.WxwDef;
import com.zhihaoscm.domain.utils.PaginationUtils;
import com.zhihaoscm.service.core.mapper.BusinessOpportunityMapper;
import com.zhihaoscm.service.core.service.BusinessOpportunityCollectService;
import com.zhihaoscm.service.core.service.BusinessOpportunityService;
import com.zhihaoscm.service.core.service.MessageService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

/**
 * <p>
 * 商机 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
public class BusinessOpportunityServiceImpl extends
		MpLongIdBaseServiceImpl<BusinessOpportunity, BusinessOpportunityMapper>
		implements BusinessOpportunityService {

	@Autowired
	private UserService userService;
	@Autowired
	private BusinessOpportunityCollectService businessOpportunityCollectService;
	@Autowired
	private MessageService messageService;

	public BusinessOpportunityServiceImpl(
			BusinessOpportunityMapper repository) {
		super(repository);
	}

	@Override
	public Page<BusinessOpportunity> paging(Integer page, Integer size,
			String keyword, List<Integer> type, List<Integer> state,
			String sortKey, String sortOrder) {
		LambdaQueryWrapper<BusinessOpportunity> wrapper = Wrappers
				.lambdaQuery(BusinessOpportunity.class);
		this.filterDeleted(wrapper);
		wrapper.like(StringUtils.isNotBlank(keyword),
				BusinessOpportunity::getTitle, keyword)
				.in(CollectionUtils.isNotEmpty(type),
						BusinessOpportunity::getType, type)
				.in(CollectionUtils.isNotEmpty(state),
						BusinessOpportunity::getState, state);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 以状态和创建时间排序，待审核1、上架中2、未通过3、已下架4
			// 相同状态按创建时间倒序
			wrapper.orderByAsc(BusinessOpportunity::getState)
					.orderByDesc(BusinessOpportunity::getCreatedTime);
		}
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<BusinessOpportunityVo> customPaging(
			Integer page, Integer size, String keyword, List<Integer> type,
			String addressCode, LocalDateTime beginTime, LocalDateTime endTime,
			Long customId) {
		LambdaQueryWrapper<BusinessOpportunity> wrapper = Wrappers
				.lambdaQuery(BusinessOpportunity.class);
		this.filterDeleted(wrapper);
		// 已上架的
		wrapper.eq(BusinessOpportunity::getState,
				BusinessOpportunityDef.State.ON_SHELF);
		wrapper.like(StringUtils.isNotBlank(keyword),
				BusinessOpportunity::getTitle, keyword)
				.in(CollectionUtils.isNotEmpty(type),
						BusinessOpportunity::getType, type);
		// 一级或二级地址编码
		wrapper.and(StringUtils.isNotBlank(addressCode),
				w -> w.eq(BusinessOpportunity::getProvinceCode, addressCode)
						.or()
						.eq(BusinessOpportunity::getCityCode, addressCode));
		// 发布时间-取更新时间
		wrapper.ge(Objects.nonNull(beginTime),
				BusinessOpportunity::getUpdatedTime, beginTime)
				.le(Objects.nonNull(endTime),
						BusinessOpportunity::getUpdatedTime, endTime);
		// 按上架时间倒序
		// 是发布时间也就是更新时间倒序
		wrapper.orderByDesc(BusinessOpportunity::getUpdatedTime);
		List<BusinessOpportunity> businessOpportunities = repository
				.selectList(wrapper);
		List<BusinessOpportunityVo> vos = this.packVo(businessOpportunities,
				customId);
		return PaginationUtils.handelPage(page, size, vos);
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<BusinessOpportunityVo> customPagingCollect(
			Integer page, Integer size, String keyword, Long customId) {
		LambdaQueryWrapper<BusinessOpportunity> wrapper = Wrappers
				.lambdaQuery(BusinessOpportunity.class);
		wrapper.like(StringUtils.isNotBlank(keyword),
				BusinessOpportunity::getTitle, keyword);
		wrapper.inSql(BusinessOpportunity::getId, String.format(
				"SELECT business_id FROM t_business_opportunity_collect WHERE del = 0 AND custom_id = %d",
				customId));
		// 发布时间也就是更新时间倒序
		wrapper.orderByDesc(BusinessOpportunity::getUpdatedTime);
		List<BusinessOpportunity> list = repository.selectList(wrapper);
		List<BusinessOpportunityVo> vos = this.packVo(list, customId);
		return PaginationUtils.handelPage(page, size, vos);
	}

	@Override
	public List<BusinessOpportunityVo> relatedList(Long businessOpportunityId,
			Long customId) {
		// 已失效的也能看详情
		BusinessOpportunity businessOpportunity = super.findOneWithDeleted(
				businessOpportunityId).orElse(null);
		if (Objects.isNull(businessOpportunity)) {
			return List.of();
		}
		Integer type = businessOpportunity.getType();
		String cityCode = businessOpportunity.getCityCode();

		LambdaQueryWrapper<BusinessOpportunity> wrapper = Wrappers
				.lambdaQuery(BusinessOpportunity.class);
		// 必须满足cityCode或type
		wrapper.and(w -> w.eq(BusinessOpportunity::getCityCode, cityCode).or()
				.eq(BusinessOpportunity::getType, type));
		// 排序+展示三条
		String caseOrder = String.format(
				"CASE WHEN city_code = '%s' AND type = %d THEN 1 "
						+ "WHEN type = %d THEN 2 "
						+ "WHEN city_code = '%s' THEN 3 " + "ELSE 4 END",
				cityCode, type, type, cityCode);
		wrapper.last("ORDER BY " + caseOrder + " , updated_time DESC LIMIT 3");

		List<BusinessOpportunity> list = repository.selectList(wrapper);
		return this.packVo(list, customId);
	}

	@Override
	public Optional<BusinessOpportunityVo> findVoById(Long id, Long customId) {

		return super.findOneWithDeleted(id)
				.map(resource -> this.packVo(resource, customId));
	}

	@Override
	public BusinessOpportunity create(BusinessOpportunity resource) {
		BusinessOpportunity businessOpportunity = super.create(resource);
		// 新增提交商机后，给有审核权限的专员发企微消息
		this.sendWxNotice(businessOpportunity);
		return businessOpportunity;
	}

	@Override
	public BusinessOpportunity updateAllProperties(
			BusinessOpportunity resource) {
		return super.updateAllProperties(resource);
	}

	@Override
	public void delete(Long id) {
		super.delete(id);
	}

	@Override
	public void audit(BusinessOpportunity resource) {
		AuditInfo auditInfo = resource.getAuditInfo();
		// 设置审核人信息
		// 获取当前登录用户id
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (Objects.nonNull(context)) {
			Long userId = context.getUserId();
			auditInfo.setAuditBy(userId);
			userService.findOne(userId).ifPresent(user -> {
				auditInfo.setAuditBy(user.getId());

				auditInfo.setAuditByName(user.getName());
			});
		}
		// 设置审核时间
		auditInfo.setAuditTime(LocalDateTime.now());
		// 更新审核信息
		resource.setAuditInfo(auditInfo);
		// 更新状态
		resource.setState(CommonDef.Symbol.YES.match(auditInfo.getState())
				? BusinessOpportunityDef.State.ON_SHELF.getCode()
				: BusinessOpportunityDef.State.FAILED.getCode());
		this.updateAllProperties(resource);
	}

	@Override
	public void downShelf(BusinessOpportunity resource) {
		TakedownInfo takedownInfo = resource.getTakedownInfo();
		// 设置下架时间
		takedownInfo.setDownShelfTime(LocalDateTime.now());
		// 清空标签
		resource.setOpportunityLabel(null);
		// 清空置顶
		resource.setIsTop(CommonDef.Symbol.NO.getCode());
		resource.setTopTime(null);
		// 更新状态
		resource.setState(BusinessOpportunityDef.State.OFF_SHELF.getCode());
		this.updateAllProperties(resource);
	}

	@Override
	public void collect(Long id, Integer state, Long customId) {
		// 收藏
		if (CommonDef.Symbol.YES.match(state)) {
			BusinessOpportunityCollect collect = new BusinessOpportunityCollect();
			collect.setCustomId(customId);
			collect.setBusinessId(id);
			businessOpportunityCollectService.create(collect);
		} else {
			// 取消收藏
			businessOpportunityCollectService
					.findByCustomIdAndBusinessId(customId, id)
					.ifPresent(collect -> businessOpportunityCollectService
							.delete(collect.getId()));
		}
	}

	@Override
	public BusinessOpportunityCountVo statisticsBusinessOpportunity(
			Boolean hasFull) {
		BusinessOpportunityCountVo vo = new BusinessOpportunityCountVo();
		vo.setToBeReviewedCount(0L);
		if (hasFull) {
			LambdaQueryWrapper<BusinessOpportunity> wrapper = Wrappers
					.lambdaQuery(BusinessOpportunity.class);
			this.filterDeleted(wrapper);
			wrapper.eq(BusinessOpportunity::getState,
					BusinessOpportunityDef.State.TO_BE_REVIEWED.getCode());
			Long count = repository.selectCount(wrapper);
			vo.setToBeReviewedCount(count);
			wrapper.clear();
		}
		return vo;
	}

	/**
	 * 组装vo
	 */
	private BusinessOpportunityVo packVo(
			BusinessOpportunity businessOpportunities, Long customId) {
		BusinessOpportunityVo vo = new BusinessOpportunityVo();
		vo.setBusinessOpportunity(businessOpportunities);
		// 是否收藏
		vo.setIsCollected(
				businessOpportunityCollectService.findByCustomIdAndBusinessId(
						customId, businessOpportunities.getId()).isPresent()
								? CommonDef.Symbol.YES.getCode()
								: CommonDef.Symbol.NO.getCode());
		return vo;
	}

	/**
	 * 组装vos
	 */
	private List<BusinessOpportunityVo> packVo(
			List<BusinessOpportunity> businessOpportunities, Long customId) {
		if (CollectionUtils.isEmpty(businessOpportunities)) {
			return List.of();
		}
		List<BusinessOpportunityVo> vos = new ArrayList<>();

		// 该用户收藏的所有商机，保证返回不为null
		List<BusinessOpportunityCollect> collectList = Optional.ofNullable(
				businessOpportunityCollectService.findByCustomId(customId))
				.orElse(List.of());
		// 该用户收藏的所有商机id，过滤掉businessId为空的收藏记录
		Set<Long> collectedBusinessOpportunityIds = collectList.stream()
				.map(BusinessOpportunityCollect::getBusinessId)
				.filter(Objects::nonNull).collect(Collectors.toSet());
		for (BusinessOpportunity businessOpportunity : businessOpportunities) {
			BusinessOpportunityVo vo = new BusinessOpportunityVo();
			vo.setBusinessOpportunity(businessOpportunity);
			// 是否收藏
			vo.setIsCollected(
					(CollectionUtils.isNotEmpty(collectedBusinessOpportunityIds)
							&& collectedBusinessOpportunityIds
									.contains(businessOpportunity.getId()))
											? CommonDef.Symbol.YES.getCode()
											: CommonDef.Symbol.NO.getCode());
			vos.add(vo);
		}
		return vos;
	}

	/**
	 * 新增提交商机后，给有审核权限的专员发企微消息
	 * 
	 * @param businessOpportunity
	 */
	private void sendWxNotice(BusinessOpportunity businessOpportunity) {
		// todo 跳转详情地址需要改变
		messageService.sendNotice(WxwMessage.builder().receiptors(userService
				.findUsersByPermission(AdminPermissionDef.BUSINESS_REVIEW, null)
				.stream().map(user -> String.valueOf(user.getId())).toList())
				.url("/logistics/oil/oilTypeManagement/detail/"
						.concat(String.valueOf(businessOpportunity.getId())))
				.prefix(WxwDef.NoticePrefix.YOU_HAVE.getDesc())
				.operationModule(
						"商机" + businessOpportunity.getTitle() + "新增待审核")
				.keyword(businessOpportunity.getTitle())
				.content(StringUtils.EMPTY).build());
	}
}
