package com.zhihaoscm.service.resource.validator.oil.order;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.OilOrder;
import com.zhihaoscm.domain.meta.biz.OilOrderDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.OilOrderService;
import com.zhihaoscm.service.resource.form.oil.order.OilOrderBookingForm;

/**
 * 油品订单校验器
 */
@Component
public class OilOrderValidator {

	@Autowired
	private OilOrderService service;

	/**
	 * 校验数据是否存在
	 *
	 * @param id
	 * @return
	 */
	public OilOrder validateExist(String id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30156001));
	}

	/**
	 * 校验确认油品订单（同意加油协议）
	 * 
	 * @param id
	 * @return
	 */
	public OilOrder validateAgree(String id) {
		OilOrder oilOrder = this.validateExist(id);
		// 订单是自己的
		if (!Objects.equals(
				Objects.requireNonNull(CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount()).getId(),
				oilOrder.getCaptainId())) {
			throw new BadRequestException(ErrorCode.CODE_403);
		}
		// 订单是预约中
		if (!OilOrderDef.State.BOOKING.match(oilOrder.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30156035);
		}
		// 用户还未同意加油协议
		if (!CommonDef.Symbol.NO.match(oilOrder.getIsAgreeProtocol())) {
			throw new BadRequestException(ErrorCode.CODE_30156036);
		}
		return oilOrder;
	}

	/**
	 * 检验预约加油
	 * 
	 * @param form
	 * @return
	 */
	public OilOrder validateBooking(OilOrderBookingForm form) {
		OilOrder oilOrder = form.convertToEntity();
		// 自行下单
		if (OilOrderDef.OrderType.SELF_ORDER.match(oilOrder.getOrderType())) {
			// 预约船舶必填
			if (StringUtils.isBlank(oilOrder.getShipId())) {
				throw new BadRequestException(ErrorCode.CODE_30156017);
			}
			// 油品类型必填
			if (Objects.isNull(oilOrder.getOilType())) {
				throw new BadRequestException(ErrorCode.CODE_30156018);
			}
			// 加油量必填
			if (Objects.isNull(oilOrder.getBookingRefuelingVolume())) {
				throw new BadRequestException(ErrorCode.CODE_30156020);
			}
			// 预约时间必填
			if (Objects.isNull(oilOrder.getBookingRefuelingTime())) {
				throw new BadRequestException(ErrorCode.CODE_30156021);
			}
			// 预约时间段必填
			if (Objects.isNull(oilOrder.getBookingRefuelingTimePeriod())) {
				throw new BadRequestException(ErrorCode.CODE_30156022);
			}
		}
		// 船主预约加油，设置油品订单的船主id为此登录账号的id
		Long customId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		oilOrder.setCaptainId(customId);
		return oilOrder;
	}

	/**
	 * 校验能否确认加油
	 * 
	 * @param id
	 * @param isAdmin
	 *            是否是管理后台
	 * @return
	 */
	public OilOrder validateConfirmRefueling(String id, Integer isAdmin) {
		OilOrder oilOrder = this.validateExist(id);
		// 支付状态为空
		if (Objects.isNull(oilOrder.getPayState())) {
			throw new BadRequestException(ErrorCode.CODE_30156002);
		} else if (OilOrderDef.PayState.UN_CONFIRM
				.match(oilOrder.getPayState())) {
			// 支付状态为待确认
			if (CommonDef.Symbol.YES.match(isAdmin)) {
				throw new BadRequestException(ErrorCode.CODE_30156008);
			} else {
				throw new BadRequestException(ErrorCode.CODE_30156003);
			}
		}
		// 订单业务状态是进行中+进行中状态是待确认加油+支付状态是已支付 才能确认加油
		if (!OilOrderDef.State.IN_PROGRESS.match(oilOrder.getState())
				|| !OilOrderDef.ProgressState.AWAITING_CONFIRMATION
						.match(oilOrder.getProgressState())
				|| !OilOrderDef.PayState.SUCCESS
						.match(oilOrder.getPayState())) {
			throw new BadRequestException(ErrorCode.CODE_30156004);
		}
		return oilOrder;
	}

	/**
	 * 校验取消订单
	 * 
	 * @param id
	 */
	public void validateCancel(String id) {
		OilOrder oilOrder = this.validateExist(id);
		// 订单业务状态为已完成前都能取消订单
		if (OilOrderDef.State.COMPLETED.match(oilOrder.getState())
				|| OilOrderDef.State.CANCELED.match(oilOrder.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30156005);
		}
	}

}
