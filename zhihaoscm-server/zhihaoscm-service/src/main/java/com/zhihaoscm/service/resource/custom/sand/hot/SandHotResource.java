package com.zhihaoscm.service.resource.custom.sand.hot;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.domain.bean.vo.SandHotVo;
import com.zhihaoscm.domain.meta.error.ErrorCodeDef;
import com.zhihaoscm.service.core.service.SandHotService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "砂石热点", description = "砂石热点API")
@RestController
@RequestMapping("/sand-hot")
public class SandHotResource {

	@Autowired
	private SandHotService service;

	@GetMapping("/custom-paging")
	@Operation(summary = "分页查询砂石热点")
	public ApiResponse<Page<SandHotVo>> customPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "标题") @RequestParam(required = false) String title,
			@Parameter(description = "分类") @RequestParam(required = false) Integer type) {
		return new ApiResponse<>(service.customPaging(page, size, title, type));
	}

	@Operation(summary = "分页查询相关砂石热点 不针对分身版")
	@GetMapping("/related-paging")
	public ApiResponse<Page<SandHotVo>> relatedPaging(
			@Parameter(description = "页码") @RequestParam(required = false, defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(required = false, defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "当前砂石热点id") @RequestParam(required = false) Long sandHotId) {
		return new ApiResponse<>(service.relatedPaging(page, size, sandHotId));
	}

	@Operation(summary = "查找单个数据")
	@GetMapping("/vo/{id}")
	public ApiResponse<SandHotVo> findVoById(@PathVariable Long id) {
		SandHotVo vo = service.findVoById(id).orElse(null);
		if (Objects.isNull(vo)) {
			throw new BadRequestException(ErrorCodeDef.CODE_30192001.getCode());
		}
		return new ApiResponse<>(vo);
	}
}
