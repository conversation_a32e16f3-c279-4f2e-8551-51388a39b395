package com.zhihaoscm.service.resource.form.oil.order;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.OilOrder;
import com.zhihaoscm.domain.meta.biz.OilOrderDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(name = "OilOrderBookingForm", title = "管理后台油品订单预约加油表单")
@Data
public class OilOrderBookingAdminForm {

	@Schema(title = "油品站点id")
	@NotNull(message = ErrorCode.CODE_30156014)
	private Long oilSiteId;

	@Schema(title = "船主id")
	@NotNull(message = ErrorCode.CODE_30156033)
	private Long captainId;

	@Schema(title = "船主信息联系人")
	@NotBlank(message = ErrorCode.CODE_30156015)
	private String captainName;

	@Schema(title = "船主信息手机号")
	@NotBlank(message = ErrorCode.CODE_30156016)
	private String captainMobile;

	@Schema(title = "船舶的MMSI")
	@NotBlank(message = ErrorCode.CODE_30156017)
	private String shipId;

	@Schema(title = "油品类型:1.0号柴油/2.轻质燃油")
	@NotNull(message = ErrorCode.CODE_30156018)
	@Range(min = 1, max = 2, message = ErrorCode.CODE_30156019)
	private Integer oilType;

	@Schema(title = "预估加油量")
	@NotNull(message = ErrorCode.CODE_30156020)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30156027)
	@DecimalMax(value = "9999.99", message = ErrorCode.CODE_30156027)
	private BigDecimal bookingRefuelingVolume;

	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	@Schema(title = "预约加油日期")
	@NotNull(message = ErrorCode.CODE_30156021)
	private LocalDateTime bookingRefuelingTime;

	@Schema(title = "预约加油时间段")
	@NotNull(message = ErrorCode.CODE_30156022)
	@Range(min = 1, max = 12, message = ErrorCode.CODE_30156023)
	private Integer bookingRefuelingTimePeriod;

	@Schema(title = "油价")
	@NotNull(message = ErrorCode.CODE_30156024)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30156026)
	@DecimalMax(value = "999999.99", message = ErrorCode.CODE_30156026)
	private BigDecimal bookingRefuelingPrice;

	@Schema(title = "备注")
	@Length(max = 200, message = ErrorCode.CODE_30156025)
	private String remark;

	public OilOrder convertToEntity() {
		OilOrder oilOrder = new OilOrder();
		// 管理后台新增的，订单方式为平台代下
		oilOrder.setOrderType(OilOrderDef.OrderType.PLATFORM_ORDER.getCode());
		oilOrder.setOilSiteId(this.getOilSiteId());
		oilOrder.setCaptainId(this.getCaptainId());
		oilOrder.setCaptainName(this.getCaptainName());
		oilOrder.setCaptainMobile(this.getCaptainMobile());
		oilOrder.setShipId(this.getShipId());
		oilOrder.setOilType(this.getOilType());
		oilOrder.setBookingRefuelingVolume(this.getBookingRefuelingVolume());
		oilOrder.setBookingRefuelingTime(this.getBookingRefuelingTime());
		oilOrder.setBookingRefuelingTimePeriod(
				this.getBookingRefuelingTimePeriod());
		oilOrder.setBookingRefuelingPrice(this.getBookingRefuelingPrice());
		oilOrder.setRemark(this.getRemark());
		// 管理后台给船主预约加油，此时船主还未同意加油协议
		oilOrder.setIsAgreeProtocol(CommonDef.Symbol.NO.getCode());
		return oilOrder;
	}
}
