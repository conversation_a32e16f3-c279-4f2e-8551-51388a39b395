package com.zhihaoscm.service.core.service.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.OrderRemark;
import com.zhihaoscm.service.core.mapper.OrderRemarkMapper;
import com.zhihaoscm.service.core.service.OrderRemarkService;

/**
 * <p>
 * 订单备注信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class OrderRemarkServiceImpl
		extends MpLongIdBaseServiceImpl<OrderRemark, OrderRemarkMapper>
		implements OrderRemarkService {

	public OrderRemarkServiceImpl(OrderRemarkMapper repository) {
		super(repository);
	}

	@Override
	public List<OrderRemark> findByOrderIds(List<String> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return List.of();
		}
		LambdaQueryWrapper<OrderRemark> wrapper = Wrappers
				.lambdaQuery(OrderRemark.class);
		this.filterDeleted(wrapper);
		wrapper.in(OrderRemark::getOrderId, orderIds);
		return repository.selectList(wrapper);
	}

	@FileId
	@Override
	public OrderRemark create(OrderRemark resource) {
		return super.create(resource);
	}
}
