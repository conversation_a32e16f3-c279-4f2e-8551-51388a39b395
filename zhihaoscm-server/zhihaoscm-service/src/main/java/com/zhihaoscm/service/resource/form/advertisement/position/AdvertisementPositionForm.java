package com.zhihaoscm.service.resource.form.advertisement.position;

import java.time.LocalDateTime;
import java.util.List;

import org.hibernate.validator.constraints.Range;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;

import com.zhihaoscm.domain.bean.entity.AdvertisementPosition;
import com.zhihaoscm.domain.bean.json.Advertisement;
import com.zhihaoscm.domain.bean.json.ArrayAdvertisement;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(name = "AdvertisementPositionForm", title = "广告位表单对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AdvertisementPositionForm {

	@Schema(description = "广告位位置类型")
	@NotNull(message = ErrorCode.CODE_30033030)
	@Range(min = 1, max = 7, message = ErrorCode.CODE_30033031)
	private Integer positionType;

	@NotNull(message = ErrorCode.CODE_30033002)
	@Range(min = 1, max = 3, message = ErrorCode.CODE_30033032)
	@Schema(description = "广告位")
	private Integer position;

	@NotNull(message = ErrorCode.CODE_30033003)
	@Range(min = 1, max = 2, message = ErrorCode.CODE_30033012)
	@Schema(description = "展现形式")
	private Integer displayForm;

	@Range(min = 1, max = 2, message = ErrorCode.CODE_30033013)
	@Schema(description = "轮播间隔")
	private Integer timeInterval;

	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	@NotNull(message = ErrorCode.CODE_30033005)
	@Schema(description = "开始时间")
	private LocalDateTime beginTime;

	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	@NotNull(message = ErrorCode.CODE_30033005)
	@Schema(description = "结束时间")
	private LocalDateTime endTime;

	@NotEmpty(message = ErrorCode.CODE_30033009)
	@Valid
	@Schema(description = "广告")
	private List<AdvertisementItem> content;

	public AdvertisementPosition convertToEntity() {
		AdvertisementPosition advertisementPosition = new AdvertisementPosition();
		return updateData(advertisementPosition);
	}

	private AdvertisementPosition updateData(
			AdvertisementPosition advertisementPosition) {
		advertisementPosition.setPositionType(this.positionType);
		advertisementPosition.setPosition(this.position);
		advertisementPosition.setDisplayForm(this.displayForm);
		advertisementPosition.setTimeInterval(this.timeInterval);
		advertisementPosition.setBeginTime(this.beginTime);
		advertisementPosition.setEndTime(this.endTime);
		ArrayAdvertisement advertisements = new ArrayAdvertisement();
		for (AdvertisementItem advertisementItem : content) {
			Advertisement advertisement = new Advertisement();
			BeanUtils.copyProperties(advertisementItem, advertisement);
			advertisements.add(advertisement);
		}
		advertisementPosition.setContent(advertisements);
		return advertisementPosition;
	}

	public AdvertisementPosition convertToEntity(
			AdvertisementPosition advertisementPosition) {
		return updateData(advertisementPosition);
	}
}
