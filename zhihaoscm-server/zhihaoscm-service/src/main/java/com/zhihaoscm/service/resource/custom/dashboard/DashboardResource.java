package com.zhihaoscm.service.resource.custom.dashboard;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.vo.GoodsFindShipStatisticVo;
import com.zhihaoscm.service.core.service.DashboardService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "用户端看板", description = "用户端看板API")
@RestController
@RequestMapping(value = "/dashboard")
public class DashboardResource {

	@Autowired
	private DashboardService service;

	@Operation(summary = "船舶统计")
	@GetMapping("/ship-statistic")
	public ApiResponse<GoodsFindShipStatisticVo> shipStatistic() {
		return new ApiResponse<>(service.shipStatistic().orElse(null));
	}

	@Operation(summary = "用户统计")
	@GetMapping("/custom-statistic")
	public ApiResponse<GoodsFindShipStatisticVo> customStatistic() {
		return new ApiResponse<>(service.customStatistic().orElse(null));
	}

	@Operation(summary = "船运统计")
	@GetMapping("/shipping-statistic")
	public ApiResponse<GoodsFindShipStatisticVo> shippingStatistic() {
		return new ApiResponse<>(service.shippingStatistic().orElse(null));
	}
}
