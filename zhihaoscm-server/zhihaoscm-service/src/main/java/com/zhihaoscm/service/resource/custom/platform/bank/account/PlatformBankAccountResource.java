package com.zhihaoscm.service.resource.custom.platform.bank.account;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.entity.PlatformBankAccount;
import com.zhihaoscm.service.core.service.PlatformBankAccountService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "平台银行账号", description = "平台银行账号管理API")
@RestController
@RequestMapping("/platform/bank-account")
public class PlatformBankAccountResource {

	@Autowired
	private PlatformBankAccountService service;

	@Operation(summary = "平台银行账号下拉列表")
	@GetMapping("/selector")
	public ApiResponse<List<PlatformBankAccount>> selector(
			@RequestParam(required = false) String name,
			@RequestParam(required = false) Integer state,
			@RequestParam(required = false) List<Long> useTypes) {
		return new ApiResponse<>(service.selector(name, state, useTypes));
	}

}
