package com.zhihaoscm.application.resource.car;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.application.core.service.CarService;
import com.zhihaoscm.application.core.service.VehicleService;
import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.domain.bean.vo.CarVo;
import com.zhihaoscm.domain.bean.vo.QueryLicenseResponseVo;
import com.zhihaoscm.domain.meta.ApplicationDef;
import com.zhihaoscm.sanyi.sdk.response.SanyiVehicleInfoResponse;
import com.zhihaoscm.zhongjiao.sdk.entity.reponse.RouterPathResponse;
import com.zhihaoscm.zhongjiao.sdk.entity.reponse.VehicleInfoResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * author: 黄昌龙
 */
@Slf4j
@Tag(name = "应用管理-查车", description = "应用管理-查车API")
@RestController
@RequestMapping("/application/car")
public class FundCarResource {

	@Autowired
	private VehicleService service;

	@Autowired
	private CarService carService;

	@Operation(summary = "车辆轨迹")
	@GetMapping("/car-route-info")
	@Secured({ ApplicationDef.FUND_CAR_ROUTE_INFO })
	public ApiResponse<RouterPathResponse> findCarRouteInfo(
			@Parameter(description = "车牌号") @RequestParam("vclN") String vclN,
			@Parameter(description = "车牌类型") @RequestParam("vco") String vco,
			@Parameter(description = "开始时间") @RequestParam("qryBtm") String qryBtm,
			@Parameter(description = "结束时间") @RequestParam("qryEtm") String qryEtm,
			@Parameter(description = "始发地坐标") @RequestParam(required = false) String startLonlat,
			@Parameter(description = "目的地坐标") @RequestParam(required = false) String endLonlat,
			@Parameter(description = "始发地行政区划编码") @RequestParam(required = false) String startAreaCode,
			@Parameter(description = "目的地行政区划编码") @RequestParam(required = false) String endAreaCode,
			@Parameter(description = "停车时长") @RequestParam(required = false) String parkMins) {
		return new ApiResponse<>(service
				.getRouterPath(vclN, vco, qryBtm, qryEtm, startLonlat,
						endLonlat, startAreaCode, endAreaCode, parkMins)
				.orElse(null));
	}

	@Operation(summary = "车辆信息")
	@GetMapping("/car-info")
	@Secured({ ApplicationDef.FUND_CAR_INFO })
	public ApiResponse<List<QueryLicenseResponseVo>> findCarInfo(
			@Parameter(description = "车牌号") @RequestParam("vclN") String vclN) {
		return new ApiResponse<>(service.getLicenseInfo(vclN, 1));
	}

	@Operation(summary = "车辆动态信息")
	@GetMapping("/car-dynamic-info")
	@Secured({ ApplicationDef.FUND_CAR_DYNAMIC_INFO })
	public ApiResponse<VehicleInfoResponse> findCarDynamicInfo(
			@Parameter(description = "车牌号,需要带上\"_{车牌颜色代码}\"") @RequestParam("vnos") String vnos,
			@Parameter(description = "始发地坐标") @RequestParam(required = false) String startLonlat,
			@Parameter(description = "目的地坐标") @RequestParam(required = false) String endLonlat,
			@Parameter(description = "始发地行政区划编码") @RequestParam(required = false) String startAreaCode,
			@Parameter(description = "目的地行政区划编码") @RequestParam(required = false) String endAreaCode,
			@Parameter(description = "目的地行政区划编码时间范围") @RequestParam(required = false) String timeNearby) {
		return new ApiResponse<>(
				service.getTransTimeManage(vnos, startLonlat, endLonlat,
						startAreaCode, endAreaCode, timeNearby).orElse(null));
	}

	@Operation(summary = "查询车辆载重")
	@GetMapping("/car-load")
	@Secured({ ApplicationDef.FUND_CAR_INFO })
	public ApiResponse<String> findCarLoad(
			@Parameter(description = "车牌号") @RequestParam String plateNumber) {
		return new ApiResponse<>(carService.findCarLoad(plateNumber));
	}

	@Operation(summary = "根据车牌号集合查询车辆和车载设备信息集合")
	@PostMapping("/vo/plate-numbers")
	@Secured({ ApplicationDef.FUND_CAR_DEVICE_INFO })
	public ApiResponse<List<CarVo>> findVoByPlateNumbers(
			@RequestBody Collection<String> plateNumbers) {
		return new ApiResponse<>(carService.findVoByPlateNumbers(plateNumbers));
	}

	@Operation(summary = "查询车辆设备信息")
	@GetMapping("/device-info")
	@Secured({ ApplicationDef.FUND_CAR_DEVICE_INFO })
	public ApiResponse<SanyiVehicleInfoResponse> findDeviceInfo(
			@Parameter(description = "车牌号") @RequestParam String plateNumber) {
		return new ApiResponse<>(carService.findDeviceInfo(plateNumber));
	}

}
