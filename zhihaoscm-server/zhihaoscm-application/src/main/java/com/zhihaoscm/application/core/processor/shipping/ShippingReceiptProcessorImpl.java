package com.zhihaoscm.application.core.processor.shipping;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.zhihaoscm.application.config.LocalDateAdapter;
import com.zhihaoscm.application.config.LocalDateTimeAdapter;
import com.zhihaoscm.application.core.processor.application.ApplicationReceiptProcessor;
import com.zhihaoscm.application.core.service.*;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.CustomerBankInfo;
import com.zhihaoscm.domain.bean.json.DeliveryInfo;
import com.zhihaoscm.domain.meta.ApplicationDef.ApplicationType;
import com.zhihaoscm.domain.meta.biz.OwnerShippingDepositDef;
import com.zhihaoscm.domain.meta.biz.ShippingCallbackDef;
import com.zhihaoscm.domain.meta.biz.ShippingRequirementPlatDef;
import com.zhihaoscm.domain.utils.ThreadPoolUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 船运需求回调处理器
 *
 * <AUTHOR>
 * @since 2.25.0
 */
@Service
@Slf4j
public class ShippingReceiptProcessorImpl extends ApplicationReceiptProcessor {
	@Autowired
	private TransportOrderShipService transportOrderShipService;
	@Autowired
	private TransportOrderDetailsShipService transportOrderDetailsShipService;
	@Autowired
	private ApplicationService applicationService;
	@Autowired
	private ShippingRequirementAcceptService shippingRequirementAcceptService;
	@Autowired
	private ApplicationPushRecordService applicationPushRecordService;
	@Autowired
	private ShippingRequirementPlatService shippingRequirementPlatService;
	@Autowired
	private OwnerShippingDepositService ownerShippingDepositService;

	@Override
	public void handle(ApplicationType type, Integer action, String id) {
		if (!support(type)) {
			log.error("不支持的回调模块类型！");
			return;
		}
		ShippingCallbackDef.PushType pushType = ShippingCallbackDef.PushType
				.from(action);
		if (Objects.isNull(pushType)) {
			log.error("错误的船运需求动作类型！");
			return;
		}
		handleReceipt(pushType, id);
	}

	/**
	 * 处理船运需求回调数据并回调对方接口
	 *
	 * @param type
	 * @param id
	 */
	private void handleReceipt(ShippingCallbackDef.PushType type, String id) {
		ThreadPoolUtil.scheduleTask(() -> {
			switch (type) {
				case SHIP_ACCEPT -> this.handleShipAccept(id, type);
				case UPDATE_SHIP_ACCEPT, UPDATE_ACCEPT ->
					this.handleUpShipAccept(id, type);
				case UPDATE_SHIP_ACCEPT_CREATE_ORDER ->
					this.handleAcceptOrder(id, type);
				case CONFIRM_RECEIVE_DEPOSIT ->
					this.handleConfirmRecDeposit(id, type);
				case REJECT_SHIP_ACCEPT ->
					this.handleRejectShipAccept(id, type);
				case START_LOADING -> this.handleStartLoading(id, type);
				case APPLY_SAILING -> this.handleApplySailing(id, type);
				case CONFIRM_ARRIVAL_PORT ->
					this.handleConfirmArrivalPort(id, type);
				case CONFIRM_UNLOAD -> this.handleConfirmUnload(id, type);
				case COMPLETE_ORDER -> this.handleCompleteOrder(id, type);
				case UPDATE_DELIVERY -> this.handleUpDelivery(id, type);
				case UPDATE_GEAR_SHIFT -> this.handleUpGearShift(id, type);
				case UPDATE_DEPARTURE_PREPARATION,
						UPLOAD_THE_DEPARTURE_INFORMATION ->
					this.handleUpDeparturePreparation(id, type);
				case UPDATE_SET_SAIL -> this.handleUpSetSail(id, type);
				case UPDATE_UNLOADING, UPLOAD_UNLOADING_INFORMATION ->
					this.handleUpUnloading(id, type);
				case UPLOAD_DRAINAGE_VIDEO ->
					this.handleUpDrainageVideo(id, type);
				case CANCEL_SHIP_ACCEPT ->
					this.handleCancelShipAccept(id, type);
				case PAY_OR_CONFIRM_SERVICE ->
					this.handlePayOrConfirmService(id, type);
				case UPLOAD_BANK_INFO -> this.handleUploadBankInfo(id, type);
				case COMPLETE_PLAT -> this.handleCompletePlat(id, type);
				case CLOSE_PLAT, UPDATE_SHIP_PLAT ->
					this.handleClosePlat(id, type);
				case CLOSE_ORDER -> this.handleCloseOrder(id, type);
				case CONFIRM_DEPARTURE, AGREE_UNLOAD ->
					this.handleConfirmDeparture(id, type);
				case HMG_PAY_SUCCESS, PAY_OWNER_SHIPPING_DEPOSIT ->
					this.handleHmgPaySuccess(id, type);
				case HMG_PUSH_PAY_FILE -> this.handleHmgPushPayFile(id, type);
			}
		}, 3L, TimeUnit.SECONDS, ThreadPoolUtil.getCaptureExecutor());
	}

	/**
	 * 船主上传银行信息
	 *
	 * @param id
	 * @param type
	 */
	private void handleUploadBankInfo(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);

		if (Objects.nonNull(transportOrderShip)) {
			// 船主银行信息
			Long captainBankId = transportOrderShip.getCaptainBankId();
			CustomerBankInfo captainBankInfo = transportOrderShip
					.getCaptainBankInfo();
			JsonObject jsonObject1 = this.convertToJsonObject(captainBankInfo);
			jsonObject.addProperty("captainBankInfo",
					getGson().toJson(jsonObject1));
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			jsonObject.addProperty("captainBankId", captainBankId);
			jsonObject.addProperty("id", id);
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 1 链云船主接单信息
	private void handleShipAccept(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		ShippingRequirementAccept shippingRequirementAccept = shippingRequirementAcceptService
				.findOne(Long.valueOf(id)).orElse(null);
		if (Objects.nonNull(shippingRequirementAccept)) {
			JsonObject jsonObject1 = this
					.convertToJsonObject(shippingRequirementAccept);
			jsonObject.addProperty("shippingRequirementAccept",
					getGson().toJson(jsonObject1));
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(shippingRequirementAccept.getSourceAppId(), jsonObject,
					type.getName());
		}
	}

	// 2 修改船主接单信息
	private void handleUpShipAccept(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		ShippingRequirementAccept shippingRequirementAccept = shippingRequirementAcceptService
				.findOne(Long.valueOf(id)).orElse(null);
		if (Objects.nonNull(shippingRequirementAccept)) {
			JsonObject jsonObject1 = this
					.convertToJsonObject(shippingRequirementAccept);
			// 接单单据
			jsonObject.addProperty("shippingRequirementAccept",
					getGson().toJson(jsonObject1));
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(shippingRequirementAccept.getSourceAppId(), jsonObject,
					type.getName());
		}
	}

	// 3 修改船主接单信息并生成船运单
	private void handleAcceptOrder(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			// 抢单信息id不为空时
			if (Objects.nonNull(transportOrderShip.getSraId())) {
				ShippingRequirementAccept shippingRequirementAccept = shippingRequirementAcceptService
						.findOne(transportOrderShip.getSraId()).orElse(null);
				if (Objects.nonNull(shippingRequirementAccept)) {
					JsonObject jsonObject1 = this
							.convertToJsonObject(shippingRequirementAccept);
					// 抢单信息id
					jsonObject.addProperty("shippingRequirementAccept",
							getGson().toJson(jsonObject1));
				}
			}
			JsonObject jsonObject2 = this
					.convertToJsonObject(transportOrderShip);
			// 船运单单据id
			jsonObject.addProperty("transportOrderShip",
					new Gson().toJson(jsonObject2));
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 4 链云船主确认收到定金
	private void handleConfirmRecDeposit(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		OwnerShippingDeposit ownerShippingDeposit = ownerShippingDepositService
				.findOne(id).orElse(null);
		if (Objects.nonNull(ownerShippingDeposit)) {
			TransportOrderShip transportOrderShip = transportOrderShipService
					.findOne(ownerShippingDeposit.getRelationCode())
					.orElse(null);
			if (Objects.nonNull(transportOrderShip)) {
				JsonObject jsonObject1 = this
						.convertToJsonObject(ownerShippingDeposit);
				// 船运费用信息
				jsonObject.addProperty("ownerShippingDeposit",
						getGson().toJson(jsonObject1));
				// 设置推送枚举
				jsonObject.addProperty("pushType", type.getCode());
				// 将数据推送到对方
				pushToTarget(transportOrderShip.getAppId(), jsonObject,
						type.getName());
			}
		}
	}

	// 5 链云管理后台拒绝船主接单
	private void handleRejectShipAccept(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		ShippingRequirementAccept shippingRequirementAccept = shippingRequirementAcceptService
				.findOneWithDeleted(Long.valueOf(id)).orElse(null);
		if (Objects.nonNull(shippingRequirementAccept)) {
			// 抢单信息id
			jsonObject.addProperty("id", shippingRequirementAccept.getId());
			// 拒绝原因
			jsonObject.addProperty("rejectReason",
					shippingRequirementAccept.getRejectReason());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(shippingRequirementAccept.getSourceAppId(), jsonObject,
					type.getName());
		}
	}

	// 6 链云平台点开始装货
	private void handleStartLoading(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			jsonObject.addProperty("id", transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 7 发航申请
	private void handleApplySailing(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			jsonObject.addProperty("id", transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 9 到港确认
	private void handleConfirmArrivalPort(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		Long appId = null;
		if (Objects.nonNull(transportOrderShip)) {
			// 船运单id
			jsonObject.addProperty("id", transportOrderShip.getId());
			appId = transportOrderShip.getAppId();
		}
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderDetailsShip)) {
			// 到港视频id
			jsonObject.addProperty("arrivalVideoId",
					transportOrderDetailsShip.getArrivalVideoId());
			// 船主确认到港时间
			jsonObject.addProperty("captainArrivalTime", String.valueOf(
					transportOrderDetailsShip.getCaptainArrivalTime()));
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());

		}
		if (Objects.nonNull(appId)) {
			// 将数据推送到对方
			pushToTarget(appId, jsonObject, type.getName());
		}

	}

	// 10 确认卸货
	private void handleConfirmUnload(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			// 船运单id
			jsonObject.addProperty("id", transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 11 完成船运单
	private void handleCompleteOrder(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			jsonObject.addProperty("id", transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 12 修改船运单明细的发货信息
	private void handleUpDelivery(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderDetailsShip)
				&& Objects.nonNull(transportOrderShip)) {
			// 发货信息
			DeliveryInfo deliveryInfo = transportOrderDetailsShip
					.getDeliveryInfo();
			jsonObject.addProperty("deliveryInfo",
					getGson().toJson(deliveryInfo));
			// 单据id
			jsonObject.addProperty("id", transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 13 修改船运单明细的接档信息
	private void handleUpGearShift(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)
				&& Objects.nonNull(transportOrderDetailsShip)) {
			jsonObject.addProperty("jdInfo",
					getGson().toJson(transportOrderDetailsShip.getJdInfo()));
			// 单据id
			jsonObject.addProperty("id", transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 14 修改船运单明细的离港准备信息
	private void handleUpDeparturePreparation(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)
				&& Objects.nonNull(transportOrderDetailsShip)) {
			// 离港准备信息
			jsonObject.addProperty("lazbInfo",
					getGson().toJson(transportOrderDetailsShip.getLazbInfo()));
			// 单据id
			jsonObject.addProperty("id", transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 15 修改船运单明细的发航信息
	private void handleUpSetSail(String id, ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)
				&& Objects.nonNull(transportOrderDetailsShip)) {
			// 发航信息
			jsonObject.addProperty("setSailInfo", getGson()
					.toJson(transportOrderDetailsShip.getSetSailInfo()));
			// 单据id
			jsonObject.addProperty("id", transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 16 修改船运单明细的卸货信息
	private void handleUpUnloading(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)
				&& Objects.nonNull(transportOrderDetailsShip)) {
			// 卸货信息
			jsonObject.addProperty("unloadingInfo", getGson()
					.toJson(transportOrderDetailsShip.getUnloadingInfo()));
			// 单据id
			jsonObject.addProperty("id", transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 17 上传排水视频
	private void handleUpDrainageVideo(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)
				&& Objects.nonNull(transportOrderDetailsShip)) {
			// 排水视频id
			jsonObject.addProperty("drainageVideoId",
					transportOrderDetailsShip.getDrainageVideoId());
			// 单据id
			jsonObject.addProperty("id", transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 18 船主取消接单
	private void handleCancelShipAccept(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		ShippingRequirementAccept shippingRequirementAccept = shippingRequirementAcceptService
				.findOneWithDeleted(Long.valueOf(id)).orElse(null);
		if (Objects.nonNull(shippingRequirementAccept)) {
			// 抢单信息id
			jsonObject.addProperty("id", shippingRequirementAccept.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(shippingRequirementAccept.getSourceAppId(), jsonObject,
					type.getName());
		}
	}

	// 19 船主线上支付信息服务费
	private void handlePayOrConfirmService(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			JsonObject jsonObject1 = this
					.convertToJsonObject(transportOrderShip);
			// 船运单信息
			jsonObject.addProperty("transportOrderShip",
					getGson().toJson(jsonObject1));
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	/**
	 * 结束船运需求
	 *
	 * @param id
	 * @param type
	 */
	private void handleCompletePlat(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		ShippingRequirementPlat shippingRequirementPlat = shippingRequirementPlatService
				.findOne(id).orElse(null);
		// 根据platId找出船运需求 且不是三方需求的数据
		List<ShippingRequirementAccept> accepts = shippingRequirementAcceptService
				.findByPlatId(id).stream()
				.filter(shippingRequirementAccept -> ShippingRequirementPlatDef.DataSource.INNER
						.match(shippingRequirementAccept.getDataSource()))
				.toList();
		if (Objects.nonNull(shippingRequirementPlat)) {
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 船运需求信息
			jsonObject.addProperty("shippingRequirementPlat",
					getGson().toJson(shippingRequirementPlat));
			jsonObject.addProperty("acceptIds", getGson().toJson(accepts
					.stream().map(ShippingRequirementAccept::getId).toList()));
			// 将数据推送到对方
			pushToTarget(shippingRequirementPlat.getSourceAppId(), jsonObject,
					type.getName());
		}
	}

	/**
	 * 关闭船运需求
	 *
	 * @param id
	 * @param type
	 */
	private void handleClosePlat(String id, ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		ShippingRequirementPlat shippingRequirementPlat = shippingRequirementPlatService
				.findOne(id).orElse(null);

		if (Objects.nonNull(shippingRequirementPlat)) {
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 船运单信息
			jsonObject.addProperty("shippingRequirementPlat",
					getGson().toJson(shippingRequirementPlat));
			// 将数据推送到对方
			pushToTarget(shippingRequirementPlat.getSourceAppId(), jsonObject,
					type.getName());
		}
	}

	// 11 完成船运单
	private void handleCloseOrder(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOneWithDeleted(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			ShippingRequirementAccept shippingRequirementAccept = shippingRequirementAcceptService
					.findOne(transportOrderShip.getSraId()).orElse(null);
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 船运单信息
			jsonObject.addProperty("transportOrderShip",
					getGson().toJson(transportOrderShip));
			if (Objects.nonNull(shippingRequirementAccept)) {
				// 接单成功的报价信息
				jsonObject.addProperty("shippingRequirementAccept",
						getGson().toJson(shippingRequirementAccept));
			}
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	// 25 发航确认
	private void handleConfirmDeparture(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipService
					.findOne(transportOrderShip.getId()).orElse(null);
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			// 船运单信息
			jsonObject.addProperty("transportOrderShip",
					getGson().toJson(transportOrderShip));
			if (Objects.nonNull(transportOrderDetailsShip)) {
				// 船运单明细信息
				jsonObject.addProperty("transportOrderDetailsShip",
						getGson().toJson(transportOrderDetailsShip));
			}
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	/**
	 * 服务商垫付时黄码港支付成功、管理后台代货主自行支付运费
	 * 
	 * @Param id 船运单id
	 */
	private void handleHmgPaySuccess(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		TransportOrderShip transportOrderShip = transportOrderShipService
				.findOne(id).orElse(null);
		if (Objects.nonNull(transportOrderShip)) {
			List<OwnerShippingDeposit> ownerShippingDeposit = ownerShippingDepositService
					.findByRelationCode(transportOrderShip.getId());
			// 设置推送枚举
			jsonObject.addProperty("pushType", type.getCode());
			if (CollectionUtils.isNotEmpty(ownerShippingDeposit)) {
				// 过滤支付成功的
				List<OwnerShippingDeposit> list = ownerShippingDeposit.stream()
						.filter(osd -> OwnerShippingDepositDef.State.PAID
								.match(osd.getState()))
						.toList();
				// 船运单明细信息
				jsonObject.addProperty("ownerShippingDepositList",
						getGson().toJson(list));
			}
			// 将数据推送到对方
			pushToTarget(transportOrderShip.getAppId(), jsonObject,
					type.getName());
		}
	}

	/**
	 * 黄码港推送结算凭证
	 *
	 * @Param id 船运费用id
	 */
	private void handleHmgPushPayFile(String id,
			ShippingCallbackDef.PushType type) {
		JsonObject jsonObject = new JsonObject();
		OwnerShippingDeposit ownerShippingDeposit = ownerShippingDepositService
				.findOne(id).orElse(null);
		if (Objects.nonNull(ownerShippingDeposit)) {
			TransportOrderShip transportOrderShip = transportOrderShipService
					.findOne(ownerShippingDeposit.getRelationCode())
					.orElse(null);
			if (Objects.nonNull(transportOrderShip)) {
				// 设置推送枚举
				jsonObject.addProperty("pushType", type.getCode());
				// 船运费用明细信息
				jsonObject.addProperty("ownerShippingDeposit",
						getGson().toJson(ownerShippingDeposit));
				// 将数据推送到对方
				pushToTarget(transportOrderShip.getAppId(), jsonObject,
						type.getName());
			}
		}
	}

	// 将对象转换为JsonObject
	public <T> JsonObject convertToJsonObject(T object) {
		Gson gson = getGson();
		// 首先将对象序列化为 JSON 字符串
		String jsonString = gson.toJson(object);
		return gson.fromJson(jsonString, JsonObject.class);
	}

	private Gson getGson() {
		return new GsonBuilder()
				.registerTypeAdapter(LocalDate.class, new LocalDateAdapter())
				.registerTypeAdapter(LocalDateTime.class,
						new LocalDateTimeAdapter())
				.create();
	}

	@Override
	public ApplicationService getApplicationService() {
		return applicationService;
	}

	@Override
	public Boolean support(ApplicationType type) {
		return getType().match(type.getCode());
	}

	@Override
	public ApplicationType getType() {
		return ApplicationType.SHIPPING_REQUIREMENT;
	}

	@Override
	public ApplicationPushRecordService getApplicationPushRecordService() {
		return applicationPushRecordService;
	}
}
