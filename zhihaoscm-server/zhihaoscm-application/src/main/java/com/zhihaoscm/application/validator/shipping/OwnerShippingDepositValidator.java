package com.zhihaoscm.application.validator.shipping;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.application.config.security.application.ApplicationInfoContextHolder;
import com.zhihaoscm.application.core.service.OwnerShippingDepositService;
import com.zhihaoscm.application.validator.ship.TransportOrderShipValidator;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.dto.OwnerShippingDepositDto;
import com.zhihaoscm.domain.bean.dto.shippingconnect.OwnerShippingDepositPayDto;
import com.zhihaoscm.domain.bean.entity.OwnerShippingDeposit;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.meta.biz.OwnerShippingDepositDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderShipDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;

/**
 * 货主运单定金校验器
 */
@Component
public class OwnerShippingDepositValidator {

	@Autowired
	private OwnerShippingDepositService ownerShippingDepositService;

	@Autowired
	private TransportOrderShipValidator transportOrderShipValidator;

	/**
	 * 校验存在
	 *
	 * @param id
	 * @return
	 */
	public OwnerShippingDeposit validateExist(String id) {
		return ownerShippingDepositService.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30044001));
	}

	/**
	 * 校验支付定金或运费-冈好运只能自行支付
	 * 
	 * @param form
	 * @return
	 */
	public OwnerShippingDepositDto validatePay(
			OwnerShippingDepositPayDto form) {

		TransportOrderShip transportOrderShip = transportOrderShipValidator
				.validateExist(form.getRelationCode());

		if (TransportOrderShipDef.DepositPayType.PAY_ONESELF
				.match(transportOrderShip.getPayType())) {
			// 支付的时候校验船主是否有银行账号
			if (Objects.isNull(transportOrderShip.getCaptainBankInfo())) {
				throw new BadRequestException(ErrorCode.CODE_30044015);
			}
		}

		// 需要校验当前船运单是否是三方的
		// 判断是否是该三方平台的船运单
		if (!transportOrderShip.getAppId()
				.equals(ApplicationInfoContextHolder.getAppId())) {
			throw new BadRequestException(ErrorCode.CODE_30044022);
		}

		// 冈好运只能支付“自行支付”的，服务商垫付的需要链云后台发起
		if (!TransportOrderShipDef.DepositPayType.PAY_ONESELF
				.match(transportOrderShip.getPayType())) {
			throw new BadRequestException(ErrorCode.CODE_30044023);
		}

		List<OwnerShippingDeposit> all = ownerShippingDepositService
				.findByRelationCode(form.getRelationCode());

		// 自行支付
		if (!(OwnerShippingDepositDef.Type.DEPOSIT.getCode()
				.equals(form.getType())
				|| OwnerShippingDepositDef.Type.FREIGHT.getCode()
						.equals(form.getType()))) {
			throw new BadRequestException(ErrorCode.CODE_30044013);
		}
		// 第一笔必须是定金
		if (CollectionUtils.isEmpty(all)) {
			if (!OwnerShippingDepositDef.Type.DEPOSIT.getCode()
					.equals(form.getType())) {
				throw new BadRequestException(ErrorCode.CODE_30044012);
			}
		}

		// 判断第一笔定金-待支付定金的时候才能操作
		if (OwnerShippingDepositDef.Type.DEPOSIT.match(form.getType())) {
			if (!TransportOrderShipDef.State.DEPOSIT_TO_BE_PAID
					.match(transportOrderShip.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30044019);
			}
		}

		// 运费 必须是 待卸货 卸货中 已清仓 已完成
		if (OwnerShippingDepositDef.Type.FREIGHT.match(form.getType())) {
			if (!(TransportOrderShipDef.State.TO_BE_UNLOADED
					.match(transportOrderShip.getState())
					|| TransportOrderShipDef.State.DURING_UNLOADING
							.match(transportOrderShip.getState())
					|| TransportOrderShipDef.State.DISCHARGED
							.match(transportOrderShip.getState())
					|| TransportOrderShipDef.State.COMPLETED
							.match(transportOrderShip.getState()))) {
				throw new BadRequestException(ErrorCode.CODE_30044018);
			}
		}

		// 判断是否存在一样类型的数据
		OwnerShippingDeposit oldOwnerShippingDeposit = ownerShippingDepositService
				.findByRelationCodeAndType(form.getRelationCode(),
						form.getType())
				.orElse(null);
		if (Objects.nonNull(oldOwnerShippingDeposit)) {
			throw new BadRequestException(ErrorCode.CODE_30044007);
		}

		OwnerShippingDepositDto depositDto = new OwnerShippingDepositDto();
		depositDto.setTransportOrderShip(transportOrderShip);
		depositDto.setOwnerShippingDeposit(form.convertToEntity());
		return depositDto;
	}

	/**
	 * 校验重新支付定金
	 * 
	 * @param id
	 *            定金id
	 * @param form
	 * @return
	 */
	public OwnerShippingDepositDto validateResetPay(String id,
			OwnerShippingDepositPayDto form) {
		OwnerShippingDeposit ownerShippingDeposit = this.validateExist(id);

		TransportOrderShip transportOrderShip = transportOrderShipValidator
				.validateExist(form.getRelationCode());

		// 需要校验当前船运单是否是三方的
		// 判断是否是该三方平台的船运单
		if (!transportOrderShip.getAppId()
				.equals(ApplicationInfoContextHolder.getAppId())) {
			throw new BadRequestException(ErrorCode.CODE_30044022);
		}

		// 冈好运只能支付“自行支付”的，服务商垫付的需要链云后台发起
		if (!TransportOrderShipDef.DepositPayType.PAY_ONESELF
				.match(transportOrderShip.getPayType())) {
			throw new BadRequestException(ErrorCode.CODE_30044023);
		}

		// 船主确认不能再确认
		if (CommonDef.Symbol.YES
				.match(ownerShippingDeposit.getCaptainConfirmState())) {
			throw new BadRequestException(ErrorCode.CODE_30044014);
		}

		OwnerShippingDepositDto depositDto = new OwnerShippingDepositDto();

		depositDto.setTransportOrderShip(transportOrderShip);
		depositDto.setOwnerShippingDeposit(
				form.convertToEntity(ownerShippingDeposit));
		return depositDto;
	}
}
