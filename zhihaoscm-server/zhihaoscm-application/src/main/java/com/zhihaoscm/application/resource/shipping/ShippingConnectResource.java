package com.zhihaoscm.application.resource.shipping;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.application.core.service.OwnerShippingDepositService;
import com.zhihaoscm.application.core.service.ShippingRequirementAcceptService;
import com.zhihaoscm.application.core.service.ShippingRequirementPlatService;
import com.zhihaoscm.application.core.service.TransportOrderShipService;
import com.zhihaoscm.application.validator.ship.TransportOrderDetailsShipValidator;
import com.zhihaoscm.application.validator.ship.TransportOrderShipValidator;
import com.zhihaoscm.application.validator.shipping.OwnerShippingDepositValidator;
import com.zhihaoscm.application.validator.shipping.ShippingRequirementAcceptValidator;
import com.zhihaoscm.application.validator.shipping.ShippingRequirementPlatValidator;
import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.domain.bean.dto.OwnerShippingDepositDto;
import com.zhihaoscm.domain.bean.dto.shippingconnect.*;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementAccept;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.entity.TransportOrderDetailsShip;
import com.zhihaoscm.domain.bean.entity.TransportOrderShip;
import com.zhihaoscm.domain.bean.vo.ShippingPlatAndTransportVo;
import com.zhihaoscm.domain.bean.vo.TransportOrderShipVo;
import com.zhihaoscm.domain.meta.ApplicationDef;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: 许晶
 * @create: 2025-02-27
 **/
@Slf4j
@Tag(name = "应用管理-船运对接", description = "应用管理-船运对接API")
@RestController
@RequestMapping("/application/shipping/connect")
public class ShippingConnectResource {
	@Autowired
	private ShippingRequirementPlatService shippingRequirementPlatService;
	@Autowired
	private ShippingRequirementPlatValidator platValidator;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private ShippingRequirementAcceptService acceptService;
	@Autowired
	private ShippingRequirementAcceptValidator acceptValidator;
	@Autowired
	private TransportOrderShipService transportOrderShipService;
	@Autowired
	private TransportOrderShipValidator transportOrderShipValidator;
	@Autowired
	private TransportOrderDetailsShipValidator transportOrderDetailsShipValidator;
	@Autowired
	private OwnerShippingDepositService ownerShippingDepositService;
	@Autowired
	private OwnerShippingDepositValidator ownerShippingDepositValidator;

	@Operation(summary = "新增船运需求")
	@PostMapping("/create/plat")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<String> handleCreatePlat(
			@Validated @RequestBody ShippingRequirementPlatDto form) {
		platValidator.validateCreatePlat(form);
		return new ApiResponse<>(shippingRequirementPlatService
				.createPlat(form.convertToEntity(redisClient)));

	}

	@Operation(summary = "取消船运需求")
	@PostMapping(value = "/cancel/plat/{id}")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<Void> handleCancelPlat(@PathVariable String id) {
		platValidator.validateCancelPlat(id);
		shippingRequirementPlatService.complete(id);
		return new ApiResponse<>();
	}

	@Operation(summary = "新增船主接单")
	@PostMapping(value = "/taking/orders")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<Long> handleCreateAccept(
			@Validated @RequestBody CustomShippingRequirementAcceptDto form) {
		acceptValidator.validateCreateAccept(form);
		ShippingRequirementPlat plat = shippingRequirementPlatService
				.findOne(form.getRequirementPlatId()).orElse(null);
		return new ApiResponse<>(
				acceptService.createAccept(form.convertToEntity(plat)));
	}

	@Operation(summary = "承运商修改接单")
	@PostMapping(value = "/update-accept")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<Void> handleUpdateAccept(
			@Validated @RequestBody CustomShippingRequirementAcceptDto form) {
		ShippingRequirementAccept accept = acceptValidator
				.validateUpdateAccept(form);
		acceptService.update(form.convertToEntity(form.getId(), accept));
		return new ApiResponse<>();
	}

	@Operation(summary = "承运商取消接单")
	@PostMapping(value = "/cancel/{id}")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<Void> handleCancelAccept(@PathVariable Long id) {
		acceptValidator.validateCancelAccept(id);
		acceptService.cancel(id);
		return new ApiResponse<>();
	}

	@Operation(summary = "拒绝接单")
	@PostMapping(value = "/reject")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<Void> handlePlatformRejectAccept(
			@Validated @RequestBody AcceptRejectDto form) {
		acceptValidator.validateRejectPlat(form.getId(),
				form.getRejectReason());
		acceptService.rejectAccept(form.getId(), form.getRejectReason());
		return new ApiResponse<>();
	}

	@Operation(summary = "修改接单")
	@PostMapping(value = "/update")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<Void> handlePlatformUpdateAccept(
			@Validated @RequestBody ShippingRequirementAcceptDto form) {
		ShippingRequirementAccept accept = acceptValidator
				.validatePlatformUpdatePlat(form);
		acceptService.updateAccept(form.convertToEntity(accept));
		return new ApiResponse<>();
	}

	@Operation(summary = "发航确认")
	@PostMapping(value = "/confirmation-departure/{id}")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<Void> confirmationDeparture(@PathVariable String id,
			@Validated @RequestBody ConfirmationDepartureDto form) {
		transportOrderDetailsShipValidator
				.validateOuterConfirmationDeparture(id);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipValidator
				.validateExist(id);
		transportOrderShipService.confirmationDeparture(
				form.convertToEntity(transportOrderDetailsShip));
		return new ApiResponse<>();
	}

	@Operation(summary = "同意卸货")
	@PostMapping(value = "/agree-unload/{id}")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<Void> agreeUnload(@PathVariable String id) {
		transportOrderDetailsShipValidator.validateOuterAgreeUnload(id);
		TransportOrderDetailsShip transportOrderDetailsShip = transportOrderDetailsShipValidator
				.validateExist(id);
		transportOrderShipService.agreeUnload(transportOrderDetailsShip);
		return new ApiResponse<>();
	}

	@Operation(summary = "查询船运单详情")
	@GetMapping("/vo/{id}")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<TransportOrderShipVo> findVoById(
			@PathVariable String id) {
		transportOrderShipValidator.validateExist(id);
		return new ApiResponse<>(
				transportOrderShipService.findVoById(id).orElse(null));
	}

	@Operation(summary = "唯一搜索链云船运单编号-过滤appId为空数据")
	@GetMapping("/vo/app-is-empty/{id}")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<TransportOrderShipVo> findVoByIdAndAppIsEmpty(
			@PathVariable String id) {
		return new ApiResponse<>(transportOrderShipService
				.findVoByIdAndAppIsEmpty(id).orElse(null));
	}

	@Operation(summary = "绑定appId")
	@PutMapping("/bind-app/{id}")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<Void> bindApp(@PathVariable String id,
			@RequestBody TransportOrderShipAppBindDto form) {
		TransportOrderShip transportOrderShip = transportOrderShipValidator
				.validateBindApp(id, form);
		transportOrderShipService
				.update(form.convertToEntity(transportOrderShip));
		return new ApiResponse<>();
	}

	@Operation(summary = "自行支付-支付定金或运费")
	@PostMapping("/pay")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<String> pay(
			@Validated @RequestBody OwnerShippingDepositPayDto form) {
		OwnerShippingDepositDto depositDto = ownerShippingDepositValidator
				.validatePay(form);
		return new ApiResponse<>(ownerShippingDepositService.pay(depositDto));
	}

	@Operation(summary = "自行支付-重新支付定金或运费")
	@PutMapping("/reset-pay/{id}")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<Void> resetPay(@PathVariable String id,
			@Validated @RequestBody OwnerShippingDepositPayDto form) {
		OwnerShippingDepositDto depositDto = ownerShippingDepositValidator
				.validateResetPay(id, form);
		ownerShippingDepositService.resetPay(depositDto);
		return new ApiResponse<>();
	}

	@Operation(summary = "根据船运需求id查询船运需求及船运单详情")
	@GetMapping("/plat-transport")
	@Secured({ ApplicationDef.SHIP_TRANSFER_PUSH_OWNER_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_PUBLISH,
			ApplicationDef.SHIP_TRANSFER_PUSH_REQUIREMENT_BILL,
			ApplicationDef.SHIP_TRANSFER_CONNECT_REQUIREMENT,
			ApplicationDef.SHIP_TRANSFER_CONNECT_TRANSPORT_BILL })
	public ApiResponse<ShippingPlatAndTransportVo> findShippingPlatAndTransportVoById(
			@Parameter(description = "船运需求id") @RequestParam(value = "platId") String platId) {
		return new ApiResponse<>(shippingRequirementPlatService
				.findShippingPlatAndTransportVoById(platId));
	}
}
