package com.zhihaoscm.application.client;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.domain.bean.entity.ShippingRequirementAccept;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.vo.ShippingPlatAndTransportVo;

/**
 * @program: codeSpace
 * @ClassName ShippingRequirementPlatClient
 * @description:
 * @author: 魏志鹏
 * @create: 2025-04-22 10 48
 * @Version 1.0
 **/
@FeignClient(name = "zhihaoscm-service", path = "/shipping/plat", url = "${application.config.zhihaoscm-service}")
public interface ShippingRequirementPlatClient {

    @GetMapping("/{id}")
    ShippingRequirementPlat findById(@PathVariable String id);

    @PostMapping(value = "/create/plat")
    String createPlat(@RequestBody ShippingRequirementPlat shippingRequirementPlat);

    @PostMapping(value = "/complete")
    void complete(@RequestParam String id);

    @GetMapping(value = "/find/plat/id")
    List<ShippingRequirementAccept> findByPlatId(
            @RequestParam String platId);

    @PostMapping(value = "/create/accept")
    Long createAccept(@RequestBody ShippingRequirementAccept shippingRequirementAccept);

    @PutMapping(value = "/cancel")
    void cancel(@RequestParam Long id);

    @PostMapping(value = "/reject/accept")
    void rejectAccept(@RequestParam Long id, @RequestParam String rejectReason);

    @PostMapping(value = "/update/accept")
    void updateAccept(@RequestBody ShippingRequirementAccept shipping);

    @PutMapping
    void update(@RequestBody ShippingRequirementAccept shipping);

	@GetMapping("/plat-transport")
	ShippingPlatAndTransportVo findShippingPlatAndTransportVoById(
			@RequestParam String platId);
}
