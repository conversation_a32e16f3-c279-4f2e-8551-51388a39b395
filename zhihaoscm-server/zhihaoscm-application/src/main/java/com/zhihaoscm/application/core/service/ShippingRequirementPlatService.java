package com.zhihaoscm.application.core.service;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zhihaoscm.application.client.ShippingRequirementPlatClient;
import com.zhihaoscm.domain.bean.entity.ShippingRequirementPlat;
import com.zhihaoscm.domain.bean.vo.ShippingPlatAndTransportVo;

/**
 * @program: codeSpace
 * @ClassName TransportOrderShipService
 * @description:
 * @author: 魏志鹏
 * @create: 2025-04-22 10 14
 * @Version 1.0
 **/
@Service
public class ShippingRequirementPlatService {

    @Autowired
    private ShippingRequirementPlatClient client;

    public Optional<ShippingRequirementPlat> findOne(String id){
        return Optional.ofNullable(client.findById(id));
    }

    public String createPlat(ShippingRequirementPlat shippingRequirementPlat){
        return client.createPlat(shippingRequirementPlat);
    }

    /**
     * 结束需求
     *
     * @param id
     */
    public void complete(String id){
        client.complete(id);
    }

	public ShippingPlatAndTransportVo findShippingPlatAndTransportVoById(
			String platId) {
		return client.findShippingPlatAndTransportVoById(platId);
	}
}
