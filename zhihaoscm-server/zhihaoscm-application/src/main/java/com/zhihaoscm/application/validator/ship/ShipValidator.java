package com.zhihaoscm.application.validator.ship;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.application.core.service.DeviceService;
import com.zhihaoscm.application.core.service.ShipService;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.json.GeoPoint;
import com.zhihaoscm.domain.bean.dto.shippingconnect.ShipExtendCustomDto;
import com.zhihaoscm.domain.bean.entity.Device;
import com.zhihaoscm.domain.bean.entity.Ship;
import com.zhihaoscm.domain.meta.biz.DeviceDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.admin.RegexUtils;

@Component
public class ShipValidator {
	@Autowired
	private ShipService shipService;

	@Autowired
	private DeviceService deviceService;

	/**
	 * 船舶存在性校验
	 *
	 * @param id
	 */
	public Ship validateExist(String id) {
		Ship ship = shipService.findOne(id).orElse(null);
		if (Objects.isNull(ship)) {
			throw new BadRequestException(ErrorCode.CODE_30120024);
		}
		return ship;
	}

	/**
	 * 校验船舶设备播放
	 *
	 * @param shipId
	 * @return
	 */
	public String validateCustomPlay(String shipId) {
		Ship ship = this.validateExist(shipId);
		Device device = deviceService
				.findByMasterIdAndTransportType(ship.getId(),
						DeviceDef.TransportType.SHIP.getCode())
				.stream().findFirst().orElse(null);
		if (Objects.isNull(device)) {
			throw new BadRequestException(ErrorCode.CODE_30037010);
		}
		if (DeviceDef.State.DISABLED.match(device.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30037026);
		}
		if (DeviceDef.Online.OFFLINE.match(device.getOnline())) {
			throw new BadRequestException(ErrorCode.CODE_30037019);
		}
		return device.getSerialNo();

	}

	/**
	 * 校验MMSI
	 *
	 * @param id
	 */
	public void validateMMSI(String id) {
		if (!RegexUtils.match(RegexUtils.MMSI_PATTERN, id)) {
			throw new BadRequestException(ErrorCode.CODE_30120002);
		}
	}

	/**
	 * 校验坐标点数
	 *
	 * @param from
	 */
	public void validateExtentCustom(ShipExtendCustomDto from) {
		if (from.getGeoPointList().size() < 3) {
			throw new BadRequestException(ErrorCode.CODE_30120050);
		}
		if (!areAllDistancesWithinRange(from.getGeoPointList())) {
			throw new BadRequestException(ErrorCode.CODE_30120049);
		}
	}

	/**
	 * 判断所有坐标点之间的距离是否都在指定范围内
	 * 
	 * @param points
	 * @return
	 */
	private static boolean areAllDistancesWithinRange(List<GeoPoint> points) {
		for (int i = 0; i < points.size(); i++) {
			for (int j = i + 1; j < points.size(); j++) {
				if (Math.abs(
						points.get(i).getLat() - points.get(j).getLat()) > 2
						|| Math.abs(points.get(i).getLng()
								- points.get(j).getLng()) > 2) {
					return false;
				}
			}
		}
		return true;
	}
}
