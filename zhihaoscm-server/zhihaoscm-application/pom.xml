<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zhihaoscm</groupId>
        <artifactId>zhihaoscm-server</artifactId>
        <version>1.0.0-dev</version>
    </parent>

    <artifactId>zhihaoscm-application</artifactId>
    <version>1.0.0-dev</version>

    <properties>
        <skipTests>true</skipTests>
        <docker.image.name>zhihaoscm-application</docker.image.name>
        <docker.image.tag>1.0.0</docker.image.tag>
        <profile>default</profile>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>zhihaoscm-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-api</artifactId>
        </dependency>

<!--        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>aliyun-sms-sdk</artifactId>
        </dependency>-->

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot</artifactId>
            <version>2.2.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>wxw-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-gray</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>4.3.1</version>
        </dependency>
       <!-- <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>aliyun-captcha-sdk</artifactId>
        </dependency>-->
       <!-- <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>qiyuesuo-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>wx-miniapp-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>wx-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel-core</artifactId>
            <version>3.3.2</version>
            <scope>compile</scope>
        </dependency>-->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
       <!-- <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>aliyun-oss-sdk</artifactId>
        </dependency>-->
        <!--<dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-dypnsapi20170525</artifactId>
            <version>1.0.8</version>
        </dependency>-->
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>1.10.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-http-apache</artifactId>
            <version>0.2.15-beta</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.8.3</version>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>jwks-rsa</artifactId>
            <version>0.12.0</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.11.5</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.11.5</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.5</version>
        </dependency>
        <!--<dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-auth</artifactId>
            <version>0.2.15-beta</version>
            <scope>compile</scope>
        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>zhongjiao-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>sanyi-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>ships66-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>ships66-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j</artifactId>
            <version>1.0.0-alpha1</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-open-ai</artifactId>
            <version>1.0.0-alpha1</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-community-dashscope</artifactId>
            <version>1.0.0-alpha1</version>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>aliyun-ocr-sdk</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.zhihaoscm</groupId>
            <artifactId>coze-sdk</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <!-- here the phase you need -->
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${basedir}/target/dockerfile</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/docker</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <!--用于指定镜像名称-->
                    <imageName>${docker.image.name}:${docker.image.tag}</imageName>
                    <!--Dockerfile文件位置-->
                    <dockerDirectory>${basedir}/target/dockerfile</dockerDirectory>
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <!--用于指定需要复制的根目录，${project.build.directory}表示target目录-->
                            <directory>${project.build.directory}</directory>
                            <!--用于指定需要复制的文件。${project.build.finalName}.jar指的是打包后的jar包文件-->
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                        <resource>
                            <targetPath>/arthas</targetPath>
                            <!-- 本地 Arthas JAR 包的路径 -->
                            <directory>${project.parent.basedir}/arthas</directory>
                            <!-- 如果需要指定特定的 Arthas JAR 包文件名，可以在这里指定 -->
                            <include>arthas.jar</include>
                        </resource>
                    </resources>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
