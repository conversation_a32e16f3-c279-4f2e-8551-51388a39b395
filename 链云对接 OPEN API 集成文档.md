---

# 链云对接 OPEN API 集成文档
## 1. API 调用认证方式
### 1.1 请求头认证参数
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
| --- | --- | --- | --- | --- |
| signature | String | 是 | - | 签名 |
| applicationId | String | 是 | - | 应用ID |
| timestamp | Long | 是 | - | 参与签名的时间戳 |


### 1.2 签名生成方法
#### 1.2.1 代码实例
```java
//1. 签名方法：
public static  String calculateHMACSHA256(String data,String key) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] bytes = mac.doFinal(data.getBytes());
        StringBuffer sb = new StringBuffer();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                sb.append('0');
            }
            sb.append(hex);
        }
        return sb.toString();
    }
    
    
//2. 签名方法调用示例：
String secret = "pkb1247be0c6c74020a31376b914d27a41";
String key = "pk3eff0f7fad04421797ab72fb4c80ce90";
String timestamp = String.valueOf(System.currentTimeMillis());
System.out.println(timestamp);
List<String> params = new java.util.ArrayList<>(List.of(key, timestamp));
Collections.sort(params);
String sign = calculateHMACSHA256(String.join("",params),secret);
System.out.println(sign);
```

#### 1.2.2 测试环境凭证
```plain
测试环境：https://staging.api.zhihaoscm.cn
applicationId = "600029"
secret = "sk5713428661294ab18e0e177381d12731"
key = "pk18b990ac66df4c9dace10c0c603b118f"
```

## 2.统一响应规范
### 2.2 响应结构
```json
{
  "code": 0,
  "message": "success",
  "data": {...}
}
```

### 2.2 状态码对照表
| HTTP 状态码 | code 字段 | 含义 | 示例场景 |
| --- | --- | --- | --- |
| 200 | 200 | 成功 | 查询到在线船舶列表 |
| 400 | 400 | 请求参数错误 | 参数缺失或格式错误 |
| 403 | 403 | 资源未找到 | 无符合条件的船舶 |
| 404 | 404 | 资源未找到 | 无符合条件的船舶 |
| 500 | 500 | 服务器内部异常 | 数据查询失败 |


## 3.核心API接口说明
### 3.1 查询船舶轨迹
#### 3.1.1 接口路径
```plain
GET https://staging.api.zhihaoscm.cn/application/ship/ship-route-info
```

#### 3.1.2 请求参数

| 参数名                                                       | 类型                                                         | 描述                                                         | 是否必传                                                     |
| ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">shipId</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">String</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">船舶mmsi</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">是</font> |
| <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">start</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">Long</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">开始时间戳</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">是</font> |
| <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">end</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">Long</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">结束时间戳</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">是</font> |

#### 3.1.3 响应示例
```json
{
    "code": "0",
    "data": [
        {
            "lon": 116.725175,
            "lat": 23.2695416,
            "createdAt": 1753930777000,
            "receiveTime": null,
            "speed": 0.2,
            "sailStatus": "",
            "hdg": null,
            "cog": 150.5,
            "from": "O_ms",
            "rot": null,
            "state": "0",
            "stayTime": null
        },
        {
            "lon": 116.725125,
            "lat": 23.2703667,
            "createdAt": 1753856014000,
            "receiveTime": null,
            "speed": 0.0,
            "sailStatus": "",
            "hdg": null,
            "cog": 40.0,
            "from": "S_ls",
            "rot": null,
            "state": null,
            "stayTime": null
        },
        {
            "lon": 116.7249533,
            "lat": 23.2696266,
            "createdAt": 1753838760000,
            "receiveTime": null,
            "speed": 0.1,
            "sailStatus": "",
            "hdg": null,
            "cog": 177.8,
            "from": "O_ms",
            "rot": null,
            "state": null,
            "stayTime": null
        },
        {
            "lon": 116.7248966,
            "lat": 23.2697533,
            "createdAt": 1753815398000,
            "receiveTime": null,
            "speed": 0.0,
            "sailStatus": "",
            "hdg": null,
            "cog": 0.0,
            "from": "O_ms",
            "rot": null,
            "state": null,
            "stayTime": null
        }
    ]
}
```

#### 3.1.4 返回字段说明
| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| lon | Double | 经度 |
| lat | Double | 纬度 |
| createdAt | Long | 动态数据创建时间 |
| receiveTime | Long |  |
| speed | Double | 速度 |
| sailStatus | String | 船舶航行状态（具体状态意思，参照 AIS 航行状态码国际标准） |
| hdg | Double | 船艏向 |
| cog | Double | 航迹向 |
| from | String | 来源 |
| rot | Double | 旋转率 |
| state | String | 船舶航速分析（0 停，1 慢） |
| stayTime | String | 停留时间（航速分析中的停留时间，单位分钟） |



### 3.2 船舶动态信息 船舶信息

#### 3.2.1 接口路径
```plain
GET  https://staging.api.zhihaoscm.cn/application/ship/dynamic-info
```

#### 3.2.2 请求参数
| 参数名 | 类型 | 描述 | 是否必传 |
| --- | --- | --- | --- |
| <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">shipId</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">String</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">船舶mmsi</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">是</font> |


#### 3.2.3 响应实例
```json
{
  "code": "0",
  "data": {
    "id": "100906707",
    "type": 2,
    "cnname": "闽漳渔09323",
    "enname": null,
    "name": "MINZHANGYU09323",
    "g": "SHIP",
    "len": 21,
    "wid": 4,
    "dpth": null,
    "a": 16,
    "b": 5,
    "c": 2,
    "d": 2,
    "geom": null,
    "lon": 117.7331433,
    "imo": null,
    "dest": null,
    "lat": 23.9138567,
    "time": 1649314567000,
    "spd": 0,
    "from": "O_lsc",
    "status": null,
    "hdg": null,
    "cog": 197.7,
    "mt": "1749871319991",
    "isactive": null,
    "num": null,
    "ord": null,
    "postype": "0",
    "pt": "0",
    "firm": null,
    "gt": "",
    "eta": null,
    "ct": "1749871344000",
    "build": "",
    "callsign": "09323",
    "diff": "1",
    "tonCapacity": null,
    "tonPure": null,
    "existDevice": 0,
    "existWaterGauge": 0,
    "existMobile": 0,
    "warehouseState": null,
    "captain": null,
    "mobile": null,
    "scope": 0,
    "createTime": null,
    "isFollowed": null,
    "bulkCargoShipType": null
  }
}
```

#### 3.2.4 返回字段说明
船舶信息

| **<font style="color:rgb(44, 44, 54);">参数名</font>**  | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>**         |
| ------------------------------------------------------- | ---------------------------------------------------- | ------------------------------------------------------------ |
| <font style="color:rgb(44, 44, 54);">id</font>          | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">船舶MMSI</font>         |
| <font style="color:rgb(44, 44, 54);">type</font>        | <font style="color:rgb(44, 44, 54);">Integer</font>  | <font style="color:rgb(44, 44, 54);">船舶类型</font>         |
| <font style="color:rgb(44, 44, 54);">cnname</font>      | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">船舶中文名</font>       |
| <font style="color:rgb(44, 44, 54);">enname</font>      | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">船舶英文名</font>       |
| <font style="color:rgb(44, 44, 54);">name</font>        | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">船名</font>             |
| <font style="color:rgb(44, 44, 54);">g</font>           | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">分组信息（SHIP、VNM、RADIO、NETSONDE）</font> |
| <font style="color:rgb(44, 44, 54);">len</font>         | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">船长</font>             |
| <font style="color:rgb(44, 44, 54);">wid</font>         | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">船宽</font>             |
| <font style="color:rgb(44, 44, 54);">dpth</font>        | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">吃水</font>             |
| <font style="color:rgb(44, 44, 54);">a</font>           | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">GPS天线位置，距船首</font> |
| <font style="color:rgb(44, 44, 54);">b</font>           | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">距船尾</font>           |
| <font style="color:rgb(44, 44, 54);">c</font>           | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">距左舷</font>           |
| <font style="color:rgb(44, 44, 54);">d</font>           | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">距右舷</font>           |
| <font style="color:rgb(44, 44, 54);">geom</font>        | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">经纬度（例如"lon,lat"）</font> |
| <font style="color:rgb(44, 44, 54);">lon</font>         | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">经度</font>             |
| <font style="color:rgb(44, 44, 54);">imo</font>         | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">名称代码</font>         |
| <font style="color:rgb(44, 44, 54);">dest</font>        | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">目的地</font>           |
| <font style="color:rgb(44, 44, 54);">lat</font>         | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">纬度</font>             |
| <font style="color:rgb(44, 44, 54);">time</font>        | <font style="color:rgb(44, 44, 54);">Long</font>     | <font style="color:rgb(44, 44, 54);">创建时间</font>         |
| <font style="color:rgb(44, 44, 54);">spd</font>         | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">速度</font>             |
| <font style="color:rgb(44, 44, 54);">from</font>        | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">来源</font>             |
| <font style="color:rgb(44, 44, 54);">status</font>      | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">状态</font>             |
| <font style="color:rgb(44, 44, 54);">hdg</font>         | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">船艏向</font>           |
| <font style="color:rgb(44, 44, 54);">cog</font>         | <font style="color:rgb(44, 44, 54);">Double</font>   | <font style="color:rgb(44, 44, 54);">航迹向</font>           |
| <font style="color:rgb(44, 44, 54);">mt</font>          | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">最后更新时间</font>     |
| <font style="color:rgb(44, 44, 54);">isactive</font>    | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">备用字段（暂无作用）</font> |
| <font style="color:rgb(44, 44, 54);">num</font>         | <font style="color:rgb(44, 44, 54);">Object</font>   | <font style="color:rgb(44, 44, 54);">备用字段（暂无作用）</font> |
| <font style="color:rgb(44, 44, 54);">ord</font>         | <font style="color:rgb(44, 44, 54);">Object</font>   | <font style="color:rgb(44, 44, 54);">备用字段（暂无作用）</font> |
| <font style="color:rgb(44, 44, 54);">postype</font>     | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">设备定位类型</font>     |
| <font style="color:rgb(44, 44, 54);">pt</font>          | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">设备定位类型（可能是重复字段或有误）</font> |
| <font style="color:rgb(44, 44, 54);">firm</font>        | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">设备厂商</font>         |
| <font style="color:rgb(44, 44, 54);">gt</font>          | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">总吨</font>             |
| <font style="color:rgb(44, 44, 54);">eta</font>         | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">预计到达时间</font>     |
| <font style="color:rgb(44, 44, 54);">ct</font>          | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">动态数据最后一次更新时间</font> |
| <font style="color:rgb(44, 44, 54);">build</font>       | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">建造时间</font>         |
| <font style="color:rgb(44, 44, 54);">callsign</font>    | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">呼号</font>             |
| <font style="color:rgb(44, 44, 54);">diff</font>        | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">定位精度：差分类别</font> |
| <font style="color:rgb(44, 44, 54);">tonCapacity</font> | <font style="color:rgb(44, 44, 54);">Long</font>     | <font style="color:rgb(44, 44, 54);">载重吨数</font>         |
| <font style="color:rgb(44, 44, 54);">tonPure</font>     | <font style="color:rgb(44, 44, 54);">Long</font>     | <font style="color:rgb(44, 44, 54);">净吨数</font>           |


![](https://cdn.nlark.com/yuque/0/2025/png/25765144/1750216986094-e293de40-246d-4937-b4f3-16cf674e86bb.png)

### 3.3 新增船运需求
#### 3.3.1 接口路径
```json
POST https://staging.api.zhihaoscm.cn/application/shipping/connect/create/plat
```

#### 3.3.2 请求参数
| **<font style="color:rgb(44, 44, 54);">参数名</font>**      | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>**         | 是否必填 |
| ----------------------------------------------------------- | ---------------------------------------------------- | ------------------------------------------------------------ | -------- |
| <font style="color:rgb(44, 44, 54);">ownerEnterprise</font> | Enterprise                                           | <font style="color:rgb(44, 44, 54);">货主公司信息</font>     | 是       |
| <font style="color:rgb(44, 44, 54);">contact</font>         | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">联系人</font>           | 是       |
| <font style="color:rgb(44, 44, 54);">mobile</font>          | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">手机号</font>           | 是       |
| shipType                                                    | Integer                                              | 船型（1.散货船，2.直冲船，3.川江船，4.自卸驳，5.海轮，6.平板驳船） | 是       |
| drainageChannel                                             | Integer                                              | 排水槽（1.无水槽，2.水槽一般，3.水槽好）                     | 是       |
| sourcePortId                                                | Long                                                 | 始发地码头id                                                 | 是       |
| sourcePortName                                              | String                                               | 始发地码头名称                                               | 是       |
| destinationPortId                                           | Long                                                 | 目的地码头id                                                 | 是       |
| destinationPortName                                         | String                                               | 目的地码头名称                                               | 是       |
| goodsType                                                   | String                                               | 货物类型                                                     | 是       |
| unitPrice                                                   | BigDecimal                                           | 意向单价                                                     | 是       |
| freightTons                                                 | Integer                                              | 运货吨数                                                     | 是       |
| tonMax                                                      | Integer                                              | 运输最大吨数                                                 | 否       |
| tonMin                                                      | Integer                                              | 运输最小吨数                                                 | 否       |
| loadDate                                                    | LocalDate                                            | 预期装载日期                                                 | 是       |
| loadDays                                                    | Integer                                              | 装载日期的宽限天数                                           | 是       |
| loadUnloadDays                                              | Integer                                              | 装卸天数（取值范围为1-127）                                  | 是       |
| demurrageFee                                                | BigDecimal                                           | 可接受滞期费                                                 | 是       |
| maritimeAffairsFee                                          | Integer                                              | 海事费用(1.货主承担上游，2.货主承担下游，3.货主承担两港，4.货主不承担) | 是       |
| deposit                                                     | BigDecimal                                           | 船运定金                                                     | 否       |
| payType                                                     | Integer                                              | 船运费用支付方式（1.自行支付，2.服务商垫付）                 | 是       |
| replenishAppoint                                            | String                                               | 货主补充约定                                                 | 否       |
| specialRemark                                               | String                                               | 特殊说明                                                     | 否       |

**Enterprise类的属性如下：**

| **<font style="color:rgb(44, 44, 54);">参数名</font>**       | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>**         |
| ------------------------------------------------------------ | ---------------------------------------------------- | ------------------------------------------------------------ |
| <font style="color:rgb(44, 44, 54);">unifiedSocialCreditCode</font> | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">统一社会信用代码</font> |
| <font style="color:rgb(44, 44, 54);">name</font>             | <font style="color:rgb(44, 44, 54);">String</font>   | <font style="color:rgb(44, 44, 54);">企业名称</font>         |
| legalRepresentative                                          | String                                               | 法人代表名称                                                 |



#### 3.3.3 请求实例

```json
{
    "ownerEnterprise": {
        "unifiedSocialCreditCode": "123879453654789458",
        "name": "升速有限责任公司",
        "legalRepresentative": "张三"
    },
    "contact": "张三",
    "mobile": "12345678901",
    "shipType": 1,
    "drainageChannel": 2,
    "sourcePortId": 88,
    "sourcePortName": "南昌码头",
    "destinationPortId": 1397,
    "destinationPortName": "惠龙码头",
    "goodsType": "细砂",
    "unitPrice": 70.1,
    "freightTons": 10000,
    "tonMax": 20000,
    "loadDate": "2025-08-31",
    "loadDays": 2,
    "loadUnloadDays": 3,
    "demurrageFee": 800,
    "maritimeAffairsFee": 4,
    "deposit": 900,
    "payType": 2,
    "replenishAppoint": "测试",
    "specialRemark": "特殊说明备注"
}
```



#### 3.3.4 响应实例

```json
{
    "code": "0",
    "data": "1025070100698"
}
```

#### 3.3.5 返回字段说明
返回的是链云平台的船运需求编号，可通过此编号查询船运需求详情及对应船运单信息






### 3.4 根据船运需求id查询船运需求及船运单详情
#### 3.4.1 接口路径
```plain
GET https://staging.api.zhihaoscm.cn/application/shipping/connect/plat-transport
```

#### 3.4.2 请求参数
| 参数名                                                       | 类型                                                         | 描述                                                         | 是否必传                                                     |
| ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">platId</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">String</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">船运需求id</font> | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">是</font> |

#### 3.4.3 响应实例

船运需求没有生成船运单的情况

```json
{
    "code": "0",
    "data": {
        "shippingRequirementPlat": {
            "id": "1025070100698",
            "del": 0,
            "tenantId": null,
            "origin": null,
            "createdBy": null,
            "createdTime": "2025-07-31T14:23:30",
            "updatedBy": null,
            "updatedTime": "2025-07-31T14:23:30",
            "demandLevel": 1,
            "ownerId": 99999999999,
            "ownerEnterprise": {
                "unifiedSocialCreditCode": "123879453654789458",
                "name": "升速有限责任公司",
                "legalRepresentative": "张三",
                "realName": null,
                "mobile": null,
                "code": null,
                "signId": null
            },
            "contact": "张三",
            "mobile": "12345678901",
            "goodsType": "细砂",
            "shipType": 1,
            "drainageChannel": 2,
            "sourcePortId": 88,
            "sourcePortName": "南昌码头",
            "destinationPortId": 1397,
            "destinationPortName": "惠龙码头",
            "shipRouteId": null,
            "shipRouteSource": null,
            "shipRouteDestination": null,
            "orderShipIds": null,
            "handlerId": 26,
            "handlerName": "李章云",
            "unitPrice": 70.10,
            "freightTons": 10000,
            "tonMax": 20000,
            "tonMin": null,
            "loadDate": "2025-08-31",
            "loadDays": 2,
            "loadUnloadDays": 3,
            "demurrageFee": 800.00,
            "maritimeAffairsFee": 4,
            "replenishAppoint": "测试",
            "specialRemark": "特殊说明备注",
            "orderAcceptanceState": 1,
            "deposit": 900.00,
            "payType": 2,
            "shipInfoServiceFee": 0.00,
            "electricContactNumberType": 1,
            "electricContactNumber": "18689793687",
            "state": 1,
            "dataSource": 2,
            "sourceAppId": 600028
        },
        "transportOrderShip": null,
        "transportOrderDetailsShip": null
    }
}
```

船运需求生成了船运单的情况

```json
{
    "code": "0",
    "data": {
        "shippingRequirementPlat": {
            "id": "1025070100210",
            "del": 0,
            "tenantId": null,
            "origin": null,
            "createdBy": null,
            "createdTime": "2025-07-07T10:05:51",
            "updatedBy": 125,
            "updatedTime": "2025-07-07T10:07:43",
            "demandLevel": 1,
            "ownerId": 99999999999,
            "ownerEnterprise": {
                "unifiedSocialCreditCode": "123879453654789458",
                "name": "升速有限责任公司",
                "legalRepresentative": "蔡升",
                "realName": null,
                "mobile": null,
                "code": null,
                "signId": null
            },
            "contact": "蔡小升",
            "mobile": "***********",
            "goodsType": "船运消息0941",
            "shipType": 3,
            "drainageChannel": 2,
            "sourcePortId": 1397,
            "sourcePortName": "南昌码头",
            "destinationPortId": 1397,
            "destinationPortName": "惠龙码头",
            "shipRouteId": null,
            "shipRouteSource": null,
            "shipRouteDestination": null,
            "orderShipIds": null,
            "handlerId": 125,
            "handlerName": "张成周",
            "unitPrice": 23.77,
            "freightTons": 10000,
            "tonMax": 600,
            "tonMin": 500,
            "loadDate": "2025-07-07",
            "loadDays": 1,
            "loadUnloadDays": 1,
            "demurrageFee": 800.00,
            "maritimeAffairsFee": 4,
            "replenishAppoint": "测试补充约定",
            "specialRemark": null,
            "orderAcceptanceState": 1,
            "deposit": 1000.88,
            "payType": 2,
            "shipInfoServiceFee": 0.00,
            "electricContactNumberType": 1,
            "electricContactNumber": null,
            "state": 2,
            "dataSource": 2,
            "sourceAppId": 600005
        },
        "transportOrderShip": {
            "id": "1125070200138",
            "del": 0,
            "tenantId": null,
            "origin": 2,
            "createdBy": 125,
            "createdTime": "2025-07-07T10:07:43",
            "updatedBy": 125,
            "updatedTime": "2025-07-07T10:45:28",
            "goodsType": "船运消息0941",
            "srpId": "1025070100210",
            "sraId": 1136,
            "ownerId": 99999999999,
            "ownerEnterprise": {
                "unifiedSocialCreditCode": "123879453654789458",
                "name": "升速有限责任公司",
                "legalRepresentative": "蔡升",
                "realName": null,
                "mobile": null,
                "code": null,
                "signId": null
            },
            "ownerName": "蔡小升",
            "ownerMobile": "***********",
            "captainId": 683,
            "captainEnterprise": {
                "unifiedSocialCreditCode": null,
                "name": null,
                "legalRepresentative": null,
                "realName": "张成周",
                "mobile": "***********",
                "code": "********",
                "signId": null
            },
            "captainName": "张成周",
            "captainMobile": "***********",
            "captainBankId": 112,
            "captainBankInfo": {
                "name": "张成周",
                "account": "6215581811001255426",
                "bank": "工商银行",
                "type": 2,
                "cnaps": null
            },
            "shipId": "*********",
            "shipType": 16,
            "shipName": "振荣16/ZHEN RONG 16",
            "sourcePortId": 1397,
            "sourcePortName": "惠龙码头",
            "destinationPortId": 1397,
            "destinationPortName": "惠龙码头",
            "shipRouteId": null,
            "shipRouteSource": null,
            "shipRouteDestination": null,
            "upstreamHandlerId": 125,
            "upstreamHandlerName": "张成周",
            "downstreamHandlerId": 125,
            "downstreamHandlerName": "张成周",
            "unitPrice": 23.00,
            "ton": 1000,
            "actualFreightCost": null,
            "loadDate": "2025-07-07",
            "loadDays": 1,
            "loadUnloadDays": 1,
            "demurrageFee": 800.00,
            "maritimeAffairsFee": 4,
            "deposit": 1000.00,
            "type": 2,
            "payType": 2,
            "state": 7,
            "ownerShipInfoServiceFee": 0.00,
            "ownerShipInfoServiceFeeState": 3,
            "captainShipInfoServiceFee": 111.00,
            "captainShipInfoServiceFeeState": 3,
            "ownerShipInfoServiceFeePayType": null,
            "captainShipInfoServiceFeePayType": 2,
            "startLoadingTime": "2025-07-07T10:40:37",
            "isSailConfirm": 1,
            "isAgreeUnloading": 1,
            "hmgCheckState": null,
            "hmgWaybillNo": "HMG059552591751854063389",
            "appId": 600005
        },
        "transportOrderDetailsShip": {
            "id": "1125070200138",
            "del": 0,
            "tenantId": null,
            "origin": 2,
            "createdBy": 125,
            "createdTime": "2025-07-07T10:07:43",
            "updatedBy": 125,
            "updatedTime": "2025-07-07T10:45:09",
            "deliveryInfo": {
                "loadingDate": "2025-07-07T10:40:33",
                "portCargoTonnage": null,
                "remark": null
            },
            "jdInfo": null,
            "lazbInfo": {
                "loadingCompletionTime": null,
                "loadingTonnage": 987.8,
                "averageWaterGauge": null,
                "tonnagePerCubicMeter": null,
                "loadVideoId": 187053,
                "sixWaterGaugesOneFileId": null,
                "sixWaterGaugesOne": null,
                "sixWaterGaugesTwoFileId": null,
                "sixWaterGaugesTwo": null,
                "sixWaterGaugesThreeFileId": null,
                "sixWaterGaugesThree": null,
                "sixWaterGaugesFourFileId": null,
                "sixWaterGaugesFour": null,
                "sixWaterGaugesFiveFileId": null,
                "sixWaterGaugesFive": null,
                "sixWaterGaugesSixFileId": null,
                "sixWaterGaugesSix": null,
                "tonProof": 187054,
                "remark": null
            },
            "drainage": 0,
            "drainageTime": "2025-07-07T10:43:40",
            "drainageVideoId": null,
            "ownerSetSailTime": "2025-07-07T10:43:40",
            "setSailInfo": null,
            "captainArrivalTime": "2025-07-07T10:44:24",
            "arrivalVideoId": 187056,
            "ownerUnloadingTime": "2025-07-07T10:45:09",
            "unloadingInfo": {
                "unloadingStartTime": null,
                "unloadingCompletionTime": "2025-07-07T10:44:48",
                "deadlineDays": null,
                "unloadingTonnage": 987.8,
                "unloadingVolumePerCubicTon": null,
                "unloadingVideoId": 187057,
                "clearanceVideoId": null
            }
        }
    }
}
```



#### 3.4.4 返回字段说明

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| shippingRequirementPlat | ShippingRequirementPlat | 船运需求详情 |
| transportOrderShip | TransportOrderShip | 关联的船运单详情 |
| transportOrderDetailsShip | TransportOrderDetailsShip | 关联的船运单明细详情 |

**ShippingRequirementPlat类的属性如下：**

| **<font style="color:rgb(44, 44, 54);">参数名</font>** | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>**         |
| ------------------------------------------------------ | ---------------------------------------------------- | ------------------------------------------------------------ |
| id                                                     | String                                               | 船运需求编号                                                 |
| goodsType                                              | String                                               | 货品类型                                                     |
| shipType                                               | Integer                                              | 船型(1.散货船,2.直冲船,3.川江船,4.自卸驳,5.海轮,6.平板驳船)  |
| drainageChannel                                        | Integer                                              | 排水槽(1.无水槽,2.水槽一般,3.水槽好)                         |
| sourcePortId                                           | Long                                                 | 始发地码头id                                                 |
| sourcePortName                                         | String                                               | 始发地码头名称                                               |
| destinationPortId                                      | Long                                                 | 目的地码头id                                                 |
| destinationPortName                                    | String                                               | 目的地码头名称                                               |
| orderShipIds                                           | ArrayString                                          | 船运单id                                                     |
| unitPrice                                              | BigDecimal                                           | 意向单价                                                     |
| freightTons                                            | Integer                                              | 运货吨数                                                     |
| tonMax                                                 | Integer                                              | 运输最大吨数                                                 |
| tonMin                                                 | Integer                                              | 运输最小吨数                                                 |
| loadDate                                               | LocalDate                                            | 装载日期                                                     |
| loadDays                                               | Integer                                              | 装载日期+的天数                                              |
| loadUnloadDays                                         | Integer                                              | 装卸天数                                                     |
| demurrageFee                                           | BigDecimal                                           | 滞期费                                                       |
| maritimeAffairsFee                                     | Integer                                              | 海事费用(1.货主承担上游,2.货主承担下游,3.货主承担两港,4.货主不承担) |
| replenishAppoint                                       | String                                               | 补充约定                                                     |
| specialRemark                                          | String                                               | 特殊说明                                                     |
| deposit                                                | BigDecimal                                           | 船运定金                                                     |
| payType                                                | Integer                                              | 船运费用支付方式(1.自行支付,2.服务商垫付)                    |
| shipInfoServiceFee                                     | BigDecimal                                           | 船务信息服务费                                               |
| dataSource                                             | Integer                                              | 数据来源                                                     |
| sourceAppId                                            | Long                                                 | 来源应用id                                                   |
| shipRouteId                                            | Long                                                 | 航线id                                                       |
| shipRouteSource                                        | String                                               | 航线始发地                                                   |
| shipRouteDestination                                   | String                                               | 航线目的地                                                   |
| demandLevel                                            | Integer                                              | 需求等级                                                     |
| electricContactNumberType                              | Integer                                              | 电联号码类型(1.手机号,2.座机)                                |
| electricContactNumber                                  | String                                               | 电联号码                                                     |
| ownerId                                                | Long                                                 | 货主id                                                       |
| ownerEnterprise                                        | Enterprise                                           | 货主公司信息                                                 |
| handlerId                                              | Long                                                 | 处理专员id                                                   |
| handlerName                                            | String                                               | 处理专员名称                                                 |
| contact                                                | String                                               | 联系人                                                       |
| mobile                                                 | String                                               | 手机号                                                       |
| orderAcceptanceState                                   | Integer                                              | 接单状态(0.待处理,1.--)                                      |
| state                                                  | Integer                                              | 状态(0.待发布,1.已发布,2.已结束,3.已关闭)                    |



**TransportOrderShip类的属性如下：**

| **<font style="color:rgb(44, 44, 54);">参数名</font>** | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>**         |
| ------------------------------------------------------ | ---------------------------------------------------- | ------------------------------------------------------------ |
| id                                                     | String                                               | 船运单编号                                                   |
| srpId                                                  | String                                               | 船运需求编号                                                 |
| sraId                                                  | Long                                                 | 承运商接单编号                                               |
| goodsType                                              | String                                               | 货品类型                                                     |
| sourcePortId                                           | Long                                                 | 始发地码头id                                                 |
| sourcePortName                                         | String                                               | 始发地码头名称                                               |
| destinationPortId                                      | Long                                                 | 目的地码头id                                                 |
| destinationPortName                                    | String                                               | 目的地码头名称                                               |
| shipId                                                 | String                                               | 船舶id                                                       |
| shipType                                               | Integer                                              | 船型(1.散货船,2.直冲船,3.川江船,4.自卸驳,5.海轮,6.平板驳船)  |
| shipName                                               | String                                               | 船舶名称                                                     |
| shipRouteId                                            | Long                                                 | 航线id                                                       |
| shipRouteSource                                        | String                                               | 航线始发地                                                   |
| shipRouteDestination                                   | String                                               | 航线目的地                                                   |
| unitPrice                                              | BigDecimal                                           | 意向单价                                                     |
| ton                                                    | Integer                                              | 吨数                                                         |
| actualFreightCost                                      | BigDecimal                                           | 实际运费                                                     |
| loadDate                                               | LocalDate                                            | 装载日期                                                     |
| loadDays                                               | Integer                                              | 装载日期宽限的天数                                           |
| loadUnloadDays                                         | Integer                                              | 装卸天数                                                     |
| demurrageFee                                           | BigDecimal                                           | 滞期费                                                       |
| maritimeAffairsFee                                     | Integer                                              | 海事费用(1.货主承担上游,2.货主承担下游,3.货主承担两港,4.货主不承担) |
| deposit                                                | BigDecimal                                           | 定金                                                         |
| type                                                   | Integer                                              | 类型(1.平台创建,2.船主接单)                                  |
| payType                                                | Integer                                              | 船运费用支付方式(1.自行支付,2.服务商垫付)                    |
| state                                                  | Integer                                              | 状态(1.待装货,2.装货中,3.待发航,4.运输中,5.待卸货,6.卸货中,7.已清仓,8.已完成,9.待支付船务信息服务费,10.待支付定金,11.服务费确认中,12.定金确认中) |
| ownerShipInfoServiceFee                                | BigDecimal                                           | 货主船务信息服务费                                           |
| ownerShipInfoServiceFeeState                           | Integer                                              | 货主船务信息服务费支付状态(1.待支付,2.待确认,3.已支付)       |
| captainShipInfoServiceFee                              | BigDecimal                                           | 船主船务信息服务费                                           |
| captainShipInfoServiceFeeState                         | Integer                                              | 船主船务信息服务费支付状态(1.待支付,2.待确认,3.已支付)       |
| ownerShipInfoServiceFeePayType                         | Integer                                              | 货主服务费支付类型(1.线上,2.线下)                            |
| captainShipInfoServiceFeePayType                       | Integer                                              | 船主服务费支付类型(1.线上,2.线下)                            |
| startLoadingTime                                       | LocalDateTime                                        | 开始装货时间                                                 |
| isSailConfirm                                          | Integer                                              | 是否发航(1.是,0.否)                                          |
| isAgreeUnloading                                       | Integer                                              | 是否同意卸货(1.是,0.否)                                      |
| hmgCheckState                                          | Integer                                              | 黄码港审核状态(1.待审核,2.审核通过,3.审核不通过)             |
| hmgWaybillNo                                           | String                                               | 黄码港运单号                                                 |
| ownerId                                                | Long                                                 | 货主id                                                       |
| ownerEnterprise                                        | Enterprise                                           | 货主公司信息                                                 |
| ownerName                                              | String                                               | 货主信息联系人                                               |
| ownerMobile                                            | String                                               | 货主信息手机号                                               |
| captainId                                              | Long                                                 | 船主账号id                                                   |
| captainEnterprise                                      | Enterprise                                           | 船主账号信息                                                 |
| captainName                                            | String                                               | 船主信息联系人                                               |
| captainMobile                                          | String                                               | 船主信息手机号                                               |
| captainBankId                                          | Long                                                 | 船主收款银行账户id                                           |
| captainBankInfo                                        | CustomerBankInfo                                     | 船主收款银行账户信息                                         |
| upstreamHandlerId                                      | Long                                                 | 上游处理专员id                                               |
| upstreamHandlerName                                    | String                                               | 上游处理专员姓名                                             |
| downstreamHandlerId                                    | Long                                                 | 下游处理专员id                                               |
| downstreamHandlerName                                  | String                                               | 下游处理专员姓名                                             |
| appId                                                  | Long                                                 | 应用id                                                       |

**TransportOrderDetailsShip类的属性如下：**

| **<font style="color:rgb(44, 44, 54);">参数名</font>** | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>** |
| ------------------------------------------------------ | ---------------------------------------------------- | ---------------------------------------------------- |
| id                                                     | String                                               | 船运单编号                                           |
| deliveryInfo                                           | DeliveryInfo                                         | 发货信息                                             |
| jdInfo                                                 | ArrayGearShift                                       | 接档信息                                             |
| lazbInfo                                               | DeparturePreparationInfo                             | 离岸准备信息                                         |
| drainage                                               | Integer                                              | 是否排水(1.是,0.否)                                  |
| drainageTime                                           | LocalDateTime                                        | 排水确认时间                                         |
| drainageVideoId                                        | Long                                                 | 排水视频ID                                           |
| ownerSetSailTime                                       | LocalDateTime                                        | 货主确认发航时间                                     |
| setSailInfo                                            | SetSailInfo                                          | 发航信息                                             |
| captainArrivalTime                                     | LocalDateTime                                        | 船主确认到港时间                                     |
| arrivalVideoId                                         | Long                                                 | 到港视频ID                                           |
| ownerUnloadingTime                                     | LocalDateTime                                        | 货主确认卸货时间                                     |
| unloadingInfo                                          | UnloadingInfo                                        | 卸货信息                                             |

**DeliveryInfo类的属性如下：**

| **<font style="color:rgb(44, 44, 54);">参数名</font>** | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>** |
| ------------------------------------------------------ | ---------------------------------------------------- | ---------------------------------------------------- |
| loadingDate                                            | LocalDateTime                                        | 装货日期                                             |
| portCargoTonnage                                       | BigDecimal                                           | 集港货物吨位                                         |
| remark                                                 | String                                               | 备注                                                 |



**GearShift类的属性如下：**

| **<font style="color:rgb(44, 44, 54);">参数名</font>** | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>** |
| ------------------------------------------------------ | ---------------------------------------------------- | ---------------------------------------------------- |
| type                                                   | Integer                                              | 类型(1.接档,2.让档)                                  |
| makeFileVideoId                                        | Long                                                 | 让档视频                                             |
| sandPileVideoId                                        | Long                                                 | 沙堆视频                                             |
| rightWaterGaugePhotoId                                 | Long                                                 | 右侧水尺照片                                         |
| 备注                                                   | String                                               | remark                                               |
| loadingFilingTime                                      | LocalDateTime                                        | 装货上档时间                                         |
| shipArrivalTime                                        | LocalDateTime                                        | 船舶到港时间                                         |
| clearanceTime                                          | LocalDateTime                                        | 清仓时间                                             |
| dockingVideoId                                         | Long                                                 | 靠港视频                                             |
| emptyWaterGaugePhotoId                                 | Long                                                 | 空载水尺照片                                         |
| emptyWaterGaugeVideoId                                 | Long                                                 | 空载水尺视频                                         |
| emptyWaterGauge                                        | BigDecimal                                           | 空载水尺                                             |
| emptyWarehousePhotoId                                  | Long                                                 | 空仓照片                                             |
| bottomPressureChamberPhotoId                           | Long                                                 | 压仓底照片                                           |
| loadingVideoId                                         | Long                                                 | 装载视频                                             |



**DeparturePreparationInfo类的属性如下：**

| **<font style="color:rgb(44, 44, 54);">参数名</font>** | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>** |
| ------------------------------------------------------ | ---------------------------------------------------- | ---------------------------------------------------- |
| loadingCompletionTime                                  | LocalDateTime                                        | 装载完成时间                                         |
| loadingTonnage                                         | BigDecimal                                           | 装载吨位                                             |
| averageWaterGauge                                      | BigDecimal                                           | 平均水尺                                             |
| tonnagePerCubicMeter                                   | BigDecimal                                           | 量方吨位                                             |
| loadVideoId                                            | Long                                                 | 满载视频                                             |
| sixWaterGaugesOneFileId                                | Long                                                 | 六处水尺-1文件                                       |
| sixWaterGaugesOne                                      | BigDecimal                                           | 六处水尺-1                                           |
| sixWaterGaugesTwoFileId                                | Long                                                 | 六处水尺-2                                           |
| sixWaterGaugesTwo                                      | BigDecimal                                           | 六处水尺-2                                           |
| sixWaterGaugesThreeFileId                              | Long                                                 | 六处水尺-3                                           |
| sixWaterGaugesThree                                    | BigDecimal                                           | 六处水尺-3                                           |
| sixWaterGaugesFourFileId                               | Long                                                 | 六处水尺-4                                           |
| sixWaterGaugesFour                                     | BigDecimal                                           | 六处水尺-4                                           |
| sixWaterGaugesFiveFileId                               | Long                                                 | 六处水尺-5                                           |
| sixWaterGaugesFive                                     | BigDecimal                                           | 六处水尺-5                                           |
| sixWaterGaugesSixFileId                                | Long                                                 | 六处水尺-6                                           |
| sixWaterGaugesSix                                      | BigDecimal                                           | 六处水尺-6                                           |
| tonProof                                               | Long                                                 | 吨位证明                                             |
| remark                                                 | String                                               | 离港准备备注                                         |



**SetSailInfo类的属性如下：**

| **<font style="color:rgb(44, 44, 54);">参数名</font>** | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>** |
| ------------------------------------------------------ | ---------------------------------------------------- | ---------------------------------------------------- |
| departureTime                                          | LocalDateTime                                        | 发航时间                                             |
| expectedArrivalDate                                    | LocalDateTime                                        | 预计到达日期                                         |
| navigationVideoId                                      | Long                                                 | 发航视频                                             |
| remark                                                 | String                                               | 船舶航行备注                                         |

**UnloadingInfo类的属性如下：**

| **<font style="color:rgb(44, 44, 54);">参数名</font>** | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>** |
| ------------------------------------------------------ | ---------------------------------------------------- | ---------------------------------------------------- |
| unloadingStartTime                                     | LocalDateTime                                        | 卸货开始时间                                         |
| unloadingCompletionTime                                | LocalDateTime                                        | 卸货完成时间                                         |
| deadlineDays                                           | Long                                                 | 滞期天数                                             |
| unloadingTonnage                                       | BigDecimal                                           | 卸货吨位                                             |
| unloadingVolumePerCubicTon                             | BigDecimal                                           | 卸货量方吨位                                         |
| unloadingVideoId                                       | Long                                                 | 卸货视频                                             |
| clearanceVideoId                                       | Long                                                 | 清仓视频                                             |



### 3.5 查询码头数据

#### 3.5.1 接口路径

```plain
GET  https://staging.api.zhihaoscm.cn/application/port/name
```

#### 3.5.2 请求参数

| 参数名 | 类型                                                         | 描述     | 是否必传                                                     |
| ------ | ------------------------------------------------------------ | -------- | ------------------------------------------------------------ |
| name   | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">String</font> | 码头名称 | <font style="color:#000000;background-color:rgba(255, 255, 255, 0);">是</font> |


#### 3.5.3 响应实例

```json
{
    "code": "0",
    "data": [
        {
            "id": 60,
            "del": 0,
            "tenantId": null,
            "origin": null,
            "createdBy": 41,
            "createdTime": "2024-04-28T16:33:38",
            "updatedBy": 24,
            "updatedTime": "2024-09-06T14:20:59",
            "name": "芜湖码头",
            "shortName": "芜湖",
            "type": 1,
            "area": null,
            "contactName": null,
            "phone": null,
            "provinceCode": "34",
            "cityCode": "3402",
            "regionCode": "340202",
            "address": "江湖汽车贸易有限公司",
            "fullAddress": "安徽省芜湖市镜湖区江湖汽车贸易有限公司",
            "latLon": "118.360149,31.343434",
            "remark": null
        },
        {
            "id": 89,
            "del": 0,
            "tenantId": null,
            "origin": 2,
            "createdBy": 49,
            "createdTime": "2024-11-01T10:59:56",
            "updatedBy": 3,
            "updatedTime": "2024-11-13T14:59:36",
            "name": "芜湖码头",
            "shortName": "小店",
            "type": 1,
            "area": null,
            "contactName": null,
            "phone": null,
            "provinceCode": "34",
            "cityCode": "3402",
            "regionCode": "340202",
            "address": "太平洋购物中心",
            "fullAddress": "安徽省芜湖市镜湖区太平洋购物中心",
            "latLon": "118.578366, 31.146514",
            "remark": null
        }
    ]
}
```

#### 3.5.4 返回字段说明

码头信息

| **<font style="color:rgb(44, 44, 54);">参数名</font>** | **<font style="color:rgb(44, 44, 54);">类型</font>** | **<font style="color:rgb(44, 44, 54);">描述</font>** |
| ------------------------------------------------------ | ---------------------------------------------------- | ---------------------------------------------------- |
| id                                                     | Long                                                 | 码头id                                               |
| name                                                   | String                                               | 码头名称                                             |
| shortName                                              | String                                               | 码头简称                                             |
| type                                                   | Integer                                              | 类型(1.码头,2.城市)                                  |
| area                                                   | String                                               | 面积                                                 |
| contactName                                            | String                                               | 联系人姓名                                           |
| phone                                                  | String                                               | 联系人手机号                                         |
| provinceCode                                           | String                                               | 省编码                                               |
| cityCode                                               | String                                               | 城市编码                                             |
| regionCode                                             | String                                               | 区域编码                                             |
| address                                                | String                                               | 详细地址                                             |
| fullAddress                                            | String                                               | 地址全称                                             |
| latLon                                                 | String                                               | 经纬度                                               |
| remark                                                 | String                                               | 简介                                                 |



### 


### 

### 

