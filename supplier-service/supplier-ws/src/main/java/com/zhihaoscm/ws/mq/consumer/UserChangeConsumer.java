package com.zhihaoscm.ws.mq.consumer;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.domain.meta.biz.TopicDef;
import com.zhihaoscm.ws.handler.SessionHolder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RocketMQMessageListener(topic = TopicDef.USER_CHANGE, consumerGroup = TopicDef.USER_CHANGE_GROUP, messageModel = MessageModel.BROADCASTING)
public class UserChangeConsumer implements RocketMQListener<List<Object>> {

	@Override
	public void onMessage(List<Object> userIds) {
		log.info("UserChangeConsumer receive message: {}", userIds);
		userIds.forEach(userId -> {
			List<WebSocketSession> webSocketSessions = SessionHolder.ADMIN_SESSIONS
					.get(Long.valueOf(userId.toString()));
			if (CollectionUtils.isEmpty(webSocketSessions)) {
				return;
			}
			webSocketSessions.forEach(session -> {
				if (session.isOpen()) {
					try {
						log.info("用户在线: {}", userIds);
						// type 1: 用户信息变更 前端重写刷新用户权限
						session.sendMessage(new TextMessage(
								JsonUtils.objectToJson(Map.of("type", 1))));
					} catch (Exception e) {
						log.error("UserChangeConsumer send message error: {}",
								e.getMessage());
					}
				}
			});
		});
	}
}