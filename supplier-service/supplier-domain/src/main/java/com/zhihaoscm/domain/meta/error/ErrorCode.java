package com.zhihaoscm.domain.meta.error;

public interface ErrorCode {

	String CODE_001 = "001";
	String CODE_002 = "002";
	// 选择的链云用户不能是同一个账号
	String CODE_003 = "003";
	// BadRequest
	String CODE_400 = "400";
	// 没有认证
	String CODE_401 = "401";
	// 没有权限
	String CODE_403 = "403";
	// 没有资源
	String CODE_404 = "404";

	// sdk异常
	String CODE_900 = "900";
	// 车辆服务接口异常
	String CODE_911 = "911";
	// 车辆服务接口异常
	String CODE_912 = "912";
	// 参数不正确
	String CODE_913 = "913";

	// 用户相关，用户不存在
	String CODE_20001003 = "20001003";
	// 合同相关
	// 合同不存在!
	String CODE_30001001 = "30001001";
	// 当前用户不存在
	String CODE_30001002 = "30001002";
	// 调用契约锁撤销合同接口异常
	String CODE_30001003 = "30001003";
	// 调用契约锁撤销合同接口失败
	String CODE_30001004 = "30001004";
	// 调用契约锁下载合同接口失败
	String CODE_30001005 = "30001005";
	// 调用契约锁生成合同草稿接口异常
	String CODE_30001006 = "30001006";
	// 调用契约锁生成合同草稿接口失败
	String CODE_30001007 = "30001007";
	// 下载合同文件异常
	String CODE_30001008 = "30001008";
	// 调用契约锁通过文件添加合同文档接口异常
	String CODE_30001009 = "30001009";
	// 调用契约锁通过文件添加合同文档接口失败
	String CODE_30001010 = "30001010";
	// 调用契约锁发起合同接口异常
	String CODE_30001011 = "30001011";
	// 调用契约锁发起合同接口失败
	String CODE_30001012 = "30001012";
	// 调用契约锁获取签署链接接口异常
	String CODE_30001013 = "30001013";
	// 调用契约锁生成签署令牌接口异常
	String CODE_30001014 = "30001014";
	// 暂无可用印章，请先创建关联该业务的印章
	String CODE_30001015 = "30001015";
	// 您尚未为该业务指定关联印章,请前往修改印章页面勾选相关业务后再试
	String CODE_30001016 = "30001016";
	// 无权签署该合同
	String CODE_30001017 = "30001017";
	// 已发起签署，请等待专人进行签署
	String CODE_30001018 = "30001018";
	// 调用契约锁查看合同详情异常
	String CODE_30001019 = "30001019";
	// 发起签署失败，请修改印章使用者
	String CODE_30001020 = "30001020";
	// 合同已存在，请返回列表查看
	String CODE_30001021 = "30001021";
	// 获取token失败
	String CODE_30001022 = "30001022";
	// 文件ID不能为空
	String CODE_30001023 = "30001023";
	// 文件格式不支持
	String CODE_30001024 = "30001024";
	// 企业信息变更中，无法签署
	String CODE_30001025 = "30001025";
	// 后台未配置签署人
	String CODE_30001026 = "30001026";

	// 船舶相关
	// 抓拍船舶照片失败
	String CODE_30002001 = "30002001";
	// 设备不在线
	String CODE_30002002 = "30002002";
	// 设备未开启直播功能
	String CODE_30002003 = "30002003";
	// 调用宇视云获取视频播放链接接口异常
	String CODE_30002004 = "30002004";
	// 设备推流错误
	String CODE_30002005 = "30002005";
	// 调用宇视云开启播放功能接口异常
	String CODE_30002006 = "30002006";
	// 调用宇视云关闭播放功能接口异常
	String CODE_30002007 = "30002007";
	// 获取视频播放链接失败
	String CODE_30002008 = "30002008";
	// 抓拍船舶照片异常
	String CODE_30002009 = "30002009";
	// 开启播放功能失败
	String CODE_30002010 = "30002010";
	// 调用宇视云关闭播放功能接口失败
	String CODE_30002011 = "30002011";
	// 调用宇视云查询设备接口异常
	String CODE_30002012 = "30002012";
	// 调用宇视云接口查询设备失败
	String CODE_30002013 = "30002013";
	// 船舶已存在
	String CODE_30002014 = "30002014";
	// 船舶不存在
	String CODE_30002015 = "30002015";
	// 调用船顺网查询船舶信息接口异常
	String CODE_30002016 = "30002016";
	// 调用船顺网区域查询船舶接口异常
	String CODE_30002017 = "30002017";
	// 调用船顺网关键字查询船舶接口异常
	String CODE_30002018 = "30002018";
	// 识别内河船舶检验报告文件不存在
	String CODE_30002019 = "CODE_30002019";

	// 航线相关
	// 航线已存在，不可重复维护
	String CODE_30003001 = "30003001";

	// 预付款相关
	// 用户账户不存在 无法撤销
	String CODE_30004001 = "30004001 ";

	// 服务费账单相关
	// 用户账户不存在,无法核销
	String CODE_30005001 = "30005001";
	// 用户不存在,无法核销
	String CODE_30005002 = "30005002";

	// 设备相关
	// 调用海康威视平台新增接口异常
	String CODE_30006001 = "30006001";
	// 调用海康威视平台修改接口异常
	String CODE_30006002 = "30006002";
	// 调用海康威视平台删除接口异常
	String CODE_30006003 = "30006003";
	// 验证码错误
	String CODE_30006004 = "30006004";
	// 设备不存在
	String CODE_30006005 = "30006005";
	// 设备不在线
	String CODE_30006006 = "30006006";
	// 设备序列号无效
	String CODE_30006007 = "30006007";
	// 远程抓图异常
	String CODE_30006008 = "30006008";
	// 获取标准流播放地址异常
	String CODE_30006009 = "30006009";
	// 失效标准流播放地址异常
	String CODE_30006010 = "30006010";
	// 参数错误
	String CODE_30006011 = "30006011";

	// 企业认证相关
	// 当前客户尚未进行过个人认证操作
	String CODE_30007001 = "30007001";
	// 当前客户未进行过企业认证操作,请核实后再试!
	String CODE_30007002 = "30007002";
	// 保存企业认证结果异常
	String CODE_30007003 = "30007003";
	// 当前企业未进行授权操作,请核实后再试!
	String CODE_30007004 = "30007004";
	// 调用获取契约锁企业认证结果接口异常,请稍后再试
	String CODE_30007005 = "30007005";
	// 该企业尚未通过认证,请等待企业审核完成后再进行授权操作
	String CODE_30007006 = "30007006";
	// 获取企业授权链接异常
	String CODE_30007007 = "30007007";
	// 获取企业授权链接失败
	String CODE_30007008 = "30007008";
	// 给当前操作人添加印章管理员角色异常!
	String CODE_30007009 = "30007009";
	// 给当前操作人添加印章管理员角色失败!
	String CODE_30007010 = "30007010";
	// 未找到相关企业秘钥
	String CODE_30007011 = "30007011";
	// 添加员工异常
	String CODE_30007012 = "30007012";
	// 添加员工失败
	String CODE_30007013 = "30007013";
	// 未找到相关企业认证信息
	String CODE_30007014 = "30007014";
	// 获取企业认证令牌异常
	String CODE_30007015 = "30007015";
	// 获取企业认证令牌失败
	String CODE_30007016 = "30007016";
	// 调用第三方接口查询公司详情异常
	String CODE_30007017 = "30007017";
	// 调用第三方接口查询公司详情失败
	String CODE_30007018 = "30007018";
	// 企业正在认证中,请等待认证结果
	String CODE_30007019 = "30007019";
	// 企业名称不能为空!
	String CODE_30007020 = "30007020";

	// openAI 相关
	// 上传文件失败
	String CODE_30008001 = "30008001";
	// 删除文件失败
	String CODE_30008002 = "30008002";
	// 创建向量存储失败
	String CODE_30008003 = "30008003";
	// 修改向量存储失败
	String CODE_30008004 = "30008004";
	// 删除向量存储失败
	String CODE_30008005 = "30008005";
	// 向量存储添加文件失败
	String CODE_30008006 = "30008006";
	// 向量存储不存在
	String CODE_30008007 = "30008007";
	// 向量存储关联文件失败
	String CODE_30008008 = "30008008";
	// 删除向量存储文件失败
	String CODE_30008009 = "30008009";
	// 文件不存在
	String CODE_30008010 = "30008010";
	// 智能助理创建失败
	String CODE_30008011 = "30008011";
	// 更新智能助理失败
	String CODE_30008012 = "30008012";
	// 删除智能助理失败
	String CODE_30008013 = "30008013";
	// 清空会话失败
	String CODE_30008014 = "30008014";
	// 读取 functions.json 文件失败
	String CODE_30008015 = "30008015";

	// 支付相关
	// 订单已关闭
	String CODE_30009001 = "30009001";
	// 订单已支付
	String CODE_30009002 = "30009002";
	// 订单不存在
	String CODE_30009003 = "30009003";
	// 获取支付二维码失败
	String CODE_30009004 = "30009004";
	// 调用支付接口异常
	String CODE_30009005 = "30009005";
	// 订单号重复
	String CODE_30009006 = "30009006";
	// 金额不能为空且必须大于0
	String CODE_30009007 = "30009007";
	// json解析异常
	String CODE_30009008 = "30009008";
	// 非会员不需要购买
	String CODE_30009009 = "30009009";
	// 会员等级不存在
	String CODE_30009010 = "30009010";
	// 购买类型不存在
	String CODE_30009011 = "30009011";
	// 船运单id不能为空
	String CODE_30009012 = "30009012";
	// 船运单不能为空
	String CODE_30009013 = "30009013";
	// 微信openid不能为空
	String CODE_30009014 = "30009014";
	// 船运单非待支付船务信息服务费状态不可进行支付
	String CODE_30009015 = "30009015";
	// 船务信息服务费id不能为空
	String CODE_30009016 = "30009016";
	// 船务信息服务费不存在
	String CODE_30009017 = "30009017";
	// 订单id不能为空
	String CODE_30009018 = "30009018";
	// 签署方式不能为空
	String CODE_30009019 = "30009019";
	// 发货方式不能为空
	String CODE_30009020 = "30009020";
	// 货物信息不能为空
	String CODE_30009021 = "30009021";
	// 货物总数量不能为空
	String CODE_30009022 = "30009022";
	// 发货日期不能为空
	String CODE_30009023 = "30009023";
	// 订单类型不能为空
	String CODE_30009024 = "30009024";
	// 物流方式不能为空
	String CODE_30009025 = "30009025";
	// 货物总金额不能为空
	String CODE_30009026 = "30009026";
	// 申请日期不能为空
	String CODE_30009027 = "30009027";
	// 货物名称不能为空
	String CODE_30009028 = "30009028";
	// 单位不能为空
	String CODE_30009029 = "30009029";
	// 凭证文件不能为空
	String CODE_30009030 = "30009030";
	// 该订单已签收，不允许删除
	String CODE_30009031 = "30009031";
	// 只有草稿状态或者已驳回状态的订单才能被删除
	String CODE_30009032 = "30009032";
	// 账期不能为空
	String CODE_30009033 = "30009033";
	// 账期范围应大于等于0小于等于1000
	String CODE_30009034 = "30009034";
	// 该合同存在应付款逾期，请先处理
	String CODE_30009035 = "30009035";
	// 当前存在销售订单被关联
	String CODE_30009036 = "30009036";
	// 销售订单已被关联，请不要重复选择
	String CODE_30009037 = "30009037";
	// 没有发货信息，不能完成发货
	String CODE_30009038 = "30009038";
	// 该订单存在待发货和发货中状态的发货单，不能完成发货
	String CODE_30009039 = "30009039";
	// 订单金额大于预估可提货余额，请先还款
	String CODE_30009040 = "30009040";
	// 该订单已付款，不允许删除
	String CODE_30009041 = "30009041";
	// 相同规格请填写一个单价
	String CODE_30009042 = "30009042";
	// 订单关联签收单后无法新增提货单
	String CODE_30009043 = "30009043";
	// 订单关联签收单后无法修改提货单
	String CODE_30009044 = "30009044";
	// 订单关联签收单后无法删除提货单
	String CODE_30009045 = "30009045";
	// 状态不为对账完成/预对账完成，无法发起作废
	String CODE_30009046 = "30009046";
	// 当前订单关联发货单，不能删除
	String CODE_30009047 = "30009047";
	// 当前订单关联提货单，不能删除
	String CODE_30009048 = "30009048";
    // 销售订单只能关联销售合同
    String CODE_30009049 = "30009049";
    // 采购订单只能关联采购合同
    String CODE_30009050 = "30009050";

	// 会员等级
	// 查询不到用户当前的会员信息
	String CODE_30010001 = "30010001";
	// 当前会员已不能购买
	String CODE_30010002 = "30010002";
	// 错误的购买类型
	String CODE_30010003 = "30010003";
	// 购买的会员等级不能小于用户当前的会员等级
	String CODE_30010004 = "30010004";
	// 当前会员已不能兑换
	String CODE_30010005 = "30010005";

	// 验证码相关
	// 验证码校验未通过，请重新获取验证码
	String CODE_30011001 = "30011001";
	// 新设备登录需要校验验证码
	String CODE_30011002 = "30011002";
	//

	// 项目相关
	// 买方信息不存在
	String CODE_30012001 = "30012001";
	// 项目不存在
	String CODE_30012002 = "30012002";

	// 微信相关
	// 微信公众号生成签名异常
	String CODE_30013001 = "30013001";

	// 印章管理相关
	// 主
	String CODE_30014001 = "30014001";
	// seal image not found
	String CODE_30014002 = "30014002";
	// 从第三方平台下载印章图片异常
	String CODE_30014003 = "30014003";
	// 调用第三方接口自动创建印章失败
	String CODE_30014004 = "30014004";
	// 调用第三方接口自动创建印章异常
	String CODE_30014005 = "30014005";
	// 调用第三方接口查询印章详情异常
	String CODE_30014006 = "30014006";
	// 调用第三方接口查询印章详情失败
	String CODE_30014007 = "30014007";
	// 调用三方接口编辑印章异常
	String CODE_30014008 = "30014008";
	// 调用三方接口编辑印章失败
	String CODE_30014009 = "30014009";

	// 子账号管理相关
	// 调用第三方接口移除子账号异常
	String CODE_30015001 = "30015001";
	// 调用第三方接口移除子账号失败
	String CODE_30015002 = "30015002";
	// 调用第三方接口邀请子账号异常
	String CODE_30015003 = "30015003";
	// 调用第三方接口邀请子账号失败
	String CODE_30015004 = "30015004";
	// 用户未登录,请登录后再试
	String CODE_30015005 = "30015005";
	// 员工的姓名和实际姓名不匹配
	String CODE_30015006 = "30015006";

	// 运价综合指数配置版本相关
	// 版本不存在
	String CODE_30016001 = "30016001";
	// 版本已生效不能进行启用
	String CODE_30016002 = "30016002";
	// 版本已失效不能进行修改
	String CODE_30016003 = "30016003";

	// 砂石综合指数配置版本相关
	// 版本不存在
	String CODE_30017001 = "30017001";
	// 版本已生效不能进行启用
	String CODE_30017002 = "30017002";
	// 版本已失效不能进行修改
	String CODE_30017003 = "30017003";

	// 船舶组相关
	// 群组不存在
	String CODE_30018001 = "30018001";
	// 群组名不能为空
	String CODE_30018002 = "30018002";
	// 群组名不能超过25个字符
	String CODE_30018003 = "30018003";
	// 群组船舶颜色不能为空
	String CODE_30018004 = "30018004";
	// 该群组名已经存在
	String CODE_30018005 = "30018005";
	// 最多可新增20个群组
	String CODE_30018006 = "30018006";
	// 固定群组不能进行操作
	String CODE_30018007 = "30018007";
	// 颜色不存在
	String CODE_30018008 = "30018008";
	// 每个群组最多只能添加200条数据
	String CODE_30018009 = "30018009";

	// 船舶关注相关
	// 群组id不能为空
	String CODE_30019001 = "30019001";
	// 船舶状态不能为空
	String CODE_30019002 = "30019002";
	// 船舶状态不存在
	String CODE_30019003 = "30019003";
	// 备注字符长度不能超过100个字符
	String CODE_30019004 = "30019004";
	// 船舶id不能为空
	String CODE_30019005 = "30019005";
	// 船舶关注信息不存在
	String CODE_30019006 = "30019006";
	// 该群组你无权限添加
	String CODE_30019007 = "30019007";
	// 船舶已经被关注
	String CODE_30019008 = "30019008";
	// 无权操作他人数据
	String CODE_30019009 = "30019009";

	// 通知管理相关
	// 通知信息不存在
	String CODE_30020001 = "30020001";
	// 标题不能为空，且最大为50个字符
	String CODE_30020002 = "30020002";
	// 分类不能为空
	String CODE_30020003 = "30020003";
	// 分类不存在
	String CODE_30020004 = "30020004";
	// 内容不能为空
	String CODE_30020005 = "30020005";
	// 定时发布时间只能选择未来时间
	String CODE_30020006 = "30020006";
	// 草稿状态才能进行编辑
	String CODE_30020007 = "30020007";
	// 待发布状态才能进行取消发布
	String CODE_30020008 = "30020008";
	// 待发布状态不能进行删除
	String CODE_30020009 = "30020009";
	// 已发布状态才能进行撤回
	String CODE_30020010 = "30020010";
	// 撤回失败，发布后7天内才可撤回
	String CODE_30020011 = "30020011";
	// 草稿状态才能进行发布
	String CODE_30020012 = "30020012";
	// 参数错误
	String CODE_30020013 = "30020013";

	// 砂石学院相关
	// 砂石学院数据不存在
	String CODE_30021001 = "30021001";
	// 标题不能为空，且最大为50个字符
	String CODE_30021002 = "30021002";
	// 收费类型不能为空
	String CODE_30021003 = "30021003";
	// 收费类型不存在
	String CODE_30021004 = "30021004";
	// 展示位置不存在
	String CODE_30021005 = "30021005";
	// 标签数量超过10个
	String CODE_30021006 = "30021006";
	// 来源长度不超过10个字符
	String CODE_30021007 = "30021007";
	// 发布日期不能为空
	String CODE_30021008 = "30021008";
	// 略缩图不能为空
	String CODE_30021009 = "30021009";
	// 摘要长度不超过200个字符
	String CODE_30021010 = "30021010";
	// 内容不能为空
	String CODE_30021011 = "30021011";
	// 免责声明长度不超过300个字符
	String CODE_30021012 = "30021012";
	// 下架状态才能编辑
	String CODE_30021013 = "30021013";
	// 下架状态才能删除
	String CODE_30021014 = "30021014";
	// 存在重复标签
	String CODE_30021015 = "30021015";
	// 参数错误
	String CODE_30021016 = "30021016";

	// 机构认证相关
	// 机构认证不存在
	String CODE_30022001 = "30022001";
	// 状态不能为空
	String CODE_30022002 = "30022002";
	// 状态不存在
	String CODE_30022003 = "30022003";
	// 备注不能超过100个字符
	String CODE_30022004 = "30022004";
	// 营业执照不能为空
	String CODE_30022005 = "30022005";
	// 企业名称不能为空
	String CODE_30022006 = "30022006";
	// 企业名称不能超过40个字符
	String CODE_30022007 = "30022007";
	// 统一社会信用代码不能为空
	String CODE_30022008 = "30022008";
	// 统一社会信用代码不能为空格式错误
	String CODE_30022009 = "30022009";
	// 法定代表人不能为空
	String CODE_30022010 = "30022010";
	// 法定代表人不能超过10个字符
	String CODE_30022011 = "30022011";
	// 该企业已被认证
	String CODE_30022012 = "30022012";
	// 待审核状态才能进行审核
	String CODE_30022013 = "30022013";
	// 无权限
	String CODE_30022014 = "30022014";
	// 已驳回状态才能进行修改
	String CODE_30022015 = "30022015";
	// 已认证
	String CODE_30022016 = "30022016";
	// 已解除机构认证，或未认证才能进行新增
	String CODE_30022017 = "30022017";
	// 备注不能为空
	String CODE_30022018 = "30022018";
	// 未完成个人认证
	String CODE_30022019 = "30022019";
	// 您还未完成本系统的组织机构认证
	String CODE_30022020 = "30022020";
	// 营业执照不合法
	String CODE_30022021 = "30022021";
	// 请选择企业身份
	String CODE_30022022 = "30022022";
	// 授权委托书不能为空
	String CODE_30022023 = "30022023";

	// 客户船舶关注相关
	// 超出关注数量
	String CODE_30023001 = "30023001";
	// 船舶关注不存在
	String CODE_30023002 = "30023002";
	// 该船不是您关注的
	String CODE_30023003 = "30023003";

	// 搜索发现
	// 搜索发现不存在
	String CODE_30024001 = "30024001";
	// 关键词不能为空
	String CODE_30024002 = "30024002";
	// 关键词不能超过10个字符
	String CODE_30024003 = "30024003";
	// 关键词不能重复
	String CODE_30024004 = "30024004";
	// 类型不能为空
	String CODE_30024005 = "30024005";
	// 类型不存在
	String CODE_30024006 = "30024006";
	// 业务id不能为空
	String CODE_30024007 = "30024007";
	// 置顶数已达上限
	String CODE_30024008 = "30024008";
	// 未置顶才能进行编辑
	String CODE_30024009 = "30024009";
	// 未置顶才能进行删除
	String CODE_30024010 = "30024010";
	// 置顶的关键词才能增加点击
	String CODE_30024011 = "30024011";

	// 留言反馈相关
	// 留言反馈数据不存在
	String CODE_30025001 = "30025001";
	// 留言类型不能为空
	String CODE_30025002 = "30025002";
	// 留言类型不存在
	String CODE_30025003 = "30025003";
	// 留言内容不能为空，且最大为150字符
	String CODE_30025004 = "30025004";
	// 最大上传6张图片
	String CODE_30025005 = "30025005";
	// 待回复状态才能进行回复
	String CODE_30025006 = "30025006";
	// 回复内容不能为空，且最大为200字符
	String CODE_30025007 = "30025007";

	// 功能反馈相关
	// 类型不存在
	String CODE_30026001 = "30026001";
	// 补充说明字数不能超过50
	String CODE_30026002 = "30026002";
	// 反馈原因不存在
	String CODE_30026003 = "30026003";
	// 类型不能为空
	String CODE_30026004 = "30026004";
	// 反馈原因不能为空
	String CODE_30026005 = "30026005";
	// 反馈原因不能多选
	String CODE_30026006 = "30026006";
	// 该数据不存在
	String CODE_30026007 = "30026007";
	// 该数据状态为已处理
	String CODE_30026008 = "30026008";
	// 备注不能为空
	String CODE_30026009 = "30026009";
	// 备注不能超过200个字符
	String CODE_30026010 = "30026010";
	// 状态码错误
	String CODE_30026011 = "30026011";

	// 帮助中心相关
	// 帮助中心数据不存在
	String CODE_30027001 = "30027001";
	// 标题不能为空，且最大为50个字符
	String CODE_30027002 = "30027002";
	// 内容不能为空
	String CODE_30027003 = "30027003";
	// 下架状态才能编辑
	String CODE_30027004 = "30027004";
	// 下架状态才能删除
	String CODE_30027005 = "30027005";
	// 参数错误
	String CODE_30027006 = "30027006";

	// 平台船运需求相关
	// 平台船运需求ID不能为空
	String CODE_30028001 = "30028001";
	// 抢单后不能进行资源反馈
	String CODE_30028002 = "30028002";
	// 船主接单ID不能为空
	String CODE_30028003 = "30028003";

	// 货主船运需求相关
	// 货主船运需求ID不能为空
	String CODE_30029001 = "30029001";

	// AI热词相关
	// AI热词不能为空
	String CODE_30030001 = "30030001";
	// AI热词长度应为1-10个字符
	String CODE_30030002 = "30030002";
	// 权重值不能为空
	String CODE_30030003 = "30030003";
	// 热词数据不存在
	String CODE_30030004 = "30030004";
	// 该热词已存在
	String CODE_30030005 = "30030005";

	// openAI 相关
	// 向量存储名称不能为空且不能超过16个字符
	String CODE_30031001 = "30031001";
	// 向量存储名称不能为空
	String CODE_30031002 = "30031002";
	// 向量存储不存在
	String CODE_30031003 = "30031003";
	// 文件不能为空
	String CODE_30031004 = "30031004";
	// 助手名称不能为空 长度为1~20个字符
	String CODE_30031005 = "30031005";
	// 指令不能为空 长度为1~500个字符
	String CODE_30031006 = "30031006";
	// 模型不能为空
	String CODE_30031007 = "30031007";
	// fileSearch参数不能为空 只能为0和1
	String CODE_30031008 = "30031008";
	// miniProgram参数不能为空 只能为0和1
	String CODE_30031009 = "30031009";
	// 向量存储不能为空
	String CODE_30031010 = "30031010";
	// 智能助理不存在
	String CODE_30031011 = "30031011";

	// 轮播图相关
	// 轮播图位置不能为空
	String CODE_30032001 = "30032001";
	// 轮播图位置只能为1到4
	String CODE_30032002 = "30032002";
	// 轮播图文件id不能为空
	String CODE_30032003 = "30032003";
	// 排序值不能为空
	String CODE_30032004 = "30032004";
	// 跳转类型不能为空
	String CODE_30032005 = "30032005";
	// 跳转类型只能为1到6
	String CODE_30032006 = "30032006";
	// 内容不能为空
	String CODE_30032007 = "30032007";
	// 轮播图不存在
	String CODE_30032008 = "30032008";
	// 承运商跳转类型不能为商品或砂价
	String CODE_30032009 = "30032009";

	// 广告位相关
	// 广告位不存在
	String CODE_30033001 = "30033001";
	// 广告名称不能为空
	String CODE_30033002 = "30033002";
	// 展现形式不能为空
	String CODE_30033003 = "30033003";
	// 轮播间隔不能为空
	String CODE_30033004 = "30033004";
	// 广告周期不能为空
	String CODE_30033005 = "30033005";
	// 广告图片不能为空
	String CODE_30033006 = "30033006";
	// 排序不能为空
	String CODE_30033007 = "30033007";
	// 广告位已存在
	String CODE_30033008 = "30033008";
	// 广告不能为空
	String CODE_30033009 = "30033009";
	// 排序只能范围1-5
	String CODE_30033010 = "30033010";
	// 排序字段存在重复值
	String CODE_30033011 = "30033011";
	// 展现形式类型不存在
	String CODE_30033012 = "30033012";
	// 轮播间隔类型不存在
	String CODE_30033013 = "30033013";
	// 标题不能为空 长度为1-50个字符
	String CODE_30033014 = "30033014";
	// 分类不能为空
	String CODE_30033015 = "30033015";
	// 广告类型不能为空
	String CODE_30033016 = "30033016";
	// 上传图片不能为空
	String CODE_30033017 = "30033017";
	// 按钮值不能为空并且长度不能超过15个字符
	String CODE_30033018 = "30033018";
	// 广告信息不存在
	String CODE_30033019 = "30033019";
	// 链接类型不存在
	String CODE_30033020 = "30033020";
	// 广告页不能为空
	String CODE_30033021 = "30033020";
	// 链接不能为空
	String CODE_30033022 = "30033020";
	// 图片不能为空
	String CODE_30033023 = "30033023";

	// 设备申领相关
	// 该认领数据已绑定设备,请核实后再试!
	String CODE_30034001 = "30034001";
	// 该设备已操作过申领绑定,请核实后再试!
	String CODE_30034002 = "30034002";
	// 该认领数据不存在!
	String CODE_30034003 = "30034003";
	// 申领id不能为空
	String CODE_30034004 = "30034004";
	// 设备id不能为空
	String CODE_30034005 = "30034005";
	// 安装位置不能为空
	String CODE_30034006 = "30034006";
	// 安装位置长度不能超过10个字符
	String CODE_30034007 = "30034007";
	// 推介人和船主不能是同一个人
	String CODE_30034008 = "30034008";
	// 船舶未认证不能申领设备!
	String CODE_30034009 = "30034009";
	// 船舶已绑定设备无需再次进行申领操作!
	String CODE_30034010 = "30034010";
	// 船舶已申请过申领,请勿重复操作!
	String CODE_30034011 = "30034011";
	// 推荐人不能填写本人手机号
	String CODE_30034012 = "30034012";

	// 砂石指数版本记录相关
	// 版本id不能为空
	String CODE_30092001 = "30092001";
	// 砂石指数版本记录不存在
	String CODE_30092002 = "30092002";
	// 指数数据为空，不能进行提交
	String CODE_30092003 = "30092003";
	// 待发布、已撤回状态才能进行发布
	String CODE_30092004 = "30092004";
	// 待发布、已撤回状态才能进行驳回
	String CODE_30092005 = "30092005";
	// 已发布状态且是最新发布数据才能进行撤回
	String CODE_30092006 = "30092006";
	// 当前已经是最初发布版本，无法进行撤回！
	String CODE_30092007 = "30092007";
	// 待提交、已驳回状态才能进行提交、修改、删除操作
	String CODE_30092008 = "30092008";
	// 待提交、已驳回状态才能进行保存
	String CODE_30092009 = "30092009";
	// 没有权限
	String CODE_30092010 = "30092010";

	// 预付款相关
	// 金额不能为空 取值范围为0.01~10000000000
	String CODE_30035001 = "30035001";
	// 日期不能为空
	String CODE_30035002 = "30035002";
	// 平台银行账号不能为空
	String CODE_30035003 = "30035003";
	// 平台银行账号不存在
	String CODE_30035004 = "30035004";
	// 预付款账号信息不能为空
	String CODE_30035005 = "30035005";
	// 开户名称不能为空 最大25个字符
	String CODE_30035006 = "30035006";
	// 银行账号不能为空 最多8到30个数字
	String CODE_30035007 = "30035007";
	// 开户行不能为空 最大128个字符
	String CODE_30035008 = "30035008";
	// 用户账户id不能为空
	String CODE_30035009 = "30035009";
	// 用户账户不存在
	String CODE_30035010 = "30035010";
	// 预付款id不能为空
	String CODE_30035011 = "30035011";
	// 撤销备注不能为空,最多一百个字符
	String CODE_30035012 = "30035012";
	// 预付款信息不存在
	String CODE_30035013 = "30035013";

	// 印章管理相关
	// 主账号未通过企业认证,请先通过认证页面进行企业认证后再创建印章
	String CODE_30036001 = "30036001";
	// 印章使用者不存在或不属于当前主账号的子账号
	String CODE_30036002 = "30036002";
	// 子账号不存在
	String CODE_30036003 = "30036003";
	// 子账号未通过个人实名认证,请子账号用户先通过实名认证页面进行个人实名认证后再将其指定为印章使用者
	String CODE_30036004 = "30036004";
	// 关联业务必须选择对应的印章使用者
	String CODE_30036005 = "30036005";
	// 当前业务已经被其他印章关联
	String CODE_30036006 = "30036006";
	// 当前主账号未关联任何子账号,请先关联子账号后再指定印章使用者
	String CODE_30036007 = "30036007";
	// 印章名称不能为空
	String CODE_30036008 = "30036008";
	// 印章名称长度必须在1-50个字符之间
	String CODE_30036009 = "30036009";
	// 印章样式不能为空
	String CODE_30036010 = "30036010";
	// 下方横排文字长度必须在1-50个字符之间
	String CODE_30036011 = "30036011";
	// 信息编码长度必须在1-50个字符之间
	String CODE_30036012 = "30036012";
	// 印章不存在
	String CODE_30036013 = "30036013";
	// 此印章关联的业务或使用者存在签署中的单据，不可修改
	String CODE_30036014 = "30036014";
	// 该印章存在签署中的业务单据，不可删除
	String CODE_30036015 = "30036015";
	// 发起签署失败，请修改印章使用者
	String CODE_30036016 = "30036016";
	// 失效印章不能进行修改
	String CODE_30036017 = "30036017";
	// 一个业务的签章使用者最多选择10个人
	String CODE_30036018 = "30036018";
	// 模块不能为空
	String CODE_30036019 = "30036019";
	// 模块类型错误
	String CODE_30036020 = "30036020";
	// 请先完成电子签章授权
	String CODE_30036021 = "30036021";


	// 设备相关
	// 设备名称不能为空
	String CODE_30037001 = "30037001";
	// 设备名称不超过40个字
	String CODE_30037002 = "30037002";
	// 设备类型不能为空
	String CODE_30037003 = "30037003";
	// 状态不能为空
	String CODE_30037004 = "30037004";
	// SMI卡号不能为空
	String CODE_30037005 = "30037005";
	// SMI卡号格式错误
	String CODE_30037006 = "30037006";
	// 序列号不能为空
	String CODE_30037007 = "30037007";
	// 序列号默认9个字符限制
	String CODE_30037008 = "30037008";
	// 验证码不能为空
	String CODE_30037009 = "30037009";
	// 设备不存在
	String CODE_30037010 = "30037010";
	// 只有设备状态为非运行状态才能删除
	String CODE_30037011 = "30037011";
	// SMI卡号已存在
	String CODE_30037012 = "30037012";
	// 序列号已存在
	String CODE_30037013 = "30037013";
	// 状态为禁用才能启用
	String CODE_30037014 = "30037014";
	// 状态为启用才能禁用
	String CODE_30037015 = "30037015";
	// 运输工具id不能为空
	String CODE_30037016 = "30037016";
	// 运输工具类型不能为空
	String CODE_30037017 = "30037017";
	// 位置不能为空
	String CODE_30037018 = "30037018";
	// 设备已经被禁用
	String CODE_30037019 = "30037019";
	// 已存在GPS设备
	String CODE_30037020 = "30037020";
	// 注册码不存在
	String CODE_30037021 = "30037021";
	// 宇视平台内部服务器错误
	String CODE_30037022 = "30037022";
	// 宇视平台接口调用失败
	String CODE_30037023 = "30037023";
	// 该设备已绑定船舶，请先解绑船舶
	String CODE_30037024 = "30037024";
	// 设备类型不存在
	String CODE_30037025 = "30037025";
	// 设备不在线
	String CODE_30037026 = "30037026";
	// 验证码格式错误
	String CODE_30037027 = "30037027";
	// 位置不能超过20个字符
	String CODE_30037028 = "30037028";
	// 船舶未认证
	String CODE_30037029 = "30037029";
	// 安装人最多10个字符长度
	String CODE_30037030 = "30037030";
	// 安装地点最多32个字符长度
	String CODE_30037031 = "30037031";
	// 图片向量不能为空
	String CODE_30037032 = "30037032";
	// 图片名称不能为空
	String CODE_30037033 = "30037033";
	// 抓拍图片不能为空
	String CODE_30037034 = "30037034";
	// 图片标记不能为空
	String CODE_30037035 = "30037035";
	// 模型数据id不能为空
	String CODE_30037036 = "30037036";
	// 模型数据标签不能为空
	String CODE_30037037 = "30037037";
	// 模型数据不存在
	String CODE_30037038 = "30037038";
	// 识别记录不存在
	String CODE_30037039 = "30037039";
	// 识别记录已加入模型
	String CODE_30037040 = "30037040";
	// 契约锁已认证不支持取消
	String CODE_30037041 = "30037041";
	// 机构认证还未通过或未进行认证
	String CODE_30037042 = "30037042";
	// 数据不能为空
	String CODE_30037043 = "30037043";

	// 定制管理相关
	// 平台用户id不能为空
	String CODE_30038001 = "30038001";
	// 后台id不能为空
	String CODE_30038002 = "30038002";
	// 系统名称不能为空
	String CODE_30038003 = "30038003";
	// 系统名称长度最大为20
	String CODE_30038004 = "30038004";
	// 系统简称长度最大为10
	String CODE_30038005 = "30038005";
	// 系统头部slogan不能为空
	String CODE_30038006 = "30038006";
	// 系统头部slogan长度最大为20
	String CODE_30038007 = "30038007";
	// AI助理名称长度最大为20
	String CODE_30038008 = "30038008";
	// 服务热线不能为空
	String CODE_30038009 = "30038009";
	// 服务热线长度最大为20
	String CODE_30038010 = "30038010";
	// copyright信息不能为空
	String CODE_30038011 = "30038011";
	// copyright信息长度最大为50
	String CODE_30038012 = "30038012";
	// access_key长度最大为50
	String CODE_30038013 = "30038013";
	// secret_key长度最大为50
	String CODE_30038014 = "30038014";
	// 登录短信模板ID长度最大为20
	String CODE_30038015 = "30038015";
	// 私有域名长度最大为20
	String CODE_30038016 = "30038016";
	// custom信息json串不能为空
	String CODE_30038017 = "30038017";
	// user信息json串
	String CODE_30038018 = "30038018";
	// 用户pc端登录页logo不能为空
	String CODE_30038019 = "30038019";
	// 用户pc端登录页登录图片不能为空
	String CODE_30038020 = "30038020";
	// 用户pc端公众页头部LOGO不能为空
	String CODE_30038021 = "30038021";
	// 用户pc端公众页首页背景图不能为空
	String CODE_30038022 = "30038022";
	// 用户pc端公众页AI形象不能为空
	String CODE_30038023 = "30038023";
	// 用户pc端公众页底部LOGO不能为空
	String CODE_30038024 = "30038024";
	// 用户pc端控制台头部LOGO不能为空
	String CODE_30038025 = "30038025";
	// 用户pc端控制台账号头像不能为空
	String CODE_30038026 = "30038026";
	// 管理后台登录页登录页LOGO不能为空
	String CODE_30038027 = "30038027";
	// 管理后台登录页登录图片不能为空
	String CODE_30038028 = "30038028";
	// 管理后台登录页头部导航栏LOGO不能为空
	String CODE_30038029 = "30038029";
	// 企业定制表id不能为空
	String CODE_30038030 = "30038030";
	// ICP备案信息不能为空
	String CODE_30038031 = "30038031";
	// ICP备案信息长度最大为20
	String CODE_30038032 = "30038032";
	// 联网备案信息不能为空
	String CODE_30038033 = "30038033";
	// 联网备案信息长度最大为20
	String CODE_30038034 = "30038034";
	// 该用户已被关联，请勿重复操作
	String CODE_30038035 = "30038035";
	// 短信签名长度最大为12
	String CODE_30038036 = "30038036";
	// 用户pc端公众页AI头像不能为空
	String CODE_30038037 = "30038037";
	// 企业样式信息不存在
	String CODE_30038038 = "30038038";
	// 企业样式信息已存在
	String CODE_30038039 = "30038039";
	// 企业定制表不存在
	String CODE_30038040 = "30038040";
	// 成员数量最大为10
	String CODE_30038041 = "30038041";
	// 成员已存在
	String CODE_30038042 = "30038042";
	// 成员不存在
	String CODE_30038043 = "30038043";

	// 关于我们相关
	// 文章id不能为空
	String CODE_30039001 = "30039001";
	// 内容富文本不能为空
	String CODE_30039002 = "30039002";
	// 属于哪个app不能为空
	String CODE_30039003 = "30039003";
	// 文章类型不能为空
	String CODE_30039004 = "30039004";
	// 同一账号下同一类型不能重复添加
	String CODE_30039005 = "30039005";

	// 文件相关
	// 文件最大为10M
	String CODE_30040001 = "30040001";
	// 文件不存在
	String CODE_30040002 = "30040002";
	// 该导入模板不存在
	String CODE_30040003 = "30040003";

	// 常跑航线相关
	// 该常跑航线不存在
	String CODE_30041001 = "30041001";
	// 常跑航线最多只能传入3条
	String CODE_30041002 = "30041002";

	// 设备推广相关
	// 船舶已经存在生效推广
	String CODE_30042001 = "30042001";
	// 推广记录状态为失效,不允许撤销激活
	String CODE_30042002 = "30042002";
	// 撤销理由不能超过200个字符
	String CODE_30042003 = "30042003";
	// 推广记录不存在
	String CODE_30042004 = "30042004";

	// 监控预警记录相关
	// 监控预警记录不存在
	String CODE_30043001 = "30043001";
	// 已处理状态不可再指派
	String CODE_30043002 = "30043002";
	// 已处理状态不可二次处理
	String CODE_30043003 = "30043003";
	// 监控预警记录id不能为空
	String CODE_30043004 = "30043004";
	// 备注不能超过200个字符
	String CODE_30043005 = "30043005";
	// 请确认是否误报
	String CODE_30043006 = "30043006";
	// 未处理状态不可修改
	String CODE_30043007 = "30043007";
	// 备注不能为空
	String CODE_30043008 = "30043008";
	// 当前用户不是处理人，无修改权限
	String CODE_30043009 = "30043009";
	// 不是监控预警记录的处理人，不能处理
	String CODE_30043010 = "30043010";

	// 船运定金
	// 船运定金不存在
	String CODE_30044001 = "30044001";
	// 当前状态不允许重新发起转账
	String CODE_30044002 = "30044002";
	// 结算日期不能为空
	String CODE_30044003 = "30044003";
	// 结算凭证不能为空
	String CODE_30044004 = "30044004";
	// 待确认状态才能进行确认
	String CODE_30044005 = "30044005";
	// 待结算状态才能进行结算
	String CODE_30044006 = "30044006";

	// 订单流水相关
	// 支付类型不能为空
	String CODE_30045001 = "30045001";
	// 业务类型不能为空
	String CODE_30045002 = "30045002";
	// 支付金额不能为空
	String CODE_30045003 = "30045003";
	// 当前订单不是未支付状态
	String CODE_30045004 = "30045004";
	// 支付明细字符长度1-255个字符
	String CODE_30045005 = "30045005";
	// 描述不能为空
	String CODE_30045006 = "30045006";
	// 描述字符长度为1-255个字符
	String CODE_30045007 = "30045007";
	// 明细不能为空
	String CODE_30045008 = "30045008";
	// 订单不存在
	String CODE_30045009 = "30045009";
	// 支付类型不存在
	String CODE_30045010 = "30045010";
	// 业务类型不存在
	String CODE_30045011 = "30045011";
	// 来源不能为空
	String CODE_30045012 = "30045012";
	// 来源不存在
	String CODE_30045013 = "30045013";
	// 类型不存在
	String CODE_30045014 = "30045014";

	// 码头相关 码头不存在
	String CODE_30046001 = "30046001";
	// 码头名称不能为空 长度为1-40个字符
	String CODE_30046002 = "30046002";
	// 码头简称不能为空 长度为1-20个字符
	String CODE_30046003 = "30046003";
	// 码头面积长度最多为16位
	String CODE_30046004 = "30046004";
	// 联系人姓名长度最大为10位
	String CODE_30046005 = "30046005";
	// 联系人手机号长度最大为16位
	String CODE_30046006 = "30046006";
	// 省编码不能为空
	String CODE_30046007 = "30046007";
	// 城市编码不能为空
	String CODE_30046008 = "30046008";
	// 区域编码不能为空
	String CODE_30046009 = "30046009";
	// 详细地址不能为空
	String CODE_30046010 = "30046010";
	// 经纬度不能为空
	String CODE_30046011 = "30046011";
	// 简介长度最大为256位
	String CODE_30046012 = "30046012";
	// 地址全称不能为空
	String CODE_30046013 = "30046013";
	// 码头id不能为空
	String CODE_30046014 = "30046014";

	// 砂石资讯相关
	// 数据不存在
	String CODE_30047001 = "30047001";
	// 标题不能为空 长度为1-50个字符
	String CODE_30047002 = "30047002";
	// 发布日期不能为空
	String CODE_30047003 = "30047003";
	// 品类不能为空
	String CODE_30047004 = "30047004";
	// 来源长度最大为10个字符
	String CODE_30047005 = "30047005";
	// 类型不能为空
	String CODE_30047006 = "30047006";
	// 封面不能为空
	String CODE_30047007 = "30047007";
	// 删除时 主键id不能为空
	String CODE_30047008 = "30047008";
	// 删除时 要取消激活的文件id不能为空
	String CODE_30047009 = "30047009";
	// 分类不能为空
	String CODE_30047010 = "30047010";
	// 收费类型不能为空
	String CODE_30047011 = "30047011";
	// 标签数量超过10个
	String CODE_30047012 = "30047012";
	// 品类不存在
	String CODE_30047013 = "30047013";
	// 类别不能为空
	String CODE_30047014 = "30047014";
	// 该资讯被广告占用，暂不支持下架
	String CODE_30047015 = "30047015";

	// 部门相关
	// 部门名称重复
	String CODE_30048001 = "30048001";
	// 部门名称不能为空
	String CODE_30048002 = "30048002";
	// 上级部门id不能为空
	String CODE_30048003 = "30048003";
	// 部门名称长度最大为64
	String CODE_30048004 = "30048004";
	// 排序不能为空
	String CODE_30048005 = "30048005";
	// 部门不存在
	String CODE_30048006 = "30048006";
	// 部门下有子部门不能删除
	String CODE_30048007 = "30048007";
	// 部门下有员工不能删除
	String CODE_30048008 = "30048008";
	// 被拖动部门id不能为空
	String CODE_30048009 = "30048009";
	// 目标部门id不能为空
	String CODE_30048010 = "30048010";
	// 拖动类型不能为空
	String CODE_30048011 = "30048011";
	// 拖动类型只能为0,1,2
	String CODE_30048012 = "30048012";

	// 客户状态不能为空
	String CODE_30049001 = "30049001";
	// 客户ID不能为空
	String CODE_30049002 = "30049002";
	// 客户不存在
	String CODE_30049003 = "30049003";
	// 采购方用户暂未授权电子签
	String CODE_30049004 = "30049004";
	// 销售方用户暂未授权电子签
	String CODE_30049005 = "30049005";
	// 此客户有业务数据，不支持变更身份
	String CODE_30049006 = "30049006";

	// 客户修改头像
	String CODE_30050001 = "30050001";

	// 客户登录
	// 客户微信昵称不能为空
	String CODE_30051001 = "30051001";
	// 客户微信openid不能为空
	String CODE_30051002 = "30051002";
	// 客户微信unionid不能为空
	String CODE_30051003 = "30051003";
	// 客户身份编码不能为空
	String CODE_30051004 = "30051004";
	// 客户微信昵称长度最大为64
	String CODE_30051005 = "30051005";
	// 客户微信openid长度最大为64
	String CODE_30051006 = "30051006";
	// 客户微信unionid长度最大为64
	String CODE_30051007 = "30051007";
	// 客户手机号码格式不正确
	String CODE_30051008 = "30051008";
	// 客户身份编码不正确
	String CODE_30051009 = "30051009";
	// 客户在系统中已存在相同身份的账号
	String CODE_30051010 = "30051010";
	// 客户信息不存在
	String CODE_30051011 = "30051011";
	// 客户该身份不可用
	String CODE_30051012 = "30051012";

	// 客户银行账户
	// 账户类型不能为空
	String CODE_30052001 = "3005201";
	// 账户类型只有1或2
	String CODE_30052002 = "3005202";
	// 开户名称/开户人不能为空
	String CODE_30052003 = "3005203";
	// 开户名称/开户人最大长度为255
	String CODE_30052004 = "3005204";
	// 银行账户/银行卡号不能为空
	String CODE_30052005 = "3005205";
	// 银行账户/银行卡号规则不正确
	String CODE_30052006 = "3005206";
	// 开户行不能为空
	String CODE_30052007 = "3005207";
	// 开户行最大长度为128
	String CODE_30052008 = "3005208";
	// 银行账户不存在
	String CODE_30052009 = "3005209";
	// 超出维护数量
	String CODE_30052010 = "3005210";
	// 银行账户无效
	String CODE_30052011 = "3005211";

	// 客户认证
	// 当前客户已进行过个人实名认证操作,请核实后重新再试!
	String CODE_30053001 = "30053001";
	// 未查询到客户实名认证信息
	String CODE_30053002 = "30053002";
	// 请求查询客户实名认证信息失败
	String CODE_30053003 = "30053003";
	// 当前客户未进行过个人实名认证操作,请首先进行个人实名认证的操作!
	String CODE_30053004 = "30053004";
	// 当前客户已进行过企业实名认证操作并且还在有效期内,请核实后重新再试!
	String CODE_30053005 = "30053005";
	// 当前客户未进行过企业实名认证操作,请首先进行企业实名认证的操作!
	String CODE_30053006 = "30053006";
	// 请求查询客户企业实名认证信息失败
	String CODE_30053007 = "30053007";
	// 机构名称长度不能超过255位
	String CODE_30053008 = "30053008";

	// 客户地址
	// 联系人不能为空
	String CODE_30053009 = "30053009";
	// 联系人长度不能超过25位
	String CODE_30053010 = "30053010";
	// 手机号不能为空
	String CODE_30053011 = "30053011";
	// 手机号格式不正确
	String CODE_30053012 = "30053012";
	// 区号长度不能超过4位不能短于3位
	String CODE_30053013 = "30053013";
	// 区号格式不正确
	String CODE_30053014 = "30053014";
	// 座机号码长度不能超过8位不能短于7位
	String CODE_30053015 = "30053015";
	// 座机号码格式不正确
	String CODE_30053016 = "30053016";
	// 座机分机号码长度不能超过4位不能短于3位
	String CODE_30053017 = "30053017";
	// 座机分机号码格式不正确
	String CODE_30053018 = "30053018";
	// 省份不能为空
	String CODE_30053019 = "30053019";
	// 省份长度不能超过10位
	String CODE_30053020 = "30053020";
	// 市不能为空
	String CODE_30053021 = "30053021";
	// 市长度不能超过25位
	String CODE_30053022 = "30053022";
	// 区不能为空
	String CODE_30053023 = "30053023";
	// 区长度不能超过25位
	String CODE_30053024 = "30053024";
	// 详细地址不能为空
	String CODE_30053025 = "30053025";
	// 详细地址长度不能超过128位
	String CODE_30053026 = "30053026";
	// 邮政编码长度不能超过6位
	String CODE_30053027 = "30053027";
	// 邮政编码格式不正确
	String CODE_30053028 = "30053028";
	// 是否默认不能为空
	String CODE_30053029 = "30053029";
	// 默认地址标识不正确, 只能为是或否
	String CODE_30053030 = "30053030";
	// 已存在默认地址
	String CODE_30053031 = "30053031";
	// 当前客户未进行企业认证
	String CODE_30053032 = "30053032";
	// 当前所添加的机构名称必须与组织机构认证主体名称保持一致
	String CODE_30053033 = "30053033";
	// 地址不存在
	String CODE_30053034 = "30053034";
	// 客户id不能为空
	String CODE_30053035 = "30053035";
	// 手机号长度不能超过11位
	String CODE_30053036 = "30053036";

	// 客户发票
	// 抬头类型不能为空
	String CODE_30054001 = "30054001";
	// 抬头类型只有1或2
	String CODE_30054002 = "30054002";
	// 抬头类型为企业时发票类型不能为空
	String CODE_30054003 = "30054003";
	// 发票类型只有1或2
	String CODE_30054004 = "30054004";
	// 发票抬头不能为空
	String CODE_30054005 = "30054005";
	// 发票抬头最大为255
	String CODE_30054006 = "30054006";
	// 纳税人识别号规则不正确
	String CODE_30054007 = "30054007";
	// 地址最大长度为128
	String CODE_30054008 = "30054008";
	// 开户行最大长度为128
	String CODE_30054009 = "30054009";
	// 对公账户银行账户不正确
	String CODE_30054010 = "30054010";
	// 纳税人识别号不能为空
	String CODE_30054011 = "30054011";
	// 地址不能为空
	String CODE_30054012 = "30054012";
	// 手机号不能为空
	String CODE_30054013 = "30054013";
	// 开户行不能为空
	String CODE_30054014 = "30054014";
	// 对公账户不能为空
	String CODE_30054015 = "30054015";
	// 是否默认抬头只有0或1
	String CODE_30054016 = "30054016";
	// 发票不存在
	String CODE_30054017 = "30054017";
	// 超出维护数量
	String CODE_30054018 = "30054018";

	// 子账号管理相关
	// 受邀人手机号不能为空
	String CODE_30055001 = "30055001";
	// 受邀人可用身份不能为空
	String CODE_30055002 = "30055002";
	// 请先前往企业认证页面完成企业认证后再试
	String CODE_30055003 = "30055003";
	// 请勿邀请自己作为子账号加入企业
	String CODE_30055004 = "30055004";
	// 该子账号已加入10个企业无法继续加入,请核实后再试
	String CODE_30055005 = "30055005";
	// 您已经邀请过该用户，请勿重复邀请
	String CODE_30055006 = "30055006";
	// 子账号不存在
	String CODE_30055007 = "30055007";
	// 您加入的企业数量已达10个上限,无法继续加入,请核实后再试
	String CODE_30055008 = "30055008";
	// 子账号可用身份中不包含承运商
	String CODE_30055009 = "30055009";
	// 已确认的邀请记录不允许删除
	String CODE_30055010 = "30055010";
	// 您不是该企业的管理员,无权进行此操作
	String CODE_30055011 = "30055011";
	// 只能移除已确认的子账号
	String CODE_30055012 = "30055012";
	// 企业不存在
	String CODE_30055013 = "30055013";
	// 您尚未加入到任何企业,请先加入企业后再切换
	String CODE_30055014 = "30055014";
	// 您尚未加入到该企业或已被该企业移除,请核实后再试
	String CODE_30055015 = "30055015";
	// 您没有该身份的权限,请核实后再试
	String CODE_30055016 = "30055016";
	// 暂不支持以承运商的角色切换企业
	String CODE_30055017 = "30055017";
	// 当前子账号数量已超过50个
	String CODE_30055018 = "30055018";
	// 当前会员等级子账号数量已达上限
	String CODE_30055019 = "30055019";
	// 开通或升级链云会员，继续使用子账号
	String CODE_30055020 = "30055020";
	// 当前会员等级子账号数量已达上限，无法激活子账号
	String CODE_30055021 = "30055021";
	// 该企业子账号已失效
	String CODE_30055022 = "30055022";
	// 受邀人可用权限不能为空
	String CODE_30055023 = "30055023";
	// 当前账号不是代理账号
	String CODE_30055024 = "30055024";
	// 请先前往组织机构认证页面完成认证后再试
	String CODE_30055025 = "30055025";

	// 认证相关
	// 当前存在已认证或认证中的企业,请勿重复操作!
	String CODE_30056001 = "30056001";
	// 该企业已经被认证或已在认证过程中
	String CODE_30056002 = "30056002";
	// 用户尚未完成个人实名认证,请先完成个人认证后再次重试!
	String CODE_30056003 = "30056003";
	// 用户已完成企业认证,请勿重复认证!
	String CODE_30056004 = "30056004";
	// 换绑手机号时企业认证需是同一企业认证才能换绑
	String CODE_30056005 = "30056005";
	// 用户不在换绑手机号流程中
	String CODE_30056006 = "30056006";
	// 此手机号已被使用
	String CODE_30056007 = "30056007";

	// 个人认证
	// 真实姓名不能为空
	String CODE_30057001 = "30057001";
	// 真实姓名长度不能超过25位
	String CODE_30057002 = "30057002";
	// 身份证号不能为空
	String CODE_30057003 = "30057003";
	// 身份证号长度不能超过18位
	String CODE_30057004 = "30057004";
	// 身份证号格式不正确
	String CODE_30057005 = "30057005";
	// 认证完成跳转页面链接不能为空
	String CODE_30057006 = "30057006";

	// 企业管理
	// 企业名称不能为空
	String CODE_30058001 = "30058001";
	// 企业名称长度不能超过255位
	String CODE_30058002 = "30058002";
	// 法定代表人不能为空
	String CODE_30058003 = "30058003";
	// 法定代表人长度不能超过25位
	String CODE_30058004 = "30058004";
	// 统一社会信用代码不能为空
	String CODE_30058005 = "30058005";
	// 统一社会信用代码格式不正确
	String CODE_30058006 = "30058006";
	// 统一社会信用代码长度不能超过18位
	String CODE_30058007 = "30058007";
	// 组织机构类型不能为空
	String CODE_30058008 = "30058008";

	// 登录相关
	// 获取微信小程序接口异常
	String CODE_30059001 = "30059001";
	// 无效的code或者code已过期
	String CODE_30059002 = "30059002";
	// 用户不存在
	String CODE_30059003 = "30059003";
	// 此账号不存在
	String CODE_30059004 = "30059004";
	// 账号或密码错误
	String CODE_30059005 = "30059005";
	// 验证码校验未通过，请重新获取验证码
	String CODE_30059006 = "30059006";
	// 用户身份不能为空
	String CODE_30059007 = "30059007";
	// 您的账户已被禁用
	String CODE_30059008 = "30059008";
	// 身份不可用
	String CODE_30059009 = "30059009";
	// 未绑定微信
	String CODE_30059010 = "30059010";
	// 该手机号已绑定微信
	String CODE_30059011 = "30059011";
	// 该手机号正用于换绑手机号流程中，暂不能使用
	String CODE_30059012 = "30059012";
	// 您的账号已被管理员禁用，暂时无法登录
	String CODE_30059013 = "30059013";
	// 此手机号不存在
	String CODE_30059014 = "30059014";
	// 您的手机号已被管理员禁用，暂时无法登录
	String CODE_30059015 = "30059015";
	// 不允许登录
	String CODE_30059016 = "30059016";
	// 账号或密码错误
	String CODE_30059017 = "30059017";
	// 账号不能为空
	String CODE_30059018 = "30059018";
	// key不能为空
	String CODE_30059019 = "30059019";
	// 新密码不能为空
	String CODE_30059020 = "30059020";
	// 两次密码输入不一致
	String CODE_30059021 = "30059021";
	// 新密码不能与旧密码相同
	String CODE_30059022 = "30059022";
	// 请设置6-16位包含字母、数字的密码
	String CODE_30059023 = "30059023";
	// 该手机号已被注册
	String CODE_30059024 = "30059024";
	// 首次登录才能设置密码
	String CODE_30059025 = "30059025";
	// 请重新设置密码
	String CODE_30059026 = "30059026";
	// 请使用供应链账号登陆
	String CODE_30059027 = "30059027";


	// 微信绑定相关
	// 该用户已绑定微信
	String CODE_30060001 = "30060001";
	// 该微信已被绑定
	String CODE_30060002 = "30060002";
	// 该用户未绑定微信
	String CODE_30060003 = "30060003";

	// 邮箱绑定相关
	// 邮箱不能为空
	String CODE_30061001 = "30061001";
	// 该邮箱已被绑定
	String CODE_30061002 = "30061002";
	// 该用户已绑定邮箱
	String CODE_30061003 = "30061003";

	// 品类指数相关
	// 版本id不能为空
	String CODE_30090001 = "30090001";
	// 记录id不能为空
	String CODE_30090002 = "30090002";
	// 品类不能为空
	String CODE_30090003 = "30090003";
	// 价格信息不能为空，最多500条数据
	String CODE_30090004 = "30090004";
	// 产地价格类型不能为空
	String CODE_30090005 = "30090005";
	// 价格类型不能为空
	String CODE_30090006 = "30090006";
	// 出货量的取值范围为0~999999999
	String CODE_30090007 = "30090007";
	// 成交均价不能为空
	String CODE_30090008 = "30090008";
	// 品类不存在
	String CODE_30090009 = "30090009";
	// 价格类型已存在 不可重复维护
	String CODE_30090010 = "30090010";
	// 待提交、已驳回状态才能进行提交、修改、删除操作
	String CODE_30090011 = "30090011";
	// 没有权限
	String CODE_30090012 = "30090012";
	// 旧数据品类id不能为空
	String CODE_30090013 = "30090013";
	// 上传的数据不能为空
	String CODE_30090014 = "30090014";
	// 上传的数据量超过最大限制 最多为500条
	String CODE_30090015 = "30090015";
	// 有数据存在上船价数据为空的数据
	String CODE_30090016 = "30090016";
	// 上船价的成交均价应大于等于0小于等于1000
	String CODE_30090017 = "30090017";
	// 靠港价的成交均价应大于0小于等于1000
	String CODE_30090018 = "30090018";

	// 砂石指数版本相关
	// 版本日期不能为空
	String CODE_30091001 = "30091001";
	// 日期不能在今天之后
	String CODE_30091002 = "30091002";
	// 版本日期已存在
	String CODE_30091003 = "30091003";
	// 存在指数数据，不能修改
	String CODE_30091004 = "30091004";
	// 版本不存在
	String CODE_30091005 = "30091005";
	// 存在指数数据，不能删除
	String CODE_30091006 = "30091006";

	// 砂石资讯相关
	// 删除时 主键id不能为空
	String CODE_30093001 = "30093001";
	// 删除时 要取消激活的文件id不能为空
	String CODE_30093002 = "30093002";

	// 商品相关
	// 商品id不能为空
	String CODE_30094001 = "30094001";
	// 商品名称不能为空
	String CODE_30094002 = "30094002";
	// 权重不能为空 取值范围为0~10000
	String CODE_30094003 = "30094003";
	// 主图文件不能为空
	String CODE_30094004 = "30094004";
	// 其他图片最多只能传五张
	String CODE_30094005 = "30094005";
	// 视频文件必须上传
	String CODE_30094006 = "30094006";
	// 品类不能为空
	String CODE_30094007 = "30094007";
	// 规格不能为空
	String CODE_30094008 = "30094008";
	// 最小细度模数不能为空 取值范围位0.001~99.999
	String CODE_30094009 = "30094009";
	// 最大细度模数取值范围位0.001~99.999
	String CODE_30094010 = "30094010";
	// 含泥量取值范围为1~99
	String CODE_30094011 = "30094011";
	// 压碎值取值范围为1~99
	String CODE_30094012 = "30094012";
	// 石粉MB值取值范围为1~99
	String CODE_30094013 = "30094013";
	// 含水率取值范围为1~99
	String CODE_30094014 = "30094014";
	// 细粉含量取值范围为1~99
	String CODE_30094015 = "30094015";
	// 供货商不能为空
	String CODE_30094016 = "30094016";
	// 起订条件不能为空
	String CODE_30094017 = "30094017";
	// 开票说明 最大为32个字符
	String CODE_30094018 = "30094018";
	// 提货周期 最大为12个字符
	String CODE_30094019 = "30094019";
	// 详细地址不能为空
	String CODE_30094023 = "30094023";
	// 经纬度不能为空
	String CODE_30094024 = "30094024";
	// 商品详情介绍不能为空
	String CODE_30094025 = "30094025";
	// 备注最大为100个字符
	String CODE_30094026 = "30094026";
	// 供应商类型不存在
	String CODE_30094027 = "30094027";
	// 价格类型不能为空
	String CODE_30094028 = "30094028";
	// 价格类型不存在
	String CODE_30094029 = "30094029";
	// 履约保证金额不能为空 取值范围为0.01~10000000000
	String CODE_30094030 = "30094030";
	// 履约金百分比不能为空 取值范围为1~99
	String CODE_30094031 = "30094031";
	// 单价不能为空 取值范围为0.01~10000
	String CODE_30094032 = "30094032";
	// 起订金额不能为空 取值范围为0.01~10000000000
	String CODE_30094033 = "30094033";
	// 起订吨数不能为空 取值范围为0.01~99999999.99
	String CODE_30094034 = "30094034";
	// 供应商不存在
	String CODE_30094035 = "30094035";
	// 当前状态不允许编辑
	String CODE_30094036 = "30094036";
	// 当前状态不允许审核
	String CODE_30094037 = "30094037";
	// 当前状态不允许下架
	String CODE_30094038 = "30094038";
	// 当前状态不允许关闭
	String CODE_30094039 = "30094039";
	// 未关闭的商品才支持绑定设备
	String CODE_30094040 = "30094040";
	// 商品不存在
	String CODE_30094041 = "30094041";
	// 该商品被广告占用，暂不支持下架
	String CODE_30094042 = "30094042";

	// 品类相关
	// 品类代码不能为空，长度为6-10个字符
	String CODE_30095001 = "30095001";
	// 品类名称不能为空, 长度为1-10个字符
	String CODE_30095002 = "30095002";
	// 品类类型不能为空
	String CODE_30095003 = "30095003";
	// 品类区域不能为空
	String CODE_30095004 = "30095004";
	// 产地价格类型不能为空
	String CODE_30095005 = "30095005";
	// 采区/矿厂全称不能为空 长度为1-40个字符
	String CODE_30095006 = "30095006";
	// 采区/矿厂简称不能为空 长度为1-10个字符
	String CODE_30095007 = "30095007";
	// 规格/细度模数不能为空 长度为1-15个字符
	String CODE_30095008 = "30095008";
	// 品类介绍不能为空 最大为256个字符
	String CODE_30095009 = "30095009";
	// 该品类代码已存在
	String CODE_30095010 = "30095010";
	// 产地价格类型必须有默认
	String CODE_30095011 = "30095011";
	// 产地价格类型只能有一个默认
	String CODE_30095012 = "30095012";
	// 产地价格类型名称不能重复
	String CODE_30095013 = "30095013";
	// 品类类型不存在
	String CODE_30095014 = "30095014";
	// 品类区域不存在
	String CODE_30095015 = "30095015";
	// 该品类关联未关闭的商品 无法删除
	String CODE_30095016 = "30095016";
	// 存在重复标签
	String CODE_30095017 = "30095017";

	// 项目相关
	// 项目类型不能为空 只能为1和2
	String CODE_30096001 = "30096001";
	// 采购类型不能为空
	String CODE_30096002 = "30096002";
	// 其他表的主键id不能为空
	String CODE_30096003 = "30096003";
	// 项目名称不能为空 最大为1~30个字符
	String CODE_30096004 = "30096004";
	// 项目介绍不能为空 最大为1~500个字符
	String CODE_30096005 = "30096005";
	// 买方id不能为空
	String CODE_30096006 = "30096006";
	// 意向id不能为空
	String CODE_30096007 = "30096007";
	// 项目id不能为空
	String CODE_30096008 = "30096008";
	// 用户不存在
	String CODE_30096009 = "30096009";
	// 用户身份不能为空
	String CODE_30096010 = "30096010";
	// 服务费不能为空
	String CODE_30096011 = "30096011";
	// 项目不存在
	String CODE_30096012 = "30096012";
	// 当前状态不允许指派
	String CODE_30096013 = "30096013";
	// 当前状态不允许开始
	String CODE_30096014 = "30096014";
	// 当前状态不允许点完成
	String CODE_30096015 = "30096015";
	// 结算未完成,无法完成项目
	String CODE_30096016 = "30096016";
	// 当前状态不允许关闭
	String CODE_30096017 = "30096017";
	// 预估单价不能为空 取值范围 0.01-100亿 保留两位小数
	String CODE_30096018 = "30096018";
	// 冻结金额不能为空 取值范围 0-100亿 保留两位小数
	String CODE_30096019 = "30096019";
	// 供应链预估单价不能为空 取值范围 0.01-100亿 保留两位小数
	String CODE_30096020 = "30096020";
	// 供应链冻结金额不能为空 取值范围 0-100亿 保留两位小数
	String CODE_30096021 = "30096021";
	// 供应链合同金额不能为空 取值范围 0-100亿 保留两位小数
	String CODE_30096022 = "30096022";
	// 供应链最低付款额度不能为空 取值范围 0-100亿 保留两位小数
	String CODE_30096023 = "30096023";
	// 项目名称不支持重名
	String CODE_30096024 = "30096024";
	// 购买意向不存在
	String CODE_30096025 = "30096025";
	// 意向采购吨数不能为空 且必须大于起订吨数
	String CODE_30096026 = "30096026";
	// 买方预估单价不能为空 取值范围 0.01-100亿 保留两位小数
	String CODE_30096027 = "30096027";
	// 买方冻结金额不能为空 取值范围 0-100亿 保留两位小数
	String CODE_30096028 = "30096028";
	// 买方合同金额不能为空 取值范围 0-100亿 保留两位小数
	String CODE_30096029 = "30096029";
	// 买方最低付款额度不能为空 取值范围 0-100亿 保留两位小数
	String CODE_30096030 = "30096030";
	// 服务费索引参数非法
	String CODE_30096031 = "30096031";
	// 服务费信息缺少,请填写完整
	String CODE_30096032 = "30096032";
	// 只支持线上项目进行导出
	String CODE_30096033 = "30096033";
	// 只支持状态为交付中和已完成的项目进行导出
	String CODE_30096034 = "30096034";
	// 采购商id不能为空
	String CODE_30096035 = "30096035";
	// 租户id不能为空
	String CODE_30096036 = "30096036";
	// 存在作废中的数据，请先处理
	String CODE_30096037 = "30096037";
	// 项目已完成 不允许新增
	String CODE_30096038 = "30096038";
	// 项目已完成才能新增
	String CODE_30096039 = "30096039";
	// 已完成状态才能进行删除
	String CODE_30096040 = "30096040";
	// 线下作废请联系平台处理
	String CODE_30096041 = "30096041";
	// 供应商需完成电子签章授权
	String CODE_30096042 = "30096042";
	// 供应商、联合购销商需完成电子签章授权
	String CODE_30096043 = "30096043";
	// 存在关联项目，不允许删除
	String CODE_30096044 = "30096044";

	// 签收单相关
	// 签收单不存在
	String CODE_30097001 = "30097001";
	// 文章类型不能为空
	String CODE_30097002 = "30097002";
	// 签收单吨数不能为空
	String CODE_30097003 = "30097003";
	// 买方认证信息不存在
	String CODE_30097004 = "30097004";
	// 签收确认日期不能为空
	String CODE_30097005 = "30097005";
	// 非草稿状态下不能修改
	String CODE_30097006 = "30097006";
	// 非草稿状态不能删除
	String CODE_30097007 = "30097007";
	// 类型不能为空
	String CODE_30097008 = "30097008";
	// 只有线下已完成的项目才允许撤回
	String CODE_30097009 = "30097009";
	// 买方客户不存在
	String CODE_30097010 = "30097010";
	// 项目状态为已完成或者已关闭,不能加入
	String CODE_30097011 = "30097011";
	// 关联签收单id不能为空
	String CODE_30097012 = "30097012";
	// 关联提货单不能为空
	String CODE_30097013 = "30097013";
	// 采购商已在项目中,加入失败
	String CODE_30097014 = "30097014";
	// 对账状态为对账中或已对账的状态无法关闭
	String CODE_30097015 = "30097015";
	// 签收日期不能为空
	String CODE_30097016 = "30097016";
	// 预估单价不能为空
	String CODE_30097017 = "30097017";
	// 已完成的状态才能删除
	String CODE_30097018 = "30097018";
	// 先删除关联的对账单才能进行删除
	String CODE_30097019 = "30097019";
	// 先删除被关联的签收单才能进行删除
	String CODE_30097020 = "30097020";
	// 签收吨数不能为空
	String CODE_30097021 = "30097021";
	// 签收id不能为空
	String CODE_30097022 = "30097022";
	// 驳回原因长度最大为100
	String CODE_30097023 = "30097023";
	// 待作废的签收单不允许关联对账单并且下游的签收单也不允许关联对账单
	String CODE_30097024 = "30097024";
	// 签署方式不能为空
	String CODE_30097025 = "30097025";
	// 备注不能超过200字符
	String CODE_30097026 = "30097026";
	// 只有草稿或者驳回状态能删除
	String CODE_30097027 = "30097027";
	// 签收重量不能为空
	String CODE_30097028 = "30097028";
	// 该签收单已对账，不允许删除
	String CODE_30097029 = "30097029";
	// 该签收单已被入库单关联，不允许删除
	String CODE_30097030 = "30097030";
	// 签收单据不能为空
	String CODE_30097031 = "30097031";
	// 下游采购商不是录入企业
	String CODE_30097032 = "30097032";
	// 该签收单已对账，不允许删除
	String CODE_30097033 = "30097033";

	// 合同相关
	// 合同名称不能为空
	String CODE_30098001 = "30098001";
	// 合同名称长度不能超过32个字符
	String CODE_30098002 = "30098002";
	// 甲方账号不能为空
	String CODE_30098003 = "30098003";
	// 甲方公司不能为空
	String CODE_30098004 = "30098004";
	// 乙方账号不能为空
	String CODE_30098005 = "30098005";
	// 乙方公司不能为空
	String CODE_30098006 = "30098006";
	// 签署模式不能为空
	String CODE_30098007 = "30098007";
	// 合同类型不能为空
	String CODE_30098008 = "30098008";
	// 合同文件id不能为空
	String CODE_30098009 = "30098009";
	// 提交方式不能为空
	String CODE_30098010 = "30098010";
	// 合同编号或合同名称搜索不得超过32个字符!
	String CODE_30098011 = "30098011";
	// 项目[%s]不存在
	String CODE_30098012 = "30098012";
	// 只有线上签署的合同才能进行修改!
	String CODE_30098013 = "30098013";
	// 只有草稿状态的合同才能进行修改!
	String CODE_30098014 = "30098014";
	// 只有签署中状态的合同才能进行撤回!
	String CODE_30098015 = "30098015";
	// 只有线上签署的合同才能进行撤回!
	String CODE_30098016 = "30098016";
	// 只有草稿状态的合同才能进行删除!
	String CODE_30098017 = "30098017";
	// 只有已完成状态的合同才能进行关闭!
	String CODE_30098018 = "30098018";
	// 甲方客户已被禁用,不能创建合同,请核实后再试!
	String CODE_30098019 = "30098019";
	// 乙方客户已被禁用,不能创建合同,请核实后再试!
	String CODE_30098020 = "30098020";
	// 运价取值范围为0.01~1000
	String CODE_30098021 = "30098021";
	// 乙方客户未进行企业认证,不能创建合同,请核实后再试!
	String CODE_30098022 = "30098022";
	// 运价指数不存在
	String CODE_30098023 = "30098023";
	// 乙方客户认证企业与传入企业不匹配!
	String CODE_30098024 = "30098024";
	// 起始日期不能为空
	String CODE_30098033 = "30098033";
	// 截止日期不能为空
	String CODE_30098034 = "30098034";
	// 当前操作用户不是项目专员,不可新增
	String CODE_30098035 = "30098035";
	// 当前操作用户不是项目专员,不可撤回
	String CODE_30098036 = "30098036";
	// 甲方必须实名
	String CODE_30098037 = "30098037";

	// 购买意向相关
	// 购买意向id不能为空
	String CODE_30099001 = "30099001";
	// 指派服务专员id不能为空
	String CODE_30099002 = "30099002";
	// 只有待确认状态可以确认
	String CODE_30099003 = "30099003";
	// 只有待处理,已驳回,处理中,待确认状态的购买意向才能搁浅
	String CODE_30099004 = "30099004";
	// 搁浅原因不能为空
	String CODE_30099005 = "30099005";
	// 搁浅原因长度最大为64
	String CODE_30099006 = "30099006";
	// 只有待指派,已指派,搁浅状态的购买意向才能指派
	String CODE_30099007 = "30099007";
	// 联合采购类型 可出资比例和下游出资金额不能为空
	String CODE_30099008 = "30099008";
	// 项目在交付状态才可以生成签收单
	String CODE_30099009 = "30099009";
	// 采购类型只有1或2
	String CODE_30099010 = "30099010";
	// 未通过企业认证不能进行操作
	String CODE_30099011 = "30099011";
	// 采购数量不能为空
	String CODE_30099012 = "30099012";
	// 总价不能为空
	String CODE_30099013 = "30099013";
	// 请确认存在关联下游采购商签收单已驳回
	String CODE_30099014 = "30099014";
	// 草稿状态下不存在联合-采购商的签收单才能删除
	String CODE_30099015 = "30099015";
	// 勾选的对账单中包含已开票的数据,请重新勾选
	String CODE_30099016 = "30099016";
	// 您没有查看监控的权限,如需查看监控,请购买或升级会员
	String CODE_30099017 = "30099017";
	// 采购说明长度最大为128
	String CODE_30099019 = "30099019";
	// 省编码长度最大为16
	String CODE_30099020 = "30099020";
	// 市编码长度最大为16
	String CODE_30099021 = "30099021";
	// 区域编码长度最大为16
	String CODE_30099022 = "30099022";
	// 联系人长度最大为64
	String CODE_30099023 = "30099023";
	// 手机号长度最大为64
	String CODE_30099024 = "30099024";
	// 提货时间不能为空
	String CODE_30099025 = "30099025";
	// 电话号10-19位
	String CODE_30099026 = "30099026";
	// 供应链id不能为空
	String CODE_30099028 = "30099028";
	// 只有已确认状态可以生成项目
	String CODE_30099029 = "30099029";
	// 只有已搁浅状态下可以关闭
	String CODE_30099030 = "30099030";
	// 只有待处理状态可以开始
	String CODE_30099031 = "30099031";
	// 只有处理中状态可以提交审核
	String CODE_30099032 = "30099032";
	// 只有待审核状态可以审核
	String CODE_30099033 = "30099033";
	// 审核意见长度最大为255
	String CODE_30099034 = "30099034";
	// 审核类型不能为空
	String CODE_30099035 = "30099035";
	// 审核类型只有1或2
	String CODE_30099036 = "30099036";
	// 驳回时审核意见必填
	String CODE_30099037 = "30099037";
	// 只有已确认,已搁浅,已完成可以撤销
	String CODE_30099038 = "30099038";
	// 意向的服务专员不是当前操作人
	String CODE_30099039 = "30099039";
	// 买方预估单价不能为空
	String CODE_30099040 = "30099040";
	// 买方合同金额不能为空
	String CODE_30099041 = "30099041";
	// 买方被冻结金额不能为空
	String CODE_30099042 = "30099042";
	// 买方最低付款额度不能为空
	String CODE_30099043 = "30099043";
	// 供应链合同金额不能为空
	String CODE_30099045 = "30099045";
	// 供应链被冻结金额不能为空
	String CODE_30099046 = "30099046";
	// 供应链最低付款额度不能为空
	String CODE_30099047 = "30099047";
	// 服务费类型错误
	String CODE_30099050 = "30099050";
	// 服务费金额不能为空 取值范围 0~10000000000
	String CODE_30099051 = "30099051";
	// 总价数据错误
	String CODE_30099052 = "30099052";

	// 提货单相关
	// 关联项目不能为空
	String CODE_30100001 = "30100001";
	// 关联签收单不能为空
	String CODE_30100002 = "30100002";
	// 关联的提货单不能为空
	String CODE_30100003 = "30100003";
	// 运输方式为船运时船舶id不能为空
	String CODE_30100004 = "30100004";
	// 确认中或者作废中状态才能进行签署
	String CODE_30100005 = "30100005";
	// 提货单不存在
	String CODE_30100006 = "30100006";
	// 草稿状态的提货单才允许修改
	String CODE_30100007 = "30100007";
	// 只有确认中的状态才能进行驳回
	String CODE_30100008 = "30100008";
	// 草稿状态的提货单才允许删除
	String CODE_30100009 = "30100009";
	// 只有已完成的提货单可以被关联
	String CODE_30100010 = "30100010";
	// 项目在交付状态才可以生成提货单
	String CODE_30100011 = "30100011";
	// 只有签署中或者作废中状态可以签署
	String CODE_30100012 = "30100012";
	// 数量不能为空
	String CODE_30100013 = "30100013";
	// 只有未关联签收单的提货单才能发起作废
	String CODE_30100014 = "30100014";
	// 只有联合购销商未转发的提货单才能发起作废
	String CODE_30100015 = "30100015";
	// 备注长度不能超过20
	String CODE_30100016 = "30100016";
	// 吨数不能为空
	String CODE_30100017 = "30100017";
	// 提货申请日期不能为空
	String CODE_30100018 = "30100018";
	// 凭证文件不能为空
	String CODE_30100019 = "30100019";
	// 已完成状态才能删除
	String CODE_30100020 = "30100020";
	// 需要先删除关联的签收单才能删除
	String CODE_30100021 = "30100021";
	// 需要先删除被关联的提货单才能删除
	String CODE_30100022 = "30100022";
	// 预估单价不能为空
	String CODE_30100023 = "30100023";
	// 提货数量超过可提货预估吨数
	String CODE_30100024 = "30100024";
	// 只有未关联签收单的提货单才能发起作废
	String CODE_30100025 = "30100025";
	// 只有联合购销商未转发的提货单才能发起作废
	String CODE_30100026 = "30100026";
	// 已完成状态才能进行新增
	String CODE_30100028 = "30100028";
	// 只有提货状态是完成并且签收状态是未签署的数据可以关闭
	String CODE_30100029 = "30100029";
	// 运货吨数不能为空
	String CODE_30100030 = "30100030";

	// 对账相关
	// 已发布状态不能进行修改
	String CODE_30101001 = "30101001";
	// 综合指数 应大于0小于1000 保留两位小数
	String CODE_30101002 = "30101002";
	// 计算总金额不能为空
	String CODE_30101003 = "30101003";
	// 实际总金额不能为空
	String CODE_30101004 = "30101004";
	// 备注长度不能超过200
	String CODE_30101005 = "30101005";
	// 签收单id集合不能为空
	String CODE_30101006 = "30101006";
	// 综合指数不能全为空
	String CODE_30101007 = "30101007";
	// 对账id不存在
	String CODE_30101008 = "30101008";
	// 只有草稿状态和驳回状态的对账单才能进行修改操作
	String CODE_30101009 = "30101009";
	// 只有草稿和签署中状态的对账单才能进行驳回操作
	String CODE_30101010 = "30101010";
	// 只有草稿状态和驳回状态的对账单才能进行删除操作
	String CODE_30101011 = "30101011";
	// 驳回原因不能为空
	String CODE_30101012 = "30101012";
	// 类型只能为1-3
	String CODE_30101013 = "30101013";
	// 类型不能为空
	String CODE_30101014 = "30101014";
	// 项目在交付状态才可以生成对账单
	String CODE_30101015 = "30101015";
	// 只有签署中状态可以签署
	String CODE_30101016 = "30101016";
	// 只有已完成状态并且未绑定的签收单可以进行对账
	String CODE_30101017 = "30101017";
	// 签署单id不能为空
	String CODE_30101018 = "30101018";
	// 存在重复日期数据
	String CODE_30101019 = "30101019";
	// 草稿和驳回状态才能进行发起签署
	String CODE_30101020 = "30101020";
	// 对账日期不能为空
	String CODE_30101021 = "30101021";
	// 提货单id集合不能为空
	String CODE_30101022 = "30101022";
	// 提货单id不能为空
	String CODE_30101023 = "30101023";
	// 船舶信息不能为空
	String CODE_30101024 = "30101024";
	// 单价不能为空
	String CODE_30101025 = "30101025";
	// 采购账号id不能为空
	String CODE_30101026 = "30101026";
	// 综合指数页应为3种不同类型数据
	String CODE_30101027 = "30101027";
	// 只有未关联开票的且已完成状态下的对账单才能发起作废
	String CODE_30101028 = "30101028";
	// 签收单不能为空
	String CODE_30101029 = "30101029";
	// 总吨数不能为空
	String CODE_30101030 = "30101030";
	// 核算金额不能为空
	String CODE_30101031 = "30101031";
	// 折扣金额不能为空
	String CODE_30101032 = "30101032";
	// 对账单不存在
	String CODE_30101033 = "30101033";
	// 只有未开票的对账单才能进行关闭操作
	String CODE_30101034 = "30101034";
	// 已完成的项目才能进行新增
	String CODE_30101035 = "30101035";

	// 开票相关
	// 对账单不能为空 最少勾选一个
	String CODE_30102001 = "30102001";

	// 付款相关
	// 已发布状态不能进行删除
	String CODE_30103001 = "30103001";
	// 非完成状态下不能关闭
	String CODE_30103002 = "30103002";
	// 项目id不能为空
	String CODE_30103003 = "30103003";
	// 类型不能为空
	String CODE_30103004 = "30103004";
	// 综合指数页需校验天然砂、机制砂、碎石都不能为空
	String CODE_30103005 = "30103005";
	// 金额不能为空
	String CODE_30103006 = "30103006";
	// 付款日期不能为空
	String CODE_30103007 = "30103007";
	// 凭证文件不能为空
	String CODE_30103008 = "30103008";
	// 综合指数页需校验天然砂、机制砂、碎石的权重和等于100%
	String CODE_30103009 = "30103009";
	// 付款方id不能为空
	String CODE_30103010 = "30103010";
	// 当前付款金额小于可提货预估金额，不支持作废
	String CODE_30103011 = "30103011";
	// 方式不能为空
	String CODE_30103012 = "30103012";
	// 凭证不能为空
	String CODE_30103013 = "30103013";
	// 金额不能大于99999999999999.99
	String CODE_30103014 = "30103014";
	// 金额不能超过（收款单位与付款单位交易金额的总和-已付款总额）
	String CODE_30103015 = "30103015";
	// 非驳回状态下不能删除
	String CODE_30103016 = "30103016";
	// 非驳回状态不能更新
	String CODE_30103017 = "30103017";
	// 驳回原因不能为空
	String CODE_30103018 = "30103018";
	// 驳回原因长度最大为100
	String CODE_30103019 = "30103019";
	// 项目在交付状态才可以生成付款单
	String CODE_30103020 = "30103020";
	// 当前付款金额小于可提货预估金额，不支持作废
	String CODE_30103021 = "30103021";

	// 服务费相关
	// 服务费id不能为空
	String CODE_30104001 = "30104001";
	// 核销日期不能为空
	String CODE_30104002 = "30104002";
	// 核销金额不能为空
	String CODE_30104003 = "30104003";
	// 核销金额不能大于账单金额
	String CODE_30104004 = "30104004";
	// 服务费账单不存在
	String CODE_30104005 = "30104005";
	// 价格日期不能为空
	String CODE_30104006 = "30104006";
	// 项目已完成才能进行新增
	String CODE_30104007 = "30104007";
	// 服务费类型不能为空
	String CODE_30104008 = "30104008";
	// 当前发布状态不是未发布不能进行发布操作
	String CODE_30104009 = "30104009";
	// 吨数不能为空
	String CODE_30104010 = "30104010";
	// 当前选择的日期已存在指数
	String CODE_30104011 = "30104011";
	// 金额不能为空
	String CODE_30104012 = "30104012";
	// 用户id不能为空
	String CODE_30104013 = "30104013";
	// 当前综合指数不存在
	String CODE_30104014 = "30104014";
	// 服务费类型不存在
	String CODE_30104015 = "30104015";

	// 平台银行账号相关
	// 开户名称不能为空 长度为1~25个字符
	String CODE_30105001 = "30105001";
	// 银行账户不能为空 8~30位的正整数
	String CODE_30105002 = "30105002";
	// 开户银行不能为空 长度为1~128个字符
	String CODE_30105003 = "30105003";
	// 平台银行账号不存在
	String CODE_30105004 = "30105004";
	// 禁用状态下不能设置默认
	String CODE_30105005 = "30105005";
	// 该账户已经是默认账户，不能进行禁用
	String CODE_30105006 = "30105006";
	// 批量上传失败
	String CODE_30105007 = "30105007";
	// 无效code
	String CODE_30105008 = "30105008";
	// 银行账户无效
	String CODE_30105009 = "30105009";
	// 最多只能有10个账户
	String CODE_30105010 = "30105010";
	// 禁用状态下不能进行修改
	String CODE_30105011 = "30105011";

	// 砂石综合指数配置相关
	// 数据不存在
	String CODE_30106001 = "30106001";
	// 商品不存在
	String CODE_30106002 = "30106002";
	// 只有草稿和驳回状态才能删除
	String CODE_30106003 = "30106003";
	// 草稿和驳回状态才能进行更新
	String CODE_30106004 = "30106004";
	// 综合指数页类型不能为空
	String CODE_30106005 = "30106005";
	// 综合指数页存在重复类型
	String CODE_30106006 = "30106006";
	// 天然砂、碎石、机制砂配置不能为空
	String CODE_30106007 = "30106007";
	// 天然砂、碎石、机制砂页中区域不能为空
	String CODE_30106008 = "30106008";
	// 天然砂、碎石、机制砂页中选择区域不存在
	String CODE_30106009 = "30106009";
	// 天然砂、碎石、机制砂页中区域不能重复
	String CODE_30106010 = "30106010";
	// 天然砂、碎石、机制砂页中品类不能为空
	String CODE_30106011 = "30106011";
	// 请确认所有关联下游采购商签收单已签署完成
	String CODE_30106012 = "30106012";
	// 天然砂、碎石、机制砂页中品类不能重复
	String CODE_30106013 = "30106013";
	// 天然砂、碎石、机制砂页中区域权重不能为空
	String CODE_30106014 = "30106014";
	// 天然砂、碎石、机制砂页中区域权重之和必须等于100%
	String CODE_30106015 = "30106015";
	// 天然砂、碎石、机制砂页中价格类型不能为空
	String CODE_30106016 = "30106016";
	// 天然砂、碎石、机制砂页中价格类型不存在
	String CODE_30106017 = "30106017";
	// 天然砂、碎石、机制砂页中价格类型不能重复
	String CODE_30106018 = "30106018";
	// 天然砂、碎石、机制砂页中价格权重不能为空
	String CODE_30106019 = "30106019";
	// 天然砂、碎石、机制砂页中价格权重之和必须等于100%
	String CODE_30106020 = "30106020";
	// 参数错误
	String CODE_30106021 = "30106021";
	// 品类不存在
	String CODE_30106022 = "30106022";
	// 请检查品类类型及所属区域
	String CODE_30106023 = "30106023";
	// 天然砂、碎石、机制砂页中区域不能全为空
	String CODE_30106024 = "30106024";
	// 类型不能为空
	String CODE_30106025 = "30106025";
	// 类型不存在
	String CODE_30106026 = "30106026";

	// 船舶相关
	// MMSI编号不能为空
	String CODE_30120001 = "30120001";
	// MMSI格式错误
	String CODE_30120002 = "30120002";
	// 船只名称不能为空
	String CODE_30120003 = "30120003";
	// 船只名称长度不能超过64位
	String CODE_30120004 = "30120004";
	// 船只类型不能为空
	String CODE_30120005 = "30120005";
	// 船只长度不能为空
	String CODE_30120006 = "30120006";
	// 船只宽度不能为空
	String CODE_30120007 = "30120007";
	// 船只编号不能为空
	String CODE_30120008 = "30120008";
	// 船只载重不能为空
	String CODE_30120009 = "30120009";
	// 船只负责人长度不能超过10位
	String CODE_30120010 = "30120010";
	// 联系方式格式不正确
	String CODE_30120011 = "30120011";
	// 船舶营业运输证不能为空
	String CODE_30120012 = "30120012";
	// 船舶营业运输证长度不能超过20位
	String CODE_30120013 = "30120013";
	// 船舶营业运输证图片链接不能为空
	String CODE_30120014 = "30120014";
	// 船只照片不能为空
	String CODE_30120015 = "30120015";
	// 船舶id不能为空
	String CODE_30120016 = "30120016";
	// 船舶编号长度不能超过20位
	String CODE_30120017 = "30120017";
	// MMSI编号已存在
	String CODE_30120018 = "30120018";
	// 船舶编号已存在
	String CODE_30120019 = "30120019";
	// 船只照片最多上传3张
	String CODE_30130121 = "30130121";
	// 审核状态不合法,只能为通过或不通过
	String CODE_30120021 = "30120021";
	// 审核备注长度不能超过100位
	String CODE_30120022 = "30120022";
	// 只有待审核状态的船舶才能进行审核操作
	String CODE_30120023 = "30120023";
	// 船舶不存在
	String CODE_30120024 = "30120024";
	// 只有已通过状态的船舶才能进行修改操作
	String CODE_30120025 = "30120025";
	// 船舶编号已存在
	String CODE_30120026 = "30120026";
	// 只有已禁用状态的船舶才能进行启用操作
	String CODE_30120027 = "30120027";
	// 只有已通过状态的船舶才能进行禁用操作
	String CODE_30120028 = "30120028";
	// 只有未通过/已禁用状态的船舶才能进行关闭操作
	String CODE_30120122 = "30120122";
	// 船舶下存在设备,不能进行关闭操作
	String CODE_30120030 = "30120030";
	// 文件名称不能为空
	String CODE_30120031 = "30120031";
	// 船只照片最多上传3张
	String CODE_30130127 = "30130127";
	// 船只照片最多上传1张
	String CODE_30120033 = "30120033";
	// 船舶下未绑定设备,不能进行直播操作
	String CODE_30120034 = "30120034";
	// 只有已通过状态的船舶才能进行直播操作
	String CODE_30120035 = "30120035";
	// 船舶下未绑定该设备,不能进行直播或抓拍图片操作
	String CODE_30120036 = "30120036";
	// 文件不存在
	String CODE_30120037 = "30120037";
	// 承运商id不能为空
	String CODE_30120038 = "30120038";
	// 坐标点存在空值
	String CODE_30120039 = "30120039";
	// 经度差值应小于等于两度
	String CODE_30120040 = "30120040";
	// 纬度差值应小于等于两度
	String CODE_30120041 = "30120041";
	// 坐标点的经度或纬度存在空值
	String CODE_30120042 = "30120042";
	// 船只照片不能为空
	String CODE_30120043 = "30120043";
	// 船只照片最少1张，最多10张
	String CODE_30120044 = "30120044";
	// 船只视频不能为空
	String CODE_30120045 = "30120045";
	// 船只视频最少1个，最多5个
	String CODE_30120046 = "30120046";
	// 船舶未认证才能进行绑定
	String CODE_30120047 = "30120047";
	// 船舶已认证才能进行解绑
	String CODE_30120048 = "30120048";
	// 范围超过限制，暂不支持查询
	String CODE_30120049 = "30120049";
	// 坐标点数量小于3
	String CODE_30120050 = "30120050";
	// 散货船细分不存在
	String CODE_30120051 = "30120051";
	// 船舶类型不存在
	String CODE_30120052 = "30120052";
	// 备注长度不能超过200
	String CODE_30120053 = "30120052";

	// 船舶认证申请相关
	// 船舶id不能为空
	String CODE_30121001 = "30121001";
	// 手持身份证的照片不能为空
	String CODE_30121002 = "30121002";
	// 船舶证书照片不能为空
	String CODE_30121003 = "30121003";
	// 船舶认证申请不存在
	String CODE_30121004 = "30121004";
	// 船舶未认证才能进行认证申请
	String CODE_30121005 = "30121005";
	// 手持身份证的照片不能为空
	String CODE_30121006 = "30121006";
	// 船舶证书照片不能为空
	String CODE_30121007 = "30121007";
	// 已驳回的状态才能进行删除
	String CODE_30121008 = "30121008";
	// 该船舶正在认证中，不能重复认证
	String CODE_30121009 = "30121009";
	// 审核备注不能为空
	String CODE_30121010 = "30121010";
	// 审核备注不能超过100个字符
	String CODE_30121011 = "30121011";
	// 非待审核状态下不能进行审核
	String CODE_30121012 = "30121012";

	// 船务信息服务费
	// 支付凭证不能为空
	String CODE_30122001 = "30122001";
	// 船运单id不能为空
	String CODE_30122002 = "30122002";
	// 类型不能为空
	String CODE_30122003 = "30122003";
	// 类型不能存在
	String CODE_30122004 = "30122004";
	// 只有待支付状态才能进行支付
	String CODE_30122005 = "30122005";
	// 待支付信息服务费状态才能进行支付
	String CODE_30122006 = "30122006";
	// 支付方式为线下才能进行线下支付
	String CODE_30122007 = "30122007";
	// 船务信息服务费不存在
	String CODE_30122008 = "30122008";
	// 当前状态不允许确认
	String CODE_30122009 = "30122009";
	// 当前支付类型不允许确认
	String CODE_30122010 = "30122010";
	// 支付时间不能为空
	String CODE_30122011 = "30122011";

	// 航线相关
	// 始发地不能为空
	String CODE_30123001 = "30123001";
	// 目的地不能为空
	String CODE_30123002 = "30123002";
	// 始发地省份不能为空
	String CODE_30123003 = "30123003";
	// 始发地城市不能为空
	String CODE_30123004 = "30123004";
	// 目的地省份不能为空
	String CODE_30123005 = "30123005";
	// 目的地城市不能为空
	String CODE_30123006 = "30123006";
	// 航线id不能为空
	String CODE_30123007 = "30123007";
	// 航线信息不存在
	String CODE_30123008 = "30123008";
	// 权重不能为空 取值范围为0~10000
	String CODE_30123009 = "30123009";

	// 船舶水尺相关
	// 吃水取值范围 0.01-100 保留两位小数
	String CODE_30124001 = "30124001";
	// 干舷取值范围 0.01-100 保留两位小数
	String CODE_30124002 = "30124002";
	// 载货量取值范围 1-100000 需为整数
	String CODE_30124003 = "30124003";
	// 上传数据量最大不能超过1000
	String CODE_30124004 = "30124004";
	// 船舶信息不存在
	String CODE_30124005 = "30124005";
	// 船舶水尺信息不存在
	String CODE_30124006 = "30124006";
	// 该船舶水尺 吃水或干舷已存在
	String CODE_30124007 = "30124007";
	// 吃水不能为空
	String CODE_30124008 = "30124008";
	// 干舷不能为空
	String CODE_30124009 = "30124009";
	// 载货量不能为空
	String CODE_30124010 = "30124010";
	// 收集人信息不能为空
	String CODE_30124011 = "30124011";

	// 船舶监控分享
	// 船舶监控分享的好友最多只能添加5位
	String CODE_30125001 = "30125001";
	// 自己的设备不能分享给自己
	String CODE_30125002 = "30125002";

	// 标签相关
	// 标签不存在
	String CODE_30126001 = "30126001";
	// 标签名称不能为空
	String CODE_30126002 = "30126002";
	// 标签名称长度不能超过10个字符
	String CODE_30126003 = "30126003";
	// 此标签被引用不支持删除
	String CODE_30126004 = "30126004";
	// 标签名称已存在
	String CODE_30126005 = "30126005";

	// 置顶相关
	// 业务ID不能为空
	String CODE_30127001 = "30127001";
	// 类型不能为空
	String CODE_30127002 = "30127002";
	// 类型不存在
	String CODE_30127003 = "30127003";

	// 船运需求相关
	// 当前状态不是确认中,不能进行接单操作
	String CODE_30128001 = "30128001";
	// 定金金额只能是大于0小于100亿的数字
	String CODE_30128002 = "30128002";
	// 定金金额必须精确到小数点后2位
	String CODE_30128003 = "30128003";
	// 不存在的承运商船运需求
	String CODE_30128004 = "30128004";
	// 当前状态不是确认中,不能进行接单操作
	String CODE_30128005 = "30128005";
	// 拒绝备注不能为空
	String CODE_30128006 = "30128006";
	// 拒绝备注不能超过200个字符
	String CODE_30128007 = "30128007";
	// 当前状态不是已接单,不能进行收到定金操作
	String CODE_30128008 = "30128008";
	// 货品类型不能为空
	String CODE_30128009 = "30128009";
	// 货品类型长度不能超过32位
	String CODE_30128010 = "30128010";
	// 船型不能为空
	String CODE_30128011 = "30128011";
	// 船型长度不能超过32位
	String CODE_30128012 = "30128012";
	// 始发地码头id不能为空
	String CODE_30128013 = "30128013";
	// 始发地码头名称不能为空
	String CODE_30128014 = "30128014";
	// 目的地码头id不能为空
	String CODE_30128015 = "30128015";
	// 目的地码头名称不能为空
	String CODE_30128016 = "30128016";
	// 目的地码头名称不能为空
	String CODE_30128017 = "30128017";
	// 航线id不能为空
	String CODE_30128018 = "30128018";
	// 航线始发地不能为空
	String CODE_30128019 = "30128019";
	// 航线目的地不能为空
	String CODE_30128020 = "30128020";
	// 意向单价不能为空
	String CODE_30128021 = "30128021";
	// 意向单价必须大于0小于100亿且最多保留两位小数
	String CODE_30128022 = "30128022";
	// 意向吨位不能为空
	String CODE_30128023 = "30128023";
	// 意向吨位必须大于0且为正整数
	String CODE_30128024 = "30128024";
	// 装载日期不能为空
	String CODE_30128025 = "30128025";
	// 宽限天数不能为空
	String CODE_30128026 = "30128026";
	// 宽限天数必须大于0且为正整数
	String CODE_30128027 = "30128027";
	// 装卸天数不能为空
	String CODE_30128028 = "30128028";
	// 装卸天数必须大于0且为正整数
	String CODE_30128029 = "30128029";
	// 滞期费必须大于0小于100亿且最多保留两位小数
	String CODE_30128030 = "30128030";
	// 海事费用不能为空
	String CODE_30128031 = "30128031";
	// 吨位随船不能为空
	String CODE_30128032 = "30128032";
	// 吨位不随船时最大吨位数必须大于0且为正整数
	String CODE_30128033 = "30128033";
	// 吨位不随船时最小吨位数必须大于0且为正整数
	String CODE_30128034 = "30128034";
	// 联系人不能为空
	String CODE_30128035 = "30128035";
	// 联系人长度不能超过32位
	String CODE_30128036 = "30128036";
	// 联系电话不能为空
	String CODE_30128037 = "30128037";
	// 联系电话格式不正确
	String CODE_30128038 = "30128038";
	// 补充约定长度不能超过200位
	String CODE_30128039 = "30128039";
	// 货主船运需求不存在
	String CODE_30128040 = "30128040";
	// 当前状态不合法,不能进行指派操作
	String CODE_30128041 = "30128041";
	// 当前状态不合法,不能进行开始操作
	String CODE_30128042 = "30128042";
	// 当前状态不合法,不能进行修改操作
	String CODE_30128043 = "30128043";
	// 吨位不随船时吨位要求不能为空
	String CODE_30128044 = "30128044";
	// 吨位不随船时最大吨位必须大于等于最小吨位
	String CODE_30128045 = "30128045";
	// 当前状态不合法,不能进行完成操作
	String CODE_30128046 = "30128046";
	// 当前货主需求下没有关联平台船运单,不能进行完成操作
	String CODE_30128047 = "30128047";
	// 船务信息服务费不能大于定金
	String CODE_30128048 = "30128048";
	// 船务信息服务费必须精确到小数点后2位
	String CODE_30128049 = "30128049";
	// 船运定金不能为空
	String CODE_30128050 = "30128050";
	// 船务信息服务费不能为空
	String CODE_30128051 = "30128051";
	// 支付类型不存在
	String CODE_30128052 = "30128052";
	// 船务信息服务费不能为空
	String CODE_30128053 = "30128053";
	// 是否包含排水槽字段不能为空
	String CODE_30128054 = "30128054";
	// 是否提交不能为空
	String CODE_30128055 = "30128055";
	// 当前货主找船需求下没有关联船运单,不能进行完成操作
	String CODE_30128056 = "30128056";
	// 当前登录用户不是该船运需求的船务专员，不可操作
	String CODE_30128057 = "30128057";
	// 当前状态不是处理中,不能进行提交操作
	String CODE_30128058 = "30128058";
	// 运价不能为空
	String CODE_30128059 = "30128059";
	// 吨位不能为空
	String CODE_30128060 = "30128060";
	// 船运定金不能为空
	String CODE_30128061 = "30128061";
	// 船务信息服务费不能为空
	String CODE_30128062 = "30128062";
	// 运费结算确认节点只能为待发航或待卸货
	String CODE_30128063 = "30128063";
	// 货主id不能为空
	String CODE_30128064 = "30128064";
	// 货主公司信息不能为空
	String CODE_30128065 = "30128065";
	// 货品类型不能为空
	String CODE_30128066 = "30128066";
	// 始发地码头id不能为空
	String CODE_30128067 = "30128067";
	// 始发地码头名称不能为空
	String CODE_30128068 = "30128068";
	// 目的地码头id不能为空
	String CODE_30128069 = "30128069";
	// 目的地码头名称不能为空
	String CODE_30128070 = "30128070";
	// 意向单价不能为空
	String CODE_30128071 = "30128071";
	// 运输最大吨数不能为空
	String CODE_30128072 = "30128072";
	// 运输最小吨数不能为空
	String CODE_30128073 = "30128073";
	// 装载日期不能为空
	String CODE_30128074 = "30128074";
	// 装载日期的宽限天数不能为空
	String CODE_30128075 = "30128075";
	// 装卸天数不能为空
	String CODE_30128076 = "30128076";
	// 装卸天数必须在1-127之间
	String CODE_30128077 = "30128077";
	// 滞期费不能为空
	String CODE_30128078 = "30128078";
	// 特殊说明不能超过200个字符
	String CODE_30128079 = "30128079";
	// 定金支付方式不能为空
	String CODE_30128080 = "30128080";
	// 船务信息服务费不能为空
	String CODE_30128081 = "30128081";
	// 船运定金的取值范围为大于0的正整数,最多10位
	String CODE_30128082 = "30128082";
	// 平台船运需求ID不能为空
	String CODE_30128083 = "30128083";
	// 承运船相关信息不能为空
	String CODE_30128084 = "30128084";
	// 找船需求已经关联船运单
	String CODE_30128085 = "30128085";
	// 只有状态为已发布的平台船运需求才允许指派专员操作
	String CODE_30128086 = "30128086";
	// 只有状态为已发布的平台船运需求才允许关闭
	String CODE_30128087 = "30128087";
	// 不存在的平台船运需求
	String CODE_30128088 = "30128088";
	// 该平台船运需求已经关联船运单,不能关闭
	String CODE_30128089 = "30128089";
	// 货主船运需求已经被关联
	String CODE_30128090 = "30128090";
	// 已确认的状态才能进行绑定
	String CODE_30128091 = "30128091";
	// 运输吨数不能为空
	String CODE_30128092 = "30128092";
	// 船运定金不能为空
	String CODE_30128093 = "30128093";
	// 需要智能推荐船舶的船运需求id不能为空
	String CODE_30128094 = "30128094";
	// 邀请抢单的船舶id不能为空
	String CODE_30128095 = "30128095";
	// 船运需求已发布状态才有智能推荐船舶
	String CODE_30128096 = "30128096";
	// 没有可以邀请的船舶
	String CODE_30128097 = "30128097";
	// 电联号码类型不能为空
	String CODE_30128098 = "30128098";
	// 电联号码类型不存在
	String CODE_30128099 = "30128099";
	// 电联号码不能为空
	String CODE_30128100 = "30128100";
	// 电联号码格式错误
	String CODE_30128101 = "30128101";

	// 承运商船运需求
	// 承运商接单信息不存在
	String CODE_30129001 = "30129001";
	// 承运商修改接单信息状态不合法
	String CODE_30129002 = "30129002";
	// 平台船运需求不存在
	String CODE_30129003 = "30129003";
	// 平台船运需求已关闭
	String CODE_30129004 = "30129004";
	// 承运商取消接单信息状态不合法
	String CODE_30129005 = "30129005";
	// 承运商确认接单信息状态不合法
	String CODE_30129006 = "30129006";
	// 承运商只能选择一条船舶
	String CODE_30129007 = "30129007";
	// 当前只有处于卸货中、已卸货或已完成状态的运单才允许抢单
	String CODE_30129008 = "30129008";
	// 晚了一步，已被其他船主抢单
	String CODE_30129009 = "30129009";
	// 不可重复接单
	String CODE_30129010 = "30129010";
	// 船运需求不存在
	String CODE_30129011 = "30129011";
	// 已发布的船运需求才能接单
	String CODE_30129012 = "30129012";

	// 船主线索信息相关
	// 船主线索信息不存在
	String CODE_30130001 = "30130001";
	// 当前状态是已处理,不能进行提交操作
	String CODE_30130002 = "30130002";
	// 备注不能为空
	String CODE_30130003 = "30130003";
	// 备注长度应为1-200位
	String CODE_30130004 = "30130004";

	// 运价综合指数
	// 运价综合指数不存在
	String CODE_30131001 = "30131001";
	// 日期不能为空
	String CODE_30131002 = "30131002";
	// 综合指数只能0-1000保留两位小数
	String CODE_30131003 = "30131003";
	// 当前日期已存在指数
	String CODE_30131004 = "30131004";
	// 综合指数不能为空
	String CODE_30131005 = "30131005";
	// 日期不能重复
	String CODE_30131006 = "30131006";
	// 未发布状态才能进行修改
	String CODE_30131007 = "30131007";
	// 未发布状态才能进行删除
	String CODE_30131008 = "30131008";
	// 未发布状态才能进行发布
	String CODE_30131009 = "30131009";
	// 已发布状态才能进行撤回
	String CODE_30131010 = "30131010";
	// 不能发布今日之后指数数据
	String CODE_30131011 = "30131011";

	// 运价综合指数配置相关
	// 运价综合指数配置不存在
	String CODE_30132001 = "30132001";
	// 始发地列表不能为空
	String CODE_30132002 = "30132002";
	// 始发地省份编码
	String CODE_30132003 = "30132003";
	// 始发地城市编码
	String CODE_30132004 = "30132004";
	// 始发地权重不能为空
	String CODE_30132005 = "30132005";
	// 始发地存在重复数据
	String CODE_30132006 = "30132006";
	// 航线不能为空
	String CODE_30132007 = "30132007";
	// 航线id不能为空
	String CODE_30132008 = "30132008";
	// 航线不存在
	String CODE_30132009 = "30132009";
	// 航线权重不能为空
	String CODE_30132010 = "30132010";
	// 航线吨位不能为空
	String CODE_30132011 = "30132011";
	// 存在重复航线
	String CODE_30132012 = "30132012";
	// 吨位类型不能为空
	String CODE_30132013 = "30132013";
	// 吨位类型不存在
	String CODE_30132014 = "30132014";
	// 吨位权重不能为空
	String CODE_30132015 = "30132015";
	// 存在重复吨位
	String CODE_30132016 = "30132016";
	// 吨位权重之和必须为100%
	String CODE_30132017 = "30132017";
	// 航线权重之和必须为100%
	String CODE_30132018 = "30132018";
	// 始发地权重之和必须为100%
	String CODE_30132019 = "30132019";

	// 消息相关
	// 流水号不能为空
	String CODE_30133001 = "30133001";
	// 场景ID不能为空
	String CODE_30133002 = "30133002";
	// 验证码校验参数不能为空
	String CODE_30133003 = "30133003";
	// 手机号不存在
	String CODE_30133004 = "30133004";
	// 手机号被禁用
	String CODE_30133005 = "30133005";

	// 货主船运需求
	// 当前状态不是处理中，待确认，找船中状态状态,不允许取消
	String CODE_30134001 = "30134001";
	// 当前状态不是待货主确认,不允许进行确认操作
	String CODE_30134002 = "30134002";
	// 吨位不随船时最大吨位必须大于等于最小吨位
	String CODE_30134003 = "30134003";
	// 装载日期宽限天数不能为负数
	String CODE_30134004 = "30134004";
	// 装卸天数不能为负数
	String CODE_30134005 = "30134005";
	// 船运定金的取值范围为大于0的正整数,最多10位
	String CODE_30134006 = "30134006";
	// 选择链云垫付需完成个人实名
	String CODE_30134007 = "30134007";
	// 船运需求不存在
	String CODE_30134008 = "30134008";
	// 船运定金不能为空
	String CODE_30134009 = "30134009";
	// 已帮您成功匹配到船主，不能取消
	String CODE_30134010 = "30134010";
	// 货品类型不能为空
	String CODE_30134011 = "30134011";
	// 货品类型长度不能超过32位
	String CODE_30134012 = "30134012";
	// 船型不能为空
	String CODE_30134013 = "30134013";
	// 排水槽字段不能为空
	String CODE_30134014 = "30134014";
	// 始发地码头id不能为空
	String CODE_30134015 = "30134015";
	// 始发地码头名称不能为空
	String CODE_30134016 = "30134016";
	// 目的地码头id不能为空
	String CODE_30134017 = "30134017";
	// 目的地码头名称不能为空
	String CODE_30134018 = "30134018";
	// 意向单价不能为空
	String CODE_30134019 = "30134019";
	// 意向单价必须大于0小于100亿且最多保留两位小数
	String CODE_30134020 = "30134020";
	// 意向吨位不能为空
	String CODE_30134021 = "30134021";
	// 意向吨位必须大于0且为正整数
	String CODE_30134022 = "30134022";
	// 装载日期不能为空
	String CODE_30134023 = "30134023";
	// 宽限天数不能为空
	String CODE_30134024 = "30134024";
	// 装卸天数不能为空
	String CODE_30134025 = "30134025";
	// 滞期费必须大于0小于100亿且最多保留两位小数
	String CODE_30134026 = "30134026";
	// 海事费用不能为空
	String CODE_30134027 = "30134027";
	// 吨位不随船时最大吨位数必须大于0且为正整数
	String CODE_30134028 = "30134028";
	// 吨位不随船时最小吨位数必须大于0且为正整数
	String CODE_30134029 = "30134029";
	// 联系人长度不能超过32位
	String CODE_30134030 = "30134030";
	// 联系电话不能为空
	String CODE_30134031 = "30134031";
	// 联系电话格式不正确
	String CODE_30134032 = "30134032";
	// 补充约定长度不能超过200位
	String CODE_30134033 = "30134033";
	// 船舶类型不存在
	String CODE_30134034 = "30134034";

	// 运价指数版本相关
	// 版本日期不能为空
	String CODE_30135001 = "30135001";
	// 版本名称最多32个字符
	String CODE_30135002 = "30135002";
	// 版本日期已存在
	String CODE_30135003 = "30135003";
	// 存在指数数据，不能修改
	String CODE_30135004 = "30135004";
	// 存在指数数据，不能删除
	String CODE_30135005 = "30135005";
	// 版本不存在
	String CODE_30135006 = "30135006";
	// 日期不能在今天之后
	String CODE_30135007 = "30135007";

	// 运价指数版本记录相关
	// 运价指数版本记录不存在
	String CODE_30136001 = "30136001";
	// 版本id不能为空
	String CODE_30136002 = "30136002";
	// 待提交、已驳回状态才能进行提交、修改、删除操作
	String CODE_30136003 = "30136003";
	// 待发布、已撤回状态才能进行发布
	String CODE_30136004 = "30136004";
	// 待发布、已撤回状态才能进行驳回
	String CODE_30136005 = "30136005";
	// 已发布状态且是最新发布数据才能进行撤回
	String CODE_30136006 = "30136006";
	// 当前已经是最初发布版本，无法进行撤回！
	String CODE_30136007 = "30136007";
	// 备注不能超过100个字符
	String CODE_30136008 = "30136008";
	// 待提交、已驳回状态才能进行保存
	String CODE_30136009 = "30136009";
	// 指数数据为空，不能进行提交
	String CODE_30136010 = "30136010";

	// 船运单相关
	// 船运单不存在
	String CODE_30137001 = "30137001";
	// 货名不能为空
	String CODE_30137002 = "30137002";
	// 货名不能超过32个字符
	String CODE_30137003 = "30137003";
	// 货主不能为空
	String CODE_30137004 = "30137004";
	// 联系人不能为空
	String CODE_30137005 = "30137005";
	// 手机号不能为空
	String CODE_30137006 = "30137006";
	// 运价不能为空
	String CODE_30137007 = "30137007";
	// 运价大于0小于10000，保留2位小数
	String CODE_30137008 = "30137008";
	// 运货量不能为空且大于0小于1000000
	String CODE_30137009 = "30137009";
	// 装载日期不能为空
	String CODE_30137010 = "30137010";
	// 宽限天数不能为空
	String CODE_30137011 = "30137011";
	// 装卸天数不能为空
	String CODE_30137012 = "30137012";
	// 船舶不能为空
	String CODE_30137013 = "30137013";
	// 联系人不能超过32个字符
	String CODE_30137014 = "30137014";
	// 手机号格式错误
	String CODE_30137015 = "30137015";
	// 始发港不能为空
	String CODE_30137019 = "30137019";
	// 目的港不能为空
	String CODE_30137020 = "30137020";
	// 链云运单ID不能为空
	String CODE_30137021 = "30137021";
	// 待发航前状态才能进行关闭
	String CODE_30137022 = "30137022";
	// 待装货才能进行开始装货
	String CODE_30137023 = "30137023";
	// 装货中才能进行发航申请
	String CODE_30137024 = "30137024";
	// 卸货中才能进行确认卸货
	String CODE_30137025 = "30137025";
	// 发货单ID不能为空
	String CODE_30137026 = "30137026";
	// 订单ID不能为空
	String CODE_30137027 = "30137027";
	// 规格/型号不能为空
	String CODE_30137028 = "30137028";
	// 货主联系地址不能为空
	String CODE_30137034 = "30137034";
	// 船主联系地址不能为空
	String CODE_30137035 = "30137035";
	// 货主联系地址不能超过128个字符
	String CODE_30137036 = "30137036";
	// 船主联系地址不能超过128个字符
	String CODE_30137037 = "30137037";
	// 已卸货状态才能完成
	String CODE_30137039 = "30137039";
	// 支付类型不能为空
	String CODE_30137040 = "30137040";
	// 支付类型不存在
	String CODE_30137041 = "30137041";
	// 合同文件不能为空
	String CODE_30137042 = "30137042";
	// 运费结算吨位确认节点不能为空
	String CODE_30137043 = "30137043";
	// 运费结算吨位确认节点不存在
	String CODE_30137044 = "30137044";
	// 货主还未确认运费结算吨位
	String CODE_30137045 = "30137045";
	// 船主还未确认运费结算吨位
	String CODE_30137046 = "30137046";
	// 货主不存在
	String CODE_30137047 = "30137047";
	// 船主不存在
	String CODE_30137048 = "30137048";
	// 货主未进行个人认证或者企业认证
	String CODE_30137049 = "30137049";
	// 船主未进行个人认证或者企业认证
	String CODE_30137050 = "30137050";
	// 货主船务信息服务费不能为空
	String CODE_30137051 = "30137051";
	// 船主船务信息服务费不能为空
	String CODE_30137052 = "30137052";
	// 海事费用不存在
	String CODE_30137053 = "30137053";
	// 货主和船主不能是同一个账号
	String CODE_30137054 = "30137054";
	// 船运单已经被绑定
	String CODE_30137055 = "30137055";
	// 平台船运需求已经被关联
	String CODE_30137056 = "30137056";
	// 船运单已关闭
	String CODE_30137057 = "30137057";
	// 已清仓状态才能开始卸货
	String CODE_30137058 = "30137058";
	// 已清仓状态才能完成清仓
	String CODE_30137059 = "30137059";
	// 宽限天数范围0-30
	String CODE_30137060 = "30137060";
	// 装卸天数范围0-30
	String CODE_30137061 = "30137061";
	// 补充说明最多200个字符
	String CODE_30137062 = "30137062";
	// 滞期费大于0小于10000元/吨/天，保留2位小数
	String CODE_30137063 = "30137063";
	// 开始卸货之后才能进行完成清仓
	String CODE_30137064 = "30137064";
	// 定金金额必须大于0小于100000000.00
	String CODE_30137065 = "30137065";
	// 装货时间不能为空
	String CODE_30137066 = "30137066";
	// 卸货时间不能为空
	String CODE_30137067 = "30137067";
	// 附件数量不能超过10个
	String CODE_30137068 = "30137068";
	// 卸货时间必须晚于装货时间
	String CODE_30137069 = "30137069";

	// 船运单相关
	// 船运单不存在
	String CODE_30138001 = "30138001";
	// 合同签署状态才能进行签署
	String CODE_30138002 = "30138002";
	// 货主未签署状态才能进行签署
	String CODE_30138003 = "30138003";
	// 船主未签署状态才能进行签署
	String CODE_30138004 = "30138004";
	// 货主签署完成之后才能进行签署
	String CODE_30138005 = "30138005";
	// 当前用户无权限进行确认
	String CODE_30138006 = "30138006";
	// 付款凭证不能为空
	String CODE_30138007 = "30138007";
	// 自行付款才能进行支付
	String CODE_30138008 = "30138008";
	// 未支付状态才能进行支付
	String CODE_30138009 = "30138009";
	// 船主未确认状态才能进行确认
	String CODE_30138010 = "30138010";
	// 待支付定金状态才能进行确认
	String CODE_30138011 = "30138011";
	// 待支付定金状态才能进行支付
	String CODE_30138012 = "30138012";
	// 船运定金不存在
	String CODE_30138013 = "30138013";
	// 船运定金未结算或未支付
	String CODE_30138014 = "30138014";
	// 您已经确吨位，不可重复操作
	String CODE_30138015 = "30138015";
	// 货主确认完之后才能进行确认
	String CODE_30138016 = "30138016";
	// 待发航状态才能进行确认
	String CODE_30138017 = "30138017";
	// 待卸货状态才能进行确认
	String CODE_30138018 = "30138018";
	// 已完成清仓不能进行修改
	String CODE_30138019 = "30138019";
	// 银行账户不存在
	String CODE_30138020 = "30138020";
	// 该类型运单不支持修改
	String CODE_30138021 = "30138021";
	// 已取消状态不能进行操作
	String CODE_30138022 = "30138022";
	// 该状态不能支付运费
	String CODE_30138023 = "30138023";
    // {0}不能操作
    String CODE_30138024 = "30138024";

	// 船运单明细相关
	// 装货日期不能为空
	String CODE_30139001 = "30139001";
	// 集港货物吨位不能为空
	String CODE_30139002 = "30139002";
	// 集港货物吨位应大于0小于1000000吨，保留2位小数
	String CODE_30139003 = "30139003";
	// 备注不能超过200个字符
	String CODE_30139004 = "30139004";
	// 装载吨位不能为空
	String CODE_30139005 = "30139005";
	// 装载吨位必须大于0小于1000000吨，保留2位小数
	String CODE_30139006 = "30139006";
	// 量方吨位必须大于0小于1000000吨，保留2位小数
	String CODE_30139007 = "30139007";
	// 运费结算吨位必须大于0
	String CODE_30139008 = "30139008";
	// 六处水尺-1范围为0-1000米，保留2位小数
	String CODE_30139009 = "30139009";
	// 六处水尺-2范围为0-1000米，保留2位小数
	String CODE_30139010 = "30139010";
	// 六处水尺-3范围为0-1000米，保留2位小数
	String CODE_30139011 = "30139011";
	// 六处水尺-4范围为0-1000米，保留2位小数
	String CODE_30139012 = "30139012";
	// 六处水尺-5范围为0-1000米，保留2位小数
	String CODE_30139013 = "30139013";
	// 六处水尺-6范围为0-1000米，保留2位小数
	String CODE_30139014 = "30139014";
	// 滞期天数必须大于等于0，小于1000的数字
	String CODE_30139015 = "30139015";
	// 卸货吨位必须大于0小于1000000吨，保留2位小数
	String CODE_30139016 = "30139016";
	// 卸货量方吨位必须大于0小于1000000吨，保留2位小数
	String CODE_30139017 = "30139017";
	// 船舶不存在
	String CODE_30139018 = "30139018";
	// 船运单明细不存在
	String CODE_30139019 = "30139019";
	// 已完成状态不能进行修改
	String CODE_30139020 = "30139020";
	// 运输中状态不能进行修改
	String CODE_30139021 = "30139021";
	// 运费结算吨位不能为空
	String CODE_30139022 = "30139022";
	// 待卸货节点才能进行确认吨位
	String CODE_30139023 = "30139023";
	// 运费结算吨位不能为空
	String CODE_30139024 = "30139024";
	// 实际运费不能为空
	String CODE_30139025 = "30139025";
	// 实际运费必须大于0
	String CODE_30139026 = "30139026";
	// 接档记录信息不能为空
	String CODE_30139027 = "30139027";
	// 船舶到港时间应早于装货上档时间
	String CODE_30139028 = "30139028";
	// 此船运单已经被绑定
	String CODE_30139029 = "30139029";
	// 发货完成不能新增删除船运单
	String CODE_30139030 = "30139030";
	// 满载视频不能为空
	String CODE_30139031 = "30139031";
	// 吨位证明不能为空
	String CODE_30139032 = "30139032";

	// 船运单明细相关
	// 船运单明细相关不存在
	String CODE_30140001 = "30140001";
	// 待发航才能进行发航确认
	String CODE_30140002 = "30140002";
	// 运输中才能进行到港确认
	String CODE_30140003 = "30140003";
	// 待卸货才能进行同意卸货
	String CODE_30140004 = "30140004";
	// 您已经确认吨位，不可重复操作
	String CODE_30140005 = "30140005";
	// 您已经确认吨位，不可重复操作
	String CODE_30140006 = "30140006";
	// 是否排水不能为空
	String CODE_30140007 = "30140007";
	// 实际结算吨位不能为空
	String CODE_30140008 = "30140008";
	// 实际结算吨位必须大于0小于1000000吨，保留2位小数
	String CODE_30140009 = "30140009";
	// 船主未确认运费结算吨位才能进行修改
	String CODE_30140010 = "30140010";
	// 待发航的状态才能进行修改运费结算吨位
	String CODE_30140011 = "30140011";
	// 待卸货的状态才能进行修改运费结算吨位
	String CODE_30140012 = "30140012";
	// 到港时间不能为空
	String CODE_30140013 = "30140013";
	// 到港视频ID不能为空
	String CODE_30140014 = "30140014";
	// 实际结算吨位不能为空
	String CODE_30140015 = "30140015";
	// 卸货完成时间不能为空
	String CODE_30140016 = "30140016";
	// 清仓视频不能为空
	String CODE_30140017 = "30140017";
	// 发航时间不能为空
	String CODE_30140018 = "30140018";
	// 装载完成时间不能为空
	String CODE_30140019 = "30140019";
	// 装载时长不能为空
	String CODE_30140020 = "30140020";
	// 发航时间不能为空
	String CODE_30140021 = "30140021";
	// 预计到达日期不能为空
	String CODE_30140022 = "30140022";
	// 卸货开始时间不能为空
	String CODE_30140023 = "30140023";
	// 卸货完成时间不能为空
	String CODE_30140024 = "30140024";
	// 滞期天数不能为空
	String CODE_30140025 = "30140025";
	// 卸货吨位不能为空
	String CODE_30140026 = "30140026";
	// 卸货量方吨位不能为空
	String CODE_30140027 = "30140027";

	// 导入相关
	// 上传的数据不能为空
	String CODE_30141001 = "30141001";
	// 上传数据量超出了限制
	String CODE_30141002 = "30141002";

	// 激活码相关
	// 等级不能为空
	String CODE_30142001 = "30142001";
	// 时长必须在1-10000之间
	String CODE_30142002 = "30142002";
	// 兑换日期只能选择今日之后的日期
	String CODE_30142003 = "30142003";
	// 生成数量必须大于0
	String CODE_30142004 = "30142004";
	// 关联了账号的激活码只能生成单条激活码
	String CODE_30142005 = "30142005";
	// 生成激活码数量不能超过1000条
	String CODE_30142006 = "30142006";
	// 兑换有效时长应为大于0小于100的整数
	String CODE_30142007 = "30142007";
	// 赠送时长应为大于0小于1000的整数
	String CODE_30142008 = "30142008";

	// 业务设置相关
	// 业务设置不存在
	String CODE_30143001 = "30143001";
	// 类型不能为空
	String CODE_30143002 = "30143002";
	// 内容不能为空
	String CODE_30143003 = "30143003";
	// 类型不存在
	String CODE_30143004 = "30143004";
	// 参数解析错误
	String CODE_30143005 = "30143005";
	// 推介人结佣金额比例不能为空
	String CODE_30143006 = "30143006";
	// 推介人分佣有效期不能为空
	String CODE_30143007 = "30143007";
	// 推介人结佣金额比例不能超过100
	String CODE_30143008 = "30143008";
	// 有效期最低不能少于一个月，不能超过120个月
	String CODE_30143009 = "30143009";

	// 会员等级相关
	// 会员等级id不能为空
	String CODE_30144001 = "30144001";
	// 会员等级不存在
	String CODE_30144002 = "30144002";
	// 包月费用不能为空
	String CODE_30144003 = "30144003";
	// 包年费用不能为空
	String CODE_30144004 = "30144004";
	// 会员权限不能为空
	String CODE_30144005 = "30144005";
	// 包月费用不能为空 取值范围 0.01~10000000000
	String CODE_30144006 = "30144006";
	// 包年费用不能为空 取值范围 0.01~10000000000
	String CODE_30144007 = "30144007";
	// 包年费用必须大于包月费用
	String CODE_30144008 = "30144008";
	// 包月费用必须大于上一个等级的包月费用
	String CODE_30144009 = "30144009";
	// 包年费用必须大于上一个等级的包年费用
	String CODE_30144010 = "30144010";
	// 包月费用必须小于下一个等级的包月费用
	String CODE_30144011 = "30144011";
	// 包年费用必须小于下一个等级的包年费用
	String CODE_30144012 = "30144012";
	// 注册送会员配置信息不存在
	String CODE_30144013 = "30144013";
	// 无法关闭，请检查注册送会员配置
	String CODE_30144014 = "30144014";
	// 无法关闭，请检查会员等级列表
	String CODE_30144015 = "30144015";
	// 无法开放，请检查会员等级列表
	String CODE_30144016 = "30144016";

	// 员工相关
	// 手机和邮箱不可同时为空
	String CODE_30145001 = "30145001";
	// 手机重复
	String CODE_30145002 = "30145002";
	// 员工主责部门只允许有一个
	String CODE_30145003 = "30145003";
	// 员工部门不能为空
	String CODE_30145004 = "30145004";
	// 组织不存在
	String CODE_30145005 = "30145005";
	// 邮箱重复
	String CODE_30145007 = "30145007";
	// 员工工号重复
	String CODE_30145008 = "30145008";
	// 员工姓名不能为空
	String CODE_30145009 = "30145009";
	// 员工工号不能为空
	String CODE_30145010 = "30145010";
	// 员工手机号不能为空
	String CODE_30145011 = "30145011";
	// 手机号格式不正确
	String CODE_30145012 = "30145012";
	// 邮箱格式不正确
	String CODE_30145013 = "30145013";
	// 员工姓名长度最大为64
	String CODE_30145014 = "30145014";
	// 员工工号长度最大为64
	String CODE_30145015 = "30145015";
	// 员工手机号长度最大为64
	String CODE_30145016 = "30145016";
	// 员工邮箱号长度最大为64
	String CODE_30145017 = "30145017";
	// 职位描述长度最大为128
	String CODE_30145018 = "30145018";
	// 员工部门信息不能为空
	String CODE_30145019 = "30145019";
	// 员工部门id不能为空
	String CODE_30145020 = "30145020";
	// 主责部门的值只能为0或1
	String CODE_30145021 = "30145021";
	// 员工不存在
	String CODE_30145022 = "30145022";

	// 推广码相关
	// 标题不能为空
	String CODE_30146001 = "30146001";
	// 标题长度必须在1-15个字符之间
	String CODE_30146002 = "30146002";
	// 关联账号信息不能为空
	String CODE_30146003 = "30146003";
	// 是否赠送会员不能为空
	String CODE_30146004 = "30146004";
	// 会员赠送时长必须在1-10000之间
	String CODE_30146005 = "30146005";
	// 会员兑换有效期必须是未来的日期
	String CODE_30146006 = "30146006";
	// 推广有效期不能为空
	String CODE_30146007 = "30146007";
	// 推广有效期必须是未来的日期
	String CODE_30146008 = "30146008";
	// 推广头图不能为空
	String CODE_30146009 = "30146009";
	// 页面不能为空
	String CODE_30146010 = "30146010";
	// 赠送会员为'是'时，会员等级不能为空
	String CODE_30146011 = "30146011";
	// 赠送会员为'是'时，赠送时长不能为空
	String CODE_30146012 = "30146012";
	// 赠送会员为'是'时，兑换有效期不能为空
	String CODE_30146013 = "30146013";
	// 推广已关闭，无法延期
	String CODE_30146014 = "30146014";
	// 延期时间只能是当前日期之后的时间
	String CODE_30146015 = "30146015";
	// 推广不存在
	String CODE_30146016 = "30146016";
	// 客户已绑定有效推广
	String CODE_30146017 = "30146017";

	// 角色相关
	// 角色名称重复
	String CODE_30147001 = "30147001";
	// 部门名称不能为空
	String CODE_30147002 = "30147002";
	// 权限列表不能为空
	String CODE_30147003 = "30147003";
	// 角色说明长度最大为100
	String CODE_30147004 = "30147004";
	// 角色名称长度最大为30
	String CODE_30147005 = "30147005";
	// 角色被使用不能删除
	String CODE_30147006 = "30147006";
	// 角色不存在
	String CODE_30147007 = "30147007";
	// 权限不存在
	String CODE_30147008 = "30147008";

	// 账号相关
	// 员工id不能为空
	String CODE_30148001 = "30148001";
	// 角色列表不能为空
	String CODE_30148002 = "30148002";
	// 状态不能为空
	String CODE_30148003 = "30148003";
	// 状态只有0或1
	String CODE_30148004 = "30148004";
	// 账号不存在
	String CODE_30148005 = "30148005";
	// 账号已存在
	String CODE_30148006 = "30148006";
	// 用户类型不能为空
	String CODE_30148007 = "30148007";
	// 员工姓名不能为空
	String CODE_30148008 = "30148008";
	// 员工号码不能为空
	String CODE_30148009 = "30148009";
	// 号码不规范
	String CODE_30148010 = "30148010";
	// 号码重复
	String CODE_30148011 = "30148011";
	// 手机号最多维护10个
	String CODE_30148012 = "30148012";
	// 姓名不能为空
	String CODE_30148013 = "30148013";
	// 姓名不能超过32个字符
	String CODE_30148014 = "30148014";
	// 手机号不能为空
	String CODE_30148015 = "30148015";
	// 手机号格式错误
	String CODE_30148016 = "30148016";
	// 该手机号已被使用
	String CODE_30148017 = "30148017";
	// 工号格式错误
	String CODE_30148018 = "30148018";
	// 该工号已存在
	String CODE_30148019 = "30148019";
	// 请先完成组织机构认证
	String CODE_30148020 = "30148020";

	// 额度变更相关
	// 额度变更不存在
	String CODE_30149001 = "30149001";
	// 项目ID不能为空
	String CODE_30149002 = "30149002";
	// 关联合同ID不能为空
	String CODE_30149003 = "30149003";
	// 变更方向不能为空
	String CODE_30149004 = "30149004";
	// 费用类型不能为空
	String CODE_30149005 = "30149005";
	// 申请金额不能为空
	String CODE_30149006 = "30149006";
	// 申请金额范围0-100亿数字，保留两位小数
	String CODE_30149007 = "30149007";
	// 申请日期不能为空
	String CODE_30149008 = "30149008";
	// 申请事由不能为空
	String CODE_30149009 = "30149009";
	// 申请事由字符长度最大为200
	String CODE_30149010 = "30149010";
	// 变更方向不存在
	String CODE_30149011 = "30149011";
	// 费用类型不存在
	String CODE_30149012 = "30149012";
	// 驳回原因不能为空
	String CODE_30149013 = "30149013";
	// 驳回原因字符长度最大为200
	String CODE_30149014 = "30149014";
	// 无权限
	String CODE_30149015 = "30149015";
	// 已驳回状态才能进行更新
	String CODE_30149016 = "30149016";
	// 已驳回状态才能进行删除
	String CODE_30149017 = "30149017";
	// 待确认状态才能进行驳回
	String CODE_30149018 = "30149018";
	// 待确认状态才能进行确认
	String CODE_30149019 = "30149019";
	// 关联项目类型不正确
	String CODE_30149020 = "30149020";
	// 变更后金额将为负数
	String CODE_30149021 = "30149021";

	// 变更类型不能为空
	String CODE_30149022 = "30149022";
	// 变更类型不存在
	String CODE_30149023 = "30149023";
    //履约保证金转货款时-申请金额不能大于当前合同的履约保证金
    String CODE_30149024 = "30149024";
    // 保证金转货款时-申请金额不能大于项目中当前预估可提货余额的值
    String CODE_30149025 = "30149025";
    // 当前履约保证金不足，无法操作
    String CODE_30149026 = "30149026";

	// 供应商相关
	// 企业名称不能为空
	String CODE_30150001 = "30150001";
	// 企业名称不超过32个字符
	String CODE_30150002 = "30150002";
	// 统一社会信用代码不能为空
	String CODE_30150003 = "30150003";
	// 法定代表人不能为空
	String CODE_30150004 = "30150004";
	// 法定代表人不超过32个字符
	String CODE_30150005 = "30150005";
	// 联系人不超过32个字符
	String CODE_30150006 = "30150006";
	// 开户名称不能为空
	String CODE_30150007 = "30150007";
	// 开户名称不超过32个字符
	String CODE_30150008 = "30150008";
	// 银行账户不能为空
	String CODE_30150009 = "30150009";
	// 银行账户不超过32个字符
	String CODE_30150010 = "30150010";
	// 开户行不能为空
	String CODE_30150011 = "30150011";
	// 开户行不超过32个字符
	String CODE_30150012 = "30150012";
	// 该企业已存在
	String CODE_30150013 = "30150013";
	// 银行账号不能超过10个
	String CODE_30150014 = "30150014";
	// 默认银行账号不能超过1个
	String CODE_30150015 = "30150015";
	// 供应商不存在
	String CODE_30150016 = "30150016";
	// 账号不能为空
	String CODE_30150017 = "30150017";
	// 密码不能为空
	String CODE_30150018 = "30150018";
	// 请设置6-16位包含字母、数字的密码
	String CODE_30150019 = "30150019";
	// 当前账号已被使用
	String CODE_30150020 = "30150020";
	// 供应商信息不存在,请先配置供应商信息
	String CODE_30150021 = "30150021";
	// 当前供应链有业务数据，不支持变更身份
	String CODE_30150022 = "30150022";

	// 合同相关
	// 合同不存在
	String CODE_30151001 = "30151001";
	// 关联项目id不能为空
	String CODE_30151002 = "30151002";
	// 合同名称不能为空，且不超过32个字符
	String CODE_30151003 = "30151003";
	// 合同名称不可重复
	String CODE_30151004 = "30151004";
	// 签署方式不能为空
	String CODE_30151005 = "30151005";
	// 签署方式不存在
	String CODE_30151006 = "30151006";
	// 签订日期不能为空
	String CODE_30151007 = "30151007";
	// 起止日期不能为空
	String CODE_30151008 = "30151008";
	// 外部合同编号不能超过32个字符
	String CODE_30151009 = "30151009";
	// 最低预付款额度 取值范围 0-100亿 保留两位小数
	String CODE_30151010 = "30151010";
	// 补充预付款额度 取值范围 0-100亿 保留两位小数
	String CODE_30151011 = "30151011";
	// 保证金与货款为大于等于0小于100亿的数字，保留两位小数
	String CODE_30151012 = "30151012";
	// 合同文件不能为空
	String CODE_30151013 = "30151013";
	// 付款方式不存在
	String CODE_30151014 = "30151014";
	// 备注不能超过200个字符
	String CODE_30151015 = "30151015";
	// 该状态无法提交
	String CODE_30151016 = "30151016";
	// 该状态无法发起签署
	String CODE_30151017 = "30151017";
	// 该状态无法修改
	String CODE_30151018 = "30151018";
	// 该状态无法驳回
	String CODE_30151019 = "30151019";
	// 线上才能进行签署
	String CODE_30151020 = "30151020";
	// 合同类型不存在
	String CODE_30151021 = "30151021";
	// 驳回原因不能为空，且不超过200个字符
	String CODE_30151022 = "30151022";
	// 该状态无法删除
	String CODE_30151023 = "30151023";
	// 签署方式为线下并且待确认状态才能进行确认
	String CODE_30151024 = "30151024";
	// 保存方式错误
	String CODE_30151025 = "30151025";
	// 线上签署只有保存为草稿
	String CODE_30151026 = "30151026";
	// 该合同已被关联，不支持删除
	String CODE_30151027 = "30151027";
	// 无权限
	String CODE_30151028 = "30151028";
	// 合同类型不能为空
	String CODE_30151029 = "30151029";
	// 结算方式不能为空
	String CODE_30151030 = "30151030";
	// 结算方式不存在
	String CODE_30151031 = "30151031";
	// 账期不能为空
	String CODE_30151032 = "30151032";
	// 账期为大于等于0小于等于1000的整数
	String CODE_30151033 = "30151033";
	// 账期起始方式不能为空
	String CODE_30151034 = "30151034";
	// 账期起始方式不存在
	String CODE_30151035 = "30151035";
	// 自定义合同编号只能包含字母、数字、符号
	String CODE_30151036 = "30151036";
	// 该状态无法撤回
	String CODE_30151037 = "30151037";
	// 银行账户上限为5个
	String CODE_30151038 = "30151038";
	// 采购方id不能为空
	String CODE_30151039 = "30151039";
	// 销售方id不能为空
	String CODE_30151040 = "30151040";
	// 至少选择两个签约主体
	String CODE_30151041 = "30151041";
	// 借款方id不能为空
	String CODE_30151042 = "30151042";
	// 资金方id不能为空
	String CODE_30151043 = "30151043";
	// 融资产品不能为空
	String CODE_30151044 = "30151044";
	// 融资产品不存在
	String CODE_30151045 = "30151045";
	// 借款额度不能为空
	String CODE_30151046 = "30151046";
	// 借款额度为大于0小于100亿的数字，保留两位小数
	String CODE_30151047 = "30151047";
	// 额度有效期起止日期不能为空
	String CODE_30151048 = "30151048";
	// 借款利率计算方式不能为空
	String CODE_30151049 = "30151049";
	// 借款利率计算方式不存在
	String CODE_30151050 = "30151050";
	// 借款利率点差不能为空
	String CODE_30151051 = "30151051";
	// 借款利率点差为大于0小于100的数字，保留两位小数
	String CODE_30151052 = "30151052";
	// 利率调整周期不能为空
	String CODE_30151053 = "30151053";
	// 利率调整周期为大于0小于100的整数
	String CODE_30151054 = "30151054";
	// 结息周期不能为空
	String CODE_30151055 = "30151055";
	// 结息周期不存在
	String CODE_30151056 = "30151056";
	// 结息日不能为空
	String CODE_30151057 = "30151057";
	// 结息日不存在
	String CODE_30151058 = "30151058";
	// 滚动货款为大于0小于100亿的数字，保留两位小数
	String CODE_30151059 = "30151059";
	// 滚动货款补足金额为大于0小于100亿的数字，保留两位小数
	String CODE_30151060 = "30151060";
	// 该合同已被关联，不支持修改
	String CODE_30151061 = "30151061";
	// 存货方id不能为空
	String CODE_30151062 = "30151062";
	// 仓库id不能为空
	String CODE_30151063 = "30151063";
	// 免费存储天数应为 大于0小于1000的整数
	String CODE_30151064 = "30151064";
	// 收费标准应为 大于0小于1000的数字，保留两位小数
	String CODE_30151065 = "30151065";
	// 备注不能超过32个字符
	String CODE_30151066 = "30151066";
	// 该存货方存在有效合同
	String CODE_30151067 = "30151067";
	// 该用户没有供应链id
	String CODE_30151068 = "30151068";
	// 用户的供应链id在供应链表中没有找到记录
	String CODE_30151069 = "30151069";


	// 项目相关
	// 项目名称不能为空
	String CODE_30152001 = "30152001";
	// 项目名称不能超过32个字符
	String CODE_30152002 = "30152002";
	// 项目介绍不能为空
	String CODE_30152003 = "30152003";
	// 项目介绍不能超过200个字符
	String CODE_30152004 = "30152004";
	// 上游供应商id不能为空
	String CODE_30152005 = "30152005";
	// 下游采购方id不能为空
	String CODE_30152006 = "30152006";
	// 项目负责人id不能为空
	String CODE_30152007 = "30152007";
	// 项目负责人名称不能为空
	String CODE_30152008 = "30152008";
	// 项目经理id不能为空
	String CODE_30152009 = "30152009";
	// 申请完结后才能点确认完结
	String CODE_30152010 = "30152010";
	// 该项目存在逾期的应收款，不允许申请完结
	String CODE_30152011 = "30152011";
	// 项目名称不支持重名
	String CODE_30152012 = "30152012";
	// 项目不存在
	String CODE_30152013 = "30152013";
	// 项目经理上限为10个
	String CODE_30152014 = "30152014";
	// 该货物名称已被合同关联
	String CODE_30152015 = "30152015";
	// 该项目已被合同关联
	String CODE_30152016 = "30152016";
	// 该下游采购方已被合同关联
	String CODE_30152017 = "30152017";
	// 上游供应商不可删除原有选项
	String CODE_30152018 = "30152018";
	// 下游采购方不可删除原有选项
	String CODE_30152019 = "30152019";
	// 当当前操作用户不是该项目指派人员,不可操作
	String CODE_30152020 = "30152020";
	// 货物id不能为空
	String CODE_30152021 = "30152021";
	// 货物名称不能为空
	String CODE_30152022 = "30152022";
	// 该项目存在未完成的单据，不允许申请完结
	String CODE_30152023 = "30152023";
	// 上游供应商的上限为10个
	String CODE_30152024 = "30152024";
	// 下游采购方的上限为10个
	String CODE_30152025 = "30152025";
	// 项目最高限额为大于0小于100亿的数字，保留两位小数
	String CODE_30152026 = "30152026";
	// 项目预计年化收益为大于0小于100的数字，保留两位小数
	String CODE_30152027 = "30152027";
	// 库存控货比为大于0小于1000的数字，保留两位小数
	String CODE_30152028 = "30152028";
	// 付款比例为大于0小于100的数字，保留两位小数
	String CODE_30152029 = "30152029";
	// 余款账期为大于0小于1000的整数
	String CODE_30152030 = "30152030";
	// 是否存在仓储不能为空
	String CODE_30152031 = "30152031";
	// 预付货款为大于0小于100亿的数字，保留两位小数
	String CODE_30152032 = "30152032";
	// 预付货款比例为大于0小于100的数字，保留两位小数
	String CODE_30152033 = "30152033";
	// 账期为大于0小于1000的整数
	String CODE_30152034 = "30152034";
	// 库存金额为大于0小于1亿的数字，保留两位小数
	String CODE_30152035 = "30152035";
	// 存货方的上限为100个
	String CODE_30152036 = "30152036";
	// 存货方不可删除原有选项
	String CODE_30152037 = "30152037";
	// 增减项不能为空
	String CODE_30152038 = "30152038";
	// 加减方向不能为空
	String CODE_30152039 = "30152039";
	// 可提货余额的自定义行的 金额应为 大于0小于1亿的数字，保留两位小数
	String CODE_30152040 = "30152040";
	// 可提货余额的自定义行的 金额不能为空
	String CODE_30152041 = "30152041";
	// 该项目的可提货余额公式项不存在
	String CODE_30152042 = "30152042";
	// 上游供应商是否自录数据不能为空
	String CODE_30152043 = "30152043";
	// 下游采购方是否自录数据不能为空
	String CODE_30152044 = "30152044";
	// 监管单位不能超过32个字符
	String CODE_30152045 = "30152045";
	// 上下游企业重复选择
	String CODE_30152046 = "30152046";
	// 请先完成组织机构认证
	String CODE_30152047 = "30152047";
	// 该供应链不存在
	String CODE_30152048 = "30152048";

	// 收付款相关
	// 付款信息不存在
	String CODE_30153001 = "30153001";
	// 收款信息不存在
	String CODE_30153002 = "30153002";
	// 关联项目id不能为空
	String CODE_30153003 = "30153003";
	// 关联合同id不能为空
	String CODE_30153004 = "30153004";
	// 费用类型不能为空
	String CODE_30153005 = "30153005";
	// 费用类型不存在
	String CODE_30153006 = "30153006";
	// 金额不能为空
	String CODE_30153007 = "30153007";
	// 金额 取值范围 0-100亿 保留两位小数
	String CODE_30153008 = "30153008";
	// 付款日期不能为空
	String CODE_30153009 = "30153009";
	// 付款方式不能为空
	String CODE_30153010 = "30153010";
	// 付款方式不存在
	String CODE_30153011 = "30153011";
	// 凭证不能为空
	String CODE_30153012 = "30153012";
	// 备注不能超过200个字符
	String CODE_30153013 = "30153013";
	// 驳回原因不能为空，且不超过200个字符
	String CODE_30153014 = "30153014";
	// 该状态无法修改
	String CODE_30153015 = "30153015";
	// 该状态无法删除
	String CODE_30153016 = "30153016";
	// 该状态无法确认
	String CODE_30153017 = "30153017";
	// 该状态无法驳回
	String CODE_30153018 = "30153018";
	// 类型不能为空
	String CODE_30153019 = "30153019";
	// 类型不存在
	String CODE_30153020 = "30153020";
	// 采购方id不能为空
	String CODE_30153021 = "30153021";
	// 销售方id不能为空
	String CODE_30153022 = "30153022";
	// 付款方银行账户id不能为空
	String CODE_30153023 = "30153023";
	// 付款方开户名称不能为空
	String CODE_30153024 = "30153024";
	// 付款方银行账号不能为空
	String CODE_30153025 = "30153025";
	// 付款方开户行不能为空
	String CODE_30153026 = "30153026";
	// 收款方银行账户id不能为空
	String CODE_30153027 = "30153027";
	// 收款方开户名称不能为空
	String CODE_30153028 = "30153028";
	// 收款方银行账号不能为空
	String CODE_30153029 = "30153029";
	// 收款方开户行不能为空
	String CODE_30153030 = "30153030";
	// 票据号码不超过30位数字
	String CODE_30153031 = "30153031";
	// 承兑人不超过25字符
	String CODE_30153032 = "30153032";
	// 信用证号码不超过16字符
	String CODE_30153033 = "30153033";
	// 开证行不超过25字符
	String CODE_30153034 = "30153034";
	// 收款方开户名称不超过25字符
	String CODE_30153035 = "30153035";
	// 收款方银行账户不超过25个数字
	String CODE_30153036 = "30153036";
	// 收款方开户行不超过25字符
	String CODE_30153037 = "30153037";
	// 关联订单id不能为空
	String CODE_30153038 = "30153038";
	// 关联应付款id不能为空
	String CODE_30153039 = "30153039";
	// 关联对账单id不能为空
	String CODE_30153040 = "30153040";
	// 金额不能大于关联项目的项目金额
	String CODE_30153041 = "30153041";
	// 关联合同、关联订单不可都为空
	String CODE_30153042 = "30153042";
	// 关联借据id不能为空
	String CODE_30153043 = "30153043";
	// 资金方信息不能为空
	String CODE_30153044 = "30153044";
    // 付款中的金额不能大于所选的应付款单列表中未付金额之和
    String CODE_30153045 = "30153045";
    // 该付款已被关联
    String CODE_30153046 = "30153046";


	// 对账相关
	// 关联项目id不能为空
	String CODE_30154001 = "30154001";
	// 关联合同id不能为空
	String CODE_30154002 = "30154002";
	// 签署方式不能为空
	String CODE_30154003 = "30154003";
	// 项目在交付状态才可以生成对账单
	String CODE_30154004 = "30154004";
	// 实际对账金额不能为空
	String CODE_30154005 = "30154005";
	// 对账日期不能为空
	String CODE_30154006 = "30154006";
	// 备注长度不能超过200
	String CODE_30154007 = "30154007";
	// 对账单不存在
	String CODE_30154008 = "30154008";
	// 当前状态不能进行删除操作
	String CODE_30154009 = "30154009";
	// 当前状态不能进行修改操作
	String CODE_30154010 = "30154010";
	// 当前状态不能进行驳回操作
	String CODE_30154011 = "30154011";
	// 只有待签署状态可以签署
	String CODE_30154012 = "30154012";
	// 签署方式为线上并且状态草稿状态才能进行发起签署
	String CODE_30154013 = "30154013";
	// 对账id不能为空
	String CODE_30154014 = "30154014";
	// 驳回原因不能为空
	String CODE_30154015 = "30154015";
	// 该对账已开票，不允许删除
	String CODE_30154016 = "30154016";
	// 签署方式为线下 才能进行确认
	String CODE_30154017 = "30154017";
	// 对账重量不能为空
	String CODE_30154018 = "30154018";
	// 关联合同为先货后款时，保证金是否转货款 为必填
	String CODE_30154019 = "30154019";
	// 关联合同为先货后款时，对账金额需大于订单保证金
	String CODE_30154020 = "30154020";
	// 签署方式为线下并且草稿状态时 pc端才能提交
	String CODE_30154021 = "30154021";
	// 草稿状态时 管理后台才能提交
	String CODE_30154022 = "30154022";
	// 该状态无法撤回
	String CODE_30154023 = "30154023";
	// 调用获取工作日接口异常
	String CODE_30154024 = "30154024";
	// 请上传单据后提交
	String CODE_30154025 = "30154025";
	// 关联合同为先货后款时，预对账金额需大于订单保证金
	String CODE_30154026 = "30154026";
	// 预对账日期不能为空
	String CODE_30154027 = "30154027";
	// 对账单据文件不能为空
	String CODE_30154028 = "30154028";
	// 是否存在预对账 不能为空
	String CODE_30154029 = "30154029";
	// 账期不能为空
	String CODE_30154030 = "30154030";
	// 存在预对账时 结算预付款不能为空
	String CODE_30154031 = "30154031";
	// 存在预对账时 预结算方式不能为空
	String CODE_30154032 = "30154032";
	// 预付比例 应大于等于0小于100
	String CODE_30154033 = "30154033";
	// 预付金额 应大于等于0小于预对账金额
	String CODE_30154034 = "30154034";

	// 开票相关
	// 开票单不存在
	String CODE_30155001 = "30155001";
	// 项目id不能为空
	String CODE_30155002 = "30155002";
	// 合同id不能为空
	String CODE_30155003 = "30155003";
	// 关联的对账单不能为空
	String CODE_30155004 = "30155004";
	// 类型不能为空
	String CODE_30155005 = "30155005";
	// 单据id不能为空
	String CODE_30155006 = "30155006";
	// 开票日期不能为空
	String CODE_30155007 = "30155007";
	// 类型错误
	String CODE_30155008 = "30155008";
	// 税率取值范围 0-100
	String CODE_30155009 = "30155009";
	// 备注不能超过200个字符
	String CODE_30155010 = "30155010";
	// 提交类型不能为空
	String CODE_30155011 = "30155011";
	// 填写的开票金额超过未开票金额
	String CODE_30155012 = "30155012";
	// 驳回原因不能为空
	String CODE_30155013 = "30155013";
	// 驳回原因不能超过200个字符
	String CODE_30155014 = "30155014";
	// 开票金额不能为空
	String CODE_30155015 = "30155015";
	// 销售方不是注册企业，不能发起作废
	String CODE_30155016 = "30155016";

	// 货物相关
	// 货物不存在
	String CODE_30156001 = "30156001";
	// 货物名称不能为空
	String CODE_30156002 = "30156002";
	// 货物名称不能超过10个字符
	String CODE_30156003 = "30156003";
	// 货物名称已存在
	String CODE_30156004 = "30156004";
	// 该货物已被使用,不能删除或修改
	String CODE_30156005 = "30156005";
	// 单位分类不能为空
	String CODE_30156006 = "30156006";
	// 单位分类错误
	String CODE_30156007 = "30156007";
	// 单位不能为空
	String CODE_30156008 = "30156008";
	// 单位不能超过5个字符
	String CODE_30156009 = "30156009";

	// 退款相关
	// 退款信息不存在
	String CODE_30157001 = "30157001";
	// 关联项目id不能为空
	String CODE_30157002 = "30157002";
	// 关联收款id不能为空
	String CODE_30157003 = "30157003";
	// 退款日期不能为空
	String CODE_30157004 = "30157004";
	// 退款方式不能为空
	String CODE_30157005 = "30157005";
	// 退款方式不存在
	String CODE_30157006 = "30157006";
	// 凭证不能为空
	String CODE_30157007 = "30157007";
	// 退款原因不能为空
	String CODE_30157008 = "30157008";
	// 退款原因不能超过200个字符
	String CODE_30157009 = "30157009";
	// 该状态无法修改
	String CODE_30157010 = "30157010";
	// 该状态无法删除
	String CODE_30157011 = "30157011";
	// 该状态无法确认
	String CODE_30157012 = "30157012";
	// 该状态无法驳回
	String CODE_30157013 = "30157013";
	// 驳回原因不能为空
	String CODE_30157014 = "30157014";
	// 驳回原因不能超过200个字符
	String CODE_30157015 = "30157015";
	// 票据号码不超过30位数字
	String CODE_30157016 = "30157016";
	// 承兑人不超过25字符
	String CODE_30157017 = "30157017";
	// 信用证号码不超过16字符
	String CODE_30157018 = "30157018";
	// 开证行不超过25字符
	String CODE_30157019 = "30157019";
	// 付款方开户名称不能为空
	String CODE_30157020 = "30157020";
	// 付款方银行账号不能为空
	String CODE_30157021 = "30157021";
	// 付款方开户行不能为空
	String CODE_30157022 = "30157022";
	// 收款方开户名称不能为空
	String CODE_30157023 = "30157023";
	// 收款方银行账号不能为空
	String CODE_30157024 = "30157024";
	// 收款方开户行不能为空
	String CODE_30157025 = "30157025";
	// 该付款单已完成退款
	String CODE_30157026 = "30157026";
	// 关联合同id不能为空
	String CODE_30157027 = "30157027";
	// 无权限
	String CODE_30157028 = "30157028";

	// 服务费相关
	// 服务费信息不存在
	String CODE_30159001 = "30159001";
	// 关联合同id不能为空
	String CODE_30159002 = "30159002";
	// 金额不能为空
	String CODE_30159003 = "30159003";
	// 金额为大于0小于100亿的数字，保留两位小数
	String CODE_30159004 = "30159004";
	// 服务月份不能为空
	String CODE_30159005 = "30159005";
	// 付款日期不能为空
	String CODE_30159006 = "30159006";
	// 付款方式不能为空
	String CODE_30159007 = "30159007";
	// 付款方式不存在
	String CODE_30159008 = "30159008";
	// 备注不能超过200个字符
	String CODE_30159009 = "30159009";
	// 驳回原因不能为空
	String CODE_30159010 = "30159010";
	// 驳回原因不超过200个字符
	String CODE_30159011 = "30159011";
	// 付款方银行账户id不能为空
	String CODE_30159012 = "30159012";
	// 收款方银行账户id不能为空
	String CODE_30159013 = "30159013";
	// 该状态无法修改
	String CODE_30159014 = "30159014";
	// 该状态无法删除
	String CODE_30159015 = "30159015";
	// 该状态无法确认
	String CODE_30159016 = "30159016";
	// 该状态无法驳回
	String CODE_30159017 = "30159017";
    // 费用类型只能是1.业务服务费 2.监管服务费
    String CODE_30159018 = "30159018";
    // 费用类型不能为空
    String CODE_30159019 = "30159019";

	// 汽运单相关
	// 汽运单不存在
	String CODE_30158001 = "30158001";
	// 地址不能为空
	String CODE_30158002 = "30158002";
	// 装货地不能为空
	String CODE_30158003 = "30158003";
	// 发货单id不能为空
	String CODE_30158004 = "30158004";
	// 订单id不能为空
	String CODE_30158005 = "30158005";
	// 司机名称不能为空
	String CODE_30158006 = "30158006";
	// 司机手机号不能为空
	String CODE_30158007 = "30158007";
	// 司机车牌号不能为空
	String CODE_30158008 = "30158008";
	// 车型不能为空
	String CODE_30158009 = "30158009";
	// 车长不能为空
	String CODE_30158010 = "30158010";
	// 运费类型不能为空
	String CODE_30158011 = "30158011";
	// 运费类型错误
	String CODE_30158012 = "30158012";
	// 里程不能为空
	String CODE_30158013 = "30158013";
	// 预估单价不能为空
	String CODE_30158014 = "30158014";
	// 运费单价类型不能为空
	String CODE_30158015 = "30158015";
	// 期望卸货时间不能为空
	String CODE_30158016 = "30158016";
	// 期望装货时间不能为空
	String CODE_30158017 = "30158017";
	// 货物名称不能为空
	String CODE_30158018 = "30158018";
	// 规格/型号不能为空
	String CODE_30158019 = "30158019";
	// 联系人不能为空
	String CODE_30158020 = "30158020";
	// 手机号不能为空
	String CODE_30158021 = "30158021";
	// 手机号格式错误
	String CODE_30158022 = "30158022";
	// 备注不能超过200个字符
	String CODE_30158023 = "30158023";
	// 货主id不能为空
	String CODE_30158024 = "30158024";
	// 货主名称不能为空
	String CODE_30158025 = "30158025";
	// 预估运费支出不能为空
	String CODE_30158026 = "30158026";
	// 运输重量不能为空
	String CODE_30158027 = "30158027";
	// 运输体积不能为空
	String CODE_30158028 = "30158028";
	// 运输件数不能为空
	String CODE_30158029 = "30158029";
	// 发车时间不能为空
	String CODE_30158030 = "30158030";
	// 发车地址不能为空
	String CODE_30158031 = "30158031";
	// 发车车头照片不能为空
	String CODE_30158032 = "30158032";
	// 只有待发车状态下才能发车
	String CODE_30158033 = "30158033";
	// 上报地址不能为空
	String CODE_30158034 = "30158034";
	// 异常原因不能超过200个字符
	String CODE_30158035 = "30158035";
	// 卸货时间不能为空
	String CODE_30158036 = "30158036";
	// 卸货地址不能为空
	String CODE_30158037 = "30158037";
	// 卸货重量不能为空
	String CODE_30158038 = "30158038";
	// 结算单据照片文件id不能为空
	String CODE_30158039 = "30158039";
	// 已发车状态才能卸货
	String CODE_30158040 = "30158040";
	// 已取消状态下不能异常上报
	String CODE_30158041 = "30158041";
	// 待发车和已发车状态下才能修改
	String CODE_30158042 = "30158042";
	// 卸货地不能为空
	String CODE_30158043 = "30158043";
	// 车型错误
	String CODE_30158044 = "30158044";
	// 车长错误
	String CODE_30158045 = "30158045";
	// 途经点最多只能有8个
	String CODE_30158046 = "30158046";
	// 发货完成的发货单不能新增和删除汽运单
	String CODE_30158047 = "30158047";
	// 发货已取消，不能进行操作
	String CODE_30158048 = "30158048";
	// 运单车辆轨迹公里数异常
	String CODE_30158049 = "30158049";
	// 附件数量不能超过10个
	String CODE_30158050 = "30158050";
	// 卸货时间必须晚于装货时间
	String CODE_30158051 = "30158051";
	// 发车信息不能为空
	String CODE_30158052 = "30158052";
	// 卸货信息不能为空
	String CODE_30158053 = "30158053";
    // 单位不能为空
    String CODE_30158054 = "30158054";
    // 车牌类型不能为空
    String CODE_30158055 = "30158055";
	// 无法查询对应的车辆
	String CODE_30158056 = "30158056";
    // 里程不能为空
    String CODE_30158057 = "30158057";
    // 已卸货重量不能为空
    String CODE_30158058 = "30158058";
    // 时间不能晚于当前时间
    String CODE_30158059 = "30158059";

	// 天地图
	// 关键字不能为空
	String CODE_30160001 = "30160001";
	// 行政区域国标码不能为空
	String CODE_30160002 = "30160002";
	// 服务查询类型参数
	String CODE_30160003 = "30160003";
	// 返回结果起始位不能为空
	String CODE_30160004 = "30160004";
	// 返回结果数量不能为空
	String CODE_30160005 = "30160005";

	// LPR利率相关
	// 发布日期不能为空
	String CODE_30161001 = "30161001";
	// 发布日期不能超过当前日期
	String CODE_30161002 = "30161002";
	// 利率类型不能为空
	String CODE_30161003 = "30161003";
	// 利率不存在
	String CODE_30161004 = "30161004";
	// 利率值不能为空
	String CODE_30161005 = "30161005";
	// 利率值必须是大于0小于100的数字，保留两位小数
	String CODE_30161006 = "30161006";
	// 当月LPR利率已发布
	String CODE_30161007 = "30161007";
	// LPR利率不存在
	String CODE_30161008 = "30161008";

	// 铁路单相关
	// 铁路单不存在
	String CODE_30162001 = "30162001";
	// 发货单id不能为空
	String CODE_30162003 = "30162003";
	// 订单id不能为空
	String CODE_30162004 = "30162004";
	// 货物名称不能为空
	String CODE_30162005 = "30162005";
	// 规格/型号不能为空
	String CODE_30162006 = "30162006";
	// 运输重量不能为空
	String CODE_30162007 = "30162007";
	// 车种车号不能为空
	String CODE_30162008 = "30162008";
	// 车种车号不超过15字符
	String CODE_30162009 = "30162009";
	// 发站不能为空
	String CODE_30162010 = "30162010";
	// 发站不超过32字符
	String CODE_30162011 = "30162011";
	// 到站不能为空
	String CODE_30162012 = "30162012";
	// 到站不超过32字符
	String CODE_30162013 = "30162013";
	// 发车时间不能为空
	String CODE_30162014 = "30162014";
	// 到达时间不能为空
	String CODE_30162015 = "30162015";
	// 附件数量不能超过10个
	String CODE_30162016 = "30162016";
	// 到达时间必须晚于发车时间
	String CODE_30162017 = "30162017";
	// 铁路单已取消状态不能修改
	String CODE_30162018 = "30162018";
	// 铁路单id不能为空
	String CODE_30162019 = "30162019";
	// 发货完成不能新增铁路单
	String CODE_30162020 = "30162020";
	// 只有发货中状态才能删除铁路单
	String CODE_30162021 = "30162021";

	// 仓库相关
	// 仓库不存在
	String CODE_30163001 = "30163001";
	// 仓库名称不能为空
	String CODE_30163002 = "30163002";
	// 仓库名称不能超过32个字符
	String CODE_30163003 = "30163003";
	// 所属主体不能为空
	String CODE_30163004 = "30163004";
	// 所属主体不能超过32个字符
	String CODE_30163005 = "30163005";
	// 仓库类型不能为空
	String CODE_30163006 = "30163006";
	// 仓库类型错误
	String CODE_30163007 = "30163007";
	// 仓库地址不能超过32个字符
	String CODE_30163008 = "30163008";
	// 负责人不能超过32个字符
	String CODE_30163009 = "30163009";
	// 联系电话格式错误
	String CODE_30163010 = "30163010";
	// 仓库名称不能重复
	String CODE_30163011 = "30163011";
	// 该仓库已被使用
	String CODE_30163012 = "30163012";
	// 该库位已关联设备，请先解除关联
	String CODE_30163013 = "30163013";
	// 该仓库已关联设备，请先解除关联
	String CODE_30163014 = "30163014";


	// 库位相关
	// 库位不存在
	String CODE_30164001 = "30164001";
	// 库位名称不能为空
	String CODE_30164002 = "30164002";
	// 库位名称不能超过32个字符
	String CODE_30164003 = "30164003";
	// 库位地址不能为空
	String CODE_30164004 = "30164004";
	// 库位地址不能超过32个字符
	String CODE_30164005 = "30164005";
	// 负责人不能超过32个字符
	String CODE_30164006 = "30164006";
	// 联系电话格式错误
	String CODE_30164007 = "30164007";
	// 库位名称不能重复
	String CODE_30164008 = "30164008";

	// 质押相关
	// 质押单不存在
	String CODE_30165001 = "30165001";
	// 项目id不能为空
	String CODE_30165002 = "30165002";
	// 合同id不能为空
	String CODE_30165003 = "30165003";
	// 出质人id不能为空
	String CODE_30165004 = "30165004";
	// 质权人id不能为空
	String CODE_30165005 = "30165005";
	// 质押信息不能为空
	String CODE_30165006 = "30165006";
	// 质押数量不能为空
	String CODE_30165007 = "30165007";
	// 生效日期不能为空
	String CODE_30165008 = "30165008";
	// 附件不能为空
	String CODE_30165009 = "30165009";
	// 备注不能超过32个字符
	String CODE_30165010 = "30165010";
	// 该合同已存在质押单
	String CODE_30165011 = "30165011";


	// 仓储期初相关
	// 仓储期初单不存在
	String CODE_30166001 = "30166001";
	// 关联项目不能为空
	String CODE_30166002 = "30166002";
	// 货物名称不能为空
	String CODE_30166003 = "30166003";
	// 请选择合同
	String CODE_30166004 = "30166004";
	// 关联合同不能为空
	String CODE_30166005 = "30166005";
	// 仓库/库位不能为空
	String CODE_30166006 = "30166006";
	// 规格/型号不能为空
	String CODE_30166007 = "30166007";
	// 数量/重量不能为空
	String CODE_30166008 = "30166008";
	// 库位不存在
	String CODE_30166009 = "30166009";
	// 请不要选择重复的合同
	String CODE_30166010 = "30166010";
	// 一个合同下不能选择重复的仓库
	String CODE_30166011 = "30166011";
	// 一个仓库不能选择重复的规格
	String CODE_30166012 = "30166012";
	// 单位不能为空
	String CODE_30166013 = "30166013";
	// 库存数量/重量小于0
	String CODE_30166014 = "30166014";

	// 融资相关
	// 融资产品不能为空
	String CODE_30170001 = "30170001";
	// 借款额度不能为空
	String CODE_30170002 = "30170002";
	// 借款额度应为大于0小于100亿的数字，保留两位小数
	String CODE_30170003 = "30170003";
	// 借款利率应为大于0小于100的数字,保留两位小数
	String CODE_30170004 = "30170004";
	// 借款类型不能为空
	String CODE_30170005 = "30170005";
	// 结息周期不能为空
	String CODE_30170006 = "30170006";
	// 结息日不能为空
	String CODE_30170007 = "30170007";
	// 额度有效期开始时间不能为空
	String CODE_30170008 = "30170008";
	// 额度有效期结束时间不能为空
	String CODE_30170009 = "30170009";
	// 放款日期不能为空
	String CODE_30170010 = "30170010";
	// 受托收款账户不能为空
	String CODE_30170011 = "30170011";
	// 融资信息不存在
	String CODE_30170012 = "30170012";
	// 结息周期选择按月时，结息日应为大于0小于29的整数
	String CODE_30170013 = "30170013";
	// 结息周期选择按季、按年时，结息日应为大于0小于31的整数。
	String CODE_30170014 = "30170014";
	// 当前单据已确认
	String CODE_30170015 = "30170015";
	// 银行端未确认。当前单据不能确认
	String CODE_30170016 = "30170016";
	// 银行端已确认，单据不能修改
	String CODE_30170017 = "30170017";
	// 利率调整周期不能为空
	String CODE_30170018 = "30170018";
	// 利率调整周期应为大于0小于100的整数
	String CODE_30170019 = "30170019";

	// 还款相关
	// 还款id不能为空
	String CODE_30171001 = "30171001";
	// 驳回原因不能为空
	String CODE_30171002 = "30171002";
	// 还款单不存在
	String CODE_30171003 = "30171003";
	// 扣款日期不能为空
	String CODE_30171004 = "30171004";
	// 滚动货款不足
	String CODE_30171005 = "30171005";
	// 输入的本金应为 大于0小于100亿的数字，保留两位小数
	String CODE_30171006 = "30171006";
	// 输入的本金不能大于贷款剩余本金
	String CODE_30171007 = "30171007";
	// 传入的还款本金有误
	String CODE_30171008 = "30171008";
	// 传入的还款利息有误
	String CODE_30171009 = "30171009";
	// 传入的还款合计有误
	String CODE_30171010 = "30171010";
	// 传入的预计释放额度有误
	String CODE_30171011 = "30171011";
	// 还款明细不存在
	String CODE_30171012 = "30171012";
	// 关联合同不存在
	String CODE_30171013 = "30171013";
	// 实际扣款日期只能选择不早于申请日期，不晚于当天。
	String CODE_30171014 = "30171014";
	// 借据不存在
	String CODE_30171015 = "30171015";
	// 存在借据还款总金额大于贷款本金
	String CODE_30171016 = "30171016";

	// 项目期初相关
	// 请填写期初数据
	String CODE_30172001 = "30172001";
	// 项目期初信息不存在
	String CODE_30172002 = "30172002";
	// 项目期初明细不存在
	String CODE_30172003 = "30172003";
	// 输入的货款收入/支出总金额应为 大于0小于100亿的数字，保留两位小数
	String CODE_30172004 = "30172004";
	// 输入的对账总金额应为 大于0小于100亿的数字，保留两位小数
	String CODE_30172005 = "30172005";
	// 输入的对账总数量/重量应为 大于0小于100亿的数字
	String CODE_30172006 = "30172006";
	// 输入的开票总金额 应为 大于0小于100亿的数字，保留两位小数
	String CODE_30172007 = "30172007";
	// 每个项目只能新增一次期初
	String CODE_30172008 = "30172008";
	// 请不要选择重复的合同
	String CODE_30172009 = "30172009";
	// 至少需要选择一个合同
	String CODE_30172010 = "30172010";

	// 盘点相关
	// 关联项目id不能为空
	String CODE_30173001 = "30173001";
	// 关联合同id不能为空
	String CODE_30173002 = "30173002";
	// 签署方式不能为空
	String CODE_30173003 = "30173003";
	// 盘点类型不能为空
	String CODE_30173004 = "30173004";
	// 盘点时间不能为空
	String CODE_30173005 = "30173005";
	// 备注长度不能超过200
	String CODE_30173006 = "30173006";
	// 是否显示盈亏不能为空
	String CODE_30173007 = "30173007";
	// 仓库id不能为空
	String CODE_30173008 = "30173008";
	// 输入的盘点数量/重量应为 大于0小于1000万的数字，保留实际小数位，不超过2位
	String CODE_30173009 = "30173009";
	// 盘点数量/重量 不能为空
	String CODE_30173010 = "30173010";
	// 输入的盘重应为 大于0小于1000万的数字，保留实际小数位，不超过2位
	String CODE_30173011 = "30173011";
	// 盘重 不能为空
	String CODE_30173012 = "30173012";
	// 输入的毛重应为 大于0小于1000万的数字，保留实际小数位，不超过2位
	String CODE_30173013 = "30173013";
	// 毛重 不能为空
	String CODE_30173014 = "30173014";
	// 盘点信息 不存在
	String CODE_30173015 = "30173015";
	// 盘点明细不存在
	String CODE_30173016 = "30173016";
	// 盘点明细id不能为空
	String CODE_30173017 = "30173017";
	// 盘点id不能为空
	String CODE_30173018 = "30173018";
	// 驳回原因不能为空
	String CODE_30173019 = "30173019";
	// 签署方式为线下 才能进行确认
	String CODE_30173020 = "30173020";
	// 传入的盘点总数量/重量有误
	String CODE_30173021 = "30173021";
	// 传入的盈亏数量/重量有误
	String CODE_30173022 = "30173022";
	// 传入的净重合计有误
	String CODE_30173023 = "30173023";
	// 盘点照片最大上传50张
	String CODE_30173024 = "30173024";
	// 盘点总数量/重量不能为空
	String CODE_30173025 = "30173025";
	// 盈亏数量/重量不能为空
	String CODE_30173026 = "30173026";
	// 净重合计不能为空
	String CODE_30173027 = "30173027";
	// 盘点人id不能为空
	String CODE_30173028 = "30173028";
	// 盘点人名称不能为空
	String CODE_30173029 = "30173029";
	// 盘点时间不能超过当前时间
	String CODE_30173030 = "30173030";
	// 该状态无法确认盘点
	String CODE_30173031 = "30173031";

	// 抽检相关
	// 关联项目id不能为空
	String CODE_30174001 = "30174001";
	// 关联合同id不能为空
	String CODE_30174002 = "30174002";
	// 签署方式不能为空
	String CODE_30174003 = "30174003";
	// 抽检类型不能为空
	String CODE_30174004 = "30174004";
	// 抽检时间不能为空
	String CODE_30174005 = "30174005";
	// 备注长度不能超过200
	String CODE_30174006 = "30174006";
	// 货物信息不能为空
	String CODE_30174007 = "30174007";
	// 仓库id不能为空
	String CODE_30174008 = "30174008";
	// 检测人不能为空
	String CODE_30174009 = "30174009";
	// 抽检结果不能为空
	String CODE_30174010 = "30174010";
	// 取回日期不能为空
	String CODE_30174011 = "30174011";
	// 取样地点不能为空
	String CODE_30174012 = "30174012";
	// 取样地点不能超过32个字符
	String CODE_30174013 = "30174013";
	// 检测人不能超过32个字符
	String CODE_30174014 = "30174014";
	// 抽检数量应大于0小于1000万
	String CODE_30174015 = "30174015";
	// 抽检数量不能为空
	String CODE_30174016 = "30174016";
	// 检测结果不能超过32个字符
	String CODE_30174017 = "30174017";
	// 检测结果不能为空
	String CODE_30174018 = "30174018";
	// 备注不能超过32个字符
	String CODE_30174019 = "30174019";
	// 规格/型号不能重复
	String CODE_30174020 = "30174020";
	// 规格/型号不能为空
	String CODE_30174021 = "30174021";
	// 传入的抽检合计数量有误
	String CODE_30174022 = "30174022";
	// 抽检照片最大上传50张
	String CODE_30174023 = "30174023";
	// 抽检总数量不能为空
	String CODE_30174024 = "30174024";
	// 抽检人id不能为空
	String CODE_30174025 = "30174025";
	// 抽检人名称不能为空
	String CODE_30174026 = "30174026";
	// 抽检时间不能超过当前时间
	String CODE_30174027 = "30174027";
	// 该状态无法确认抽检
	String CODE_30174028 = "30174028";

	// 出库相关
	// 出库单不存在
	String CODE_30175001 = "30175001";
	// 出库类型不能为空
	String CODE_30175002 = "30175002";
	// 签署类型不能为空
	String CODE_30175003 = "30175003";
	// 仓库id不能为空
	String CODE_30175004 = "30175004";
	// 是否计算出库金额不能为空
	String CODE_30175005 = "30175005";
	// 是否有检测不能为空
	String CODE_30175006 = "30175006";
	// 检测人不能为空
	String CODE_30175007 = "30175007";
	// 检测日期不能为空
	String CODE_30175008 = "30175008";
	// 保存类型不能为空
	String CODE_30175009 = "30175009";
	// 非草稿状态不能提交
	String CODE_30175010 = "30175010";
	// 只有确认中状态以及线下单据才能进行确认操作
	String CODE_30175011 = "30175011";
	// 此单据状态无法驳回
	String CODE_30175012 = "30175012";
	// 状态不为已出库，无法发起作废
	String CODE_30175013 = "30175013";
	// 入库单不存在
	String CODE_30175014 = "30175014";
    // 只有待入库状态才可进行确认
	String CODE_30175015 = "30175015";
	// 状态不为已入库，无法发起作废
	String CODE_30175016 = "30175016";
	// 该签收单已被关联，不能重复关联
	String CODE_30175017 = "30175017";
	// 库存不足
	String CODE_30175018 = "30175018";
	// 出库数量/重量应大于0小于10000000
	String CODE_30175019 = "30175019";
	// 该出库单已被关联，不能重复关联
	String CODE_30175020 = "30175020";
	// 该出库单已被入库单关联，不可作废
	String CODE_30175021 = "30175021";
	// 入库数量不能大于关联签收单的签收数量
	String CODE_30175022 = "30175022";
	// 线上只可保存
	String CODE_30175023 = "30175023";
	// 该状态不可撤回
	String CODE_30175024= "30175024";
	// 该状态不可确认
	String CODE_30175025 = "30175025";
	// {0}（规格/型号）出库数量/重量大于库存数量/重量
	String CODE_30175026 = "30175026";
	// 此状态不可作废
	String CODE_30175027 = "30175027";
	// 入库人不能为空
	String CODE_30175028 = "30175028";
	// 入库人限制2-6个字
	String CODE_30175029 = "30175029";
	// 入库时间不能为空
	String CODE_30175030 = "30175030";
	// 收费标准不能为空
	String CODE_30175031 = "30175031";
	// 出库人不能为空
	String CODE_30175032 = "30175032";
	// 出库人限制2-6个字
	String CODE_30175033 = "30175033";
	// 出库时间不能为空
	String CODE_30175034 = "30175034";
	// 检测信息不能为空
	String CODE_30175035 = "30175035";
	// 检测结果不能为空
	String CODE_30175036 = "30175036";
	// 取样柜台、车间不能为空
	String CODE_30175037 = "30175037";
	// 入库时间不能超过当前时间
	String CODE_30175038 = "30175038";
	// 出库时间不能超过当前时间
	String CODE_30175039 = "30175039";
	// 出库单据不能为空
	String CODE_30175040 = "30175040";
	// 待出库状态才能出库
	String CODE_30175041 = "30175041";
	// 已出库状态才能作废
	String CODE_30175042 = "30175042";
	// 作废原因不能超过200个字符
	String CODE_30175043 = "30175043";

	// 核库报表相关
	// 关联项目id不能为空
	String CODE_30176001 = "30176001";
	// 关联合同id不能为空
	String CODE_30176002 = "30176002";
	// 上日加权平均价： 大于0，不超过100万，保留两位小数
	String CODE_30176003 = "30176003";
	// 核库日期不能为空
	String CODE_30176004 = "30176004";
	// 上日加权平均价不能为空
	String CODE_30176005 = "30176005";
	// 核库报表信息 不存在
	String CODE_30176006 = "30176006";
	// 此合同已核库完成，不可重复核库
	String CODE_30176007 = "30176007";

	// 摄像头相关
	// 摄像头已上线，不能删除
	String CODE_30177001 = "30177001";
	// 摄像头不存在
	String CODE_30177002 = "30177002";
	// 离线状态不能查看视频
	String CODE_30177003 = "30177003";
	// 已有相同序列号的设备
	String CODE_30177004 = "30177004";
	// 序列号不能为空
	String CODE_30177005 = "30177005";
	// ICCID不正确
	String CODE_30177006 = "30177006";
	// 摄像头名称不能为空
	String CODE_30177007 = "30177007";
	// 摄像头名称长度不能超过40
	String CODE_30177008 = "30177008";
	// 仓库id不能为空
	String CODE_30177009 = "30177009";
	// 安装位置不能为空
	String CODE_30177010 = "30177010";
	// 已有相同名称的设备
	String CODE_30177011 = "30177011";
	// 设备令牌不能为空
	String CODE_30177012 = "30177012";
	// 设备令牌不能超过20个字符
	String CODE_30177013 = "30177013";
	// ICCID不能超过20个字符
	String CODE_30177014 = "30177014";
	// 序列号不能超过9个字符
	String CODE_30177015 = "30177015";
	// 设备类型不能为空
	String CODE_30177016 = "30177016";
	// 设备类型错误
	String CODE_30177017 = "30177017";
	// 设备id不能为空
	String CODE_30177018 = "30177018";
	// 安装位置不能超过20个字符
	String CODE_30177019 = "30177019";
	// 无法删除，请先解除关联摄像头
	String CODE_30177020 = "30177020";

	// 往来客户相关相关
	// 往来客户不存在
	String CODE_30178001 = "30178001";
	// 该往来客户已被平台禁用
	String CODE_30178002 = "30178002";
	// 已启用的客户不能删除
	String CODE_30178003 = "30178003";
	// 不能重复绑定同一个客户
	String CODE_30178004 = "30178004";



    //<editor-fold desc="过户管理相关">
    // 过户信息不存在
    String CODE_30179001 = "30179001";
    // 只有草稿状态或驳回状态才可修改
    String CODE_30179002 = "30179002";
    // 删除对象不存在
    String CODE_30179003 = "30179003";
    // 只有草稿状态或驳回状态才可删除
    String CODE_30179004 = "30179004";
    // 草稿、驳回、过户完成状态不能审核数据
    String CODE_30179005 = "30179005";
    // 下载pdf错误
    String CODE_30179006 = "30179006";
    // 勾选的列表中过户信息不存在
    String CODE_30179007 = "30179007";
    // 项目id不能为空
    String CODE_30179008 = "30179008";
    //合同ID不能为空
    String CODE_30179009 = "30179009";
    // 货物信息列表不能为空
    String CODE_30179010 = "30179010";
    // 买方ID不能为空
    String CODE_30179011 = "30179011";
    // 买方名称不能为空
    String CODE_30179012 = "30179012";
    // 卖方ID不能为空
    String CODE_30179013 = "30179013";
    // 卖方名称不能为空
    String CODE_30179014 = "30179014";
    // 所属仓库id不能为空
    String CODE_30179015 = "30179015";
    // 所属仓库名称不能为空
    String CODE_30179016 = "30179016";
    // 过户总数量/重量不能为空
    String CODE_30179017 = "30179017";
    // 净重不能为空
    String CODE_30179018 = "30179018";
    // 毛重不能为空
    String CODE_30179019 = "30179019";
    // 保存类型不能为空
    String CODE_30179020 = "30179020";
    // 过户日期不能为空
    String CODE_30179021 = "30179021";
    // 备注长度不能超过200个字符
    String CODE_30179022 = "30179022";
    // 过户数量范围为0~1000万
    String CODE_30179023 = "30179023";
    // 过户的净重、毛重范围是0~1000万
    String CODE_30179024 = "30179024";
    // 保存类型只有保存为草稿或保存并提交两种类型
    String CODE_30179025 = "30179025";
    // 非提交状态不能审核
    String CODE_30179026 = "30179026";
    // 签署类型只有线上签署和线下签署两种类型
    String CODE_30179027 = "30179027";
    // 非买方核实状态不能签署
    String CODE_30179028 = "30179028";
    // 非仓储方审核完毕，卖方不能核实
    String CODE_30179029 = "30179029";
    // 非卖方审核完毕，买方不能核实
    String CODE_30179030 = "30179030";
    // 当前用户不是签章使用者
    String CODE_30179031 = "30179031";
    // 此业务非签署中状态，不能签署
    String CODE_30179032 = "30179032";
    // 用户非生成签署链接用户，不能签署
    String CODE_30179033 = "30179033";
    // 验证码错误或已过期
    String CODE_30179034 = "30179034";
    // {0}（规格/型号）过户数量/重量大于库存数量/重量
    String CODE_30179035 = "30179035";
    // 该仓库下不存在{0}（规格/型号）数据
    String CODE_30179036 = "30179036";
    // 买卖双方不同为同一个
    String CODE_30179037 = "30179037";
    // 货物信息列表中存在相同规格/型号数据
    String CODE_30179038 = "30179038";
    // 买方不存在仓储合同，不支持过户
    String CODE_30179039 = "30179039";
    // 买方仓储合同未选择{0}/{1}
    String CODE_30179040 = "30179040";

    // 往来企业相关
	// 往来企业不存在
	String CODE_30180001 = "30180001";
	// 企业类型不能为空
	String CODE_30180002 = "30180002";
	// 企业类型不存在
	String CODE_30180003 = "30180003";
	// 类型不能为空
	String CODE_30180004 = "30180004";
	// 类型不存在
	String CODE_30180005 = "30180005";
	// 企业名称不能超过32个字符
	String CODE_30180006 = "30180006";
	// 法定代表人不能超过32个字符
	String CODE_30180007 = "30180007";
	// 联系人不能超过32个字符
	String CODE_30180008 = "30180008";
	// 手机号码不能超过11个字符
	String CODE_30180009 = "30180009";
	// 账号不能为空
	String CODE_30180010 = "30180010";
	// 账号不存在
	String CODE_30180011 = "30180011";
	// 账号未认证
	String CODE_30180012 = "30180012";
	// 该往来供应链已存在
	String CODE_30180013 = "30180013";
	// 该往来企业已存在
	String CODE_30180014 = "30180014";
	// 企业名称不能为空
	String CODE_30180015 = "30180015";
	// 统一社会信用代码不能为空
	String CODE_30180016 = "30180016";
	// 法定代表人不能为空
	String CODE_30180017 = "30180017";
	// 无权限
	String CODE_30180018 = "30180018";
	// 已驳回状态才能被删除
	String CODE_30180019 = "30180019";
	// 待确认状态才能确认
	String CODE_30180020 = "30180020";
	// 待确认状态才能驳回
	String CODE_30180021 = "30180021";
	// 开户名称不能为空
	String CODE_30180022 = "30180022";
	// 开户名称不能超过32个字符
	String CODE_30180023 = "30180023";
	// 银行账户不能为空
	String CODE_30180024 = "30180024";
	// 银行账户不能超过32个字符
	String CODE_30180025 = "30180025";
	// 开户行不能为空
	String CODE_30180026 = "30180026";
	// 开户行不能超过32个字符
	String CODE_30180027 = "30180027";
	// 该企业已被项目关联
	String CODE_30180028 = "30180028";
	// 请先完成组织机构认证
	String CODE_30180029 = "30180029";
	// 驳回原因不能为空
	String CODE_30180030 = "30180030";
	// 驳回原因不超过200个字符
	String CODE_30180031 = "30180031";


}
