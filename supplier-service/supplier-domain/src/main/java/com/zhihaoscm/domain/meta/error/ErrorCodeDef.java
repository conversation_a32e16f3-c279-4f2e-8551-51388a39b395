package com.zhihaoscm.domain.meta.error;

import java.util.function.Function;

import lombok.Getter;

@Getter
public enum ErrorCodeDef {
	CODE_001(ErrorCode.CODE_001, "业务错误"),
	CODE_002(ErrorCode.CODE_002, "签署错误"),
	CODE_003(ErrorCode.CODE_003, "选择的链云用户不能是同一个账号"),
	CODE_400(ErrorCode.CODE_400, "BadRequest"),
	CODE_401(ErrorCode.CODE_401, "没有权限"),
	CODE_403(ErrorCode.CODE_403, "权限错误"),
	CODE_404(ErrorCode.CODE_404, "没有资源"),
	CODE_900(ErrorCode.CODE_900, "sdk异常"),
	CODE_911(ErrorCode.CODE_911, "车辆服务接口异常"),
	CODE_912(ErrorCode.CODE_912, "车辆服务查询结果为空"),
	CODE_913(ErrorCode.CODE_913, "参数不正确（参数为空、检索时间范围不正确、参数数量不正确）"),

	//用户不存在
	CODE_20001003(ErrorCode.CODE_20001003, "用户不存在!"),

	// 合同相关
	CODE_30001001(ErrorCode.CODE_30001001, "合同不存在!"),
	CODE_30001002(ErrorCode.CODE_30001002, "当前用户不存在!"),
	CODE_30001003(ErrorCode.CODE_30001003, "调用契约锁撤销合同接口异常"),
	CODE_30001004(ErrorCode.CODE_30001004, "调用契约锁撤销合同接口失败"),
	CODE_30001005(ErrorCode.CODE_30001005, "调用契约锁下载合同接口失败"),
	CODE_30001006(ErrorCode.CODE_30001006, "调用契约锁生成合同草稿接口异常"),
	CODE_30001007(ErrorCode.CODE_30001007, "调用契约锁生成合同草稿接口失败"),
	CODE_30001008(ErrorCode.CODE_30001008, "下载合同文件异常!"),
	CODE_30001009(ErrorCode.CODE_30001009, "调用契约锁通过文件添加合同文档接口异常"),
	CODE_30001010(ErrorCode.CODE_30001010, "调用契约锁通过文件添加合同文档接口失败"),
	CODE_30001011(ErrorCode.CODE_30001011, "调用契约锁发起合同接口异常"),
	CODE_30001012(ErrorCode.CODE_30001012, "调用契约锁发起合同接口失败"),
	CODE_30001013(ErrorCode.CODE_30001013, "调用契约锁获取签署链接接口异常"),
	CODE_30001014(ErrorCode.CODE_30001014, "调用契约锁生成签署令牌接口异常"),
	CODE_30001015(ErrorCode.CODE_30001015, "暂无可用印章，请先创建关联该业务的印章"),
	CODE_30001016(ErrorCode.CODE_30001016, "您尚未为该业务指定关联印章,请前往修改印章页面勾选相关业务后再试"),
	CODE_30001017(ErrorCode.CODE_30001017, "无权签署该合同"),
	CODE_30001018(ErrorCode.CODE_30001018, "已发起签署，请等待专人进行签署"),
	CODE_30001019(ErrorCode.CODE_30001019, "调用契约锁查看合同详情异常"),
	CODE_30001020(ErrorCode.CODE_30001020, "发起签署失败，请修改印章使用者"),
	CODE_30001021(ErrorCode.CODE_30001021, "合同已存在，请返回列表查看"),
	CODE_30001022(ErrorCode.CODE_30001022, "获取token失败"),
	CODE_30001023(ErrorCode.CODE_30001023, "文件ID不能为空"),
	CODE_30001024(ErrorCode.CODE_30001024, "文件格式不支持"),
	CODE_30001025(ErrorCode.CODE_30001025, "企业信息变更中，无法签署"),
	CODE_30001026(ErrorCode.CODE_30001026, "后台未配置签署人"),

	// 船舶相关
	CODE_30002001(ErrorCode.CODE_30002001, "抓拍船舶照片失败"),
	CODE_30002002(ErrorCode.CODE_30002002, "设备不在线"),
	CODE_30002003(ErrorCode.CODE_30002003, "设备未开启直播功能"),
	CODE_30002004(ErrorCode.CODE_30002004, "调用宇视云获取视频播放链接接口异常"),
	CODE_30002005(ErrorCode.CODE_30002005, "设备推流错误"),
	CODE_30002006(ErrorCode.CODE_30002006, "调用宇视云开启播放功能接口异常"),
	CODE_30002007(ErrorCode.CODE_30002007, "调用宇视云关闭播放功能接口异常"),
	CODE_30002008(ErrorCode.CODE_30002008, "获取视频播放链接失败"),
	CODE_30002009(ErrorCode.CODE_30002009, "抓拍船舶照片异常"),
	CODE_30002010(ErrorCode.CODE_30002010, "开启播放功能失败"),
	CODE_30002011(ErrorCode.CODE_30002011, "调用宇视云关闭播放功能接口失败"),
	CODE_30002012(ErrorCode.CODE_30002012, "调用宇视云查询设备接口异常"),
	CODE_30002013(ErrorCode.CODE_30002013, "调用宇视云接口查询设备失败"),
	CODE_30002014(ErrorCode.CODE_30002014, "船舶已存在"),
	CODE_30002015(ErrorCode.CODE_30002015, "船舶不存在"),
	CODE_30002016(ErrorCode.CODE_30002016, "调用船顺网查询船舶信息接口异常"),
	CODE_30002017(ErrorCode.CODE_30002017, "调用船顺网区域查询船舶接口异常"),
	CODE_30002018(ErrorCode.CODE_30002018, "调用船顺网关键字查询船舶接口异常"),
	CODE_30002019(ErrorCode.CODE_30002019, "识别内河船舶检验报告文件不存在"),


	// 航线相关
	CODE_30003001(ErrorCode.CODE_30003001, "航线已存在，不可重复维护"),

	// 预付款相关
	CODE_30004001(ErrorCode.CODE_30004001, "用户账户不存在,无法撤销"),

	// 服务费账单相关
	CODE_30005001(ErrorCode.CODE_30005001, "用户账户不存在,无法核销"),
	CODE_30005002(ErrorCode.CODE_30005002, "用户不存在,无法结算"),
	// 设备相关
	CODE_30006001(ErrorCode.CODE_30006001, "调用海康威视平台新增接口异常"),
	CODE_30006002(ErrorCode.CODE_30006002, "调用海康威视平台修改接口异常"),
	CODE_30006003(ErrorCode.CODE_30006003, "调用海康威视平台删除接口异常"),
	CODE_30006004(ErrorCode.CODE_30006004, "验证码错误"),
	CODE_30006005(ErrorCode.CODE_30006005, "设备不存在"),
	CODE_30006006(ErrorCode.CODE_30006006, "设备不在线"),
	CODE_30006007(ErrorCode.CODE_30006007, "设备序列号无效"),
	CODE_30006008(ErrorCode.CODE_30006008, "远程抓图异常"),
	CODE_30006009(ErrorCode.CODE_30006009, "获取标准流播放地址异常"),
	CODE_30006010(ErrorCode.CODE_30006010, "失效标准流播放地址异常"),
	CODE_30006011(ErrorCode.CODE_30006011, "参数错误"),

	// 企业认证相关
	CODE_30007001(ErrorCode.CODE_30007001, "当前客户尚未进行过个人认证操作"),
	CODE_30007002(ErrorCode.CODE_30007002, "当前客户未进行过企业认证操作,请核实后再试!"),
	CODE_30007003(ErrorCode.CODE_30007003, "保存企业认证结果异常"),
	CODE_30007004(ErrorCode.CODE_30007004, "当前企业未进行授权操作,请核实后再试!"),
	CODE_30007005(ErrorCode.CODE_30007005, "调用获取契约锁企业认证结果接口异常,请稍后再试"),
	CODE_30007006(ErrorCode.CODE_30007006, "该企业尚未通过认证,请等待企业审核完成后再进行授权操作"),
	CODE_30007007(ErrorCode.CODE_30007007, "获取企业授权链接异常"),
	CODE_30007008(ErrorCode.CODE_30007008, "获取企业授权链接失败"),
	CODE_30007009(ErrorCode.CODE_30007009, "给当前操作人添加印章管理员角色异常!"),
	CODE_30007010(ErrorCode.CODE_30007010, "给当前操作人添加印章管理员角色失败!"),
	CODE_30007011(ErrorCode.CODE_30007011, "未找到相关企业秘钥"),
	CODE_30007012(ErrorCode.CODE_30007012, "添加员工异常"),
	CODE_30007013(ErrorCode.CODE_30007013, "添加员工失败"),
	CODE_30007014(ErrorCode.CODE_30007014, "未找到相关企业认证信息"),
	CODE_30007015(ErrorCode.CODE_30007015, "获取企业认证令牌异常"),
	CODE_30007016(ErrorCode.CODE_30007016, "获取企业认证令牌失败"),
	CODE_30007017(ErrorCode.CODE_30007017, "调用第三方接口查询公司详情异常"),
	CODE_30007018(ErrorCode.CODE_30007018, "调用第三方接口查询公司详情失败"),
	CODE_30007019(ErrorCode.CODE_30007019, "企业正在认证中,请等待认证结果"),
	CODE_30007020(ErrorCode.CODE_30007020, "企业名称不能为空!"),


	// openAI相关
	CODE_30008001(ErrorCode.CODE_30008001, "上传文件失败"),
	CODE_30008002(ErrorCode.CODE_30008002, "删除文件失败"),
	CODE_30008003(ErrorCode.CODE_30008003, "创建向量存储失败"),
	CODE_30008004(ErrorCode.CODE_30008004, "修改向量存储失败"),
	CODE_30008005(ErrorCode.CODE_30008005, "删除向量存储失败"),
	CODE_30008006(ErrorCode.CODE_30008006, "向量存储添加文件失败"),
	CODE_30008007(ErrorCode.CODE_30008007, "向量存储不存在"),
	CODE_30008008(ErrorCode.CODE_30008008, "向量存储关联文件失败"),
	CODE_30008009(ErrorCode.CODE_30008009, "删除向量存储文件失败"),
	CODE_30008010(ErrorCode.CODE_30008010, "文件不存在"),
	CODE_30008011(ErrorCode.CODE_30008011, "智能助理创建失败"),
	CODE_30008012(ErrorCode.CODE_30008012, "更新智能助理失败"),
	CODE_30008013(ErrorCode.CODE_30008013, "删除智能助理失败"),
	CODE_30008014(ErrorCode.CODE_30008014, "清空会话失败"),
	CODE_30008015(ErrorCode.CODE_30008015, "读取 functions.json 文件失败"),

	// 支付相关
	CODE_30009001(ErrorCode.CODE_30009001, "订单已关闭"),
	CODE_30009002(ErrorCode.CODE_30009002, "订单已支付"),
	CODE_30009003(ErrorCode.CODE_30009003, "订单不存在"),
	CODE_30009004(ErrorCode.CODE_30009004, "获取支付二维码失败"),
	CODE_30009005(ErrorCode.CODE_30009005, "调用支付接口异常"),
	CODE_30009006(ErrorCode.CODE_30009006, "订单号重复"),
	CODE_30009007(ErrorCode.CODE_30009007, "金额不能为空且必须大于0"),
	CODE_30009008(ErrorCode.CODE_30009008, "json解析异常"),
	CODE_30009009(ErrorCode.CODE_30009009, "非会员不需要购买"),
	CODE_30009010(ErrorCode.CODE_30009010, "会员等级不存在"),
	CODE_30009011(ErrorCode.CODE_30009011, "购买类型不存在"),
	CODE_30009012(ErrorCode.CODE_30009012, "船运单id不能为空"),
	CODE_30009013(ErrorCode.CODE_30009013, "购买类型不存在"),
	CODE_30009014(ErrorCode.CODE_30009014, "微信openid不能为空"),
	CODE_30009015(ErrorCode.CODE_30009015, "船运单非待支付船务信息服务费状态不可进行支付"),
	CODE_30009016(ErrorCode.CODE_30009016, "船务信息服务费id不能为空"),
	CODE_30009017(ErrorCode.CODE_30009017, "船务信息服务费不存在"),
	CODE_30009018(ErrorCode.CODE_30009018, "订单id不能为空"),
	CODE_30009019(ErrorCode.CODE_30009019, "签署方式不能为空"),
	CODE_30009020(ErrorCode.CODE_30009020, "发货方式不能为空"),
	CODE_30009021(ErrorCode.CODE_30009021, "货物信息不能为空"),
	CODE_30009022(ErrorCode.CODE_30009022, "货物总数量不能为空"),
	CODE_30009023(ErrorCode.CODE_30009023, "发货日期不能为空"),
	CODE_30009024(ErrorCode.CODE_30009024, "订单类型不能为空"),
	CODE_30009025(ErrorCode.CODE_30009025, "物流方式不能为空"),
	CODE_30009026(ErrorCode.CODE_30009026, "货物总金额不能为空"),
	CODE_30009027(ErrorCode.CODE_30009027, "申请日期不能为空"),
	CODE_30009028(ErrorCode.CODE_30009028, "货物名称不能为空"),
	CODE_30009029(ErrorCode.CODE_30009029, "单位不能为空"),
	CODE_30009030(ErrorCode.CODE_30009030, "单据文件不能为空"),
	CODE_30009031(ErrorCode.CODE_30009031, "该订单已签收，不允许删除"),
	CODE_30009032(ErrorCode.CODE_30009032, "只有草稿状态或者已驳回状态的订单才能被删除"),
	CODE_30009033(ErrorCode.CODE_30009033, "账期不能为空"),
	CODE_30009034(ErrorCode.CODE_30009034, "账期范围应大于等于0小于等于1000"),
	CODE_30009035(ErrorCode.CODE_30009035, "该合同存在应付款逾期，请先处理"),
	CODE_30009036(ErrorCode.CODE_30009036, "当前存在销售订单被关联"),
	CODE_30009037(ErrorCode.CODE_30009037, "销售订单已被关联，请不要重复选择"),
	CODE_30009038(ErrorCode.CODE_30009038, "没有发货信息，不能完成发货"),
	CODE_30009039(ErrorCode.CODE_30009039, "该订单存在待发货和发货中状态的发货单，不能完成发货"),
	CODE_30009040(ErrorCode.CODE_30009040, "订单金额大于预估可提货余额，请先还款"),
	CODE_30009041(ErrorCode.CODE_30009041, "该订单已付款，不允许删除"),
	CODE_30009042(ErrorCode.CODE_30009042, "相同规格请填写一个单价"),
	CODE_30009043(ErrorCode.CODE_30009043, "订单关联签收单后无法新增提货单"),
	CODE_30009044(ErrorCode.CODE_30009044, "订单关联签收单后无法修改提货单"),
	CODE_30009045(ErrorCode.CODE_30009045, "订单关联签收单后无法删除提货单"),
	CODE_30009046(ErrorCode.CODE_30009046, "状态不为对账完成/预对账完成，无法发起作废"),
	CODE_30009047(ErrorCode.CODE_30009047, "当前订单关联发货单，不能删除"),
	CODE_30009048(ErrorCode.CODE_30009048, "当前订单关联提货单，不能删除"),
    CODE_30009049(ErrorCode.CODE_30009049, "销售订单只能关联销售合同"),
    CODE_30009050(ErrorCode.CODE_30009050, "采购订单只能关联采购合同"),

	// 会员等级
	CODE_30010001(ErrorCode.CODE_30010001, "查询不到用户当前的会员信息"),
	CODE_30010002(ErrorCode.CODE_30010002, "当前会员已不能购买"),
	CODE_30010003(ErrorCode.CODE_30010003, "错误的购买类型"),
	CODE_30010004(ErrorCode.CODE_30010004, "购买的会员等级不能小于用户当前的会员等级"),
	CODE_30010005(ErrorCode.CODE_30010005, "当前会员已不能兑换"),

	// 验证码相关
	CODE_30011001(ErrorCode.CODE_30011001, "验证码校验未通过，请重新获取验证码"),
	CODE_30011002(ErrorCode.CODE_30011002, "新设备登录需要校验验证码"),


	// 项目相关
	CODE_30012001(ErrorCode.CODE_30012001, "买方信息不存在"),
	CODE_30012002(ErrorCode.CODE_30012002, "项目不存在"),

	// 微信相关
	CODE_30013001(ErrorCode.CODE_30013001, "微信公众号生成签名异常"),

	// 印章管理相关
	CODE_30014001(ErrorCode.CODE_30014001, "主账号不存在"),
	CODE_30014002(ErrorCode.CODE_30014002, "seal image not found"),
	CODE_30014003(ErrorCode.CODE_30014003, "从第三方平台下载印章图片异常"),
	CODE_30014004(ErrorCode.CODE_30014004, "调用第三方接口自动创建印章失败"),
	CODE_30014005(ErrorCode.CODE_30014005, "调用第三方接口自动创建印章异常"),
	CODE_30014006(ErrorCode.CODE_30014006, "调用第三方接口查询印章详情异常"),
	CODE_30014007(ErrorCode.CODE_30014007, "调用第三方接口查询印章详情失败"),
	CODE_30014008(ErrorCode.CODE_30014008, "调用三方接口编辑印章异常"),
	CODE_30014009(ErrorCode.CODE_30014009, "调用三方接口编辑印章失败"),

	// 子账号管理相关
	CODE_30015001(ErrorCode.CODE_30015001, "调用第三方接口移除子账号异常"),
	CODE_30015002(ErrorCode.CODE_30015002, "调用第三方接口移除子账号失败"),
	CODE_30015003(ErrorCode.CODE_30015003, "调用第三方接口邀请子账号异常"),
	CODE_30015004(ErrorCode.CODE_30015004, "调用第三方接口邀请子账号失败"),
	CODE_30015005(ErrorCode.CODE_30015005, "用户未登录,请登录后再试"),
	CODE_30015006(ErrorCode.CODE_30015006, "员工的姓名和实际姓名不匹配"),

	// 运价综合指数配置版本相关
	CODE_30016001(ErrorCode.CODE_30016001, "版本不存在"),
	CODE_30016002(ErrorCode.CODE_30016002, "版本已生效不能进行启用"),
	CODE_30016003(ErrorCode.CODE_30016003, "版本已失效不能进行修改"),

	// 砂石综合指数配置版本相关
	CODE_30017001(ErrorCode.CODE_30017001, "版本不存在"),
	CODE_30017002(ErrorCode.CODE_30017002, "版本已生效不能进行启用"),
	CODE_30017003(ErrorCode.CODE_30017003, "版本已失效不能进行修改"),

	// 船舶群组相关
	CODE_30018001(ErrorCode.CODE_30018001, "群组不存在"),
	CODE_30018002(ErrorCode.CODE_30018002, "群组名不能为空"),
	CODE_30018003(ErrorCode.CODE_30018003, "群组名不能超过25个字符"),
	CODE_30018004(ErrorCode.CODE_30018004, "群组船舶颜色不能为空"),
	CODE_30018005(ErrorCode.CODE_30018005, "该群组名已经存在"),
	CODE_30018006(ErrorCode.CODE_30018006, "最多可新增20个群组"),
	CODE_30018007(ErrorCode.CODE_30018007, "固定群组不能进行操作"),
	CODE_30018008(ErrorCode.CODE_30018008, "颜色不存在"),
	CODE_30018009(ErrorCode.CODE_30018009, "每个群组最多只能添加200条数据"),

	// 船舶关注相关
	CODE_30019001(ErrorCode.CODE_30019001, "群组id不能为空"),
	CODE_30019002(ErrorCode.CODE_30019002, "船舶状态不能为空"),
	CODE_30019003(ErrorCode.CODE_30019003, "船舶状态不存在"),
	CODE_30019004(ErrorCode.CODE_30019004, "备注字符长度不能超过100个字符"),
	CODE_30019005(ErrorCode.CODE_30019005, "船舶id不能为空"),
	CODE_30019006(ErrorCode.CODE_30019006, "船舶关注信息不存在"),
	CODE_30019007(ErrorCode.CODE_30019007, "该群组你无权限添加"),
	CODE_30019008(ErrorCode.CODE_30019008, "船舶已经被关注"),
	CODE_30019009(ErrorCode.CODE_30019009, "无权操作他人数据"),

	//通知管理相关
	CODE_30020001(ErrorCode.CODE_30020001, "通知信息不存在"),
	CODE_30020002(ErrorCode.CODE_30020002, "标题不能为空，且最大为50个字符"),
	CODE_30020003(ErrorCode.CODE_30020003, "分类不能为空"),
	CODE_30020004(ErrorCode.CODE_30020004, "分类不存在"),
	CODE_30020005(ErrorCode.CODE_30020005, "内容不能为空"),
	CODE_30020006(ErrorCode.CODE_30020006, "定时发布时间只能选择未来时间"),
	CODE_30020007(ErrorCode.CODE_30020007, "草稿状态才能进行编辑"),
	CODE_30020008(ErrorCode.CODE_30020008, "待发布状态才能进行取消发布"),
	CODE_30020009(ErrorCode.CODE_30020009, "待发布状态不能进行删除"),
	CODE_30020010(ErrorCode.CODE_30020010, "已发布状态才能进行撤回"),
	CODE_30020011(ErrorCode.CODE_30020011, "撤回失败，发布后7天内才可撤回"),
	CODE_30020012(ErrorCode.CODE_30020012, "草稿状态才能进行发布"),
	CODE_30020013(ErrorCode.CODE_30020013, "参数错误"),

	//砂石学院相关
	CODE_30021001(ErrorCode.CODE_30021001, "砂石学院数据不存在"),
	CODE_30021002(ErrorCode.CODE_30021002, "标题不能为空，且最大为50个字符"),
	CODE_30021003(ErrorCode.CODE_30021003, "收费类型不能为空"),
	CODE_30021004(ErrorCode.CODE_30021004, "收费类型不存在"),
	CODE_30021005(ErrorCode.CODE_30021005, "展示位置不存在"),
	CODE_30021006(ErrorCode.CODE_30021006, "标签数量超过10个"),
	CODE_30021007(ErrorCode.CODE_30021007, "来源长度不超过10个字符"),
	CODE_30021008(ErrorCode.CODE_30021008, "发布日期不能为空"),
	CODE_30021009(ErrorCode.CODE_30021009, "略缩图不能为空"),
	CODE_30021010(ErrorCode.CODE_30021010, "摘要长度不超过200个字符"),
	CODE_30021011(ErrorCode.CODE_30021011, "内容不能为空"),
	CODE_30021012(ErrorCode.CODE_30021012, "免责声明长度不超过300个字符"),
	CODE_30021013(ErrorCode.CODE_30021013, "下架状态才能编辑"),
	CODE_30021014(ErrorCode.CODE_30021014, "下架状态才能删除"),
	CODE_30021015(ErrorCode.CODE_30021015, "存在重复标签"),
	CODE_30021016(ErrorCode.CODE_30021016, "参数错误"),

	// 机构认证相关
	CODE_30022001(ErrorCode.CODE_30022001, "机构认证不存在"),
	CODE_30022002(ErrorCode.CODE_30022002, "状态不能为空"),
	CODE_30022003(ErrorCode.CODE_30022003, "状态不存在"),
	CODE_30022004(ErrorCode.CODE_30022004, "备注不能超过100个字符"),
	CODE_30022005(ErrorCode.CODE_30022005, "营业执照不能为空"),
	CODE_30022006(ErrorCode.CODE_30022006, "企业名称不能为空"),
	CODE_30022007(ErrorCode.CODE_30022007, "企业名称不能超过40个字符"),
	CODE_30022008(ErrorCode.CODE_30022008, "统一社会信用代码不能为空"),
	CODE_30022009(ErrorCode.CODE_30022009, "统一社会信用代码必须为18位"),
	CODE_30022010(ErrorCode.CODE_30022010, "法定代表人不能为空"),
	CODE_30022011(ErrorCode.CODE_30022011, "法定代表人不能超过10个字符"),
	CODE_30022012(ErrorCode.CODE_30022012, "该企业已被认证"),
	CODE_30022013(ErrorCode.CODE_30022013, "待审核状态才能进行审核"),
	CODE_30022014(ErrorCode.CODE_30022014, "无权限"),
	CODE_30022015(ErrorCode.CODE_30022015, "已驳回状态才能进行修改"),
	CODE_30022016(ErrorCode.CODE_30022016, "已认证"),
	CODE_30022017(ErrorCode.CODE_30022017, "已解除机构认证，或未认证才能进行新增"),
	CODE_30022018(ErrorCode.CODE_30022018, "备注不能为空"),
	CODE_30022019(ErrorCode.CODE_30022019, "未完成个人认证"),
	CODE_30022020(ErrorCode.CODE_30022020, "您还未完成本系统的组织机构认证"),
	CODE_30022021(ErrorCode.CODE_30022021, "营业执照不合法"),
	CODE_30022022(ErrorCode.CODE_30022022, "请选择企业身份"),
	CODE_30022023(ErrorCode.CODE_30022023, "授权委托书不能为空"),

	// 客户船舶关注相关
	CODE_30023001(ErrorCode.CODE_30023001, "超出关注数量"),
	CODE_30023002(ErrorCode.CODE_30023002, "船舶关注不存在"),
	CODE_30023003(ErrorCode.CODE_30023003, "该船不是您关注的"),


	// 搜索发现相关
	CODE_30024001(ErrorCode.CODE_30024001, "搜索发现不存在"),
	CODE_30024002(ErrorCode.CODE_30024002, "关键词不能为空"),
	CODE_30024003(ErrorCode.CODE_30024003, "关键词不能超过10个字符"),
	CODE_30024004(ErrorCode.CODE_30024004, "关键词不能重复"),
	CODE_30024005(ErrorCode.CODE_30024005, "类型不能为空"),
	CODE_30024006(ErrorCode.CODE_30024006, "类型不存在"),
	CODE_30024007(ErrorCode.CODE_30024007, "业务id不能为空"),
	CODE_30024008(ErrorCode.CODE_30024008, "置顶数已达上限"),
	CODE_30024009(ErrorCode.CODE_30024009, "未置顶才能进行编辑"),
	CODE_30024010(ErrorCode.CODE_30024010, "未置顶才能进行删除"),
	CODE_30024011(ErrorCode.CODE_30024011, "置顶的关键词才能增加点击"),

	// 留言反馈相关
	CODE_30025001(ErrorCode.CODE_30025001, "留言反馈数据不存在"),
	CODE_30025002(ErrorCode.CODE_30025002, "留言类型不能为空"),
	CODE_30025003(ErrorCode.CODE_30025003, "留言类型不存在"),
	CODE_30025004(ErrorCode.CODE_30025004, "留言内容不能为空，且最大为150字符"),
	CODE_30025005(ErrorCode.CODE_30025005, "最大上传6张图片"),
	CODE_30025006(ErrorCode.CODE_30025006, "待回复状态才能进行回复"),
	CODE_30025007(ErrorCode.CODE_30025007, "回复内容不能为空，且最大为200字符"),

	// 功能反馈相关
	CODE_30026001(ErrorCode.CODE_30026001, "类型不存在"),
	CODE_30026002(ErrorCode.CODE_30026002, "补充说明字数不能超过50"),
	CODE_30026003(ErrorCode.CODE_30026003, "反馈原因不存在"),
	CODE_30026004(ErrorCode.CODE_30026004, "类型不能为空"),
	CODE_30026005(ErrorCode.CODE_30026005, "反馈原因不能为空"),
	CODE_30026006(ErrorCode.CODE_30026006, "反馈原因不能多选"),
	CODE_30026007(ErrorCode.CODE_30026007, "该数据不存在"),
	CODE_30026008(ErrorCode.CODE_30026008, "该数据状态为已处理"),
	CODE_30026009(ErrorCode.CODE_30026009, "备注不能为空"),
	CODE_30026010(ErrorCode.CODE_30026010, "备注不能超过200个字符"),
	CODE_30026011(ErrorCode.CODE_30026011, "状态码错误"),

	// 帮助中心相关
	CODE_30027001(ErrorCode.CODE_30027001, "帮助中心数据不存在"),
	CODE_30027002(ErrorCode.CODE_30027002, "标题不能为空，且最大为50个字符"),
	CODE_30027003(ErrorCode.CODE_30027003, "内容不能为空"),
	CODE_30027004(ErrorCode.CODE_30027004, "下架状态才能编辑"),
	CODE_30027005(ErrorCode.CODE_30027005, "下架状态才能删除"),
	CODE_30027006(ErrorCode.CODE_30027006, "参数错误"),

	// 平台船运需求相关
	// 平台船运需求ID不能为空
	CODE_30028001(ErrorCode.CODE_30028001, "平台船运需求ID不能为空"),
	CODE_30028002(ErrorCode.CODE_30028002, "抢单后不能进行资源反馈"),
	CODE_30028003(ErrorCode.CODE_30028003, "船主接单ID不能为空"),

	// 货主船运需求相关
	// 货主船运需求ID不能为空
	CODE_30029001(ErrorCode.CODE_30029001, "货主船运需求ID不能为空"),

	// AI热词相关
	// AI热词不能为空
	CODE_30030001(ErrorCode.CODE_30030001, "AI热词不能为空"),
	// AI热词长度应为1-10个字符
	CODE_30030002(ErrorCode.CODE_30030002, "AI热词长度应为1-10个字符"),
	// 权重值不能为空
	CODE_30030003(ErrorCode.CODE_30030003, "权重值不能为空"),
	// 热词数据不存在
	CODE_30030004(ErrorCode.CODE_30030004, "热词数据不存在"),
	// 该热词数据已存在
	CODE_30030005(ErrorCode.CODE_30030005, "该热词已存在"),

	// 向量存储器
	CODE_30031001(ErrorCode.CODE_30031001, "向量存储名称不能为空且不能超过16个字符"),
	CODE_30031002(ErrorCode.CODE_30031002, "向量存储器名称不能为空"),
	CODE_30031003(ErrorCode.CODE_30031003, "向量存储器不存在"),
	CODE_30031004(ErrorCode.CODE_30031004, "文件不能为空"),
	CODE_30031005(ErrorCode.CODE_30031005, "助手名称不能为空 长度为1~20个字符"),
	CODE_30031006(ErrorCode.CODE_30031006, "指令不能为空 长度为1~500个字符"),
	CODE_30031007(ErrorCode.CODE_30031007, "模型不能为空"),
	CODE_30031008(ErrorCode.CODE_30031008, "fileSearch参数不能为空 只能为0和1"),
	CODE_30031009(ErrorCode.CODE_30031009, "miniProgram参数不能为空 只能为0和1"),
	CODE_30031010(ErrorCode.CODE_30031010, "向量存储不能为空"),
	CODE_30031011(ErrorCode.CODE_30031011, "智能助理不存在"),

	// 轮播图相关
	CODE_30032001(ErrorCode.CODE_30032001, "轮播图位置不能为空"),
	CODE_30032002(ErrorCode.CODE_30032002, "轮播图位置只能为1到4"),
	CODE_30032003(ErrorCode.CODE_30032003, "轮播图文件id不能为空"),
	CODE_30032004(ErrorCode.CODE_30032004, "排序值不能为空"),
	CODE_30032005(ErrorCode.CODE_30032005, "跳转类型不能为空"),
	CODE_30032006(ErrorCode.CODE_30032006, "跳转类型只能为1到6"),
	CODE_30032007(ErrorCode.CODE_30032007, "内容不能为空"),
	CODE_30032008(ErrorCode.CODE_30032008, "轮播图不存在"),
	CODE_30032009(ErrorCode.CODE_30032009, "承运商跳转类型不能为商品或砂价"),

	// 广告位相关
	CODE_30033001(ErrorCode.CODE_30033001, "广告位不存在"),
	CODE_30033002(ErrorCode.CODE_30033002, "广告名称不能为空"),
	CODE_30033003(ErrorCode.CODE_30033003, "展现形式不能为空"),
	CODE_30033004(ErrorCode.CODE_30033004, "轮播间隔不能为空"),
	CODE_30033005(ErrorCode.CODE_30033005, "广告周期不能为空"),
	CODE_30033006(ErrorCode.CODE_30033006, "广告图片不能为空"),
	CODE_30033007(ErrorCode.CODE_30033007, "排序不能为空"),
	CODE_30033008(ErrorCode.CODE_30033008, "广告位已存在"),
	CODE_30033009(ErrorCode.CODE_30033009, "广告不能为空"),
	CODE_30033010(ErrorCode.CODE_30033010, "排序只能范围1-5"),
	CODE_30033011(ErrorCode.CODE_30033011, "排序字段存在重复值"),
	CODE_30033012(ErrorCode.CODE_30033012, "展现形式类型不存在"),
	CODE_30033013(ErrorCode.CODE_30033013, "轮播间隔类型不存在"),
	CODE_30033014(ErrorCode.CODE_30033014, "标题不能为空 长度为1-50个字符"),
	CODE_30033015(ErrorCode.CODE_30033015, "分类不能为空"),
	CODE_30033016(ErrorCode.CODE_30033016, "广告类型不能为空"),
	CODE_30033017(ErrorCode.CODE_30033017, "上传图片不能为空"),
	CODE_30033018(ErrorCode.CODE_30033018, "按钮值不能为空并且长度不能超过15个字符"),
	CODE_30033019(ErrorCode.CODE_30033019, "广告信息不存在"),
	CODE_30033020(ErrorCode.CODE_30033020, "链接类型不存在"),
	CODE_30033021(ErrorCode.CODE_30033021, "广告页不能为空"),
	CODE_30033022(ErrorCode.CODE_30033022, "链接不能为空"),
	CODE_30033023(ErrorCode.CODE_30033023, "图片不能为空"),

	// 设备申领相关
	CODE_30034001(ErrorCode.CODE_30034001, "该认领数据已绑定设备,请核实后再试!"),
	CODE_30034002(ErrorCode.CODE_30034002, "该设备已操作过申领绑定,请核实后再试!"),
	CODE_30034003(ErrorCode.CODE_30034003, "该认领数据不存在!"),
	CODE_30034004(ErrorCode.CODE_30034004, "申领id不能为空"),
	CODE_30034005(ErrorCode.CODE_30034005, "设备id不能为空"),
	CODE_30034006(ErrorCode.CODE_30034006, "安装位置不能为空"),
	CODE_30034007(ErrorCode.CODE_30034007, "安装位置长度不能超过10个字符"),
	CODE_30034008(ErrorCode.CODE_30034008, "推介人和船主不能是同一个人"),
	CODE_30034009(ErrorCode.CODE_30034009, "船舶未认证不能申领设备!"),
	CODE_30034010(ErrorCode.CODE_30034010, "船舶已绑定设备无需再次进行申领操作!"),
	CODE_30034011(ErrorCode.CODE_30034011, "船舶已申请过申领,请勿重复操作!"),
	CODE_30034012(ErrorCode.CODE_30034012, "推荐人不能填写本人手机号"),

	// 预付款相关
	CODE_30035001(ErrorCode.CODE_30035001, "金额不能为空 取值范围为0.01~10000000000"),
	CODE_30035002(ErrorCode.CODE_30035002, "日期不能为空"),
	CODE_30035003(ErrorCode.CODE_30035003, "平台银行账号不能为空"),
	CODE_30035004(ErrorCode.CODE_30035004, "平台银行账号不存在"),
	CODE_30035005(ErrorCode.CODE_30035005, "预付款账号信息不能为空"),
	CODE_30035006(ErrorCode.CODE_30035006, "开户名称不能为空 最大25个字符"),
	CODE_30035007(ErrorCode.CODE_30035007, "银行账号不能为空 最多8到30个数字"),
	CODE_30035008(ErrorCode.CODE_30035008, "开户行不能为空 最大128个字符"),
	CODE_30035009(ErrorCode.CODE_30035009, "用户账户id不能为空"),
	CODE_30035010(ErrorCode.CODE_30035010, "用户账户不存在"),
	CODE_30035011(ErrorCode.CODE_30035011, "预付款id不能为空"),
	CODE_30035012(ErrorCode.CODE_30035012, "撤销备注不能为空,最多一百个字符"),
	CODE_30035013(ErrorCode.CODE_30035013, "预付款信息不存在"),

	// 印章管理相关
	CODE_30036001(ErrorCode.CODE_30036001, "主账号未通过企业认证,请先通过认证页面进行企业认证后再创建印章") ,
	CODE_30036002(ErrorCode.CODE_30036002, "印章使用者不存在或不属于当前主账号的子账号") ,
	CODE_30036003(ErrorCode.CODE_30036003, "子账号不存在") ,
	CODE_30036004(ErrorCode.CODE_30036004, "子账号未通过个人实名认证,请子账号用户先通过实名认证页面进行个人实名认证后再将其指定为印章使用者") ,
	CODE_30036005(ErrorCode.CODE_30036005, "关联业务必须选择对应的印章使用者") ,
	CODE_30036006(ErrorCode.CODE_30036006, "当前业务已经被其他印章关联") ,
	CODE_30036007(ErrorCode.CODE_30036007, "当前主账号未关联任何子账号,请先关联子账号后再指定印章使用者") ,
	CODE_30036008(ErrorCode.CODE_30036008, "印章名称不能为空") ,
	CODE_30036009(ErrorCode.CODE_30036009, "印章名称长度必须在1-50个字符之间") ,
	CODE_30036010(ErrorCode.CODE_30036010, "印章样式不能为空") ,
	CODE_30036011(ErrorCode.CODE_30036011, "下方横排文字长度必须在1-50个字符之间") ,
	CODE_30036012(ErrorCode.CODE_30036012, "信息编码长度必须在1-50个字符之间") ,
	CODE_30036013(ErrorCode.CODE_30036013, "印章不存在") ,
	CODE_30036014(ErrorCode.CODE_30036014, "存在签署中的单据，暂不支持修改") ,
	CODE_30036015(ErrorCode.CODE_30036015, "该印章存在签署中的业务单据，不可删除") ,
	CODE_30036016(ErrorCode.CODE_30036016, "发起签署失败，请修改印章使用者") ,
	CODE_30036017(ErrorCode.CODE_30036017, "失效印章不能进行修改") ,
	CODE_30036018(ErrorCode.CODE_30036018, "一个业务的签章使用者最多选择10个人") ,
	CODE_30036019(ErrorCode.CODE_30036019, "模块不能为空") ,
	CODE_30036020(ErrorCode.CODE_30036020, "模块类型错误") ,
	CODE_30036021(ErrorCode.CODE_30036021, "请先完成电子签章授权") ,

	// 设备相关
	CODE_30037001(ErrorCode.CODE_30037001, "设备名称不能为空"),
	CODE_30037002(ErrorCode.CODE_30037002, "设备名称不超过40个字"),
	CODE_30037003(ErrorCode.CODE_30037003, "设备类型不能为空"),
	CODE_30037004(ErrorCode.CODE_30037004, "状态不能为空"),
	CODE_30037005(ErrorCode.CODE_30037005, "SMI卡号不能为空"),
	CODE_30037006(ErrorCode.CODE_30037006, "SMI卡号格式错误"),
	CODE_30037007(ErrorCode.CODE_30037007, "序列号不能为空"),
	CODE_30037008(ErrorCode.CODE_30037008, "序列号默认9个字符限制"),
	CODE_30037009(ErrorCode.CODE_30037009, "验证码不能为空"),
	CODE_30037010(ErrorCode.CODE_30037010, "设备不存在"),
	CODE_30037011(ErrorCode.CODE_30037011, "只有设备状态为非运行状态才能删除"),
	CODE_30037012(ErrorCode.CODE_30037012, "SMI卡号已存在"),
	CODE_30037013(ErrorCode.CODE_30037013, "序列号已存在"),
	CODE_30037014(ErrorCode.CODE_30037014, "状态为禁用才能启用"),
	CODE_30037015(ErrorCode.CODE_30037015, "状态为启用才能禁用"),
	CODE_30037016(ErrorCode.CODE_30037016, "运输工具id不能为空"),
	CODE_30037017(ErrorCode.CODE_30037017, "运输工具类型不能为空"),
	CODE_30037018(ErrorCode.CODE_30037018, "位置不能为空"),
	CODE_30037019(ErrorCode.CODE_30037019, "设备已经被禁用"),
	CODE_30037020(ErrorCode.CODE_30037020, "已存在GPS设备"),
	CODE_30037021(ErrorCode.CODE_30037021, "注册码不存在"),
	CODE_30037022(ErrorCode.CODE_30037022, "宇视平台内部服务器错误"),
	CODE_30037023(ErrorCode.CODE_30037023, "宇视平台接口调用失败"),
	CODE_30037024(ErrorCode.CODE_30037024, "该设备已绑定船舶，请先解绑船舶"),
	CODE_30037025(ErrorCode.CODE_30037025, "设备类型不存在"),
	CODE_30037026(ErrorCode.CODE_30037026, "设备不在线"),
	CODE_30037027(ErrorCode.CODE_30037027, "验证码格式错误"),
	CODE_30037028(ErrorCode.CODE_30037028, "位置不能超过20个字符"),
	CODE_30037029(ErrorCode.CODE_30037029, "船舶未认证"),
	CODE_30037030(ErrorCode.CODE_30037030, "安装人最多10个字符长度"),
	CODE_30037031(ErrorCode.CODE_30037031, "安装地点最多32个字符长度"),
	CODE_30037032(ErrorCode.CODE_30037032, "图片向量不能为空"),
	CODE_30037033(ErrorCode.CODE_30037033, "图片名称不能为空"),
	CODE_30037034(ErrorCode.CODE_30037034, "抓拍图片不能为空"),
	CODE_30037035(ErrorCode.CODE_30037035, "图片标记不能为空"),
	CODE_30037036(ErrorCode.CODE_30037036, "模型数据id不能为空"),
	CODE_30037037(ErrorCode.CODE_30037037, "模型数据标签不能为空"),
	CODE_30037038(ErrorCode.CODE_30037038, "模型数据不存在"),
	CODE_30037039(ErrorCode.CODE_30037039, "识别记录不存在"),
	CODE_30037040(ErrorCode.CODE_30037040, "识别记录已加入模型"),
	CODE_30037041(ErrorCode.CODE_30037041, "已进入契约锁认证不支持取消"),
	CODE_30037042(ErrorCode.CODE_30037042, "机构认证还未通过或未进行认证"),
	CODE_30037043(ErrorCode.CODE_30037043, "数据不能为空"),

	// 定制管理相关
	CODE_30038001(ErrorCode.CODE_30038001, "平台用户id不能为空"),
	CODE_30038002(ErrorCode.CODE_30038002, "后台id不能为空") ,
	CODE_30038003(ErrorCode.CODE_30038003, "系统名称不能为空") ,
	CODE_30038004(ErrorCode.CODE_30038004, "系统名称长度最大为20") ,
	CODE_30038005(ErrorCode.CODE_30038005, "系统简称长度最大为10") ,
	CODE_30038006(ErrorCode.CODE_30038006, "系统头部slogan不能为空") ,
	CODE_30038007(ErrorCode.CODE_30038007, "系统头部slogan长度最大为20") ,
	CODE_30038008(ErrorCode.CODE_30038008, "AI助理名称长度最大为20") ,
	CODE_30038009(ErrorCode.CODE_30038009, "服务热线不能为空") ,
	CODE_30038010(ErrorCode.CODE_30038010, "服务热线长度最大为20") ,
	CODE_30038011(ErrorCode.CODE_30038011, "版权信息不能为空") ,
	CODE_30038012(ErrorCode.CODE_30038012, "版权信息长度最大为50") ,
	CODE_30038013(ErrorCode.CODE_30038013, "access_key长度最大为50") ,
	CODE_30038014(ErrorCode.CODE_30038014, "secret_key长度最大为50") ,
	CODE_30038015(ErrorCode.CODE_30038015, "登录短信模板ID长度最大为20") ,
	CODE_30038016(ErrorCode.CODE_30038016, "私有域名长度最大为20") ,
	CODE_30038017(ErrorCode.CODE_30038017, "custom信息不能为空") ,
	CODE_30038018(ErrorCode.CODE_30038018, "user信息不能为空") ,
	CODE_30038019(ErrorCode.CODE_30038019, "用户pc端登录页logo不能为空") ,
	CODE_30038020(ErrorCode.CODE_30038020, "用户pc端登录页登录图片不能为空") ,
	CODE_30038021(ErrorCode.CODE_30038021, "用户pc端公众页头部LOGO不能为空") ,
	CODE_30038022(ErrorCode.CODE_30038022, "用户pc端公众页首页背景图不能为空") ,
	CODE_30038023(ErrorCode.CODE_30038023, "用户pc端公众页AI形象不能为空") ,
	CODE_30038024(ErrorCode.CODE_30038024, "用户pc端公众页底部LOGO不能为空") ,
	CODE_30038025(ErrorCode.CODE_30038025, "用户pc端控制台头部LOGO不能为空") ,
	CODE_30038026(ErrorCode.CODE_30038026, "用户pc端控制台账号头像不能为空") ,
	CODE_30038027(ErrorCode.CODE_30038027, "管理后台登录页登录页LOGO不能为空") ,
	CODE_30038028(ErrorCode.CODE_30038028, "管理后台登录页登录图片不能为空") ,
	CODE_30038029(ErrorCode.CODE_30038029, "管理后台登录页头部导航栏LOGO不能为空") ,
	CODE_30038030(ErrorCode.CODE_30038030, "企业定制表id不能为空") ,
	CODE_30038031(ErrorCode.CODE_30038031, "ICP备案信息不能为空") ,
	CODE_30038032(ErrorCode.CODE_30038032, "ICP备案信息长度最大为20") ,
	CODE_30038033(ErrorCode.CODE_30038033, "联网备案信息不能为空") ,
	CODE_30038034(ErrorCode.CODE_30038034, "联网备案信息长度最大为20") ,
	CODE_30038035(ErrorCode.CODE_30038035, "该用户已被关联，请勿重复操作") ,
	CODE_30038036(ErrorCode.CODE_30038036, "短信签名长度最大为12") ,
	CODE_30038037(ErrorCode.CODE_30038037, "用户pc端公众页AI头像不能为空") ,
	CODE_30038038(ErrorCode.CODE_30038038, "企业样式信息不存在") ,
	CODE_30038039(ErrorCode.CODE_30038039, "企业样式信息已存在") ,
	CODE_30038040(ErrorCode.CODE_30038040, "企业定制表不存在") ,
	CODE_30038041(ErrorCode.CODE_30038041, "成员数量最大为10") ,
	CODE_30038042(ErrorCode.CODE_30038042, "成员已存在") ,
	CODE_30038043(ErrorCode.CODE_30038043, "成员不存在") ,

	// 关于我们相关
	CODE_30039001(ErrorCode.CODE_30039001, "文章id不能为空"),
	CODE_30039002(ErrorCode.CODE_30039002, "内容富文本不能为空"),
	CODE_30039003(ErrorCode.CODE_30039003, "属于哪个app不能为空"),
	CODE_30039004(ErrorCode.CODE_30039004, "文章类型不能为空"),
	CODE_30039005(ErrorCode.CODE_30039005, "同一账号下同一类型不能重复添加"),

	// 文件相关
	CODE_30040001(ErrorCode.CODE_30040001, "文件最大为100M"),
	CODE_30040002(ErrorCode.CODE_30040002, "文件不存在"),
	CODE_30040003(ErrorCode.CODE_30040003, "该导入模板不存在"),

	//常跑航线相关
	CODE_30041001(ErrorCode.CODE_30041001, "该常跑航线不存在"),
	CODE_30041002(ErrorCode.CODE_30041002, "常跑航线最多只能传入3条"),

	// 船舶已经存在生效推广
	CODE_30042001(ErrorCode.CODE_30042001, "船舶已经存在生效推广"),
	CODE_30042002(ErrorCode.CODE_30042002, "推广记录状态为失效,不允许撤销激活"),
	CODE_30042003(ErrorCode.CODE_30042003, "撤销理由不能超过200个字符"),
	CODE_30042004(ErrorCode.CODE_30042004, "推广记录不存在"),

	// 监控预警记录相关
	CODE_30043001(ErrorCode.CODE_30043001, "监控预警记录不存在"),
	CODE_30043002(ErrorCode.CODE_30043002, "已处理状态不可再指派"),
	CODE_30043003(ErrorCode.CODE_30043003, "已处理状态不可二次处理"),
	CODE_30043004(ErrorCode.CODE_30043004, "监控预警记录id不能为空"),
	CODE_30043005(ErrorCode.CODE_30043005, "备注不能超过200个字符"),
	CODE_30043006(ErrorCode.CODE_30043006, "请确认是否误报"),
	CODE_30043007(ErrorCode.CODE_30043007, "未处理状态不可修改"),
	CODE_30043008(ErrorCode.CODE_30043008, "备注不能为空"),
	CODE_30043009(ErrorCode.CODE_30043009, "当前用户不是处理人，无修改权限"),
	CODE_30043010(ErrorCode.CODE_30043010, "不是监控预警记录的处理人，不能处理"),

	// 船运定金
	CODE_30044001(ErrorCode.CODE_30044001, "船运定金不存在"),
	CODE_30044002(ErrorCode.CODE_30044002, "当前状态不允许重新发起转账"),
	CODE_30044003(ErrorCode.CODE_30044003, "结算日期不能为空"),
	CODE_30044004(ErrorCode.CODE_30044004, "结算凭证不能为空"),
	CODE_30044005(ErrorCode.CODE_30044005, "待确认状态才能进行确认"),
	CODE_30044006(ErrorCode.CODE_30044006, "待结算状态才能进行结算"),

	// 订单流水详情
	CODE_30045001(ErrorCode.CODE_30045001, "支付类型不能为空"),
	CODE_30045002(ErrorCode.CODE_30045002, "业务类型不能为空"),
	CODE_30045003(ErrorCode.CODE_30045003, "支付金额不能为空"),
	CODE_30045004(ErrorCode.CODE_30045004, "当前订单不是未支付状态"),
	CODE_30045005(ErrorCode.CODE_30045005, "支付明细字符长度1-255个字符"),
	CODE_30045006(ErrorCode.CODE_30045006, "描述不能为空"),
	CODE_30045007(ErrorCode.CODE_30045007, "描述字符长度为1-255个字符"),
	CODE_30045008(ErrorCode.CODE_30045008, "明细不能为空"),
	CODE_30045009(ErrorCode.CODE_30045009, "订单不存在"),
	CODE_30045010(ErrorCode.CODE_30045010, "支付类型不存在"),
	CODE_30045011(ErrorCode.CODE_30045011, "业务类型不存在"),
	CODE_30045012(ErrorCode.CODE_30045012, "来源不能为空"),
	CODE_30045013(ErrorCode.CODE_30045013, "来源不存在"),
	CODE_30045014(ErrorCode.CODE_30045014, "类型不存在"),

	// 码头相关
	CODE_30046001(ErrorCode.CODE_30046001, "码头不存在"),
	CODE_30046002(ErrorCode.CODE_30046002, "码头名称不能为空 长度为1-40个字符"),
	CODE_30046003(ErrorCode.CODE_30046003, "码头简称不能为空 长度为1-20个字符"),
	CODE_30046004(ErrorCode.CODE_30046004, "码头面积长度最多为16位"),
	CODE_30046005(ErrorCode.CODE_30046005, "联系人姓名长度最大为10位"),
	CODE_30046006(ErrorCode.CODE_30046006, "联系人手机号长度最大为16位"),
	CODE_30046007(ErrorCode.CODE_30046007, "省编码不能为空"),
	CODE_30046008(ErrorCode.CODE_30046008, "城市编码不能为空"),
	CODE_30046009(ErrorCode.CODE_30046009, "区域编码不能为空"),
	CODE_30046010(ErrorCode.CODE_30046010, "详细地址不能为空"),
	CODE_30046011(ErrorCode.CODE_30046011, "经纬度不能为空"),
	CODE_30046012(ErrorCode.CODE_30046012, "简介长度最大为256位"),
	CODE_30046013(ErrorCode.CODE_30046013, "地址全称不能为空"),
	CODE_30046014(ErrorCode.CODE_30046014, "码头id不能为空"),

	// 砂石资讯相关
	CODE_30047001(ErrorCode.CODE_30047001, "数据不存在"),
	CODE_30047002(ErrorCode.CODE_30047002, "标题不能为空 长度为1-50个字符"),
	CODE_30047003(ErrorCode.CODE_30047003, "发布日期不能为空"),
	CODE_30047004(ErrorCode.CODE_30047004, "品类不能为空"),
	CODE_30047005(ErrorCode.CODE_30047005, "来源长度最大为10个字符"),
	CODE_30047006(ErrorCode.CODE_30047006, "类型不能为空"),
	CODE_30047007(ErrorCode.CODE_30047007, "封面不能为空"),
	CODE_30047008(ErrorCode.CODE_30047008, "删除时 主键id不能为空"),
	CODE_30047009(ErrorCode.CODE_30047009, "删除时 要取消激活的文件id不能为空"),
	CODE_30047010(ErrorCode.CODE_30047010, "分类不能为空"),
	CODE_30047011(ErrorCode.CODE_30047011, "收费类型不能为空"),
	CODE_30047012(ErrorCode.CODE_30047012, "标签数量超过10个"),
	CODE_30047013(ErrorCode.CODE_30047013, "品类不存在"),
	CODE_30047014(ErrorCode.CODE_30047014, "类别不能为空"),
	CODE_30047015(ErrorCode.CODE_30047015, "该资讯被广告占用，暂不支持下架"),

	//部门相关
	CODE_30048001(ErrorCode.CODE_30048001, "部门名称重复"),
	CODE_30048002(ErrorCode.CODE_30048002, "部门名称不能为空"),
	CODE_30048003(ErrorCode.CODE_30048003, "上级部门id不能为空"),
	CODE_30048004(ErrorCode.CODE_30048004, "部门名称长度最大为64"),
	CODE_30048005(ErrorCode.CODE_30048005, "排序不能为空"),
	CODE_30048006(ErrorCode.CODE_30048006, "部门不存在"),
	CODE_30048007(ErrorCode.CODE_30048007, "部门下有子部门不能删除"),
	CODE_30048008(ErrorCode.CODE_30048008, "部门下有员工不能删除"),
	CODE_30048009(ErrorCode.CODE_30048009, "被拖动部门id不能为空"),
	CODE_30048010(ErrorCode.CODE_30048010, "目标部门id不能为空"),
	CODE_30048011(ErrorCode.CODE_30048011, "拖动类型不能为空"),
	CODE_30048012(ErrorCode.CODE_30048012, "拖动类型只能为0,1,2"),

	CODE_30049001(ErrorCode.CODE_30049001, "客户状态不能为空"),
	CODE_30049002(ErrorCode.CODE_30049002, "客户id不能为空"),
	CODE_30049003(ErrorCode.CODE_30049003, "客户不存在"),
	CODE_30049004(ErrorCode.CODE_30049004, "采购方用户暂未授权电子签"),
	CODE_30049005(ErrorCode.CODE_30049005, "销售方用户暂未授权电子签"),
	// 此客户有业务数据，不支持变更身份
	CODE_30049006(ErrorCode.CODE_30049006, "此客户有业务数据，不支持变更身份"),

	//客户修改头像
	CODE_30050001(ErrorCode.CODE_30050001, "文件Id不能为空"),

	// 客户登录
	CODE_30051001(ErrorCode.CODE_30051001, "客户微信昵称不能为空"),
	CODE_30051002(ErrorCode.CODE_30051002, "客户微信openid不能为空"),
	CODE_30051003(ErrorCode.CODE_30051003, "客户微信unionid不能为空"),
	CODE_30051004(ErrorCode.CODE_30051004, "客户身份编码不能为空"),
	CODE_30051005(ErrorCode.CODE_30051005, "客户微信昵称长度最大为64"),
	CODE_30051006(ErrorCode.CODE_30051006, "客户微信openid长度最大为64"),
	CODE_30051007(ErrorCode.CODE_30051007, "客户微信unionid长度最大为64"),
	CODE_30051008(ErrorCode.CODE_30051008, "客户手机号码格式不正确"),
	CODE_30051009(ErrorCode.CODE_30051009, "客户身份编码不正确"),
	CODE_30051010(ErrorCode.CODE_30051010, "客户在系统中已存在相同身份的账号"),
	CODE_30051011(ErrorCode.CODE_30051011, "客户信息不存在"),
	CODE_30051012(ErrorCode.CODE_30051012, "客户该身份不可用"),

	// 客户银行账户
	CODE_30052001(ErrorCode.CODE_30052001, "账户类型不能为空"),
	CODE_30052002(ErrorCode.CODE_30052002, "账户类型只能为1或2"),
	CODE_30052003(ErrorCode.CODE_30052003, "开户名称不能为空"),
	CODE_30052004(ErrorCode.CODE_30052004, "开户名称长度最大为255"),
	CODE_30052005(ErrorCode.CODE_30052005, "银行账户/银行卡号不能为空"),
	CODE_30052006(ErrorCode.CODE_30052006, "银行账户/银行卡号规则不正确"),
	CODE_30052007(ErrorCode.CODE_30052007, "开户行不能为空"),
	CODE_30052008(ErrorCode.CODE_30052008, "开户行长度最大为128"),
	CODE_30052009(ErrorCode.CODE_30052009, "银行账户不能为空"),
	CODE_30052010(ErrorCode.CODE_30052010, "超出维护数量"),
	CODE_30052011(ErrorCode.CODE_30052011, "银行账户无效"),

	// 客户认证
	CODE_30053001(ErrorCode.CODE_30053001, "当前客户已进行过个人实名认证操作,请核实后重新再试!"),
	CODE_30053002(ErrorCode.CODE_30053002, "未查询到客户实名认证信息"),
	CODE_30053003(ErrorCode.CODE_30053003, "请求查询客户实名认证信息失败"),
	CODE_30053004(ErrorCode.CODE_30053004, "当前客户未进行过个人实名认证操作,请首先进行个人实名认证的操作!"),
	CODE_30053005(ErrorCode.CODE_30053005, "当前客户已进行过企业实名认证操作并且还在有效期内,请核实后重新再试!"),
	CODE_30053006(ErrorCode.CODE_30053006, "当前客户未进行过企业实名认证操作,请首先进行企业实名认证的操作!"),
	CODE_30053007(ErrorCode.CODE_30053007, "请求查询客户企业实名认证信息失败"),
	CODE_30053008(ErrorCode.CODE_30053008, "机构名称长度不能超过255位"),

	// 客户地址
	CODE_30053009(ErrorCode.CODE_30053009, "联系人不能为空"),
	CODE_30053010(ErrorCode.CODE_30053010, "联系人长度不能超过25位"),
	CODE_30053011(ErrorCode.CODE_30053011, "手机号不能为空"),
	CODE_30053012(ErrorCode.CODE_30053012, "手机号格式不正确"),
	CODE_30053013(ErrorCode.CODE_30053013, "区号长度不能超过4位不能短于3位"),
	CODE_30053014(ErrorCode.CODE_30053014, "区号格式不正确"),
	CODE_30053015(ErrorCode.CODE_30053015, "座机号码长度不能超过8位不能短于7位"),
	CODE_30053016(ErrorCode.CODE_30053016, "座机号码格式不正确"),
	CODE_30053017(ErrorCode.CODE_30053017, "座机分机号码长度不能超过4位不能短于3位"),
	CODE_30053018(ErrorCode.CODE_30053018, "座机分机号码格式不正确"),
	CODE_30053019(ErrorCode.CODE_30053019, "省份不能为空"),
	CODE_30053020(ErrorCode.CODE_30053020, "省份长度不能超过10位"),
	CODE_30053021(ErrorCode.CODE_30053021, "市不能为空"),
	CODE_30053022(ErrorCode.CODE_30053022, "市长度不能超过25位"),
	CODE_30053023(ErrorCode.CODE_30053023, "区不能为空"),
	CODE_30053024(ErrorCode.CODE_30053024, "区长度不能超过25位"),
	CODE_30053025(ErrorCode.CODE_30053025, "详细地址不能为空"),
	CODE_30053026(ErrorCode.CODE_30053026, "详细地址长度不能超过128位"),
	CODE_30053027(ErrorCode.CODE_30053027, "邮政编码长度不能超过6位"),
	CODE_30053028(ErrorCode.CODE_30053028, "邮政编码格式不正确"),
	CODE_30053029(ErrorCode.CODE_30053029, "是否默认不能为空"),
	CODE_30053030(ErrorCode.CODE_30053030, "默认地址标识不正确, 只能为是或否"),
	CODE_30053031(ErrorCode.CODE_30053031, "已存在默认地址"),
	CODE_30053032(ErrorCode.CODE_30053032, "当前客户未进行企业认证"),
	CODE_30053033(ErrorCode.CODE_30053033, "当前所添加的机构名称必须与组织机构认证主体名称保持一致"),
	CODE_30053034(ErrorCode.CODE_30053034, "地址不存在"),
	CODE_30053035(ErrorCode.CODE_30053035, "客户id不能为空"),
	CODE_30053036(ErrorCode.CODE_30053036, "手机号长度不能超过11位"),

	// 客户发票
	CODE_30054001(ErrorCode.CODE_30054001, "抬头类型不能为空"),
	CODE_30054002(ErrorCode.CODE_30054002, "抬头类型只能为1或2"),
	CODE_30054003(ErrorCode.CODE_30054003, "抬头类型为企业时发票类型不能为空"),
	CODE_30054004(ErrorCode.CODE_30054004, "发票类型只能为1或2"),
	CODE_30054005(ErrorCode.CODE_30054005, "发票抬头不能为空"),
	CODE_30054006(ErrorCode.CODE_30054006, "发票抬头长度最大为55"),
	CODE_30054007(ErrorCode.CODE_30054007, "纳税人识别号规则不正确"),
	CODE_30054008(ErrorCode.CODE_30054008, "地址长度最大为128"),
	CODE_30054009(ErrorCode.CODE_30054009, "开户行长度最大为128"),
	CODE_30054010(ErrorCode.CODE_30054010, "对公账户银行账户不正确"),
	CODE_30054011(ErrorCode.CODE_30054011, "纳税人识别号不能为空"),
	CODE_30054012(ErrorCode.CODE_30054012, "地址不能为空"),
	CODE_30054013(ErrorCode.CODE_30054013, "手机号不能为空"),
	CODE_30054014(ErrorCode.CODE_30054014, "开户行不能为空"),
	CODE_30054015(ErrorCode.CODE_30054015, "对公账户不能为空"),
	CODE_30054016(ErrorCode.CODE_30054016, "是否默认抬头只能为0或1"),
	CODE_30054017(ErrorCode.CODE_30054017, "发票不存在"),
	CODE_30054018(ErrorCode.CODE_30054018, "超出维护数量"),

	// 子账号管理相关
	CODE_30055001(ErrorCode.CODE_30055001, "受邀人手机号不能为空"),
	CODE_30055002(ErrorCode.CODE_30055002, "受邀人可用身份不能为空"),
	CODE_30055003(ErrorCode.CODE_30055003, "请先前往企业认证页面完成企业认证后再试"),
	CODE_30055004(ErrorCode.CODE_30055004, "请勿邀请自己作为子账号加入企业"),
	CODE_30055005(ErrorCode.CODE_30055005, "该子账号已加入10个企业无法继续加入,请核实后再试"),
	CODE_30055006(ErrorCode.CODE_30055006, "您已经邀请过该用户，请勿重复邀请"),
	CODE_30055007(ErrorCode.CODE_30055007, "子账号不存在"),
	CODE_30055008(ErrorCode.CODE_30055008, "您加入的企业数量已达上限,无法继续加入,请核实后再试"),
	CODE_30055009(ErrorCode.CODE_30055009, "子账号可用身份中不包含承运商"),
	CODE_30055010(ErrorCode.CODE_30055010, "已确认的邀请记录不允许删除"),
	CODE_30055011(ErrorCode.CODE_30055011, "您不是该企业的管理员,无权进行此操作"),
	CODE_30055012(ErrorCode.CODE_30055012, "只能移除已确认的子账号"),
	CODE_30055013(ErrorCode.CODE_30055013, "企业不存在"),
	CODE_30055014(ErrorCode.CODE_30055014, "您尚未加入到任何企业,请先加入企业后再切换"),
	CODE_30055015(ErrorCode.CODE_30055015, "您尚未加入到该企业或已被该企业移除,请核实后再试"),
	CODE_30055016(ErrorCode.CODE_30055016, "您没有该身份的权限,请核实后再试"),
	CODE_30055017(ErrorCode.CODE_30055017, "暂不支持以承运商的角色切换企业"),
	CODE_30055018(ErrorCode.CODE_30055018, "当前子账号数量已超过50个"),
	CODE_30055019(ErrorCode.CODE_30055019, "当前会员等级子账号数量已达上限"),
	CODE_30055020(ErrorCode.CODE_30055020, "开通或升级链云会员，继续使用子账号"),
	CODE_30055021(ErrorCode.CODE_30055021, "当前会员等级子账号数量已达上限，无法激活子账号"),
	CODE_30055022(ErrorCode.CODE_30055022, "该企业子账号已失效"),
	CODE_30055023(ErrorCode.CODE_30055023, "受邀人可用权限不能为空"),
	CODE_30055024(ErrorCode.CODE_30055024, "当前账号不是代理账号"),
	CODE_30055025(ErrorCode.CODE_30055025, "请先前往组织机构认证页面完成认证后再试"),

	// 认证相关
	CODE_30056001(ErrorCode.CODE_30056001, "当前存在已认证或认证中的企业,请勿重复操作!") ,
	CODE_30056002(ErrorCode.CODE_30056002, "该企业已经被认证或已在认证过程中") ,
	CODE_30056003(ErrorCode.CODE_30056003, "用户尚未完成个人实名认证,请先完成个人认证后再次重试!") ,
	CODE_30056004(ErrorCode.CODE_30056004, "用户已完成企业认证,请勿重复认证!") ,
	CODE_30056005(ErrorCode.CODE_30056005, "换绑手机号时企业认证需是同一企业认证才能换绑!") ,
	CODE_30056006(ErrorCode.CODE_30056006, "用户不在换绑手机号流程中!") ,
	CODE_30056007(ErrorCode.CODE_30056007, "此手机号已被使用!") ,


	// 个人认证
	CODE_30057001(ErrorCode.CODE_30057001, "真实姓名不能为空"),
	CODE_30057002(ErrorCode.CODE_30057002, "真实姓名长度不能超过25位"),
	CODE_30057003(ErrorCode.CODE_30057003, "身份证号码不能为空"),
	CODE_30057004(ErrorCode.CODE_30057004, "身份证号长度不能超过18位"),
	CODE_30057005(ErrorCode.CODE_30057005, "身份证号格式不正确"),
	CODE_30057006(ErrorCode.CODE_30057006, "认证完成跳转页面链接不能为空"),

	// 企业管理
	CODE_30058001(ErrorCode.CODE_30058001, "企业名称不能为空"),
	CODE_30058002(ErrorCode.CODE_30058002, "企业名称长度最大为255"),
	CODE_30058003(ErrorCode.CODE_30058003, "法定代表人不能为空"),
	CODE_30058004(ErrorCode.CODE_30058004, "法定代表人长度不能超过25位"),
	CODE_30058005(ErrorCode.CODE_30058005, "统一社会信用代码不能为空"),
	CODE_30058006(ErrorCode.CODE_30058006, "统一社会信用代码不正确"),
	CODE_30058007(ErrorCode.CODE_30058007, "统一社会信用代码长度不能超过18位"),
	CODE_30058008(ErrorCode.CODE_30058008, "组织机构类型不能为空"),

	// 登录相关
	CODE_30059001(ErrorCode.CODE_30059001, "获取微信小程序接口异常"),
	CODE_30059002(ErrorCode.CODE_30059002, "无效的code或者code已过期"),
	CODE_30059003(ErrorCode.CODE_30059003, "用户不存在"),
	CODE_30059004(ErrorCode.CODE_30059004, "此账号不存在"),
	CODE_30059005(ErrorCode.CODE_30059005, "账号或密码错误"),
	CODE_30059006(ErrorCode.CODE_30059006, "验证码校验未通过，请重新获取验证码"),
	CODE_30059007(ErrorCode.CODE_30059007, "用户身份不能为空"),
	CODE_30059008(ErrorCode.CODE_30059008, "您的账户已被禁用"),
	CODE_30059009(ErrorCode.CODE_30059009, "身份不可用"),
	CODE_30059010(ErrorCode.CODE_30059010, "未绑定微信"),
	CODE_30059011(ErrorCode.CODE_30059011, "该手机号已绑定微信"),
	CODE_30059012(ErrorCode.CODE_30059012, "该手机号正用于换绑手机号流程中，暂不能使用"),
	CODE_30059013(ErrorCode.CODE_30059013, "您的账号已被管理员禁用，暂时无法登录"),
	CODE_30059014(ErrorCode.CODE_30059014, "此手机号不存在"),
	CODE_30059015(ErrorCode.CODE_30059015, "您的手机号已被管理员禁用，暂时无法登录"),
	CODE_30059016(ErrorCode.CODE_30059016, "不允许登录"),
	CODE_30059017(ErrorCode.CODE_30059017, "账号或密码错误"),
	CODE_30059018(ErrorCode.CODE_30059018, "账号不能为空"),
	CODE_30059019(ErrorCode.CODE_30059019, "key不能为空"),
	CODE_30059020(ErrorCode.CODE_30059020, "新密码不能为空"),
	CODE_30059021(ErrorCode.CODE_30059021, "两次密码输入不一致"),
	CODE_30059022(ErrorCode.CODE_30059022, "新密码不能与旧密码相同"),
	CODE_30059023(ErrorCode.CODE_30059023, "请设置6-16位包含字母、数字的密码"),
	CODE_30059024(ErrorCode.CODE_30059024, "该手机号已被注册"),
	CODE_30059025(ErrorCode.CODE_30059025, "首次登录才能设置密码"),
	CODE_30059026(ErrorCode.CODE_30059026, "请重新设置密码"),
	// 请使用供应链账号登陆
	CODE_30059027(ErrorCode.CODE_30059027, "请使用供应链账号登陆"),

	//微信绑定相关
	CODE_30060001(ErrorCode.CODE_30060001, "该用户已绑定微信"),
	CODE_30060002(ErrorCode.CODE_30060002, "该微信已被绑定"),
	CODE_30060003(ErrorCode.CODE_30060003, "该用户未绑定微信"),

	// 邮箱绑定相关
	CODE_30061001(ErrorCode.CODE_30061001, "邮箱不能为空"),
	CODE_30061002(ErrorCode.CODE_30061002, "该邮箱已被绑定"),
	CODE_30061003(ErrorCode.CODE_30061003, "该用户已绑定邮箱"),









	// 品类指数相关
	CODE_30090001(ErrorCode.CODE_30090001, "版本id不能为空"),
	CODE_30090002(ErrorCode.CODE_30090002, "记录id不能为空"),
	CODE_30090003(ErrorCode.CODE_30090003, "品类不能为空"),
	CODE_30090004(ErrorCode.CODE_30090004, "价格信息不能为空，最多500条数据"),
	CODE_30090005(ErrorCode.CODE_30090005, "产地价格类型不能为空"),
	CODE_30090006(ErrorCode.CODE_30090006, "价格类型不能为空"),
	CODE_30090007(ErrorCode.CODE_30090007, "出货量的取值范围为0~999999999"),
	CODE_30090008(ErrorCode.CODE_30090008, "成交均价不能为空"),
	CODE_30090009(ErrorCode.CODE_30090009, "品类不存在"),
	CODE_30090010(ErrorCode.CODE_30090010, "价格类型已存在 不可重复维护"),
	CODE_30090011(ErrorCode.CODE_30090011, "待提交、已驳回状态才能进行提交、修改、删除操作"),
	CODE_30090012(ErrorCode.CODE_30090012, "没有权限"),
	CODE_30090013(ErrorCode.CODE_30090013, "旧数据品类id不能为空"),
	CODE_30090014(ErrorCode.CODE_30090014, "上传的数据不能为空"),
	CODE_30090015(ErrorCode.CODE_30090015, "上传的数据量超过最大限制 最多为500条"),
	CODE_30090016(ErrorCode.CODE_30090016, "有数据存在上船价数据为空的数据"),
	CODE_30090017(ErrorCode.CODE_30090017, "上船价的成交均价应大于等于0小于等于1000"),
	CODE_30090018(ErrorCode.CODE_30090018, "靠港价的成交均价应大于0小于等于1000"),

	// 砂石指数版本相关
	CODE_30091001(ErrorCode.CODE_30091001, "版本日期不能为空"),
	CODE_30091002(ErrorCode.CODE_30091002, "日期不能在今天之后"),
	CODE_30091003(ErrorCode.CODE_30091003, "版本日期已存在"),
	CODE_30091004(ErrorCode.CODE_30091004, "存在指数数据，不能修改"),
	CODE_30091005(ErrorCode.CODE_30091005, "版本不存在"),
	CODE_30091006(ErrorCode.CODE_30091006, "存在指数数据，不能删除"),

	// 砂石指数版本记录相关
	CODE_30092001(ErrorCode.CODE_30092001, "版本id不能为空"),
	CODE_30092002(ErrorCode.CODE_30092002, "砂石指数版本记录不存在"),
	CODE_30092003(ErrorCode.CODE_30092003, "指数数据为空，不能进行提交"),
	CODE_30092004(ErrorCode.CODE_30092004, "待发布、已撤回状态才能进行发布"),
	CODE_30092005(ErrorCode.CODE_30092005, "待发布、已撤回状态才能进行驳回"),
	CODE_30092006(ErrorCode.CODE_30092006, "已发布状态且是最新发布数据才能进行撤回"),
	CODE_30092007(ErrorCode.CODE_30092007, "当前已经是最初发布版本，无法进行撤回！"),
	CODE_30092008(ErrorCode.CODE_30092008, "待提交、已驳回状态才能进行提交、修改、删除操作"),
	CODE_30092009(ErrorCode.CODE_30092009, "待提交、已驳回状态才能进行保存"),
	CODE_30092010(ErrorCode.CODE_30092010, "没有权限"),

	// 砂石资讯相关
	CODE_30093001(ErrorCode.CODE_30093001, "删除时 主键id不能为空"),
	CODE_30093002(ErrorCode.CODE_30093002, "删除时 要取消激活的文件id不能为空"),

	// 商品相关
	CODE_30094001(ErrorCode.CODE_30094001, "商品id不能为空"),
	CODE_30094002(ErrorCode.CODE_30094002, "商品名称不能为空"),
	CODE_30094003(ErrorCode.CODE_30094003, "权重不能为空 取值范围为0~10000"),
	CODE_30094004(ErrorCode.CODE_30094004, "主图文件不能为空"),
	CODE_30094005(ErrorCode.CODE_30094005, "其他图片最多只能传五张"),
	CODE_30094006(ErrorCode.CODE_30094006, "视频文件必须上传"),
	CODE_30094007(ErrorCode.CODE_30094007, "品类不能为空"),
	CODE_30094008(ErrorCode.CODE_30094008, "规格不能为空"),
	CODE_30094009(ErrorCode.CODE_30094009, "最小细度模数不能为空 取值范围位0.001~99.999"),
	CODE_30094010(ErrorCode.CODE_30094010, "最大细度模数取值范围位0.001~99.999"),
	CODE_30094011(ErrorCode.CODE_30094011, "含泥量取值范围为1~99"),
	CODE_30094012(ErrorCode.CODE_30094012, "压碎值取值范围为1~99"),
	CODE_30094013(ErrorCode.CODE_30094013, "石粉MB值取值范围为1~99"),
	CODE_30094014(ErrorCode.CODE_30094014, "含水率取值范围为1~99"),
	CODE_30094015(ErrorCode.CODE_30094015, "细粉含量取值范围为1~99"),
	CODE_30094016(ErrorCode.CODE_30094016, "供货商不能为空"),
	CODE_30094017(ErrorCode.CODE_30094017, "起订条件不能为空"),
	CODE_30094018(ErrorCode.CODE_30094018, "开票说明 最大为32个字符"),
	CODE_30094019(ErrorCode.CODE_30094019, "提货周期 最大为12个字符"),
	CODE_30094023(ErrorCode.CODE_30094023, "详细地址不能为空"),
	CODE_30094024(ErrorCode.CODE_30094024, "经纬度不能为空"),
	CODE_30094025(ErrorCode.CODE_30094025, "商品详情介绍不能为空"),
	CODE_30094026(ErrorCode.CODE_30094026, "备注最大为100个字符"),
	CODE_30094027(ErrorCode.CODE_30094027, "供应商类型不存在"),
	CODE_30094028(ErrorCode.CODE_30094028, "价格类型不能为空"),
	CODE_30094029(ErrorCode.CODE_30094029, "价格类型不存在"),
	CODE_30094030(ErrorCode.CODE_30094030, "履约保证金额不能为空 取值范围为0.01~10000000000"),
	CODE_30094031(ErrorCode.CODE_30094031, "履约金百分比不能为空 取值范围为1~99"),
	CODE_30094032(ErrorCode.CODE_30094032, "单价不能为空 取值范围为0.01~10000"),
	CODE_30094033(ErrorCode.CODE_30094033, "起订金额不能为空 取值范围为0.01~10000000000"),
	CODE_30094034(ErrorCode.CODE_30094034, "起订吨数不能为空 取值范围为0.01~99999999.99"),
	CODE_30094035(ErrorCode.CODE_30094035, "供应商不存在"),
	CODE_30094036(ErrorCode.CODE_30094036, "当前状态不允许编辑"),
	CODE_30094037(ErrorCode.CODE_30094037, "当前状态不允许审核"),
	CODE_30094038(ErrorCode.CODE_30094038, "当前状态不允许下架"),
	CODE_30094039(ErrorCode.CODE_30094039, "当前状态不允许关闭"),
	CODE_30094040(ErrorCode.CODE_30094040, "未关闭的商品才支持绑定设备"),
	CODE_30094041(ErrorCode.CODE_30094041, "商品不存在"),
	CODE_30094042(ErrorCode.CODE_30094042, "该商品被广告占用，暂不支持下架"),




	// 品类相关
	CODE_30095001(ErrorCode.CODE_30095001, "品类代码不能为空，长度为6-10个字符"),
	CODE_30095002(ErrorCode.CODE_30095002, "品类名称不能为空, 长度为1-10个字符"),
	CODE_30095003(ErrorCode.CODE_30095003, "品类类型不能为空"),
	CODE_30095004(ErrorCode.CODE_30095004, "品类区域不能为空"),
	CODE_30095005(ErrorCode.CODE_30095005, "产地价格类型不能为空"),
	CODE_30095006(ErrorCode.CODE_30095006, "采区/矿厂全称不能为空 长度为1-40个字符"),
	CODE_30095007(ErrorCode.CODE_30095007, "采区/矿厂简称不能为空 长度为1-10个字符"),
	CODE_30095008(ErrorCode.CODE_30095008, "规格/细度模数不能为空 长度为1-15个字符"),
	CODE_30095009(ErrorCode.CODE_30095009, "品类介绍不能为空 最大为256个字符"),
	CODE_30095010(ErrorCode.CODE_30095010, "该品类代码已存在"),
	CODE_30095011(ErrorCode.CODE_30095011, "产地价格类型必须有默认"),
	CODE_30095012(ErrorCode.CODE_30095012, "产地价格类型只能有一个默认"),
	CODE_30095013(ErrorCode.CODE_30095013, "产地价格类型名称不能重复"),
	CODE_30095014(ErrorCode.CODE_30095014, "品类类型不存在"),
	CODE_30095015(ErrorCode.CODE_30095015, "品类区域不存在"),
	CODE_30095016(ErrorCode.CODE_30095016, "该品类关联未关闭的商品 无法删除"),
	CODE_30095017(ErrorCode.CODE_30095017, "存在重复标签"),

	// 项目相关
	CODE_30096001(ErrorCode.CODE_30096001, "项目类型不能为空 只能为1和2"),
	CODE_30096002(ErrorCode.CODE_30096002, "采购类型不能为空"),
	CODE_30096003(ErrorCode.CODE_30096003, "其他表的主键id不能为空"),
	CODE_30096004(ErrorCode.CODE_30096004, "项目名称不能为空 最大为1~30个字符"),
	CODE_30096005(ErrorCode.CODE_30096005, "项目介绍不能为空 最大为1~500个字符"),
	CODE_30096006(ErrorCode.CODE_30096006, "买方id不能为空"),
	CODE_30096007(ErrorCode.CODE_30096007, "意向id不能为空"),
	CODE_30096008(ErrorCode.CODE_30096008, "项目id不能为空"),
	CODE_30096009(ErrorCode.CODE_30096009, "用户不存在"),
	CODE_30096010(ErrorCode.CODE_30096010, "用户身份不能为空"),
	CODE_30096011(ErrorCode.CODE_30096011, "服务费不能为空"),
	CODE_30096012(ErrorCode.CODE_30096012, "项目不存在"),
	CODE_30096013(ErrorCode.CODE_30096013, "当前状态不允许指派"),
	CODE_30096014(ErrorCode.CODE_30096014, "当前状态不允许开始"),
	CODE_30096015(ErrorCode.CODE_30096015, "当前状态不允许点完成"),
	CODE_30096016(ErrorCode.CODE_30096016, "结算未完成,无法完成项目"),
	CODE_30096017(ErrorCode.CODE_30096017, "当前状态不允许关闭"),
	CODE_30096018(ErrorCode.CODE_30096018, "预估单价不能为空 取值范围 0.01-100亿 保留两位小数"),
	CODE_30096019(ErrorCode.CODE_30096019, "冻结金额不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096020(ErrorCode.CODE_30096020, "供应链预估单价不能为空 取值范围 0.01-100亿 保留两位小数"),
	CODE_30096021(ErrorCode.CODE_30096021, "供应链冻结金额不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096022(ErrorCode.CODE_30096022, "供应链合同金额不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096023(ErrorCode.CODE_30096023, "供应链最低付款额度不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096024(ErrorCode.CODE_30096024, "项目名称不支持重名"),
	CODE_30096025(ErrorCode.CODE_30096025, "购买意向不存在"),
	CODE_30096026(ErrorCode.CODE_30096026, "意向采购吨数不能为空 且必须大于起订吨数"),
	CODE_30096027(ErrorCode.CODE_30096027, "买方预估单价不能为空 取值范围 0.01-100亿 保留两位小数"),
	CODE_30096028(ErrorCode.CODE_30096028, "买方冻结金额不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096029(ErrorCode.CODE_30096029, "买方合同金额不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096030(ErrorCode.CODE_30096030, "买方最低付款额度不能为空 取值范围 0-100亿 保留两位小数"),
	CODE_30096031(ErrorCode.CODE_30096031, "服务费索引参数非法"),
	CODE_30096032(ErrorCode.CODE_30096032, "服务费信息缺少,请填写完整"),
	CODE_30096033(ErrorCode.CODE_30096033, "只支持线上项目进行导出"),
	CODE_30096034(ErrorCode.CODE_30096034, "只支持状态为交付中和已完成的项目进行导出"),
	CODE_30096035(ErrorCode.CODE_30096035, "采购商id不能为空"),
	CODE_30096036(ErrorCode.CODE_30096036, "租户id不能为空"),
	CODE_30096037(ErrorCode.CODE_30096037, "存在作废中的数据，请先处理"),
	CODE_30096038(ErrorCode.CODE_30096038, "项目已完成 不允许新增"),
	CODE_30096039(ErrorCode.CODE_30096039, "项目已完成才能新增"),
	CODE_30096040(ErrorCode.CODE_30096040, "已完成状态才能进行删除"),
	CODE_30096041(ErrorCode.CODE_30096041, "线下作废请联系平台处理"),
	CODE_30096042(ErrorCode.CODE_30096042, "供应商需完成电子签章授权"),
	CODE_30096043(ErrorCode.CODE_30096043, "供应商、联合购销商需完成电子签章授权"),
	CODE_30096044(ErrorCode.CODE_30096044, "存在关联项目，不允许删除"),

	// 签收单相关
	CODE_30097001(ErrorCode.CODE_30097001, "签收单不存在"),
	CODE_30097002(ErrorCode.CODE_30097002, "文章类型不能为空"),
	CODE_30097003(ErrorCode.CODE_30097003, "签收单吨数不能为空"),
	CODE_30097004(ErrorCode.CODE_30097004, "买方认证信息不存在"),
	CODE_30097005(ErrorCode.CODE_30097005, "签收确认日期不能为空"),
	CODE_30097006(ErrorCode.CODE_30097006, "非草稿状态下不能修改"),
	CODE_30097007(ErrorCode.CODE_30097007, "非草稿状态不能删除"),
	CODE_30097008(ErrorCode.CODE_30097008, "类型不能为空"),
	CODE_30097009(ErrorCode.CODE_30097009, "只有线下已完成的项目才允许撤回"),
	CODE_30097010(ErrorCode.CODE_30097010, "买方客户不存在"),
	CODE_30097011(ErrorCode.CODE_30097011, "项目状态为已完成或者已关闭,不能加入"),
	CODE_30097012(ErrorCode.CODE_30097012, "关联签收单id不能为空"),
	CODE_30097013(ErrorCode.CODE_30097013, "关联提货单不能为空"),
	CODE_30097014(ErrorCode.CODE_30097014, "采购商已在项目中,加入失败"),
	CODE_30097015(ErrorCode.CODE_30097015, "对账状态为对账中或已对账的状态无法关闭"),
	CODE_30097016(ErrorCode.CODE_30097016, "签收日期不能为空"),
	CODE_30097017(ErrorCode.CODE_30097017, "预估单价不能为空"),
	CODE_30097018(ErrorCode.CODE_30097018, "已完成的状态才能删除"),
	CODE_30097019(ErrorCode.CODE_30097019, "先删除关联的对账单才能进行删除"),
	CODE_30097020(ErrorCode.CODE_30097020, "先删除被关联的签收单才能进行删除"),
	CODE_30097021(ErrorCode.CODE_30097021, "签收吨数不能为空"),
	CODE_30097022(ErrorCode.CODE_30097022, "签收id不能为空"),
	CODE_30097023(ErrorCode.CODE_30097023, "驳回原因长度最大为100"),
	CODE_30097024(ErrorCode.CODE_30097024, "待作废的签收单不允许关联对账单并且下游的签收单也不允许关联对账单"),
	CODE_30097025(ErrorCode.CODE_30097025, "签署方式不能为空"),
	CODE_30097026(ErrorCode.CODE_30097026, "备注不能超过200字符"),
	CODE_30097027(ErrorCode.CODE_30097027, "只有草稿或者驳回状态能删除"),
	CODE_30097028(ErrorCode.CODE_30097028, "签收重量不能为空"),
	CODE_30097029(ErrorCode.CODE_30097029, "该签收单已对账，不允许删除"),
	CODE_30097030(ErrorCode.CODE_30097030, "该签收单已被入库单关联，不允许删除"),
	CODE_30097031(ErrorCode.CODE_30097031, "签收单据不能为空"),
	CODE_30097032(ErrorCode.CODE_30097032, "下游采购商不是录入企业"),
	CODE_30097033(ErrorCode.CODE_30097033, "该签收单已对账，不允许删除"),

	// 合同相关
	CODE_30098001(ErrorCode.CODE_30098001, "合同名称不能为空"),
	CODE_30098002(ErrorCode.CODE_30098002, "合同名称长度不能超过32个字符"),
	CODE_30098003(ErrorCode.CODE_30098003, "甲方账号不能为空"),
	CODE_30098004(ErrorCode.CODE_30098004, "甲方公司不能为空"),
	CODE_30098005(ErrorCode.CODE_30098005, "乙方账号不能为空"),
	CODE_30098006(ErrorCode.CODE_30098006, "乙方公司不能为空"),
	CODE_30098007(ErrorCode.CODE_30098007, "签署模式不能为空"),
	CODE_30098008(ErrorCode.CODE_30098008, "合同类型不能为空"),
	CODE_30098009(ErrorCode.CODE_30098009, "合同文件id不能为空"),
	CODE_30098010(ErrorCode.CODE_30098010, "提交方式不能为空"),
	CODE_30098011(ErrorCode.CODE_30098011, "合同编号或合同名称搜索不得超过32个字符!"),
	CODE_30098012(ErrorCode.CODE_30098012, "项目[%s]不存在"),
	CODE_30098013(ErrorCode.CODE_30098013, "只有线上签署的合同才能进行修改!"),
	CODE_30098014(ErrorCode.CODE_30098014, "只有草稿状态的合同才能进行修改!"),
	CODE_30098015(ErrorCode.CODE_30098015, "只有签署中状态的合同才能进行撤回!"),
	CODE_30098016(ErrorCode.CODE_30098016, "只有线上签署的合同才能进行撤回!"),
	CODE_30098017(ErrorCode.CODE_30098017, "只有草稿状态的合同才能进行删除!"),
	CODE_30098018(ErrorCode.CODE_30098018, "只有已完成状态的合同才能进行关闭!"),
	CODE_30098019(ErrorCode.CODE_30098019, "甲方客户已被禁用,不能创建合同,请核实后再试!"),
	CODE_30098020(ErrorCode.CODE_30098020, "乙方客户已被禁用,不能创建合同,请核实后再试!"),
	CODE_30098021(ErrorCode.CODE_30098021, "运价取值范围为0.01~1000"),
	CODE_30098022(ErrorCode.CODE_30098022, "乙方客户未进行企业认证,不能创建合同,请核实后再试!"),
	CODE_30098023(ErrorCode.CODE_30098023, "运价指数不存在"),
	CODE_30098024(ErrorCode.CODE_30098024, "乙方客户认证企业与传入企业不匹配!"),
	CODE_30098033(ErrorCode.CODE_30098033, "起始日期不能为空"),
	CODE_30098034(ErrorCode.CODE_30098034, "截止日期不能为空"),
	CODE_30098035(ErrorCode.CODE_30098035, "当前操作用户不是项目专员,不可新增"),
	CODE_30098036(ErrorCode.CODE_30098036, "当前操作用户不是项目专员,不可撤回"),
	CODE_30098037(ErrorCode.CODE_30098037, "甲方必须实名"),

	// 购买意向相关
	CODE_30099001(ErrorCode.CODE_30099001, "购买意向id不能为空"),
	CODE_30099002(ErrorCode.CODE_30099002, "指派服务专员id不能为空"),
	CODE_30099003(ErrorCode.CODE_30099003, "只有待确认状态可以确认"),
	CODE_30099004(ErrorCode.CODE_30099004, "只有待处理,已驳回,处理中,待确认状态的购买意向才能搁浅"),
	CODE_30099005(ErrorCode.CODE_30099005, "搁浅原因不能为空"),
	CODE_30099006(ErrorCode.CODE_30099006, "搁浅原因长度最大为64"),
	CODE_30099007(ErrorCode.CODE_30099007, "只有待指派,已指派,搁浅状态的购买意向才能指派"),
	CODE_30099008(ErrorCode.CODE_30099008, "联合采购类型 可出资比例和下游出资金额不能为空"),
	CODE_30099009(ErrorCode.CODE_30099009, "项目在交付状态才可以生成签收单"),
	CODE_30099010(ErrorCode.CODE_30099010, "采购类型只有1或2"),
	CODE_30099011(ErrorCode.CODE_30099011, "未通过企业认证不能进行操作"),
	CODE_30099012(ErrorCode.CODE_30099012, "采购数量不能为空"),
	CODE_30099013(ErrorCode.CODE_30099013, "总价不能为空"),
	CODE_30099014(ErrorCode.CODE_30099014, "请确认存在关联下游采购商签收单已驳回"),
	CODE_30099015(ErrorCode.CODE_30099015, "草稿状态下不存在联合-采购商的签收单才能删除"),
	CODE_30099016(ErrorCode.CODE_30099016, "勾选的对账单中包含已开票的数据,请重新勾选"),
	CODE_30099017(ErrorCode.CODE_30099017, "您没有查看监控的权限,如需查看监控,请购买或升级会员"),
	CODE_30099019(ErrorCode.CODE_30099019, "采购说明长度最大为128"),
	CODE_30099020(ErrorCode.CODE_30099020, "省编码长度最大为16"),
	CODE_30099021(ErrorCode.CODE_30099021, "市编码长度最大为16"),
	CODE_30099022(ErrorCode.CODE_30099022, "区域编码长度最大为16"),
	CODE_30099023(ErrorCode.CODE_30099023, "联系人长度最大为64"),
	CODE_30099024(ErrorCode.CODE_30099024, "手机号长度最大为64"),
	CODE_30099025(ErrorCode.CODE_30099025, "提货时间不能为空"),
	CODE_30099026(ErrorCode.CODE_30099026, "电话号10-19位"),
	CODE_30099028(ErrorCode.CODE_30099028, "供应链id不能为空"),
	CODE_30099029(ErrorCode.CODE_30099029, "只有已确认状态可以生成项目"),
	CODE_30099030(ErrorCode.CODE_30099030, "只有已搁浅状态下可以关闭"),
	CODE_30099031(ErrorCode.CODE_30099031, "只有待处理状态可以开始"),
	CODE_30099032(ErrorCode.CODE_30099032, "只有处理中状态可以提交审核"),
	CODE_30099033(ErrorCode.CODE_30099033, "只有待审核状态可以审核"),
	CODE_30099034(ErrorCode.CODE_30099034, "审核意见长度最大为255"),
	CODE_30099035(ErrorCode.CODE_30099035, "审核类型不能为空"),
	CODE_30099036(ErrorCode.CODE_30099036, "审核类型只有1或2"),
	CODE_30099037(ErrorCode.CODE_30099037, "驳回时审核意见必填"),
	CODE_30099038(ErrorCode.CODE_30099038, "只有已确认,已搁浅,已完成可以撤销"),
	CODE_30099039(ErrorCode.CODE_30099039, "意向的服务专员不是当前操作人"),
	CODE_30099040(ErrorCode.CODE_30099040, "买方预估单价不能为空"),
	CODE_30099041(ErrorCode.CODE_30099041, "买方合同金额不能为空"),
	CODE_30099042(ErrorCode.CODE_30099042, "买方被冻结金额不能为空"),
	CODE_30099043(ErrorCode.CODE_30099043, "买方最低付款额度不能为空"),
	CODE_30099045(ErrorCode.CODE_30099045, "供应链合同金额不能为空"),
	CODE_30099046(ErrorCode.CODE_30099046, "供应链被冻结金额不能为空"),
	CODE_30099047(ErrorCode.CODE_30099047, "供应链最低付款额度不能为空"),
	CODE_30099050(ErrorCode.CODE_30099050, "服务费类型错误"),
	CODE_30099051(ErrorCode.CODE_30099051, "服务费金额不能为空 取值范围 0~10000000000"),
	CODE_30099052(ErrorCode.CODE_30099052, "总价数据错误"),

	// 提货单相关
	CODE_30100001(ErrorCode.CODE_30100001, "关联项目不能为空"),
	CODE_30100002(ErrorCode.CODE_30100002, "关联签收单不能为空"),
	CODE_30100003(ErrorCode.CODE_30100003, "关联的提货单不能为空"),
	CODE_30100004(ErrorCode.CODE_30100004, "运输方式为船运时船舶id不能为空"),
	CODE_30100005(ErrorCode.CODE_30100005, "确认中或者作废中状态才能进行签署"),
	CODE_30100006(ErrorCode.CODE_30100006, "提货单不存在"),
	CODE_30100007(ErrorCode.CODE_30100007, "草稿状态的提货单才允许修改"),
	CODE_30100008(ErrorCode.CODE_30100008, "只有确认中的状态才能进行驳回"),
	CODE_30100009(ErrorCode.CODE_30100009, "草稿状态的提货单才允许删除"),
	CODE_30100010(ErrorCode.CODE_30100010, "只有已完成的提货单可以被关联"),
	CODE_30100011(ErrorCode.CODE_30100011, "项目在交付状态才可以生成提货单"),
	CODE_30100012(ErrorCode.CODE_30100012, "只有签署中或者作废中状态可以签署"),
	CODE_30100013(ErrorCode.CODE_30100013, "数量不能为空"),
	CODE_30100014(ErrorCode.CODE_30100014, "只有未关联签收单的提货单才能发起作废"),
	CODE_30100015(ErrorCode.CODE_30100015, "只有联合购销商未转发的提货单才能发起作废"),
	CODE_30100016(ErrorCode.CODE_30100016, "备注长度不能超过20"),
	CODE_30100017(ErrorCode.CODE_30100017, "吨数不能为空"),
	CODE_30100018(ErrorCode.CODE_30100018, "提货申请日期不能为空"),
	CODE_30100019(ErrorCode.CODE_30100019, "凭证文件不能为空"),
	CODE_30100020(ErrorCode.CODE_30100020, "已完成状态才能删除"),
	CODE_30100021(ErrorCode.CODE_30100021, "需要先删除关联的签收单才能删除"),
	CODE_30100022(ErrorCode.CODE_30100022, "需要先删除被关联的提货单才能删除"),
	CODE_30100023(ErrorCode.CODE_30100023, "预估单价不能为空"),
	CODE_30100024(ErrorCode.CODE_30100024, "提货数量超过可提货预估吨数"),
	CODE_30100025(ErrorCode.CODE_30100025, "只有未关联签收单的提货单才能发起作废"),
	CODE_30100026(ErrorCode.CODE_30100026, "只有联合购销商未转发的提货单才能发起作废"),
	CODE_30100028(ErrorCode.CODE_30100028, "已完成状态才能进行新增"),
	CODE_30100029(ErrorCode.CODE_30100029, "只有提货状态是完成并且签收状态是未签署的数据可以关闭"),
	CODE_30100030(ErrorCode.CODE_30100030, "运货吨数不能为空"),

	// 对账相关
	CODE_30101001(ErrorCode.CODE_30101001, "已发布状态不能进行修改"),
	CODE_30101002(ErrorCode.CODE_30101002, "综合指数 应大于0小于1000 保留两位小数"),
	CODE_30101003(ErrorCode.CODE_30101003, "计算总金额不能为空"),
	CODE_30101004(ErrorCode.CODE_30101004, "实际总金额不能为空"),
	CODE_30101005(ErrorCode.CODE_30101005, "备注长度不能超过200"),
	CODE_30101006(ErrorCode.CODE_30101006, "签收单id集合不能为空"),
	CODE_30101007(ErrorCode.CODE_30101007, "综合指数不能全为空"),
	CODE_30101008(ErrorCode.CODE_30101008, "对账id不存在"),
	CODE_30101009(ErrorCode.CODE_30101009, "只有草稿状态和驳回状态的对账单才能进行修改操作"),
	CODE_30101010(ErrorCode.CODE_30101010, "只有草稿和签署中状态的对账单才能进行驳回操作"),
	CODE_30101011(ErrorCode.CODE_30101011, "只有草稿状态和驳回状态的对账单才能进行删除操作"),
	CODE_30101012(ErrorCode.CODE_30101012, "驳回原因不能为空"),
	CODE_30101013(ErrorCode.CODE_30101013, "类型只能为1-3"),
	CODE_30101014(ErrorCode.CODE_30101014, "类型不能为空"),
	CODE_30101015(ErrorCode.CODE_30101015, "项目在交付状态才可以生成对账单"),
	CODE_30101016(ErrorCode.CODE_30101016, "只有签署中状态可以签署"),
	CODE_30101017(ErrorCode.CODE_30101017, "只有已完成状态并且未绑定的签收单可以进行对账"),
	CODE_30101018(ErrorCode.CODE_30101018, "签署单id不能为空"),
	CODE_30101019(ErrorCode.CODE_30101019, "存在重复日期数据"),
	CODE_30101020(ErrorCode.CODE_30101020, "草稿和驳回状态才能进行发起签署"),
	CODE_30101021(ErrorCode.CODE_30101021, "对账日期不能为空"),
	CODE_30101022(ErrorCode.CODE_30101022, "提货单id集合不能为空"),
	CODE_30101023(ErrorCode.CODE_30101023, "提货单id不能为空"),
	CODE_30101024(ErrorCode.CODE_30101024, "船舶信息不能为空"),
	CODE_30101025(ErrorCode.CODE_30101025, "单价不能为空"),
	CODE_30101026(ErrorCode.CODE_30101026, "采购账号id不能为空"),
	CODE_30101027(ErrorCode.CODE_30101027, "综合指数页应为3种不同类型数据"),
	CODE_30101028(ErrorCode.CODE_30101028, "只有未关联开票的且已完成状态下的对账单才能发起作废"),
	CODE_30101029(ErrorCode.CODE_30101029, "签收单不能为空"),
	CODE_30101030(ErrorCode.CODE_30101030, "总吨数不能为空"),
	CODE_30101031(ErrorCode.CODE_30101031, "核算金额不能为空"),
	CODE_30101032(ErrorCode.CODE_30101032, "折扣金额不能为空"),
	CODE_30101033(ErrorCode.CODE_30101033, "对账单不存在"),
	CODE_30101034(ErrorCode.CODE_30101034, "只有未开票的对账单才能进行关闭操作"),
	CODE_30101035(ErrorCode.CODE_30101035, "已完成的项目才能进行新增"),

	// 开票相关
	CODE_30102001(ErrorCode.CODE_30102001, "对账单不能为空 最少勾选一个"),

	// 付款相关
	CODE_30103001(ErrorCode.CODE_30103001, "已发布状态不能进行删除"),
	CODE_30103002(ErrorCode.CODE_30103002, "非完成状态下不能关闭"),
	CODE_30103003(ErrorCode.CODE_30103003, "项目id不能为空"),
	CODE_30103004(ErrorCode.CODE_30103004, "类型不能为空"),
	CODE_30103005(ErrorCode.CODE_30103005, "综合指数页需校验天然砂、机制砂、碎石都不能为空"),
	CODE_30103006(ErrorCode.CODE_30103006, "金额不能为空"),
	CODE_30103007(ErrorCode.CODE_30103007, "付款日期不能为空"),
	CODE_30103008(ErrorCode.CODE_30103008, "凭证文件不能为空"),
	CODE_30103009(ErrorCode.CODE_30103009, "综合指数页需校验天然砂、机制砂、碎石的权重和等于100%"),
	CODE_30103010(ErrorCode.CODE_30103010, "付款方id不能为空"),
	CODE_30103011(ErrorCode.CODE_30103011, "当前付款金额小于可提货预估金额，不支持作废"),
	CODE_30103012(ErrorCode.CODE_30103012, "方式不能为空"),
	CODE_30103013(ErrorCode.CODE_30103013, "凭证不能为空"),
	CODE_30103014(ErrorCode.CODE_30103014, "金额不能大于99999999999999.99"),
	CODE_30103015(ErrorCode.CODE_30103015, "金额不能超过（收款单位与付款单位交易金额的总和-已付款总额）"),
	CODE_30103016(ErrorCode.CODE_30103016, "非驳回状态下不能删除"),
	CODE_30103017(ErrorCode.CODE_30103017, "非驳回状态不能更新"),
	CODE_30103018(ErrorCode.CODE_30103018, "驳回原因不能为空"),
	CODE_30103019(ErrorCode.CODE_30103019, "驳回原因长度最大为100"),
	CODE_30103020(ErrorCode.CODE_30103020, "项目在交付状态才可以生成付款单"),
	CODE_30103021(ErrorCode.CODE_30103021, "当前付款金额小于可提货预估金额，不支持作废"),

	// 服务费相关
	CODE_30104001(ErrorCode.CODE_30104001, "服务费id不能为空"),
	CODE_30104002(ErrorCode.CODE_30104002, "核销日期不能为空"),
	CODE_30104003(ErrorCode.CODE_30104003, "核销金额不能为空"),
	CODE_30104004(ErrorCode.CODE_30104004, "核销金额不能大于账单金额"),
	CODE_30104005(ErrorCode.CODE_30104005, "服务费账单不存在"),
	CODE_30104006(ErrorCode.CODE_30104006, "价格日期不能为空"),
	CODE_30104007(ErrorCode.CODE_30104007, "项目已完成才能进行新增"),
	CODE_30104008(ErrorCode.CODE_30104008, "服务费类型不能为空"),
	CODE_30104009(ErrorCode.CODE_30104009, "当前发布状态不是未发布不能进行发布操作"),
	CODE_30104010(ErrorCode.CODE_30104010, "吨数不能为空"),
	CODE_30104011(ErrorCode.CODE_30104011, "当前选择的日期已存在指数"),
	CODE_30104012(ErrorCode.CODE_30104012, "金额不能为空"),
	CODE_30104013(ErrorCode.CODE_30104013, "用户id不能为空"),
	CODE_30104014(ErrorCode.CODE_30104014, "当前综合指数不存在"),
	CODE_30104015(ErrorCode.CODE_30104015, "服务费类型不存在"),

	// 平台银行账号相关
	CODE_30105001(ErrorCode.CODE_30105001, "开户名称不能为空 长度为1~25个字符"),
	CODE_30105002(ErrorCode.CODE_30105002, "银行账户不能为空 8~30位的正整数"),
	CODE_30105003(ErrorCode.CODE_30105003, "开户银行不能为空 长度为1~128个字符"),
	CODE_30105004(ErrorCode.CODE_30105004, "平台银行账号不存在"),
	CODE_30105005(ErrorCode.CODE_30105005, "禁用状态下不能设置默认"),
	CODE_30105006(ErrorCode.CODE_30105006, "该账户已经是默认账户，不能进行禁用"),
	CODE_30105007(ErrorCode.CODE_30105007, "批量上传失败"),
	CODE_30105008(ErrorCode.CODE_30105008, "无效code"),
	CODE_30105009(ErrorCode.CODE_30105009, "银行账户无效"),
	CODE_30105010(ErrorCode.CODE_30105010, "最多只能有10个账户"),
	CODE_30105011(ErrorCode.CODE_30105011, "禁用状态下不能进行修改"),

	// 砂石综合指数配置相关
	CODE_30106001(ErrorCode.CODE_30106001, "数据不存在"),
	CODE_30106002(ErrorCode.CODE_30106002, "商品不存在"),
	CODE_30106003(ErrorCode.CODE_30106003, "只有草稿和驳回状态才能删除"),
	CODE_30106004(ErrorCode.CODE_30106004, "草稿和驳回状态才能进行更新"),
	CODE_30106005(ErrorCode.CODE_30106005, "综合指数页类型不能为空"),
	CODE_30106006(ErrorCode.CODE_30106006, "综合指数页存在重复类型"),
	CODE_30106007(ErrorCode.CODE_30106007, "天然砂、碎石、机制砂配置不能为空"),
	CODE_30106008(ErrorCode.CODE_30106008, "天然砂、碎石、机制砂页中区域不能为空"),
	CODE_30106009(ErrorCode.CODE_30106009, "天然砂、碎石、机制砂页中选择区域不存在"),
	CODE_30106010(ErrorCode.CODE_30106010, "天然砂、碎石、机制砂页中区域不能重复"),
	CODE_30106011(ErrorCode.CODE_30106011, "天然砂、碎石、机制砂页中品类不能为空"),
	CODE_30106012(ErrorCode.CODE_30106012, "请确认所有关联下游采购商签收单已签署完成"),
	CODE_30106013(ErrorCode.CODE_30106013, "天然砂、碎石、机制砂页中品类不能重复"),
	CODE_30106014(ErrorCode.CODE_30106014, "天然砂、碎石、机制砂页中区域权重不能为空"),
	CODE_30106015(ErrorCode.CODE_30106015, "天然砂、碎石、机制砂页中区域权重之和必须等于100%"),
	CODE_30106016(ErrorCode.CODE_30106016, "天然砂、碎石、机制砂页中价格类型不能为空"),
	CODE_30106017(ErrorCode.CODE_30106017, "天然砂、碎石、机制砂页中价格类型不存在"),
	CODE_30106018(ErrorCode.CODE_30106018, "天然砂、碎石、机制砂页中价格类型不能重复"),
	CODE_30106019(ErrorCode.CODE_30106019, "天然砂、碎石、机制砂页中价格权重不能为空"),
	CODE_30106020(ErrorCode.CODE_30106020, "天然砂、碎石、机制砂页中价格权重之和必须等于100%"),
	CODE_30106021(ErrorCode.CODE_30106021, "参数错误"),
	CODE_30106022(ErrorCode.CODE_30106022, "品类不存在"),
	CODE_30106023(ErrorCode.CODE_30106023, "请检查品类类型及所属区域"),
	CODE_30106024(ErrorCode.CODE_30106024, "天然砂、碎石、机制砂页中区域不能全为空"),
	CODE_30106025(ErrorCode.CODE_30106025, "类型不能为空"),
	CODE_30106026(ErrorCode.CODE_30106026, "类型不存在"),

	// 船舶相关
	CODE_30120001(ErrorCode.CODE_30120001, "MMSI编号不能为空"),
	CODE_30120002(ErrorCode.CODE_30120002, "MMSI格式错误"),
	CODE_30120003(ErrorCode.CODE_30120003, "船只名称不能为空"),
	CODE_30120004(ErrorCode.CODE_30120004, "船只名称长度不能超过64位"),
	CODE_30120005(ErrorCode.CODE_30120005, "船只类型不能为空"),
	CODE_30120006(ErrorCode.CODE_30120006, "船只长度不能为空"),
	CODE_30120007(ErrorCode.CODE_30120007, "船只宽度不能为空"),
	CODE_30120008(ErrorCode.CODE_30120008, "船只编号不能为空"),
	CODE_30120009(ErrorCode.CODE_30120009, "船只载重不能为空"),
	CODE_30120010(ErrorCode.CODE_30120010, "船只负责人长度不能超过10位"),
	CODE_30120011(ErrorCode.CODE_30120011, "联系方式格式不正确"),
	CODE_30120012(ErrorCode.CODE_30120012, "船舶营业运输证不能为空"),
	CODE_30120013(ErrorCode.CODE_30120013, "船舶营业运输证长度不能超过20位"),
	CODE_30120014(ErrorCode.CODE_30120014, "船舶营业运输证图片链接不能为空"),
	CODE_30120015(ErrorCode.CODE_30120015, "船只照片不能为空"),
	CODE_30120016(ErrorCode.CODE_30120016, "船舶id不能为空"),
	CODE_30120017(ErrorCode.CODE_30120017, "船舶编号长度不能超过20位"),
	CODE_30120018(ErrorCode.CODE_30120018, "MMSI编号已存在"),
	CODE_30120019(ErrorCode.CODE_30120019, "船舶编号已存在"),
	CODE_30130121(ErrorCode.CODE_30130121, "船只照片最多上传3张"),
	CODE_30120021(ErrorCode.CODE_30120021, "审核状态不合法,只能为通过或不通过"),
	CODE_30120022(ErrorCode.CODE_30120022, "审核备注长度不能超过100位"),
	CODE_30120023(ErrorCode.CODE_30120023, "只有待审核状态的船舶才能进行审核操作"),
	CODE_30120024(ErrorCode.CODE_30120024, "船舶不存在"),
	CODE_30120025(ErrorCode.CODE_30120025, "只有已通过状态的船舶才能进行修改操作"),
	CODE_30120026(ErrorCode.CODE_30120026, "船舶编号已存在"),
	CODE_30120027(ErrorCode.CODE_30120027, "只有已禁用状态的船舶才能进行启用操作"),
	CODE_30120028(ErrorCode.CODE_30120028, "只有已通过状态的船舶才能进行禁用操作"),
	CODE_30120122(ErrorCode.CODE_30120122, "只有未通过/已禁用状态的船舶才能进行关闭操作"),
	CODE_30120030(ErrorCode.CODE_30120030, "船舶下存在设备,不能进行关闭操作"),
	CODE_30120031(ErrorCode.CODE_30120031, "文件名称不能为空"),
	CODE_30130127(ErrorCode.CODE_30130127, "船只照片最多上传3张"),
	CODE_30120033(ErrorCode.CODE_30120033, "船只照片最多上传1张"),
	CODE_30120034(ErrorCode.CODE_30120034, "船舶下未绑定设备,不能进行直播操作"),
	CODE_30120035(ErrorCode.CODE_30120035, "只有已通过状态的船舶才能进行直播操作"),
	CODE_30120036(ErrorCode.CODE_30120036, "船舶下未绑定该设备,不能进行直播或抓拍图片操作"),
	CODE_30120037(ErrorCode.CODE_30120037, "文件不存在"),
	CODE_30120038(ErrorCode.CODE_30120038, "承运商id不能为空"),
	CODE_30120039(ErrorCode.CODE_30120039, "坐标点存在空值"),
	CODE_30120040(ErrorCode.CODE_30120040, "经度差值应小于等于两度"),
	CODE_30120041(ErrorCode.CODE_30120041, "纬度差值应小于等于两度"),
	CODE_30120042(ErrorCode.CODE_30120042, "坐标点的经度或纬度存在空值"),
	CODE_30120043(ErrorCode.CODE_30120043, "船只照片不能为空"),
	CODE_30120044(ErrorCode.CODE_30120044, "船只照片最少1张，最多10张"),
	CODE_30120045(ErrorCode.CODE_30120045, "船只视频不能为空"),
	CODE_30120046(ErrorCode.CODE_30120046, "船只视频最少1个，最多5个"),
	CODE_30120047(ErrorCode.CODE_30120047, "船舶未认证才能进行绑定"),
	CODE_30120048(ErrorCode.CODE_30120048, "船舶已认证才能进行解绑"),
	CODE_30120049(ErrorCode.CODE_30120049, "范围超过限制，暂不支持查询"),
	CODE_30120050(ErrorCode.CODE_30120050, "坐标点数量小于3"),
	CODE_30120051(ErrorCode.CODE_30120051, "散货船细分不存在"),
	CODE_30120052(ErrorCode.CODE_30120052, "船舶类型不存在"),
	CODE_30120053(ErrorCode.CODE_30120053, "备注长度不能超过200"),

	// 船舶认证相关
	CODE_30121001(ErrorCode.CODE_30121001, "船舶id不能为空"),
	CODE_30121002(ErrorCode.CODE_30121002, "手持身份证的照片不能为空"),
	CODE_30121003(ErrorCode.CODE_30121003, "船舶证书照片不能为空"),
	CODE_30121004(ErrorCode.CODE_30121004, "船舶认证申请不存在"),
	CODE_30121005(ErrorCode.CODE_30121005, "船舶未认证才能进行认证申请"),
	CODE_30121006(ErrorCode.CODE_30121006, "手持身份证的照片不能为空"),
	CODE_30121007(ErrorCode.CODE_30121007, "船舶证书照片不能为空"),
	CODE_30121008(ErrorCode.CODE_30121008, "已驳回的状态才能进行删除"),
	CODE_30121009(ErrorCode.CODE_30121009, "该船舶正在认证中，不能重复认证"),
	CODE_30121010(ErrorCode.CODE_30121010, "审核备注不能为空"),
	CODE_30121011(ErrorCode.CODE_30121011, "审核备注不能超过100个字符"),
	CODE_30121012(ErrorCode.CODE_30121012, "非待审核状态下不能进行审核"),

	// 船务信息服务费相关
	CODE_30122001(ErrorCode.CODE_30122001, "支付凭证不能为空"),
	CODE_30122002(ErrorCode.CODE_30122002, "船运单id不能为空"),
	CODE_30122003(ErrorCode.CODE_30122003, "类型不能为空"),
	CODE_30122004(ErrorCode.CODE_30122004, "类型不能存在"),
	CODE_30122005(ErrorCode.CODE_30122005, "只有待支付状态才能进行支付"),
	CODE_30122006(ErrorCode.CODE_30122006, "待支付信息服务费状态才能进行支付"),
	CODE_30122007(ErrorCode.CODE_30122007, "支付方式为线下才能进行线下支付"),
	CODE_30122008(ErrorCode.CODE_30122008, "船务信息服务费不存在"),
	CODE_30122009(ErrorCode.CODE_30122009, "当前状态不允许确认"),
	CODE_30122010(ErrorCode.CODE_30122010, "当前支付类型不允许确认"),
	CODE_30122011(ErrorCode.CODE_30122011, "支付时间不能为空"),

	// 航线相关
	CODE_30123001(ErrorCode.CODE_30123001, "始发地不能为空"),
	CODE_30123002(ErrorCode.CODE_30123002, "目的地不能为空"),
	CODE_30123003(ErrorCode.CODE_30123003, "始发地省份不能为空"),
	CODE_30123004(ErrorCode.CODE_30123004, "始发地城市不能为空"),
	CODE_30123005(ErrorCode.CODE_30123005, "目的地省份不能为空"),
	CODE_30123006(ErrorCode.CODE_30123006, "目的地城市不能为空"),
	CODE_30123007(ErrorCode.CODE_30123007, "航线id不能为空"),
	CODE_30123008(ErrorCode.CODE_30123008, "航线信息不存在"),
	CODE_30123009(ErrorCode.CODE_30123009, "权重不能为空 取值范围为0~10000"),

	// 船舶水尺相关
	CODE_30124001(ErrorCode.CODE_30124001, "吃水取值范围 0.01-100 保留两位小数"),
	CODE_30124002(ErrorCode.CODE_30124002, "干舷取值范围 0.01-100 保留两位小数"),
	CODE_30124003(ErrorCode.CODE_30124003, "载货量取值范围 1-100000 需为整数"),
	CODE_30124004(ErrorCode.CODE_30124004, "上传数据量最大不能超过300"),
	CODE_30124005(ErrorCode.CODE_30124005, "船舶信息不存在"),
	CODE_30124006(ErrorCode.CODE_30124006, "船舶水尺信息不存在"),
	CODE_30124007(ErrorCode.CODE_30124007, "该船舶水尺 吃水或干舷已存在"),
	CODE_30124008(ErrorCode.CODE_30124008, "吃水不能为空"),
	CODE_30124009(ErrorCode.CODE_30124009, "干舷不能为空 "),
	CODE_30124010(ErrorCode.CODE_30124010, "载货量不能为空 "),
	CODE_30124011(ErrorCode.CODE_30124011, "收集人信息不能为空 "),

	//船舶监控分享相关
	CODE_30125001(ErrorCode.CODE_30125001, "船舶监控分享的好友最多只能添加5位"),
	CODE_30125002(ErrorCode.CODE_30125002, "自己的设备不能分享给自己"),

	// 标签相关
	CODE_30126001(ErrorCode.CODE_30126001, "标签不存在"),
	CODE_30126002(ErrorCode.CODE_30126002, "标签名称不能为空"),
	CODE_30126003(ErrorCode.CODE_30126003, "标签名称长度不能超过10个字符"),
	CODE_30126004(ErrorCode.CODE_30126004, "此标签被引用不支持删除"),
	CODE_30126005(ErrorCode.CODE_30126005, "标签名称已存在"),

	// 置顶相关
	CODE_30127001(ErrorCode.CODE_30127001, "业务ID不能为空"),
	CODE_30127002(ErrorCode.CODE_30127002, "类型不能为空"),
	CODE_30127003(ErrorCode.CODE_30127003, "类型不存在"),

	// 承运商船运需求相关
	CODE_30128001(ErrorCode.CODE_30128001, "当前状态不是确认中,不能进行接单操作"),
	CODE_30128002(ErrorCode.CODE_30128002, "定金金额只能是大于0小于100亿的数字"),
	CODE_30128003(ErrorCode.CODE_30128003, "定金金额必须精确到小数点后2位"),
	CODE_30128004(ErrorCode.CODE_30128004, "不存在的承运商船运需求"),
	CODE_30128005(ErrorCode.CODE_30128005, "当前状态不是确认中,不能进行接单操作"),
	CODE_30128006(ErrorCode.CODE_30128006, "拒绝备注不能为空"),
	CODE_30128007(ErrorCode.CODE_30128007, "拒绝备注不能超过200个字符"),
	CODE_30128008(ErrorCode.CODE_30128008, "当前状态不是已接单,不能进行收到定金操作"),
	CODE_30128009(ErrorCode.CODE_30128009, "货品类型不能为空"),
	CODE_30128010(ErrorCode.CODE_30128010, "货品类型长度不能超过32位"),
	CODE_30128011(ErrorCode.CODE_30128011, "船型不能为空"),
	CODE_30128012(ErrorCode.CODE_30128012, "船型长度不能超过32位"),
	CODE_30128013(ErrorCode.CODE_30128013, "始发地码头id不能为空"),
	CODE_30128014(ErrorCode.CODE_30128014, "始发地码头名称不能为空"),
	CODE_30128015(ErrorCode.CODE_30128015, "目的地码头id不能为空"),
	CODE_30128016(ErrorCode.CODE_30128016, "目的地码头名称不能为空"),
	CODE_30128017(ErrorCode.CODE_30128017, "目的地码头名称不能为空"),
	CODE_30128018(ErrorCode.CODE_30128018, "航线id不能为空"),
	CODE_30128019(ErrorCode.CODE_30128019, "航线始发地不能为空"),
	CODE_30128020(ErrorCode.CODE_30128020, "航线目的地不能为空"),
	CODE_30128021(ErrorCode.CODE_30128021, "意向单价不能为空"),
	CODE_30128022(ErrorCode.CODE_30128022, "意向单价必须大于0小于100亿且最多保留两位小数"),
	CODE_30128023(ErrorCode.CODE_30128023, "意向吨位不能为空"),
	CODE_30128024(ErrorCode.CODE_30128024, "意向吨位必须大于0且为正整数"),
	CODE_30128025(ErrorCode.CODE_30128025, "装载日期不能为空"),
	CODE_30128026(ErrorCode.CODE_30128026, "宽限天数不能为空"),
	CODE_30128027(ErrorCode.CODE_30128027, "宽限天数必须大于0且为正整数"),
	CODE_30128028(ErrorCode.CODE_30128028, "装卸天数不能为空"),
	CODE_30128029(ErrorCode.CODE_30128029, "装卸天数必须大于0且为正整数"),
	CODE_30128030(ErrorCode.CODE_30128030, "滞期费必须大于0小于100亿且最多保留两位小数"),
	CODE_30128031(ErrorCode.CODE_30128031, "海事费用不能为空"),
	CODE_30128032(ErrorCode.CODE_30128032, "吨位随船不能为空"),
	CODE_30128033(ErrorCode.CODE_30128033, "吨位不随船时最大吨位数必须大于0且为正整数"),
	CODE_30128034(ErrorCode.CODE_30128034, "吨位不随船时最大吨位数必须大于0且为正整数"),
	CODE_30128035(ErrorCode.CODE_30128035, "联系人不能为空"),
	CODE_30128036(ErrorCode.CODE_30128036, "联系人长度不能超过32位"),
	CODE_30128037(ErrorCode.CODE_30128037, "联系电话不能为空"),
	CODE_30128038(ErrorCode.CODE_30128038, "联系电话格式不正确"),
	CODE_30128039(ErrorCode.CODE_30128039, "补充约定长度不能超过200位"),
	CODE_30128040(ErrorCode.CODE_30128040, "货主船运需求不存在"),
	CODE_30128041(ErrorCode.CODE_30128041, "当前状态不合法,不能进行指派操作"),
	CODE_30128042(ErrorCode.CODE_30128042, "当前状态不合法,不能进行开始操作"),
	CODE_30128043(ErrorCode.CODE_30128043, "当前状态不合法,不能进行修改操作"),
	CODE_30128044(ErrorCode.CODE_30128044, "吨位不随船时吨位要求不能为空"),
	CODE_30128045(ErrorCode.CODE_30128045, "吨位不随船时最大吨位必须大于等于最小吨位"),
	CODE_30128046(ErrorCode.CODE_30128046, "当前状态不合法,不能进行完成操作"),
	CODE_30128047(ErrorCode.CODE_30128047, "当前货主需求下没有关联平台船运单,不能进行完成操作"),
	CODE_30128048(ErrorCode.CODE_30128048, "船务信息服务费不能大于定金"),
	CODE_30128049(ErrorCode.CODE_30128049, "船务信息服务费必须精确到小数点后2位"),
	CODE_30128050(ErrorCode.CODE_30128050, "船运定金不能为空"),
	CODE_30128051(ErrorCode.CODE_30128051, "船务信息服务费不能为空"),
	CODE_30128052(ErrorCode.CODE_30128052, "支付类型不存在"),
	CODE_30128053(ErrorCode.CODE_30128053, "船务信息服务费不能为空"),
	CODE_30128054(ErrorCode.CODE_30128054, "是否包含排水槽字段不能为空"),
	CODE_30128055(ErrorCode.CODE_30128055, "是否提交不能为空"),
	CODE_30128056(ErrorCode.CODE_30128056, "当前货主找船需求下没有关联船运单,不能进行完成操作"),
	CODE_30128057(ErrorCode.CODE_30128057, "当前登录用户不是该船运需求的船务专员，不可操作"),
	CODE_30128058(ErrorCode.CODE_30128058, "当前状态不是处理中,不能进行提交操作"),
	CODE_30128059(ErrorCode.CODE_30128059, "运价不能为空"),
	CODE_30128060(ErrorCode.CODE_30128060, "吨位不能为空"),
	CODE_30128061(ErrorCode.CODE_30128061, "船运定金不能为空"),
	CODE_30128062(ErrorCode.CODE_30128062, "船务信息服务费不能为空"),
	CODE_30128063(ErrorCode.CODE_30128063, "运费结算确认节点只能为待发航或待卸货"),
	CODE_30128064(ErrorCode.CODE_30128064, "货主id不能为空"),
	CODE_30128065(ErrorCode.CODE_30128065, "货主公司信息不能为空"),
	CODE_30128066(ErrorCode.CODE_30128066, "货品类型不能为空"),
	CODE_30128067(ErrorCode.CODE_30128067, "始发地码头id不能为空"),
	CODE_30128068(ErrorCode.CODE_30128068, "始发地码头名称不能为空"),
	CODE_30128069(ErrorCode.CODE_30128069, "目的地码头id不能为空"),
	CODE_30128070(ErrorCode.CODE_30128070, "目的地码头名称不能为空"),
	CODE_30128071(ErrorCode.CODE_30128071, "意向单价不能为空"),
	CODE_30128072(ErrorCode.CODE_30128072, "运输最大吨数不能为空"),
	CODE_30128073(ErrorCode.CODE_30128073, "运输最小吨数不能为空"),
	CODE_30128074(ErrorCode.CODE_30128074, "装载日期不能为空"),
	CODE_30128075(ErrorCode.CODE_30128075, "装载日期的宽限天数不能为空"),
	CODE_30128076(ErrorCode.CODE_30128076, "装卸天数不能为空"),
	CODE_30128077(ErrorCode.CODE_30128077, "装卸天数必须在1-127之间"),
	CODE_30128078(ErrorCode.CODE_30128078, "滞期费不能为空"),
	CODE_30128079(ErrorCode.CODE_30128079, "特殊说明不能超过200个字符"),
	CODE_30128080(ErrorCode.CODE_30128080, "定金支付方式不能为空"),
	CODE_30128081(ErrorCode.CODE_30128081, "船务信息服务费不能为空"),
	CODE_30128082(ErrorCode.CODE_30128082, "船运定金的取值范围为大于0的正整数,最多10位"),
	CODE_30128083(ErrorCode.CODE_30128083, "平台船运需求ID不能为空"),
	CODE_30128084(ErrorCode.CODE_30128084, "承运船相关信息不能为空"),
	CODE_30128085(ErrorCode.CODE_30128085, "找船需求已经关联船运单"),
	CODE_30128086(ErrorCode.CODE_30128086, "只有状态为已发布的平台船运需求才允许指派专员操作"),
	CODE_30128087(ErrorCode.CODE_30128087, "只有状态为已发布的平台船运需求才允许关闭"),
	CODE_30128088(ErrorCode.CODE_30128088, "不存在的平台船运需求"),
	CODE_30128089(ErrorCode.CODE_30128089, "该平台船运需求已经关联船运单,不能关闭"),
	CODE_30128090(ErrorCode.CODE_30128090, "货主船运需求已经被关联"),
	CODE_30128091(ErrorCode.CODE_30128091, "已确认的状态才能进行绑定"),
	CODE_30128092(ErrorCode.CODE_30128092, "运货吨数不能为空"),
	CODE_30128093(ErrorCode.CODE_30128093, "船运定金不能为空"),
	CODE_30128094(ErrorCode.CODE_30128094, "需要智能推荐船舶的船运需求id不能为空"),
	CODE_30128095(ErrorCode.CODE_30128095, "邀请抢单的船舶id不能为空"),
	CODE_30128096(ErrorCode.CODE_30128096, "船运需求已发布状态才有智能推荐船舶"),
	CODE_30128097(ErrorCode.CODE_30128097, "没有可以邀请的船舶"),
	CODE_30128098(ErrorCode.CODE_30128098, "电联号码类型不能为空"),
	CODE_30128099(ErrorCode.CODE_30128099, "电联号码类型不存在"),
	CODE_30128100(ErrorCode.CODE_30128100, "电联号码不能为空"),
	CODE_30128101(ErrorCode.CODE_30128101, "电联号码格式错误"),



	// 承运商船运需求
	CODE_30129001(ErrorCode.CODE_30129001, "承运商接单信息不存在"),
	CODE_30129002(ErrorCode.CODE_30129002, "承运商修改接单信息状态不合法"),
	CODE_30129003(ErrorCode.CODE_30129003, "平台船运需求不存在"),
	CODE_30129004(ErrorCode.CODE_30129004, "平台船运需求已关闭"),
	CODE_30129005(ErrorCode.CODE_30129005, "承运商取消接单信息状态不合法"),
	CODE_30129006(ErrorCode.CODE_30129006, "承运商确认接单信息状态不合法"),
	CODE_30129007(ErrorCode.CODE_30129007, "承运商只能选择一条船舶"),
	CODE_30129008(ErrorCode.CODE_30129008, "只有处于卸货中、已卸货或已完成状态的运单才允许抢单"),
	CODE_30129009(ErrorCode.CODE_30129009, "晚了一步，已被其他船主抢单"),
	CODE_30129010(ErrorCode.CODE_30129010, "不可重复接单"),
	CODE_30129011(ErrorCode.CODE_30129011, "船运需求不存在"),
	CODE_30129012(ErrorCode.CODE_30129012, "已发布的船运需求才能接单"),

	// 船主线索相关
	CODE_30130001(ErrorCode.CODE_30130001, "船主线索信息不存在"),
	CODE_30130002(ErrorCode.CODE_30130002, "当前状态是已处理,不能进行提交操作"),
	CODE_30130003(ErrorCode.CODE_30130003, "备注不能为空"),
	CODE_30130004(ErrorCode.CODE_30130004, "备注长度应为1-200位"),

	// 运价综合指数
	CODE_30131001(ErrorCode.CODE_30131001, "运价综合指数不存在"),
	CODE_30131002(ErrorCode.CODE_30131002, "日期不能为空"),
	CODE_30131003(ErrorCode.CODE_30131003, "综合指数只能0-1000保留两位小数"),
	CODE_30131004(ErrorCode.CODE_30131004, "当前日期已存在指数"),
	CODE_30131005(ErrorCode.CODE_30131005, "综合指数不能为空"),
	CODE_30131006(ErrorCode.CODE_30131006, "日期不能重复"),
	CODE_30131007(ErrorCode.CODE_30131007, "未发布状态才能进行修改"),
	CODE_30131008(ErrorCode.CODE_30131008, "未发布状态才能进行删除"),
	CODE_30131009(ErrorCode.CODE_30131009, "未发布状态才能进行发布"),
	CODE_30131010(ErrorCode.CODE_30131010, "已发布状态才能进行撤回"),
	CODE_30131011(ErrorCode.CODE_30131011, "不能发布今日之后指数数据"),

	// 运价综合指数配置相关
	CODE_30132001(ErrorCode.CODE_30132001, "运价综合指数配置不存在"),
	CODE_30132002(ErrorCode.CODE_30132002, "始发地列表不能为空"),
	CODE_30132003(ErrorCode.CODE_30132003, "始发地省份编码"),
	CODE_30132004(ErrorCode.CODE_30132004, "始发地城市编码"),
	CODE_30132005(ErrorCode.CODE_30132005, "始发地权重不能为空"),
	CODE_30132006(ErrorCode.CODE_30132006, "始发地存在重复数据"),
	CODE_30132007(ErrorCode.CODE_30132007, "航线不能为空"),
	CODE_30132008(ErrorCode.CODE_30132008, "航线id不能为空"),
	CODE_30132009(ErrorCode.CODE_30132009, "航线不存在"),
	CODE_30132010(ErrorCode.CODE_30132010, "航线权重不能为空"),
	CODE_30132011(ErrorCode.CODE_30132011, "航线吨位不能为空"),
	CODE_30132012(ErrorCode.CODE_30132012, "存在重复航线"),
	CODE_30132013(ErrorCode.CODE_30132013, "吨位类型不能为空"),
	CODE_30132014(ErrorCode.CODE_30132014, "吨位类型不存在"),
	CODE_30132015(ErrorCode.CODE_30132015, "吨位权重不能为空"),
	CODE_30132016(ErrorCode.CODE_30132016, "存在重复吨位"),
	CODE_30132017(ErrorCode.CODE_30132017, "吨位权重之和必须为100%"),
	CODE_30132018(ErrorCode.CODE_30132018, "航线权重之和必须为100%"),
	CODE_30132019(ErrorCode.CODE_30132019, "始发地权重之和必须为100%"),

	//消息相关
	CODE_30133001(ErrorCode.CODE_30133001, "流水号不能为空"),
	CODE_30133002(ErrorCode.CODE_30133002, "场景ID不能为空"),
	CODE_30133003(ErrorCode.CODE_30133003, "验证码校验参数不能为空"),
	CODE_30133004(ErrorCode.CODE_30133004, "手机号不存在"),
	CODE_30133005(ErrorCode.CODE_30133005, "手机号被禁用"),


	// 货主船运需求
	CODE_30134001(ErrorCode.CODE_30134001, "当前状态不是处理中，待确认,找船中状态,不允许取消"),
	CODE_30134002(ErrorCode.CODE_30134002, "当前状态不是待货主确认,不允许进行确认操作"),
	CODE_30134003(ErrorCode.CODE_30134003, "吨位不随船时最大吨位必须大于等于最小吨位"),
	CODE_30134004(ErrorCode.CODE_30134004, "装载日期宽限天数不能为负数"),
	CODE_30134005(ErrorCode.CODE_30134005, "装卸天数不能为负数"),
	CODE_30134006(ErrorCode.CODE_30134006, "船运定金的取值范围为大于0的正整数,最多10位"),
	CODE_30134007(ErrorCode.CODE_30134007, "选择链云垫付需完成个人实名"),
	CODE_30134008(ErrorCode.CODE_30134008, "船运需求不存在"),
	CODE_30134009(ErrorCode.CODE_30134009, "船运定金不能为空"),
	CODE_30134010(ErrorCode.CODE_30134010, "已帮您成功匹配到船主，不能取消"),
	CODE_30134011(ErrorCode.CODE_30134011, "货品类型不能为空"),
	CODE_30134012(ErrorCode.CODE_30134012, "货品类型长度不能超过32位"),
	CODE_30134013(ErrorCode.CODE_30134013, "船型不能为空"),
	CODE_30134014(ErrorCode.CODE_30134014, "排水槽字段不能为空"),
	CODE_30134015(ErrorCode.CODE_30134015, "始发地码头id不能为空"),
	CODE_30134016(ErrorCode.CODE_30134016, "始发地码头名称不能为空"),
	CODE_30134017(ErrorCode.CODE_30134017, "目的地码头id不能为空"),
	CODE_30134018(ErrorCode.CODE_30134018, "目的地码头名称不能为空"),
	CODE_30134019(ErrorCode.CODE_30134019, "意向单价不能为空"),
	CODE_30134020(ErrorCode.CODE_30134020, "意向单价必须大于0小于100亿且最多保留两位小数"),
	CODE_30134021(ErrorCode.CODE_30134021, "意向吨位不能为空"),
	CODE_30134022(ErrorCode.CODE_30134022, "意向吨位必须大于0且为正整数"),
	CODE_30134023(ErrorCode.CODE_30134023, "装载日期不能为空"),
	CODE_30134024(ErrorCode.CODE_30134024, "宽限天数不能为空"),
	CODE_30134025(ErrorCode.CODE_30134025, "装卸天数不能为空"),
	CODE_30134026(ErrorCode.CODE_30134026, "滞期费必须大于0小于100亿且最多保留两位小数"),
	CODE_30134027(ErrorCode.CODE_30134027, "海事费用不能为空"),
	CODE_30134028(ErrorCode.CODE_30134028, "吨位不随船时最大吨位数必须大于0且为正整数"),
	CODE_30134029(ErrorCode.CODE_30134029, "吨位不随船时最小吨位数必须大于0且为正整数"),
	CODE_30134030(ErrorCode.CODE_30134030, "联系人长度不能超过32位"),
	CODE_30134031(ErrorCode.CODE_30134031, "联系电话不能为空"),
	CODE_30134032(ErrorCode.CODE_30134032, "联系电话格式不正确"),
	CODE_30134033(ErrorCode.CODE_30134033, "补充约定长度不能超过200位"),
	CODE_30134034(ErrorCode.CODE_30134034, "船舶类型不存在"),

	// 运价指数版本相关
	CODE_30135001(ErrorCode.CODE_30135001, "版本日期不能为空"),
	CODE_30135002(ErrorCode.CODE_30135002, "版本日期已存在"),
	CODE_30135003(ErrorCode.CODE_30135003, "版本不存在"),
	CODE_30135004(ErrorCode.CODE_30135004, "存在指数数据，不能修改"),
	CODE_30135005(ErrorCode.CODE_30135005, "存在指数数据，不能删除"),
	CODE_30135006(ErrorCode.CODE_30135006, "版本不存在"),
	CODE_30135007(ErrorCode.CODE_30135007, "版本日期不能选择今天之后的日期"),

	// 运价指数版本记录相关
	CODE_30136001(ErrorCode.CODE_30136001, "运价指数版本记录不存在"),
	CODE_30136002(ErrorCode.CODE_30136002, "版本id不能为空"),
	CODE_30136003(ErrorCode.CODE_30136003, "待提交、已驳回状态才能进行提交、修改、删除操作"),
	CODE_30136004(ErrorCode.CODE_30136004, "待发布、已撤回状态才能进行发布"),
	CODE_30136005(ErrorCode.CODE_30136005, "待发布、已撤回状态才能进行驳回"),
	CODE_30136006(ErrorCode.CODE_30136006, "已发布状态且是最新发布数据才能进行撤回"),
	CODE_30136007(ErrorCode.CODE_30136007, "当前已经是最初发布版本，无法进行撤回"),
	CODE_30136008(ErrorCode.CODE_30136008, "备注不能超过100个字符"),
	CODE_30136009(ErrorCode.CODE_30136009, "待提交、已驳回状态才能进行保存"),
	CODE_30136010(ErrorCode.CODE_30136010, "指数数据为空，不能进行提交"),

	// 船运单相关
	CODE_30137001(ErrorCode.CODE_30137001, "船运单不存在"),
	CODE_30137002(ErrorCode.CODE_30137002, "货名不能为空"),
	CODE_30137003(ErrorCode.CODE_30137003, "货名不能超过32个字符"),
	CODE_30137004(ErrorCode.CODE_30137004, "货主不能为空"),
	CODE_30137005(ErrorCode.CODE_30137005, "联系人不能为空"),
	CODE_30137006(ErrorCode.CODE_30137006, "手机号不能为空"),
	CODE_30137007(ErrorCode.CODE_30137007, "运价不能为空"),
	CODE_30137008(ErrorCode.CODE_30137008, "运价必须大于0小于10000，保留2位小数"),
	CODE_30137009(ErrorCode.CODE_30137009, "运货量不能为空且大于0小于1000000"),
	CODE_30137010(ErrorCode.CODE_30137010, "装载日期不能为空"),
	CODE_30137011(ErrorCode.CODE_30137011, "宽限天数不能为空"),
	CODE_30137012(ErrorCode.CODE_30137012, "装卸天数不能为空"),
	CODE_30137013(ErrorCode.CODE_30137013, "船舶不能为空"),
	CODE_30137014(ErrorCode.CODE_30137014, "联系人不能超过32个字符"),
	CODE_30137015(ErrorCode.CODE_30137015, "手机号格式错误"),
	CODE_30137019(ErrorCode.CODE_30137019, "始发港不能为空"),
	CODE_30137020(ErrorCode.CODE_30137020, "目的港不能为空"),
	CODE_30137021(ErrorCode.CODE_30137021, "链云运单ID不能为空"),
	CODE_30137022(ErrorCode.CODE_30137022, "待发航前状态才能进行关闭"),
	CODE_30137023(ErrorCode.CODE_30137023, "待装货才能进行开始装货"),
	CODE_30137024(ErrorCode.CODE_30137024, "装货中才能进行发航申请"),
	CODE_30137025(ErrorCode.CODE_30137025, "卸货中才能进行确认卸货"),
	CODE_30137026(ErrorCode.CODE_30137026, "发货单ID不能为空"),
	CODE_30137027(ErrorCode.CODE_30137027, "订单ID不能为空"),
	CODE_30137028(ErrorCode.CODE_30137028, "规格/型号不能为空"),
	CODE_30137034(ErrorCode.CODE_30137034, "货主联系地址不能为空"),
	CODE_30137035(ErrorCode.CODE_30137035, "船主联系地址不能为空"),
	CODE_30137036(ErrorCode.CODE_30137036, "货主联系地址不能超过128个字符"),
	CODE_30137037(ErrorCode.CODE_30137037, "船主联系地址不能超过128个字符"),
	CODE_30137039(ErrorCode.CODE_30137039, "已卸货状态才能完成"),
	CODE_30137040(ErrorCode.CODE_30137040, "支付类型不能为空"),
	CODE_30137041(ErrorCode.CODE_30137041, "支付类型不存在"),
	CODE_30137042(ErrorCode.CODE_30137042, "合同文件不能为空"),
	CODE_30137043(ErrorCode.CODE_30137043, "运费结算吨位确认节点不能为空"),
	CODE_30137044(ErrorCode.CODE_30137044, "运费结算吨位确认节点不存在"),
	CODE_30137045(ErrorCode.CODE_30137045, "货主还未确认运费结算吨位"),
	CODE_30137046(ErrorCode.CODE_30137046, "船主还未确认运费结算吨位"),
	CODE_30137047(ErrorCode.CODE_30137047, "货主不存在"),
	CODE_30137048(ErrorCode.CODE_30137048, "船主不存在"),
	CODE_30137049(ErrorCode.CODE_30137049, "货主未进行个人认证或者企业认证"),
	CODE_30137050(ErrorCode.CODE_30137050, "船主未进行个人认证或者企业认证"),
	CODE_30137051(ErrorCode.CODE_30137051, "货主船务信息服务费不能为空"),
	CODE_30137052(ErrorCode.CODE_30137052, "船主船务信息服务费不能为空"),
	CODE_30137053(ErrorCode.CODE_30137053, "海事费用不存在"),
	CODE_30137054(ErrorCode.CODE_30137054, "货主和船主不能是同一个账号"),
	CODE_30137055(ErrorCode.CODE_30137055, "船运单已经被绑定"),
	CODE_30137056(ErrorCode.CODE_30137056, "平台船运需求已经被关联"),
	CODE_30137057(ErrorCode.CODE_30137057, "船运单已关闭"),
	CODE_30137058(ErrorCode.CODE_30137058, "已清仓状态才能开始卸货"),
	CODE_30137059(ErrorCode.CODE_30137059, "已清仓状态才能完成清仓"),
	CODE_30137060(ErrorCode.CODE_30137060, "宽限天数范围0-30"),
	CODE_30137061(ErrorCode.CODE_30137061, "装卸天数范围0-30"),
	CODE_30137062(ErrorCode.CODE_30137062, "补充说明最多200个字符"),
	CODE_30137063(ErrorCode.CODE_30137063, "滞期费大于0小于10000元/吨/天，保留2位小数"),
	CODE_30137064(ErrorCode.CODE_30137064, "开始卸货之后才能进行完成清仓"),
	CODE_30137065(ErrorCode.CODE_30137065, "定金金额必须大于0小于100000000.00"),
	CODE_30137066(ErrorCode.CODE_30137066, "装货时间不能为空"),
	CODE_30137067(ErrorCode.CODE_30137067, "卸货时间不能为空"),
	CODE_30137068(ErrorCode.CODE_30137068, "附件数量不能超过10个"),
	CODE_30137069(ErrorCode.CODE_30137069, "卸货时间必须晚于装货时间"),

	// 船运单相关
	CODE_30138001(ErrorCode.CODE_30138001, "船运单不存在"),
	CODE_30138002(ErrorCode.CODE_30138002, "合同签署状态才能进行签署"),
	CODE_30138003(ErrorCode.CODE_30138003, "货主未签署状态才能进行签署"),
	CODE_30138004(ErrorCode.CODE_30138004, "船主未签署状态才能进行签署"),
	CODE_30138005(ErrorCode.CODE_30138005, "货主签署完成之后才能进行签署"),
	CODE_30138006(ErrorCode.CODE_30138006, "当前用户无权限进行确认"),
	CODE_30138007(ErrorCode.CODE_30138007, "付款凭证不能为空"),
	CODE_30138008(ErrorCode.CODE_30138008, "自行付款才能进行支付"),
	CODE_30138009(ErrorCode.CODE_30138009, "未支付状态才能进行支付"),
	CODE_30138010(ErrorCode.CODE_30138010, "船主未确认状态才能进行确认"),
	CODE_30138011(ErrorCode.CODE_30138011, "待支付定金状态才能进行确认"),
	CODE_30138012(ErrorCode.CODE_30138012, "待支付定金状态才能进行支付"),
	CODE_30138013(ErrorCode.CODE_30138013, "船运定金不存在"),
	CODE_30138014(ErrorCode.CODE_30138014, "船运定金未结算或未支付"),
	CODE_30138015(ErrorCode.CODE_30138015, "您已经确吨位，不可重复操作！"),
	CODE_30138016(ErrorCode.CODE_30138016, "货主确认完之后才能进行确认"),
	CODE_30138017(ErrorCode.CODE_30138017, "待发航状态才能进行确认"),
	CODE_30138018(ErrorCode.CODE_30138018, "待卸货状态才能进行确认"),
	CODE_30138019(ErrorCode.CODE_30138019, "已完成清仓不能进行修改"),
	CODE_30138020(ErrorCode.CODE_30138020, "银行账户不存在"),
	CODE_30138021(ErrorCode.CODE_30138021, "该类型运单不支持修改"),
	CODE_30138022(ErrorCode.CODE_30138022, "已取消状态不能进行操作"),
	CODE_30138023(ErrorCode.CODE_30138023, "该状态不能支付运费"),
    CODE_30138024(ErrorCode.CODE_30138024, "{0}不能操作"),

	// 船运单明细相关
	CODE_30139001(ErrorCode.CODE_30139001, "装货日期不能为空"),
	CODE_30139002(ErrorCode.CODE_30139002, "集港货物吨位不能为空"),
	CODE_30139003(ErrorCode.CODE_30139003, "集港货物吨位应大于0小于1000000吨，保留2位小数"),
	CODE_30139004(ErrorCode.CODE_30139004, "备注不能超过200个字符"),
	CODE_30139005(ErrorCode.CODE_30139005, "装载吨位不能为空"),
	CODE_30139006(ErrorCode.CODE_30139006, "装载吨位必须大于0小于1000000吨，保留2位小数"),
	CODE_30139007(ErrorCode.CODE_30139007, "量方吨位必须大于0小于1000000吨，保留2位小数"),
	CODE_30139008(ErrorCode.CODE_30139008, "运费结算吨位必须大于0"),
	CODE_30139009(ErrorCode.CODE_30139009, "六处水尺-1范围为0-1000米，保留2位小数"),
	CODE_30139010(ErrorCode.CODE_30139010, "六处水尺-2范围为0-1000米，保留2位小数"),
	CODE_30139011(ErrorCode.CODE_30139011, "六处水尺-3范围为0-1000米，保留2位小数"),
	CODE_30139012(ErrorCode.CODE_30139012, "六处水尺-4范围为0-1000米，保留2位小数"),
	CODE_30139013(ErrorCode.CODE_30139013, "六处水尺-5范围为0-1000米，保留2位小数"),
	CODE_30139014(ErrorCode.CODE_30139014, "六处水尺-6范围为0-1000米，保留2位小数"),
	CODE_30139015(ErrorCode.CODE_30139015, "滞期天数必须大于等于0，小于1000的数字"),
	CODE_30139016(ErrorCode.CODE_30139016, "卸货吨位必须大于0小于1000000吨，保留2位小数"),
	CODE_30139017(ErrorCode.CODE_30139017, "卸货量方吨位必须大于0小于1000000吨，保留2位小数"),
	CODE_30139018(ErrorCode.CODE_30139018, "船舶不存在"),
	CODE_30139019(ErrorCode.CODE_30139019, "船运单明细不存在"),
	CODE_30139020(ErrorCode.CODE_30139020, "已完成状态不能进行修改"),
	CODE_30139021(ErrorCode.CODE_30139021, "运输中状态不能进行修改"),
	CODE_30139022(ErrorCode.CODE_30139022, "运费结算吨位不能为空"),
	CODE_30139023(ErrorCode.CODE_30139023, "待卸货节点才能进行确认吨位"),
	CODE_30139024(ErrorCode.CODE_30139024, "运费结算吨位不能为空"),
	CODE_30139025(ErrorCode.CODE_30139025, "实际运费不能为空"),
	CODE_30139026(ErrorCode.CODE_30139026, "实际运费必须大于0"),
	CODE_30139027(ErrorCode.CODE_30139027, "接档记录信息不能为空"),
	CODE_30139028(ErrorCode.CODE_30139028, "船舶到港时间应早于装货上档时间"),
	CODE_30139029(ErrorCode.CODE_30139029, "此船运单已经被绑定"),
	CODE_30139030(ErrorCode.CODE_30139030, "发货完成不能新增删除船运单"),
	CODE_30139031(ErrorCode.CODE_30139031, "满载视频不能为空"),
	CODE_30139032(ErrorCode.CODE_30139032, "吨位证明不能为空"),

	// 船运单明细相关
	CODE_30140001(ErrorCode.CODE_30140001, "船运单明细相关不存在"),
	CODE_30140002(ErrorCode.CODE_30140002, "待发航才能进行发航确认"),
	CODE_30140003(ErrorCode.CODE_30140003, "运输中才能进行到港确认"),
	CODE_30140004(ErrorCode.CODE_30140004, "待卸货才能进行同意卸货"),
	CODE_30140005(ErrorCode.CODE_30140005, "您已经进行发航申请并确认吨位，不可重复操作！"),
	CODE_30140006(ErrorCode.CODE_30140006, "您已经进行同意卸货并确认吨位，不可重复操作！"),
	CODE_30140007(ErrorCode.CODE_30140007, "是否排水不能为空"),
	CODE_30140008(ErrorCode.CODE_30140008, "实际结算吨位不能为空"),
	CODE_30140009(ErrorCode.CODE_30140009, "实际结算吨位必须大于0小于1000000吨，保留2位小数"),
	CODE_30140010(ErrorCode.CODE_30140010, "船主未确认运费结算吨位才能进行修改"),
	CODE_30140011(ErrorCode.CODE_30140011, "待发航的状态才能进行修改运费结算吨位"),
	CODE_30140012(ErrorCode.CODE_30140012, "待卸货的状态才能进行修改运费结算吨位"),
	CODE_30140013(ErrorCode.CODE_30140013, "到港时间不能为空"),
	CODE_30140014(ErrorCode.CODE_30140014, "到港视频ID不能为空"),
	CODE_30140015(ErrorCode.CODE_30140015, "实际结算吨位不能为空"),
	CODE_30140016(ErrorCode.CODE_30140016, "卸货完成时间不能为空"),
	CODE_30140017(ErrorCode.CODE_30140017, "清仓视频不能为空"),
	CODE_30140018(ErrorCode.CODE_30140018, "发航时间不能为空"),
	CODE_30140019(ErrorCode.CODE_30140019, "装载完成时间不能为空"),
	CODE_30140020(ErrorCode.CODE_30140020, "装载时长不能为空"),
	CODE_30140021(ErrorCode.CODE_30140021, "发航时间不能为空"),
	CODE_30140022(ErrorCode.CODE_30140022, "预计到达日期不能为空"),
	CODE_30140023(ErrorCode.CODE_30140023, "卸货开始时间不能为空"),
	CODE_30140024(ErrorCode.CODE_30140024, "卸货完成时间不能为空"),
	CODE_30140025(ErrorCode.CODE_30140025, "滞期天数不能为空"),
	CODE_30140026(ErrorCode.CODE_30140026, "卸货吨位不能为空"),
	CODE_30140027(ErrorCode.CODE_30140027, "卸货量方吨位不能为空"),

	// 导入相关
	CODE_30141001(ErrorCode.CODE_30141001, "上传的数据不能为空"),
	CODE_30141002(ErrorCode.CODE_30141002, "上传数据量超出了限制"),

	// 激活码相关
	CODE_30142001(ErrorCode.CODE_30142001, "等级不能为空"),
	CODE_30142002(ErrorCode.CODE_30142002, "时长必须在1-10000之间"),
	CODE_30142003(ErrorCode.CODE_30142003, "兑换日期只能选择今日之后的日期"),
	CODE_30142004(ErrorCode.CODE_30142004, "生成数量必须大于0"),
	CODE_30142005(ErrorCode.CODE_30142005, "关联了账号的激活码只能生成单条激活码"),
	CODE_30142006(ErrorCode.CODE_30142006, "生成激活码数量不能超过1000条"),
	CODE_30142007(ErrorCode.CODE_30142007, "兑换有效时长应为大于0小于100的整数"),
	CODE_30142008(ErrorCode.CODE_30142008, "赠送时长应为大于0小于1000的整数"),

	// 业务设置相关
	CODE_30143001(ErrorCode.CODE_30143001, "业务设置数据不存在"),
	CODE_30143002(ErrorCode.CODE_30143002, "类型不能为空"),
	CODE_30143003(ErrorCode.CODE_30143003, "内容不能为空"),
	CODE_30143004(ErrorCode.CODE_30143004, "类型不存在"),
	CODE_30143005(ErrorCode.CODE_30143005, "参数解析错误"),
	CODE_30143006(ErrorCode.CODE_30143006, "推介人结佣金额比例不能为空"),
	CODE_30143007(ErrorCode.CODE_30143007, "推介人分佣有效期不能为空"),
	CODE_30143008(ErrorCode.CODE_30143008, "推介人结佣金额比例不能超过100"),
	CODE_30143009(ErrorCode.CODE_30143009, "有效期最低不能少于一个月，不能超过120个月"),

	// 会员等级相关
	CODE_30144001(ErrorCode.CODE_30144001, "会员等级id不能为空"),
	CODE_30144002(ErrorCode.CODE_30144002, "会员等级不存在"),
	CODE_30144003(ErrorCode.CODE_30144003, "包月费用不能为空"),
	CODE_30144004(ErrorCode.CODE_30144004, "包年费用不能为空"),
	CODE_30144005(ErrorCode.CODE_30144005, "会员权限不能为空"),
	CODE_30144006(ErrorCode.CODE_30144006, "包月费用不能为空 取值范围 0.01~10000000000"),
	CODE_30144007(ErrorCode.CODE_30144007, "包年费用不能为空 取值范围 0.01~10000000000"),
	CODE_30144008(ErrorCode.CODE_30144008, "包年费用必须大于包月费用"),
	CODE_30144009(ErrorCode.CODE_30144009, "包月费用必须大于上一个等级的包月费用"),
	CODE_30144010(ErrorCode.CODE_30144010, "包年费用必须大于上一个等级的包年费用"),
	CODE_30144011(ErrorCode.CODE_30144011, "包月费用必须小于下一个等级的包月费用"),
	CODE_30144012(ErrorCode.CODE_30144012, "包年费用必须小于下一个等级的包年费用"),
	CODE_30144013(ErrorCode.CODE_30144013, "注册送会员配置信息不存在"),
	CODE_30144014(ErrorCode.CODE_30144014, "无法关闭，请检查注册送会员配置"),
	CODE_30144015(ErrorCode.CODE_30144015, "无法关闭，请检查会员等级列表"),
	CODE_30144016(ErrorCode.CODE_30144016, "无法开放，请检查会员等级列表"),

	//员工相关
	CODE_30145001(ErrorCode.CODE_30145001, "手机和邮箱不可同时为空"),
	CODE_30145002(ErrorCode.CODE_30145002, "手机号重复"),
	CODE_30145003(ErrorCode.CODE_30145003, "员工主责部门只允许有一个"),
	CODE_30145004(ErrorCode.CODE_30145004, "员工部门不能为空"),
	CODE_30145005(ErrorCode.CODE_30145005, "组织不存在"),
	CODE_30145007(ErrorCode.CODE_30145007, "邮箱账号重复"),
	CODE_30145008(ErrorCode.CODE_30145008, "员工工号重复"),
	CODE_30145009(ErrorCode.CODE_30145009, "员工姓名不能为空"),
	CODE_30145010(ErrorCode.CODE_30145010, "员工工号不能为空"),
	CODE_30145011(ErrorCode.CODE_30145011, "员工手机号不能为空"),
	CODE_30145012(ErrorCode.CODE_30145012, "手机号格式不正确"),
	CODE_30145013(ErrorCode.CODE_30145013, "邮箱格式不正确"),
	CODE_30145014(ErrorCode.CODE_30145014, "员工姓名长度最大为64"),
	CODE_30145015(ErrorCode.CODE_30145015, "员工工号长度最大为10"),
	CODE_30145016(ErrorCode.CODE_30145016, "员工手机号长度最大为64"),
	CODE_30145017(ErrorCode.CODE_30145017, "员工邮箱长度最大为64"),
	CODE_30145018(ErrorCode.CODE_30145018, "员工职位描述长度最大为128"),
	CODE_30145019(ErrorCode.CODE_30145019, "员工部门不能为空"),
	CODE_30145020(ErrorCode.CODE_30145020, "员工部门id不能为空"),
	CODE_30145021(ErrorCode.CODE_30145021, "主责部门的值只能为0或1"),
	CODE_30145022(ErrorCode.CODE_30145022, "员工不存在"),

	// 推广码相关
	CODE_30146001(ErrorCode.CODE_30146001, "标题不能为空"),
	CODE_30146002(ErrorCode.CODE_30146002, "标题长度必须在1-15个字符之间"),
	CODE_30146003(ErrorCode.CODE_30146003, "关联账号信息不能为空"),
	CODE_30146004(ErrorCode.CODE_30146004, "是否赠送会员不能为空"),
	CODE_30146005(ErrorCode.CODE_30146005, "会员赠送时长必须在1-10000之间"),
	CODE_30146006(ErrorCode.CODE_30146006, "会员兑换有效期必须是未来的日期"),
	CODE_30146007(ErrorCode.CODE_30146007, "推广有效期不能为空"),
	CODE_30146008(ErrorCode.CODE_30146008, "推广有效期必须是未来的日期"),
	CODE_30146009(ErrorCode.CODE_30146009, "推广头图不能为空"),
	CODE_30146010(ErrorCode.CODE_30146010, "页面不能为空"),
	CODE_30146011(ErrorCode.CODE_30146011, "赠送会员为'是'时，会员等级不能为空"),
	CODE_30146012(ErrorCode.CODE_30146012, "赠送会员为'是'时，赠送时长不能为空"),
	CODE_30146013(ErrorCode.CODE_30146013, "赠送会员为'是'时，兑换有效期不能为空"),
	CODE_30146014(ErrorCode.CODE_30146014, "推广已关闭，无法延期"),
	CODE_30146015(ErrorCode.CODE_30146015, "延期时间只能是当前日期之后的时间"),
	CODE_30146016(ErrorCode.CODE_30146016, "推广不存在"),
	CODE_30146017(ErrorCode.CODE_30146017, "客户已绑定有效推广"),

	//角色相关
	CODE_30147001(ErrorCode.CODE_30147001, "该角色已存在"),
	CODE_30147002(ErrorCode.CODE_30147002, "角色名称不能为空"),
	CODE_30147003(ErrorCode.CODE_30147003, "权限列表不能为空"),
	CODE_30147004(ErrorCode.CODE_30147004, "角色说明长度最大为100"),
	CODE_30147005(ErrorCode.CODE_30147005, "角色名称长度最大为30"),
	CODE_30147006(ErrorCode.CODE_30147006, "该角色被用户关联，请先解除关联"),
	CODE_30147007(ErrorCode.CODE_30147007, "角色不存在"),
	CODE_30147008(ErrorCode.CODE_30147008, "权限不存在"),

	// 账号相关
	CODE_30148001(ErrorCode.CODE_30148001, "员工id不能为空"),
	CODE_30148002(ErrorCode.CODE_30148002, "角色列表不能为空"),
	CODE_30148003(ErrorCode.CODE_30148003, "状态不能为空"),
	CODE_30148004(ErrorCode.CODE_30148004, "状态只有0或1"),
	CODE_30148005(ErrorCode.CODE_30148005, "账号不存在"),
	CODE_30148006(ErrorCode.CODE_30148006, "账号已存在"),
	CODE_30148007(ErrorCode.CODE_30148007, "用户类型不能为空"),
	CODE_30148008(ErrorCode.CODE_30148008, "员工姓名不能为空"),
	CODE_30148009(ErrorCode.CODE_30148009, "员工号码不能为空"),
	CODE_30148010(ErrorCode.CODE_30148010, "号码不规范"),
	CODE_30148011(ErrorCode.CODE_30148011, "号码重复"),
	CODE_30148012(ErrorCode.CODE_30148012, "手机号最多维护10个"),
	CODE_30148013(ErrorCode.CODE_30148013, "姓名不能为空"),
	CODE_30148014(ErrorCode.CODE_30148014, "姓名不能超过32个字符"),
	CODE_30148015(ErrorCode.CODE_30148015, "手机号不能为空"),
	CODE_30148016(ErrorCode.CODE_30148016, "手机号格式错误"),
	CODE_30148017(ErrorCode.CODE_30148017, "该手机号已被使用"),
	CODE_30148018(ErrorCode.CODE_30148018, "工号格式错误"),
	CODE_30148019(ErrorCode.CODE_30148019, "该工号已存在"),
	CODE_30148020(ErrorCode.CODE_30148020, "请先完成组织机构认证"),



	// 额度变更相关
	CODE_30149001(ErrorCode.CODE_30149001, "额度变更不存在"),
	CODE_30149002(ErrorCode.CODE_30149002, "项目ID不能为空"),
	CODE_30149003(ErrorCode.CODE_30149003, "合同ID不能为空"),
	CODE_30149004(ErrorCode.CODE_30149004, "变更方向不能为空"),
	CODE_30149005(ErrorCode.CODE_30149005, "费用类型不能为空"),
	CODE_30149006(ErrorCode.CODE_30149006, "申请金额不能为空"),
	CODE_30149007(ErrorCode.CODE_30149007, "申请金额范围0-100亿数字，保留两位小数"),
	CODE_30149008(ErrorCode.CODE_30149008, "申请日期不能为空"),
	CODE_30149009(ErrorCode.CODE_30149009, "申请事由不能为空"),
	CODE_30149010(ErrorCode.CODE_30149010, "申请事由字符长度最大为200"),
	CODE_30149011(ErrorCode.CODE_30149011, "变更方向不存在"),
	CODE_30149012(ErrorCode.CODE_30149012, "费用类型不存在"),
	CODE_30149013(ErrorCode.CODE_30149013, "驳回原因不能为空"),
	CODE_30149014(ErrorCode.CODE_30149014, "驳回原因字符长度最大为200"),
	CODE_30149015(ErrorCode.CODE_30149015, "无权限"),
	CODE_30149016(ErrorCode.CODE_30149016, "已驳回状态才能进行更新"),
	CODE_30149017(ErrorCode.CODE_30149017, "已驳回状态才能进行删除"),
	CODE_30149018(ErrorCode.CODE_30149018, "待确认状态才能进行驳回"),
	CODE_30149019(ErrorCode.CODE_30149019, "待确认状态才能进行确认"),
	CODE_30149020(ErrorCode.CODE_30149020, "关联项目类型不正确"),
	CODE_30149021(ErrorCode.CODE_30149021, "变更后金额将为负数"),
	CODE_30149022(ErrorCode.CODE_30149022, "变更类型不能为空"),
	CODE_30149023(ErrorCode.CODE_30149023, "变更类型不存在"),
    CODE_30149024(ErrorCode.CODE_30149024, "申请金额不能大于当前合同的履约保证金"),
    CODE_30149025(ErrorCode.CODE_30149025, "您的申请金额过大，请调整金额"),
    CODE_30149026(ErrorCode.CODE_30149026, "当前履约保证金不足，无法操作"),



	// 供应商相关
    CODE_30150001(ErrorCode.CODE_30150001, "企业名称不能为空"),
    CODE_30150002(ErrorCode.CODE_30150002, "企业名称不超过32个字符"),
    CODE_30150003(ErrorCode.CODE_30150003, "统一社会信用代码不能为空"),
    CODE_30150004(ErrorCode.CODE_30150004, "法定代表人不能为空"),
    CODE_30150005(ErrorCode.CODE_30150005, "法定代表人不超过32个字符"),
    CODE_30150006(ErrorCode.CODE_30150006, "联系人不超过32个字符"),
    CODE_30150007(ErrorCode.CODE_30150007, "开户名称不能为空"),
    CODE_30150008(ErrorCode.CODE_30150008, "开户名称不超过32个字符"),
    CODE_30150009(ErrorCode.CODE_30150009, "银行账户不能为空"),
    CODE_30150010(ErrorCode.CODE_30150010, "银行账户不超过32个字符"),
    CODE_30150011(ErrorCode.CODE_30150011, "开户行不能为空"),
    CODE_30150012(ErrorCode.CODE_30150012, "开户行不超过32个字符"),
    CODE_30150013(ErrorCode.CODE_30150013, "该企业已存在"),
    CODE_30150014(ErrorCode.CODE_30150014, "银行账号不能超过10个"),
    CODE_30150015(ErrorCode.CODE_30150015, "默认银行账号不能超过1个"),
    CODE_30150016(ErrorCode.CODE_30150016, "供应商不存在"),
	// 账号不能为空
	CODE_30150017(ErrorCode.CODE_30150017, "账号不能为空"),
	// 密码不能为空
	CODE_30150018(ErrorCode.CODE_30150018, "密码不能为空"),
	// 请设置6-16位包含字母、数字的密码
	CODE_30150019(ErrorCode.CODE_30150019, "请设置6-16位包含字母、数字的密码"),
	// 当前账号已被使用
	CODE_30150020(ErrorCode.CODE_30150020, "当前账号已被使用"),
	// 供应商信息不存在,请先配置供应商信息
	CODE_30150021(ErrorCode.CODE_30150021, "供应商信息不存在,请先配置供应商信息"),
	// 当前供应链有业务数据，不支持变更身份
	CODE_30150022(ErrorCode.CODE_30150022, "当前供应链有业务数据，不支持变更身份"),




	// 账号相关
	CODE_30151001(ErrorCode.CODE_30151001, "合同不存在"),
	CODE_30151002(ErrorCode.CODE_30151002, "关联项目id不能为空"),
	CODE_30151003(ErrorCode.CODE_30151003, "合同名称不能为空，且不超过32个字符"),
	CODE_30151004(ErrorCode.CODE_30151004, "合同名称不可重复"),
	CODE_30151005(ErrorCode.CODE_30151005, "签署方式不能为空"),
	CODE_30151006(ErrorCode.CODE_30151006, "签署方式不存在"),
	CODE_30151007(ErrorCode.CODE_30151007, "签订日期不能为空"),
	CODE_30151008(ErrorCode.CODE_30151008, "起止日期不能为空"),
	CODE_30151009(ErrorCode.CODE_30151009, "外部合同编号不能超过32个字符"),
	CODE_30151010(ErrorCode.CODE_30151010, "最低预付款额度 取值范围 0-100亿 保留两位小数"),
	CODE_30151011(ErrorCode.CODE_30151011, "补充预付款额度 取值范围 0-100亿 保留两位小数"),
	CODE_30151012(ErrorCode.CODE_30151012, "保证金与货款为大于等于0小于100亿的数字，保留两位小数"),
	CODE_30151013(ErrorCode.CODE_30151013, "合同文件不能为空"),
	CODE_30151014(ErrorCode.CODE_30151014, "付款方式不存在"),
	CODE_30151015(ErrorCode.CODE_30151015, "备注不能超过200个字符"),
	CODE_30151016(ErrorCode.CODE_30151016, "该状态无法提交"),
	CODE_30151017(ErrorCode.CODE_30151017, "该状态无法发起签署"),
	CODE_30151018(ErrorCode.CODE_30151018, "该状态无法修改"),
	CODE_30151019(ErrorCode.CODE_30151019, "该状态无法驳回"),
	CODE_30151020(ErrorCode.CODE_30151020, "线上才能进行签署"),
	CODE_30151021(ErrorCode.CODE_30151021, "合同类型不存在"),
	CODE_30151022(ErrorCode.CODE_30151022, "驳回原因不能为空，且不超过200个字符"),
	CODE_30151023(ErrorCode.CODE_30151023, "该状态无法删除"),
	CODE_30151024(ErrorCode.CODE_30151024, "签署方式为线下并且待确认状态才能进行确认"),
	CODE_30151025(ErrorCode.CODE_30151025, "保存方式错误"),
	CODE_30151026(ErrorCode.CODE_30151026, "线上签署只有保存为草稿"),
	CODE_30151027(ErrorCode.CODE_30151027, "该合同已被关联，不支持删除"),
	CODE_30151028(ErrorCode.CODE_30151028, "无权限"),
	CODE_30151029(ErrorCode.CODE_30151029, "合同类型不能为空"),
	CODE_30151030(ErrorCode.CODE_30151030, "结算方式不能为空"),
	CODE_30151031(ErrorCode.CODE_30151031, "结算方式不存在"),
	CODE_30151032(ErrorCode.CODE_30151032, "账期不能为空"),
	CODE_30151033(ErrorCode.CODE_30151033, "账期为大于等于0小于等于1000的整数"),
	CODE_30151034(ErrorCode.CODE_30151034, "账期起始方式不能为空"),
	CODE_30151035(ErrorCode.CODE_30151035, "账期起始方式不存在"),
	CODE_30151036(ErrorCode.CODE_30151036, "自定义合同编号只能包含字母、数字、符号"),
	CODE_30151037(ErrorCode.CODE_30151037, "该状态无法撤回"),
	CODE_30151038(ErrorCode.CODE_30151038, "银行账户上限为5个"),
	CODE_30151039(ErrorCode.CODE_30151039, "采购方id不能为空"),
	CODE_30151040(ErrorCode.CODE_30151040, "销售方id不能为空"),
	CODE_30151041(ErrorCode.CODE_30151041, "至少选择两个签约主体"),
	CODE_30151042(ErrorCode.CODE_30151042, "借款方id不能为空"),
	CODE_30151043(ErrorCode.CODE_30151043, "资金方id不能为空"),
	CODE_30151044(ErrorCode.CODE_30151044, "融资产品不能为空"),
	CODE_30151045(ErrorCode.CODE_30151045, "融资产品不存在"),
	CODE_30151046(ErrorCode.CODE_30151046, "借款额度不能为空"),
	CODE_30151047(ErrorCode.CODE_30151047, "借款额度为大于0小于100亿的数字，保留两位小数"),
	CODE_30151048(ErrorCode.CODE_30151048, "额度有效期起止日期不能为空"),
	CODE_30151049(ErrorCode.CODE_30151049, "借款利率计算方式不能为空"),
	CODE_30151050(ErrorCode.CODE_30151050, "借款利率计算方式不存在"),
	CODE_30151051(ErrorCode.CODE_30151051, "借款利率点差不能为空"),
	CODE_30151052(ErrorCode.CODE_30151052, "借款利率点差为大于0小于100的数字，保留两位小数"),
	CODE_30151053(ErrorCode.CODE_30151053, "利率调整周期不能为空"),
	CODE_30151054(ErrorCode.CODE_30151054, "利率调整周期为大于0小于100的整数"),
	CODE_30151055(ErrorCode.CODE_30151055, "结息周期不能为空"),
	CODE_30151056(ErrorCode.CODE_30151056, "结息周期不存在"),
	CODE_30151057(ErrorCode.CODE_30151057, "结息日不能为空"),
	CODE_30151058(ErrorCode.CODE_30151058, "结息日不存在"),
	CODE_30151059(ErrorCode.CODE_30151059, "滚动货款为大于0小于100亿的数字，保留两位小数"),
	CODE_30151060(ErrorCode.CODE_30151060, "滚动货款补足金额为大于0小于100亿的数字，保留两位小数"),
	CODE_30151061(ErrorCode.CODE_30151061, "该合同已被关联，不支持修改"),
	CODE_30151062(ErrorCode.CODE_30151062, "存货方id不能为空"),
	CODE_30151063(ErrorCode.CODE_30151063, "仓库id不能为空"),
	CODE_30151064(ErrorCode.CODE_30151064, "免费存储天数应为 大于0小于1000的整数"),
	CODE_30151065(ErrorCode.CODE_30151065, "收费标准应为 大于0小于1000的数字，保留两位小数"),
	CODE_30151066(ErrorCode.CODE_30151066, "备注不能超过32个字符"),
	CODE_30151067(ErrorCode.CODE_30151067, "该存货方存在有效合同"),
	CODE_30151068(ErrorCode.CODE_30151068, "该用户没有供应链id"),
	CODE_30151069(ErrorCode.CODE_30151069, "用户的供应链id在供应链表中没有找到记录"),








	//项目相关
	CODE_30152001(ErrorCode.CODE_30152001, "项目名称不能为空"),
	CODE_30152002(ErrorCode.CODE_30152002, "项目名称不能超过32个字符"),
	CODE_30152003(ErrorCode.CODE_30152003, "项目介绍不能为空"),
	CODE_30152004(ErrorCode.CODE_30152004, "项目介绍不能超过200个字符"),
	CODE_30152005(ErrorCode.CODE_30152005, "上游供应商id不能为空"),
	CODE_30152006(ErrorCode.CODE_30152006, "下游采购方id不能为空"),
	CODE_30152007(ErrorCode.CODE_30152007, "项目负责人不能为空"),
	CODE_30152008(ErrorCode.CODE_30152008, "项目负责人名称不能为空"),
	CODE_30152009(ErrorCode.CODE_30152009, "项目经理id不能为空"),
	CODE_30152010(ErrorCode.CODE_30152010, "申请完结后才能点确认完结"),
	CODE_30152011(ErrorCode.CODE_30152011, "该项目存在逾期的应收款，不允许申请完结"),
	CODE_30152012(ErrorCode.CODE_30152012, "项目名称不支持重名"),
	CODE_30152013(ErrorCode.CODE_30152013, "项目不存在"),
	CODE_30152014(ErrorCode.CODE_30152014, "项目经理上限为10个"),
	CODE_30152015(ErrorCode.CODE_30152015, "该货物名称已被合同关联"),
	CODE_30152016(ErrorCode.CODE_30152016, "该项目已被合同关联"),
	CODE_30152017(ErrorCode.CODE_30152017, "该下游采购方已被合同关联"),
	CODE_30152018(ErrorCode.CODE_30152018, "上游供应商不可删除原有选项"),
	CODE_30152019(ErrorCode.CODE_30152019, "下游采购方不可删除原有选项"),
	CODE_30152020(ErrorCode.CODE_30152020, "当前操作用户不是该项目指派人员,不可操作"),
	CODE_30152021(ErrorCode.CODE_30152021, "货物id不能为空"),
	CODE_30152022(ErrorCode.CODE_30152022, "货物名称不能为空"),
	CODE_30152023(ErrorCode.CODE_30152023, "该项目存在未完成的单据，不允许申请完结"),
	CODE_30152024(ErrorCode.CODE_30152024, "上游供应商的上限为10个"),
	CODE_30152025(ErrorCode.CODE_30152025, "下游采购方的上限为10个"),
	CODE_30152026(ErrorCode.CODE_30152026, "项目最高限额为大于0小于100亿的数字，保留两位小数"),
	CODE_30152027(ErrorCode.CODE_30152027, "项目预计年化收益为大于0小于100的数字，保留两位小数"),
	CODE_30152028(ErrorCode.CODE_30152028, "库存控货比为大于0小于1000的数字，保留两位小数"),
	CODE_30152029(ErrorCode.CODE_30152029, "付款比例为大于0小于100的数字，保留两位小数"),
	CODE_30152030(ErrorCode.CODE_30152030, "余款账期为大于0小于1000的整数"),
	CODE_30152031(ErrorCode.CODE_30152031, "是否存在仓储不能为空"),
	CODE_30152032(ErrorCode.CODE_30152032, "预付货款为大于0小于100亿的数字，保留两位小数"),
	CODE_30152033(ErrorCode.CODE_30152033, "预付货款比例为大于0小于100的数字，保留两位小数"),
	CODE_30152034(ErrorCode.CODE_30152034, "账期为大于0小于1000的整数"),
	CODE_30152035(ErrorCode.CODE_30152035, "库存金额为大于0小于1亿的数字，保留两位小数"),
	CODE_30152036(ErrorCode.CODE_30152036, "存货方的上限为100个"),
	CODE_30152037(ErrorCode.CODE_30152037, "存货方不可删除原有选项"),
	CODE_30152038(ErrorCode.CODE_30152038, "增减项不能为空"),
	CODE_30152039(ErrorCode.CODE_30152039, "加减方向不能为空"),
	CODE_30152040(ErrorCode.CODE_30152040, "可提货余额的自定义行的 金额应为 大于0小于1亿的数字，保留两位小数"),
	CODE_30152041(ErrorCode.CODE_30152041, "可提货余额的自定义行的 金额不能为空"),
	CODE_30152042(ErrorCode.CODE_30152042, "该项目的可提货余额公式项不存在"),
	CODE_30152043(ErrorCode.CODE_30152043, "上游供应商是否自录数据不能为空"),
	CODE_30152044(ErrorCode.CODE_30152044, "下游采购方是否自录数据不能为空"),
	CODE_30152045(ErrorCode.CODE_30152045, "监管单位不能超过32个字符"),
	CODE_30152046(ErrorCode.CODE_30152046, "上下游企业重复选择"),
	CODE_30152047(ErrorCode.CODE_30152047, "请先完成组织机构认证"),
	CODE_30152048(ErrorCode.CODE_30152048, "该供应链不存在"),


















	// 收付款相关
	CODE_30153001(ErrorCode.CODE_30153001, "付款信息不存在"),
	CODE_30153002(ErrorCode.CODE_30153002, "收款信息不存在"),
	CODE_30153003(ErrorCode.CODE_30153003, "关联项目id不能为空"),
	CODE_30153004(ErrorCode.CODE_30153004, "关联合同id不能为空"),
	CODE_30153005(ErrorCode.CODE_30153005, "费用类型不能为空"),
	CODE_30153006(ErrorCode.CODE_30153006, "费用类型不存在"),
	CODE_30153007(ErrorCode.CODE_30153007, "金额不能为空"),
	CODE_30153008(ErrorCode.CODE_30153008, "金额 取值范围 0-100亿 保留两位小数"),
	CODE_30153009(ErrorCode.CODE_30153009, "付款日期不能为空"),
	CODE_30153010(ErrorCode.CODE_30153010, "付款方式不能为空"),
	CODE_30153011(ErrorCode.CODE_30153011, "付款方式不存在"),
	CODE_30153012(ErrorCode.CODE_30153012, "凭证不能为空"),
	CODE_30153013(ErrorCode.CODE_30153013, "备注不能超过200个字符"),
	CODE_30153014(ErrorCode.CODE_30153014, "驳回原因不能为空，且不超过200个字符"),
	CODE_30153015(ErrorCode.CODE_30153015, "该状态无法修改"),
	CODE_30153016(ErrorCode.CODE_30153016, "该状态无法删除"),
	CODE_30153017(ErrorCode.CODE_30153017, "该状态无法确认"),
	CODE_30153018(ErrorCode.CODE_30153018, "该状态无法驳回"),
	CODE_30153019(ErrorCode.CODE_30153019, "类型不能为空"),
	CODE_30153020(ErrorCode.CODE_30153020, "类型不存在"),
	CODE_30153021(ErrorCode.CODE_30153021, "采购方id不能为空"),
	CODE_30153022(ErrorCode.CODE_30153022, "销售方id不能为空"),
	CODE_30153023(ErrorCode.CODE_30153023, "付款方银行账户不能为空"),
	CODE_30153024(ErrorCode.CODE_30153024, "付款方开户名称不能为空"),
	CODE_30153025(ErrorCode.CODE_30153025, "付款方银行账号不能为空"),
	CODE_30153026(ErrorCode.CODE_30153026, "付款方开户行不能为空"),
	CODE_30153027(ErrorCode.CODE_30153027, "收款方银行账户id不能为空"),
	CODE_30153028(ErrorCode.CODE_30153028, "收款方开户名称不能为空"),
	CODE_30153029(ErrorCode.CODE_30153029, "收款方银行账号不能为空"),
	CODE_30153030(ErrorCode.CODE_30153030, "收款方开户行不能为空"),
	CODE_30153031(ErrorCode.CODE_30153031, "票据号码不超过30位数字"),
	CODE_30153032(ErrorCode.CODE_30153032, "承兑人不超过25字符"),
	CODE_30153033(ErrorCode.CODE_30153033, "信用证号码不超过16字符"),
	CODE_30153034(ErrorCode.CODE_30153034, "开证行不超过25字符"),
	CODE_30153035(ErrorCode.CODE_30153035, "收款方开户名称不超过25字符"),
	CODE_30153036(ErrorCode.CODE_30153036, "收款方银行账户不超过25个数字"),
	CODE_30153037(ErrorCode.CODE_30153037, "收款方开户行不超过25字符"),
	CODE_30153038(ErrorCode.CODE_30153038, "关联订单id不能为空"),
	CODE_30153039(ErrorCode.CODE_30153039, "关联应付款id不能为空"),
	CODE_30153040(ErrorCode.CODE_30153040, "关联对账单id不能为空"),
	CODE_30153041(ErrorCode.CODE_30153041, "金额不能大于关联项目的项目金额"),
	CODE_30153042(ErrorCode.CODE_30153042, "关联合同、关联订单不可都为空"),
	CODE_30153043(ErrorCode.CODE_30153043, "关联借据id不能为空"),
	CODE_30153044(ErrorCode.CODE_30153044, "资金方信息不能为空"),
    CODE_30153045(ErrorCode.CODE_30153045, "付款中的金额不能大于所选的应付款单列表中未付金额之和"),
    CODE_30153046(ErrorCode.CODE_30153046, "该付款已被关联"),



	// 开票相关
	CODE_30155001(ErrorCode.CODE_30155001, "开票单不存在"),
	CODE_30155002(ErrorCode.CODE_30155002, "项目id不能为空"),
	CODE_30155003(ErrorCode.CODE_30155003, "合同id不能为空"),
	CODE_30155004(ErrorCode.CODE_30155004, "关联的对账单不能为空"),
	CODE_30155005(ErrorCode.CODE_30155005, "类型不能为空"),
	CODE_30155006(ErrorCode.CODE_30155006, "单据id不能为空"),
	CODE_30155007(ErrorCode.CODE_30155007, "开票日期不能为空"),
	CODE_30155008(ErrorCode.CODE_30155008, "类型错误"),
	CODE_30155009(ErrorCode.CODE_30155009, "税率取值范围 0-100"),
	CODE_30155010(ErrorCode.CODE_30155010, "备注不能超过200个字符"),
	CODE_30155011(ErrorCode.CODE_30155011, "提交类型不能为空"),
	CODE_30155012(ErrorCode.CODE_30155012, "填写的开票金额超过未开票金额"),
	CODE_30155013(ErrorCode.CODE_30155013, "驳回原因不能为空"),
	CODE_30155014(ErrorCode.CODE_30155014, "驳回原因不能超过200个字符"),
	CODE_30155015(ErrorCode.CODE_30155015, "开票金额不能为空！"),

	//对账相关
	CODE_30154001(ErrorCode.CODE_30154001, "关联项目id不能为空"),
	CODE_30154002(ErrorCode.CODE_30154002, "关联合同id不能为空"),
	CODE_30154003(ErrorCode.CODE_30154003, "签署方式不能为空"),
	CODE_30154004(ErrorCode.CODE_30154004, "项目在交付状态才可以生成对账单"),
	CODE_30154005(ErrorCode.CODE_30154005, "实际对账金额不能为空"),
	CODE_30154006(ErrorCode.CODE_30154006, "对账日期不能为空"),
	CODE_30154007(ErrorCode.CODE_30154007, "备注长度不能超过200"),
	CODE_30154008(ErrorCode.CODE_30154008, "对账单不存在"),
	CODE_30154009(ErrorCode.CODE_30154009, "当前状态不能进行删除操作"),
	CODE_30154010(ErrorCode.CODE_30154010, "当前状态不能进行修改操作"),
	CODE_30154011(ErrorCode.CODE_30154011, "当前状态不能进行驳回操作"),
	CODE_30154012(ErrorCode.CODE_30154012, "只有待签署状态可以签署"),
	CODE_30154013(ErrorCode.CODE_30154013, "签署方式为线上并且状态草稿状态才能进行发起签署"),
	CODE_30154014(ErrorCode.CODE_30154014, "对账id不能为空"),
	CODE_30154015(ErrorCode.CODE_30154015, "驳回原因不能为空"),
	CODE_30154016(ErrorCode.CODE_30154016, "该对账已开票，不允许删除"),
	CODE_30154017(ErrorCode.CODE_30154017, "签署方式为线下 才能进行确认"),
	CODE_30154018(ErrorCode.CODE_30154018, "对账重量不能为空"),
	CODE_30154019(ErrorCode.CODE_30154019, "关联合同为先货后款时，保证金是否转货款 为必填"),
	CODE_30154020(ErrorCode.CODE_30154020, "关联合同为先货后款时，对账金额需大于订单保证金"),
	CODE_30154021(ErrorCode.CODE_30154021, "签署方式为线下并且草稿状态时 pc端才能提交"),
	CODE_30154022(ErrorCode.CODE_30154022, "草稿状态时 管理后台才能提交"),
	CODE_30154023(ErrorCode.CODE_30154023, "该状态无法撤回"),
	CODE_30154024(ErrorCode.CODE_30154024, "调用获取工作日接口异常"),
	CODE_30154025(ErrorCode.CODE_30154025, "请上传单据后提交"),
	CODE_30154026(ErrorCode.CODE_30154026, "关联合同为先货后款时，预对账金额需大于订单保证金"),
	CODE_30154027(ErrorCode.CODE_30154027, "预对账日期不能为空"),
	CODE_30154028(ErrorCode.CODE_30154028, "对账单据文件不能为空"),
	CODE_30154029(ErrorCode.CODE_30154029, "是否存在预对账不能为空"),
	CODE_30154030(ErrorCode.CODE_30154030, "账期不能为空"),
	CODE_30154031(ErrorCode.CODE_30154031, "存在预对账时 结算预付款不能为空"),
	CODE_30154032(ErrorCode.CODE_30154032, "存在预对账时 预结算方式不能为空"),
	CODE_30154033(ErrorCode.CODE_30154033, "预付比例 应大于等于0小于100"),
	CODE_30154034(ErrorCode.CODE_30154034, "预付金额 应大于等于0小于预对账金额"),









	// 货物相关
	CODE_30156001(ErrorCode.CODE_30156001, "货物不存在"),
	CODE_30156002(ErrorCode.CODE_30156002, "货物名称不能为空"),
	CODE_30156003(ErrorCode.CODE_30156003, "货物名称不能超过10个字符"),
	CODE_30156004(ErrorCode.CODE_30156004, "货物名称已存在"),
	CODE_30156005(ErrorCode.CODE_30156005, "该货物已被使用,不能删除或修改"),
	CODE_30156006(ErrorCode.CODE_30156006, "单位分类不能为空"),
	CODE_30156007(ErrorCode.CODE_30156007, "单位分类错误"),
	CODE_30156008(ErrorCode.CODE_30156008, "单位不能为空"),
	CODE_30156009(ErrorCode.CODE_30156009, "单位不能超过5个字符"),

	// 退款相关
	CODE_30157001(ErrorCode.CODE_30157001, "退款信息不存在"),
	CODE_30157002(ErrorCode.CODE_30157002, "关联项目id不能为空"),
	CODE_30157003(ErrorCode.CODE_30157003, "关联收款id不能为空"),
	CODE_30157004(ErrorCode.CODE_30157004, "退款日期不能为空"),
	CODE_30157005(ErrorCode.CODE_30157005, "退款方式不能为空"),
	CODE_30157006(ErrorCode.CODE_30157006, "退款方式不存在"),
	CODE_30157007(ErrorCode.CODE_30157007, "凭证不能为空"),
	CODE_30157008(ErrorCode.CODE_30157008, "退款原因不能为空"),
	CODE_30157009(ErrorCode.CODE_30157009, "退款原因不能超过200个字符"),
	CODE_30157010(ErrorCode.CODE_30157010, "该状态无法修改"),
	CODE_30157011(ErrorCode.CODE_30157011, "该状态无法删除"),
	CODE_30157012(ErrorCode.CODE_30157012, "该状态无法确认"),
	CODE_30157013(ErrorCode.CODE_30157013, "该状态无法驳回"),
	CODE_30157014(ErrorCode.CODE_30157014, "驳回原因不能为空"),
	CODE_30157015(ErrorCode.CODE_30157015, "驳回原因不能超过200个字符"),
	CODE_30157016(ErrorCode.CODE_30157016, "票据号码不超过30位数字"),
	CODE_30157017(ErrorCode.CODE_30157017, "承兑人不超过25字符"),
	CODE_30157018(ErrorCode.CODE_30157018, "信用证号码不超过16字符"),
	CODE_30157019(ErrorCode.CODE_30157019, "开证行不超过25字符"),
	CODE_30157020(ErrorCode.CODE_30157020, "付款方开户名称不能为空"),
	CODE_30157021(ErrorCode.CODE_30157021, "付款方银行账号不能为空"),
	CODE_30157022(ErrorCode.CODE_30157022, "付款方开户行不能为空"),
	CODE_30157023(ErrorCode.CODE_30157023, "收款方开户名称不能为空"),
	CODE_30157024(ErrorCode.CODE_30157024, "收款方银行账号不能为空"),
	CODE_30157025(ErrorCode.CODE_30157025, "收款方开户行不能为空"),
	CODE_30157026(ErrorCode.CODE_30157026, "该付款单已完成退款"),
	CODE_30157027(ErrorCode.CODE_30157027, "关联合同id不能为空"),
	CODE_30157028(ErrorCode.CODE_30157028, "无权限"),

	// 汽运单相关
	CODE_30158001(ErrorCode.CODE_30158001, "汽运单不存在"),
	CODE_30158002(ErrorCode.CODE_30158002, "地址不能为空"),
	CODE_30158003(ErrorCode.CODE_30158003, "装货地不能为空"),
	CODE_30158004(ErrorCode.CODE_30158004, "发货单id不能为空"),
	CODE_30158005(ErrorCode.CODE_30158005, "订单id不能为空"),
	CODE_30158006(ErrorCode.CODE_30158006, "司机名称不能为空"),
	CODE_30158007(ErrorCode.CODE_30158007, "司机手机号不能为空"),
	CODE_30158008(ErrorCode.CODE_30158008, "司机车牌号不能为空"),
	CODE_30158009(ErrorCode.CODE_30158009, "车型不能为空"),
	CODE_30158010(ErrorCode.CODE_30158010, "车长不能为空"),
	CODE_30158011(ErrorCode.CODE_30158011, "运费类型不能为空"),
	CODE_30158012(ErrorCode.CODE_30158012, "运费类型错误"),
	CODE_30158013(ErrorCode.CODE_30158013, "里程不能为空"),
	CODE_30158014(ErrorCode.CODE_30158014, "预估单价不能为空"),
	CODE_30158015(ErrorCode.CODE_30158015, "运费单价类型不能为空"),
	CODE_30158016(ErrorCode.CODE_30158016, "期望卸货时间不能为空"),
	CODE_30158017(ErrorCode.CODE_30158017, "期望装货时间不能为空"),
	CODE_30158018(ErrorCode.CODE_30158018, "货物名称不能为空"),
	CODE_30158019(ErrorCode.CODE_30158019, "货物类型不能为空"),
	CODE_30158020(ErrorCode.CODE_30158020, "联系人不能为空"),
	CODE_30158021(ErrorCode.CODE_30158021, "手机号不能为空"),
	CODE_30158022(ErrorCode.CODE_30158022, "手机号格式错误"),
	CODE_30158023(ErrorCode.CODE_30158023, "备注不能超过200个字符"),
	CODE_30158024(ErrorCode.CODE_30158024, "货主id不能为空"),
	CODE_30158025(ErrorCode.CODE_30158025, "货主名称不能为空"),
	CODE_30158026(ErrorCode.CODE_30158026, "预估运费支出不能为空"),
	CODE_30158027(ErrorCode.CODE_30158027, "运输重量不能为空"),
	CODE_30158028(ErrorCode.CODE_30158028, "运输体积不能为空"),
	CODE_30158029(ErrorCode.CODE_30158029, "运输件数不能为空"),
	CODE_30158030(ErrorCode.CODE_30158030, "发车时间不能为空"),
	CODE_30158031(ErrorCode.CODE_30158031, "发车地址不能为空"),
	CODE_30158032(ErrorCode.CODE_30158032, "发车车头照片不能为空"),
	CODE_30158033(ErrorCode.CODE_30158033, "只有待发车状态下才能发车"),
	CODE_30158034(ErrorCode.CODE_30158034, "上报地址不能为空"),
	CODE_30158035(ErrorCode.CODE_30158035, "异常原因不能超过200个字符"),
	CODE_30158036(ErrorCode.CODE_30158036, "卸货时间不能为空"),
	CODE_30158037(ErrorCode.CODE_30158037, "卸货地址不能为空"),
	CODE_30158038(ErrorCode.CODE_30158038, "卸货重量不能为空"),
	CODE_30158039(ErrorCode.CODE_30158039, "结算单据照片文件id不能为空"),
	CODE_30158040(ErrorCode.CODE_30158040, "已发车状态才能卸货"),
	CODE_30158041(ErrorCode.CODE_30158041, "已取消状态下不能异常上报"),
	CODE_30158042(ErrorCode.CODE_30158042, "待发车和已发车状态下才能修改"),
	CODE_30158043(ErrorCode.CODE_30158043, "卸货地不能为空"),
	CODE_30158044(ErrorCode.CODE_30158044, "车型错误"),
	CODE_30158045(ErrorCode.CODE_30158045, "车长错误"),
	CODE_30158046(ErrorCode.CODE_30158046, "途经点最多只能有8个"),
	CODE_30158047(ErrorCode.CODE_30158047, "发货完成的发货单不能新增和删除汽运单"),
	CODE_30158048(ErrorCode.CODE_30158048, "发货已取消，不能进行操作"),
	CODE_30158049(ErrorCode.CODE_30158049, "运单车辆轨迹公里数异常"),
	CODE_30158050(ErrorCode.CODE_30158050, "附件数量不能超过10个"),
	CODE_30158051(ErrorCode.CODE_30158051, "卸货时间必须晚于装货时间"),
	CODE_30158052(ErrorCode.CODE_30158052, "发车信息不能为空"),
	CODE_30158053(ErrorCode.CODE_30158053, "卸货信息不能为空"),
	CODE_30158054(ErrorCode.CODE_30158054, "单位不能为空"),
    CODE_30158055(ErrorCode.CODE_30158055, "车牌类型不能为空"),
    CODE_30158056(ErrorCode.CODE_30158056, "无法查询对应的车辆"),
    CODE_30158057(ErrorCode.CODE_30158057, "里程不能为空"),
    CODE_30158058(ErrorCode.CODE_30158058, "已卸货重量不能为空"),
    CODE_30158059(ErrorCode.CODE_30158059, "时间不能晚于当前时间"),

	// 服务费相关
	CODE_30159001(ErrorCode.CODE_30159001, "服务费信息不存在"),
	CODE_30159002(ErrorCode.CODE_30159002, "关联合同id不能为空"),
	CODE_30159003(ErrorCode.CODE_30159003, "金额不能为空"),
	CODE_30159004(ErrorCode.CODE_30159004, "金额为大于0小于100亿的数字，保留两位小数"),
	CODE_30159005(ErrorCode.CODE_30159005, "服务月份不能为空"),
	CODE_30159006(ErrorCode.CODE_30159006, "付款日期不能为空"),
	CODE_30159007(ErrorCode.CODE_30159007, "付款方式不能为空"),
	CODE_30159008(ErrorCode.CODE_30159008, "付款方式不存在"),
	CODE_30159009(ErrorCode.CODE_30159009, "备注不能超过200个字符"),
	CODE_30159010(ErrorCode.CODE_30159010, "驳回原因不能为空"),
	CODE_30159011(ErrorCode.CODE_30159011, "驳回原因不超过200个字符"),
	CODE_30159012(ErrorCode.CODE_30159012, "付款方银行账户id不能为空"),
	CODE_30159013(ErrorCode.CODE_30159013, "收款方银行账户id不能为空"),
	CODE_30159014(ErrorCode.CODE_30159014, "该状态无法修改"),
	CODE_30159015(ErrorCode.CODE_30159015, "该状态无法删除"),
	CODE_30159016(ErrorCode.CODE_30159016, "该状态无法确认"),
	CODE_30159017(ErrorCode.CODE_30159017, "该状态无法驳回"),
    CODE_30159018(ErrorCode.CODE_30159018, "费用类型只能是1.业务服务费 2.监管服务费"),
    CODE_30159019(ErrorCode.CODE_30159019, "费用类型不能为空"),

	// 天地图相关
	CODE_30160001(ErrorCode.CODE_30160001, "关键字不能为空"),
	CODE_30160002(ErrorCode.CODE_30160002, "行政区域国标码不能为空"),
	CODE_30160003(ErrorCode.CODE_30160003, "服务查询类型参数"),
	CODE_30160004(ErrorCode.CODE_30160004, "返回结果起始位不能为空"),
	CODE_30160005(ErrorCode.CODE_30160005, "返回结果数量不能为空"),

	// LPR利率相关
	CODE_30161001(ErrorCode.CODE_30161001, "发布日期不能为空"),
	CODE_30161002(ErrorCode.CODE_30161002, "发布日期不能超过当前日期"),
	CODE_30161003(ErrorCode.CODE_30161003, "利率类型不能为空"),
	CODE_30161004(ErrorCode.CODE_30161004, "利率不存在"),
	CODE_30161005(ErrorCode.CODE_30161005, "利率值不能为空"),
	CODE_30161006(ErrorCode.CODE_30161006, "利率值必须是大于0小于100的数字，保留两位小数"),
	CODE_30161007(ErrorCode.CODE_30161007, "当月LPR利率已发布"),
	CODE_30161008(ErrorCode.CODE_30161008, "LPR利率不存在"),

	// 铁路单相关
	CODE_30162001(ErrorCode.CODE_30162001, "铁路单不存在"),
	CODE_30162003(ErrorCode.CODE_30162003, "发货单id不能为空"),
	CODE_30162004(ErrorCode.CODE_30162004, "订单id不能为空"),
	CODE_30162005(ErrorCode.CODE_30162005, "货物名称不能为空"),
	CODE_30162006(ErrorCode.CODE_30162006, "规格/型号不能为空"),
	CODE_30162007(ErrorCode.CODE_30162007, "运输重量不能为空"),
	CODE_30162008(ErrorCode.CODE_30162008, "车种车号不能为空"),
	CODE_30162009(ErrorCode.CODE_30162009, "车种车号不超过15字符"),
	CODE_30162010(ErrorCode.CODE_30162010, "发站不能为空"),
	CODE_30162011(ErrorCode.CODE_30162011, "发站不超过32字符"),
	CODE_30162012(ErrorCode.CODE_30162012, "到站不能为空"),
	CODE_30162013(ErrorCode.CODE_30162013, "到站不超过32字符"),
	CODE_30162014(ErrorCode.CODE_30162014, "发车时间不能为空"),
	CODE_30162015(ErrorCode.CODE_30162015, "到达时间不能为空"),
	CODE_30162016(ErrorCode.CODE_30162016, "附件数量不能超过10个"),
	CODE_30162017(ErrorCode.CODE_30162017, "到达时间必须晚于发车时间"),
	CODE_30162018(ErrorCode.CODE_30162018, "铁路单已取消状态不能修改"),
	CODE_30162019(ErrorCode.CODE_30162019, "铁路单id不能为空"),
	CODE_30162020(ErrorCode.CODE_30162020, "发货完成不能新增铁路单"),
	CODE_30162021(ErrorCode.CODE_30162021, "只有发货中状态才能删除铁路单"),


	// 仓库相关
	CODE_30163001(ErrorCode.CODE_30163001, "仓库不存在"),
	CODE_30163002(ErrorCode.CODE_30163002, "仓库名称不能为空"),
	CODE_30163003(ErrorCode.CODE_30163003, "仓库名称不能超过32个字符"),
	CODE_30163004(ErrorCode.CODE_30163004, "所属主体不能为空"),
	CODE_30163005(ErrorCode.CODE_30163005, "规格/所属主体不能超过32个字符"),
	CODE_30163006(ErrorCode.CODE_30163006, "仓库类型不能为空"),
	CODE_30163007(ErrorCode.CODE_30163007, "仓库类型错误"),
	CODE_30163008(ErrorCode.CODE_30163008, "仓库地址不能超过32个字符"),
	CODE_30163009(ErrorCode.CODE_30163009, "负责人不能超过32个字符"),
	CODE_30163010(ErrorCode.CODE_30163010, "联系电话格式错误"),
	CODE_30163011(ErrorCode.CODE_30163011, "仓库名称不能重复"),
	CODE_30163012(ErrorCode.CODE_30163012, "该仓库已被使用"),
	CODE_30163013(ErrorCode.CODE_30163013, "该库位已关联设备，请先解除关联"),
	CODE_30163014(ErrorCode.CODE_30163014, "该仓库已关联设备，请先解除关联"),


	// 库位相关
	CODE_30164001(ErrorCode.CODE_30164001, "库位不存在"),
	CODE_30164002(ErrorCode.CODE_30164002, "库位名称不能为空"),
	CODE_30164003(ErrorCode.CODE_30164003, "库位名称不能超过32个字符"),
	CODE_30164004(ErrorCode.CODE_30164004, "库位地址不能为空"),
	CODE_30164005(ErrorCode.CODE_30164005, "库位地址不能超过32个字符"),
	CODE_30164006(ErrorCode.CODE_30164006, "负责人不能超过32个字符"),
	CODE_30164007(ErrorCode.CODE_30164007, "联系电话格式错误"),
	CODE_30164008(ErrorCode.CODE_30164008, "库位名称不能重复"),


	// 质押相关
	CODE_30165001(ErrorCode.CODE_30165001, "质押单不存在"),
	CODE_30165002(ErrorCode.CODE_30165002, "项目id不能为空"),
	CODE_30165003(ErrorCode.CODE_30165003, "合同id不能为空"),
	CODE_30165004(ErrorCode.CODE_30165004, "出质人id不能为空"),
	CODE_30165005(ErrorCode.CODE_30165005, "质权人id不能为空"),
	CODE_30165006(ErrorCode.CODE_30165006, "质押信息不能为空"),
	CODE_30165007(ErrorCode.CODE_30165007, "质押数量不能为空"),
	CODE_30165008(ErrorCode.CODE_30165008, "生效日期不能为空"),
	CODE_30165009(ErrorCode.CODE_30165009, "附件不能为空"),
	CODE_30165010(ErrorCode.CODE_30165010, "备注不能超过32个字符"),
	CODE_30165011(ErrorCode.CODE_30165011, "该合同已存在质押单"),

	// 仓储期初相关
	CODE_30166001(ErrorCode.CODE_30166001, "仓储期初单不存在"),
	CODE_30166002(ErrorCode.CODE_30166002, "关联项目不能为空"),
	CODE_30166003(ErrorCode.CODE_30166003, "货物名称不能为空"),
	CODE_30166004(ErrorCode.CODE_30166004, "请选择合同"),
	CODE_30166005(ErrorCode.CODE_30166005, "关联合同不能为空"),
	CODE_30166006(ErrorCode.CODE_30166006, "仓库/库位不能为空"),
	CODE_30166007(ErrorCode.CODE_30166007, "规格/型号不能为空"),
	CODE_30166008(ErrorCode.CODE_30166008, "数量/重量不能为空"),
	CODE_30166009(ErrorCode.CODE_30166009, "库位不存在"),
	CODE_30166010(ErrorCode.CODE_30166010, "请不要选择重复的合同"),
	CODE_30166011(ErrorCode.CODE_30166011, "一个合同下不能选择重复的仓库"),
	CODE_30166012(ErrorCode.CODE_30166012, "一个仓库不能选择重复的规格"),
	CODE_30166013(ErrorCode.CODE_30166013, "单位不能为空"),
	CODE_30166014(ErrorCode.CODE_30166014, "库存数量/重量小于0"),


	// 融资相关
	// 融资产品不能为空
	CODE_30170001(ErrorCode.CODE_30170001, "融资产品不能为空"),
	CODE_30170002(ErrorCode.CODE_30170002, "借款额度不能为空"),
	CODE_30170003(ErrorCode.CODE_30170003, "借款额度应为大于0小于100亿的数字，保留两位小数"),
	CODE_30170004(ErrorCode.CODE_30170004, "借款利率应为大于0小于100的数字,保留两位小数"),
	CODE_30170005(ErrorCode.CODE_30170005, "借款类型不能为空"),
	CODE_30170006(ErrorCode.CODE_30170006, "结息周期不能为空"),
	CODE_30170007(ErrorCode.CODE_30170007, "结息日不能为空"),
	CODE_30170008(ErrorCode.CODE_30170008, "额度有效期开始时间不能为空"),
	CODE_30170009(ErrorCode.CODE_30170009, "额度有效期结束时间不能为空"),
	CODE_30170010(ErrorCode.CODE_30170010, "放款日期不能为空"),
	CODE_30170011(ErrorCode.CODE_30170011, "受托收款账户不能为空"),
	CODE_30170012(ErrorCode.CODE_30170012, "融资信息不存在"),
	CODE_30170013(ErrorCode.CODE_30170013, "结息周期选择按月时，结息日应为大于0小于29的整数"),
	CODE_30170014(ErrorCode.CODE_30170014, "结息周期选择按季、按年时，结息日应为大于0小于31的整数"),
	CODE_30170015(ErrorCode.CODE_30170015, "当前单据已确认"),
	CODE_30170016(ErrorCode.CODE_30170016, "银行端未确认。当前单据不能确认"),
	CODE_30170017(ErrorCode.CODE_30170017, "银行端已确认，单据不能修改"),
	CODE_30170018(ErrorCode.CODE_30170018, "利率调整周期不能为空"),
	CODE_30170019(ErrorCode.CODE_30170019, "利率调整周期应为大于0小于100的整数"),

	// 还款相关
	// 还款id不能为空
	CODE_30171001(ErrorCode.CODE_30171001, "还款id不能为空"),
	// 驳回原因不能为空
	CODE_30171002(ErrorCode.CODE_30171002, "驳回原因不能为空"),
	//还款单不存在
	CODE_30171003(ErrorCode.CODE_30171003, "还款单不存在"),
	//扣款日期不能为空
	CODE_30171004(ErrorCode.CODE_30171004, "扣款日期不能为空"),
	//滚动货款不足
	CODE_30171005(ErrorCode.CODE_30171005, "滚动货款不足"),
	// 本金应 大于0小于100亿的数字，保留两位小数。
	CODE_30171006(ErrorCode.CODE_30171006, "本金 应为大于0小于100亿的数字，保留两位小数"),
	//输入的本金不能大于贷款剩余本金
	CODE_30171007(ErrorCode.CODE_30171007, "输入的本金不能大于贷款剩余本金"),
    //传入的还款本金有误
	CODE_30171008(ErrorCode.CODE_30171008, "传入的还款本金有误"),
	//传入的还款利息有误
	CODE_30171009(ErrorCode.CODE_30171009, "传入的还款利息有误"),
	//传入的还款合计有误
	CODE_30171010(ErrorCode.CODE_30171010, "传入的还款合计有误"),
	//传入的预计释放额度有误
	CODE_30171011(ErrorCode.CODE_30171011, "传入的预计释放额度有误"),
	//还款明细不存在
	CODE_30171012(ErrorCode.CODE_30171012, "还款明细不存在"),
	//关联合同不存在
	CODE_30171013(ErrorCode.CODE_30171013, "关联合同不存在"),
	//实际扣款日期只能选择不早于申请日期，不晚于当天。
	CODE_30171014(ErrorCode.CODE_30171014, "实际扣款日期只能选择不早于申请日期，不晚于当天。"),
	//借据不存在
	CODE_30171015(ErrorCode.CODE_30171015, "借据不存在"),
	// 存在借据还款总金额大于贷款本金
	CODE_30171016(ErrorCode.CODE_30171016, "存在借据还款总金额大于贷款本金"),

	// 项目期初相关
	// 请填写期初数据
	CODE_30172001(ErrorCode.CODE_30172001, "请填写期初数据"),
	// 项目期初信息不存在
	CODE_30172002(ErrorCode.CODE_30172002, "项目期初信息不存在"),
	//项目期初明细不存在
	CODE_30172003(ErrorCode.CODE_30172003, "项目期初明细不存在"),
	//输入的货款收入/支出总金额应为 大于0小于100亿的数字，保留两位小数
	CODE_30172004(ErrorCode.CODE_30172004, "输入的货款收入/支出总金额应为 大于0小于100亿的数字，保留两位小数"),
	//输入的对账总金额应为 大于0小于100亿的数字，保留两位小数
	CODE_30172005(ErrorCode.CODE_30172005, "输入的对账总金额应为 大于0小于100亿的数字，保留两位小数"),
	// 输入的对账总数量/重量应为 大于0小于100亿的数字
	CODE_30172006(ErrorCode.CODE_30172006, " 输入的对账总数量/重量应为 大于0小于100亿的数字"),
	// 输入的开票总金额 应为 大于0小于100亿的数字，保留两位小数
	CODE_30172007(ErrorCode.CODE_30172007, " 输入的开票总金额 应为 大于0小于100亿的数字，保留两位小数"),
	// 每个项目只能新增一次期初
	CODE_30172008(ErrorCode.CODE_30172008, " 每个项目只能新增一次期初"),
	// 请不要选择重复的合同
	CODE_30172009(ErrorCode.CODE_30172009, " 请不要选择重复的合同"),
	// 至少需要选择一个合同
	CODE_30172010(ErrorCode.CODE_30172010, " 至少需要选择一个合同"),

	// 盘点相关
	// 关联项目id不能为空
	CODE_30173001(ErrorCode.CODE_30173001, "关联项目id不能为空"),
	// 关联合同id不能为空
	CODE_30173002(ErrorCode.CODE_30173002, "关联合同id不能为空"),
	// 签署方式不能为空
	CODE_30173003(ErrorCode.CODE_30173003, "签署方式不能为空"),
	// 盘点类型不能为空
	CODE_30173004(ErrorCode.CODE_30173004, "盘点类型不能为空"),
	// 盘点日期不能为空
	CODE_30173005(ErrorCode.CODE_30173005, "盘点时间不能为空"),
	// 备注长度不能超过200
	CODE_30173006(ErrorCode.CODE_30173006, "备注长度不能超过200"),
	// 是否显示盈亏不能为空
	CODE_30173007(ErrorCode.CODE_30173007, "是否显示盈亏不能为空"),
	// 仓库id不能为空
	CODE_30173008(ErrorCode.CODE_30173008, "仓库id不能为空"),
	// 输入的盘点数量/重量应为 大于0小于1000万的数字，保留实际小数位，不超过2位
	CODE_30173009(ErrorCode.CODE_30173009, "输入的盘点数量应为 大于0小于1000万的数字，保留实际小数位，不超过2位"),
	// 盘点数量/重量 不能为空
	CODE_30173010(ErrorCode.CODE_30173010, "盘点数量/重量 不能为空"),
	// 输入的盘重应为 大于0小于1000万的数字，保留实际小数位，不超过2位
	CODE_30173011(ErrorCode.CODE_30173011, "输入的盘重应为 大于0小于1000万的数字，保留实际小数位，不超过2位"),
	// 盘重 不能为空
	CODE_30173012(ErrorCode.CODE_30173012, "盘重 不能为空"),
	// 输入的毛重应为 大于0小于1000万的数字，保留实际小数位，不超过2位
	CODE_30173013(ErrorCode.CODE_30173013, "输入的毛重应为 大于0小于1000万的数字，保留实际小数位，不超过2位"),
	// 毛重 不能为空
	CODE_30173014(ErrorCode.CODE_30173014, "毛重 不能为空"),
	// 盘点信息 不存在
	CODE_30173015(ErrorCode.CODE_30173015, "盘点信息 不存在"),
	// 盘点明细不存在
	CODE_30173016(ErrorCode.CODE_30173016, "盘点明细不存在"),
	// 盘点明细id不能为空
	CODE_30173017(ErrorCode.CODE_30173017, "盘点明细id不能为空"),
	// 盘点id不能为空
	CODE_30173018(ErrorCode.CODE_30173018, "盘点id不能为空"),
	// 驳回原因不能为空
	CODE_30173019(ErrorCode.CODE_30173019, "盘点id不能为空"),
	// 签署方式为线下 才能进行确认
	CODE_30173020(ErrorCode.CODE_30173020, "签署方式为线下 才能进行确认"),
	// 传入的盘点总数量/重量有误
	CODE_30173021(ErrorCode.CODE_30173021, "传入的盘点总数量/重量有误"),
	// 传入的盈亏数量/重量有误
	CODE_30173022(ErrorCode.CODE_30173022, "传入的盈亏数量/重量有误"),
	// 传入的净重合计有误
	CODE_30173023(ErrorCode.CODE_30173023, "传入的净重合计有误"),
	// 盘点照片最大上传50张
	CODE_30173024(ErrorCode.CODE_30173024, "盘点照片最大上传50张"),
	// 盘点总数量/重量不能为空
	CODE_30173025(ErrorCode.CODE_30173025, "盘点总数量/重量不能为空"),
	//  盈亏数量/重量不能为空
	CODE_30173026(ErrorCode.CODE_30173026, " 盈亏数量/重量不能为空"),
	// 净重合计不能为空
	CODE_30173027(ErrorCode.CODE_30173027, "净重合计不能为空"),
	// 盘点人id不能为空
	CODE_30173028(ErrorCode.CODE_30173028, "盘点人id不能为空"),
	// 盘点人名称不能为空
	CODE_30173029(ErrorCode.CODE_30173029, "盘点人名称不能为空"),
	// 盘点时间不能超过当前时间
	CODE_30173030(ErrorCode.CODE_30173030, "盘点时间不能超过当前时间"),
	// 该状态无法确认盘点
	CODE_30173031(ErrorCode.CODE_30173031, "该状态无法确认盘点"),

	// 抽检相关
	// 关联项目id不能为空
	CODE_30174001(ErrorCode.CODE_30174001, "关联项目id不能为空"),
	// 关联合同id不能为空
	CODE_30174002(ErrorCode.CODE_30174002, "关联合同id不能为空"),
	// 签署方式不能为空
	CODE_30174003(ErrorCode.CODE_30174003, "签署方式不能为空"),
	// 抽检类型不能为空
	CODE_30174004(ErrorCode.CODE_30174004, "抽检类型不能为空"),
	// 抽检日期不能为空
	CODE_30174005(ErrorCode.CODE_30174005, "抽检时间不能为空"),
	// 备注长度不能超过200
	CODE_30174006(ErrorCode.CODE_30174006, "备注长度不能超过200"),
	// 货物信息不能为空
	CODE_30174007(ErrorCode.CODE_30174007, "货物信息不能为空"),
	// 仓库id不能为空
	CODE_30174008(ErrorCode.CODE_30174008, "仓库id不能为空"),
	// 检测人不能为空
	CODE_30174009(ErrorCode.CODE_30174009, "检测人不能为空"),
	// 抽检结果不能为空
	CODE_30174010(ErrorCode.CODE_30174010, "抽检结果不能为空"),
	// 取回日期不能为空
	CODE_30174011(ErrorCode.CODE_30174011, "取回日期不能为空"),
	// 取样地点不能为空
	CODE_30174012(ErrorCode.CODE_30174012, "取样地点不能为空"),
	// 取样地点不能超过32个字符
	CODE_30174013(ErrorCode.CODE_30174013, "取样地点不能超过32个字符"),
	// 检测人不能超过32个字符
	CODE_30174014(ErrorCode.CODE_30174014, "检测人不能超过32个字符"),
	// 抽检数量应大于0小于1000万
	CODE_30174015(ErrorCode.CODE_30174015, "抽检数量应大于0小于1000万"),
	// 抽检数量不能为空
	CODE_30174016(ErrorCode.CODE_30174016, "抽检数量不能为空"),
	// 检测结果应大于0小于100
	CODE_30174017(ErrorCode.CODE_30174017, "检测结果不能超过32个字符"),
	// 检测结果不能为空
	CODE_30174018(ErrorCode.CODE_30174018, "检测结果不能为空"),
	// 备注不能超过32个字符
	CODE_30174019(ErrorCode.CODE_30174019, "备注不能超过32个字符"),
	// 规格/型号不能重复
	CODE_30174020(ErrorCode.CODE_30174020, "规格/型号不能重复"),
	// 规格/型号不能为空
	CODE_30174021(ErrorCode.CODE_30174021, "规格/型号不能为空"),
	// 传入的抽检合计数量有误
	CODE_30174022(ErrorCode.CODE_30174022, "传入的抽检合计数量有误"),
	// 抽检照片最大上传50张
	CODE_30174023(ErrorCode.CODE_30174023, "抽检照片最大上传50张"),
	// 抽检总数量不能为空
	CODE_30174024(ErrorCode.CODE_30174024, "抽检总数量不能为空"),
	// 抽检人id不能为空
	CODE_30174025(ErrorCode.CODE_30174025, "抽检人id不能为空"),
	// 抽检人名称不能为空
	CODE_30174026(ErrorCode.CODE_30174026, "抽检人名称不能为空"),
	// 抽检时间不能超过当前时间
	CODE_30174027(ErrorCode.CODE_30174027, "抽检时间不能超过当前时间"),
	// 该状态无法确认抽检
	CODE_30174028(ErrorCode.CODE_30174028, "该状态无法确认抽检"),


	// 出库相关
	// 出库单不存在
	CODE_30175001(ErrorCode.CODE_30175001, "出库单不存在"),
	// 出库类型不能为空
	CODE_30175002(ErrorCode.CODE_30175002, "出库类型不能为空"),
	// 签署类型不能为空
	CODE_30175003(ErrorCode.CODE_30175003, "签署类型不能为空"),
	// 仓库id不能为空
	CODE_30175004(ErrorCode.CODE_30175004, "仓库id不能为空"),
	// 是否计算出库金额不能为空
	CODE_30175005(ErrorCode.CODE_30175005, "是否计算出库金额不能为空"),
	// 是否有检测不能为空
	CODE_30175006(ErrorCode.CODE_30175006, "是否有检测不能为空"),
	// 检测人不能为空
	CODE_30175007(ErrorCode.CODE_30175007, "检测人不能为空"),
	// 检测日期不能为空
	CODE_30175008(ErrorCode.CODE_30175008, "检测日期不能为空"),
	// 保存类型不能为空
	CODE_30175009(ErrorCode.CODE_30175009, "保存类型不能为空"),
	// 非草稿状态不能提交
	CODE_30175010(ErrorCode.CODE_30175010, "非草稿状态不能提交"),
	// 只有确认中状态以及线下单据才能进行确认操作
	CODE_30175011(ErrorCode.CODE_30175011, "只有确认中状态以及线下单据才能进行确认操作"),
	// 只有确认中状态以及线下单据才能进行驳回操作
	CODE_30175012(ErrorCode.CODE_30175012, "此单据状态无法驳回"),
	// 状态不为已出库，无法发起作废
	CODE_30175013(ErrorCode.CODE_30175013, "状态不为已出库，无法发起作废"),
	// 入库单不存在
	CODE_30175014(ErrorCode.CODE_30175014, "入库单不存在"),
	// 只有待入库状态才可进行确认
	CODE_30175015(ErrorCode.CODE_30175015, "只有待入库状态才可进行确认"),
	// 状态不为已入库，无法发起作废
	CODE_30175016(ErrorCode.CODE_30175016, "状态不为已入库，无法发起作废"),
	// 该签收单已被关联，不能重复关联
	CODE_30175017(ErrorCode.CODE_30175017, "该签收单已被关联，不能重复关联"),
	// 库存不足
	CODE_30175018(ErrorCode.CODE_30175018, "库存不足"),
	// 出库数量/重量应大于0小于10000000
	CODE_30175019(ErrorCode.CODE_30175019, "出库数量/重量应大于0小于10000000"),
	// 该出库单已被关联，不能重复关联
	CODE_30175020(ErrorCode.CODE_30175020, "该出库单已被关联，不能重复关联"),
	// 该出库单已被入库单关联，不可作废
	CODE_30175021(ErrorCode.CODE_30175021, "该出库单已被入库单关联，不可作废"),
	// 入库数量不能大于关联签收单的签收数量
	CODE_30175022(ErrorCode.CODE_30175022, "入库数量不能大于关联签收单的签收数量"),
	// 线上只可保存
	CODE_30175023(ErrorCode.CODE_30175023, "线上只可保存"),
	// 该状态不可撤回
	CODE_30175024(ErrorCode.CODE_30175024, "该状态不可撤回"),
	// 该状态不可确认
	CODE_30175025(ErrorCode.CODE_30175025, "该状态不可确认"),
	// {0}（规格/型号）出库数量/重量大于库存数量/重量
	CODE_30175026(ErrorCode.CODE_30175026, "{0}出库数量/重量大于库存数量/重量"),
	// 此状态不可作废
	CODE_30175027(ErrorCode.CODE_30175027, "此状态不可作废"),
	CODE_30175028(ErrorCode.CODE_30175028, "入库人不能为空"),
	CODE_30175029(ErrorCode.CODE_30175029, "入库人限制2-6个字"),
	CODE_30175030(ErrorCode.CODE_30175030, "入库时间不能为空"),
	CODE_30175031(ErrorCode.CODE_30175031, "收费标准不能为空"),
	CODE_30175032(ErrorCode.CODE_30175032, "出库人不能为空"),
	CODE_30175033(ErrorCode.CODE_30175033, "出库人限制2-6个字"),
	CODE_30175034(ErrorCode.CODE_30175034, "出库时间不能为空"),
	CODE_30175035(ErrorCode.CODE_30175035, "检测信息不能为空"),
	CODE_30175036(ErrorCode.CODE_30175036, "检测结果不能为空"),
	CODE_30175037(ErrorCode.CODE_30175037, "取样柜台、车间不能为空"),
	CODE_30175038(ErrorCode.CODE_30175038, "入库时间不能超过当前时间"),
	CODE_30175039(ErrorCode.CODE_30175039, "出库时间不能超过当前时间"),
	CODE_30175040(ErrorCode.CODE_30175040, "出库单据不能为空"),
	CODE_30175041(ErrorCode.CODE_30175041, "待出库状态才能出库"),
	CODE_30175042(ErrorCode.CODE_30175042, "已出库状态才能作废"),
	CODE_30175043(ErrorCode.CODE_30175043, "作废原因不能超过200个字符"),

	// 核库报表相关
	// 关联项目id不能为空
	CODE_30176001(ErrorCode.CODE_30176001, "关联项目id不能为空"),
	// 关联合同id不能为空
	CODE_30176002(ErrorCode.CODE_30176002, "关联合同id不能为空"),
	// 上日加权平均价： 大于0，不超过100万，保留两位小数
	CODE_30176003(ErrorCode.CODE_30176003, "上日加权平均价： 大于0，不超过100万，保留两位小数"),
	// 核库日期不能为空
	CODE_30176004(ErrorCode.CODE_30176004, "核库日期不能为空"),
	// 上日加权平均价不能为空
	CODE_30176005(ErrorCode.CODE_30176005, "上日加权平均价不能为空"),
	//  核库报表信息 不存在
	CODE_30176006(ErrorCode.CODE_30176006, " 核库报表信息 不存在"),
	//  此合同已核库完成，不可重复核库
	CODE_30176007(ErrorCode.CODE_30176007, "此合同已核库完成，不可重复核库"),

	// 摄像头相关
	// 摄像头已上线，不能删除
	CODE_30177001(ErrorCode.CODE_30177001, "摄像头已上线，不能删除"),
	// 摄像头不存在
	CODE_30177002(ErrorCode.CODE_30177002, "摄像头不存在"),
	// 离线状态不能查看视频
	CODE_30177003(ErrorCode.CODE_30177003, "离线状态不能查看视频"),
	// 已有相同序列号的设备
	CODE_30177004(ErrorCode.CODE_30177004, "已有相同序列号的设备"),
	// 序列号不能为空
	CODE_30177005(ErrorCode.CODE_30177005, "序列号不能为空"),
	// ICCID不正确
	CODE_30177006(ErrorCode.CODE_30177006, "ICCID不正确"),
	// 摄像头名称不能为空
	CODE_30177007(ErrorCode.CODE_30177007, "摄像头名称不能为空"),
	// 摄像头名称长度不能超过40
	CODE_30177008(ErrorCode.CODE_30177008, "摄像头名称长度不能超过40"),
	// 仓库id不能为空
	CODE_30177009(ErrorCode.CODE_30177009, "仓库id不能为空"),
	// 安装位置不能为空
	CODE_30177010(ErrorCode.CODE_30177010, "安装位置不能为空"),
	// 已有相同名称的设备
	CODE_30177011(ErrorCode.CODE_30177011, "已有相同名称的设备"),
	CODE_30177012(ErrorCode.CODE_30177012, "设备令牌不能为空"),
	CODE_30177013(ErrorCode.CODE_30177013, "设备令牌不能超过20个字符"),
	CODE_30177014(ErrorCode.CODE_30177014, "ICCID不能超过20个字符"),
	CODE_30177015(ErrorCode.CODE_30177015, "序列号不能超过9个字符"),
	CODE_30177016(ErrorCode.CODE_30177016, "设备类型不能为空"),
	CODE_30177017(ErrorCode.CODE_30177017, "设备类型错误"),
	CODE_30177018(ErrorCode.CODE_30177018, "设备id不能为空"),
	CODE_30177019(ErrorCode.CODE_30177019, "安装位置不能超过20个字符"),
	CODE_30177020(ErrorCode.CODE_30177020, "无法删除，请先解除关联摄像头"),

	// 往来客户相关
	CODE_30178001(ErrorCode.CODE_30178001, "往来客户不存在"),
	CODE_30178002(ErrorCode.CODE_30178002, "该往来客户已被平台禁用"),
	CODE_30178003(ErrorCode.CODE_30178003, "已启用的客户不能删除"),
	CODE_30178004(ErrorCode.CODE_30178004, "不能重复绑定同一个客户"),

    //<editor-fold desc="过户管理相关">
    CODE_30179001(ErrorCode.CODE_30179001, "过户信息不存在"),
    CODE_30179002(ErrorCode.CODE_30179002, "只有草稿状态或驳回状态才可修改"),
    CODE_30179003(ErrorCode.CODE_30179003, "删除对象不存在"),
    CODE_30179004(ErrorCode.CODE_30179004, "只有草稿状态或驳回状态才可删除"),
    CODE_30179005(ErrorCode.CODE_30179005, "草稿、驳回、过户完成状态不能驳回操作"),
    CODE_30179006(ErrorCode.CODE_30179006, "下载pdf文档错误"),
    CODE_30179007(ErrorCode.CODE_30179007, "勾选的列表中过户信息不存在"),

    CODE_30179008(ErrorCode.CODE_30179008, "项目id不能为空"),
    CODE_30179009(ErrorCode.CODE_30179009, "合同ID不能为空"),
    CODE_30179010(ErrorCode.CODE_30179010, "货物信息列表不能为空"),
    CODE_30179011(ErrorCode.CODE_30179011, "买方ID不能为空"),
    CODE_30179012(ErrorCode.CODE_30179012, "买方名称不能为空"),
    CODE_30179013(ErrorCode.CODE_30179013, "卖方ID不能为空"),
    CODE_30179014(ErrorCode.CODE_30179014, "卖方名称不能为空"),
    CODE_30179015(ErrorCode.CODE_30179015, "所属仓库id不能为空"),
    CODE_30179016(ErrorCode.CODE_30179016, "所属仓库名称不能为空"),
    CODE_30179017(ErrorCode.CODE_30179017, "过户总数量/重量不能为空"),
    CODE_30179018(ErrorCode.CODE_30179018, "净重不能为空"),
    CODE_30179019(ErrorCode.CODE_30179019, "毛重不能为空"),
    CODE_30179020(ErrorCode.CODE_30179020, "保存类型不能为空"),
    CODE_30179021(ErrorCode.CODE_30179021, "过户日期不能为空"),
    CODE_30179022(ErrorCode.CODE_30179022, "备注长度不能超过200个字符"),
    CODE_30179023(ErrorCode.CODE_30179023, "过户数量范围为0~1000万"),
    CODE_30179024(ErrorCode.CODE_30179024, "过户的净重、毛重范围是0~1000万"),
    CODE_30179025(ErrorCode.CODE_30179025, "保存类型只有保存为草稿或保存并提交两种类型"),
    CODE_30179026(ErrorCode.CODE_30179026, "非提交状态不能审核"),
    CODE_30179027(ErrorCode.CODE_30179027, "签署类型只有线上签署和线下签署两种类型"),
    CODE_30179028(ErrorCode.CODE_30179028, "非买方核实状态不能签署"),
    CODE_30179029(ErrorCode.CODE_30179029, "非仓储方审核完毕，卖方不能核实"),
    CODE_30179030(ErrorCode.CODE_30179030, "非卖方审核完毕，买方不能核实"),
    CODE_30179031(ErrorCode.CODE_30179031, "当前用户不是签章使用者"),
    CODE_30179032(ErrorCode.CODE_30179032, "此业务非签署中状态，不能签署"),
    CODE_30179033(ErrorCode.CODE_30179033, "当前用户非生成签署链接用户，不能签署"),
    CODE_30179034(ErrorCode.CODE_30179034, "验证码错误或已过期"),
    CODE_30179035(ErrorCode.CODE_30179035, "{0}过户数量/重量大于库存数量/重量"),
    CODE_30179036(ErrorCode.CODE_30179036, "该仓库下不存在{0}数据"),
    CODE_30179037(ErrorCode.CODE_30179037, "买卖双方不同为同一个"),
    CODE_30179038(ErrorCode.CODE_30179038, "货物信息列表中存在相同规格/型号数据"),
    CODE_30179039(ErrorCode.CODE_30179039, "买方不存在仓储合同，不支持过户"),
    CODE_30179040(ErrorCode.CODE_30179040, "买方仓储合同未选择{0}/{1}"),

        //</editor-fold>
	//往来企业相关
	CODE_30180001(ErrorCode.CODE_30180001, "往来企业不存在"),
	CODE_30180002(ErrorCode.CODE_30180002, "企业类型不能为空"),
	CODE_30180003(ErrorCode.CODE_30180003, "企业类型不存在"),
	CODE_30180004(ErrorCode.CODE_30180004, "类型不能为空"),
	CODE_30180005(ErrorCode.CODE_30180005, "类型不存在"),
	CODE_30180006(ErrorCode.CODE_30180006, "企业名称不能超过32个字符"),
	CODE_30180007(ErrorCode.CODE_30180007, "法定代表人不能超过32个字符"),
	CODE_30180008(ErrorCode.CODE_30180008, "联系人不能超过32个字符"),
	CODE_30180009(ErrorCode.CODE_30180009, "手机号码不能超过11个字符"),
	CODE_30180010(ErrorCode.CODE_30180010, "账号不能为空"),
	CODE_30180011(ErrorCode.CODE_30180011, "账号不存在"),
	CODE_30180012(ErrorCode.CODE_30180012, "账号未认证"),
	CODE_30180013(ErrorCode.CODE_30180013, "该往来供应链已存在"),
	CODE_30180014(ErrorCode.CODE_30180014, "该往来企业已存在"),
	CODE_30180015(ErrorCode.CODE_30180015, "企业名称不能为空"),
	CODE_30180016(ErrorCode.CODE_30180016, "统一社会信用代码不能为空"),
	CODE_30180017(ErrorCode.CODE_30180017, "法定代表人不能为空"),
	CODE_30180018(ErrorCode.CODE_30180018, "无权限"),
	CODE_30180019(ErrorCode.CODE_30180019, "已驳回状态才能被删除"),
	CODE_30180020(ErrorCode.CODE_30180020, "待确认状态才能确认"),
	CODE_30180021(ErrorCode.CODE_30180021, "待确认状态才能驳回"),
	CODE_30180022(ErrorCode.CODE_30180022, "开户名称不能为空"),
	CODE_30180023(ErrorCode.CODE_30180023, "开户名称不能超过32个字符"),
	CODE_30180024(ErrorCode.CODE_30180024, "银行账户不能为空"),
	CODE_30180025(ErrorCode.CODE_30180025, "银行账户不能超过32个字符"),
	CODE_30180026(ErrorCode.CODE_30180026, "开户行不能为空"),
	CODE_30180027(ErrorCode.CODE_30180027, "开户行不能超过32个字符"),
	CODE_30180028(ErrorCode.CODE_30180028, "该企业已被项目关联"),
	CODE_30180029(ErrorCode.CODE_30180029, "请先完成组织机构认证"),
	CODE_30180030(ErrorCode.CODE_30180030, "驳回原因不能为空"),
	CODE_30180031(ErrorCode.CODE_30180031, "驳回原因不超过200个字符"),
	;

    /**
	 * 国际化前缀
	 */
	private static final String PREFIX = "com.zhihaoscm.service.meta.";
	/**
	 * 获取i18n
	 */
	public static final Function<String, String> I18N = code -> ErrorCodeDef.PREFIX
			+ code;
	/**
	 * 编号
	 */
	private final String code;
	/**
	 * 编号信息
	 */
	@Getter
	private final String message;

	/**
	 * 构造器
	 */
	ErrorCodeDef(String code, String message) {
		this.code = code;
		this.message = message;
	}

	/**
	 * 通过编号信息获取枚举对象
	 */
	public static ErrorCodeDef from(String code) {
		for (ErrorCodeDef item : ErrorCodeDef.values()) {
			if (item.getCode().equals(code)) {
				return item;
			}
		}
		return ErrorCodeDef.CODE_001;
	}
}
