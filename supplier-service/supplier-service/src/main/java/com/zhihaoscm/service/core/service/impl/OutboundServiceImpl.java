package com.zhihaoscm.service.core.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.api.util.multipart.file.CustomMultipartFile;
import com.zhihaoscm.common.api.util.multipart.file.MultipartFileUtils;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.GoodsInfo;
import com.zhihaoscm.domain.bean.json.PledgeInfo;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.OutboundCountVo;
import com.zhihaoscm.domain.bean.vo.OutboundVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.pdf.OutboundDetectionPdfUtils;
import com.zhihaoscm.domain.utils.pdf.OutboundPdfUtils;
import com.zhihaoscm.service.config.LocalDateTimeAdapter;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.OutboundMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 出库单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Slf4j
@Service
public class OutboundServiceImpl
		extends MpStringIdBaseServiceImpl<Outbound, OutboundMapper>
		implements OutboundService {

	public OutboundServiceImpl(OutboundMapper repository) {
		super(repository);
	}

	@Autowired
	private WarehouseGoodsInfoService warehouseGoodsInfoService;

	@Autowired
	private ContractService contractService;

	@Autowired
	private ProjectService projectService;

	@Autowired
	private GoodsService goodsService;

	@Autowired
	private WarehouseService warehouseService;

	@Autowired
	private StorageService storageService;

	@Autowired
	private UserService userService;

	@Autowired
	private ContractRecordService contractRecordService;

	@Autowired
	private FileService fileService;

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private InventoryDetailsService inventoryDetailsService;

	@Autowired
	private InboundDetectionService inboundDetectionService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Autowired
	private StorageInceptionInboundDetailService storageInceptionInboundDetailService;

	@Autowired
	private StorageInceptionOutboundDetailService storageInceptionOutboundDetailService;

	@Autowired
	private InboundService inboundService;

	@Autowired
	private PledgeService pledgeService;

	@Autowired
	private StockContractService stockContractService;

	@Autowired
	private StockProjectService stockProjectService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private StockContractFeesService stockContractFeesService;

	@Autowired
	private StorageFeeService storageFeeService;

	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Override
	public Page<OutboundVo> paging(Integer page, Integer size,
			String outboundId, String purchaserName, List<String> warehouseIds,
			List<Long> storageIds, List<Integer> outboundType, Integer signType,
			List<Integer> states, LocalDateTime startTime,
			LocalDateTime endTime, String sortKey, String sortOrder,
			Boolean hasAll, Long userId, String projectId) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);

		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (Objects.nonNull(userId)) {
			if (!hasAll) {
				// 处理人是自己在的
				List<String> projectIdList = projectService.findByUserId(userId,
						null);
				if (org.apache.commons.collections4.CollectionUtils
						.isNotEmpty(projectIdList)) {
					wrapper.in(Outbound::getProjectId, projectIdList);
				} else {
					return Page.of(page, size, 0);
				}
			}
		}
		wrapper.eq(Outbound::getType, InboundDef.Type.JXC.getCode());
		wrapper.like(StringUtils.isNotBlank(outboundId), Outbound::getId,
				outboundId);
		if (StringUtils.isNotBlank(purchaserName)) {
			List<Contract> contracts = contractService.find(purchaserName, null,
					ContractDef.Type.SELL.getCode(), null);
			if (CollectionUtils.isEmpty(contracts)) {
				return new Page<>();
			}
			List<String> contractIds = contracts.stream().map(Contract::getId)
					.toList();
			wrapper.in(CollectionUtils.isNotEmpty(contractIds),
					Outbound::getContractId, contractIds);
		}
		if (CollectionUtils.isNotEmpty(warehouseIds)
				&& CollectionUtils.isNotEmpty(storageIds)) {
			wrapper.and(e -> e.in(Outbound::getWarehouseId, warehouseIds).or()
					.in(Outbound::getStorageId, storageIds));
		} else {
			if (CollectionUtils.isNotEmpty(warehouseIds)) {
				wrapper.in(Outbound::getWarehouseId, warehouseIds);
			}
			if (CollectionUtils.isNotEmpty(storageIds)) {
				wrapper.in(Outbound::getStorageId, storageIds);
			}
		}
		wrapper.in(CollectionUtils.isNotEmpty(outboundType),
				Outbound::getOutboundType, outboundType);
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.and(x -> {
				for (Integer state : states) {
					OutboundDef.Status from = OutboundDef.Status.from(state);
					switch (from) {
						case DRAFT, TO_BE_INITIATED, REJECTED, OUTBOUND,
								INVALIDED, INVALIDING, TO_BE_OUTBOUND ->
							x.or(y -> y.eq(Outbound::getState, state));
						case CONFIRMING, SIGNING ->
							x.or(y -> y.eq(Outbound::getState, state).eq(
									Outbound::getSignState,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode()));
						case TO_BE_SIGN -> x.or(y -> y
								.eq(Outbound::getState,
										OutboundDef.Status.SIGNING.getCode())
								.in(Outbound::getSignState,
										BusinessContractDef.CommonSignState.BUYER_SIGNED
												.getCode(),
										BusinessContractDef.CommonSignState.UNSIGNED
												.getCode()));
					}
				}
			});

		}
		wrapper.ge(Objects.nonNull(startTime), Outbound::getOutboundTime,
				startTime);
		wrapper.le(Objects.nonNull(endTime), Outbound::getOutboundTime,
				endTime);
		wrapper.eq(Objects.nonNull(signType), Outbound::getSignType, signType);
		wrapper.eq(Objects.nonNull(projectId), Outbound::getProjectId,
				projectId);

		if (com.zhihaoscm.common.util.utils.StringUtils.isNoneBlank(sortKey,
				sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			wrapper.orderByDesc(Outbound::getCreatedTime);
		}

		Page<Outbound> inboundPage = repository
				.selectPage(new Page<>(page, size), wrapper);
		return PageUtil.getRecordsInfoPage(inboundPage,
				this.packVos(inboundPage.getRecords()));

	}

	@Override
	public Page<OutboundVo> pagingBuy(Integer page, Integer size,
			String outboundId, String purchaserName, List<String> warehouseIds,
			List<Long> storageIds, List<Integer> outboundType, Integer signType,
			List<Integer> states, LocalDateTime startTime,
			LocalDateTime endTime, String sortKey, String sortOrder,
			Boolean hasAll, Long userId, String projectId) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);

		wrapper.eq(Outbound::getType, InboundDef.Type.STORAGE.getCode());

		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (Objects.nonNull(userId)) {
			if (!hasAll) {
				// 处理人是自己在的
				List<String> projectIdList = stockProjectService
						.findByUserId(userId, null);
				if (org.apache.commons.collections4.CollectionUtils
						.isNotEmpty(projectIdList)) {
					wrapper.in(Outbound::getProjectId, projectIdList);
				} else {
					return Page.of(page, size, 0);
				}
			}
		}

		wrapper.like(StringUtils.isNotBlank(outboundId), Outbound::getId,
				outboundId);
		if (StringUtils.isNotBlank(purchaserName)) {
			// 仓储合同
			List<StockContract> contracts = stockContractService
					.findPurchase(purchaserName, null, null);
			if (CollectionUtils.isEmpty(contracts)) {
				return new Page<>();
			}
			List<String> contractIds = contracts.stream()
					.map(StockContract::getId).toList();
			wrapper.in(CollectionUtils.isNotEmpty(contractIds),
					Outbound::getContractId, contractIds);
		}
		if (CollectionUtils.isNotEmpty(warehouseIds)
				&& CollectionUtils.isNotEmpty(storageIds)) {
			wrapper.and(e -> e.in(Outbound::getWarehouseId, warehouseIds).or()
					.in(Outbound::getStorageId, storageIds));
		} else {
			if (CollectionUtils.isNotEmpty(warehouseIds)) {
				wrapper.in(Outbound::getWarehouseId, warehouseIds);
			}
			if (CollectionUtils.isNotEmpty(storageIds)) {
				wrapper.in(Outbound::getStorageId, storageIds);
			}
		}
		wrapper.in(CollectionUtils.isNotEmpty(outboundType),
				Outbound::getOutboundType, outboundType);
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.and(x -> {
				for (Integer state : states) {
					OutboundDef.Status from = OutboundDef.Status.from(state);
					switch (from) {
						case OUTBOUND, INVALIDED, INVALIDING ->
							x.or(y -> y.eq(Outbound::getState, state));
						case SIGNING ->
							x.or(y -> y.eq(Outbound::getState, state).eq(
									Outbound::getSignState,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode()));
						case TO_BE_SIGN -> x.or(y -> y
								.eq(Outbound::getState,
										OutboundDef.Status.SIGNING.getCode())
								.in(Outbound::getSignState, List.of(
										BusinessContractDef.CommonSignState.UNSIGNED
												.getCode(),
										BusinessContractDef.CommonSignState.BUYER_SIGNED
												.getCode())));
						case TO_BE_CONFIRM -> x.or(y -> y
								.eq(Outbound::getState,
										OutboundDef.Status.CONFIRMING.getCode())
								.eq(Outbound::getSignState,
										BusinessContractDef.CommonSignState.BUYER_SIGNED
												.getCode()));
					}
				}
			});
		} else {
			List<Integer> defaultStates = List.of(
					OutboundDef.Status.CONFIRMING.getCode(),
					OutboundDef.Status.SIGNING.getCode(),
					OutboundDef.Status.OUTBOUND.getCode(),
					OutboundDef.Status.INVALIDING.getCode(),
					OutboundDef.Status.INVALIDED.getCode());
			wrapper.in(Outbound::getState, defaultStates);
		}
		wrapper.ge(Objects.nonNull(startTime), Outbound::getOutboundTime,
				startTime);
		wrapper.le(Objects.nonNull(endTime), Outbound::getOutboundTime,
				endTime);
		wrapper.eq(Objects.nonNull(signType), Outbound::getSignType, signType);
		wrapper.eq(Objects.nonNull(projectId), Outbound::getProjectId,
				projectId);

		if (com.zhihaoscm.common.util.utils.StringUtils.isNoneBlank(sortKey,
				sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			wrapper.orderByDesc(Outbound::getCreatedTime);
		}

		Page<Outbound> inboundPage = repository
				.selectPage(new Page<>(page, size), wrapper);
		return PageUtil.getRecordsInfoPage(inboundPage,
				this.packVos(inboundPage.getRecords()));

	}

	@Override
	public Page<OutboundVo> pageSelector(Integer page, Integer size, String key,
			String contractId, List<Integer> status, List<String> ids,
			String id, String projectId) {
		// 合同关联的签收列表（已经去除掉已经关联对账单的签收单)
		LambdaQueryWrapper<Outbound> queryWrapper = Wrappers
				.lambdaQuery(Outbound.class);
		queryWrapper.like(Objects.nonNull(key), Outbound::getId, key);
		queryWrapper.eq(Outbound::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(contractId), Outbound::getContractId,
				contractId);
		queryWrapper.in(CollectionUtils.isNotEmpty(status), Outbound::getState,
				status);
		queryWrapper.in(CollectionUtils.isNotEmpty(ids), Outbound::getId, ids);
		queryWrapper.eq(Objects.nonNull(projectId), Outbound::getProjectId,
				projectId);

		List<String> idList = new ArrayList<>();
		List<Inbound> relateOutbound = inboundService.findRelateOutbound();
		if (CollectionUtils.isNotEmpty(relateOutbound)) {
			List<String> relateOutboundIds = relateOutbound.stream()
					.map(Inbound::getOutboundId).toList();
			idList = new ArrayList<>(relateOutboundIds);
			idList.removeIf(e -> e.equals(id));
		}
		queryWrapper.notIn(CollectionUtils.isNotEmpty(idList), Outbound::getId,
				idList);

		// 合同关联的签收列表去除掉被对账单已经关联的数据
		Page<Outbound> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		List<OutboundVo> vos = this.packVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<OutboundVo> customPaging(Integer page, Integer size, String key,
			String sellerName, String warehouseName, List<Integer> outboundType,
			Integer signType, List<Integer> states, LocalDateTime startTime,
			LocalDateTime endTime, Long customerId, String sortKey,
			String sortOrder, String projectId) {

		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		if (Objects.nonNull(key)) {
			// 仓储合同
			List<Contract> contractList = contractService.findByNameLike(key);
			if (CollectionUtils.isNotEmpty(contractList)) {
				wrapper.and(e -> e.like(Outbound::getId, key).or().in(
						Outbound::getContractId,
						contractList.stream().map(Contract::getId)
								.collect(Collectors.toList())));
			} else {
				wrapper.like(Outbound::getId, key);
			}
		}
		wrapper.eq(Outbound::getType, InboundDef.Type.JXC.getCode());
		if (StringUtils.isNotBlank(sellerName)) {
			List<Contract> contracts = contractService.customerFind(sellerName,
					null, null, ContractDef.Type.SELL.getCode(), customerId);
			if (CollectionUtils.isEmpty(contracts)) {
				return new Page<>();
			}
			List<String> contractIds = contracts.stream().map(Contract::getId)
					.toList();
			wrapper.in(CollectionUtils.isNotEmpty(contractIds),
					Outbound::getContractId, contractIds);
		}
		if (Objects.nonNull(warehouseName)) {
			List<String> warehouseIds = warehouseService
					.findByNameLike(warehouseName).stream()
					.map(Warehouse::getId).toList();
			List<Long> storageIds = storageService.findByNameLike(warehouseName)
					.stream().map(Storage::getId).toList();
			if (CollectionUtils.isNotEmpty(warehouseIds)
					&& CollectionUtils.isNotEmpty(storageIds)) {
				wrapper.and(e -> e.in(Outbound::getWarehouseId, warehouseIds)
						.or().in(Outbound::getStorageId, storageIds));
			} else if (CollectionUtils.isEmpty(warehouseIds)
					&& CollectionUtils.isEmpty(storageIds)) {
				// 如果没有仓库和库位则不查询
				return new Page<>();
			} else {
				if (CollectionUtils.isNotEmpty(warehouseIds)) {
					wrapper.in(Outbound::getWarehouseId, warehouseIds);
				}
				if (CollectionUtils.isNotEmpty(storageIds)) {
					wrapper.in(Outbound::getStorageId, storageIds);
				}
			}

		}
		wrapper.in(CollectionUtils.isNotEmpty(outboundType),
				Outbound::getOutboundType, outboundType);
		wrapper.eq(Outbound::getPurchaserId, customerId);
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.and(x -> {
				for (Integer state : states) {
					OutboundDef.Status from = OutboundDef.Status.from(state);
					switch (from) {
						case OUTBOUND, INVALIDED, INVALIDING ->
							x.or(y -> y.eq(Outbound::getState, state));
						case SIGNING ->
							x.or(y -> y.eq(Outbound::getState, state).eq(
									Outbound::getSignState,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode()));
						case TO_BE_SIGN -> x.or(y -> y
								.eq(Outbound::getState,
										OutboundDef.Status.SIGNING.getCode())
								.in(Outbound::getSignState, List.of(
										BusinessContractDef.CommonSignState.UNSIGNED
												.getCode(),
										BusinessContractDef.CommonSignState.SUPPLY_SIGNED
												.getCode())));
						case TO_BE_CONFIRM -> x.or(y -> y
								.eq(Outbound::getState,
										OutboundDef.Status.CONFIRMING.getCode())
								.eq(Outbound::getSignState,
										BusinessContractDef.CommonSignState.SUPPLY_SIGNED
												.getCode()));
					}
				}
			});
		} else {
			List<Integer> defaultStates = List.of(
					OutboundDef.Status.CONFIRMING.getCode(),
					OutboundDef.Status.SIGNING.getCode(),
					OutboundDef.Status.OUTBOUND.getCode(),
					OutboundDef.Status.INVALIDING.getCode(),
					OutboundDef.Status.INVALIDED.getCode());
			wrapper.in(Outbound::getState, defaultStates);
		}
		wrapper.ge(Objects.nonNull(startTime), Outbound::getOutboundTime,
				startTime);
		wrapper.le(Objects.nonNull(endTime), Outbound::getOutboundTime,
				endTime);
		wrapper.eq(Objects.nonNull(signType), Outbound::getSignType, signType);
		wrapper.eq(Objects.nonNull(projectId), Outbound::getProjectId,
				projectId);

		if (com.zhihaoscm.common.util.utils.StringUtils.isNoneBlank(sortKey,
				sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			wrapper.orderByDesc(Outbound::getCreatedTime);
		}

		Page<Outbound> inboundPage = repository
				.selectPage(new Page<>(page, size), wrapper);
		return PageUtil.getRecordsInfoPage(inboundPage,
				this.packVos(inboundPage.getRecords()));
	}

	@Override
	public Page<OutboundVo> customPagingBuy(Integer page, Integer size,
			String key, String sellerName, String warehouseName,
			List<Integer> outboundType, Integer signType, List<Integer> states,
			LocalDateTime startTime, LocalDateTime endTime, String sortKey,
			String sortOrder, Long customerId, String projectId) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);

		if (Objects.nonNull(key)) {
			// 仓储合同
			List<StockContract> contractList = stockContractService
					.findByNameLike(key);
			if (CollectionUtils.isNotEmpty(contractList)) {
				wrapper.and(e -> e.like(Outbound::getId, key).or().in(
						Outbound::getContractId,
						contractList.stream().map(StockContract::getId)
								.collect(Collectors.toList())));
			} else {
				wrapper.like(Outbound::getId, key);
			}
		}
		wrapper.eq(Outbound::getType, InboundDef.Type.STORAGE.getCode());
		if (StringUtils.isNotBlank(sellerName)) {
			// 仓储合同
			List<StockContract> contracts = stockContractService
					.find(sellerName, null, customerId);
			if (CollectionUtils.isEmpty(contracts)) {
				return new Page<>();
			}
			List<String> contractIds = contracts.stream()
					.map(StockContract::getId).toList();
			wrapper.in(CollectionUtils.isNotEmpty(contractIds),
					Outbound::getContractId, contractIds);
		}
		if (Objects.nonNull(warehouseName)) {
			List<String> warehouseIds = warehouseService
					.findByNameLike(warehouseName).stream()
					.map(Warehouse::getId).toList();
			List<Long> storageIds = storageService.findByNameLike(warehouseName)
					.stream().map(Storage::getId).toList();
			if (CollectionUtils.isNotEmpty(warehouseIds)
					&& CollectionUtils.isNotEmpty(storageIds)) {
				wrapper.and(e -> e.in(Outbound::getWarehouseId, warehouseIds)
						.or().in(Outbound::getStorageId, storageIds));
			} else if (CollectionUtils.isEmpty(warehouseIds)
					&& CollectionUtils.isEmpty(storageIds)) {
				// 如果没有仓库和库位则不查询
				return new Page<>();
			} else {
				if (CollectionUtils.isNotEmpty(warehouseIds)) {
					wrapper.in(Outbound::getWarehouseId, warehouseIds);
				}
				if (CollectionUtils.isNotEmpty(storageIds)) {
					wrapper.in(Outbound::getStorageId, storageIds);
				}
			}

		}
		wrapper.in(CollectionUtils.isNotEmpty(outboundType),
				Outbound::getOutboundType, outboundType);
		wrapper.eq(Outbound::getPurchaserId, customerId);
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.and(x -> {
				for (Integer state : states) {
					OutboundDef.Status from = OutboundDef.Status.from(state);
					switch (from) {
						case DRAFT, TO_BE_INITIATED, REJECTED, OUTBOUND,
								INVALIDED, INVALIDING ->
							x.or(y -> y.eq(Outbound::getState, state));
						case CONFIRMING, SIGNING ->
							x.or(y -> y.eq(Outbound::getState, state).eq(
									Outbound::getSignState,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode()));
						case TO_BE_SIGN -> x.or(y -> y
								.eq(Outbound::getState,
										OutboundDef.Status.SIGNING.getCode())
								.in(Outbound::getSignState, List.of(
										BusinessContractDef.CommonSignState.UNSIGNED
												.getCode(),
										BusinessContractDef.CommonSignState.SUPPLY_SIGNED
												.getCode())));
					}
				}
			});

		}
		wrapper.ge(Objects.nonNull(startTime), Outbound::getOutboundTime,
				startTime);
		wrapper.le(Objects.nonNull(endTime), Outbound::getOutboundTime,
				endTime);
		wrapper.eq(Objects.nonNull(signType), Outbound::getSignType, signType);
		wrapper.eq(Objects.nonNull(projectId), Outbound::getProjectId,
				projectId);

		if (com.zhihaoscm.common.util.utils.StringUtils.isNoneBlank(sortKey,
				sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			wrapper.orderByDesc(Outbound::getCreatedTime);
		}

		Page<Outbound> inboundPage = repository
				.selectPage(new Page<>(page, size), wrapper);
		return PageUtil.getRecordsInfoPage(inboundPage,
				this.packVos(inboundPage.getRecords()));

	}

	@Override
	public List<Outbound> findByPurchaserIdAndState(Long customId,
			Integer state, Integer module) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(customId), Outbound::getPurchaserId,
				customId).eq(Objects.nonNull(state), Outbound::getState, state)
				.eq(Objects.nonNull(module), Outbound::getType, module);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<OutboundVo> findVoById(String id) {
		return Optional.ofNullable(this.packVo(this.findOne(id).orElse(null)));
	}

	@Override
	public List<Outbound> findByProjectIds(List<String> projectIds,
			List<Integer> states, Integer type) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		if (CollectionUtils.isNotEmpty(projectIds)) {
			wrapper.in(Outbound::getProjectId, projectIds);
		}
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.in(Outbound::getState, states);
		}
		if (Objects.nonNull(type)) {
			wrapper.eq(Outbound::getType, type);
		}
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findByContractIds(List<String> contractIds,
			List<Integer> states) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		if (CollectionUtils.isNotEmpty(contractIds)) {
			wrapper.in(Outbound::getContractId, contractIds);
		}
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.in(Outbound::getState, states);
		}
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findByContractIds(List<String> contractIds,
			List<Integer> states, Integer type) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		if (CollectionUtils.isNotEmpty(contractIds)) {
			wrapper.in(Outbound::getContractId, contractIds);
		}
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.in(Outbound::getState, states);
		}
		if (Objects.nonNull(type)) {
			wrapper.eq(Outbound::getType, type);
		}
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findByProjectIdAndTime(String projectId,
			LocalDateTime startTime, LocalDateTime endTime) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(projectId), Outbound::getProjectId,
				projectId);
		wrapper.ge(Objects.nonNull(startTime), Outbound::getOutboundTime,
				startTime);
		wrapper.le(Objects.nonNull(endTime), Outbound::getOutboundTime,
				endTime);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findByReceiptIds(List<String> receiptIds) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		wrapper.in(CollectionUtils.isNotEmpty(receiptIds),
				Outbound::getReceiptId, receiptIds);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findByProjectIdAndWarehouseId(String projectId,
			String warehouseId, Long storageId, List<Integer> states,
			Integer type) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		if (Objects.nonNull(projectId)) {
			wrapper.eq(Outbound::getProjectId, projectId);
		}
		if (Objects.nonNull(warehouseId)) {
			wrapper.eq(Outbound::getWarehouseId, warehouseId);
		}
		if (Objects.nonNull(storageId)) {
			wrapper.eq(Outbound::getStorageId, storageId);
		}
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.in(Outbound::getState, states);
		}
		if (Objects.nonNull(type)) {
			wrapper.eq(Outbound::getType, type);
		}
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findByContractIdAndWarehouseId(String contractId,
			String warehouseId, Long storageId, List<Integer> states,
			Integer type) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		if (Objects.nonNull(contractId)) {
			wrapper.eq(Outbound::getContractId, contractId);
		}
		if (Objects.nonNull(warehouseId)) {
			wrapper.eq(Outbound::getWarehouseId, warehouseId);
		}
		if (Objects.nonNull(storageId)) {
			wrapper.eq(Outbound::getStorageId, storageId);
		}
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.in(Outbound::getState, states);
		}
		if (Objects.nonNull(type)) {
			wrapper.eq(Outbound::getType, type);
		}
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findByProjectIdAndGoodsId(String projectId,
			Long goodsId, List<Integer> states, Integer type) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		if (Objects.nonNull(projectId)) {
			wrapper.eq(Outbound::getProjectId, projectId);
		}
		if (Objects.nonNull(goodsId)) {
			wrapper.eq(Outbound::getGoodsNameId, goodsId);
		}
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.in(Outbound::getState, states);
		}
		if (Objects.nonNull(type)) {
			wrapper.eq(Outbound::getType, type);
		}
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findByWarehouseId(List<String> id) {
		if (CollectionUtils.isEmpty(id)) {
			return List.of();
		}
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		wrapper.in(Outbound::getWarehouseId, id);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findByStorageId(List<Long> storageId) {
		if (CollectionUtils.isEmpty(storageId)) {
			return List.of();
		}
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		wrapper.in(Outbound::getStorageId, storageId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> find(String projectId, String warehouseId,
			Long storageId, List<Integer> states, Integer type) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(projectId), Outbound::getProjectId,
				projectId);
		wrapper.eq(Objects.nonNull(type), Outbound::getType, type);
		wrapper.eq(StringUtils.isNotBlank(warehouseId),
				Outbound::getWarehouseId, warehouseId);
		wrapper.eq(Objects.nonNull(storageId), Outbound::getStorageId,
				storageId);
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.in(Outbound::getState, states);
		}
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findRelateReceipt() {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		wrapper.notIn(Outbound::getState,
				List.of(OutboundDef.Status.INVALIDED.getCode()));
		wrapper.isNotNull(Outbound::getReceiptId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Outbound> findByReceiptId(String receiptId) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Outbound::getReceiptId, receiptId);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<BigDecimal> totalWeight(String projectId,
			List<Integer> states, Integer type) {
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(projectId), Outbound::getProjectId,
				projectId);
		if (Objects.nonNull(type)) {
			wrapper.eq(Outbound::getType, type);
		}
		if (CollectionUtils.isNotEmpty(states)) {
			wrapper.in(Outbound::getState, states);
		}
		BigDecimal reduce = repository.selectList(wrapper).stream()
				.map(Outbound::getOutboundWeight).filter(Objects::nonNull)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		return Optional.of(reduce);
	}

	/**
	 * 查询可销售出库的重量
	 *
	 * @return
	 */
	@Override
	public Optional<BigDecimal> findCanSaleOutboundWeight(String projectId,
			String warehouseId, Long storageId, String contractId) {

		Project project = projectService.findOne(projectId)
				.orElse(new Project());

		// 入库完成重量
		BigDecimal inboundWeight = inboundService
				.find(projectId, warehouseId, storageId,
						List.of(InboundDef.Status.INBOUNDED.getCode()),
						InboundDef.Type.JXC.getCode())
				.stream().map(Inbound::getInboundWeight)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 出库完成重量
		BigDecimal outboundWeight = this
				.find(projectId, warehouseId, storageId,
						List.of(OutboundDef.Status.OUTBOUND.getCode()),
						InboundDef.Type.JXC.getCode())
				.stream().map(Outbound::getOutboundWeight)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 待出库的重量
		BigDecimal toOutboundWeight = this
				.find(projectId, warehouseId, storageId,
						List.of(OutboundDef.Status.CONFIRMING.getCode(),
								OutboundDef.Status.TO_BE_INITIATED.getCode(),
								OutboundDef.Status.DRAFT.getCode(),
								OutboundDef.Status.REJECTED.getCode(),
								OutboundDef.Status.INVALIDING.getCode(),
								OutboundDef.Status.SIGNING.getCode()),
						InboundDef.InfoType.OUTBOUND.getCode())
				.stream().map(Outbound::getOutboundWeight)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 仓储期初入库重量
		BigDecimal inceptionInboundWeight = storageInceptionInboundDetailService
				.find(projectId, warehouseId, storageId).stream()
				.map(StorageInceptionInboundDetail::getQuantity)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 仓储期初出库重量
		BigDecimal inceptionOutboundWeight = storageInceptionOutboundDetailService
				.find(projectId, warehouseId, storageId).stream()
				.map(StorageInceptionOutboundDetail::getQuantity)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 库存数量
		BigDecimal inventoryWeight = inboundWeight.subtract(outboundWeight)
				.add(inceptionInboundWeight).subtract(inceptionOutboundWeight);

		// 质押重量
		BigDecimal pledgeWeight = BigDecimal.ZERO;
		Optional<Pledge> pledge = pledgeService
				.findByProjectIdAndContractId(projectId, contractId);
		if (pledge.isPresent()) {
			String pledgeInfo = pledge.get().getPledgeInfo();
			List<PledgeInfo> pledgeInfos = this.convertPledgeInfo(pledgeInfo);
			PledgeInfo pledgeInfo1 = pledgeInfos.get(pledgeInfos.size() - 1);
			pledgeWeight = pledgeInfo1.getPledgeNum();
		}
		pledgeWeight = Objects.nonNull(project.getInventoryControlRatio())
				? pledgeWeight.multiply(project.getInventoryControlRatio())
						.divide(BigDecimal.valueOf(100))
				: pledgeWeight;

		// 待出库的数量
		return Optional.of(inventoryWeight.subtract(pledgeWeight)
				.subtract(toOutboundWeight));
	}

	/**
	 * 查询当前库存
	 * 
	 * @param warehouseId
	 * @param storageId
	 * @return
	 */
	@Override
	public Optional<BigDecimal> findCurrentInventory(String warehouseId,
			Long storageId, String projectId) {
		// 入库完成重量
		BigDecimal inboundWeight = inboundService
				.find(projectId, warehouseId, storageId,
						List.of(InboundDef.Status.INBOUNDED.getCode()),
						InboundDef.Type.JXC.getCode())
				.stream().map(Inbound::getInboundWeight)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 出库完成重量
		BigDecimal outboundWeight = this
				.find(projectId, warehouseId, storageId,
						List.of(OutboundDef.Status.OUTBOUND.getCode()),
						InboundDef.Type.JXC.getCode())
				.stream().map(Outbound::getOutboundWeight)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		// 待出库的重量
		BigDecimal toOutboundWeight = this
				.find(projectId, warehouseId, storageId,
						List.of(OutboundDef.Status.CONFIRMING.getCode(),
								OutboundDef.Status.TO_BE_INITIATED.getCode(),
								OutboundDef.Status.DRAFT.getCode(),
								OutboundDef.Status.REJECTED.getCode(),
								OutboundDef.Status.INVALIDING.getCode(),
								OutboundDef.Status.SIGNING.getCode()),
						InboundDef.Type.JXC.getCode())
				.stream().map(Outbound::getOutboundWeight)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 仓储期初入库重量
		BigDecimal inceptionInboundWeight = storageInceptionInboundDetailService
				.find(projectId, warehouseId, storageId).stream()
				.map(StorageInceptionInboundDetail::getQuantity)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 仓储期初出库重量
		BigDecimal inceptionOutboundWeight = storageInceptionOutboundDetailService
				.find(projectId, warehouseId, storageId).stream()
				.map(StorageInceptionOutboundDetail::getQuantity)
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		return Optional.of(inboundWeight.add(inceptionInboundWeight)
				.subtract(outboundWeight).subtract(inceptionOutboundWeight)
				.subtract(toOutboundWeight));

	}

	@Override
	public List<Outbound> findUnfinished(String projectId, Integer type) {
		LambdaQueryWrapper<Outbound> queryWrapper = Wrappers
				.lambdaQuery(Outbound.class);
		queryWrapper.eq(Outbound::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Outbound::getProjectId, projectId);
		queryWrapper.eq(Objects.nonNull(type), Outbound::getType, type);
		queryWrapper.ne(Outbound::getState,
				OutboundDef.Status.OUTBOUND.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<Outbound> create(Outbound outbound, Integer saveType) {

		// 根据saveType判断状态存储
		OutboundDef.SaveType from = OutboundDef.SaveType.from(saveType);
		switch (from) {
			case SAVE_TO_DRAFT ->
				// 保存到草稿
				outbound.setState(OutboundDef.Status.DRAFT.getCode());
			case SAVE_AND_COMMIT ->
			// 提交
			{
				// 线上提交状态流转为待发起
				if (OutboundDef.SignMode.ONLINE.match(outbound.getSignType())) {
					if (InboundDef.Type.JXC.match(outbound.getType())) {
						outbound.setState(
								OutboundDef.Status.TO_BE_INITIATED.getCode());
					} else {
						outbound.setState(OutboundDef.Status.SIGNING.getCode());
						outbound.setSignState(
								BusinessContractDef.CommonSignState.UNSIGNED
										.getCode());
					}
				} else {
					// 线下提交状态流转为确认中
					if (Boolean.TRUE.equals(contractService
							.validateIsRecorded(outbound.getContractId(),
									ContractDef.ContractType.SALES.getCode())
							.orElse(null))) {
						outbound.setState(
								OutboundDef.Status.TO_BE_OUTBOUND.getCode());
					} else {
						outbound.setState(
								OutboundDef.Status.CONFIRMING.getCode());
						outbound.setReviewTime(LocalDateTime.now());
					}

					if (InboundDef.Type.JXC.match(outbound.getType())) {
						outbound.setSignState(
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode());
					} else {
						outbound.setSignState(
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode());
					}
				}
			}
		}

		if (InboundDef.Type.JXC.match(outbound.getType())) {
			outbound.setReviewer(Objects
					.requireNonNull(UserContextHolder.getUser()).getId());
			Contract contract = contractService
					.findOne(outbound.getContractId()).orElse(new Contract());

			Project project = projectService.findOne(contract.getProjectId())
					.orElse(new Project());

			outbound.setPurchaserBusinessId(contract.getDownstreamId());
			outbound.setPurchaserId(contract.getDownstreamPurchasersId());
			outbound.setPurchaserEnterprise(
					contract.getDownstreamPurchasersEnterprise());
			outbound.setSellerId(contract.getSupplierChainId());
			outbound.setSellerEnterprise(contract.getSupplierChainEnterprise());
			outbound.setProjectId(contract.getProjectId());

			// 设置出库单id
			outbound.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
					project.getCode(), RedisKeys.Cache.OUTBOUND,
					AutoCodeDef.BusinessRuleCode.OUTBOUND.getCode(), 4,
					AutoCodeDef.DATE_TYPE.yy));

			outbound.setGoodsNameId(project.getGoodsId());
		} else {
			StockContract stockContract = stockContractService
					.findOne(outbound.getContractId())
					.orElse(new StockContract());
			StockProject project = stockProjectService
					.findOne(stockContract.getProjectId())
					.orElse(new StockProject());
			// 设置出库单id
			outbound.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
					project.getCode(), RedisKeys.Cache.OUTBOUND,
					AutoCodeDef.BusinessRuleCode.OUTBOUND.getCode(), 4,
					AutoCodeDef.DATE_TYPE.yy));
			outbound.setGoodsNameId(project.getGoodsId());
			outbound.setSellerId(stockContract.getStockId());
			outbound.setSellerEnterprise(stockContract.getStockEnterprise());
			outbound.setPurchaserId(stockContract.getCustomerId());
			outbound.setPurchaserEnterprise(
					stockContract.getCustomerEnterprise());
			outbound.setProjectId(stockContract.getProjectId());
		}

		Outbound result = super.create(outbound);

		if (InboundDef.Type.STORAGE.match(result.getType())) {
			if (InboundDef.SignMode.OFFLINE.match(result.getSignType())) {
				SpringUtil.getBean(OutboundService.class).notice(result, 6);
			}
		}

		warehouseService.findOne(result.getWarehouseId())
				.ifPresent(warehouse -> {
					warehouse.setIsUsed(CommonDef.Symbol.YES.getCode());
					warehouseService.updateAllProperties(warehouse);
				});

		if (Objects.nonNull(result.getStorageId())) {
			storageService.findOne(result.getStorageId()).ifPresent(storage -> {
				storage.setIsUsed(CommonDef.Symbol.YES.getCode());
				storageService.updateAllProperties(storage);
			});
		}

		if (InboundDef.Type.JXC.match(outbound.getType())) {
			if (OutboundDef.SaveType.SAVE_AND_COMMIT.match(saveType)
					&& OutboundDef.SignMode.OFFLINE
							.match(result.getSignType())) {
				this.sendNotice(result,
						wxSubscriptionProperties.getUnConfirmOutboundCode(),
						MessageFormat.format(
								UserMessageConstants.OUTBOUND_UNCONFIRMED_TEMPLATE,
								result.getId()));
			}
		}

		if (OutboundDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
			// 入库检测信息
			this.setDetectionInfo(result);
		}

		return Optional.of(result);
	}

	@Override
	public Optional<Outbound> update(Outbound outbound, Integer saveType) {

		// 根据saveType判断状态存储
		OutboundDef.SaveType from = OutboundDef.SaveType.from(saveType);
		switch (from) {
			case SAVE_TO_DRAFT ->
				// 保存到草稿
				outbound.setState(OutboundDef.Status.DRAFT.getCode());
			case SAVE_AND_COMMIT ->
			// 提交
			{
				if (InboundDef.Type.JXC.match(outbound.getType())) {
					outbound.setReviewer(
							Objects.requireNonNull(UserContextHolder.getUser())
									.getId());
				}
				// 线上提交状态流转为待发起
				if (OutboundDef.SignMode.ONLINE.match(outbound.getSignType())) {
					if (InboundDef.Type.JXC.match(outbound.getType())) {
						outbound.setState(
								OutboundDef.Status.TO_BE_INITIATED.getCode());
					} else {
						outbound.setState(OutboundDef.Status.SIGNING.getCode());
						outbound.setSignState(
								BusinessContractDef.CommonSignState.UNSIGNED
										.getCode());
					}
				} else {
					// 线下提交状态流转为确认中
					if (Boolean.TRUE.equals(contractService
							.validateIsRecorded(outbound.getContractId(),
									ContractDef.ContractType.SALES.getCode())
							.orElse(null))) {
						outbound.setState(
								OutboundDef.Status.TO_BE_OUTBOUND.getCode());
					} else {
						outbound.setState(
								OutboundDef.Status.CONFIRMING.getCode());
					}

					if (InboundDef.Type.JXC.match(outbound.getType())) {
						outbound.setSignState(
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode());
					} else {
						outbound.setSignState(
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode());
					}
				}
			}
		}

		Outbound result = super.updateAllProperties(outbound);

		if (OutboundDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
			// 入库检测信息
			this.setDetectionInfo(result);
		}

		return Optional.of(result);
	}

	@Override
	public void delete(Outbound outbound) {
		// 删除出库单货物信息
		warehouseGoodsInfoService.deleteByRelateId(outbound.getId());
		// 删除出库单
		super.delete(outbound.getId());
	}

	@Override
	public Optional<Outbound> submit(Outbound outbound) {
		// 线上提交状态流转为待发起
		if (OutboundDef.SignMode.ONLINE.match(outbound.getSignType())) {
			if (InboundDef.Type.JXC.match(outbound.getType())) {
				outbound.setState(OutboundDef.Status.TO_BE_INITIATED.getCode());
			} else {
				outbound.setState(OutboundDef.Status.SIGNING.getCode());
				outbound.setSignState(
						BusinessContractDef.CommonSignState.UNSIGNED.getCode());
			}
		} else {
			// 线下提交状态流转为确认中
			if (Boolean.TRUE.equals(contractService
					.validateIsRecorded(outbound.getContractId(),
							ContractDef.ContractType.SALES.getCode())
					.orElse(null))) {
				outbound.setState(OutboundDef.Status.TO_BE_OUTBOUND.getCode());
			} else {
				outbound.setState(OutboundDef.Status.CONFIRMING.getCode());
			}

			if (InboundDef.Type.JXC.match(outbound.getType())) {
				outbound.setSignState(
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
			} else {
				outbound.setSignState(
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
			}
		}
		if (InboundDef.Type.JXC.match(outbound.getType())) {
			outbound.setReviewer(Objects
					.requireNonNull(UserContextHolder.getUser()).getId());
		}
		return Optional.of(super.update(outbound));
	}

	@Override
	public Optional<Outbound> revoke(Outbound outbound) {
		// 待发起状态流转为草稿
		outbound.setState(OutboundDef.Status.DRAFT.getCode());
		return Optional.of(super.update(outbound));
	}

	/*
	 * @description: 客户端线下确认
	 *
	 * @author: pp
	 *
	 * @date: 2025/5/19 16:16
	 *
	 * @param: [outbound]
	 *
	 * @return: java.util.Optional<com.zhihaoscm.domain.bean.entity.Outbound>
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Outbound> confirm(Outbound outbound) {
		// 确认出库单
		outbound.setState(OutboundDef.Status.OUTBOUND.getCode());
		outbound.setSignState(
				BusinessContractDef.CommonSignState.COMPLETED.getCode());
		outbound.setReviewTime(LocalDateTime.now());
		if (InboundDef.Type.STORAGE.match(outbound.getType())) {
			outbound.setReviewer(Objects
					.requireNonNull(UserContextHolder.getUser()).getId());
		}
		// 货物信息存储到入库货物信息表中
		this.setGoodsInfo(outbound, outbound.getGoodsInfo());
		// 创建库存明细
		this.handleCreateInventoryDetails(outbound);
		if (InboundDef.Type.JXC.match(outbound.getType())) {
			SpringUtil.getBean(OutboundServiceImpl.class).notice(outbound, 1);
		} else {
			SpringUtil.getBean(OutboundServiceImpl.class).notice(outbound, 6);
		}
		this.setStorageFee(outbound);
		return Optional.of(super.update(outbound));
	}

	@Override
	public Optional<Outbound> reject(Outbound outbound) {
		// 驳回出库单
		outbound.setState(OutboundDef.Status.REJECTED.getCode());
		if (Objects.nonNull(outbound.getSignFileId())
				&& OutboundDef.SignMode.ONLINE.match(outbound.getSignType())) {
			fileService.batchUnActive(List.of(outbound.getSignFileId()));
			outbound.setSignFileId(null);
			// 撤销合同
			contractRecordService.revoke(outbound.getId(),
					PurchaseContractDef.CorrelationTable.OUTBOUND);
		}
		outbound.setRejectTime(LocalDateTime.now());
		Outbound result = super.update(outbound);
		if (InboundDef.Type.STORAGE.match(result.getType())) {
			this.sendNotice(result,
					wxSubscriptionProperties.getOutboundDismissCode(),
					MessageFormat.format(
							UserMessageConstants.OUTBOUND_DISMISS_TEMPLATE,
							result.getId()));
		} else {
			SpringUtil.getBean(OutboundServiceImpl.class).notice(outbound, 2);
		}
		return Optional.of(result);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ContractPageResponse> initiateSign(Outbound resource,
			Integer origin) {

		Long fileId = this.getFileId(resource);
		if (Objects.nonNull(fileId)) {
			resource.setSignFileId(fileId);
			resource.setState(OutboundDef.Status.SIGNING.getCode());
			resource.setSignState(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode());
			this.updateAllProperties(resource);
		} else {
			log.info("订单生成pdf文件失败");
			return Optional.empty();
		}

		// 发起合同
		Map<Long, String> customerMap = contractRecordService.draft("出库单",
				List.of(resource.getPurchaserId(), resource.getSellerId()),
				List.of(resource.getSignFileId()), resource.getId(),
				PurchaseContractDef.CorrelationTable.OUTBOUND, null,
				resource.getType());
		// 设置文件id
		resource.setSignFileId(contractRecordService.download(resource.getId(),
				PurchaseContractDef.CorrelationTable.OUTBOUND));

		resource.getPurchaserEnterprise()
				.setSignMobile(customerMap.get(resource.getPurchaserId()));
		resource.getSellerEnterprise()
				.setSignMobile(customerMap.get(resource.getSellerId()));

		resource.setState(OutboundDef.Status.SIGNING.getCode());
		resource.setSignState(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode());
		this.updateAllProperties(resource);

		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				resource.getPurchaserEnterprise().getSignMobile(),
				resource.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}

		// 获取签署链接
		return contractRecordService.sign(resource.getId(),
				PurchaseContractDef.CorrelationTable.OUTBOUND, origin);

	}

	@Override
	public Optional<ContractPageResponse> signing(Outbound outbound,
			Integer origin) {
		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				outbound.getPurchaserEnterprise().getSignMobile(),
				outbound.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}
		super.update(outbound);
		return contractRecordService.sign(outbound.getId(),
				PurchaseContractDef.CorrelationTable.OUTBOUND, origin);
	}

	@Override
	public Optional<Boolean> checkInvalid(String id) {
		List<Inbound> inboundList = inboundService.findByOutboundId(id);
		if (CollectionUtils.isNotEmpty(inboundList)) {
			for (Inbound inbound : inboundList) {
				if (!InboundDef.Status.INBOUND_INVALID
						.match(inbound.getState())) {
					return Optional.of(Boolean.FALSE);
				}
			}
		}
		return Optional.of(Boolean.TRUE);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ContractPageResponse> invalid(Outbound outbound) {
		// 调用契约锁撤销合同接口
		contractRecordService.revoke(outbound.getId(),
				PurchaseContractDef.CorrelationTable.OUTBOUND);
		// 作废后获取作废合同id
		Long fileId = contractRecordService.detail(outbound.getId(),
				PurchaseContractDef.CorrelationTable.OUTBOUND);
		outbound.setInvalidRevokeReason(null);
		outbound.setInvalidRevokeTime(null);
		outbound.setPurchaseInvalidTime(null);
		outbound.setSellerInvalidTime(null);
		outbound.setInvalidFileId(fileId);
		outbound.setState(OutboundDef.Status.INVALIDING.getCode());
		outbound.setInvalidSignState(
				PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
		super.updateAllProperties(outbound);
		// 修改对应库存明细的状态
		this.handleUpdateInventoryDetails(outbound,
				CommonDef.Symbol.NO.getCode());

		// 删除出库单货物信息
		warehouseGoodsInfoService.deleteByRelateId(outbound.getId());

		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				outbound.getPurchaserEnterprise().getSignMobile(),
				outbound.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}

		if (InboundDef.Type.JXC.match(outbound.getType())) {
			if (CommonDef.UserType.OUTER
					.match(outbound.getInvalidInitiator())) {
				SpringUtil.getBean(OutboundServiceImpl.class).notice(outbound,
						4);
			} else {
				this.sendNotice(outbound,
						wxSubscriptionProperties
								.getOutboundNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.OUTBOUND_INVALID_UNCONFIRMED_TEMPLATE,
								outbound.getId()));
			}
		} else {
			if (CommonDef.UserType.OUTER
					.match(outbound.getInvalidInitiator())) {
				SpringUtil.getBean(OutboundServiceImpl.class).notice(outbound,
						7);
			} else {
				this.sendNotice(outbound,
						wxSubscriptionProperties
								.getOutboundNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.OUTBOUND_INVALID_UNCONFIRMED_TEMPLATE,
								outbound.getId()));
			}
		}

		return contractRecordService.sign(outbound.getId(),
				PurchaseContractDef.CorrelationTable.OUTBOUND,
				CertificationDef.Origin.PC.getCode());

	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Outbound> invalidOffLine(Outbound outbound,
			Integer initiator) {
		// 作废合同
		outbound.setState(OutboundDef.Status.INVALIDING.getCode());
		outbound.setInvalidInitiator(initiator);
		outbound.setInvalidRevokeReason(null);
		outbound.setInvalidRevokeTime(null);
		outbound.setPurchaseInvalidTime(null);
		outbound.setSellerInvalidTime(null);
		// 修改对应库存明细的状态
		this.handleUpdateInventoryDetails(outbound,
				CommonDef.Symbol.NO.getCode());

		// 删除出库单货物信息
		warehouseGoodsInfoService.deleteByRelateId(outbound.getId());

		if (InboundDef.Type.JXC.match(outbound.getType())) {
			if (CommonDef.UserType.INNER.match(initiator)) {
				outbound.setInvalidSignState(
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				outbound.setSellerInvalidTime(LocalDateTime.now());
				this.sendNotice(outbound,
						wxSubscriptionProperties
								.getOutboundNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.OUTBOUND_INVALID_UNCONFIRMED_TEMPLATE,
								outbound.getId()));
			} else {
				outbound.setInvalidSignState(
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				outbound.setPurchaseInvalidTime(LocalDateTime.now());
				SpringUtil.getBean(OutboundServiceImpl.class).notice(outbound,
						4);
			}
		} else {
			if (CommonDef.UserType.INNER.match(initiator)) {
				outbound.setInvalidSignState(
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				outbound.setSellerInvalidTime(LocalDateTime.now());
				this.sendNotice(outbound,
						wxSubscriptionProperties
								.getOutboundNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.OUTBOUND_INVALID_UNCONFIRMED_TEMPLATE,
								outbound.getId()));
			} else {
				outbound.setInvalidSignState(
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				outbound.setPurchaseInvalidTime(LocalDateTime.now());
				SpringUtil.getBean(OutboundServiceImpl.class).notice(outbound,
						7);
			}
		}

		return Optional.of(super.updateAllProperties(outbound));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Outbound> confirmInvalid(Outbound outbound) {
		// 确认作废合同
		outbound.setState(OutboundDef.Status.INVALIDED.getCode());
		outbound.setInvalidSignState(
				BusinessContractDef.CommonSignState.COMPLETED.getCode());
		if (CommonDef.UserType.INNER.match(outbound.getInvalidInitiator())) {
			outbound.setPurchaseInvalidTime(LocalDateTime.now());
		} else {
			outbound.setSellerInvalidTime(LocalDateTime.now());
		}
		storageFeeService.deleteByRelateId(List.of(outbound.getId()));
		return Optional.of(super.updateAllProperties(outbound));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Outbound> revertInvalid(Outbound outbound) {
		// 撤销作废合同
		outbound.setState(OutboundDef.Status.OUTBOUND.getCode());
		outbound.setInvalidSignState(null);
		outbound.setInvalidRevokeTime(LocalDateTime.now());
		Outbound outbound1 = super.updateAllProperties(outbound);
		this.handleUpdateInventoryDetails(outbound,
				CommonDef.Symbol.YES.getCode());

		this.setGoodsInfo(outbound, outbound.getGoodsInfo());

		if (InboundDef.Type.JXC.match(outbound.getType())) {
			if (CommonDef.UserType.INNER
					.match(outbound.getInvalidInitiator())) {
				SpringUtil.getBean(OutboundServiceImpl.class).notice(outbound,
						5);
			} else {
				this.sendNotice(outbound,
						wxSubscriptionProperties
								.getOutboundNullifyDismissCode(),
						MessageFormat.format(
								UserMessageConstants.OUTBOUND_INVALID_DISMISS_TEMPLATE,
								outbound.getId()));
			}
		} else {
			if (CommonDef.UserType.INNER
					.match(outbound.getInvalidInitiator())) {
				SpringUtil.getBean(OutboundServiceImpl.class).notice(outbound,
						8);
			} else {
				this.sendNotice(outbound,
						wxSubscriptionProperties
								.getOutboundNullifyDismissCode(),
						MessageFormat.format(
								UserMessageConstants.OUTBOUND_INVALID_DISMISS_TEMPLATE,
								outbound.getId()));
			}
		}

		// 签收单完成后判断订单中的签收单状态是否可以改为已完成状态
		return Optional.of(outbound1);
	}

	/*
	 * @description: 设置货物信息
	 *
	 * @author: pp
	 *
	 * @date: 2025/5/19 16:04
	 *
	 * @param: [result, goodsInfo, isUpdate]
	 *
	 * @return: void
	 **/
	@Override
	public void setGoodsInfo(Outbound result, String goodsInfo) {
		// 货物信息存储到入库货物信息表中
		if (goodsInfo != null && !goodsInfo.isEmpty()) {
			List<GoodsInfo> goodsInfos = this.convertJsonToList(goodsInfo,
					GoodsInfo.class);
			Warehouse warehouse = warehouseService
					.findOne(result.getWarehouseId()).orElse(new Warehouse());
			Storage storage = storageService.findOne(result.getStorageId())
					.orElse(new Storage());
			List<WarehouseGoodsInfo> warehouseGoodsInfos = new ArrayList<>();
			goodsInfos.forEach(goods -> {
				WarehouseGoodsInfo warehouseGoodsInfo = new WarehouseGoodsInfo();
				warehouseGoodsInfo.setAttributionType(result.getType());
				warehouseGoodsInfo.setModel(goods.getModel());
				warehouseGoodsInfo.setUnitPrice(goods.getUnitPrice());
				warehouseGoodsInfo.setInboundWeight(goods.getInboundWeight());
				warehouseGoodsInfo.setInboundAmount(goods.getInboundAmount());
				warehouseGoodsInfo.setRelateId(result.getId());
				warehouseGoodsInfo.setProjectId(result.getProjectId());
				warehouseGoodsInfo.setContractId(result.getContractId());
				warehouseGoodsInfo.setGoodsId(result.getGoodsNameId());
				warehouseGoodsInfo.setWarehouseId(result.getWarehouseId());
				warehouseGoodsInfo.setWarehouseName(warehouse.getName());
				warehouseGoodsInfo.setStorageId(result.getStorageId());
				warehouseGoodsInfo.setStorageName(storage.getName());
				warehouseGoodsInfo.setInboundTime(result.getOutboundTime());
				warehouseGoodsInfo.setInboundWeight(goods.getOutboundWeight());
				warehouseGoodsInfo.setInboundAmount(goods.getOutboundAmount());
				warehouseGoodsInfo.setRemark(goods.getRemark());
				warehouseGoodsInfo.setUnit(goods.getUnit());
				if (InboundDef.Type.JXC.match(result.getType())) {
					warehouseGoodsInfo
							.setType(InboundDef.InfoType.OUTBOUND.getCode());
				} else {
					warehouseGoodsInfo
							.setType(InboundDef.InfoType.STORAGE_MANAGE_OUTBOUND
									.getCode());
				}
				warehouseGoodsInfos.add(warehouseGoodsInfo);
			});

			warehouseGoodsInfoService.batchCreate(warehouseGoodsInfos);
		}

	}

	private void setDetectionInfo(Outbound result) {
		if (Objects.isNull(result.getDetectionInfo())) {
			return;
		}
		Project project = projectService.findOne(result.getProjectId())
				.orElse(new Project());
		Contract contract = contractService.findOne(result.getContractId())
				.orElse(new Contract());
		InboundDetection inboundDetection = new InboundDetection();
		inboundDetection.setInboundId(result.getId());
		inboundDetection.setProjectId(result.getProjectId());
		inboundDetection.setContractId(result.getContractId());
		inboundDetection.setGoodsId(project.getGoodsId());
		inboundDetection.setSellerId(contract.getUpstreamSuppliersId());
		inboundDetection
				.setSellerEnterprise(contract.getUpstreamSuppliersEnterprise());
		inboundDetection.setPurchaserId(contract.getSupplierChainId());
		inboundDetection
				.setPurchaserEnterprise(contract.getSupplierChainEnterprise());
		inboundDetection.setWarehouseId(result.getWarehouseId());
		inboundDetection.setStorageId(result.getStorageId());
		inboundDetection.setInboundWeight(result.getOutboundWeight());
		inboundDetection.setType(InboundDef.InfoType.OUTBOUND.getCode());
		inboundDetection.setInboundTime(result.getOutboundTime());
		inboundDetectionService.create(inboundDetection);
	}

	/**
	 * 出库完成之后 生成库存明细
	 *
	 * @param outbound
	 */
	@Override
	public void handleCreateInventoryDetails(Outbound outbound) {
		List<InventoryDetails> inventoryDetailsList = new ArrayList<>();
		String warehouseName = null;
		Integer warehouseType = null;
		String storageName = null;
		String goodsName = null;
		String creatorName = null;
		String goodsInfo = outbound.getGoodsInfo();
		if (goodsInfo != null && !goodsInfo.isEmpty()) {
			List<GoodsInfo> goodsInfos = this.convertJsonToList(goodsInfo,
					GoodsInfo.class);
			Goods goods = goodsService.findOne(outbound.getGoodsNameId())
					.orElse(null);
			if (Objects.nonNull(goods)) {
				goodsName = goods.getGoodsName();
			}
			Warehouse warehouse = warehouseService
					.findOne(outbound.getWarehouseId()).orElse(null);
			if (Objects.nonNull(warehouse)) {
				warehouseName = warehouse.getName();
				warehouseType = warehouse.getType();
			}
			Storage storage = storageService.findOne(outbound.getStorageId())
					.orElse(null);
			if (Objects.nonNull(storage)) {
				storageName = storage.getName();
			}
			if (InboundDef.Type.JXC.match(outbound.getType())) {
				User user = userService.findOne(outbound.getCreatedBy())
						.orElse(null);
				if (Objects.nonNull(user)) {
					creatorName = user.getName();
				}
			} else {
				Customer customer = customerService
						.findOne(outbound.getCreatedBy()).orElse(null);
				if (Objects.nonNull(customer)) {
					creatorName = customer.getRealName();
				}
			}
			for (GoodsInfo warehouseGoodsInfo : goodsInfos) {
				InventoryDetails inventoryDetail = new InventoryDetails();
				inventoryDetail.setType(outbound.getType());
				inventoryDetail.setProjectId(outbound.getProjectId());
				inventoryDetail.setContractId(outbound.getContractId());
				inventoryDetail.setBoundId(outbound.getId());
				if (InboundDef.Type.JXC.match(outbound.getType())) {
					inventoryDetail.setBoundType(OutboundDef.OutboundType
							.from(outbound.getOutboundType()).getRelatecode());
				}
				inventoryDetail.setBoundTime(outbound.getOutboundTime());
				inventoryDetail.setWarehouseId(outbound.getWarehouseId());
				inventoryDetail.setStorageId(outbound.getStorageId());
				inventoryDetail.setWarehouseName(warehouseName);
				inventoryDetail.setStorageName(storageName);
				inventoryDetail.setWarehouseType(warehouseType);
				inventoryDetail.setGoodsNameId(outbound.getGoodsNameId());
				inventoryDetail.setGoodsName(goodsName);
				inventoryDetail.setModel(warehouseGoodsInfo.getModel());
				inventoryDetail.setUnitPrice(warehouseGoodsInfo.getUnitPrice());
				inventoryDetail
						.setQuantity(warehouseGoodsInfo.getOutboundWeight());
				inventoryDetail
						.setAmount(warehouseGoodsInfo.getOutboundAmount());
				inventoryDetail.setCreatorId(outbound.getCreatedBy());
				inventoryDetail.setCreatorName(creatorName);
				inventoryDetail.setState(CommonDef.Symbol.YES.getCode());
				inventoryDetail.setBusinessType(
						InventoryDetailsDef.businessType.OUTBOUND.getCode());
				inventoryDetailsList.add(inventoryDetail);
			}
		}
		// 批量创建库存明细
		inventoryDetailsService.batchCreate(inventoryDetailsList);

	}

	/**
	 * 出库作废之后 将对应的库存明细修改状态
	 *
	 * @param outbound
	 */
	@Override
	public void handleUpdateInventoryDetails(Outbound outbound, Integer state) {
		List<InventoryDetails> inventoryDetailsList = inventoryDetailsService
				.findByBoundId(outbound.getId());
		inventoryDetailsList
				.forEach(inventoryDetails -> inventoryDetails.setState(state));
		inventoryDetailsService.batchUpdate(inventoryDetailsList);
	}

	@Override
	public Optional<OutboundCountVo> staticsAdminOutbound(boolean isManage,
			boolean isSeal) {
		OutboundCountVo outboundCountVo = new OutboundCountVo();
		outboundCountVo.setWaitOutboundCount(0L);
		outboundCountVo.setWaitSigningCount(0L);
		outboundCountVo.setWaitConfirmingCount(0L);
		outboundCountVo.setRejectedCount(0L);
		outboundCountVo.setInvalidingCount(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计已驳回
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Outbound::getState,
						OutboundDef.Status.REJECTED.getCode());
				wrapper.in(Outbound::getProjectId, projectIds);
				wrapper.eq(Outbound::getType, InboundDef.Type.JXC.getCode());
				outboundCountVo
						.setRejectedCount(repository.selectCount(wrapper));

				// 统计作废中
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Outbound::getState,
						OutboundDef.Status.INVALIDING.getCode());
				wrapper.in(Outbound::getProjectId, projectIds);
				wrapper.eq(Outbound::getType, InboundDef.Type.JXC.getCode());
				outboundCountVo
						.setInvalidingCount(repository.selectCount(wrapper));

				// 统计待出库
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Outbound::getState,
						OutboundDef.Status.TO_BE_OUTBOUND.getCode());
				wrapper.in(Outbound::getProjectId, projectIds);
				wrapper.eq(Outbound::getType, InboundDef.Type.JXC.getCode());
				outboundCountVo
						.setWaitOutboundCount(repository.selectCount(wrapper));
			}
		}
		if (isSeal) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计待签署
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Outbound::getState,
						OutboundDef.Status.SIGNING.getCode())
						.in(Outbound::getSignState,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode(),
								BusinessContractDef.CommonSignState.UNSIGNED
										.getCode());
				wrapper.in(Outbound::getProjectId, projectIds);
				wrapper.eq(Outbound::getType, InboundDef.Type.JXC.getCode());
				outboundCountVo
						.setWaitOutboundCount(repository.selectCount(wrapper));
			}
		}
		return Optional.of(outboundCountVo);
	}

	@Override
	public Optional<OutboundCountVo> staticsCustomerOutbound(boolean isSeal,
			boolean isPermission) {
		OutboundCountVo outboundCountVo = new OutboundCountVo();
		outboundCountVo.setWaitOutboundCount(0L);
		outboundCountVo.setWaitSigningCount(0L);
		outboundCountVo.setWaitConfirmingCount(0L);
		outboundCountVo.setRejectedCount(0L);
		outboundCountVo.setInvalidingCount(0L);
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		if (isPermission) {
			// 统计待确认
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Outbound::getPurchaserId, customerId);
			wrapper.eq(Outbound::getState,
					OutboundDef.Status.CONFIRMING.getCode())
					.eq(Outbound::getSignState,
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode());
			outboundCountVo
					.setWaitConfirmingCount(repository.selectCount(wrapper));

			// 统计作废中
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Outbound::getPurchaserId, customerId);
			wrapper.eq(Outbound::getState,
					OutboundDef.Status.INVALIDING.getCode());
			outboundCountVo.setInvalidingCount(repository.selectCount(wrapper));
		}

		if (isSeal) {
			// 统计待签署
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Outbound::getPurchaserId, customerId);
			wrapper.eq(Outbound::getState, OutboundDef.Status.SIGNING.getCode())
					.in(Outbound::getSignState, List.of(
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode(),
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode()));
			outboundCountVo
					.setWaitSigningCount(repository.selectCount(wrapper));
		}

		return Optional.of(outboundCountVo);
	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = "{{#success}}", type = "{{#type}}", bizNo = "{{#resource.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#resource.getId()}}"),
			@LogRecord.KeyValuePair(key = "#projectId#", value = "{{#code}}") }, messageType = LogDef.MESSAGE_TYPE_INVENTORY, permission = "{{#permission}}")
	public void notice(Outbound resource, Integer type) {
		// 提前定义 type -> success 的映射
		Map<Integer, String> successMap = Map.of(

				1, LogDef.OUTBOUND_CUSTOMER_CONFIRMED,

				2, LogDef.OUTBOUND_CUSTOMER_REJECTED,

				3, LogDef.OUTBOUND_COMPLETED,

				4, LogDef.OUTBOUND_INVALID_UNCONFIRMED,

				5, LogDef.OUTBOUND_INVALID_REJECTED,

				6, LogDef.SUPERVISION_OUTBOUND_BE_CONFIRMED,

				7, LogDef.SUPERVISION_OUTBOUND_INVALID_UNCONFIRMED,

				8, LogDef.SUPERVISION_OUTBOUND_INVALID_REJECTED

		);

		// 设置 success
		if (successMap.containsKey(type)) {
			LogRecordContext.putVariable("success", successMap.get(type));

			// 判断是进销存还是仓储监管
			String typeLog = type <= 5 ? LogDef.OUTBOUND_INFO
					: LogDef.SUPERVISION_OUTBOUND_INFO;

			String permissionLog = type <= 5 ? LogDef.PROJECT_DEAL
					: LogDef.S_PROJECT_DEAL;

			if (type <= 5) {
				Project project = projectService
						.findOne(resource.getProjectId())
						.orElseThrow(() -> new BadRequestException(
								ErrorCode.CODE_30152013));
				LogRecordContext.putVariable("code", project.getName());
			} else {
				StockProject stockProject = stockProjectService
						.findOne(resource.getProjectId())
						.orElseThrow(() -> new BadRequestException(
								ErrorCode.CODE_30152013));
				LogRecordContext.putVariable("code", stockProject.getName());

			}

			LogRecordContext.putVariable("type", typeLog);
			LogRecordContext.putVariable("permission", permissionLog);
		}

		log.info("出库发送通知:{}", resource.getId());
	}

	@Override
	public void downloadPdf(HttpServletResponse response, List<String> ids,
			Boolean flag) throws IOException {
		setExportResponseFields(response, ids.get(0));
		List<Outbound> outbounds = this.findByIds(ids);
		Map<String, Warehouse> warehouseMap = warehouseService.getIdMap(
				outbounds.stream().map(Outbound::getWarehouseId).toList());
		Map<Long, Storage> storageMap;
		Map<Long, String> userMap;
		if (CollectionUtils.isNotEmpty(
				outbounds.stream().map(Outbound::getStorageId).toList())) {
			storageMap = storageService.getIdMap(
					outbounds.stream().map(Outbound::getStorageId).toList());
		} else {
			storageMap = new HashMap<>();
		}
		String name;
		if (Objects.nonNull(UserContextHolder.getUser())) {
			name = UserContextHolder.getUser().getName();
		} else {
			name = CustomerContextHolder.getCustomerLoginVo().getProxyAccount()
					.getRealName();
		}

		if (InboundDef.Type.JXC.match(outbounds.get(0).getType())) {
			userMap = userService
					.findByIds(outbounds.stream().map(Outbound::getCreatedBy)
							.toList())
					.stream()
					.collect(Collectors.toMap(User::getId, User::getName));
			OutboundPdfUtils.getPdf(response.getOutputStream(), outbounds, name,
					warehouseMap, storageMap, userMap, flag);
		} else {
			userMap = customerService
					.findByIds(outbounds.stream().map(Outbound::getCreatedBy)
							.toList())
					.stream().collect(Collectors.toMap(Customer::getId,
							Customer::getRealName));
			OutboundPdfUtils.getStockPdf(response.getOutputStream(), outbounds,
					name, warehouseMap, storageMap, userMap, flag);
		}
	}

	@Override
	public Optional<OutboundCountVo> staticsStockAdminOutbound(
			boolean isManage) {
		OutboundCountVo outboundCountVo = new OutboundCountVo();
		outboundCountVo.setRejectedCount(0L);
		outboundCountVo.setInvalidingCount(0L);
		outboundCountVo.setWaitSigningCount(0L);
		outboundCountVo.setWaitConfirmingCount(0L);
		outboundCountVo.setWaitOutboundCount(0L);
		List<String> projectIds = stockProjectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计待确认
				LambdaQueryWrapper<Outbound> wrapper = Wrappers
						.lambdaQuery(Outbound.class);
				this.filterDeleted(wrapper);
				wrapper.eq(Outbound::getState,
						OutboundDef.Status.CONFIRMING.getCode());
				wrapper.eq(Outbound::getSignState,
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				wrapper.eq(Outbound::getType,
						InboundDef.Type.STORAGE.getCode());
				wrapper.in(Outbound::getProjectId, projectIds);
				outboundCountVo.setWaitConfirmingCount(
						repository.selectCount(wrapper));
				wrapper.clear();

				// 统计待签署
				this.filterDeleted(wrapper);
				wrapper.eq(Outbound::getState,
						OutboundDef.Status.TO_BE_SIGN.getCode())
						.in(Outbound::getSignState, List.of(
								BusinessContractDef.CommonSignState.UNSIGNED
										.getCode(),
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode()));
				wrapper.eq(Outbound::getType,
						InboundDef.Type.STORAGE.getCode());
				outboundCountVo
						.setWaitSigningCount(repository.selectCount(wrapper));
				wrapper.clear();

				// 统计作废中
				this.filterDeleted(wrapper);
				wrapper.eq(Outbound::getState,
						OutboundDef.Status.INVALIDING.getCode());
				wrapper.eq(Outbound::getType,
						InboundDef.Type.STORAGE.getCode());
				wrapper.in(Outbound::getProjectId, projectIds);
				outboundCountVo
						.setInvalidingCount(repository.selectCount(wrapper));
			}
		}
		return Optional.of(outboundCountVo);
	}

	@Override
	public Optional<OutboundCountVo> staticsStockCustomerOutbound() {
		OutboundCountVo outboundCountVo = new OutboundCountVo();
		outboundCountVo.setRejectedCount(0L);
		outboundCountVo.setInvalidingCount(0L);
		outboundCountVo.setWaitSigningCount(0L);
		outboundCountVo.setWaitConfirmingCount(0L);
		outboundCountVo.setWaitOutboundCount(0L);
		// 统计已驳回
		LambdaQueryWrapper<Outbound> wrapper = Wrappers
				.lambdaQuery(Outbound.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Outbound::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		wrapper.eq(Outbound::getType, InboundDef.Type.STORAGE.getCode());
		wrapper.eq(Outbound::getState, OutboundDef.Status.REJECTED.getCode());
		outboundCountVo.setRejectedCount(repository.selectCount(wrapper));
		wrapper.clear();

		// 统计待签署
		this.filterDeleted(wrapper);
		wrapper.eq(Outbound::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		wrapper.eq(Outbound::getType, InboundDef.Type.STORAGE.getCode());
		wrapper.eq(Outbound::getState, OutboundDef.Status.SIGNING.getCode()).in(
				Outbound::getSignState,
				List.of(BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode()));
		outboundCountVo.setWaitSigningCount(repository.selectCount(wrapper));
		wrapper.clear();

		// 统计作废中
		this.filterDeleted(wrapper);
		wrapper.eq(Outbound::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		wrapper.eq(Outbound::getType, InboundDef.Type.STORAGE.getCode());
		wrapper.eq(Outbound::getState, OutboundDef.Status.INVALIDING.getCode());
		outboundCountVo.setInvalidingCount(repository.selectCount(wrapper));
		return Optional.of(outboundCountVo);
	}

	/**
	 * @description: 获取文件id，生成pdf文件并上传文件服务器，返回文件id
	 * @author: 彭湃
	 * @date: 2025/1/23 15:26
	 * @param: [resource]
	 * @return: java.lang.Long
	 **/
	private Long getFileId(Outbound outbound) {
		// 生成pdf
		OutputStream outputStream = new ByteArrayOutputStream();
		Map<String, Warehouse> warehouseMap = warehouseService
				.getIdMap(List.of(outbound.getWarehouseId()));
		Map<Long, Storage> storageMap;
		Map<Long, String> userMap;
		if (Objects.nonNull(outbound.getStorageId())) {
			storageMap = storageService
					.getIdMap(List.of(outbound.getStorageId()));
		} else {
			storageMap = new HashMap<>();
		}

		if (InboundDef.Type.JXC.match(outbound.getType())) {
			userMap = userService.findByIds(List.of(outbound.getCreatedBy()))
					.stream()
					.collect(Collectors.toMap(User::getId, User::getName));
			OutboundPdfUtils.getPdf(outputStream, List.of(outbound),
					Objects.requireNonNull(UserContextHolder.getUser())
							.getName(),
					warehouseMap, storageMap, userMap, false);
		} else {
			userMap = customerService
					.findByIds(List.of(outbound.getCreatedBy())).stream()
					.collect(Collectors.toMap(Customer::getId,
							Customer::getRealName));
			OutboundPdfUtils.getStockPdf(outputStream, List.of(outbound),
					Objects.requireNonNull(
							CustomerContextHolder.getCustomerLoginVo()
									.getProxyAccount().getRealName()),
					warehouseMap, storageMap, userMap, false);
		}

		try {
			CustomMultipartFile convert = MultipartFileUtils.convert(
					outputStream, "出库合同" + outbound.getId(),
					"出库合同" + outbound.getId() + ".pdf", "text/plain");
			File file = fileService
					.upload(convert, "出库合同" + outbound.getId() + ".pdf")
					.orElse(null);
			if (Objects.nonNull(file)) {
				return file.getId();
			}
		} catch (Exception e) {
			log.error("生成pdf文件失败", e);
		}
		return null;
	}

	/**
	 * 设置仓储费用
	 *
	 * @param outbound
	 */
	@Override
	public void setStorageFee(Outbound outbound) {
		if (Objects.isNull(outbound.getOutboundFee())) {
			return;
		}
		if (InboundDef.Type.JXC.match(outbound.getType())) {
			// 只在仓储入库单中设置仓储费用
			return;
		}
		List<StockContractFees> contractFees = stockContractFeesService
				.findByContractIdAndFeeItem(outbound.getContractId(),
						ContractDef.FeeItem.OUTBOUND.getCode());
		StorageFee storageFee = new StorageFee();
		storageFee.setProjectId(outbound.getProjectId());
		storageFee.setContractId(outbound.getContractId());
		storageFee.setGoodsId(outbound.getGoodsNameId());
		storageFee.setBusinessDate(outbound.getOutboundTime());
		storageFee.setWeight(outbound.getOutboundWeight());
		storageFee.setFeeType(StorageFeeDef.FeeType.OUTBOUND.getCode());
		storageFee.setFeeStandards(outbound.getFeeStandards());
		storageFee.setUnit(contractFees.get(0).getUnit());
		storageFee.setAmount(outbound.getOutboundFee());
		storageFee.setStorageId(outbound.getStorageId());
		storageFee.setWarehouseId(outbound.getWarehouseId());
		storageFee.setRelateId(outbound.getId());
		storageFeeService.create(storageFee);
	}

	@Override
	public void downloadTemplate(HttpServletResponse response,
			Outbound outbound, String detectionInfo,
			LocalDateTime detectionDate) throws IOException {
		setExportResponseFields(response, outbound.getId());
		Goods goods = goodsService.findOne(outbound.getGoodsNameId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30156001));
		Warehouse warehouse = warehouseService
				.findOne(outbound.getWarehouseId()).orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30163001));
		String storageName = "";
		if (Objects.nonNull(outbound.getStorageId())) {
			Storage storage = storageService.findOne(outbound.getStorageId())
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30164001));
			if (Objects.nonNull(storage)) {
				storageName = storage.getName();
			}
		}
		OutboundDetectionPdfUtils.getTemplate(response.getOutputStream(),
				outbound, goods, warehouse.getName(), storageName,
				detectionInfo, detectionDate);
	}

	/**
	 * 设置导出响应头
	 *
	 * @param response
	 */
	private void setExportResponseFields(HttpServletResponse response,
			String id) {
		response.setContentType("application/pdf");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(
				String.format("出库_%s_%s",
						DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
								.format(LocalDateTime.now()),
						id),
				StandardCharsets.UTF_8).replaceAll("\\+", "%20");
		response.setHeader("Content-disposition",
				"attachment;filename*=utf-8''" + fileName + ".pdf");
	}

	/*
	 * @description: json转换为List<WarehouseGoodsInfo>
	 *
	 * @author: pp
	 *
	 * @date: 2025/5/19 14:05
	 *
	 * @param: [data]
	 *
	 * @return:
	 * java.util.List<com.zhihaoscm.domain.bean.entity.WarehouseGoodsInfo>
	 **/
	private <T> List<T> convertJsonToList(String data, Class<T> clazz) {
		Gson gson = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
		Type listType = TypeToken.getParameterized(List.class, clazz).getType();
		return gson.fromJson(data, listType);
	}

	/*
	 * @description: 组装出库单vos
	 *
	 * @author: pp
	 *
	 * @date: 2025/5/19 14:48
	 *
	 * @param: [inbounds]
	 *
	 * @return: java.util.List<com.zhihaoscm.domain.bean.vo.InboundVo>
	 **/
	private List<OutboundVo> packVos(List<Outbound> outbounds) {
		if (CollectionUtils.isEmpty(outbounds)) {
			return List.of();
		}
		List<String> projectIds = outbounds.stream().map(Outbound::getProjectId)
				.toList();
		List<String> contractIds = outbounds.stream()
				.map(Outbound::getContractId).toList();
		List<Long> goodsIds = outbounds.stream().map(Outbound::getGoodsNameId)
				.toList();
		List<String> warehouseIds = outbounds.stream()
				.map(Outbound::getWarehouseId).toList();
		List<Long> storageIds = outbounds.stream().map(Outbound::getStorageId)
				.toList();
		List<Long> userIds = outbounds.stream().map(Outbound::getCreatedBy)
				.toList();
		List<Long> persons = outbounds.stream().map(Outbound::getOutboundPerson)
				.toList();
		List<Long> reviewer = outbounds.stream().map(Outbound::getReviewer)
				.toList();
		if (CollectionUtils.isNotEmpty(persons)) {
			userIds = new ArrayList<>(userIds);
			userIds.addAll(persons);
		}
		if (CollectionUtils.isNotEmpty(reviewer)) {
			userIds = new ArrayList<>(userIds);
			userIds.addAll(reviewer);
		}
		List<Project> projectList = projectService.findByIds(projectIds);
		List<StockProject> stockProjectList = stockProjectService
				.findByIds(projectIds);
		List<Contract> contractList = contractService.findByIds(contractIds);
		List<StockContract> stockContractList = stockContractService
				.findByIds(contractIds);
		List<Goods> goodsList = goodsService.findByIds(goodsIds);
		List<Warehouse> warehouseList = warehouseService
				.findByIds(warehouseIds);
		List<Storage> storageList = storageService.findByIds(storageIds);
		List<User> users = userService.findByIds(userIds);
		List<Customer> customerList = customerService.findByIds(userIds);
		Map<String, Project> projectMap = projectList.stream()
				.collect(Collectors.toMap(Project::getId, e -> e));
		Map<String, StockProject> stockProjectMap = stockProjectList.stream()
				.collect(Collectors.toMap(StockProject::getId, e -> e));
		Map<String, Contract> contractMap = contractList.stream()
				.collect(Collectors.toMap(Contract::getId, e -> e));
		Map<String, StockContract> stockContractMap = stockContractList.stream()
				.collect(Collectors.toMap(StockContract::getId, e -> e));
		Map<Long, Goods> goodsMap = goodsList.stream()
				.collect(Collectors.toMap(Goods::getId, e -> e));
		Map<String, Warehouse> warehouseMap = warehouseList.stream()
				.collect(Collectors.toMap(Warehouse::getId, e -> e));
		Map<Long, Storage> storageMap = storageList.stream()
				.collect(Collectors.toMap(Storage::getId, e -> e));
		Map<Long, User> userMap = users.stream()
				.collect(Collectors.toMap(User::getId, e -> e));
		Map<Long, Customer> customerMap = customerList.stream()
				.collect(Collectors.toMap(Customer::getId, e -> e));

		return outbounds.stream().map(e -> {
			OutboundVo vo = new OutboundVo();
			vo.setOutbound(e);
			vo.setProject(projectMap.get(e.getProjectId()));
			vo.setStockProject(stockProjectMap.get(e.getProjectId()));
			vo.setContract(contractMap.get(e.getContractId()));
			vo.setStockContract(stockContractMap.get(e.getContractId()));
			vo.setGoods(goodsMap.get(e.getGoodsNameId()));
			vo.setWarehouse(warehouseMap.get(e.getWarehouseId()));
			vo.setStorage(storageMap.get(e.getStorageId()));
			vo.setUser(userMap.get(e.getCreatedBy()));
			if (Objects.nonNull(e.getOutboundPerson())) {
				vo.setOutboundPerson(userMap.get(e.getOutboundPerson()));
			}
			if (Objects.nonNull(e.getReviewer())) {
				vo.setReviewUser(userMap.get(e.getReviewer()));
			}
			vo.setCustomer(customerMap.get(e.getCreatedBy()));
			return vo;
		}).toList();
	}

	private OutboundVo packVo(Outbound outbound) {
		if (Objects.isNull(outbound)) {
			return null;
		}
		OutboundVo vo = new OutboundVo();
		vo.setOutbound(outbound);
		vo.setProject(
				projectService.findOne(outbound.getProjectId()).orElse(null));
		vo.setStockProject(stockProjectService.findOne(outbound.getProjectId())
				.orElse(null));
		vo.setContract(
				contractService.findOne(outbound.getContractId()).orElse(null));
		vo.setStockContract(stockContractService
				.findOne(outbound.getContractId()).orElse(null));
		vo.setGoods(goodsService.findOne(outbound.getGoodsNameId())
				.orElse(new Goods()));
		vo.setWarehouse(warehouseService.findOne(outbound.getWarehouseId())
				.orElse(new Warehouse()));
		vo.setStorage(storageService.findOne(outbound.getStorageId())
				.orElse(new Storage()));
		vo.setUser(userService.findOne(outbound.getCreatedBy()).orElse(null));
		vo.setReviewUser(
				userService.findOne(outbound.getReviewer()).orElse(null));
		vo.setCustomer(
				customerService.findOne(outbound.getCreatedBy()).orElse(null));
		if (Objects.nonNull(outbound.getOutboundPerson())) {
			vo.setOutboundPerson(userService
					.findOne(outbound.getOutboundPerson()).orElse(new User()));
		}
		return vo;

	}

	private List<PledgeInfo> convertPledgeInfo(String data) {
		Gson gson = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
		Type listType = new TypeToken<List<PledgeInfo>>() {
		}.getType();
		return gson.fromJson(data, listType);
	}

	/**
	 * 发送短信
	 *
	 * @param outbound
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(Outbound outbound, String templateCode,
			String title) {
		Customer customer = null;
		DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
				.findOne(outbound.getPurchaserBusinessId()).orElse(null);
		if (Objects.nonNull(dealingsEnterprise)
				&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
			customer = customerService
					.findOne(dealingsEnterprise.getCustomerId()).orElse(null);
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("order_id", outbound.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.INVENTORY.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.OUTBOUND_DETAIL_PAGE)
						.detailId(String.valueOf(outbound.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}

}
