package com.zhihaoscm.service.core.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.api.util.multipart.file.CustomMultipartFile;
import com.zhihaoscm.common.api.util.multipart.file.MultipartFileUtils;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.bean.dto.GeneratPdfDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.GoodsInfo;
import com.zhihaoscm.domain.bean.json.ReconciliationJsonInfo;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.PdfUtils;
import com.zhihaoscm.domain.utils.holiday.HolidayUtils;
import com.zhihaoscm.service.config.LocalDateTimeAdapter;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.properties.WorkDayProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.ReconciliationMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.AdminSealService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.InstitutionApplyService;
import com.zhihaoscm.service.resource.form.reconciliation.ReconciliationAssociaForm;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 对账 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Service
public class ReconciliationServiceImpl
		extends MpStringIdBaseServiceImpl<Reconciliation, ReconciliationMapper>
		implements ReconciliationService {

	@Autowired
	TransportOrderRailwayService transportOrderRailwayService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private ProjectService projectService;
	@Autowired
	private SignReceiptService signReceiptService;
	@Autowired
	private DeliverGoodsService deliverGoodsService;
	@Autowired
	private OrderService orderService;
	@Autowired
	private ContractRecordService contractRecordService;
	@Autowired
	private FileService fileService;
	@Autowired
	private BillPaymentService billPaymentService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private PaymentService paymentService;
	@Autowired
	private ContractService contractService;
	@Autowired
	private AdminSealService adminSealService;
	@Autowired
	private AccountsService accountsService;
	@Autowired
	private WorkDayProperties workDayProperties;
	@Autowired
	private TransportOrderShipService transportOrderShipService;
	@Autowired
	private TransportOrderVehicleService transportOrderVehicleService;
	@Autowired
	private InstitutionApplyService institutionApplyService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	public ReconciliationServiceImpl(ReconciliationMapper repository) {
		super(repository);
	}

	// 处理gson类
	private static Gson getGson() {
		return new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
	}

	@Override
	public Page<ReconciliationVo> buyPaging(Integer page, Integer size,
			String sortKey, String sortOrder, String param, String goodsName,
			String seller, LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, Boolean hasAll, Long userId) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class)
				.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Reconciliation::getType, ContractDef.Type.BUY.getCode());
		if (Objects.nonNull(param)) {
			List<String> projectIds = projectService.findByNameLike(param)
					.stream().map(Project::getId).distinct().toList();
			wrapper.and(x -> x.like(Reconciliation::getId, param).or().in(
					CollectionUtils.isNotEmpty(projectIds),
					Reconciliation::getProjectId, projectIds));
		}
		if (Objects.nonNull(goodsName)) {
			wrapper.and(x -> x.like(Reconciliation::getGoodsName, goodsName));
		}
		wrapper.apply(StringUtils.isNotBlank(seller),
				" (JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%')"
						+ "OR JSON_EXTRACT(seller_enterprise, '$.realName') LIKE CONCAT('%',{0},'%'))",
				seller);
		// 检查 beginTime 和 endTime 是否为空
		if (Objects.nonNull(beginTime) && Objects.nonNull(endTime)) {
			// 如果都不为空，则添加条件
			wrapper.and(w -> w
					.or(y -> y
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z
									.ge(Reconciliation::getReconciliationDate,
											beginTime)
									.le(Reconciliation::getReconciliationDate,
											endTime)))
					.or(y -> y
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z.ge(
									Reconciliation::getPreReconciliationDate,
									beginTime)
									.le(Reconciliation::getPreReconciliationDate,
											endTime))));
		}
		// 买方未签署
		List<Integer> toBeSignedList = List.of(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
				BusinessContractDef.CommonSignState.SUPPLY_SIGNED.getCode());

		// 没有传入状态时
		if (CollectionUtils.isEmpty(states)) {
			// 除了草稿和已驳回查询发起方是自己的 其他根据状态查询
			wrapper.and(x -> x
					.in(Reconciliation::getState,
							ReconciliationDef.State.SIGNING.getCode(),
							ReconciliationDef.State.CONFIRMING.getCode(),
							ReconciliationDef.State.FINISHED.getCode(),
							ReconciliationDef.State.INVALIDING.getCode(),
							ReconciliationDef.State.INVALID.getCode(),
							ReconciliationDef.State.PRE_FINISHED.getCode())
					.or(y -> y
							.eq(Reconciliation::getInitiator,
									CommonDef.AccountSource.INNER.getCode())
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode(),
									ReconciliationDef.State.TO_BE_INITIATE
											.getCode()))
					.or(y -> y
							.eq(Reconciliation::getPreInitiator,
									CommonDef.AccountSource.INNER.getCode())
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode(),
									ReconciliationDef.State.TO_BE_INITIATE
											.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿状态
					if (ReconciliationDef.State.DRAFT.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
					}
					// 已驳回状态
					else if (ReconciliationDef.State.REJECTED.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
					}
					// 待确认状态
					else if (ReconciliationDef.State.WAIT_CONFIRM
							.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 确认中状态
					else if (ReconciliationDef.State.CONFIRMING.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 签署中状态 签署中状态 并且销售方已签署
					else if (ReconciliationDef.State.SIGNING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 待签署状态 签署中状态 并且销售方已签署或者双方未签署
					else if (ReconciliationDef.State.TO_BE_SIGNED
							.match(state)) {
						x.or(y -> y
								.in(Reconciliation::getSignStatus,
										toBeSignedList)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y
								.in(Reconciliation::getPreSignStatus,
										toBeSignedList)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 已完成状态
					else if (ReconciliationDef.State.FINISHED.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.FINISHED.getCode()));
					}
					// 预对账完成状态
					else if (ReconciliationDef.State.PRE_FINISHED
							.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.PRE_FINISHED
										.getCode()));
					}
					// 待发起
					else if (ReconciliationDef.State.TO_BE_INITIATE
							.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.TO_BE_INITIATE
										.getCode()));
					}
					// 作废中状态
					else if (ReconciliationDef.State.INVALIDING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALIDING.getCode()));
					}
					// 已作废状态
					else if (ReconciliationDef.State.INVALID.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALID.getCode()));
					}
				}
			});

		}
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			// 处理人是自己在的
			List<String> projectIdList = projectService.findByUserId(userId,
					null);
			if (CollectionUtils.isNotEmpty(projectIdList)) {
				wrapper.in(Reconciliation::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			if ("reconciliation_date".equalsIgnoreCase(sortKey)) {
				// 如果是对账日期排序，则按照对账日期倒序，对账日期相同按创建时间倒序排序。
				if ("DESC".equalsIgnoreCase(sortOrder)) {
					wrapper.last("order by GREATEST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '0001-01-01' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '0001-01-01')"
							+ "    )" + "DESC");
				} else {
					wrapper.last("order by LEAST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '9999-12-31' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '9999-12-31')"
							+ "  ) ASC");
				}
			} else {
				wrapper.last(
						"order by " + sortKey + " " + sortOrder + ", id DESC");
			}
		} else {
			wrapper.last("ORDER BY updated_time DESC");
		}
		Page<Reconciliation> paging = repository
				.selectPage(new Page<>(page, size), wrapper);
		List<ReconciliationVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<ReconciliationVo> salePaging(Integer page, Integer size,
			String sortKey, String sortOrder, String param, String goodsName,
			String buyer, LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, Integer origin, Boolean hasAll, Long userId) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class)
				.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Reconciliation::getType, ContractDef.Type.SELL.getCode());
		if (Objects.nonNull(param)) {
			List<String> projectIds = projectService.findByNameLike(param)
					.stream().map(Project::getId).distinct().toList();
			wrapper.and(x -> x.like(Reconciliation::getId, param).or().in(
					CollectionUtils.isNotEmpty(projectIds),
					Reconciliation::getProjectId, projectIds));
		}
		if (Objects.nonNull(goodsName)) {
			wrapper.and(x -> x.like(Reconciliation::getGoodsName, goodsName));
		}
		wrapper.apply(StringUtils.isNotBlank(buyer),
				" (JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%')"
						+ "OR JSON_EXTRACT(purchaser_enterprise, '$.realName') LIKE CONCAT('%',{0},'%'))",
				buyer);

		// 检查 beginTime 和 endTime 是否为空
		if (Objects.nonNull(beginTime) && Objects.nonNull(endTime)) {
			// 如果都不为空，则添加条件
			wrapper.and(w -> w
					.or(y -> y
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z
									.ge(Reconciliation::getReconciliationDate,
											beginTime)
									.le(Reconciliation::getReconciliationDate,
											endTime)))
					.or(y -> y
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z.ge(
									Reconciliation::getPreReconciliationDate,
									beginTime)
									.le(Reconciliation::getPreReconciliationDate,
											endTime))));
		}
		// 卖方未签署
		List<Integer> toBeSignedList = List.of(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
				BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
		// 没有传入状态时
		if (CollectionUtils.isEmpty(states)) {
			// 除了草稿和已驳回查询发起方是自己的 其他根据状态查询
			wrapper.and(x -> x
					.in(Reconciliation::getState,
							ReconciliationDef.State.SIGNING.getCode(),
							ReconciliationDef.State.CONFIRMING.getCode(),
							ReconciliationDef.State.FINISHED.getCode(),
							ReconciliationDef.State.INVALIDING.getCode(),
							ReconciliationDef.State.INVALID.getCode(),
							ReconciliationDef.State.PRE_FINISHED.getCode())
					.or(y -> y.eq(Reconciliation::getInitiator, origin)
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode(),
									ReconciliationDef.State.TO_BE_INITIATE
											.getCode()))
					.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode(),
									ReconciliationDef.State.TO_BE_INITIATE
											.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿状态
					if (ReconciliationDef.State.DRAFT.match(state)) {
						x.or(y -> y.eq(Reconciliation::getInitiator, origin)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
					}
					// 已驳回状态
					else if (ReconciliationDef.State.REJECTED.match(state)) {
						x.or(y -> y.eq(Reconciliation::getInitiator, origin)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
					}
					// 待确认状态
					else if (ReconciliationDef.State.WAIT_CONFIRM
							.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 确认中状态
					else if (ReconciliationDef.State.CONFIRMING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getInitiator, origin)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getInitiator, origin)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 签署中状态 签署中状态 并且销售方已签署
					else if (ReconciliationDef.State.SIGNING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 待签署状态 签署中状态 并且买方已签署或者双方未签署
					else if (ReconciliationDef.State.TO_BE_SIGNED
							.match(state)) {
						x.or(y -> y
								.in(Reconciliation::getSignStatus,
										toBeSignedList)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y
								.in(Reconciliation::getPreSignStatus,
										toBeSignedList)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 已完成状态
					else if (ReconciliationDef.State.FINISHED.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.FINISHED.getCode()));
					}
					// 预对账完成状态
					else if (ReconciliationDef.State.PRE_FINISHED
							.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.PRE_FINISHED
										.getCode()));
					}
					// 待发起
					else if (ReconciliationDef.State.TO_BE_INITIATE
							.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.TO_BE_INITIATE
										.getCode()));
					}
					// 作废中状态
					else if (ReconciliationDef.State.INVALIDING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALIDING.getCode()));
					}
					// 已作废状态
					else if (ReconciliationDef.State.INVALID.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALID.getCode()));
					}
				}
			});

		}
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			// 处理人是自己在的
			List<String> projectIdList = projectService.findByUserId(userId,
					null);
			if (CollectionUtils.isNotEmpty(projectIdList)) {
				wrapper.in(Reconciliation::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			if ("reconciliation_date".equalsIgnoreCase(sortKey)) {
				// 如果是对账日期排序，则按照对账日期倒序，对账日期相同按创建时间倒序排序。
				if ("DESC".equalsIgnoreCase(sortOrder)) {
					wrapper.last("order by GREATEST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '0001-01-01' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '0001-01-01')"
							+ "    )" + "DESC");
				} else {
					wrapper.last("order by LEAST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '9999-12-31' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '9999-12-31')"
							+ "  ) ASC");
				}
			} else {
				wrapper.last(
						"order by " + sortKey + " " + sortOrder + ", id DESC");
			}
		} else {
			wrapper.last("ORDER BY updated_time DESC");
		}
		Page<Reconciliation> paging = repository
				.selectPage(new Page<>(page, size), wrapper);
		List<ReconciliationVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<ReconciliationVo> customBuyPaging(Integer page, Integer size,
			String sortKey, String sortOrder, String param, String goodsName,
			String buyer, LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, Long customerId, Integer origin) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class)
				.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Reconciliation::getPurchaserId, customerId);
		// 客户端的采购对应供应链是销售对账
		wrapper.eq(Reconciliation::getType,
				ReconciliationDef.Type.SELL.getCode());
		if (Objects.nonNull(param)) {
			wrapper.and(x -> x.like(Reconciliation::getContractName, param).or()
					.like(Reconciliation::getId, param));
		}
		if (Objects.nonNull(goodsName)) {
			wrapper.and(x -> x.like(Reconciliation::getGoodsName, goodsName));
		}
		wrapper.apply(StringUtils.isNotBlank(buyer),
				" (JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%')"
						+ "OR JSON_EXTRACT(purchaser_enterprise, '$.realName') LIKE CONCAT('%',{0},'%'))",
				buyer);

		// 检查 beginTime 和 endTime 是否为空
		if (Objects.nonNull(beginTime) && Objects.nonNull(endTime)) {
			// 如果都不为空，则添加条件
			wrapper.and(w -> w
					.or(y -> y
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z
									.ge(Reconciliation::getReconciliationDate,
											beginTime)
									.le(Reconciliation::getReconciliationDate,
											endTime)))
					.or(y -> y
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z.ge(
									Reconciliation::getPreReconciliationDate,
									beginTime)
									.le(Reconciliation::getPreReconciliationDate,
											endTime))));
		}
		// 买方未签署
		List<Integer> toBeSignedList = List.of(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
				BusinessContractDef.CommonSignState.SUPPLY_SIGNED.getCode());
		// 只有销售项目才需要判断状态
		// 查询所有数据，则查询除了pc端草稿和pc端驳回状态的所有状态
		if (CollectionUtils.isEmpty(states)) {
			// 除了草稿和已驳回,待发起 查询发起方是自己的 其他根据状态查询
			wrapper.and(x -> x
					.in(Reconciliation::getState,
							ReconciliationDef.State.SIGNING.getCode(),
							ReconciliationDef.State.CONFIRMING.getCode(),
							ReconciliationDef.State.FINISHED.getCode(),
							ReconciliationDef.State.INVALIDING.getCode(),
							ReconciliationDef.State.INVALID.getCode(),
							ReconciliationDef.State.PRE_FINISHED.getCode())
					.or(y -> y.eq(Reconciliation::getInitiator, origin)
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode()))
					.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED
											.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿状态
					if (ReconciliationDef.State.DRAFT.match(state)) {
						x.or(y -> y.eq(Reconciliation::getInitiator, origin)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
					}
					// 已驳回状态
					else if (ReconciliationDef.State.REJECTED.match(state)) {
						x.or(y -> y.eq(Reconciliation::getInitiator, origin)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
					}
					// 待确认状态 对方发起的确认中的状态
					else if (ReconciliationDef.State.WAIT_CONFIRM
							.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 确认中状态
					else if (ReconciliationDef.State.CONFIRMING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getInitiator, origin)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 签署中状态 签署中状态 并且买方已签署
					else if (ReconciliationDef.State.SIGNING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getSignStatus,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreSignStatus,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 待签署状态 签署中状态 并且卖方已签署或者双方未签署
					else if (ReconciliationDef.State.TO_BE_SIGNED
							.match(state)) {
						x.or(y -> y
								.in(Reconciliation::getSignStatus,
										toBeSignedList)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y
								.in(Reconciliation::getPreSignStatus,
										toBeSignedList)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 已完成状态
					else if (ReconciliationDef.State.FINISHED.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.FINISHED.getCode()));
					}
					// 预对账完成状态
					else if (ReconciliationDef.State.PRE_FINISHED
							.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.PRE_FINISHED
										.getCode()));
					}
					// 作废中状态
					else if (ReconciliationDef.State.INVALIDING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALIDING.getCode()));
					}
					// 已作废状态
					else if (ReconciliationDef.State.INVALID.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALID.getCode()));
					}
				}
			});
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			if ("reconciliation_date".equalsIgnoreCase(sortKey)) {
				// 如果是对账日期排序，则按照对账日期倒序，对账日期相同按创建时间倒序排序。
				if ("DESC".equalsIgnoreCase(sortOrder)) {
					wrapper.last("order by GREATEST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '0001-01-01' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '0001-01-01')"
							+ "    )" + "DESC");
				} else {
					wrapper.last("order by LEAST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '9999-12-31' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '9999-12-31')"
							+ "  ) ASC");
				}
			} else {
				wrapper.last(
						"order by " + sortKey + " " + sortOrder + ", id DESC");
			}
		} else {
			wrapper.last("ORDER BY updated_time DESC");
		}
		Page<Reconciliation> paging = repository
				.selectPage(new Page<>(page, size), wrapper);
		List<ReconciliationVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<ReconciliationVo> customSalePaging(Integer page, Integer size,
			String sortKey, String sortOrder, String param, String goodsName,
			String buyer, LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, Long customerId, Integer origin) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class)
				.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Reconciliation::getSellerId, customerId);
		// 客户端的销售 对应供应链是采购对账
		wrapper.eq(Reconciliation::getType,
				ReconciliationDef.Type.BUY.getCode());
		if (Objects.nonNull(param)) {
			wrapper.and(x -> x.like(Reconciliation::getContractName, param).or()
					.like(Reconciliation::getId, param));
		}
		if (Objects.nonNull(goodsName)) {
			wrapper.and(x -> x.like(Reconciliation::getGoodsName, goodsName));
		}
		wrapper.apply(StringUtils.isNotBlank(buyer),
				" (JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%')"
						+ "OR JSON_EXTRACT(seller_enterprise, '$.realName') LIKE CONCAT('%',{0},'%'))",
				buyer);

		// 检查 beginTime 和 endTime 是否为空
		if (Objects.nonNull(beginTime) && Objects.nonNull(endTime)) {
			// 如果都不为空，则添加条件
			wrapper.and(w -> w
					.or(y -> y
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z
									.ge(Reconciliation::getReconciliationDate,
											beginTime)
									.le(Reconciliation::getReconciliationDate,
											endTime)))
					.or(y -> y
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z.ge(
									Reconciliation::getPreReconciliationDate,
									beginTime)
									.le(Reconciliation::getPreReconciliationDate,
											endTime))));
		}
		// 销售方未签署
		List<Integer> toBeSignedList = List.of(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
				BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
		// 只有销售项目才需要判断状态
		// 查询所有数据，则查询除了pc端草稿和pc端驳回状态的所有状态
		if (CollectionUtils.isEmpty(states)) {
			// 除了草稿和已驳回,待发起 查询发起方是自己的 其他根据状态查询
			wrapper.and(x -> x
					.in(Reconciliation::getState,
							ReconciliationDef.State.SIGNING.getCode(),
							ReconciliationDef.State.CONFIRMING.getCode(),
							ReconciliationDef.State.FINISHED.getCode(),
							ReconciliationDef.State.INVALIDING.getCode(),
							ReconciliationDef.State.INVALID.getCode(),
							ReconciliationDef.State.PRE_FINISHED.getCode())
					.or(y -> y.eq(Reconciliation::getInitiator, origin)
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode()))
					.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED
											.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿状态
					if (ReconciliationDef.State.DRAFT.match(state)) {
						x.or(y -> y.eq(Reconciliation::getInitiator, origin)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
					}
					// 已驳回状态
					else if (ReconciliationDef.State.REJECTED.match(state)) {
						x.or(y -> y.eq(Reconciliation::getInitiator, origin)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
					}
					// 待确认状态 对方发起的确认中的状态
					else if (ReconciliationDef.State.WAIT_CONFIRM
							.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 确认中状态
					else if (ReconciliationDef.State.CONFIRMING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getInitiator, origin)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreInitiator, origin)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 签署中状态 签署中状态 并且买方已签署
					else if (ReconciliationDef.State.SIGNING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getSignStatus,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreSignStatus,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 待签署状态 签署中状态 并且卖方已签署或者双方未签署
					else if (ReconciliationDef.State.TO_BE_SIGNED
							.match(state)) {
						x.or(y -> y
								.in(Reconciliation::getSignStatus,
										toBeSignedList)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y
								.in(Reconciliation::getPreSignStatus,
										toBeSignedList)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 已完成状态
					else if (ReconciliationDef.State.FINISHED.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.FINISHED.getCode()));
					}
					// 预对账完成状态
					else if (ReconciliationDef.State.PRE_FINISHED
							.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.PRE_FINISHED
										.getCode()));
					}
					// 作废中状态
					else if (ReconciliationDef.State.INVALIDING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALIDING.getCode()));
					}
					// 已作废状态
					else if (ReconciliationDef.State.INVALID.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALID.getCode()));
					}
				}
			});
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			if ("reconciliation_date".equalsIgnoreCase(sortKey)) {
				// 如果是对账日期排序，则按照对账日期倒序，对账日期相同按创建时间倒序排序。
				if ("DESC".equalsIgnoreCase(sortOrder)) {
					wrapper.last("order by GREATEST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '0001-01-01' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '0001-01-01')"
							+ "    )" + "DESC");
				} else {
					wrapper.last("order by LEAST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '9999-12-31' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '9999-12-31')"
							+ "  ) ASC");
				}
			} else {
				wrapper.last(
						"order by " + sortKey + " " + sortOrder + ", id DESC");
			}
		} else {
			wrapper.last("ORDER BY updated_time DESC");
		}
		Page<Reconciliation> paging = repository
				.selectPage(new Page<>(page, size), wrapper);
		List<ReconciliationVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public List<Reconciliation> findByContractId(String contractId) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Reconciliation::getContractId, contractId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Reconciliation> findByPurchaserIdAndState(Long customId,
			Integer state) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(customId), Reconciliation::getPurchaserId,
				customId)
				.eq(Objects.nonNull(state), Reconciliation::getState, state);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Reconciliation> findByContractId(String contractId,
			ReconciliationAssociaForm form, Integer type) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		this.filterDeleted(wrapper);
		wrapper.eq(StringUtils.isNotBlank(contractId),
				Reconciliation::getContractId, contractId);
		wrapper.eq(Objects.nonNull(type), Reconciliation::getType, type);
		wrapper.and(x -> x
				.in(Reconciliation::getInvoiceState,
						ReconciliationDef.InvoiceState.NOT_INVOICED.getCode(),
						ReconciliationDef.InvoiceState.INVOICING.getCode())
				.or().isNull(Reconciliation::getInvoiceState));
		wrapper.eq(Reconciliation::getState,
				ReconciliationDef.State.FINISHED.getCode());
		List<Reconciliation> reconciliations = repository.selectList(wrapper);
		if (Objects.isNull(form) || Objects.isNull(form.getBillId())) {
			reconciliations = reconciliations
					.stream().filter(reconciliation -> reconciliation
							.getUnbilledAmount().compareTo(BigDecimal.ZERO) > 0)
					.toList();
		} else {
			// 根据billId查询BillPayment对象
			BillPayment billPayment = billPaymentService
					.findOne(form.getBillId()).orElse(null);
			// 如果BillPayment对象不为空，则进行进一步处理
			if (Objects.nonNull(billPayment)) {
				// 将修改的对账单和开票金额生成map
				Map<String, BigDecimal> map = form.getReconciliationJsonInfo()
						.stream()
						.collect(Collectors.toMap(ReconciliationJsonInfo::getId,
								ReconciliationJsonInfo::getBillAmount));

				List<String> reconciliationIds = form
						.getReconciliationJsonInfo().stream()
						.map(ReconciliationJsonInfo::getId).toList();

				// 将该开票单的对账信息生成map
				Map<String, BigDecimal> collect = billPayment
						.getReconciliationIds().stream()
						.collect(Collectors.toMap(ReconciliationJsonInfo::getId,
								ReconciliationJsonInfo::getBillAmount));

				// 获取该开票单的对账单id
				List<String> list = billPayment.getReconciliationIds().stream()
						.map(ReconciliationJsonInfo::getId).toList();

				// 新增的对账单
				List<Reconciliation> createList = this
						.findByIds(reconciliationIds.stream()
								.filter(id -> !list.contains(id)).toList());
				// 删除的对账单
				List<Reconciliation> deleteList = this.findByIds(list.stream()
						.filter(id -> !reconciliationIds.contains(id))
						.toList());
				// 修改的对账单
				List<Reconciliation> updateList = this.findByIds(list.stream()
						.filter(reconciliationIds::contains).toList());

				// 修改未对账金额
				List<Reconciliation> finalReconciliations = reconciliations;
				createList.forEach(reconciliation -> finalReconciliations
						.forEach(reconciliation1 -> {
							/* 未开票金额 = 未开票金额 - 修改的对账单开票金额 */
							if (reconciliation.getId()
									.equals(reconciliation1.getId())) {
								if (Objects.nonNull(
										map.get(reconciliation.getId()))) {
									reconciliation1.setUnbilledAmount(
											reconciliation1.getUnbilledAmount()
													.subtract(map
															.get(reconciliation
																	.getId())));
								}
							}
						}));
				List<Reconciliation> finalReconciliations1 = reconciliations;
				deleteList.forEach(reconciliation -> finalReconciliations1
						.forEach(reconciliation1 -> {
							/*
							 * 如果数据库中的对账单未开票金额不为0 未开票金额 = 未开票金额 + 删除的对账单开票金额
							 */
							if (reconciliation.getId()
									.equals(reconciliation1.getId())) {
								if (Objects.nonNull(
										collect.get(reconciliation.getId()))) {
									reconciliation1.setUnbilledAmount(
											reconciliation1.getUnbilledAmount()
													.add(collect
															.get(reconciliation
																	.getId())));
								}
							}
						}));
				List<Reconciliation> finalReconciliations2 = reconciliations;
				updateList.forEach(reconciliation -> finalReconciliations2
						.forEach(reconciliation1 -> {
							/*
							 * 如果数据库中的对账单未开票金额不为0 未开票金额 = 未开票金额 +
							 * 该开票单中对应的对账单开票金额 - 修改的开票单金额
							 */
							if (reconciliation.getId()
									.equals(reconciliation1.getId())) {
								if (Objects.nonNull(
										map.get(reconciliation.getId()))) {
									reconciliation1.setUnbilledAmount(
											reconciliation1.getUnbilledAmount()
													.add(collect
															.get(reconciliation
																	.getId()))
													.subtract(map
															.get(reconciliation
																	.getId())));
								}
							}
						}));
			}
		}

		return reconciliations;
	}

	@Override
	public Optional<Integer> validateDisplay(String contractId,
			List<String> signReceiptIds) {
		Contract contract = contractService.findOne(contractId).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30151001));
		// 只有注册企业才有保证金转货款
		if (!(contractService
				.validateIsRecorded(contractId, contract.getContractType())
				.orElse(true))) {
			// 关联的合同为先货后款时
			if (ContractDef.SettleWay.GOODS_FIRST
					.match(contract.getSettleWay())) {
				List<SignReceipt> signReceiptList = signReceiptService
						.findByIds(signReceiptIds);
				// 用于存储所有出现的订单ID
				Set<String> orderIds = new HashSet<>();
				for (SignReceipt signReceipt : signReceiptList) {
					// 遍历relatedOrderIds，添加到Set中
					for (String orderId : signReceipt.getRelatedOrderIds()) {
						orderIds.add(orderId);
						// 如果Set大小超过1，说明有多个不同的订单ID，直接返回false
						if (orderIds.size() > 1) {
							return Optional.of(CommonDef.Symbol.NO.getCode());
						}
					}
				}
				// 如果只关联一个订单 则继续判断订单为已完成状态，订单中的发货单均为发货完成状态
				if (orderIds.size() == 1) {
					Iterator<String> iterator = orderIds.iterator();
					if (iterator.hasNext()) {
						// firstOrderId是orderIds的第一个值
						String firstOrderId = iterator.next();
						Order order = orderService.findOne(firstOrderId)
								.orElse(null);
						if (Objects.nonNull(order)) {
							// 订单状态为已完成且是否完成发货为是
							if ((order.getStatus() == 327683)
									&& (OrderDef.BusinessStatus.COMPLETED.match(
											order.getDeliveryStatus()))) {
								List<DeliverGoods> deliverGoodsList = deliverGoodsService
										.findByOrderIds(List.of(firstOrderId));
								// 判断订单关联的发货是否存在不为已完成的
								if (deliverGoodsList.stream().anyMatch(
										deliverGoods -> !DeliverGoodsDef.Status.DELIVER_COMPLETE
												.match(deliverGoods
														.getStatus()))) {
									return Optional
											.of(CommonDef.Symbol.NO.getCode());
								} else {
									// 存在订单关联的发货单没有关联签收单时
									if (deliverGoodsList.stream()
											.anyMatch(deliverGoods -> Objects
													.isNull(deliverGoods
															.getSignReceiptId()))) {
										return Optional.of(
												CommonDef.Symbol.NO.getCode());
									} else {
										// 根据发货信息查询出所有的签收单
										List<String> signReceiptIdList = deliverGoodsList
												.stream()
												.map(DeliverGoods::getSignReceiptId)
												.toList();
										List<SignReceipt> signReceipts = signReceiptService
												.findByIds(signReceiptIdList);
										// 判断签收单是否都为已完成状态 存在不为已完成状态的则不能显示
										if (signReceipts.stream().anyMatch(
												signReceipt -> !SignReceiptDef.Status.FINISHED
														.match(signReceipt
																.getStatus()))) {
											return Optional
													.of(CommonDef.Symbol.NO
															.getCode());
										} else {
											// 除去本次关联的签收单 判断其他签收单是否都已关联对账单
											List<SignReceipt> filteredList = signReceipts
													.stream()
													.filter(signReceipt -> !signReceiptIds
															.contains(
																	signReceipt
																			.getId()))
													.toList();
											if (filteredList.stream().anyMatch(
													signReceipt -> !OrderDef.BusinessStatus.NOT_STARTED
															.match(signReceipt
																	.getReconciliationStatus()))) {
												return Optional
														.of(CommonDef.Symbol.NO
																.getCode());
											} else {
												return Optional
														.of(CommonDef.Symbol.YES
																.getCode());
											}
										}
									}

								}
							}
						}
					}
				}
			}
		}
		return Optional.of(CommonDef.Symbol.NO.getCode());
	}

	@Override
	public Page<ReconciliationVo> pagingFindByProjectIdAndType(Integer page,
			Integer size, String sortKey, String sortOrder, String projectId,
			Integer type, LocalDateTime beginTime, LocalDateTime endTime,
			Long customerId, String param, String buyer, String seller,
			List<Integer> states) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		wrapper.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.eq(Objects.nonNull(customerId), Reconciliation::getPurchaserId,
				customerId);
		wrapper.eq(Reconciliation::getProjectId, projectId);
		wrapper.eq(ObjectUtils.isNotEmpty(type), Reconciliation::getType, type);
		// 检查 beginTime 和 endTime 是否为空
		if (Objects.nonNull(beginTime) && Objects.nonNull(endTime)) {
			// 如果都不为空，则添加条件
			wrapper.and(w -> w
					.or(y -> y
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z
									.ge(Reconciliation::getReconciliationDate,
											beginTime)
									.le(Reconciliation::getReconciliationDate,
											endTime)))
					.or(y -> y
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z.ge(
									Reconciliation::getPreReconciliationDate,
									beginTime)
									.le(Reconciliation::getPreReconciliationDate,
											endTime))));
		}
		// 合同名称或对账编号
		if (Objects.nonNull(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(x -> x.like(Reconciliation::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Reconciliation::getContractId, contractIds));
		}
		// 采购方名称
		wrapper.apply(StringUtils.isNotBlank(buyer),
				" (JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
				buyer);
		// 销售方名称
		wrapper.apply(StringUtils.isNotBlank(seller),
				" (JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
				seller);

		// 待签署状态集合
		List<Integer> toBeSignedList;
		// 销售对账
		if (ReconciliationDef.Type.SELL.match(type)) {
			// 卖方未签署
			toBeSignedList = List.of(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
					BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
		}
		// 采购对账
		else {
			// 买方未签署
			toBeSignedList = List.of(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
					BusinessContractDef.CommonSignState.SUPPLY_SIGNED
							.getCode());
		}
		// 没有传入状态时
		if (CollectionUtils.isEmpty(states)) {
			// 除了草稿和已驳回查询发起方是自己的 其他根据状态查询
			wrapper.and(x -> x
					.in(Reconciliation::getState,
							ReconciliationDef.State.SIGNING.getCode(),
							ReconciliationDef.State.CONFIRMING.getCode(),
							ReconciliationDef.State.FINISHED.getCode(),
							ReconciliationDef.State.INVALIDING.getCode(),
							ReconciliationDef.State.INVALID.getCode(),
							ReconciliationDef.State.PRE_FINISHED.getCode())
					.or(y -> y
							.eq(Reconciliation::getInitiator,
									CommonDef.AccountSource.INNER.getCode())
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode(),
									ReconciliationDef.State.TO_BE_INITIATE
											.getCode()))
					.or(y -> y
							.eq(Reconciliation::getPreInitiator,
									CommonDef.AccountSource.INNER.getCode())
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode(),
									ReconciliationDef.State.TO_BE_INITIATE
											.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿状态
					if (ReconciliationDef.State.DRAFT.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
					}
					// 已驳回状态
					else if (ReconciliationDef.State.REJECTED.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
					}
					// 待确认状态
					else if (ReconciliationDef.State.WAIT_CONFIRM
							.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 确认中状态
					else if (ReconciliationDef.State.CONFIRMING.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 签署中状态 签署中状态 并且销售方已签署
					else if (ReconciliationDef.State.SIGNING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 待签署状态 签署中状态 并且买方已签署或者双方未签署
					else if (ReconciliationDef.State.TO_BE_SIGNED
							.match(state)) {
						x.or(y -> y
								.in(Reconciliation::getSignStatus,
										toBeSignedList)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y
								.in(Reconciliation::getPreSignStatus,
										toBeSignedList)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 已完成状态
					else if (ReconciliationDef.State.FINISHED.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.FINISHED.getCode()));
					}
					// 预对账完成状态
					else if (ReconciliationDef.State.PRE_FINISHED
							.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.PRE_FINISHED
										.getCode()));
					}
					// 待发起
					else if (ReconciliationDef.State.TO_BE_INITIATE
							.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.TO_BE_INITIATE
										.getCode()));
					}
					// 作废中状态
					else if (ReconciliationDef.State.INVALIDING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALIDING.getCode()));
					}
					// 已作废状态
					else if (ReconciliationDef.State.INVALID.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALID.getCode()));
					}
				}
			});

		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			if ("reconciliation_date".equalsIgnoreCase(sortKey)) {
				// 如果是对账日期排序，则按照对账日期倒序，对账日期相同按创建时间倒序排序。
				if ("DESC".equalsIgnoreCase(sortOrder)) {
					wrapper.last("order by GREATEST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '0001-01-01' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '0001-01-01')"
							+ "    )" + "DESC");
				} else {
					wrapper.last("order by LEAST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '9999-12-31' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '9999-12-31')"
							+ "  ) ASC");
				}
			} else {
				wrapper.last(
						"order by " + sortKey + " " + sortOrder + ", id DESC");
			}
		} else {
			// 默认按照对账时间倒叙
			wrapper.orderByDesc(Reconciliation::getUpdatedTime);
		}
		Page<Reconciliation> paging = repository
				.selectPage(new Page<>(page, size), wrapper);
		List<ReconciliationVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Optional<BigDecimal> totalFindByProjectIdAndType(String projectId,
			Integer type, LocalDateTime beginTime, LocalDateTime endTime,
			Long customerId, String param, String buyer, String seller,
			List<Integer> states) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		wrapper.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.eq(Objects.nonNull(customerId), Reconciliation::getPurchaserId,
				customerId);
		wrapper.eq(Reconciliation::getProjectId, projectId);
		wrapper.eq(ObjectUtils.isNotEmpty(type), Reconciliation::getType, type);
		// 检查 beginTime 和 endTime 是否为空
		if (Objects.nonNull(beginTime) && Objects.nonNull(endTime)) {
			// 如果都不为空，则添加条件
			wrapper.and(w -> w
					.or(y -> y
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z
									.ge(Reconciliation::getReconciliationDate,
											beginTime)
									.le(Reconciliation::getReconciliationDate,
											endTime)))
					.or(y -> y
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z.ge(
									Reconciliation::getPreReconciliationDate,
									beginTime)
									.le(Reconciliation::getPreReconciliationDate,
											endTime))));
		}
		// 合同名称或对账编号
		if (Objects.nonNull(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(x -> x.like(Reconciliation::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Reconciliation::getContractId, contractIds));
		}
		// 采购方名称
		wrapper.apply(StringUtils.isNotBlank(buyer),
				" (JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
				buyer);
		// 销售方名称
		wrapper.apply(StringUtils.isNotBlank(seller),
				" (JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
				seller);
		// 待签署状态集合
		List<Integer> toBeSignedList;
		// 销售对账
		if (ContractDef.Type.SELL.match(type)) {
			// 卖方未签署
			toBeSignedList = List.of(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
					BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
		}
		// 采购对账
		else {
			// 买方未签署
			toBeSignedList = List.of(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
					BusinessContractDef.CommonSignState.SUPPLY_SIGNED
							.getCode());
		}
		// 没有传入状态时
		if (CollectionUtils.isEmpty(states)) {
			// 除了草稿和已驳回查询发起方是自己的 其他根据状态查询
			wrapper.and(x -> x
					.in(Reconciliation::getState,
							ReconciliationDef.State.SIGNING.getCode(),
							ReconciliationDef.State.CONFIRMING.getCode(),
							ReconciliationDef.State.FINISHED.getCode(),
							ReconciliationDef.State.INVALIDING.getCode(),
							ReconciliationDef.State.INVALID.getCode(),
							ReconciliationDef.State.PRE_FINISHED.getCode())
					.or(y -> y
							.eq(Reconciliation::getInitiator,
									CommonDef.AccountSource.INNER.getCode())
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode(),
									ReconciliationDef.State.TO_BE_INITIATE
											.getCode()))
					.or(y -> y
							.eq(Reconciliation::getPreInitiator,
									CommonDef.AccountSource.INNER.getCode())
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode(),
									ReconciliationDef.State.TO_BE_INITIATE
											.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿状态
					if (ReconciliationDef.State.DRAFT.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.DRAFT
												.getCode()));
					}
					// 已驳回状态
					else if (ReconciliationDef.State.REJECTED.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.REJECTED
												.getCode()));

					}
					// 待确认状态
					else if (ReconciliationDef.State.WAIT_CONFIRM
							.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
					}
					// 确认中状态
					else if (ReconciliationDef.State.CONFIRMING.match(state)) {
						x.or(y -> y
								.eq(Reconciliation::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));
						x.or(y -> y
								.eq(Reconciliation::getPreInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.CONFIRMING
												.getCode()));

					}
					// 签署中状态 签署中状态 并且销售方已签署
					else if (ReconciliationDef.State.SIGNING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y.eq(Reconciliation::getPreSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 待签署状态 签署中状态 并且买方已签署或者双方未签署
					else if (ReconciliationDef.State.TO_BE_SIGNED
							.match(state)) {
						x.or(y -> y
								.in(Reconciliation::getSignStatus,
										toBeSignedList)
								.eq(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
						x.or(y -> y
								.in(Reconciliation::getPreSignStatus,
										toBeSignedList)
								.ne(Reconciliation::getIsConductReconciliation,
										CommonDef.Symbol.YES.getCode())
								.eq(Reconciliation::getState,
										ReconciliationDef.State.SIGNING
												.getCode()));
					}
					// 已完成状态
					else if (ReconciliationDef.State.FINISHED.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.FINISHED.getCode()));
					}
					// 预对账完成状态
					else if (ReconciliationDef.State.PRE_FINISHED
							.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.PRE_FINISHED
										.getCode()));
					}
					// 待发起
					else if (ReconciliationDef.State.TO_BE_INITIATE
							.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.TO_BE_INITIATE
										.getCode()));
					}
					// 作废中状态
					else if (ReconciliationDef.State.INVALIDING.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALIDING.getCode()));
					}
					// 已作废状态
					else if (ReconciliationDef.State.INVALID.match(state)) {
						x.or(y -> y.eq(Reconciliation::getState,
								ReconciliationDef.State.INVALID.getCode()));
					}
				}
			});
		}
		// 根据查询条件查询出来的数据
		List<Reconciliation> reconciliations = repository.selectList(wrapper);
		// 最终需要计算对账金额的对账列表
		List<Reconciliation> finalReconciliations;
		// 过滤出状态为已完成的数据
		finalReconciliations = reconciliations.stream().filter(
				e -> ReconciliationDef.State.FINISHED.match(e.getState()))
				.toList();
		// 最终的对账列表计算对账金额之和
		BigDecimal reconciliationsNumber = finalReconciliations.stream()
				.map(Reconciliation::getReconciliationAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		return Optional.of(reconciliationsNumber);
	}

	@Override
	public Page<ReconciliationVo> pagingFindByOrderIdAndType(Integer page,
			Integer size, String sortKey, String sortOrder, String orderId,
			Integer type, LocalDateTime beginTime, LocalDateTime endTime,
			Long customerId) {
		// 签收直接关联订单，直接通过订单找签收单
		List<SignReceipt> signReceiptList = signReceiptService
				.findByOrderId(orderId);
		if (CollectionUtils.isNotEmpty(signReceiptList)) {
			return this.getReconciliationPage(page, size, signReceiptList,
					sortKey, sortOrder, orderId, type, beginTime, endTime,
					customerId);
		}
		return Page.of(page, size, 0);
	}

	/**
	 * 分页-根据签收单id或者订单id 和类型查询对账列表
	 *
	 * @return
	 */
	private Page<ReconciliationVo> getReconciliationPage(Integer page,
			Integer size, List<SignReceipt> signReceiptList, String sortKey,
			String sortOrder, String orderId, Integer type,
			LocalDateTime beginTime, LocalDateTime endTime, Long customerId) {
		LambdaQueryWrapper<Reconciliation> queryWrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		queryWrapper.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode());
		// 检查 beginTime 和 endTime 是否为空
		if (Objects.nonNull(beginTime) && Objects.nonNull(endTime)) {
			// 如果都不为空，则添加条件
			queryWrapper.and(w -> w
					.or(y -> y
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z
									.ge(Reconciliation::getReconciliationDate,
											beginTime)
									.le(Reconciliation::getReconciliationDate,
											endTime)))
					.or(y -> y
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z.ge(
									Reconciliation::getPreReconciliationDate,
									beginTime)
									.le(Reconciliation::getPreReconciliationDate,
											endTime))));
		}
		if (Objects.nonNull(customerId)) {
			// 除了草稿和已驳回,待发起 查询发起方是自己的 其他根据状态查询
			queryWrapper.and(x -> x
					.in(Reconciliation::getState,
							ReconciliationDef.State.SIGNING.getCode(),
							ReconciliationDef.State.CONFIRMING.getCode(),
							ReconciliationDef.State.FINISHED.getCode(),
							ReconciliationDef.State.INVALIDING.getCode(),
							ReconciliationDef.State.INVALID.getCode(),
							ReconciliationDef.State.PRE_FINISHED.getCode())
					.or(y -> y
							.eq(Reconciliation::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode())
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode()))
					.or(y -> y
							.eq(Reconciliation::getPreInitiator,
									CommonDef.AccountSource.CUSTOM.getCode())
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED
											.getCode())));
		} else {
			queryWrapper.and(x -> x
					.in(Reconciliation::getState,
							ReconciliationDef.State.SIGNING.getCode(),
							ReconciliationDef.State.CONFIRMING.getCode(),
							ReconciliationDef.State.FINISHED.getCode(),
							ReconciliationDef.State.INVALIDING.getCode(),
							ReconciliationDef.State.INVALID.getCode(),
							ReconciliationDef.State.PRE_FINISHED.getCode())
					.or(y -> y
							.eq(Reconciliation::getInitiator,
									CommonDef.AccountSource.INNER.getCode())
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode(),
									ReconciliationDef.State.TO_BE_INITIATE
											.getCode()))
					.or(y -> y
							.eq(Reconciliation::getPreInitiator,
									CommonDef.AccountSource.INNER.getCode())
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.in(Reconciliation::getState,
									ReconciliationDef.State.DRAFT.getCode(),
									ReconciliationDef.State.REJECTED.getCode(),
									ReconciliationDef.State.TO_BE_INITIATE
											.getCode())));
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			if ("reconciliation_date".equalsIgnoreCase(sortKey)) {
				// 如果是对账日期排序，则按照对账日期倒序，对账日期相同按创建时间倒序排序。
				if ("DESC".equalsIgnoreCase(sortOrder)) {
					queryWrapper.last("order by GREATEST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '0001-01-01' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '0001-01-01')"
							+ "    )" + "DESC");
				} else {
					queryWrapper.last("order by LEAST("
							+ "    CASE WHEN is_conduct_reconciliation = 0 THEN '9999-12-31' ELSE reconciliation_date END,"
							+ "    COALESCE(pre_reconciliation_date, '9999-12-31')"
							+ "  ) ASC");
				}
			} else {
				queryWrapper.last(
						"order by " + sortKey + " " + sortOrder + ", id DESC");
			}
		} else {
			// 默认按照对账时间倒叙
			queryWrapper.orderByDesc(Reconciliation::getUpdatedTime);
		}
		// PC端筛选条件
		if (Objects.nonNull(customerId)) {
			if (ReconciliationDef.Type.SELL.match(type)) {
				// 销售对账 查询采购方是客户的数据
				queryWrapper.eq(Reconciliation::getPurchaserId, customerId);
			} else {
				// 采购对账 查询销售方是客户的数据
				queryWrapper.eq(Reconciliation::getSellerId, customerId);
			}
		}
		// 对账单关联签收单
		if (CollectionUtils.isNotEmpty(signReceiptList)) {
			List<String> signReceiptIds = signReceiptList.stream()
					.map(SignReceipt::getId).toList();
			// 动态添加 JSON_CONTAINS 条件
			if (CollectionUtils.isNotEmpty(signReceiptIds)) {
				if (signReceiptIds.size() > 1) {
					String orderIdConditions = signReceiptIds.stream()
							.map(signReceiptId -> String.format(
									"JSON_CONTAINS(receipt_ids, JSON_QUOTE('%s'))",
									signReceiptId))
							.collect(Collectors.joining(" OR "));
					queryWrapper.apply(orderIdConditions);
				} else {
					queryWrapper.and(wrapper -> wrapper.apply(
							"JSON_CONTAINS(receipt_ids, JSON_QUOTE({0}))",
							signReceiptIds.get(0)));
				}
			}
		}

		Page<Reconciliation> pageResult = repository
				.selectPage(new Page<>(page, size), queryWrapper);
		List<ReconciliationVo> reconciliationVoList = this
				.packVos(pageResult.getRecords(), orderId, type);
		return PageUtil.getRecordsInfoPage(pageResult, reconciliationVoList);
	}

	@Override
	public Optional<BigDecimal> totalFindByOrderIdAndType(String orderId,
			Integer type, LocalDateTime beginTime, LocalDateTime endTime,
			Long customerId) {
		// 找到订单关联的签收单
		List<SignReceipt> signReceiptList = signReceiptService
				.findByOrderId(orderId);
		// 初始化已对账金额
		BigDecimal reconciliationsNumber = BigDecimal.ZERO;
		List<ReconciliationVo> reconciliationVos = this.getReconciliationList(
				signReceiptList, orderId, type, beginTime, endTime, customerId);
		if (CollectionUtils.isNotEmpty(reconciliationVos)) {
			// 将对账列表的订单对账金额相加
			reconciliationsNumber = reconciliationVos.stream()
					.map(ReconciliationVo::getOrderReconAmount)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		return Optional.of(reconciliationsNumber);
	}

	/**
	 * 分页-根据签收单id和类型查询对账列表
	 *
	 * @return
	 */
	private List<ReconciliationVo> getReconciliationList(
			List<SignReceipt> signReceiptList, String orderId, Integer type,
			LocalDateTime beginTime, LocalDateTime endTime, Long customerId) {
		LambdaQueryWrapper<Reconciliation> queryWrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		queryWrapper.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode());
		// 检查 beginTime 和 endTime 是否为空
		if (Objects.nonNull(beginTime) && Objects.nonNull(endTime)) {
			// 如果都不为空，则添加条件
			queryWrapper.and(w -> w
					.or(y -> y
							.eq(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z
									.ge(Reconciliation::getReconciliationDate,
											beginTime)
									.le(Reconciliation::getReconciliationDate,
											endTime)))
					.or(y -> y
							.ne(Reconciliation::getIsConductReconciliation,
									CommonDef.Symbol.YES.getCode())
							.and(z -> z.ge(
									Reconciliation::getPreReconciliationDate,
									beginTime)
									.le(Reconciliation::getPreReconciliationDate,
											endTime))));
		}
		// PC端筛选条件
		if (Objects.nonNull(customerId)) {
			if (ReconciliationDef.Type.SELL.match(type)) {
				queryWrapper.eq(Reconciliation::getPurchaserId, customerId);
			} else {
				queryWrapper.eq(Reconciliation::getSellerId, customerId);
			}
		}
		// 类型
		queryWrapper.eq(Objects.nonNull(type), Reconciliation::getType, type);
		// 对账单是关联签收单
		if (CollectionUtils.isNotEmpty(signReceiptList)) {
			List<String> signReceiptIds = signReceiptList.stream()
					.map(SignReceipt::getId).toList();
			// 动态添加 JSON_CONTAINS 条件
			if (CollectionUtils.isNotEmpty(signReceiptIds)) {
				if (signReceiptIds.size() > 1) {
					String signReceiptIdConditions = signReceiptIds.stream()
							.map(signReceiptId -> String.format(
									"JSON_CONTAINS(receipt_ids, JSON_QUOTE('%s'))",
									signReceiptId))
							.collect(Collectors.joining(" OR "));
					queryWrapper.apply(signReceiptIdConditions);
				} else {
					queryWrapper.and(wrapper -> wrapper.apply(
							"JSON_CONTAINS(receipt_ids, JSON_QUOTE({0}))",
							signReceiptIds.get(0)));
				}
			}
		}

		// 查询对账状态为已完成的数据
		queryWrapper.eq(Reconciliation::getState,
				ReconciliationDef.State.FINISHED.getCode());
		List<Reconciliation> reconciliations = repository
				.selectList(queryWrapper);
		return this.packVos(reconciliations, orderId, type);
	}

	@Override
	public Page<Reconciliation> selectorAssociated(Integer page, Integer size,
			String projectId, String contractId, String reconciliationId) {
		if (StringUtils.isBlank(contractId)) {
			return new Page<>();
		}
		Contract contract = contractService.findOne(contractId).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30151001));
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(projectId), Reconciliation::getProjectId,
				projectId)
				.eq(StringUtils.isNotBlank(contractId),
						Reconciliation::getContractId, contractId)
				.like(StringUtils.isNotBlank(reconciliationId),
						Reconciliation::getId, reconciliationId);
		if (ContractDef.Type.SELL.match(contract.getContractType())) {
			// 已完成状态
			wrapper.eq(Reconciliation::getState,
					ReconciliationDef.State.FINISHED.getCode());
		}
		// 默认按更新时间倒序
		wrapper.orderByDesc(Reconciliation::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Optional<ReconciliationVo> findVoById(String id) {
		return this.findOne(id).map(this::packVo);
	}

	@Override
	public Optional<ReconciliationVo> findBuyVoById(String id) {
		return this.findOne(id).map(this::packBuyVo);
	}

	@Override
	public List<Reconciliation> findByOrderIds(List<String> orderIds,
			boolean filterReconciliation) {
		LambdaQueryWrapper<Reconciliation> queryWrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		queryWrapper.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode());
		// 是否过滤出已完成对账的数据
		queryWrapper.eq(filterReconciliation, Reconciliation::getState,
				ReconciliationDef.State.FINISHED.getCode());
		// 动态添加 JSON_CONTAINS 条件
		if (CollectionUtils.isNotEmpty(orderIds)) {
			if (orderIds.size() > 1) {
				String orderIdConditions = orderIds.stream()
						.map(signReceiptId -> String.format(
								"JSON_CONTAINS(order_ids, JSON_QUOTE('%s'))",
								signReceiptId))
						.collect(Collectors.joining(" OR "));
				queryWrapper.apply(orderIdConditions);
			} else {
				queryWrapper.and(wrapper -> wrapper.apply(
						"JSON_CONTAINS(order_ids, JSON_QUOTE({0}))",
						orderIds.get(0)));
			}
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<BigDecimal> findRecAmount(String contractId,
			List<Integer> states, Integer type) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.query(Reconciliation.class)
				.select("SUM(reconciliation_amount) reconciliationAmount")
				.lambda()
				.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Reconciliation::getContractId, contractId)
				.eq(Objects.nonNull(type), Reconciliation::getType, type)
				.in(CollectionUtils.isNotEmpty(states),
						Reconciliation::getState, states);
		Reconciliation reconciliation = repository.selectOne(wrapper);
		return Objects.isNull(reconciliation) ? Optional.of(BigDecimal.ZERO)
				: Optional.of(reconciliation.getReconciliationAmount());
	}

	@Override
	public List<CustomerTransactionRankTopVo> findTopVosByTypeAndState(
			Integer type, Integer state, LocalDateTime beginTime,
			LocalDateTime endTime) {
		List<CustomerTransactionRankTopVo> customerTransactionRankTopVoList;
		if (Objects.isNull(beginTime) && Objects.isNull(endTime)) {
			if (ReconciliationDef.Type.SELL.match(type)) {
				customerTransactionRankTopVoList = repository
						.findTopVosBySaleAndStateWithNoTime();
			} else {
				customerTransactionRankTopVoList = repository
						.findTopVosByBuyAndStateWithNoTime();
			}
		} else {
			customerTransactionRankTopVoList = repository
					.findTopVosByTypeAndState(type, state, beginTime, endTime);
		}
		if (CollectionUtils.isNotEmpty(customerTransactionRankTopVoList)) {
			for (CustomerTransactionRankTopVo customerTransactionRankTopVo : customerTransactionRankTopVoList) {
				// 如果客户只做了系统里的组织机构认证，客户名称取组织机构认证表的
				if (ReconciliationDef.Type.SELL.getCode().equals(type)
						&& StringUtils.isBlank(
								customerTransactionRankTopVo.getName())) {
					institutionApplyService.findByCustomerIdAndStateCancel(
							customerTransactionRankTopVo.getCustomerId(),
							InstitutionApplyDef.State.APPROVED.getCode(),
							CommonDef.Symbol.NO.getCode(),
							InstitutionApplyDef.Source.PURCHASE_CUSTOMER
									.getCode())
							.ifPresent(
									institutionApply -> customerTransactionRankTopVo
											.setName(institutionApply
													.getInstitutionName()));
				}
				BigDecimal reconciliationAmount = customerTransactionRankTopVo
						.getReconciliationAmount();
				// 转成万元
				BigDecimal amount = reconciliationAmount
						.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP);
				customerTransactionRankTopVo.setReconciliationAmount(amount);
			}
		}
		return customerTransactionRankTopVoList;
	}

	@Override
	public List<Reconciliation> findByProjectId(String projectId) {
		LambdaQueryWrapper<Reconciliation> queryWrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		queryWrapper.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Reconciliation::getProjectId, projectId);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Reconciliation> findProjectIdByState(String projectId,
			Integer type, Integer state, LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(type), Reconciliation::getType, type);
		wrapper.eq(Objects.nonNull(projectId), Reconciliation::getProjectId,
				projectId);
		wrapper.eq(Objects.nonNull(state), Reconciliation::getState, state);
		wrapper.ge(Objects.nonNull(beginTime),
				Reconciliation::getReconciliationDate, beginTime);
		wrapper.le(Objects.nonNull(endTime),
				Reconciliation::getReconciliationDate, endTime);
		return repository.selectList(wrapper);
	}

	@Override
	public List<GoodsStatisticsVo> findGoodsStatisticsVoByProjectStatesAndReconciliationType(
			List<Integer> states, Integer reconciliationType,
			LocalDateTime beginTime, LocalDateTime endTime) {
		return repository
				.findGoodsStatisticsVoByProjectStatesAndReconciliationType(
						states, reconciliationType, beginTime, endTime);
	}

	@Override
	public List<GoodsStatisticsVo> findGoodsStatisticsVoByProjectStatesAndReconciliationTypeWithNoTime(
			List<Integer> states, Integer reconciliationType) {
		return repository
				.findGoodsStatisticsVoByProjectStatesAndReconciliationTypeWithNoTime(
						states, reconciliationType);
	}

	@Override
	public List<GoodsStatisticsVo> findByReconciliationTypeAndGoodsIdsAndProjectStates(
			Integer reconciliationType, List<Long> goodsIdList,
			List<Integer> projectStates, LocalDateTime beginTime,
			LocalDateTime endTime) {
		return repository.findByReconciliationTypeAndGoodsIdsAndProjectStates(
				reconciliationType, goodsIdList, projectStates, beginTime,
				endTime);
	}

	@Override
	public List<GoodsStatisticsVo> findByReconciliationTypeAndGoodsIdsAndProjectStatesWithNoTime(
			Integer reconciliationType, List<Long> goodsIdList,
			List<Integer> projectStates) {
		return repository
				.findByReconciliationTypeAndGoodsIdsAndProjectStatesWithNoTime(
						reconciliationType, goodsIdList, projectStates);
	}

	@Override
	public List<Reconciliation> findBySignReceiptId(String signReceiptId) {
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		this.filterDeleted(wrapper);
		wrapper.and(i -> i.apply(Objects.nonNull(signReceiptId),
				"JSON_CONTAINS(receipt_ids, JSON_QUOTE({0}))", signReceiptId));
		return repository.selectList(wrapper);
	}

	@Override
	public List<Reconciliation> findUnfinished(String projectId) {
		LambdaQueryWrapper<Reconciliation> queryWrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		queryWrapper.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Reconciliation::getProjectId, projectId);
		queryWrapper.ne(Reconciliation::getState,
				ReconciliationDef.State.FINISHED.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Reconciliation> findByProjectIdsAndType(List<String> projectIds,
			Integer type) {
		LambdaQueryWrapper<Reconciliation> queryWrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		queryWrapper.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.in(CollectionUtils.isNotEmpty(projectIds),
				Reconciliation::getProjectId, projectIds);
		queryWrapper.eq(Objects.nonNull(type), Reconciliation::getType, type);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Reconciliation> findByProjectIdsAndType(List<String> projectIds,
			Integer type, LocalDateTime beginTime, LocalDateTime endTime) {
		LambdaQueryWrapper<Reconciliation> queryWrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		queryWrapper.eq(Reconciliation::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.in(CollectionUtils.isNotEmpty(projectIds),
				Reconciliation::getProjectId, projectIds);
		queryWrapper.eq(Objects.nonNull(type), Reconciliation::getType, type);
		queryWrapper.ge(Objects.nonNull(beginTime),
				Reconciliation::getReconciliationDate, beginTime);
		queryWrapper.le(Objects.nonNull(endTime),
				Reconciliation::getReconciliationDate, endTime);
		return repository.selectList(queryWrapper);
	}

	@Override
	@FileId
	@Transactional(rollbackFor = Exception.class)
	public Optional<Reconciliation> create(Reconciliation reconciliation,
			List<DeliverGoodsVo> deliverGoodsVoList) {
		Project project = projectService.findOne(reconciliation.getProjectId())
				.orElse(null);
		if (Objects.nonNull(project)) {
			reconciliation.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(
					redisClient, project.getCode(),
					RedisKeys.Cache.RECONCILIATION_CODE_GENERATOR,
					ReconciliationDef.Type.SELL.getStr()
							+ AutoCodeDef.BusinessRuleCode.RECONCILIATION_SUFFIX
									.getCode(),
					4, AutoCodeDef.DATE_TYPE.yy));
			List<DeliverGoods> deliverGoodsList = new ArrayList<>();
			List<TransportOrderShip> transportOrderShipList = new ArrayList<>();
			List<TransportOrderVehicle> transportOrderVehicleList = new ArrayList<>();
			List<TransportOrderRailway> transportOrderRailwayList = new ArrayList<>();
			for (DeliverGoodsVo deliverGoodsVo : deliverGoodsVoList) {
				deliverGoodsList.add(deliverGoodsVo.getDeliverGoods());
				if (CollectionUtils
						.isNotEmpty(deliverGoodsVo.getTransportOrderShips())) {
					transportOrderShipList
							.addAll(deliverGoodsVo.getTransportOrderShips());
				}
				if (CollectionUtils.isNotEmpty(
						deliverGoodsVo.getTransportOrderVehicles())) {
					transportOrderVehicleList
							.addAll(deliverGoodsVo.getTransportOrderVehicles());
				}
				if (CollectionUtils.isNotEmpty(
						deliverGoodsVo.getTransportOrderRailways())) {
					transportOrderRailwayList
							.addAll(deliverGoodsVo.getTransportOrderRailways());
				}
			}
			// 设置给关联订单和签收单的对账状态
			Integer stateCode = getStateCode(reconciliation);
			// 发货单关联的订单 对账状态改为对账中/预对账
			List<String> orderIds = deliverGoodsList.stream()
					.map(DeliverGoods::getOrderId).filter(Objects::nonNull)
					.distinct().toList();

			// 发货单关联的签收单 对账状态改为对账中/预对账
			List<String> signReceiptIds = deliverGoodsList.stream()
					.map(DeliverGoods::getSignReceiptId)
					.filter(Objects::nonNull).distinct().toList();
			List<SignReceipt> signReceiptList = signReceiptService
					.findByIds(signReceiptIds);
			for (SignReceipt e : signReceiptList) {
				e.setReconciliationStatus(stateCode);
			}
			signReceiptService.batchUpdate(signReceiptList);
			if (CollectionUtils.isNotEmpty(orderIds)) {
				reconciliation.setOrderIds(new ArrayString(
						orderIds.stream().filter(Objects::nonNull).toList()));
			}
			if (CollectionUtils.isNotEmpty(signReceiptIds)) {
				reconciliation.setReceiptIds(new ArrayString(signReceiptIds
						.stream().filter(Objects::nonNull).toList()));
			}
			// 更新发货单信息
			deliverGoodsService.batchUpdate(deliverGoodsList);
			if (CollectionUtils.isNotEmpty(transportOrderShipList)) {
				// 更新船运单信息
				transportOrderShipService.batchUpdate(transportOrderShipList);
			}
			if (CollectionUtils.isNotEmpty(transportOrderVehicleList)) {
				// 更新汽运单信息
				transportOrderVehicleService
						.batchUpdate(transportOrderVehicleList);
			}
			if (CollectionUtils.isNotEmpty(transportOrderRailwayList)) {
				// 更新铁路单信息
				transportOrderRailwayService
						.batchUpdate(transportOrderRailwayList);
			}
		}

		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			if (ReconciliationDef.State.CONFIRMING
					.match(reconciliation.getState())) {
				if (CommonDef.AccountSource.INNER
						.match(reconciliation.getInitiator())) {
					this.sendNotice(reconciliation,
							wxSubscriptionProperties
									.getReconciliationUnConfirmCode(),
							MessageFormat.format(
									UserMessageConstants.RECONCILIATION_UNCONFIRMED_TEMPLATE,
									reconciliation.getId()),
							ReconciliationDef.Type.SELL.getCode());
				} else {
					SpringUtil.getBean(ReconciliationService.class)
							.notice(reconciliation, 1);
				}
			}
		}

		super.create(reconciliation);
		return Optional.of(reconciliation);
	}

	@Override
	@FileId
	public Optional<Reconciliation> createBuy(Reconciliation reconciliation,
			List<DeliverGoods> deliverGoodsList,
			List<DeliverGoodsVo> deliverGoodsVoList) {
		Project project = projectService.findOne(reconciliation.getProjectId())
				.orElse(null);
		if (Objects.nonNull(project)) {
			reconciliation.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(
					redisClient, project.getCode(),
					RedisKeys.Cache.RECONCILIATION_CODE_GENERATOR,
					ReconciliationDef.Type.BUY.getStr()
							+ AutoCodeDef.BusinessRuleCode.RECONCILIATION_SUFFIX
									.getCode(),
					4, AutoCodeDef.DATE_TYPE.yy));

			List<String> deliverGoodsIds = new ArrayList<>();
			List<String> signReceiptIds = new ArrayList<>();
			List<String> orderIds = new ArrayList<>();
			// 设置给关联订单和签收单的对账状态
			Integer stateCode = getStateCode(reconciliation);
			if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
				// 提货单id
				for (DeliverGoods deliverGoods : deliverGoodsList) {
					deliverGoodsIds.add(deliverGoods.getId());
					orderIds.add(deliverGoods.getOrderId());
				}
			}
			List<DeliverGoods> goodsList = this
					.getDeliverGoodsList(deliverGoodsVoList);
			// 提货单id
			for (DeliverGoods deliverGoods : goodsList) {
				deliverGoodsIds.add(deliverGoods.getId());
				orderIds.add(deliverGoods.getOrderId());
			}
			// 根据提货单单信息查找签收单信息
			List<SignReceipt> signReceiptList = signReceiptService
					.findByDeliverGoodsIds(deliverGoodsIds);
			for (SignReceipt e : signReceiptList) {
				e.setReconciliationStatus(stateCode);
				signReceiptIds.add(e.getId());
			}
			// 批量更新签收单的对账状态
			signReceiptService.batchUpdate(signReceiptList);
			if (CollectionUtils.isNotEmpty(signReceiptIds)) {
				reconciliation.setReceiptIds(new ArrayString(signReceiptIds
						.stream().filter(Objects::nonNull).toList()));
			}
			if (CollectionUtils.isNotEmpty(orderIds)) {
				reconciliation.setOrderIds(new ArrayString(
						orderIds.stream().filter(Objects::nonNull).toList()));
			}
		}
		if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
			// 更新提货单信息
			deliverGoodsService.batchUpdate(deliverGoodsList);
		}
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			if (ReconciliationDef.State.CONFIRMING
					.match(reconciliation.getState())) {
				if (CommonDef.AccountSource.INNER
						.match(reconciliation.getInitiator())) {
					this.sendNotice(reconciliation,
							wxSubscriptionProperties
									.getReconciliationUnConfirmCode(),
							MessageFormat.format(
									UserMessageConstants.RECONCILIATION_UNCONFIRMED_TEMPLATE,
									reconciliation.getId()),
							ReconciliationDef.Type.BUY.getCode());
				} else {
					SpringUtil.getBean(ReconciliationService.class)
							.notice(reconciliation, 1);
				}
			}
		}
		super.create(reconciliation);
		return Optional.of(reconciliation);
	}

	@Transactional(rollbackFor = Exception.class)
	@FileId(type = 2)
	@Override
	public Optional<Reconciliation> update(Reconciliation resource,
			List<DeliverGoodsVo> deliverGoodsVoList, Integer saveType,
			Integer origin) {
		// 设置给关联订单和签收单的对账状态
		Integer stateCode = getStateCode(resource);
		// 发货单列表
		List<DeliverGoods> deliverGoodsList = new ArrayList<>();
		// 船运单列表
		List<TransportOrderShip> transportOrderShipList = new ArrayList<>();
		// 汽运单列表
		List<TransportOrderVehicle> transportOrderVehicleList = new ArrayList<>();
		// 铁路单列表
		List<TransportOrderRailway> transportOrderRailwayList = new ArrayList<>();
		for (DeliverGoodsVo deliverGoodsVo : deliverGoodsVoList) {
			deliverGoodsList.add(deliverGoodsVo.getDeliverGoods());
			if (CollectionUtils
					.isNotEmpty(deliverGoodsVo.getTransportOrderShips())) {
				transportOrderShipList
						.addAll(deliverGoodsVo.getTransportOrderShips());
			}
			if (CollectionUtils
					.isNotEmpty(deliverGoodsVo.getTransportOrderVehicles())) {
				transportOrderVehicleList
						.addAll(deliverGoodsVo.getTransportOrderVehicles());
			}
			if (CollectionUtils
					.isNotEmpty(deliverGoodsVo.getTransportOrderRailways())) {
				transportOrderRailwayList
						.addAll(deliverGoodsVo.getTransportOrderRailways());
			}
		}
		ArrayString oldSignReceiptIds = resource.getReceiptIds();
		// 之前关联的签收单
		List<SignReceipt> oldSignReceiptList = signReceiptService
				.findByIds(oldSignReceiptIds);
		// 对账状态进行更新
		for (SignReceipt signReceipt : oldSignReceiptList) {
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
		}
		// 根据签收单id找到 之前关联的发货信息
		List<DeliverGoods> deliverGoodsList1 = deliverGoodsService
				.findBySignReceiptIds(oldSignReceiptIds);
		// 之前发货单id
		List<String> deliverGoodsIds = deliverGoodsList1.stream()
				.map(DeliverGoods::getId).distinct().toList();
		// 之前的船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		// 之前的汽运信息
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		// 之前的铁路单信息
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		// 现在被关联的签收单
		List<String> newSignReceiptIds = deliverGoodsList.stream()
				.map(DeliverGoods::getSignReceiptId).filter(Objects::nonNull)
				.distinct().toList();
		// 现在关联的签收单
		List<SignReceipt> newSignReceiptList = signReceiptService
				.findByIds(newSignReceiptIds);
		// 将现在被关联的签收单的对账状态发货信息进行更新
		for (SignReceipt signReceipt : newSignReceiptList) {
			signReceipt.setReconciliationStatus(stateCode);
		}
		// 现在被关联的订单
		List<String> newOrderIds = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).filter(Objects::nonNull)
				.distinct().toList();

		// 将之前被关联的发货信息里面 的对账信息/预对账信息 清空
		this.handDeliverGoods(deliverGoodsList1,
				resource.getIsConductReconciliation());
		// 将之前被关联的船运单信息里面 的对账信息/预对账信息 清空
		this.handTransportOrderShips(transportOrderShips,
				resource.getIsConductReconciliation());
		// 将之前被关联的汽运单信息里面 的对账信息/预对账信息 清空
		this.handTransportOrderVehicles(transportOrderVehicles,
				resource.getIsConductReconciliation());
		// 将之前被关联的汽运单信息里面 的对账信息/预对账信息 清空
		this.handTransportOrderRailways(transportOrderRailways,
				resource.getIsConductReconciliation());
		signReceiptService.batchUpdate(oldSignReceiptList);
		signReceiptService.batchUpdate(newSignReceiptList);
		// 该条数据的采购方不是录入企业时才需要设置状态
		if (CommonDef.Symbol.NO.match(resource.getIsRecord())) {
			// 签署方式为线下时提交方式有两种
			if (((ReconciliationDef.SignType.OFFLINE
					.match(resource.getSignType())
					&& CommonDef.Symbol.YES
							.match(resource.getIsConductReconciliation()))
					|| (ReconciliationDef.SignType.OFFLINE
							.match(resource.getPreSignType())
							&& CommonDef.Symbol.NO.match(
									resource.getIsConductReconciliation())))
					&& ReconciliationDef.SaveType.SAVE_AND_COMMIT
							.match(saveType)) {
				if (CommonDef.AccountSource.INNER.match(origin)) {
					// admin端(销售方)提交
					resource.setState(
							ReconciliationDef.State.CONFIRMING.getCode());
					this.sendNotice(resource,
							wxSubscriptionProperties
									.getReconciliationUnConfirmCode(),
							MessageFormat.format(
									UserMessageConstants.RECONCILIATION_UNCONFIRMED_TEMPLATE,
									resource.getId()),
							ReconciliationDef.Type.SELL.getCode());
				} else {
					// custom端(采购方)提交
					resource.setState(
							ReconciliationDef.State.CONFIRMING.getCode());
				}
			}
			// 签署方式为线上时只能保存为草稿
			else {
				resource.setState(ReconciliationDef.State.DRAFT.getCode());
			}
		} else {
			// 采购方为录入企业时，需要修改未开票金额
			// 找出对账单关联的发票
			List<BillPayment> billPayments = billPaymentService
					.findByReconciliationIds(List.of(resource.getId()));
			// 已关联的开票金额
			BigDecimal billedAmount = BigDecimal.ZERO;
			// 循环关联的开票单列表，计算已经关联开票的金额
			for (BillPayment billPayment : billPayments) {
				for (ReconciliationJsonInfo reconciliationInfo : billPayment
						.getReconciliationIds()) {
					if (reconciliationInfo.getId().equals(resource.getId())) {
						billedAmount = billedAmount
								.add(reconciliationInfo.getBillAmount());
					}
				}
			}
			// 更新未开票金额
			resource.setUnbilledAmount(
					resource.getReconciliationAmount().subtract(billedAmount));
		}
		// 修改后的订单id，更新进对账单
		resource.setOrderIds(new ArrayString(
				newOrderIds.stream().filter(Objects::nonNull).toList()));
		// 修改后的签收单id，更新进对账单
		resource.setReceiptIds(new ArrayString(
				newSignReceiptIds.stream().filter(Objects::nonNull).toList()));

		this.updateAllProperties(resource);
		// 将新传过来的发货信息进行更新
		deliverGoodsService.batchUpdate(deliverGoodsList);
		if (CollectionUtils.isNotEmpty(transportOrderShipList)) {
			// 新传过来的船运单信息进行更新
			transportOrderShipService.batchUpdate(transportOrderShipList);
		}
		if (CollectionUtils.isNotEmpty(transportOrderVehicleList)) {
			// 新传过来的汽运单信息进行更新
			transportOrderVehicleService.batchUpdate(transportOrderVehicleList);
		}
		if (CollectionUtils.isNotEmpty(transportOrderRailwayList)) {
			// 新传过来的铁路单信息进行更新
			transportOrderRailwayService.batchUpdate(transportOrderRailwayList);
		}
		return Optional.of(resource);
	}

	@Transactional(rollbackFor = Exception.class)
	@FileId(type = 2)
	@Override
	public Optional<Reconciliation> updateReconciliation(
			Reconciliation resource, List<DeliverGoodsVo> deliverGoodsVoList) {
		// 设置状态为对账中
		Integer stateCode = OrderDef.BusinessStatus.IN_PROGRESS.getCode();
		// 发货单列表
		List<DeliverGoods> deliverGoodsList = new ArrayList<>();
		// 船运单列表
		List<TransportOrderShip> transportOrderShipList = new ArrayList<>();
		// 汽运单列表
		List<TransportOrderVehicle> transportOrderVehicleList = new ArrayList<>();
		// 铁路单列表
		List<TransportOrderRailway> transportOrderRailwayList = new ArrayList<>();
		for (DeliverGoodsVo deliverGoodsVo : deliverGoodsVoList) {
			deliverGoodsList.add(deliverGoodsVo.getDeliverGoods());
			if (CollectionUtils
					.isNotEmpty(deliverGoodsVo.getTransportOrderShips())) {
				transportOrderShipList
						.addAll(deliverGoodsVo.getTransportOrderShips());
			}
			if (CollectionUtils
					.isNotEmpty(deliverGoodsVo.getTransportOrderVehicles())) {
				transportOrderVehicleList
						.addAll(deliverGoodsVo.getTransportOrderVehicles());
			}
			if (CollectionUtils
					.isNotEmpty(deliverGoodsVo.getTransportOrderRailways())) {
				transportOrderRailwayList
						.addAll(deliverGoodsVo.getTransportOrderRailways());
			}
		}

		// 被关联的签收单
		List<String> newSignReceiptIds = deliverGoodsList.stream()
				.map(DeliverGoods::getSignReceiptId).filter(Objects::nonNull)
				.distinct().toList();

		// 被关联的签收单的对账状态改为对账中
		if (CollectionUtils.isNotEmpty(newSignReceiptIds)) {
			List<SignReceipt> signReceiptList1 = signReceiptService
					.findByIds(newSignReceiptIds);
			signReceiptList1.forEach(e -> e.setReconciliationStatus(stateCode));
			signReceiptService.batchUpdate(signReceiptList1);
		}
		this.updateAllProperties(resource);
		// 将新传过来的发货信息进行更新
		deliverGoodsService.batchUpdate(deliverGoodsList);
		if (CollectionUtils.isNotEmpty(transportOrderShipList)) {
			// 新传过来的船运单信息进行更新
			transportOrderShipService.batchUpdate(transportOrderShipList);
		}
		if (CollectionUtils.isNotEmpty(transportOrderVehicleList)) {
			// 新传过来的汽运单信息进行更新
			transportOrderVehicleService.batchUpdate(transportOrderVehicleList);
		}
		if (CollectionUtils.isNotEmpty(transportOrderRailwayList)) {
			// 新传过来的铁路单信息进行更新
			transportOrderRailwayService.batchUpdate(transportOrderRailwayList);
		}
		if (ReconciliationDef.SignType.ONLINE.match(resource.getSignType())
				&& CommonDef.AccountSource.CUSTOM
						.match(resource.getInitiator())) {
			// 客户端新增的对账并且是线上签署时
			this.initiateSign(resource, CertificationDef.Origin.PC.getCode(),
					CommonDef.AccountSource.CUSTOM.getCode());
		}
		return Optional.of(resource);
	}

	@Transactional(rollbackFor = Exception.class)
	@FileId(type = 2)
	@Override
	public Optional<Reconciliation> updateBuyReconciliation(
			Reconciliation resource, List<DeliverGoods> deliverGoodsList,
			List<DeliverGoodsVo> deliverGoodsVoList) {
		// 设置状态为对账中
		Integer stateCode = OrderDef.BusinessStatus.IN_PROGRESS.getCode();
		List<DeliverGoods> goodsList = this
				.getDeliverGoodsList(deliverGoodsVoList);
		// 被关联的签收单
		List<String> newSignReceiptIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
			newSignReceiptIds = new ArrayList<>(deliverGoodsList.stream()
					.map(DeliverGoods::getSignReceiptId)
					.filter(Objects::nonNull).distinct().toList());
		}
		// 被关联的签收单的对账状态改为对账中
		if (CollectionUtils.isNotEmpty(newSignReceiptIds)) {
			List<SignReceipt> signReceiptList1 = signReceiptService
					.findByIds(newSignReceiptIds);
			signReceiptList1.forEach(e -> e.setReconciliationStatus(stateCode));
			signReceiptService.batchUpdate(signReceiptList1);
		}
		this.updateAllProperties(resource);
		// 将新传过来的发货信息进行更新
		if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
			deliverGoodsService.batchUpdate(deliverGoodsList);
		}
		if (CollectionUtils.isNotEmpty(goodsList)) {
			deliverGoodsService.batchUpdate(goodsList);
		}
		if (ReconciliationDef.SignType.ONLINE.match(resource.getSignType())
				&& CommonDef.AccountSource.CUSTOM
						.match(resource.getInitiator())) {
			// 客户端新增的对账并且是线上签署时
			this.initiateSign(resource, CertificationDef.Origin.PC.getCode(),
					CommonDef.AccountSource.CUSTOM.getCode());
		}
		return Optional.of(resource);
	}

	@Transactional(rollbackFor = Exception.class)
	@FileId(type = 2)
	@Override
	public Optional<Reconciliation> updateBuy(Reconciliation resource,
			List<DeliverGoods> deliverGoodsList,
			List<DeliverGoodsVo> deliverGoodsVoList, Integer saveType,
			Integer origin) {
		// 该条数据的采购方不是录入企业时才需要设置状态
		if (CommonDef.Symbol.NO.match(resource.getIsRecord())) {
			// 签署方式为线下时提交方式有两种
			if (((ReconciliationDef.SignType.OFFLINE
					.match(resource.getSignType())
					&& CommonDef.Symbol.YES
							.match(resource.getIsConductReconciliation()))
					|| (ReconciliationDef.SignType.OFFLINE
							.match(resource.getPreSignType())
							&& CommonDef.Symbol.NO.match(
									resource.getIsConductReconciliation())))
					&& ReconciliationDef.SaveType.SAVE_AND_COMMIT
							.match(saveType)) {
				if (CommonDef.AccountSource.INNER.match(origin)) {
					// admin端(销售方)提交
					resource.setState(
							ReconciliationDef.State.CONFIRMING.getCode());
					this.sendNotice(resource,
							wxSubscriptionProperties
									.getReconciliationUnConfirmCode(),
							MessageFormat.format(
									UserMessageConstants.RECONCILIATION_UNCONFIRMED_TEMPLATE,
									resource.getId()),
							ReconciliationDef.Type.SELL.getCode());
				} else {
					// custom端(采购方)提交
					resource.setState(
							ReconciliationDef.State.CONFIRMING.getCode());
				}
			}
			// 签署方式为线上时只能保存为草稿
			else {
				resource.setState(ReconciliationDef.State.DRAFT.getCode());
			}
		} else {
			// 采购方为录入企业时，需要修改未开票金额
			// 找出对账单关联的发票
			List<BillPayment> billPayments = billPaymentService
					.findByReconciliationIds(List.of(resource.getId()));
			// 已关联的开票金额
			BigDecimal billedAmount = BigDecimal.ZERO;
			// 循环关联的开票单列表，计算已经关联开票的金额
			for (BillPayment billPayment : billPayments) {
				for (ReconciliationJsonInfo reconciliationInfo : billPayment
						.getReconciliationIds()) {
					if (reconciliationInfo.getId().equals(resource.getId())) {
						billedAmount = billedAmount
								.add(reconciliationInfo.getBillAmount());
					}
				}
			}
			// 更新未开票金额
			resource.setUnbilledAmount(
					resource.getReconciliationAmount().subtract(billedAmount));
		}
		// 设置给关联订单和签收单的对账状态
		Integer stateCode = getStateCode(resource);
		// 查找原先的签收单，将之前关联的签收单对账信息清空
		ArrayString receiptIds = resource.getReceiptIds();
		// 之前关联的签收单
		List<SignReceipt> oldSignReceiptList = signReceiptService
				.findByIds(receiptIds);
		List<DeliverGoods> oldDeliverGoods = deliverGoodsService
				.findBySignReceiptIds(receiptIds);
		List<String> oldDeliverGoodIds = oldDeliverGoods.stream()
				.map(DeliverGoods::getId).distinct().toList();
		// 之前的船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(oldDeliverGoodIds);
		// 之前的汽运信息
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(oldDeliverGoodIds);
		// 之前的铁路单信息
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(oldDeliverGoodIds);
		// 将之前被关联的发货信息里面 的对账信息/预对账信息 清空
		this.handDeliverGoods(oldDeliverGoods,
				resource.getIsConductReconciliation());
		// 将之前被关联的船运单信息里面 的对账信息/预对账信息 清空
		this.handTransportOrderShips(transportOrderShips,
				resource.getIsConductReconciliation());
		// 将之前被关联的汽运单信息里面 的对账信息/预对账信息 清空
		this.handTransportOrderVehicles(transportOrderVehicles,
				resource.getIsConductReconciliation());
		// 将之前被关联的汽运单信息里面 的对账信息/预对账信息 清空
		this.handTransportOrderRailways(transportOrderRailways,
				resource.getIsConductReconciliation());

		Map<String, DeliverGoods> oldDeliverGoodsMap = new HashMap<>();
		// 现在关联的签收单id
		List<String> newSignReceiptIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
			newSignReceiptIds = new ArrayList<>(deliverGoodsList.stream()
					.map(DeliverGoods::getSignReceiptId).distinct().toList());
		}
		// 现在关联的订单id
		List<String> newOrderIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
			newOrderIds = new ArrayList<>(deliverGoodsList.stream()
					.map(DeliverGoods::getOrderId).distinct().toList());
		}
		// 根据提货单单信息查找签收单信息
		List<SignReceipt> newSignReceiptList = signReceiptService
				.findByIds(newSignReceiptIds);
		List<DeliverGoods> goodsList = this
				.getDeliverGoodsList(deliverGoodsVoList);
		// 提货单id
		for (DeliverGoods deliverGoods : goodsList) {
			newSignReceiptIds.add(deliverGoods.getSignReceiptId());
			newOrderIds.add(deliverGoods.getOrderId());
		}
		// 现在关联的签收单更新对账状态
		for (SignReceipt e : newSignReceiptList) {
			e.setReconciliationStatus(stateCode);
		}
		// 批量更新签收单的对账状态
		signReceiptService.batchUpdate(newSignReceiptList);
		// 更新采购对账 如果销售方是录入企业 将之前的订单和签收单的对账状态修改成未对账
		if (CommonDef.Symbol.YES.match(resource.getIsRecord())) {
			// 找出对账单关联的发票
			List<BillPayment> billPayments = billPaymentService
					.findByReconciliationIds(List.of(resource.getId()));
			// 已关联的开票金额
			BigDecimal billedAmount = BigDecimal.ZERO;
			// 循环关联的开票单列表，计算已经关联开票的金额
			for (BillPayment billPayment : billPayments) {
				for (ReconciliationJsonInfo reconciliationInfo : billPayment
						.getReconciliationIds()) {
					if (reconciliationInfo.getId().equals(resource.getId())) {
						billedAmount = billedAmount
								.add(reconciliationInfo.getBillAmount());
					}
				}
			}
			// 更新未开票金额
			resource.setUnbilledAmount(
					resource.getReconciliationAmount().subtract(billedAmount));
			// 将之前被关联的签收单的对账状态为未对账
			this.handSignReceipt(oldSignReceiptList, oldDeliverGoodsMap);
		} else {
			// 将之前被关联的签收单的对账状态为未对账
			for (SignReceipt signReceipt : oldSignReceiptList) {
				signReceipt.setReconciliationStatus(
						OrderDef.BusinessStatus.NOT_STARTED.getCode());
			}
			// 存在预对账时更新 要清空之前签收单的预对账信息
			if (CommonDef.Symbol.YES.match(resource.getIsPreReconciliation())) {
				// 存在预对账时 清空发货信息的与对账信息
				for (DeliverGoods deliverGoods : oldDeliverGoods) {
					// 将原来提货单的货物信息的取出来
					List<GoodsInfo> list = this
							.convertGoodsInfo(deliverGoods.getGoodsInfo());
					// 清空货物信息的对账信息
					this.clearPreGoodsInfo(list);
					// 将修改后的信息进行更新
					Gson gson = getGson();
					deliverGoods.setGoodsInfo(gson.toJson(list));
				}
			} else {
				// 不存在预对账时更新 要清空之前签收单的对账信息
				// 将发货信息的对账信息清空
				for (DeliverGoods deliverGoods : oldDeliverGoods) {
					// 将原来提货单的货物信息的取出来
					List<GoodsInfo> list = this
							.convertGoodsInfo(deliverGoods.getGoodsInfo());
					// 清空货物信息的对账信息
					this.clearGoodsInfo(list);
					// 将修改后的信息进行更新
					Gson gson = getGson();
					deliverGoods.setGoodsInfo(gson.toJson(list));
				}
			}
		}
		// 将新旧发货信息进行更新
		if (CollectionUtils.isNotEmpty(oldDeliverGoods)) {
			deliverGoodsService.batchUpdate(oldDeliverGoods);
		}
		if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
			deliverGoodsService.batchUpdate(deliverGoodsList);
		}
		if(CollectionUtils.isNotEmpty(goodsList)) {
			deliverGoodsService.batchUpdate(goodsList);
		}
		// 将新旧签收单进行更新
		if (CollectionUtils.isNotEmpty(oldSignReceiptList)) {
			signReceiptService.batchUpdate(oldSignReceiptList);
		}
		if (CollectionUtils.isNotEmpty(oldSignReceiptList)) {
			signReceiptService.batchUpdate(newSignReceiptList);
		}
		// 对账单关联的签收单id进行更新
		resource.setReceiptIds(new ArrayString(
				newSignReceiptIds.stream().filter(Objects::nonNull).toList()));
		// 对账单关联的订单id进行更新
		resource.setOrderIds(new ArrayString(
				newOrderIds.stream().filter(Objects::nonNull).toList()));
		this.updateAllProperties(resource);
		return Optional.of(resource);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(Reconciliation resource) {
		// 将之前关联的发货信息里面的对账信息清空
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findBySignReceiptIds(resource.getReceiptIds());
		// 之前发货单id
		List<String> deliverGoodsIds = deliverGoodsList.stream()
				.map(DeliverGoods::getId).distinct().toList();
		// 船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		// 汽运单信息
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		// 铁路单信息
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		// 将之前被关联的发货信息里面 的对账信息清空
		this.handDeliverGoods(deliverGoodsList,
				resource.getIsConductReconciliation());
		// 将之前被关联的船运单信息里面 的对账信息清空
		this.handTransportOrderShips(transportOrderShips,
				resource.getIsConductReconciliation());
		// 将之前被关联的汽运单信息里面 的对账信息清空
		this.handTransportOrderVehicles(transportOrderVehicles,
				resource.getIsConductReconciliation());
		// 将之前被关联的铁路单信息里面 的对账信息清空
		this.handTransportOrderRailways(transportOrderRailways,
				resource.getIsConductReconciliation());
		// 根据发货信息查询之前被关联的签收单
		List<String> oldSignReceiptIds = deliverGoodsList.stream()
				.map(DeliverGoods::getSignReceiptId).filter(Objects::nonNull)
				.distinct().toList();
		// 删除销售合同 如果采购方是录入企业 删除的时候将之前的订单和签收单的对账状态修改成未对账
		if (CommonDef.Symbol.YES.match(resource.getIsRecord())) {
			// 之前关联的签收单
			List<SignReceipt> oldSignReceiptList = signReceiptService
					.findByIds(oldSignReceiptIds);
			// 之前被关联的签收单的对账状态 为未对账
			for (SignReceipt signReceipt : oldSignReceiptList) {
				// 签收单所关联的订单对账状态改为未开始
				signReceipt.setReconciliationStatus(
						OrderDef.BusinessStatus.NOT_STARTED.getCode());
			}
			// 删除对账信息
			super.delete(resource.getId());
		}
		// 如果是注册企业 处理和之前一样
		else {
			// 存在预对账并且没有进入对账阶段或者不存在预对账时删除
			if ((CommonDef.Symbol.YES.match(resource.getIsPreReconciliation())
					&& !CommonDef.Symbol.YES
							.match(resource.getIsConductReconciliation())
					|| CommonDef.Symbol.NO
							.match(resource.getIsPreReconciliation()))) {
				// 更新签收单的对账状态 判断是否需要改为未开始
				if (ContractDef.Type.SELL.match(resource.getType())) {
					this.changeSignReceiptStatus(oldSignReceiptIds, List.of());
				}
				super.delete(resource.getId());
			} else {
				// 更新签收单的对账状态 改为预对账
				if (ContractDef.Type.SELL.match(resource.getType())) {
					this.changePreSignReceiptStatus(oldSignReceiptIds);
				}
				// 更新对账单为预对账完成
				resource.setState(
						ReconciliationDef.State.PRE_FINISHED.getCode());
				resource.setIsConductReconciliation(
						CommonDef.Symbol.NO.getCode());
				// 清空对账的信息
				resource.setReconciliationAmount(null);
				resource.setReconciliationWeight(null);
				resource.setUnbilledAmount(null);
				resource.setReconciliationDate(null);
				super.updateAllProperties(resource);
			}
		}
	}

	@Override
	public void changeRelate(Reconciliation resource) {
		// 将之前关联的发货信息里面的对账信息清空
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findBySignReceiptIds(resource.getReceiptIds());
		Map<String, DeliverGoods> deliverGoodsMap = deliverGoodsList.stream()
				.collect(Collectors.toMap(DeliverGoods::getId, e -> e));
		List<GoodsInfo> goodsInfoList = new ArrayList<>();
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			if (Objects.nonNull(deliverGoods.getGoodsInfo())) {
				List<GoodsInfo> list = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				list.forEach(goodsInfo -> {
					goodsInfo.setReceiptId(deliverGoods.getSignReceiptId());
					goodsInfo.setOrderId(deliverGoods.getOrderId());
					goodsInfo.setDeliverGoodsId(deliverGoods.getId());
					goodsInfo.setTransportType(deliverGoods.getDelivery());
					goodsInfo.setReceiptWay(deliverGoods.getReceiveWay());
					if (DeliverGoodsDef.ReceiveWay.MERGE
							.match(deliverGoods.getReceiveWay())) {
						goodsInfo.setReceiptQuantity(
								deliverGoods.getReceiptWeight());
					}
				});
				goodsInfoList.addAll(list);
			}
		}

		// 之前发货单id
		List<String> deliverGoodsIds = deliverGoodsList.stream()
				.map(DeliverGoods::getId).distinct().toList();
		// 船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(transportOrderShips)) {
			for (TransportOrderShip transportOrderShip : transportOrderShips) {
				GoodsInfo goodsInfo = new GoodsInfo();
				goodsInfo.setOrderId(transportOrderShip.getOrderId());
				goodsInfo.setDeliverGoodsId(transportOrderShip.getGoodsId());
				goodsInfo.setTransportType(
						DeliverGoodsDef.DeliverWay.SHIPPING.getCode());
				goodsInfo.setModel(transportOrderShip.getModel());
				goodsInfo.setGoodsName(transportOrderShip.getGoodsType());
				goodsInfo.setDeliveryQuantity(
						new BigDecimal(transportOrderShip.getTon()));
				goodsInfo.setReceiptWay(deliverGoodsMap
						.get(transportOrderShip.getGoodsId()).getReceiveWay());
				goodsInfo.setReceiptId(
						deliverGoodsMap.get(transportOrderShip.getGoodsId())
								.getSignReceiptId());
				DeliverGoods deliverGoods = deliverGoodsMap
						.get(transportOrderShip.getGoodsId());
				if (DeliverGoodsDef.ReceiveWay.SEPARATE
						.match(deliverGoods.getReceiveWay())) {
					goodsInfo.setReceiptQuantity(
							transportOrderShip.getReceiptQuantity());
				} else {
					goodsInfo.setReceiptQuantity(
							deliverGoods.getReceiptWeight());
				}
				goodsInfo.setReceiptId(deliverGoods.getSignReceiptId());
				goodsInfo.setReceiptDate(transportOrderShip.getReceiptDate());
				goodsInfo.setReceiptRemark(
						transportOrderShip.getReceiptRemark());
				goodsInfo.setTransportToolsName(
						transportOrderShip.getShipName());
				goodsInfo.setShipId(transportOrderShip.getId());
				goodsInfo.setReconciliationSubtotal(
						transportOrderShip.getReconciliationSubtotal());
				goodsInfo.setReconciliationUnitPrice(
						transportOrderShip.getReconciliationUnitPrice());
				goodsInfo.setReconciliationRemark(
						transportOrderShip.getReconciliationRemark());
				goodsInfoList.add(goodsInfo);
			}
		}
		// 汽运单信息
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(transportOrderVehicles)) {
			for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicles) {
				GoodsInfo goodsInfo = new GoodsInfo();
				goodsInfo.setOrderId(transportOrderVehicle.getOrderId());
				goodsInfo.setDeliverGoodsId(
						transportOrderVehicle.getDeliverGoodsId());
				goodsInfo.setTransportType(
						DeliverGoodsDef.DeliverWay.CAR.getCode());
				goodsInfo.setGoodsName(transportOrderVehicle.getGoodsName());
				goodsInfo.setModel(transportOrderVehicle.getGoodsType());
				goodsInfo.setDeliveryQuantity(
						transportOrderVehicle.getTransportWeight());
				goodsInfo.setReceiptWay(deliverGoodsMap
						.get(transportOrderVehicle.getDeliverGoodsId())
						.getReceiveWay());
				goodsInfo.setReceiptId(deliverGoodsMap
						.get(transportOrderVehicle.getDeliverGoodsId())
						.getSignReceiptId());
				DeliverGoods deliverGoods = deliverGoodsMap
						.get(transportOrderVehicle.getDeliverGoodsId());
				if (DeliverGoodsDef.ReceiveWay.SEPARATE
						.match(deliverGoods.getReceiveWay())) {
					goodsInfo.setReceiptQuantity(
							transportOrderVehicle.getReceiptQuantity());
				} else {
					goodsInfo.setReceiptQuantity(
							deliverGoods.getReceiptWeight());
				}
				goodsInfo.setReceiptId(deliverGoods.getSignReceiptId());
				goodsInfo
						.setReceiptDate(transportOrderVehicle.getReceiptDate());
				goodsInfo.setReceiptRemark(
						transportOrderVehicle.getReceiptRemark());
				goodsInfo.setTransportToolsName(
						transportOrderVehicle.getDriverLicensePlate());
				goodsInfo.setVehicleId(transportOrderVehicle.getId());
				goodsInfo.setReconciliationSubtotal(
						transportOrderVehicle.getReconciliationSubtotal());
				goodsInfo.setReconciliationUnitPrice(
						transportOrderVehicle.getReconciliationUnitPrice());
				goodsInfo.setReconciliationRemark(
						transportOrderVehicle.getReconciliationRemark());
				goodsInfoList.add(goodsInfo);
			}
		}

		// 铁路单信息
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(transportOrderRailways)) {
			for (TransportOrderRailway transportOrderRailway : transportOrderRailways) {
				GoodsInfo goodsInfo = new GoodsInfo();
				goodsInfo.setOrderId(transportOrderRailway.getOrderId());
				goodsInfo.setDeliverGoodsId(
						transportOrderRailway.getDeliverGoodsId());
				goodsInfo.setTransportType(
						DeliverGoodsDef.DeliverWay.TRAIN.getCode());
				goodsInfo.setGoodsName(transportOrderRailway.getGoodsName());
				goodsInfo.setModel(transportOrderRailway.getModel());
				goodsInfo.setDeliveryQuantity(
						transportOrderRailway.getTransportWeight());
				goodsInfo.setReceiptWay(deliverGoodsMap
						.get(transportOrderRailway.getDeliverGoodsId())
						.getReceiveWay());
				DeliverGoods deliverGoods = deliverGoodsMap
						.get(transportOrderRailway.getDeliverGoodsId());
				if (DeliverGoodsDef.ReceiveWay.SEPARATE
						.match(deliverGoods.getReceiveWay())) {
					goodsInfo.setReceiptQuantity(
							transportOrderRailway.getReceiptQuantity());
				} else {
					goodsInfo.setReceiptQuantity(
							deliverGoods.getReceiptWeight());
				}
				goodsInfo.setReceiptId(deliverGoods.getSignReceiptId());
				goodsInfo
						.setReceiptDate(transportOrderRailway.getReceiptDate());
				goodsInfo.setReceiptRemark(
						transportOrderRailway.getReceiptRemark());
				goodsInfo.setTransportToolsName(
						transportOrderRailway.getCarTypeNumber());
				goodsInfo.setRailwayId(transportOrderRailway.getId());
				goodsInfo.setReceiptId(deliverGoodsMap
						.get(transportOrderRailway.getDeliverGoodsId())
						.getSignReceiptId());
				goodsInfo.setReconciliationSubtotal(
						transportOrderRailway.getReconciliationSubtotal());
				goodsInfo.setReconciliationUnitPrice(
						transportOrderRailway.getReconciliationUnitPrice());
				goodsInfo.setReconciliationRemark(
						transportOrderRailway.getReconciliationRemark());
				goodsInfoList.add(goodsInfo);
			}
		}

		// 将之前被关联的发货信息里面 的对账信息清空
		this.handDeliverGoods(deliverGoodsList, CommonDef.Symbol.YES.getCode());
		// 将之前被关联的船运单信息里面 的对账信息清空
		this.handTransportOrderShips(transportOrderShips,
				CommonDef.Symbol.YES.getCode());
		// 将之前被关联的汽运单信息里面 的对账信息清空
		this.handTransportOrderVehicles(transportOrderVehicles,
				CommonDef.Symbol.YES.getCode());
		// 将之前被关联的铁路单信息里面 的对账信息清空
		this.handTransportOrderRailways(transportOrderRailways,
				CommonDef.Symbol.YES.getCode());
		// 根据发货信息查询之前被关联的签收单
		List<String> oldSignReceiptIds = deliverGoodsList.stream()
				.map(DeliverGoods::getSignReceiptId).filter(Objects::nonNull)
				.distinct().toList();
		// 更新签收单的对账状态 判断是否需要改为未开始/预对账
		if (ContractDef.Type.SELL.match(resource.getType())) {
			// 存在预对账并且进入了对账阶段时 确认作废时
			if (CommonDef.Symbol.YES.match(resource.getIsPreReconciliation())) {
				// 被关联的签收单的对账状态 为预对账
				this.changePreSignReceiptStatus(oldSignReceiptIds);
			} else {
				// 存在预对账 在预对账阶段作废或者是 不存在预对账 对账阶段作废 判断签收单是否变成未对账
				this.changeSignReceiptStatus(oldSignReceiptIds, List.of());
			}
		}

		resource.setDeliveredInfo(JSONArray.toJSONString(goodsInfoList));
		super.update(resource);
	}

	@Override
	public void changePreRelate(Reconciliation resource) {
		// 将之前关联的发货信息里面的对账信息清空
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findBySignReceiptIds(resource.getReceiptIds());
		Map<String, DeliverGoods> deliverGoodsMap = deliverGoodsList.stream()
				.collect(Collectors.toMap(DeliverGoods::getId, e -> e));
		// 所有的发货信息
		List<GoodsInfo> goodsInfoList = new ArrayList<>();
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			if (Objects.nonNull(deliverGoods.getGoodsInfo())) {
				List<GoodsInfo> list = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				list.forEach(goodsInfo -> {
					goodsInfo.setReceiptId(deliverGoods.getSignReceiptId());
					goodsInfo.setOrderId(deliverGoods.getOrderId());
					goodsInfo.setDeliverGoodsId(deliverGoods.getId());
					goodsInfo.setTransportType(deliverGoods.getDelivery());
					goodsInfo.setReceiptWay(deliverGoods.getReceiveWay());
					if (DeliverGoodsDef.ReceiveWay.MERGE
							.match(deliverGoods.getReceiveWay())) {
						goodsInfo.setReceiptQuantity(
								deliverGoods.getReceiptWeight());
					}
				});
				goodsInfoList.addAll(list);
			}
		}

		// 之前发货单id
		List<String> deliverGoodsIds = deliverGoodsList.stream()
				.map(DeliverGoods::getId).distinct().toList();
		// 船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(transportOrderShips)) {
			for (TransportOrderShip transportOrderShip : transportOrderShips) {
				GoodsInfo goodsInfo = new GoodsInfo();
				goodsInfo.setOrderId(transportOrderShip.getOrderId());
				goodsInfo.setDeliverGoodsId(transportOrderShip.getGoodsId());
				goodsInfo.setTransportType(
						DeliverGoodsDef.DeliverWay.SHIPPING.getCode());
				goodsInfo.setModel(transportOrderShip.getModel());
				goodsInfo.setGoodsName(transportOrderShip.getGoodsType());
				goodsInfo.setDeliveryQuantity(
						new BigDecimal(transportOrderShip.getTon()));
				goodsInfo.setReceiptWay(deliverGoodsMap
						.get(transportOrderShip.getGoodsId()).getReceiveWay());
				goodsInfo.setReceiptId(
						deliverGoodsMap.get(transportOrderShip.getGoodsId())
								.getSignReceiptId());
				DeliverGoods deliverGoods = deliverGoodsMap
						.get(transportOrderShip.getGoodsId());
				if (DeliverGoodsDef.ReceiveWay.SEPARATE
						.match(deliverGoods.getReceiveWay())) {
					goodsInfo.setReceiptQuantity(
							transportOrderShip.getReceiptQuantity());
				} else {
					goodsInfo.setReceiptQuantity(
							deliverGoods.getReceiptWeight());
				}
				goodsInfo.setReceiptId(deliverGoods.getSignReceiptId());
				goodsInfo.setReceiptDate(transportOrderShip.getReceiptDate());
				goodsInfo.setReceiptRemark(
						transportOrderShip.getReceiptRemark());
				goodsInfo.setTransportToolsName(
						transportOrderShip.getShipName());
				goodsInfo.setShipId(transportOrderShip.getId());
				goodsInfo.setPreReconciliationSubtotal(
						transportOrderShip.getPreReconciliationSubtotal());
				goodsInfo.setPreReconciliationUnitPrice(
						transportOrderShip.getPreReconciliationUnitPrice());
				goodsInfo.setPreReconciliationRemark(
						transportOrderShip.getPreReconciliationRemark());
				goodsInfoList.add(goodsInfo);
			}
		}
		// 汽运单信息
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(transportOrderVehicles)) {
			for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicles) {
				GoodsInfo goodsInfo = new GoodsInfo();
				goodsInfo.setOrderId(transportOrderVehicle.getOrderId());
				goodsInfo.setDeliverGoodsId(
						transportOrderVehicle.getDeliverGoodsId());
				goodsInfo.setTransportType(
						DeliverGoodsDef.DeliverWay.CAR.getCode());
				goodsInfo.setGoodsName(transportOrderVehicle.getGoodsName());
				goodsInfo.setModel(transportOrderVehicle.getGoodsType());
				goodsInfo.setDeliveryQuantity(
						transportOrderVehicle.getTransportWeight());
				goodsInfo.setReceiptWay(deliverGoodsMap
						.get(transportOrderVehicle.getDeliverGoodsId())
						.getReceiveWay());
				goodsInfo.setReceiptId(deliverGoodsMap
						.get(transportOrderVehicle.getDeliverGoodsId())
						.getSignReceiptId());
				DeliverGoods deliverGoods = deliverGoodsMap
						.get(transportOrderVehicle.getDeliverGoodsId());
				if (DeliverGoodsDef.ReceiveWay.SEPARATE
						.match(deliverGoods.getReceiveWay())) {
					goodsInfo.setReceiptQuantity(
							transportOrderVehicle.getReceiptQuantity());
				} else {
					goodsInfo.setReceiptQuantity(
							deliverGoods.getReceiptWeight());
				}
				goodsInfo.setReceiptId(deliverGoods.getSignReceiptId());
				goodsInfo
						.setReceiptDate(transportOrderVehicle.getReceiptDate());
				goodsInfo.setReceiptRemark(
						transportOrderVehicle.getReceiptRemark());
				goodsInfo.setTransportToolsName(
						transportOrderVehicle.getDriverLicensePlate());
				goodsInfo.setVehicleId(transportOrderVehicle.getId());
				goodsInfo.setPreReconciliationSubtotal(
						transportOrderVehicle.getPreReconciliationSubtotal());
				goodsInfo.setPreReconciliationUnitPrice(
						transportOrderVehicle.getPreReconciliationUnitPrice());
				goodsInfo.setPreReconciliationRemark(
						transportOrderVehicle.getPreReconciliationRemark());
				goodsInfoList.add(goodsInfo);
			}
		}

		// 铁路单信息
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(transportOrderRailways)) {
			for (TransportOrderRailway transportOrderRailway : transportOrderRailways) {
				GoodsInfo goodsInfo = new GoodsInfo();
				goodsInfo.setOrderId(transportOrderRailway.getOrderId());
				goodsInfo.setDeliverGoodsId(
						transportOrderRailway.getDeliverGoodsId());
				goodsInfo.setTransportType(
						DeliverGoodsDef.DeliverWay.TRAIN.getCode());
				goodsInfo.setGoodsName(transportOrderRailway.getGoodsName());
				goodsInfo.setModel(transportOrderRailway.getModel());
				goodsInfo.setDeliveryQuantity(
						transportOrderRailway.getTransportWeight());
				goodsInfo.setReceiptWay(deliverGoodsMap
						.get(transportOrderRailway.getDeliverGoodsId())
						.getReceiveWay());
				DeliverGoods deliverGoods = deliverGoodsMap
						.get(transportOrderRailway.getDeliverGoodsId());
				if (DeliverGoodsDef.ReceiveWay.SEPARATE
						.match(deliverGoods.getReceiveWay())) {
					goodsInfo.setReceiptQuantity(
							transportOrderRailway.getReceiptQuantity());
				} else {
					goodsInfo.setReceiptQuantity(
							deliverGoods.getReceiptWeight());
				}
				goodsInfo.setReceiptId(deliverGoods.getSignReceiptId());
				goodsInfo
						.setReceiptDate(transportOrderRailway.getReceiptDate());
				goodsInfo.setReceiptRemark(
						transportOrderRailway.getReceiptRemark());
				goodsInfo.setTransportToolsName(
						transportOrderRailway.getCarTypeNumber());
				goodsInfo.setRailwayId(transportOrderRailway.getId());
				goodsInfo.setReceiptId(deliverGoodsMap
						.get(transportOrderRailway.getDeliverGoodsId())
						.getSignReceiptId());
				goodsInfo.setPreReconciliationSubtotal(
						transportOrderRailway.getPreReconciliationSubtotal());
				goodsInfo.setPreReconciliationUnitPrice(
						transportOrderRailway.getPreReconciliationUnitPrice());
				goodsInfo.setPreReconciliationRemark(
						transportOrderRailway.getPreReconciliationRemark());

				goodsInfoList.add(goodsInfo);
			}
		}

		// 将之前被关联的发货信息里面 的预对账信息清空
		this.handDeliverGoods(deliverGoodsList, CommonDef.Symbol.NO.getCode());
		// 将之前被关联的船运单信息里面 的对账信息清空
		this.handTransportOrderShips(transportOrderShips,
				CommonDef.Symbol.NO.getCode());
		// 将之前被关联的汽运单信息里面 的对账信息清空
		this.handTransportOrderVehicles(transportOrderVehicles,
				CommonDef.Symbol.NO.getCode());
		// 将之前被关联的铁路单信息里面 的对账信息清空
		this.handTransportOrderRailways(transportOrderRailways,
				CommonDef.Symbol.NO.getCode());
		// 根据发货信息查询之前被关联的签收单
		List<String> oldSignReceiptIds = deliverGoodsList.stream()
				.map(DeliverGoods::getSignReceiptId).filter(Objects::nonNull)
				.distinct().toList();

		// 更新签收单的对账状态 判断是否需要改为未开始/预对账
		if (ContractDef.Type.SELL.match(resource.getType())) {
			// 存在预对账 在预对账阶段作废或者是 不存在预对账 对账阶段作废 判断签收单是否变成未对账
			this.changeSignReceiptStatus(oldSignReceiptIds, List.of());

		}
		resource.setPreDeliveredInfo(JSONArray.toJSONString(goodsInfoList));
		super.update(resource);
	}

	@Override
	public void changeRejectInvalid(Reconciliation reconciliation) {
		// 查询之前被关联的签收单
		List<SignReceipt> oldSignReceiptList = signReceiptService
				.findByIds(reconciliation.getReceiptIds());

		// 将之前被关联的签收单的对账状态以及发货信息进行更新
		for (SignReceipt signReceipt : oldSignReceiptList) {
			// 对账状态变成对账完成
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.COMPLETED.getCode());
		}
		if (CollectionUtils.isNotEmpty(oldSignReceiptList)) {
			signReceiptService.batchUpdate(oldSignReceiptList);
		}
	}

	@Override
	public void changePreRejectInvalid(Reconciliation reconciliation) {
		// 查询之前被关联的签收单
		List<SignReceipt> oldSignReceiptList = signReceiptService
				.findByIds(reconciliation.getReceiptIds());
		// 将之前被关联的签收单的对账状态以及发货信息进行更新
		for (SignReceipt signReceipt : oldSignReceiptList) {
			// 对账状态变成预对账
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.PRE_RECONCILIATION.getCode());
		}
		if (CollectionUtils.isNotEmpty(oldSignReceiptList)) {
			signReceiptService.batchUpdate(oldSignReceiptList);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteBuy(Reconciliation resource) {
		// 删除之前被关联的签收单
		List<SignReceipt> oldSignReceiptList = signReceiptService
				.findByIds(resource.getReceiptIds());
		List<DeliverGoods> oldDeliverGoods = deliverGoodsService
				.findBySignReceiptIds(resource.getReceiptIds());
		List<String> oldDeliverGoodIds = oldDeliverGoods.stream()
				.map(DeliverGoods::getId).distinct().toList();
		// 之前的船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(oldDeliverGoodIds);
		// 之前的汽运信息
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(oldDeliverGoodIds);
		// 之前的铁路单信息
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(oldDeliverGoodIds);
		// 将之前被关联的发货信息里面 的对账信息/预对账信息 清空
		this.handDeliverGoods(oldDeliverGoods,
				resource.getIsConductReconciliation());
		// 将之前被关联的船运单信息里面 的对账信息/预对账信息 清空
		this.handTransportOrderShips(transportOrderShips,
				resource.getIsConductReconciliation());
		// 将之前被关联的汽运单信息里面 的对账信息/预对账信息 清空
		this.handTransportOrderVehicles(transportOrderVehicles,
				resource.getIsConductReconciliation());
		// 将之前被关联的汽运单信息里面 的对账信息/预对账信息 清空
		this.handTransportOrderRailways(transportOrderRailways,
				resource.getIsConductReconciliation());

		Map<String, DeliverGoods> oldDeliverGoodsMap = new HashMap<>();
		// 删除采购合同 如果销售方是录入企业 删除的时候将之前签收单的对账状态修改成未对账 货物信息的对账清空掉
		if (CommonDef.Symbol.YES.match(resource.getIsRecord())) {
			// 处理关联的签收单和提货单
			this.handSignReceipt(oldSignReceiptList, oldDeliverGoodsMap);
			super.delete(resource.getId());
		}
		// 如果是注册企业
		else {
			// 存在预对账并且没有进入对账阶段或者不存在预对账时删除
			if ((CommonDef.Symbol.YES.match(resource.getIsPreReconciliation())
					&& !CommonDef.Symbol.YES
							.match(resource.getIsConductReconciliation())
					|| CommonDef.Symbol.NO
							.match(resource.getIsPreReconciliation()))) {
				for (SignReceipt signReceipt : oldSignReceiptList) {
					signReceipt.setReconciliationStatus(
							OrderDef.BusinessStatus.NOT_STARTED.getCode());
				}
				// 不存在预对账时 清空发货信息的对账信息
				if (CommonDef.Symbol.NO
						.match(resource.getIsPreReconciliation())) {
					for (DeliverGoods deliverGoods : oldDeliverGoods) {
						// 将原来提货单的货物信息的取出来
						List<GoodsInfo> list = this
								.convertGoodsInfo(deliverGoods.getGoodsInfo());
						// 清空货物信息的对账信息
						this.clearGoodsInfo(list);
						// 将修改后的信息进行更新
						Gson gson = getGson();
						deliverGoods.setGoodsInfo(gson.toJson(list));
					}
				} else {
					// 存在预对账时 清空发货信息的与对账信息
					for (DeliverGoods deliverGoods : oldDeliverGoods) {
						// 将原来提货单的货物信息的取出来
						List<GoodsInfo> list = this
								.convertGoodsInfo(deliverGoods.getGoodsInfo());
						// 清空货物信息的对账信息
						this.clearPreGoodsInfo(list);
						// 将修改后的信息进行更新
						Gson gson = getGson();
						deliverGoods.setGoodsInfo(gson.toJson(list));
					}

				}
				super.delete(resource.getId());
			} else {
				// 存在预对账进入对账阶段时删除
				// 更新签收单的对账状态 改为预对账
				for (SignReceipt signReceipt : oldSignReceiptList) {
					signReceipt.setReconciliationStatus(
							OrderDef.BusinessStatus.PRE_RECONCILIATION
									.getCode());
				}
				// 将发货信息的对账信息清空
				for (DeliverGoods deliverGoods : oldDeliverGoods) {
					// 将原来提货单的货物信息的取出来
					List<GoodsInfo> list = this
							.convertGoodsInfo(deliverGoods.getGoodsInfo());
					// 清空货物信息的对账信息
					this.clearGoodsInfo(list);
					// 将修改后的信息进行更新
					Gson gson = getGson();
					deliverGoods.setGoodsInfo(gson.toJson(list));
				}
				// 更新对账单为预对账完成
				resource.setState(
						ReconciliationDef.State.PRE_FINISHED.getCode());
				// 设置为没有进入对账阶段
				resource.setIsConductReconciliation(
						CommonDef.Symbol.NO.getCode());
				// 清空对账的信息
				resource.setReconciliationAmount(null);
				resource.setReconciliationWeight(null);
				resource.setUnbilledAmount(null);
				resource.setReconciliationDate(null);
				super.updateAllProperties(resource);
			}
		}
		// 更新原来的签收单
		if (CollectionUtils.isNotEmpty(oldSignReceiptList)) {
			signReceiptService.batchUpdate(oldSignReceiptList);
		}
		// 更新原来的提货单
		if (CollectionUtils.isNotEmpty(oldDeliverGoods)) {
			deliverGoodsService.batchUpdate(oldDeliverGoods);
		}
		super.delete(resource.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Reconciliation> confirm(Reconciliation resource) {
		Contract contract = contractService.findOne(resource.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30151001));
		// 进入对账阶段后 确认对账
		if (CommonDef.Symbol.YES.match(resource.getIsConductReconciliation())) {
			// 更新对账单状态 对账状态改为已完成
			resource.setState(ReconciliationDef.State.FINISHED.getCode());
			// 开票状态设置为未开票
			resource.setInvoiceState(
					ReconciliationDef.InvoiceState.NOT_INVOICED.getCode());
		} else {
			// 预对账阶段后 确认预对账
			// 更新对账单状态 对账状态改为预对账完成
			resource.setState(ReconciliationDef.State.PRE_FINISHED.getCode());
		}
		Reconciliation reconciliation = this.updateAllProperties(resource);
		// 进入对账阶段后 确认对账要修改订单和签收单的信息 预对账阶段时 确认预对账时 订单和签收单还是预对账状态
		if (CommonDef.Symbol.YES.match(resource.getIsConductReconciliation())) {
			if (Objects.nonNull(contract)) {
				// 该对账单所关联的所有订单
				List<Order> orders = orderService
						.findByIds(resource.getOrderIds());
				List<String> orderIds = orders.stream().map(Order::getId)
						.filter(Objects::nonNull).distinct().toList();
				// 根据签收单id找到所有的发货信息
				List<DeliverGoodsVo> deliverGoodsVoList = deliverGoodsService
						.findVosBySignReceiptIds(resource.getReceiptIds());
				// 更新签收单的对账状态 改为已完成
				this.completedChangeSignReceiptStatus(resource.getReceiptIds());
				// 处理支付记录状态
				this.handlePayment(reconciliation, orderIds);
				// 销售对账生成应收应付款
				if (ReconciliationDef.Type.SELL.match(resource.getType())) {
					// 处理应收/付款
					this.handleAccounts(reconciliation, orders,
							deliverGoodsVoList);
				}
				// 采购对账生成应收应付款
				else {
					// 处理应收/付款
					this.handleBuyAccounts(reconciliation, orders,
							deliverGoodsVoList);
				}
			}
		}
		if (ReconciliationDef.SignType.OFFLINE
				.match(reconciliation.getSignType())) {
			if (CommonDef.AccountSource.CUSTOM
					.match(reconciliation.getInitiator())) {
				this.sendNotice(reconciliation,
						wxSubscriptionProperties.getReconciliationConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.RECONCILIATION_CONFIRMED_TEMPLATE,
								reconciliation.getId()),
						ReconciliationDef.Type.SELL.getCode());
			} else {
				SpringUtil.getBean(ReconciliationService.class)
						.notice(reconciliation, 2);
			}

		}
		return Optional.of(reconciliation);
	}

	@Override
	public Optional<Reconciliation> submit(Reconciliation reconciliation) {
		if ((ReconciliationDef.SignType.OFFLINE
				.match(reconciliation.getSignType())
				&& CommonDef.Symbol.YES
						.match(reconciliation.getIsConductReconciliation()))
				|| (ReconciliationDef.SignType.OFFLINE
						.match(reconciliation.getPreSignType())
						&& CommonDef.Symbol.NO.match(
								reconciliation.getIsConductReconciliation()))) {
			// 对账阶段判断对账签署方式为线下，预对账阶段判断预对账签署方式为线下 则设置为确认中
			reconciliation
					.setState(ReconciliationDef.State.CONFIRMING.getCode());
			if (CommonDef.AccountSource.CUSTOM
					.match(reconciliation.getInitiator())) {
				SpringUtil.getBean(ReconciliationService.class)
						.notice(reconciliation, 1);

			} else {
				this.sendNotice(reconciliation,
						wxSubscriptionProperties
								.getReconciliationUnConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.RECONCILIATION_UNCONFIRMED_TEMPLATE,
								reconciliation.getId()),
						ReconciliationDef.Type.SELL.getCode());
			}
		} else {
			if ((CommonDef.AccountSource.INNER
					.match(reconciliation.getInitiator())
					&& CommonDef.Symbol.YES
							.match(reconciliation.getIsConductReconciliation()))
					|| (CommonDef.AccountSource.INNER
							.match(reconciliation.getPreInitiator())
							&& CommonDef.Symbol.NO.match(reconciliation
									.getIsConductReconciliation()))) {
				// 对账阶段判断对账发起签署的为管理后台，预对账阶段判断预预对账签署发起签署的为管理后台 则设置为待发起状态
				reconciliation.setState(
						ReconciliationDef.State.TO_BE_INITIATE.getCode());
			} else {
				reconciliation
						.setState(ReconciliationDef.State.SIGNING.getCode());
			}
		}
		return Optional.of(super.updateAllProperties(reconciliation));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Reconciliation> retract(Reconciliation reconciliation) {
		// 存在预对账并且在对账阶段
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())
				&& CommonDef.Symbol.YES
						.match(reconciliation.getIsPreReconciliation())) {
			this.delete(reconciliation);
		} else {
			reconciliation.setState(ReconciliationDef.State.DRAFT.getCode());
		}
		return Optional.of(super.updateAllProperties(reconciliation));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ContractPageResponse> initiateSign(Reconciliation resource,
			Integer origin, Integer initiateType) {
		Long fileId = this.getFileId(resource);
		if (CommonDef.Symbol.YES.match(resource.getIsConductReconciliation())) {
			// 对账阶段
			if (Objects.nonNull(fileId)) {
				resource.setReconciliationFileId(fileId);
				this.updateAllProperties(resource);
			} else {
				log.info("生成pdf文件失败");
				return Optional.empty();
			}
			// 设置供应链签署人
			String name = "";
			// 销售对账 客户端是采购方
			if (ReconciliationDef.Type.SELL.match(resource.getType())) {
				switch (CommonDef.AccountSource.from(initiateType)) {
					case CUSTOM -> {
						name = resource.getPurchaserEnterprise().getName()
								+ "对账单";
						User user1 = adminSealService
								.findByType(
										SignerSettings.billType.reconciliation)
								.orElse(null);
						if (Objects.nonNull(user1)) {
							resource.setSupplierSigner(user1.getName());
							resource.setSupplierSignerId(user1.getId());
						}
					}
					case INNER -> {
						name = resource.getSellerEnterprise().getName() + "对账单";
						User user = UserContextHolder.getUser();
						if (Objects.nonNull(user)) {
							resource.setSupplierSigner(user.getName());
						}
					}
				}
			}
			// 采购对账 客户端是销售方
			else {
				switch (CommonDef.AccountSource.from(initiateType)) {
					case CUSTOM -> {
						name = resource.getSellerEnterprise().getName() + "对账单";
						User user1 = adminSealService
								.findByType(
										SignerSettings.billType.reconciliation)
								.orElse(null);
						if (Objects.nonNull(user1)) {
							resource.setSupplierSigner(user1.getName());
							resource.setSupplierSignerId(user1.getId());
						}
					}
					case INNER -> {
						name = resource.getPurchaserEnterprise().getName()
								+ "对账单";
						User user = UserContextHolder.getUser();
						if (Objects.nonNull(user)) {
							resource.setSupplierSigner(user.getName());
						}
					}
				}

			}
			// 发起合同
			Map<Long, String> customerMap = contractRecordService.draft(name,
					List.of(resource.getPurchaserId(), resource.getSellerId()),
					List.of(resource.getReconciliationFileId()),
					resource.getId(),
					PurchaseContractDef.CorrelationTable.RECONCILIATION, null,
					null);
			// 设置文件id
			resource.setReconciliationFileId(contractRecordService.download(
					resource.getId(),
					PurchaseContractDef.CorrelationTable.RECONCILIATION));

			resource.getPurchaserEnterprise()
					.setSignMobile(customerMap.get(resource.getPurchaserId()));
			resource.getSellerEnterprise()
					.setSignMobile(customerMap.get(resource.getSellerId()));
			// 对账签署状态设置为双方未签署
			resource.setSignStatus(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode());
		} else {
			// 预对账阶段
			if (Objects.nonNull(fileId)) {
				resource.setPreReconciliationFileId(fileId);
				this.updateAllProperties(resource);
			} else {
				log.info("生成pdf文件失败");
				return Optional.empty();
			}
			// 设置供应链签署人
			String name = "";
			// 销售对账 客户端是采购方
			if (ReconciliationDef.Type.SELL.match(resource.getType())) {
				switch (CommonDef.AccountSource.from(initiateType)) {
					case CUSTOM -> {
						name = resource.getPurchaserEnterprise().getName()
								+ "对账单";
						User user1 = adminSealService
								.findByType(
										SignerSettings.billType.reconciliation)
								.orElse(null);
						if (Objects.nonNull(user1)) {
							resource.setPreSupplierSigner(user1.getName());
							resource.setPreSupplierSignerId(user1.getId());
						}
					}
					case INNER -> {
						name = resource.getSellerEnterprise().getName() + "对账单";
						User user = UserContextHolder.getUser();
						if (Objects.nonNull(user)) {
							resource.setPreSupplierSigner(user.getName());
							resource.setPreSupplierSignerId(user.getId());
						}
					}
				}
			} else {
				switch (CommonDef.AccountSource.from(initiateType)) {
					case CUSTOM -> {
						name = resource.getSellerEnterprise().getName() + "对账单";
						User user1 = adminSealService
								.findByType(
										SignerSettings.billType.reconciliation)
								.orElse(null);
						if (Objects.nonNull(user1)) {
							resource.setPreSupplierSigner(user1.getName());
							resource.setPreSupplierSignerId(user1.getId());
						}
					}
					case INNER -> {
						name = resource.getPurchaserEnterprise().getName()
								+ "对账单";
						User user = UserContextHolder.getUser();
						if (Objects.nonNull(user)) {
							resource.setPreSupplierSigner(user.getName());
							resource.setPreSupplierSignerId(user.getId());
						}
					}
				}
			}
			// 发起合同
			Map<Long, String> customerMap = contractRecordService.draft(name,
					List.of(resource.getPurchaserId(), resource.getSellerId()),
					List.of(resource.getPreReconciliationFileId()),
					resource.getId(),
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION,
					null, null);
			// 设置文件id
			resource.setPreReconciliationFileId(contractRecordService.download(
					resource.getId(),
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION));
			resource.getPurchaserEnterprise()
					.setSignMobile(customerMap.get(resource.getPurchaserId()));
			resource.getSellerEnterprise()
					.setSignMobile(customerMap.get(resource.getSellerId()));
			// 预对账签署状态设置为双方未签署
			resource.setPreSignStatus(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode());
		}
		// 状态设置为签署中
		resource.setState(ReconciliationDef.State.SIGNING.getCode());
		this.updateAllProperties(resource);

		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				resource.getPurchaserEnterprise().getSignMobile(),
				resource.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}
		if (CommonDef.Symbol.YES.match(resource.getIsConductReconciliation())) {
			// 对账阶段
			// 获取签署链接
			return contractRecordService.sign(resource.getId(),
					PurchaseContractDef.CorrelationTable.RECONCILIATION,
					origin);
		} else {
			// 预对账阶段
			// 获取签署链接
			return contractRecordService.sign(resource.getId(),
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION,
					origin);
		}
	}

	@Override
	public Optional<ContractPageResponse> signing(Reconciliation reconciliation,
			Integer origin) {
		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				reconciliation.getPurchaserEnterprise().getSignMobile(),
				reconciliation.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			return contractRecordService.sign(reconciliation.getId(),
					PurchaseContractDef.CorrelationTable.RECONCILIATION,
					origin);
		} else {
			return contractRecordService.sign(reconciliation.getId(),
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION,
					origin);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ContractPageResponse> invalid(Reconciliation reconciliation,
			Integer origin) {
		// 对账 被关联的订单的对账状态 为作废中
		List<Order> oldOrderList = orderService
				.findByIds(reconciliation.getOrderIds());
		List<String> oldOrderIds = oldOrderList.stream().map(Order::getId)
				.filter(Objects::nonNull).distinct().toList();
		// 将之前关联的发货信息
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByOrderIds(oldOrderIds);
		// 根据发货信息查询之前被关联的签收单
		List<String> oldSignReceiptIds = deliverGoodsList.stream()
				.map(DeliverGoods::getSignReceiptId).filter(Objects::nonNull)
				.distinct().toList();
		List<SignReceipt> oldSignReceiptList = signReceiptService
				.findByIds(oldSignReceiptIds);
		// 将之前被关联的订单的对账状态以及发货信息进行更新
		for (Order order : oldOrderList) {
			// 对账状态变成作废中
			order.setReconciliationStatus(
					OrderDef.BusinessStatus.INVALID.getCode());
		}
		// 将之前被关联的签收单的对账状态以及发货信息进行更新
		for (SignReceipt signReceipt : oldSignReceiptList) {
			// 对账状态变成作废中
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.INVALID.getCode());
		}
		if (CollectionUtils.isNotEmpty(oldOrderList)) {
			orderService.batchUpdate(oldOrderList);
		}
		if (CollectionUtils.isNotEmpty(oldSignReceiptList)) {
			signReceiptService.batchUpdate(oldSignReceiptList);
		}
		// 进入对账阶段时 和之前处理一样
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 调用契约锁撤销合同接口
			contractRecordService.revoke(reconciliation.getId(),
					PurchaseContractDef.CorrelationTable.RECONCILIATION);
			// 作废后获取作废合同id
			Long fileId = contractRecordService.detail(reconciliation.getId(),
					PurchaseContractDef.CorrelationTable.RECONCILIATION);
			reconciliation.setInvalidFileId(fileId);
			reconciliation
					.setState(ReconciliationDef.State.INVALIDING.getCode());
			reconciliation.setInvalidSignState(
					PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
			reconciliation.setInvalidRevokeReason(null);
			reconciliation.setInvalidRevokeTime(null);
			reconciliation.setPurchaseInvalidTime(null);
			reconciliation.setSellerInvalidTime(null);
		} else {
			// 预对账阶段 设置预对账信息
			// 调用契约锁撤销合同接口
			contractRecordService.revoke(reconciliation.getId(),
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION);
			// 作废后获取作废合同id
			Long fileId = contractRecordService.detail(reconciliation.getId(),
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION);
			reconciliation.setPreInvalidFileId(fileId);
			reconciliation
					.setState(ReconciliationDef.State.INVALIDING.getCode());
			reconciliation.setPreInvalidSignState(
					PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
			reconciliation.setPreInvalidRevokeReason(null);
			reconciliation.setPreInvalidRevokeTime(null);
			reconciliation.setPrePurchaseInvalidTime(null);
			reconciliation.setPreSellerInvalidTime(null);
		}
		super.updateAllProperties(reconciliation);
		if (CommonDef.UserType.INNER.match(origin)) {
			this.sendNotice(reconciliation,
					wxSubscriptionProperties
							.getNullifyConfirmReconciliationCode(),
					MessageFormat.format(
							UserMessageConstants.RECONCILIATION_INVALID_TEMPLATE,
							reconciliation.getId()),
					ReconciliationDef.Type.SELL.getCode());
		} else {
			SpringUtil.getBean(ReconciliationService.class)
					.notice(reconciliation, 5);
		}

		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				reconciliation.getPurchaserEnterprise().getSignMobile(),
				reconciliation.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}

		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 对账阶段时 发起对账单签署
			return contractRecordService.sign(reconciliation.getId(),
					PurchaseContractDef.CorrelationTable.RECONCILIATION,
					CertificationDef.Origin.PC.getCode());
		} else {
			// 预对账阶段时 发起预对账单签署
			return contractRecordService.sign(reconciliation.getId(),
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION,
					CertificationDef.Origin.PC.getCode());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Reconciliation> invalidOffLine(
			Reconciliation reconciliation, Integer initiator) {
		// 对账 被关联的订单的对账状态 为作废中
		List<Order> oldOrderList = orderService
				.findByIds(reconciliation.getOrderIds());
		List<String> oldOrderIds = oldOrderList.stream().map(Order::getId)
				.filter(Objects::nonNull).distinct().toList();
		// 订单关联的发货信息
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByOrderIds(oldOrderIds);
		// 根据发货信息查询之前被关联的签收单
		List<String> oldSignReceiptIds = deliverGoodsList.stream()
				.map(DeliverGoods::getSignReceiptId).filter(Objects::nonNull)
				.distinct().toList();
		List<SignReceipt> oldSignReceiptList = signReceiptService
				.findByIds(oldSignReceiptIds);
		// 将之前被关联的订单的对账状态以及发货信息进行更新
		for (Order order : oldOrderList) {
			// 对账状态变成作废中
			order.setReconciliationStatus(
					OrderDef.BusinessStatus.INVALID.getCode());
		}
		// 将之前被关联的签收单的对账状态以及发货信息进行更新
		for (SignReceipt signReceipt : oldSignReceiptList) {
			// 对账状态变成作废中
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.INVALID.getCode());
		}
		if (CollectionUtils.isNotEmpty(oldOrderList)) {
			orderService.batchUpdate(oldOrderList);
		}
		if (CollectionUtils.isNotEmpty(oldSignReceiptList)) {
			signReceiptService.batchUpdate(oldSignReceiptList);
		}

		// 作废合同
		reconciliation.setState(ReconciliationDef.State.INVALIDING.getCode());
		// 进入对账阶段后
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			reconciliation.setInvalidInitiator(initiator);
			reconciliation.setInvalidRevokeReason(null);
			reconciliation.setInvalidRevokeTime(null);
			reconciliation.setPurchaseInvalidTime(null);
			reconciliation.setSellerInvalidTime(null);
		} else {
			// 在预对账阶段时
			reconciliation.setPreInvalidInitiator(initiator);
			reconciliation.setPreInvalidRevokeReason(null);
			reconciliation.setPreInvalidRevokeTime(null);
			reconciliation.setPrePurchaseInvalidTime(null);
			reconciliation.setPreSellerInvalidTime(null);
		}
		if (CommonDef.UserType.INNER.match(initiator)) {
			// 进入对账阶段后
			if (CommonDef.Symbol.YES
					.match(reconciliation.getIsConductReconciliation())) {
				// 销售对账设置销售方作废状态和时间
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					reconciliation.setInvalidSignState(
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode());
					reconciliation.setSellerInvalidTime(LocalDateTime.now());
				}
				// 采购对账
				else {
					reconciliation.setInvalidSignState(
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode());
					reconciliation.setPurchaseInvalidTime(LocalDateTime.now());
				}
			} else {
				// 销售对账设置销售方作废状态和时间
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					// 预对账阶段 设置预对账信息
					reconciliation.setPreInvalidSignState(
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode());
					reconciliation.setPreSellerInvalidTime(LocalDateTime.now());
				} else {
					// 预对账阶段 设置预对账信息
					reconciliation.setPreInvalidSignState(
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode());
					reconciliation
							.setPrePurchaseInvalidTime(LocalDateTime.now());
				}
			}
			this.sendNotice(reconciliation,
					wxSubscriptionProperties
							.getNullifyConfirmReconciliationCode(),
					MessageFormat.format(
							UserMessageConstants.RECONCILIATION_INVALID_TEMPLATE,
							reconciliation.getId()),
					ReconciliationDef.Type.SELL.getCode());
		} else {
			// 进入对账阶段后 设置对账信息
			if (CommonDef.Symbol.YES
					.match(reconciliation.getIsConductReconciliation())) {
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					reconciliation.setInvalidSignState(
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode());
					reconciliation.setPurchaseInvalidTime(LocalDateTime.now());
				} else {
					reconciliation.setInvalidSignState(
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode());
					reconciliation.setSellerInvalidTime(LocalDateTime.now());
				}
			} else {
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					// 预对账阶段 设置预对账信息
					reconciliation.setPreInvalidSignState(
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode());
					reconciliation
							.setPrePurchaseInvalidTime(LocalDateTime.now());
				} else {
					// 预对账阶段 设置预对账信息
					reconciliation.setPreInvalidSignState(
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode());
					reconciliation.setPreSellerInvalidTime(LocalDateTime.now());
				}
			}
			SpringUtil.getBean(ReconciliationService.class)
					.notice(reconciliation, 5);
		}
		return Optional.of(super.updateAllProperties(reconciliation));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Reconciliation> confirmInvalid(
			Reconciliation reconciliation) {
		// 存在预对账时
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsPreReconciliation())) {
			// 在对账阶段时
			if (CommonDef.Symbol.YES
					.match(reconciliation.getIsConductReconciliation())) {
				// 确认作废后 变成预对账完成状态
				reconciliation.setState(
						ReconciliationDef.State.PRE_FINISHED.getCode());
				// 是否进入对账设置为否
				reconciliation.setIsConductReconciliation(
						CommonDef.Symbol.NO.getCode());
				// 设置对账的作废信息
				reconciliation.setInvalidSignState(
						BusinessContractDef.CommonSignState.COMPLETED
								.getCode());
				this.changeRelate(reconciliation);
				// 对账作废后就删除应收/付款
				this.handleDelAccounts(reconciliation);
				// 对账作废之后清空对账信息
				// 管理后台
				if (CommonDef.UserType.INNER
						.match(reconciliation.getInvalidInitiator())) {
					// 销售对账
					if (ReconciliationDef.Type.SELL
							.match(reconciliation.getType())) {
						reconciliation
								.setPurchaseInvalidTime(LocalDateTime.now());
					} else {
						reconciliation
								.setSellerInvalidTime(LocalDateTime.now());
					}
					SpringUtil.getBean(ReconciliationService.class)
							.notice(reconciliation, 7);
				} else {
					// 销售对账
					if (ReconciliationDef.Type.SELL
							.match(reconciliation.getType())) {
						reconciliation
								.setSellerInvalidTime(LocalDateTime.now());
						this.sendNotice(reconciliation,
								wxSubscriptionProperties
										.getPreFinishedReconciliationCode(),
								MessageFormat.format(
										UserMessageConstants.RECONCILIATION_PRE_FINISHED_TEMPLATE,
										reconciliation.getId()),
								ReconciliationDef.Type.SELL.getCode());
					} else {
						reconciliation
								.setPurchaseInvalidTime(LocalDateTime.now());
					}

				}
			} else {
				// 在预对账阶段时 确认作废后 变成已作废状态
				reconciliation
						.setState(ReconciliationDef.State.INVALID.getCode());
				// 设置预对账的作废信息
				reconciliation.setPreInvalidSignState(
						BusinessContractDef.CommonSignState.COMPLETED
								.getCode());
				// 管理后台
				if (CommonDef.UserType.INNER
						.match(reconciliation.getPreInvalidInitiator())) {
					// 销售对账
					if (ReconciliationDef.Type.SELL
							.match(reconciliation.getType())) {
						reconciliation
								.setPrePurchaseInvalidTime(LocalDateTime.now());
					} else {
						reconciliation
								.setPreSellerInvalidTime(LocalDateTime.now());
					}
				}
				// 客户端
				else {
					// 销售对账
					if (ReconciliationDef.Type.SELL
							.match(reconciliation.getType())) {
						reconciliation
								.setPreSellerInvalidTime(LocalDateTime.now());
					} else {
						reconciliation
								.setPrePurchaseInvalidTime(LocalDateTime.now());
					}
				}
				this.changePreRelate(reconciliation);
			}
		} else {
			// 不存在预对账时 处理和之前一样
			// 确认作废合同
			reconciliation.setState(ReconciliationDef.State.INVALID.getCode());
			reconciliation.setInvalidSignState(
					BusinessContractDef.CommonSignState.COMPLETED.getCode());
			// 管理后台
			if (CommonDef.UserType.INNER
					.match(reconciliation.getInvalidInitiator())) {
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					reconciliation.setPurchaseInvalidTime(LocalDateTime.now());
				} else {
					reconciliation.setSellerInvalidTime(LocalDateTime.now());
				}
			}
			// 客户端
			else {
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					reconciliation.setSellerInvalidTime(LocalDateTime.now());
				} else {
					reconciliation.setPurchaseInvalidTime(LocalDateTime.now());
				}
			}
			this.changeRelate(reconciliation);
			// 处理应收/付款
			this.handleDelAccounts(reconciliation);
		}
		return Optional.of(super.updateAllProperties(reconciliation));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Reconciliation> revertInvalid(
			Reconciliation reconciliation) {
		// 没有进入对账阶段时
		if (!CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 处理订单和签收的对账状态
			this.changePreRejectInvalid(reconciliation);
			// 撤销作废合同 预对账作废中驳回作废变成 预对账完成 状态
			reconciliation
					.setState(ReconciliationDef.State.PRE_FINISHED.getCode());
			reconciliation.setPreInvalidSignState(null);
			reconciliation.setPreInvalidRevokeTime(LocalDateTime.now());
			// 处理订单和签收的对账状态 改成预对账状态
			this.changeRejectInvalid(reconciliation);

			if (CommonDef.UserType.INNER
					.match(reconciliation.getInvalidInitiator())) {
				SpringUtil.getBean(ReconciliationService.class)
						.notice(reconciliation, 7);
			} else {
				this.sendNotice(reconciliation,
						wxSubscriptionProperties
								.getPreFinishedReconciliationCode(),
						MessageFormat.format(
								UserMessageConstants.RECONCILIATION_PRE_FINISHED_TEMPLATE,
								reconciliation.getId()),
						ReconciliationDef.Type.SELL.getCode());
			}
		} else {
			// 进入对账阶段时 处理对账的信息
			// 处理订单和签收的对账状态
			this.changeRejectInvalid(reconciliation);
			// 撤销作废合同
			reconciliation.setState(ReconciliationDef.State.FINISHED.getCode());
			reconciliation.setInvalidSignState(null);
			reconciliation.setInvalidRevokeTime(LocalDateTime.now());
		}

		if (ReconciliationDef.Type.SELL.match(reconciliation.getType())) {
			if (CommonDef.UserType.INNER
					.match(reconciliation.getInvalidInitiator())) {
				SpringUtil.getBean(ReconciliationService.class)
						.notice(reconciliation, 6);
			} else {
				this.sendNotice(reconciliation,
						wxSubscriptionProperties
								.getNullifyDismissReconciliationCode(),
						MessageFormat.format(
								UserMessageConstants.RECONCILIATION_INVALID_DISMISS_TEMPLATE,
								reconciliation.getId()),
						ReconciliationDef.Type.SELL.getCode());
			}
		} else {
			if (CommonDef.UserType.OUTER
					.match(reconciliation.getInvalidInitiator())) {
				SpringUtil.getBean(ReconciliationService.class)
						.notice(reconciliation, 6);
			}
		}
		return Optional.of(super.updateAllProperties(reconciliation));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void reject(Reconciliation reconciliation, Boolean isRevoke) {
		// 非对账阶段时处理预对账数据
		if (!CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			if (Objects.nonNull(reconciliation.getPreReconciliationFileId())) {
				fileService.batchUnActive(
						List.of(reconciliation.getPreReconciliationFileId()));
				if (ReconciliationDef.SignType.ONLINE
						.match(reconciliation.getPreSignType())) {
					reconciliation.setPreReconciliationFileId(null);
				}
				if (isRevoke && ReconciliationDef.SignType.ONLINE
						.match(reconciliation.getPreSignType())) {
					// 撤销合同
					contractRecordService.revoke(reconciliation.getId(),
							PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION);
				}
			}
		} else {
			if (Objects.nonNull(reconciliation.getReconciliationFileId())) {
				fileService.batchUnActive(
						List.of(reconciliation.getReconciliationFileId()));
				if (ReconciliationDef.SignType.ONLINE
						.match(reconciliation.getSignType())) {
					reconciliation.setReconciliationFileId(null);
				}
				if (isRevoke && ReconciliationDef.SignType.ONLINE
						.match(reconciliation.getSignType())) {
					// 撤销合同
					contractRecordService.revoke(reconciliation.getId(),
							PurchaseContractDef.CorrelationTable.RECONCILIATION);
				}
			}
		}
		super.updateAllProperties(reconciliation);

		if (ReconciliationDef.SignType.OFFLINE
				.match(reconciliation.getSignType())) {
			if (CommonDef.AccountSource.CUSTOM
					.match(reconciliation.getInitiator())) {
				this.sendNotice(reconciliation,
						wxSubscriptionProperties.getReconciliationDismissCode(),
						MessageFormat.format(
								UserMessageConstants.RECONCILIATION_DISMISS_TEMPLATE,
								reconciliation.getId()),
						ReconciliationDef.Type.SELL.getCode());
			} else {
				SpringUtil.getBean(ReconciliationService.class)
						.notice(reconciliation, 3);
			}
		}
	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = "{{#success}}", type = LogDef.RECONCILIATION_INFO, bizNo = "{{#resource.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#resource.getId()}}"),
			@LogRecord.KeyValuePair(key = "#projectId#", value = "{{#code}}") }, messageType = LogDef.MESSAGE_TYPE_ORDER, permission = LogDef.PROJECT_DEAL)
	public void notice(Reconciliation resource, Integer type) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElse(new Project());
		LogRecordContext.putVariable("code", project.getName());
		switch (type) {
			case 1 -> LogRecordContext.putVariable("success",
					LogDef.RECONCILIATION_PENDING_CONFIRMATION);
			case 2 -> LogRecordContext.putVariable("success",
					LogDef.RECONCILIATION_CONFIRMED);
			case 3 -> LogRecordContext.putVariable("success",
					LogDef.RECONCILIATION_REJECTED);
			case 4 -> LogRecordContext.putVariable("success",
					LogDef.RECONCILIATION_COMPLETED);
			case 5 -> LogRecordContext.putVariable("success",
					LogDef.RECONCILIATION_INVALID_ADD);
			case 6 -> LogRecordContext.putVariable("success",
					LogDef.RECONCILIATION_INVALID_REJECTED);
			case 7 -> LogRecordContext.putVariable("success",
					LogDef.RECONCILIATION_PRE_FINISHED_COMPLETED);
			default -> {
			}
		}
		log.info("对账发送通知:{}", resource.getId());
	}

	@Override
	public void downloadPdf(HttpServletResponse response, String id)
			throws IOException {
		setExportResponseFields(response, id);
		GeneratPdfDto pdf = generatPdfDto(id);
		Reconciliation reconciliation = super.findOne(id)
				.orElseThrow(() -> new RuntimeException("对账单不存在"));
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 对账阶段是生成对账pdf
			PdfUtils.getPdf(TransactionDef.PdfType.RECONCILIATION,
					response.getOutputStream(), pdf);
		} else {
			// 预对账阶段时生成预对账pdf
			PdfUtils.getPdf(TransactionDef.PdfType.PRE_RECONCILIATION,
					response.getOutputStream(), pdf);
		}
	}

	@Override
	public void completedChangeSignReceiptStatus(List<String> signReceiptIds) {
		List<SignReceipt> signReceiptList = signReceiptService
				.findByIds(signReceiptIds);
		// 循环签收单 签收单的对账状态都为已完成
		for (SignReceipt signReceipt : signReceiptList) {
			// 签收单所关联的所有订单对账状态为已对账 则该签收单也为已对账
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.COMPLETED.getCode());
		}
		signReceiptService.batchUpdate(signReceiptList);
	}

	private void changePreSignReceiptStatus(List<String> oldSignReceiptIds) {
		// 之前关联的签收单
		List<SignReceipt> oldSignReceiptList = signReceiptService
				.findByIds(oldSignReceiptIds);
		// 循环签收单 判断签收单所关联的订单的对账状态是否都为预对账
		for (SignReceipt signReceipt : oldSignReceiptList) {
			// 签收单所关联的所有订单对账状态为预对账 则该签收单也为预对账
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.PRE_RECONCILIATION.getCode());
		}
		signReceiptService.batchUpdate(oldSignReceiptList);
	}

	private void changeSignReceiptStatus(List<String> oldSignReceiptIds,
			List<String> newSignReceiptIds) {
		// 之前关联的签收单
		List<SignReceipt> oldSignReceiptList = signReceiptService
				.findByIds(oldSignReceiptIds);
		// 循环签收单 判断签收单所关联的订单的对账状态是否都为已完成
		for (SignReceipt signReceipt : oldSignReceiptList) {
			// 签收单所关联的订单对账状态改为未开始
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
		}
		signReceiptService.batchUpdate(oldSignReceiptList);
		// 现在被关联的签收单的对账状态改为对账中
		if (CollectionUtils.isNotEmpty(newSignReceiptIds)) {
			List<SignReceipt> signReceiptList1 = signReceiptService
					.findByIds(newSignReceiptIds);
			signReceiptList1.forEach(e -> e.setReconciliationStatus(
					OrderDef.BusinessStatus.IN_PROGRESS.getCode()));
			signReceiptService.batchUpdate(signReceiptList1);
		}
	}

	@Override
	public void handlePayment(Reconciliation reconciliation,
			List<String> orderIds) {
		// 保证金转货款选择了是
		if (CommonDef.Symbol.YES
				.match(reconciliation.getDepositTransferAmount())) {
			// 根据订单id查询已完成并且费用类型为订单保证金的支付记录
			List<Payment> paymentList = paymentService.findByOrders(orderIds,
					PaymentDef.State.COMPLETED.getCode(),
					PaymentDef.CostType.ORDER_DEPOSIT.getCode(), null);
			// 将支付记录的费用类型由订单保证金转为货款，变更事由：保证金转货款,对账单id为当前对账单id
			if (CollectionUtils.isNotEmpty(paymentList)) {
				for (Payment payment : paymentList) {
					payment.setCostType(
							PaymentDef.CostType.GOODS_PAYMENT.getCode());
					payment.setChangeRemark("保证金转货款");
					payment.setReconciliationId(reconciliation.getId());
				}
				paymentService.batchUpdate(paymentList);
				// 批量处理中间表的数据、不需要处理中间表的数据、因为是通过orderIds查询表的数据
			}
		}
	}

	@Override
	public Optional<ReconciliationCountVo> staticsAdminReconciliation(
			boolean isManage, boolean isSeal) {
		ReconciliationCountVo reconciliationCountVo = new ReconciliationCountVo();
		reconciliationCountVo.setWaitConfirm(0L);
		reconciliationCountVo.setToBeSigned(0L);
		reconciliationCountVo.setReject(0L);
		reconciliationCountVo.setInvaliding(0L);
		reconciliationCountVo.setPurchaseToBeConfirmed(0L);
		reconciliationCountVo.setPurchaseToBeSigned(0L);
		reconciliationCountVo.setPurchaseRejected(0L);
		reconciliationCountVo.setPurchaseInvaliding(0L);
		reconciliationCountVo.setSaleToBeConfirmed(0L);
		reconciliationCountVo.setSaleRejected(0L);
		reconciliationCountVo.setSaleInvalided(0L);
		reconciliationCountVo.setSaleInvaliding(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计销售待确认
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Reconciliation::getType,
						ReconciliationDef.Type.SELL.getCode());
				wrapper.and(x -> x
						.eq(Reconciliation::getInitiator,
								CommonDef.AccountSource.CUSTOM.getCode())
						.eq(Reconciliation::getState,
								ReconciliationDef.State.CONFIRMING.getCode()));
				wrapper.in(Reconciliation::getProjectId, projectIds);
				reconciliationCountVo
						.setSaleToBeConfirmed(repository.selectCount(wrapper));

				// 统计销售已驳回
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Reconciliation::getType,
						ReconciliationDef.Type.SELL.getCode());
				wrapper.and(x -> x
						.eq(Reconciliation::getInitiator,
								CommonDef.AccountSource.INNER.getCode())
						.eq(Reconciliation::getState,
								ReconciliationDef.State.REJECTED.getCode()));
				wrapper.in(Reconciliation::getProjectId, projectIds);
				reconciliationCountVo
						.setSaleInvalided(repository.selectCount(wrapper));

				// 统计销售作废中
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Reconciliation::getType,
						ReconciliationDef.Type.SELL.getCode());
				wrapper.eq(Reconciliation::getState,
						ReconciliationDef.State.INVALIDING.getCode());
				wrapper.in(Reconciliation::getProjectId, projectIds);
				reconciliationCountVo
						.setSaleInvaliding(repository.selectCount(wrapper));

				// 统计采购待确认
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Reconciliation::getType,
						ReconciliationDef.Type.BUY.getCode());
				wrapper.and(x -> x
						.eq(Reconciliation::getInitiator,
								CommonDef.AccountSource.INNER.getCode())
						.eq(Reconciliation::getState,
								ReconciliationDef.State.CONFIRMING.getCode()));
				wrapper.in(Reconciliation::getProjectId, projectIds);
				reconciliationCountVo.setPurchaseToBeConfirmed(
						repository.selectCount(wrapper));

				// 统计采购已驳回
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Reconciliation::getType,
						ReconciliationDef.Type.BUY.getCode());
				wrapper.and(x -> x
						.eq(Reconciliation::getInitiator,
								CommonDef.AccountSource.CUSTOM.getCode())
						.eq(Reconciliation::getState,
								ReconciliationDef.State.REJECTED.getCode()));
				wrapper.in(Reconciliation::getProjectId, projectIds);
				reconciliationCountVo
						.setPurchaseRejected(repository.selectCount(wrapper));

				// 统计采购作废中
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Reconciliation::getType,
						ReconciliationDef.Type.BUY.getCode());
				wrapper.eq(Reconciliation::getState,
						ReconciliationDef.State.INVALIDING.getCode());
				wrapper.in(Reconciliation::getProjectId, projectIds);
				reconciliationCountVo
						.setPurchaseInvaliding(repository.selectCount(wrapper));
			}
		}

		if (isSeal) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计销售待签署
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Reconciliation::getType,
						ReconciliationDef.Type.SELL.getCode());
				wrapper.eq(Reconciliation::getState,
						ReconciliationDef.State.SIGNING.getCode());
				wrapper.and(x -> x
						.eq(Reconciliation::getSignStatus,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode())
						.or().eq(Reconciliation::getSignStatus,
								BusinessContractDef.CommonSignState.UNSIGNED
										.getCode()));
				wrapper.in(Reconciliation::getProjectId, projectIds);
				reconciliationCountVo
						.setSaleRejected(repository.selectCount(wrapper));

				// 统计采购待签署
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Reconciliation::getType,
						ReconciliationDef.Type.BUY.getCode());
				wrapper.eq(Reconciliation::getState,
						ReconciliationDef.State.SIGNING.getCode());
				wrapper.and(x -> x.eq(Reconciliation::getSignStatus,
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode())
						.or().eq(Reconciliation::getSignStatus,
								BusinessContractDef.CommonSignState.UNSIGNED
										.getCode()));
				wrapper.in(Reconciliation::getProjectId, projectIds);
				reconciliationCountVo
						.setPurchaseToBeSigned(repository.selectCount(wrapper));
			}
		}
		return Optional.of(reconciliationCountVo);
	}

	@Override
	public Optional<ReconciliationCountVo> staticsCustomerReconciliation(
			boolean isSeal, boolean isPermission) {
		ReconciliationCountVo reconciliationCountVo = new ReconciliationCountVo();
		reconciliationCountVo.setWaitConfirm(0L);
		reconciliationCountVo.setToBeSigned(0L);
		reconciliationCountVo.setReject(0L);
		reconciliationCountVo.setInvaliding(0L);
		reconciliationCountVo.setPurchaseToBeConfirmed(0L);
		reconciliationCountVo.setPurchaseToBeSigned(0L);
		reconciliationCountVo.setPurchaseRejected(0L);
		reconciliationCountVo.setPurchaseInvaliding(0L);
		reconciliationCountVo.setSaleToBeConfirmed(0L);
		reconciliationCountVo.setSaleRejected(0L);
		reconciliationCountVo.setSaleInvalided(0L);
		reconciliationCountVo.setSaleInvaliding(0L);
		LambdaQueryWrapper<Reconciliation> wrapper = Wrappers
				.lambdaQuery(Reconciliation.class);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		if (isPermission) {
			// 统计采购待确认
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.and(x -> x
					.eq(Reconciliation::getInitiator,
							CommonDef.AccountSource.INNER.getCode())
					.eq(Reconciliation::getState,
							ReconciliationDef.State.CONFIRMING.getCode()));
			wrapper.eq(Reconciliation::getPurchaserId, customerId);
			reconciliationCountVo
					.setPurchaseToBeConfirmed(repository.selectCount(wrapper));

			// 统计采购已驳回
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.and(x -> x
					.eq(Reconciliation::getInitiator,
							CommonDef.AccountSource.CUSTOM.getCode())
					.eq(Reconciliation::getState,
							ReconciliationDef.State.REJECTED.getCode()));
			wrapper.eq(Reconciliation::getPurchaserId, customerId);
			reconciliationCountVo
					.setPurchaseRejected(repository.selectCount(wrapper));

			// 统计采购作废中
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Reconciliation::getPurchaserId, customerId);
			wrapper.eq(Reconciliation::getState,
					ReconciliationDef.State.INVALIDING.getCode());
			reconciliationCountVo
					.setPurchaseInvaliding(repository.selectCount(wrapper));

			// 统计销售待确认
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.and(x -> x
					.eq(Reconciliation::getInitiator,
							CommonDef.AccountSource.CUSTOM.getCode())
					.eq(Reconciliation::getState,
							ReconciliationDef.State.CONFIRMING.getCode()));
			wrapper.eq(Reconciliation::getSellerId, customerId);
			reconciliationCountVo
					.setSaleToBeConfirmed(repository.selectCount(wrapper));

			// 统计销售已驳回
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.and(x -> x
					.eq(Reconciliation::getInitiator,
							CommonDef.AccountSource.INNER.getCode())
					.eq(Reconciliation::getState,
							ReconciliationDef.State.REJECTED.getCode()));
			wrapper.eq(Reconciliation::getSellerId, customerId);
			reconciliationCountVo
					.setSaleInvalided(repository.selectCount(wrapper));

			// 统计销售作废中
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Reconciliation::getSellerId, customerId);
			wrapper.eq(Reconciliation::getState,
					ReconciliationDef.State.INVALIDING.getCode());
			reconciliationCountVo
					.setSaleInvaliding(repository.selectCount(wrapper));
		}

		if (isSeal) {
			// 统计采购待签署
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Reconciliation::getState,
					ReconciliationDef.State.SIGNING.getCode());
			wrapper.eq(Reconciliation::getPurchaserId, customerId);
			wrapper.and(x -> x
					.eq(Reconciliation::getSignStatus,
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode())
					.or().eq(Reconciliation::getSignStatus,
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode()));
			reconciliationCountVo
					.setPurchaseToBeSigned(repository.selectCount(wrapper));

			// 统计销售待签署
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Reconciliation::getState,
					ReconciliationDef.State.SIGNING.getCode());
			wrapper.eq(Reconciliation::getSellerId, customerId);
			wrapper.and(x -> x
					.eq(Reconciliation::getSignStatus,
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode())
					.or().eq(Reconciliation::getSignStatus,
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode()));
			reconciliationCountVo
					.setSaleRejected(repository.selectCount(wrapper));
		}

		return Optional.of(reconciliationCountVo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void handleDelAccounts(Reconciliation reconciliation) {
		Contract contract = contractService
				.findOne(reconciliation.getContractId()).orElse(null);
		if (Objects.nonNull(contract)) {
			// 合同为先货后款的对账单状态为已作废时 删除供应链端对账单生成的应收款单
			if (ContractDef.SettleWay.GOODS_FIRST
					.match(contract.getSettleWay())) {
				List<Accounts> accountsList = accountsService
						.findByRecId(reconciliation.getId());
				List<String> accountsIds = accountsList.stream()
						.map(Accounts::getId).toList();
				if (CollectionUtils.isNotEmpty(accountsIds)) {
					accountsService.batchDelete(accountsIds);
				}
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void handleAccounts(Reconciliation reconciliation,
			List<Order> orders, List<DeliverGoodsVo> deliverGoodsVoList) {
		// 根据发货信息 被关联的签收单ids
		List<String> contractIds = orders.stream().map(Order::getContractId)
				.distinct().toList();
		List<Contract> contracts = contractService.findByIds(contractIds);
		Map<String, Contract> contractMap = contracts.stream().collect(
				Collectors.toMap(Contract::getId, contract -> contract));
		Contract contact = contractService
				.findOne(reconciliation.getContractId()).orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30151001));
		// 合同为先货后款的已完成对账单，对账单内关联的订单，关联一个订单生成一个应付款，多个订单生成多个应付款
		if (ContractDef.SettleWay.GOODS_FIRST.match(contact.getSettleWay())) {
			// 根据vo找出所有发货列表
			List<DeliverGoods> deliverGoodsList = deliverGoodsVoList.stream()
					.map(DeliverGoodsVo::getDeliverGoods).toList();
			// 船运单列表
			List<TransportOrderShip> shipList = deliverGoodsVoList.stream()
					.map(DeliverGoodsVo::getTransportOrderShips)
					.filter(com.baomidou.mybatisplus.core.toolkit.CollectionUtils::isNotEmpty)
					.flatMap(Collection::stream).toList();
			// 将船运单根据发货id进行分组
			Map<String, List<TransportOrderShip>> transportOrderShipMap = shipList
					.stream().collect(Collectors
							.groupingBy(TransportOrderShip::getGoodsId));
			// 汽运单列表
			List<TransportOrderVehicle> vehicleList = deliverGoodsVoList
					.stream().map(DeliverGoodsVo::getTransportOrderVehicles)
					.filter(com.baomidou.mybatisplus.core.toolkit.CollectionUtils::isNotEmpty)
					.flatMap(Collection::stream).toList();
			// 将汽运单根据发货id进行分组
			Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = vehicleList
					.stream().collect(Collectors.groupingBy(
							TransportOrderVehicle::getDeliverGoodsId));
			// 铁路单列表
			List<TransportOrderRailway> railwayList = deliverGoodsVoList
					.stream().map(DeliverGoodsVo::getTransportOrderRailways)
					.filter(com.baomidou.mybatisplus.core.toolkit.CollectionUtils::isNotEmpty)
					.flatMap(Collection::stream).toList();
			// 将铁路单根据发货id进行分组
			Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = railwayList
					.stream().collect(Collectors.groupingBy(
							TransportOrderRailway::getDeliverGoodsId));
			// 将订单ids关联的发货单根据订单进行分组
			Map<String, List<DeliverGoods>> deliverGoodsMap = deliverGoodsList
					.stream()
					.collect(Collectors.groupingBy(DeliverGoods::getOrderId));
			// 关联了对账单，并状态为完成的收款单
			List<Payment> paymentList = paymentService.findByRecIdAndAccId(
					reconciliation.getId(),
					PaymentDef.State.COMPLETED.getCode(), null, null);
			// 已收款金额
			BigDecimal receiptedAmount = paymentList.stream().reduce(
					BigDecimal.ZERO, (sum, item) -> sum.add(item.getAmount()),
					BigDecimal::add);
			// 循环对账单里面的订单，生成应收/付款单
			for (Order order : orders) {
				List<Accounts> accountsList = accountsService
						.findByOrderId(order.getId(), reconciliation.getId());
				// 该对账单里面的订单没有应收款数据时 才生成应收应付款记录
				if (CollectionUtils.isEmpty(accountsList)) {
					// 订单关联的合同
					Contract contract1 = contractMap.get(order.getContractId());
					Accounts accounts = new Accounts();
					accounts.setProjectId(order.getProjectId());
					accounts.setProjectName(order.getProjectName());
					accounts.setContractId(order.getContractId());
					accounts.setPurchaseDate(order.getApplyDate());
					if (Objects.nonNull(contract1)) {
						accounts.setContractName(contract1.getName());
					}
					accounts.setOrderId(order.getId());
					accounts.setReconciliationId(reconciliation.getId());
					accounts.setPaymentId(reconciliation.getPurchaserId());
					accounts.setPaymentBusinessId(
							reconciliation.getPurchaserBusinessId());
					accounts.setPaymentEnterprise(
							reconciliation.getPurchaserEnterprise());
					// 设置类型
					accounts.setType(reconciliation.getType());
					accounts.setReceiptId(reconciliation.getSellerId());
					accounts.setReceiptBusinessId(
							reconciliation.getSellerBusinessId());
					accounts.setReceiptEnterprise(
							reconciliation.getSellerEnterprise());
					// 应收/应付 金额
					BigDecimal amount = BigDecimal.ZERO;
					// 订单关联的发货信息
					List<DeliverGoods> deliverGoodsList1 = deliverGoodsMap
							.get(order.getId());
					// 循环发货信息计算小计之和为 应收/付款金额
					if (CollectionUtils.isNotEmpty(deliverGoodsList1)) {
						for (DeliverGoods deliverGoods : deliverGoodsList1) {
							// 发货方式为自提
							if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
									.match(deliverGoods.getDelivery())) {
								List<GoodsInfo> goodsInfos = this
										.convertGoodsInfo(
												deliverGoods.getGoodsInfo());
								// 多条签收时金额循环发货信息计算
								if (DeliverGoodsDef.ReceiveWay.SEPARATE
										.match(deliverGoods.getReceiveWay())) {
									for (GoodsInfo goodsInfo : goodsInfos) {
										if (Objects.nonNull(goodsInfo
												.getReconciliationSubtotal())) {
											amount = amount.add(goodsInfo
													.getReconciliationSubtotal());
										}
									}
								}
								// 合并签收的 只要取第1条货物信息的小计
								else {
									if (CollectionUtils
											.isNotEmpty(goodsInfos)) {
										if (Objects.nonNull(goodsInfos.get(0)
												.getReconciliationSubtotal())) {
											amount = amount.add(goodsInfos
													.get(0)
													.getReconciliationSubtotal());
										}
									}
								}
							}

							// 发货方式为船运
							else if (DeliverGoodsDef.DeliverWay.SHIPPING
									.match(deliverGoods.getDelivery())) {
								// 发货单对应的船运单
								List<TransportOrderShip> transportOrderShipList = transportOrderShipMap
										.get(deliverGoods.getId());
								// 多条签收的循环船运单信息
								if (DeliverGoodsDef.ReceiveWay.SEPARATE
										.match(deliverGoods.getReceiveWay())) {
									for (TransportOrderShip transportOrderShip : transportOrderShipList) {
										if (Objects.nonNull(transportOrderShip
												.getReconciliationSubtotal())) {
											amount = amount
													.add(transportOrderShip
															.getReconciliationSubtotal());
										}
									}
								}
								// 合并签收
								else {
									if (CollectionUtils.isNotEmpty(
											transportOrderShipList)) {
										if (Objects.nonNull(
												transportOrderShipList.get(0)
														.getReconciliationSubtotal())) {
											amount = amount
													.add(transportOrderShipList
															.get(0)
															.getReconciliationSubtotal());
										}
									}
								}
							}
							// 发货方式为汽运
							else if (DeliverGoodsDef.DeliverWay.CAR
									.match(deliverGoods.getDelivery())) {
								// 发货单对应的汽运单
								List<TransportOrderVehicle> transportOrderVehicleList = transportOrderVehicleMap
										.get(deliverGoods.getId());
								// 多条签收的循环汽运单信息
								if (DeliverGoodsDef.ReceiveWay.SEPARATE
										.match(deliverGoods.getReceiveWay())) {
									for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicleList) {
										if (Objects
												.nonNull(transportOrderVehicle
														.getReconciliationSubtotal())) {
											amount = amount
													.add(transportOrderVehicle
															.getReconciliationSubtotal());
										}
									}
								}
								// 合并签收
								else {
									if (CollectionUtils.isNotEmpty(
											transportOrderVehicleList)) {
										if (Objects.nonNull(
												transportOrderVehicleList.get(0)
														.getReconciliationSubtotal())) {
											amount = amount.add(
													transportOrderVehicleList
															.get(0)
															.getReconciliationSubtotal());
										}
									}
								}
							}
							// 发货方式为铁路
							else if (DeliverGoodsDef.DeliverWay.TRAIN
									.match(deliverGoods.getDelivery())) {
								// 发货单对应的铁路单
								List<TransportOrderRailway> transportOrderRailwayList = transportOrderRailwayMap
										.get(deliverGoods.getId());
								// 多条签收的循环铁炉单信息
								if (DeliverGoodsDef.ReceiveWay.SEPARATE
										.match(deliverGoods.getReceiveWay())) {
									for (TransportOrderRailway transportOrderRailway : transportOrderRailwayList) {
										if (Objects
												.nonNull(transportOrderRailway
														.getReconciliationSubtotal())) {
											amount = amount
													.add(transportOrderRailway
															.getReconciliationSubtotal());
										}
									}
								}
								// 合并签收
								else {
									if (CollectionUtils.isNotEmpty(
											transportOrderRailwayList)) {
										if (Objects.nonNull(
												transportOrderRailwayList.get(0)
														.getReconciliationSubtotal())) {
											amount = amount.add(
													transportOrderRailwayList
															.get(0)
															.getReconciliationSubtotal());
										}
									}
								}
							}
						}

					}
					accounts.setAmount(amount);
					accounts.setReceiptedAmount(receiptedAmount);
					accounts.setUnReceiptAmount(
							amount.subtract(receiptedAmount));
					// 未收款金额小于0时设置为0
					if (accounts.getUnReceiptAmount()
							.compareTo(BigDecimal.ZERO) < 0) {
						accounts.setUnReceiptAmount(BigDecimal.ZERO);
					}
					// 合同账期起始方式为签收确认日期时 ，账期为 对应订单的签收单签收最新日期（多条签收单取最新日期）加上账期
					if (ContractDef.SettleStartWith.SIGNATURE_CONFIRMATION
							.match(contact.getSettleStartWith())) {
						List<DeliverGoods> deliverGoodsLists = deliverGoodsMap
								.get(order.getId());
						List<String> signReceiptIds = deliverGoodsLists.stream()
								.map(DeliverGoods::getSignReceiptId).distinct()
								.toList();
						List<SignReceipt> signReceiptList = signReceiptService
								.findByIds(signReceiptIds);
						if (CollectionUtils.isNotEmpty(signReceiptList)) {
							SignReceipt signReceipt = signReceiptList.stream()
									.max(Comparator.comparing(
											SignReceipt::getSignConfirmDate))
									.orElse(null);
							if (Objects.nonNull(signReceipt)) {
								if (Objects.nonNull(
										signReceipt.getSignConfirmDate())) {
									accounts.setSettleEndDate(signReceipt
											.getSignConfirmDate()
											.plusDays(order.getSettlePeriod()));
								}
							}
						}
					}
					// 合同账期起始方式为采购确认日期时，账期为 对应订单关联的采购订单关联的付款单的付货款日期加上账期
					else {
						if (Objects.nonNull(order.getRelatedBuyOrderId())) {
							List<Payment> payments = paymentService
									.findByOrderIds(
											List.of(order
													.getRelatedBuyOrderId()),
											new ArrayList<>(),
											PaymentDef.CostType.GOODS_PAYMENT
													.getCode());
							// 对应多条付款单时 取第一条创建的付款单
							if (CollectionUtils.isNotEmpty(payments)) {
								Payment payment = payments.stream()
										.min(Comparator.comparing(
												Payment::getCreatedTime))
										.orElse(null);
								if (Objects.nonNull(payment)) {
									// 若关联了这个销售订单的采购订单的货款付款日期加账期天数后的日期超过关联合同的到期日期，需要取合同的到期日期。
									LocalDateTime settleEndDate = payment
											.getPaymentDate()
											.plusDays(order.getSettlePeriod());
									accounts.setSettleEndDate(settleEndDate);

								}

							}
						}
					}
					if (Objects.nonNull(accounts.getSettleEndDate())) {
						// 账期超过关联合同的到期日期，需要取合同的到期日期。
						if (accounts.getSettleEndDate()
								.isAfter(contract1.getFinishDate())) {
							accounts.setSettleEndDate(
									contract1.getFinishDate());
						}
						// 启用了工作日配置时 账期截止日期是周末或节假日，需要取下一个工作日的日期。
						if (workDayProperties.getEnabled()) {
							// 处理工作日
							LocalDateTime finalSettleEndDate = this
									.handleWorkDay(accounts.getSettleEndDate());
							accounts.setSettleEndDate(finalSettleEndDate);
						}
						// 设置账期的状态
						// 未收款金额大于0
						if ((accounts.getUnReceiptAmount()
								.compareTo(BigDecimal.ZERO) > 0)) {
							// 且 当前日期 大于 账期截止日期 --状态 已逾期
							if ((LocalDateTime.now().toLocalDate())
									.isAfter(accounts.getSettleEndDate()
											.toLocalDate())) {
								accounts.setStatus(
										AccountsDef.Status.DELAY.getCode());
							}
							// 当前日期 不大于 账期截止日期 --状态 未结清
							else {
								accounts.setStatus(
										AccountsDef.Status.UNSETTLED.getCode());
							}
						}
						// 未收款金额等于0 --状态 已结清
						else if ((accounts.getUnReceiptAmount()
								.compareTo(BigDecimal.ZERO) == 0)) {
							accounts.setStatus(
									AccountsDef.Status.SETTLED.getCode());
						}
					} else {
						// 没有账期截止日期的就是未结清
						accounts.setStatus(
								AccountsDef.Status.UNSETTLED.getCode());
					}
					accounts.setType(reconciliation.getType());
					accountsService.create(accounts);
				}
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void handleBuyAccounts(Reconciliation reconciliation,
			List<Order> orders, List<DeliverGoodsVo> deliverGoodsVoList) {
		// 根据发货信息 被关联的签收单ids
		List<String> contractIds = orders.stream().map(Order::getContractId)
				.distinct().toList();
		List<Contract> contracts = contractService.findByIds(contractIds);
		Map<String, Contract> contractMap = contracts.stream().collect(
				Collectors.toMap(Contract::getId, contract -> contract));
		Contract contact = contractService
				.findOne(reconciliation.getContractId()).orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30151001));
		// 合同为先货后款的已完成对账单，对账单内关联的订单，关联一个订单生成一个应付款，多个订单生成多个应付款
		if (ContractDef.SettleWay.GOODS_FIRST.match(contact.getSettleWay())) {
			// 根据vo找出所有发货列表
			List<DeliverGoods> deliverGoodsList = deliverGoodsVoList.stream()
					.map(DeliverGoodsVo::getDeliverGoods).toList();
			// 将签收单ids关联的发货单根据订单进行分组
			Map<String, List<DeliverGoods>> deliverGoodsMap = deliverGoodsList
					.stream()
					.collect(Collectors.groupingBy(DeliverGoods::getOrderId));
			// 船运单列表
			List<TransportOrderShip> shipList = deliverGoodsVoList.stream()
					.map(DeliverGoodsVo::getTransportOrderShips)
					.filter(com.baomidou.mybatisplus.core.toolkit.CollectionUtils::isNotEmpty)
					.flatMap(Collection::stream).toList();
			// 将船运单根据发货id进行分组
			Map<String, List<TransportOrderShip>> transportOrderShipMap = shipList
					.stream().collect(Collectors
							.groupingBy(TransportOrderShip::getGoodsId));
			// 汽运单列表
			List<TransportOrderVehicle> vehicleList = deliverGoodsVoList
					.stream().map(DeliverGoodsVo::getTransportOrderVehicles)
					.filter(com.baomidou.mybatisplus.core.toolkit.CollectionUtils::isNotEmpty)
					.flatMap(Collection::stream).toList();
			// 将汽运单根据发货id进行分组
			Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = vehicleList
					.stream().collect(Collectors.groupingBy(
							TransportOrderVehicle::getDeliverGoodsId));
			// 铁路单列表
			List<TransportOrderRailway> railwayList = deliverGoodsVoList
					.stream().map(DeliverGoodsVo::getTransportOrderRailways)
					.filter(com.baomidou.mybatisplus.core.toolkit.CollectionUtils::isNotEmpty)
					.flatMap(Collection::stream).toList();
			// 将铁路单根据发货id进行分组
			Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = railwayList
					.stream().collect(Collectors.groupingBy(
							TransportOrderRailway::getDeliverGoodsId));
			// 关联了对账单，并状态为完成的收款单
			List<Payment> paymentList = paymentService.findByRecIdAndAccId(
					reconciliation.getId(),
					PaymentDef.State.COMPLETED.getCode(), null, null);
			// 已收款金额
			BigDecimal receiptedAmount = paymentList.stream().reduce(
					BigDecimal.ZERO, (sum, item) -> sum.add(item.getAmount()),
					BigDecimal::add);
			// 循环对账单里面的订单，生成应收/付款单
			for (Order order : orders) {
				List<Accounts> accountsList = accountsService
						.findByOrderId(order.getId(), reconciliation.getId());
				// 该对账单的订单没有应收款数据时 才生成应收应付款记录
				if (CollectionUtils.isEmpty(accountsList)) {
					// 订单关联的合同
					Contract contract1 = contractMap.get(order.getContractId());
					Accounts accounts = new Accounts();
					accounts.setProjectId(order.getProjectId());
					accounts.setProjectName(order.getProjectName());
					accounts.setContractId(order.getContractId());
					accounts.setPurchaseDate(order.getApplyDate());
					if (Objects.nonNull(contract1)) {
						accounts.setContractName(contract1.getName());
					}
					accounts.setOrderId(order.getId());
					accounts.setReconciliationId(reconciliation.getId());

					accounts.setPaymentId(reconciliation.getPurchaserId());
					accounts.setPaymentBusinessId(
							reconciliation.getPurchaserBusinessId());
					accounts.setPaymentEnterprise(
							reconciliation.getPurchaserEnterprise());
					// 设置类型
					accounts.setType(reconciliation.getType());

					accounts.setReceiptId(reconciliation.getSellerId());
					accounts.setReceiptBusinessId(
							reconciliation.getSellerBusinessId());
					accounts.setReceiptEnterprise(
							reconciliation.getSellerEnterprise());

					// 对账的结算预付款
					BigDecimal settlementPayment = BigDecimal.ZERO;
					if (Objects
							.nonNull(reconciliation.getSettlementPayment())) {
						settlementPayment = reconciliation
								.getSettlementPayment();
					}
					// 应收/应付 金额
					BigDecimal amount = BigDecimal.ZERO;
					// 订单关联的发货信息
					List<DeliverGoods> deliverGoodsList1 = deliverGoodsMap
							.get(order.getId());
					// 循环发货信息计算小计之和为 应收/付款金额
					if (CollectionUtils.isNotEmpty(deliverGoodsList1)) {
						for (DeliverGoods deliverGoods : deliverGoodsList1) {
							// 录入企业的信息存在goodsInfo里面
							if (Objects.isNull(
									contract1.getUpstreamSuppliersId())) {
								List<GoodsInfo> goodsInfos = this
										.convertGoodsInfo(
												deliverGoods.getGoodsInfo());
								// 多条签收时金额循环发货信息计算
								if (DeliverGoodsDef.ReceiveWay.SEPARATE
										.match(deliverGoods.getReceiveWay())) {
									for (GoodsInfo goodsInfo : goodsInfos) {
										if (Objects.nonNull(goodsInfo
												.getReconciliationSubtotal())) {
											amount = amount.add(goodsInfo
													.getReconciliationSubtotal());
										}
									}
								}
								// 合并签收的 只要取第1条货物信息的小计
								else {
									if (CollectionUtils
											.isNotEmpty(goodsInfos)) {
										if (Objects.nonNull(goodsInfos.get(0)
												.getReconciliationSubtotal())) {
											amount = amount.add(goodsInfos
													.get(0)
													.getReconciliationSubtotal());
										}
									}
								}
							} else {
								// 发货方式为自提
								if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
										.match(deliverGoods.getDelivery())) {
									List<GoodsInfo> goodsInfos = this
											.convertGoodsInfo(deliverGoods
													.getGoodsInfo());
									// 多条签收时金额循环发货信息计算
									if (DeliverGoodsDef.ReceiveWay.SEPARATE
											.match(deliverGoods
													.getReceiveWay())) {
										for (GoodsInfo goodsInfo : goodsInfos) {
											if (Objects.nonNull(goodsInfo
													.getReconciliationSubtotal())) {
												amount = amount.add(goodsInfo
														.getReconciliationSubtotal());
											}
										}
									}
									// 合并签收的 只要取第1条货物信息的小计
									else {
										if (CollectionUtils
												.isNotEmpty(goodsInfos)) {
											if (Objects.nonNull(goodsInfos
													.get(0)
													.getReconciliationSubtotal())) {
												amount = amount.add(goodsInfos
														.get(0)
														.getReconciliationSubtotal());
											}
										}
									}
								}

								// 发货方式为船运
								else if (DeliverGoodsDef.DeliverWay.SHIPPING
										.match(deliverGoods.getDelivery())) {
									// 发货单对应的船运单
									List<TransportOrderShip> transportOrderShipList = transportOrderShipMap
											.get(deliverGoods.getId());
									// 多条签收的循环船运单信息
									if (DeliverGoodsDef.ReceiveWay.SEPARATE
											.match(deliverGoods
													.getReceiveWay())) {
										for (TransportOrderShip transportOrderShip : transportOrderShipList) {
											if (Objects
													.nonNull(transportOrderShip
															.getReconciliationSubtotal())) {
												amount = amount
														.add(transportOrderShip
																.getReconciliationSubtotal());
											}
										}
									}
									// 合并签收
									else {
										if (CollectionUtils.isNotEmpty(
												transportOrderShipList)) {
											if (Objects.nonNull(
													transportOrderShipList
															.get(0)
															.getReconciliationSubtotal())) {
												amount = amount.add(
														transportOrderShipList
																.get(0)
																.getReconciliationSubtotal());
											}
										}
									}
								}
								// 发货方式为汽运
								else if (DeliverGoodsDef.DeliverWay.CAR
										.match(deliverGoods.getDelivery())) {
									// 发货单对应的汽运单
									List<TransportOrderVehicle> transportOrderVehicleList = transportOrderVehicleMap
											.get(deliverGoods.getId());
									// 多条签收的循环汽运单信息
									if (DeliverGoodsDef.ReceiveWay.SEPARATE
											.match(deliverGoods
													.getReceiveWay())) {
										for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicleList) {
											if (Objects.nonNull(
													transportOrderVehicle
															.getReconciliationSubtotal())) {
												amount = amount.add(
														transportOrderVehicle
																.getReconciliationSubtotal());
											}
										}
									}
									// 合并签收
									else {
										if (CollectionUtils.isNotEmpty(
												transportOrderVehicleList)) {
											if (Objects.nonNull(
													transportOrderVehicleList
															.get(0)
															.getReconciliationSubtotal())) {
												amount = amount.add(
														transportOrderVehicleList
																.get(0)
																.getReconciliationSubtotal());
											}
										}
									}
								}
								// 发货方式为铁路
								else if (DeliverGoodsDef.DeliverWay.TRAIN
										.match(deliverGoods.getDelivery())) {
									// 发货单对应的铁路单
									List<TransportOrderRailway> transportOrderRailwayList = transportOrderRailwayMap
											.get(deliverGoods.getId());
									// 多条签收的循环铁炉单信息
									if (DeliverGoodsDef.ReceiveWay.SEPARATE
											.match(deliverGoods
													.getReceiveWay())) {
										for (TransportOrderRailway transportOrderRailway : transportOrderRailwayList) {
											if (Objects.nonNull(
													transportOrderRailway
															.getReconciliationSubtotal())) {
												amount = amount.add(
														transportOrderRailway
																.getReconciliationSubtotal());
											}
										}
									}
									// 合并签收
									else {
										if (CollectionUtils.isNotEmpty(
												transportOrderRailwayList)) {
											if (Objects.nonNull(
													transportOrderRailwayList
															.get(0)
															.getReconciliationSubtotal())) {
												amount = amount.add(
														transportOrderRailwayList
																.get(0)
																.getReconciliationSubtotal());
											}
										}
									}
								}
							}
						}

					}
					// 应付款金额=订单对应的实际对账金额-结算预付款
					accounts.setAmount(amount.subtract(settlementPayment));
					accounts.setReceiptedAmount(receiptedAmount);
					accounts.setUnReceiptAmount(
							amount.subtract(receiptedAmount));
					// 未收款金额小于0时设置为0
					if (accounts.getUnReceiptAmount()
							.compareTo(BigDecimal.ZERO) < 0) {
						accounts.setUnReceiptAmount(BigDecimal.ZERO);
					}
					// 应付款关联对账单的对账日期加上账期
					LocalDateTime settleEndDate = reconciliation
							.getReconciliationDate()
							.plusDays(reconciliation.getSettlePeriod());
					accounts.setSettleEndDate(settleEndDate);
					if (Objects.nonNull(accounts.getSettleEndDate())) {
						// 设置账期的状态
						// 未收款金额大于0
						if ((accounts.getUnReceiptAmount()
								.compareTo(BigDecimal.ZERO) > 0)) {
							// 且 当前日期 大于 账期截止日期 --状态 已逾期
							if ((LocalDateTime.now().toLocalDate())
									.isAfter(accounts.getSettleEndDate()
											.toLocalDate())) {
								accounts.setStatus(
										AccountsDef.Status.DELAY.getCode());
							}
							// 当前日期 不大于 账期截止日期 --状态 未结清
							else {
								accounts.setStatus(
										AccountsDef.Status.UNSETTLED.getCode());
							}
						}
						// 未收款金额等于0 --状态 已结清
						else if ((accounts.getUnReceiptAmount()
								.compareTo(BigDecimal.ZERO) == 0)) {
							accounts.setStatus(
									AccountsDef.Status.SETTLED.getCode());
						}
					} else {
						// 没有账期截止日期的就是未结清
						accounts.setStatus(
								AccountsDef.Status.UNSETTLED.getCode());
					}
					// 设置类型
					accounts.setType(reconciliation.getType());
					accountsService.create(accounts);
				}
			}
		}
	}

	// 处理工作日
	private LocalDateTime handleWorkDay(LocalDateTime settleEndDate) {
		// 将LocalDateTime转换为Date（这里会丢失时分秒信息)
		Date dateBeforeConversion = Date
				.from(settleEndDate.atZone(ZoneId.systemDefault()).toInstant());
		try {
			// 获取当前类的类加载器
			ClassLoader classLoader = getClass().getClassLoader();
			// 尝试获取resources文件夹中某个文件的URL
			URL resourcesDirUrl = classLoader.getResource("");
			if (resourcesDirUrl != null) {
				String resourcesDirPathStr = resourcesDirUrl.getPath();
				// 去除末尾的斜杠（如果是Windows路径，可能需要其他处理）
				if (resourcesDirPathStr.endsWith("/")) {
					resourcesDirPathStr = resourcesDirPathStr.substring(0,
							resourcesDirPathStr.length() - 1);
				}
				// 调用getLatestWorkDay方法（获取最近的一个工作日日期)
				Date latestWorkDayDate = HolidayUtils.getLatestWorkDay(
						resourcesDirPathStr, dateBeforeConversion);
				System.out.println("latestWorkDayDate:  " + latestWorkDayDate);
				// 将返回的Date对象转换回ZonedDateTime（需要指定时区）
				ZonedDateTime zonedDateTime = latestWorkDayDate.toInstant()
						.atZone(ZoneId.systemDefault());
				// 为了保持原始的时分秒信息，我们需要从原始的LocalDateTime中获取这些信息
				LocalDateTime finalLocalDateTime = LocalDateTime.of(
						zonedDateTime.toLocalDate(),
						settleEndDate.toLocalTime());
				// 返回最终的工作日日期
				System.out.println("finalLocalDateTime: " + finalLocalDateTime);
				return finalLocalDateTime;
			}
			return null;
		} catch (Exception e) {
			log.error("调用获取工作日接口异常 -> {}", e.getMessage(), e);
			throw new BadRequestException(ErrorCode.CODE_30154024);
		}
	}

	/**
	 * 封装订单里面的对账明细和项目里面的对账明细的对账单vo信息
	 *
	 * @param
	 * @return
	 */
	private List<ReconciliationVo> packVos(List<Reconciliation> reconciliations,
			String orderId, Integer type) {
		List<ReconciliationVo> vos = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(reconciliations)) {
			// 项目信息
			List<String> projectIds = reconciliations.stream()
					.map(Reconciliation::getProjectId).distinct().toList();
			Map<String, Project> projectMap = projectService
					.findByIds(projectIds).stream()
					.collect(Collectors.toMap(Project::getId, e -> e));
			// 合同信息
			List<String> contractIds = reconciliations.stream()
					.map(Reconciliation::getContractId).distinct().toList();
			Map<String, Contract> contractMap = contractService
					.findByIds(contractIds).stream()
					.collect(Collectors.toMap(Contract::getId, e -> e));
			// 订单数量/重量
			BigDecimal orderReceiptWeight = BigDecimal.ZERO;
			// 订单对账金额
			BigDecimal orderReconAmount = BigDecimal.ZERO;
			// 订单预对账金额
			BigDecimal orderPreReconAmount = BigDecimal.ZERO;
			for (Reconciliation reconciliation : reconciliations) {
				ReconciliationVo vo = new ReconciliationVo();
				vo.setReconciliation(reconciliation);
				// 采购对账并且是录入企业时 对账信息直接存在提货里面
				if (ReconciliationDef.Type.BUY.match(type)
						&& CommonDef.Symbol.YES
								.match(reconciliation.getIsRecord())) {
					// 找到对账里面这个订单对应的发货信息
					List<DeliverGoods> deliverGoodsList = deliverGoodsService
							.findByOrderIdAndSignReceiptIds(orderId,
									reconciliation.getReceiptIds());
					for (DeliverGoods deliverGoods : deliverGoodsList) {
						// 如果发货方式为合并发货，则订单数量/重量为发货信息里面的 签收数量/重量之和
						if (DeliverGoodsDef.ReceiveWay.MERGE
								.match(deliverGoods.getReceiveWay())) {
							orderReceiptWeight = orderReceiptWeight
									.add(Objects.requireNonNullElse(
											deliverGoods.getReceiptWeight(),
											BigDecimal.ZERO));
							// 获取合并签收订单对账金额
							orderReconAmount = this.getOrderRecAmount(
									deliverGoods, orderReconAmount);
						}
						// 分开签收的
						else {
							// 采购对账单关联的订单 里面的货物信息
							List<GoodsInfo> goodsInfos = this.convertGoodsInfo(
									deliverGoods.getGoodsInfo());
							// 订单数量/重量 为订单货物信息里面的 签收数量/重量之和
							BigDecimal orderReceiptWeight1 = goodsInfos.stream()
									.map(goodsInfo -> Objects
											.requireNonNullElse(goodsInfo
													.getReceiptQuantity(),
													BigDecimal.ZERO))
									.reduce(BigDecimal.ZERO, BigDecimal::add);
							orderReceiptWeight = orderReceiptWeight
									.add(orderReceiptWeight1);
							// 订单对账金额 为订单货物信息里面的 对账小计之和
							BigDecimal orderReconAmount1 = goodsInfos.stream()
									.map(goodsInfo -> Objects
											.requireNonNullElse(goodsInfo
													.getReconciliationSubtotal(),
													BigDecimal.ZERO))
									.reduce(BigDecimal.ZERO, BigDecimal::add);
							orderReconAmount = orderReconAmount
									.add(orderReconAmount1);
						}
					}

				}
				// 销售项目 对账信息存在发货信息里面
				else {
					// 找到对账里面这个订单对应的发货信息
					List<DeliverGoods> deliverGoodsList = deliverGoodsService
							.findByOrderIdAndSignReceiptIds(orderId,
									reconciliation.getReceiptIds());
					List<String> deliverGoodsIds = deliverGoodsList.stream()
							.map(DeliverGoods::getId).distinct().toList();
					// 船运单信息
					List<TransportOrderShip> transportOrderShips = transportOrderShipService
							.findByDeliverGoodsIds(deliverGoodsIds);
					Map<String, List<TransportOrderShip>> transportOrderShipMap = transportOrderShips
							.stream().collect(Collectors.groupingBy(
									TransportOrderShip::getGoodsId));
					// 汽运运单信息
					List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
							.findByDeliverGoodsIds(deliverGoodsIds);
					Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = transportOrderVehicles
							.stream().collect(Collectors.groupingBy(
									TransportOrderVehicle::getDeliverGoodsId));
					// 铁路单信息
					List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
							.findByDeliverGoodsIds(deliverGoodsIds);
					Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = transportOrderRailways
							.stream().collect(Collectors.groupingBy(
									TransportOrderRailway::getDeliverGoodsId));
					// 循环发货信息
					for (DeliverGoods deliverGoods : deliverGoodsList) {
						// 如果发货方式为合并发货，则订单数量/重量为发货信息里面的 签收数量/重量之和
						if (DeliverGoodsDef.ReceiveWay.MERGE
								.match(deliverGoods.getReceiveWay())) {
							orderReceiptWeight = orderReceiptWeight
									.add(Objects.requireNonNullElse(
											deliverGoods.getReceiptWeight(),
											BigDecimal.ZERO));
							// 获取合并签收订单对账金额
							orderReconAmount = getOrderReconAmount(deliverGoods,
									orderReconAmount,
									transportOrderShipMap
											.get(deliverGoods.getId()),
									transportOrderVehicleMap
											.get(deliverGoods.getId()),
									transportOrderRailwayMap
											.get(deliverGoods.getId()));
							// 获取合并签收订单对账金额
							orderPreReconAmount = getOrderPreReconAmount(
									deliverGoods, orderPreReconAmount,
									transportOrderShipMap
											.get(deliverGoods.getId()),
									transportOrderVehicleMap
											.get(deliverGoods.getId()),
									transportOrderRailwayMap
											.get(deliverGoods.getId()));
						}
						// 如果发货方式为多条签收，则订单数量/重量为发货信息里面的 货物信息里面的 签收数量/重量之和
						else {
							if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
									.match(deliverGoods.getDelivery())) {
								List<GoodsInfo> goodsInfos = this
										.convertGoodsInfo(
												deliverGoods.getGoodsInfo());
								// 循环发货信息里面的货物信息
								for (GoodsInfo goodsInfo : goodsInfos) {
									// 将货物信息里面的 签收数量相加
									orderReceiptWeight = orderReceiptWeight.add(
											Objects.requireNonNullElse(goodsInfo
													.getReceiptQuantity(),
													BigDecimal.ZERO));
									// 将货物信息里面的 对账小计相加
									orderReconAmount = orderReconAmount.add(
											Objects.requireNonNullElse(goodsInfo
													.getReconciliationSubtotal(),
													BigDecimal.ZERO));
									// 将货物信息里面的 预对账小计相加
									orderPreReconAmount = orderPreReconAmount
											.add(Objects.requireNonNullElse(
													goodsInfo
															.getPreReconciliationSubtotal(),
													BigDecimal.ZERO));
								}
							} else if (DeliverGoodsDef.DeliverWay.SHIPPING
									.match(deliverGoods.getDelivery())) {
								List<TransportOrderShip> transportOrderShipList = transportOrderShipMap
										.get(deliverGoods.getId());
								if (CollectionUtils
										.isNotEmpty(transportOrderShipList)) {
									// 循环发货信息里面的货物信息
									for (TransportOrderShip transportOrderShip : transportOrderShipList) {
										// 将货物信息里面的 签收数量相加
										orderReceiptWeight = orderReceiptWeight
												.add(Objects.requireNonNullElse(
														transportOrderShip
																.getReceiptQuantity(),
														BigDecimal.ZERO));
										// 将货物信息里面的 对账小计相加
										orderReconAmount = orderReconAmount
												.add(Objects.requireNonNullElse(
														transportOrderShip
																.getReconciliationSubtotal(),
														BigDecimal.ZERO));
										// 将货物信息里面的 预对账小计相加
										orderPreReconAmount = orderPreReconAmount
												.add(Objects.requireNonNullElse(
														transportOrderShip
																.getPreReconciliationSubtotal(),
														BigDecimal.ZERO));
									}
								}
							} else if (DeliverGoodsDef.DeliverWay.CAR
									.match(deliverGoods.getDelivery())) {
								List<TransportOrderVehicle> transportOrderVehicleList = transportOrderVehicleMap
										.get(deliverGoods.getId());
								if (CollectionUtils.isNotEmpty(
										transportOrderVehicleList)) {
									// 循环发货信息里面的货物信息
									for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicleList) {
										// 将货物信息里面的 签收数量相加
										orderReceiptWeight = orderReceiptWeight
												.add(Objects.requireNonNullElse(
														transportOrderVehicle
																.getReceiptQuantity(),
														BigDecimal.ZERO));
										// 将货物信息里面的 对账小计相加
										orderReconAmount = orderReconAmount
												.add(Objects.requireNonNullElse(
														transportOrderVehicle
																.getReconciliationSubtotal(),
														BigDecimal.ZERO));
										// 将货物信息里面的 预对账小计相加
										orderPreReconAmount = orderPreReconAmount
												.add(Objects.requireNonNullElse(
														transportOrderVehicle
																.getPreReconciliationSubtotal(),
														BigDecimal.ZERO));
									}
								}
							} else if (DeliverGoodsDef.DeliverWay.TRAIN
									.match(deliverGoods.getDelivery())) {
								List<TransportOrderRailway> transportOrderRailwayList = transportOrderRailwayMap
										.get(deliverGoods.getId());
								if (CollectionUtils.isNotEmpty(
										transportOrderRailwayList)) {
									// 循环发货信息里面的货物信息
									for (TransportOrderRailway transportOrderRailway : transportOrderRailwayList) {
										// 将货物信息里面的 签收数量相加
										orderReceiptWeight = orderReceiptWeight
												.add(Objects.requireNonNullElse(
														transportOrderRailway
																.getReceiptQuantity(),
														BigDecimal.ZERO));
										// 将货物信息里面的 对账小计相加
										orderReconAmount = orderReconAmount
												.add(Objects.requireNonNullElse(
														transportOrderRailway
																.getReconciliationSubtotal(),
														BigDecimal.ZERO));
										// 将货物信息里面的 预对账小计相加
										orderPreReconAmount = orderPreReconAmount
												.add(Objects.requireNonNullElse(
														transportOrderRailway
																.getPreReconciliationSubtotal(),
														BigDecimal.ZERO));
									}
								}
							}
						}
					}
				}
				// 如果对应的对账状态是已作废 则从快照信息里面处理
				if (ReconciliationDef.State.INVALID
						.match(reconciliation.getState())
						&& Objects.nonNull(orderId)) {
					// 订单数量/重量
					BigDecimal orderReceiptWeight1 = BigDecimal.ZERO;
					// 订单对账金额
					BigDecimal orderReconAmount1 = BigDecimal.ZERO;
					// 存在预对账并且已作废 则取预对账的数据
					if (CommonDef.Symbol.YES
							.match(reconciliation.getIsPreReconciliation())) {
						List<GoodsInfo> goodsInfoList = convertGoodsInfo(
								reconciliation.getPreDeliveredInfo());
						// 订单对应的货物信息
						List<GoodsInfo> goodsInfoList1 = goodsInfoList.stream()
								.filter(k -> k.getOrderId().equals(orderId))
								.toList();
						Map<String, List<GoodsInfo>> groupedByDeliverGoodsId = goodsInfoList1
								.stream().collect(Collectors.groupingBy(
										GoodsInfo::getDeliverGoodsId));
						for (int i = 0; i < groupedByDeliverGoodsId.entrySet()
								.size(); i++) {
							Map.Entry<String, List<GoodsInfo>> entry = groupedByDeliverGoodsId
									.entrySet().stream().skip(i).findFirst()
									.orElse(null);
							if (entry != null) {
								List<GoodsInfo> goodsInfoList2 = entry
										.getValue();
								// 循环发货信息里面的货物信息
								for (GoodsInfo goodsInfo : goodsInfoList2) {
									if (DeliverGoodsDef.ReceiveWay.MERGE
											.match(goodsInfo.getReceiptWay())) {
										orderReceiptWeight1 = orderReceiptWeight1
												.add(Objects.requireNonNullElse(
														goodsInfoList2.get(0)
																.getReceiptQuantity(),
														BigDecimal.ZERO));
										orderReconAmount1 = orderReconAmount1
												.add(Objects.requireNonNullElse(
														goodsInfoList2.get(0)
																.getPreReconciliationSubtotal(),
														BigDecimal.ZERO));
										break;
									} else {
										// 将货物信息里面的 签收数量相加
										orderReceiptWeight1 = orderReceiptWeight1
												.add(Objects.requireNonNullElse(
														goodsInfo
																.getReceiptQuantity(),
														BigDecimal.ZERO));
										// 将货物信息里面的 预对账小计相加
										orderReconAmount1 = orderReconAmount1
												.add(Objects.requireNonNullElse(
														goodsInfo
																.getPreReconciliationSubtotal(),
														BigDecimal.ZERO));
									}
								}
							}
						}
					} else {
						List<GoodsInfo> goodsInfoList = convertGoodsInfo(
								reconciliation.getDeliveredInfo());
						// 订单对应的货物信息
						List<GoodsInfo> goodsInfoList1 = goodsInfoList.stream()
								.filter(k -> k.getOrderId().equals(orderId))
								.toList();
						Map<String, List<GoodsInfo>> groupedByDeliverGoodsId = goodsInfoList1
								.stream().collect(Collectors.groupingBy(
										GoodsInfo::getDeliverGoodsId));
						for (int i = 0; i < groupedByDeliverGoodsId.entrySet()
								.size(); i++) {
							Map.Entry<String, List<GoodsInfo>> entry = groupedByDeliverGoodsId
									.entrySet().stream().skip(i).findFirst()
									.orElse(null);
							if (entry != null) {
								List<GoodsInfo> goodsInfoList2 = entry
										.getValue();
								// 循环发货信息里面的货物信息
								for (GoodsInfo goodsInfo : goodsInfoList2) {
									if (DeliverGoodsDef.ReceiveWay.MERGE
											.match(goodsInfo.getReceiptWay())) {
										orderReceiptWeight1 = orderReceiptWeight1
												.add(Objects.requireNonNullElse(
														goodsInfoList2.get(0)
																.getReceiptQuantity(),
														BigDecimal.ZERO));
										orderReconAmount1 = orderReconAmount1
												.add(Objects.requireNonNullElse(
														goodsInfoList2.get(0)
																.getReconciliationSubtotal(),
														BigDecimal.ZERO));

										break;

									} else {
										// 将货物信息里面的 签收数量相加
										orderReceiptWeight1 = orderReceiptWeight1
												.add(Objects.requireNonNullElse(
														goodsInfo
																.getReceiptQuantity(),
														BigDecimal.ZERO));
										// 将货物信息里面的 对账小计相加
										orderReconAmount1 = orderReconAmount1
												.add(Objects.requireNonNullElse(
														goodsInfo
																.getReconciliationSubtotal(),
														BigDecimal.ZERO));
									}
								}
							}
						}
					}
					vo.setOrderReceiptWeight(orderReceiptWeight1);
					vo.setOrderReconAmount(orderReconAmount1);
				} else {
					vo.setOrderReceiptWeight(orderReceiptWeight);
					if (CommonDef.Symbol.YES.match(
							reconciliation.getIsConductReconciliation())) {
						// 对账阶段设置对账的金额
						vo.setOrderReconAmount(orderReconAmount);
					} else {
						// 预对账阶段设置预对账金额
						vo.setOrderReconAmount(orderPreReconAmount);
					}
				}
				vo.setProject(projectMap.get(reconciliation.getProjectId()));
				vo.setContract(contractMap.get(reconciliation.getContractId()));
				vos.add(vo);
			}
		}
		return vos;
	}

	private BigDecimal getOrderReconAmount(DeliverGoods deliverGoods,
			BigDecimal orderReconAmount,
			List<TransportOrderShip> transportOrderShips,
			List<TransportOrderVehicle> transportOrderVehicles,
			List<TransportOrderRailway> transportOrderRailways) {
		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			List<GoodsInfo> goodsInfos = this
					.convertGoodsInfo(deliverGoods.getGoodsInfo());
			// 合并签收的只要取货物信息里面第一条的 对账小计
			if (CollectionUtils.isNotEmpty(goodsInfos)) {
				orderReconAmount = orderReconAmount
						.add(Objects.requireNonNullElse(
								goodsInfos.get(0).getReconciliationSubtotal(),
								BigDecimal.ZERO));
			}
		} else if (DeliverGoodsDef.DeliverWay.SHIPPING
				.match(deliverGoods.getDelivery())) {
			// 合并签收的只要取船运信息里面第一条的 对账小计
			if (CollectionUtils.isNotEmpty(transportOrderShips)) {
				orderReconAmount = orderReconAmount
						.add(Objects.requireNonNullElse(
								transportOrderShips.get(0)
										.getReconciliationSubtotal(),
								BigDecimal.ZERO));
			}
		} else if (DeliverGoodsDef.DeliverWay.CAR
				.match(deliverGoods.getDelivery())) {
			// 合并签收的只要取船运信息里面第一条的 对账小计
			if (CollectionUtils.isNotEmpty(transportOrderVehicles)) {
				orderReconAmount = orderReconAmount
						.add(Objects.requireNonNullElse(
								transportOrderVehicles.get(0)
										.getReconciliationSubtotal(),
								BigDecimal.ZERO));
			}
		} else if (DeliverGoodsDef.DeliverWay.TRAIN
				.match(deliverGoods.getDelivery())) {
			// 合并签收的只要取铁路单信息里面第一条的 对账小计
			if (CollectionUtils.isNotEmpty(transportOrderRailways)) {
				orderReconAmount = orderReconAmount
						.add(Objects.requireNonNullElse(
								transportOrderRailways.get(0)
										.getReconciliationSubtotal(),
								BigDecimal.ZERO));
			}
		}
		return orderReconAmount;
	}

	private BigDecimal getOrderPreReconAmount(DeliverGoods deliverGoods,
			BigDecimal orderPreReconAmount,
			List<TransportOrderShip> transportOrderShips,
			List<TransportOrderVehicle> transportOrderVehicles,
			List<TransportOrderRailway> transportOrderRailways) {
		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			List<GoodsInfo> goodsInfos = this
					.convertGoodsInfo(deliverGoods.getGoodsInfo());
			// 合并签收的只要取货物信息里面第一条的 预对账小计
			if (CollectionUtils.isNotEmpty(goodsInfos)) {
				orderPreReconAmount = orderPreReconAmount
						.add(Objects.requireNonNullElse(
								goodsInfos.get(0)
										.getPreReconciliationSubtotal(),
								BigDecimal.ZERO));
			}
		} else if (DeliverGoodsDef.DeliverWay.SHIPPING
				.match(deliverGoods.getDelivery())) {
			// 合并签收的只要取船运信息里面第一条的 预对账小计
			if (CollectionUtils.isNotEmpty(transportOrderShips)) {
				orderPreReconAmount = orderPreReconAmount
						.add(Objects.requireNonNullElse(
								transportOrderShips.get(0)
										.getPreReconciliationSubtotal(),
								BigDecimal.ZERO));
			}
		} else if (DeliverGoodsDef.DeliverWay.CAR
				.match(deliverGoods.getDelivery())) {
			// 合并签收的只要取船运信息里面第一条的 预对账小计
			if (CollectionUtils.isNotEmpty(transportOrderVehicles)) {
				orderPreReconAmount = orderPreReconAmount
						.add(Objects.requireNonNullElse(
								transportOrderVehicles.get(0)
										.getPreReconciliationSubtotal(),
								BigDecimal.ZERO));
			}
		} else if (DeliverGoodsDef.DeliverWay.TRAIN
				.match(deliverGoods.getDelivery())) {
			// 合并签收的只要取铁路单信息里面第一条的 预对账小计
			if (CollectionUtils.isNotEmpty(transportOrderRailways)) {
				orderPreReconAmount = orderPreReconAmount
						.add(Objects.requireNonNullElse(
								transportOrderRailways.get(0)
										.getPreReconciliationSubtotal(),
								BigDecimal.ZERO));
			}
		}
		return orderPreReconAmount;
	}

	private BigDecimal getOrderRecAmount(DeliverGoods deliverGoods,
			BigDecimal orderReconAmount) {
		List<GoodsInfo> goodsInfos = this
				.convertGoodsInfo(deliverGoods.getGoodsInfo());
		// 合并签收的只要取货物信息里面第一条的 对账小计
		if (CollectionUtils.isNotEmpty(goodsInfos)) {
			orderReconAmount = orderReconAmount.add(Objects.requireNonNullElse(
					goodsInfos.get(0).getReconciliationSubtotal(),
					BigDecimal.ZERO));
		}
		return orderReconAmount;
	}

	/**
	 * 封装销售对账单和pc端的vo信息
	 *
	 * @param reconciliation
	 * @return
	 */
	private ReconciliationVo packVo(Reconciliation reconciliation) {
		ReconciliationVo vo = new ReconciliationVo();
		vo.setReconciliation(reconciliation);
		// 设置签收信息
		vo.setSignReceiptList(
				signReceiptService.findByIds(reconciliation.getReceiptIds()));
		List<SignReceiptVo> signReceiptVos = new ArrayList<>();
		// 设置签收单信息
		for (String receiptId : reconciliation.getReceiptIds()) {
			signReceiptVos.add(signReceiptService.findVoById(receiptId));
		}
		vo.setSignReceiptVos(signReceiptVos);
		// 设置项目信息
		if (ObjectUtils.isNotEmpty(reconciliation.getProjectId())) {
			projectService.findOne(reconciliation.getProjectId())
					.ifPresent(vo::setProject);
		}
		contractService.findOne(reconciliation.getContractId())
				.ifPresent(vo::setContract);
		projectService.findOne(reconciliation.getProjectId())
				.ifPresent(vo::setProject);
		return vo;
	}

	/**
	 * 封装采购对账单详情的vo信息
	 *
	 * @param reconciliation
	 * @return
	 */
	private ReconciliationVo packBuyVo(Reconciliation reconciliation) {
		ReconciliationVo vo = new ReconciliationVo();
		vo.setReconciliation(reconciliation);
		// 设置签收信息
		vo.setSignReceiptList(
				signReceiptService.findByIds(reconciliation.getReceiptIds()));
		List<SignReceiptVo> signReceiptVos = new ArrayList<>();
		// 设置签收单信息
		for (String receiptId : reconciliation.getReceiptIds()) {
			signReceiptVos.add(signReceiptService.findVoById(receiptId));
		}
		vo.setSignReceiptVos(signReceiptVos);
		// 设置对账单据文件信息
		if (Objects.nonNull(reconciliation.getReconciliationFileId())) {
			fileService.findOne(reconciliation.getReconciliationFileId())
					.ifPresent(vo::setFile);
		}
		// 设置项目信息
		if (ObjectUtils.isNotEmpty(reconciliation.getProjectId())) {
			projectService.findOne(reconciliation.getProjectId())
					.ifPresent(vo::setProject);
		}
		contractService.findOne(reconciliation.getContractId())
				.ifPresent(vo::setContract);
		return vo;
	}

	/**
	 * 封装分页查询的对账单vo信息
	 *
	 * @param
	 * @return
	 */
	private List<ReconciliationVo> packPageVos(
			List<Reconciliation> reconciliations) {
		List<ReconciliationVo> vos = new ArrayList<>();
		// 合同信息
		List<String> contracts = reconciliations.stream()
				.map(Reconciliation::getContractId).distinct().toList();
		Map<String, Contract> contractMap = contractService.findByIds(contracts)
				.stream().collect(Collectors.toMap(Contract::getId, e -> e));
		// 项目信息
		List<String> projectIds = reconciliations.stream()
				.map(Reconciliation::getProjectId).distinct().toList();
		Map<String, Project> projectMap = projectService.findByIds(projectIds)
				.stream().collect(Collectors.toMap(Project::getId, e -> e));
		for (Reconciliation reconciliation : reconciliations) {
			ReconciliationVo vo = new ReconciliationVo();
			vo.setReconciliation(reconciliation);
			vo.setContract(contractMap.get(reconciliation.getContractId()));
			vo.setProject(projectMap.get(reconciliation.getProjectId()));
			vos.add(vo);
		}
		return vos;
	}

	/**
	 * 转换为货物信息
	 *
	 * @param data
	 * @return
	 */
	private List<GoodsInfo> convertGoodsInfo(String data) {
		Gson gson = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
		Type listType = new TypeToken<List<GoodsInfo>>() {
		}.getType();
		return gson.fromJson(data, listType);
	}

	/**
	 * 设置导出响应头
	 *
	 * @param response
	 */
	private void setExportResponseFields(HttpServletResponse response,
			String id) {
		response.setContentType("application/pdf");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(
				String.format("订单_%s_%s",
						DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
								.format(LocalDateTime.now()),
						id),
				StandardCharsets.UTF_8).replaceAll("\\+", "%20");
		response.setHeader("Content-disposition",
				"attachment;filename*=utf-8''" + fileName + ".pdf");
	}

	/**
	 * 生成pdf转化类方法
	 *
	 * @param reconciliationId
	 * @return
	 * @throws JsonProcessingException
	 */
	private GeneratPdfDto generatPdfDto(String reconciliationId) {
		ReconciliationVo reconciliationVo = this.findVoById(reconciliationId)
				.orElse(new ReconciliationVo());
		Reconciliation reconciliation = reconciliationVo.getReconciliation();
		Contract contract = reconciliationVo.getContract();
		// 拿项目里面的
		Project project = projectService.findOne(reconciliation.getProjectId())
				.orElse(new Project());
		// 对账单关联的订单单id
		List<String> orderIdList = reconciliation.getReceiptIds().stream()
				.distinct().toList();
		// 将订单id拼接成字符串 用顿号隔开
		StringJoiner joiner = new StringJoiner("、");
		for (String orderId : orderIdList) {
			joiner.add(orderId);
		}
		String orderIds = joiner.toString();
		// 对账单关联的签收单id
		List<String> receiptIdList = reconciliation.getReceiptIds().stream()
				.distinct().toList();
		// 根据签收单id查询发货信息中发货已完成的数据
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findBySignReceiptIds(receiptIdList).stream()
				.filter(e -> DeliverGoodsDef.Status.DELIVER_COMPLETE
						.match(e.getStatus()))
				.toList();
		// 发货单id
		List<String> deliverGoodsIds = deliverGoodsList.stream()
				.map(DeliverGoods::getId).distinct().toList();
		// 船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		Map<String, List<TransportOrderShip>> transportOrderShipMap = transportOrderShips
				.stream()
				.collect(Collectors.groupingBy(TransportOrderShip::getGoodsId));
		// 汽运单信息
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = transportOrderVehicles
				.stream().collect(Collectors
						.groupingBy(TransportOrderVehicle::getDeliverGoodsId));
		// 铁路单信息
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = transportOrderRailways
				.stream().collect(Collectors
						.groupingBy(TransportOrderRailway::getDeliverGoodsId));
		// pdf里面的货物信息
		List<GoodsInfo> goodsInfoList = new ArrayList<>();

		// 循环发货信息 设置货物信息里面的货物名称和单位
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			// 采购录入企业的发货信息全在goodsInfo里面
			if (ContractDef.ContractType.PURCHASE
					.match(contract.getContractType())
					&& Objects.isNull(contract.getUpstreamSuppliersId())) {
				this.handleGoodsInfo(deliverGoods, reconciliation, project,
						goodsInfoList);
			} else {
				// 发货方式为自提
				if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
						.match(deliverGoods.getDelivery())) {
					this.handleGoodsInfo(deliverGoods, reconciliation, project,
							goodsInfoList);
				}
				// 发货方式为船运
				else if (DeliverGoodsDef.DeliverWay.SHIPPING
						.match(deliverGoods.getDelivery())) {
					// 发货单对应的船运单
					List<TransportOrderShip> transportOrderShipList = transportOrderShipMap
							.get(deliverGoods.getId());
					// 多条签收的循环船运单信息
					if (DeliverGoodsDef.ReceiveWay.SEPARATE
							.match(deliverGoods.getReceiveWay())) {
						for (TransportOrderShip transportOrderShip : transportOrderShipList) {
							GoodsInfo goodsInfo = getGoodsInfo(
									transportOrderShip, reconciliation,
									project);
							goodsInfoList.add(goodsInfo);
						}
					}
					// 合并签收
					else {
						if (CollectionUtils
								.isNotEmpty(transportOrderShipList)) {
							// 循环货物信息的规格用;隔开
							String model = transportOrderShipList.stream()
									.map(TransportOrderShip::getModel)
									.collect(Collectors.joining(";"));
							GoodsInfo goodsInfo = new GoodsInfo();
							goodsInfo.setGoodsName(
									reconciliation.getGoodsName());
							goodsInfo.setModel(model);
							goodsInfo.setReceiptQuantity(
									deliverGoods.getReceiptWeight());
							goodsInfo.setUnit(project.getGoodsUnit());
							goodsInfo.setReconciliationUnitPrice(
									transportOrderShipList.get(0)
											.getReconciliationUnitPrice());
							goodsInfo.setReconciliationSubtotal(
									transportOrderShipList.get(0)
											.getReconciliationSubtotal());
							goodsInfo.setReconciliationRemark(
									transportOrderShipList.get(0)
											.getReconciliationRemark());
							goodsInfo.setPreReconciliationUnitPrice(
									transportOrderShipList.get(0)
											.getPreReconciliationUnitPrice());
							goodsInfo.setPreReconciliationSubtotal(
									transportOrderShipList.get(0)
											.getPreReconciliationSubtotal());
							goodsInfo.setPreReconciliationRemark(
									transportOrderShipList.get(0)
											.getPreReconciliationRemark());
							goodsInfoList.add(goodsInfo);
						}
					}
				}
				// 发货方式为汽运
				else if (DeliverGoodsDef.DeliverWay.CAR
						.match(deliverGoods.getDelivery())) {
					// 发货单对应的汽运单
					List<TransportOrderVehicle> transportOrderVehicleList = transportOrderVehicleMap
							.get(deliverGoods.getId());
					// 多条签收的循环汽运单信息
					if (DeliverGoodsDef.ReceiveWay.SEPARATE
							.match(deliverGoods.getReceiveWay())) {
						for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicleList) {
							GoodsInfo goodsInfo = getGoodsInfo(
									transportOrderVehicle, reconciliation,
									project);
							goodsInfoList.add(goodsInfo);
						}
					}
					// 合并签收
					else {
						if (CollectionUtils
								.isNotEmpty(transportOrderVehicleList)) {
							// 循环货物信息的规格用;隔开
							String model = transportOrderVehicleList.stream()
									.map(TransportOrderVehicle::getGoodsType)
									.collect(Collectors.joining(";"));
							GoodsInfo goodsInfo = new GoodsInfo();
							goodsInfo.setGoodsName(
									reconciliation.getGoodsName());
							goodsInfo.setModel(model);
							goodsInfo.setReceiptQuantity(
									deliverGoods.getReceiptWeight());
							goodsInfo.setUnit(project.getGoodsUnit());
							goodsInfo.setReconciliationUnitPrice(
									transportOrderVehicleList.get(0)
											.getReconciliationUnitPrice());
							goodsInfo.setReconciliationSubtotal(
									transportOrderVehicleList.get(0)
											.getReconciliationSubtotal());
							goodsInfo.setReconciliationRemark(
									transportOrderVehicleList.get(0)
											.getReconciliationRemark());
							goodsInfo.setPreReconciliationUnitPrice(
									transportOrderVehicleList.get(0)
											.getPreReconciliationUnitPrice());
							goodsInfo.setPreReconciliationSubtotal(
									transportOrderVehicleList.get(0)
											.getPreReconciliationSubtotal());
							goodsInfo.setPreReconciliationRemark(
									transportOrderVehicleList.get(0)
											.getPreReconciliationRemark());
							goodsInfoList.add((goodsInfo));
						}
					}
				}
				// 发货方式为铁路
				else if (DeliverGoodsDef.DeliverWay.TRAIN
						.match(deliverGoods.getDelivery())) {
					// 发货单对应的铁路单
					List<TransportOrderRailway> transportOrderRailwayList = transportOrderRailwayMap
							.get(deliverGoods.getId());
					// 多条签收的循环铁路单信息
					if (DeliverGoodsDef.ReceiveWay.SEPARATE
							.match(deliverGoods.getReceiveWay())) {
						for (TransportOrderRailway transportOrderRailway : transportOrderRailwayList) {
							GoodsInfo goodsInfo = getGoodsInfo(
									transportOrderRailway, reconciliation,
									project);
							goodsInfoList.add(goodsInfo);
						}
					}
					// 合并签收
					else {
						if (CollectionUtils
								.isNotEmpty(transportOrderRailwayList)) {
							// 循环货物信息的规格用;隔开
							String model = transportOrderRailwayList.stream()
									.map(TransportOrderRailway::getModel)
									.collect(Collectors.joining(";"));
							GoodsInfo goodsInfo = new GoodsInfo();
							goodsInfo.setGoodsName(
									reconciliation.getGoodsName());
							goodsInfo.setModel(model);
							goodsInfo.setReceiptQuantity(
									deliverGoods.getReceiptWeight());
							goodsInfo.setUnit(project.getGoodsUnit());
							goodsInfo.setReconciliationUnitPrice(
									transportOrderRailwayList.get(0)
											.getReconciliationUnitPrice());
							goodsInfo.setReconciliationSubtotal(
									transportOrderRailwayList.get(0)
											.getReconciliationSubtotal());
							goodsInfo.setReconciliationRemark(
									transportOrderRailwayList.get(0)
											.getReconciliationRemark());
							goodsInfo.setPreReconciliationUnitPrice(
									transportOrderRailwayList.get(0)
											.getPreReconciliationUnitPrice());
							goodsInfo.setPreReconciliationSubtotal(
									transportOrderRailwayList.get(0)
											.getPreReconciliationSubtotal());
							goodsInfo.setPreReconciliationRemark(
									transportOrderRailwayList.get(0)
											.getPreReconciliationRemark());
							goodsInfoList.add(goodsInfo);
						}
					}
				}
			}
		}
		GeneratPdfDto pdf = new GeneratPdfDto();
		pdf.setBillId(reconciliationId);
		pdf.setBuyerName(reconciliation.getPurchaserEnterprise().getName());
		pdf.setSellerName(reconciliation.getSellerEnterprise().getName());
		pdf.setContractName(contract.getName());
		DateTimeFormatter formatter = DateTimeFormatter
				.ofPattern("【yyyy】年【MM】月【dd】日");
		pdf.setContractSignDate(formatter.format(contract.getSignDate()));
		pdf.setContractId(contract.getId());
		pdf.setOrderId(orderIds);
		pdf.setGoodsInfoList(JSON.toJSONString(goodsInfoList));
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 对账阶段用对账日期
			pdf.setSignReceiptDate(
					formatter.format(reconciliation.getReconciliationDate()));
		} else {
			// 预对账阶段用预对账日期
			pdf.setSignReceiptDate(formatter
					.format(reconciliation.getPreReconciliationDate()));
		}
		return pdf;
	}

	// 处理存在GoodsInfo里面的对账信息
	private void handleGoodsInfo(DeliverGoods deliverGoods,
			Reconciliation reconciliation, Project project,
			List<GoodsInfo> goodsInfoList) {
		List<GoodsInfo> goodsInfos = this
				.convertGoodsInfo(deliverGoods.getGoodsInfo());
		// 多条签收的循环发货信信息的货物信息
		if (DeliverGoodsDef.ReceiveWay.SEPARATE
				.match(deliverGoods.getReceiveWay())) {
			for (GoodsInfo goodsInfo : goodsInfos) {
				goodsInfo.setGoodsName(reconciliation.getGoodsName());
				goodsInfo.setUnit(project.getGoodsUnit());
				goodsInfoList.add(goodsInfo);
			}
		}
		// 合并签收
		else {
			if (CollectionUtils.isNotEmpty(goodsInfos)) {
				// 循环货物信息的规格用;隔开
				String model = goodsInfos.stream().map(GoodsInfo::getModel)
						.collect(Collectors.joining(";"));
				GoodsInfo goodsInfo = new GoodsInfo();
				goodsInfo.setGoodsName(reconciliation.getGoodsName());
				goodsInfo.setModel(model);
				goodsInfo.setReceiptQuantity(deliverGoods.getReceiptWeight());
				goodsInfo.setUnit(project.getGoodsUnit());
				goodsInfo.setReconciliationUnitPrice(
						goodsInfos.get(0).getReconciliationUnitPrice());
				goodsInfo.setReconciliationSubtotal(
						goodsInfos.get(0).getReconciliationSubtotal());
				goodsInfo.setReconciliationRemark(
						goodsInfos.get(0).getReconciliationRemark());
				goodsInfo.setPreReconciliationUnitPrice(
						goodsInfos.get(0).getPreReconciliationUnitPrice());
				goodsInfo.setPreReconciliationSubtotal(
						goodsInfos.get(0).getPreReconciliationSubtotal());
				goodsInfo.setPreReconciliationRemark(
						goodsInfos.get(0).getPreReconciliationRemark());
				goodsInfoList.add(goodsInfo);
			}
		}
	}

	// 处理汽运单信息
	private GoodsInfo getGoodsInfo(TransportOrderVehicle transportOrderVehicle,
			Reconciliation reconciliation, Project project) {
		GoodsInfo goodsInfo = new GoodsInfo();
		// 对账单的货物名称
		goodsInfo.setGoodsName(reconciliation.getGoodsName());
		// 对账单的规格
		goodsInfo.setModel(transportOrderVehicle.getGoodsType());
		// 对账单的签收数量
		goodsInfo
				.setReceiptQuantity(transportOrderVehicle.getReceiptQuantity());
		// 对账单的单位
		goodsInfo.setUnit(project.getGoodsUnit());
		// 对账单价
		goodsInfo.setReconciliationUnitPrice(
				transportOrderVehicle.getReconciliationUnitPrice());
		// 对账小计
		goodsInfo.setReconciliationSubtotal(
				transportOrderVehicle.getReconciliationSubtotal());
		// 对账备注
		goodsInfo.setReconciliationRemark(
				transportOrderVehicle.getReconciliationRemark());
		// 预对账单价
		goodsInfo.setPreReconciliationUnitPrice(
				transportOrderVehicle.getPreReconciliationUnitPrice());
		// 预对账小计
		goodsInfo.setPreReconciliationSubtotal(
				transportOrderVehicle.getPreReconciliationSubtotal());
		// 预对账备注
		goodsInfo.setPreReconciliationRemark(
				transportOrderVehicle.getPreReconciliationRemark());
		return goodsInfo;
	}

	// 处理铁路单信息
	private GoodsInfo getGoodsInfo(TransportOrderRailway transportOrderRailway,
			Reconciliation reconciliation, Project project) {
		GoodsInfo goodsInfo = new GoodsInfo();
		// 对账单的货物名称
		goodsInfo.setGoodsName(reconciliation.getGoodsName());
		// 对账单的规格
		goodsInfo.setModel(transportOrderRailway.getModel());
		// 对账单的签收数量
		goodsInfo
				.setReceiptQuantity(transportOrderRailway.getReceiptQuantity());
		// 对账单的单位
		goodsInfo.setUnit(project.getGoodsUnit());
		// 对账单价
		goodsInfo.setReconciliationUnitPrice(
				transportOrderRailway.getReconciliationUnitPrice());
		// 对账小计
		goodsInfo.setReconciliationSubtotal(
				transportOrderRailway.getReconciliationSubtotal());
		// 对账备注
		goodsInfo.setReconciliationRemark(
				transportOrderRailway.getReconciliationRemark());
		// 预对账单价
		goodsInfo.setPreReconciliationUnitPrice(
				transportOrderRailway.getPreReconciliationUnitPrice());
		// 预对账小计
		goodsInfo.setPreReconciliationSubtotal(
				transportOrderRailway.getPreReconciliationSubtotal());
		// 预对账备注
		goodsInfo.setPreReconciliationRemark(
				transportOrderRailway.getPreReconciliationRemark());
		return goodsInfo;
	}

	// 处理船运单信息
	private GoodsInfo getGoodsInfo(TransportOrderShip transportOrderShip,
			Reconciliation reconciliation, Project project) {
		GoodsInfo goodsInfo = new GoodsInfo();
		// 对账单的货物名称
		goodsInfo.setGoodsName(reconciliation.getGoodsName());
		// 对账单的规格
		goodsInfo.setModel(transportOrderShip.getModel());
		// 对账单的签收数量
		goodsInfo.setReceiptQuantity(transportOrderShip.getReceiptQuantity());
		// 对账单的单位
		goodsInfo.setUnit(project.getGoodsUnit());
		// 对账单价
		goodsInfo.setReconciliationUnitPrice(
				transportOrderShip.getReconciliationUnitPrice());
		// 对账小计
		goodsInfo.setReconciliationSubtotal(
				transportOrderShip.getReconciliationSubtotal());
		// 对账备注
		goodsInfo.setReconciliationRemark(
				transportOrderShip.getReconciliationRemark());
		// 预对账单价
		goodsInfo.setPreReconciliationUnitPrice(
				transportOrderShip.getPreReconciliationUnitPrice());
		// 预对账小计
		goodsInfo.setPreReconciliationSubtotal(
				transportOrderShip.getPreReconciliationSubtotal());
		// 预对账备注
		goodsInfo.setPreReconciliationRemark(
				transportOrderShip.getPreReconciliationRemark());
		return goodsInfo;
	}

	/**
	 * @description: 获取文件id，生成pdf文件并上传文件服务器，返回文件id
	 * @param: [resource]
	 * @return: java.lang.Long
	 **/
	private Long getFileId(Reconciliation reconciliation) {
		// 生成pdf
		OutputStream outputStream = new ByteArrayOutputStream();
		GeneratPdfDto pdf = generatPdfDto(reconciliation.getId());
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 对账阶段生成对账单pdf
			PdfUtils.getPdf(TransactionDef.PdfType.RECONCILIATION, outputStream,
					pdf);
		} else {
			// 预对账阶段生成预对账单pdf
			PdfUtils.getPdf(TransactionDef.PdfType.PRE_RECONCILIATION,
					outputStream, pdf);
		}
		try {
			CustomMultipartFile convert = MultipartFileUtils.convert(
					outputStream, "对账单合同" + reconciliation.getId(),
					"对账单合同" + reconciliation.getId() + ".pdf", "text/plain");
			File file = fileService
					.upload(convert, "对账单合同" + reconciliation.getId() + ".pdf")
					.orElse(null);
			if (Objects.nonNull(file)) {
				return file.getId();
			}
		} catch (Exception e) {
			log.error("生成pdf文件失败", e);
		}
		return null;
	}

	// 将签收单的对账状态改成未对账 以及签收单所关联的发货信息的对账信息清空
	private void handSignReceipt(List<SignReceipt> oldSignReceiptList,
			Map<String, DeliverGoods> oldDeliverGoodsMap) {
		for (SignReceipt signReceipt : oldSignReceiptList) {
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
			// 签收单原来关联的提货单
			List<DeliverGoods> deliverGoodsList1 = deliverGoodsService
					.findByIds(signReceipt.getRelatedDeliverGoodsIds());
			// 循环原来的提货单
			for (DeliverGoods deliverGoods : deliverGoodsList1) {
				if (!oldDeliverGoodsMap.containsKey(deliverGoods.getId())) {
					oldDeliverGoodsMap.put(deliverGoods.getId(), deliverGoods);
				}
				// 将原来提货单的货物信息的取出来
				List<GoodsInfo> list = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				// 清空货物信息的对账信息
				this.clearGoodsInfo(list);
				// 将修改后的信息进行更新
				Gson gson = getGson();
				deliverGoods.setGoodsInfo(gson.toJson(list));
			}
		}
		List<DeliverGoods> oldDeliverGoods = new ArrayList<>(
				oldDeliverGoodsMap.values());
		// 将新旧发货信息进行更新
		deliverGoodsService.batchUpdate(oldDeliverGoods);
	}

	// 处理发货信息
	private void handDeliverGoods(List<DeliverGoods> deliverGoodsList,
			Integer isConductReconciliation) {
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			// 货物信息不为空且发货方式为自提时需要清空货物信息的数据
			if (Objects.nonNull(deliverGoods.getGoodsInfo())
					&& DeliverGoodsDef.DeliverWay.SELF_PICKUP
							.match(deliverGoods.getDelivery())) {
				List<GoodsInfo> list = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				// 没有进入对账阶段时 就是处于预对账阶段 清空预对账的数据
				if (!CommonDef.Symbol.YES.match(isConductReconciliation)) {
					for (GoodsInfo goodsInfo : list) {
						// 预对账单价和备注清空
						goodsInfo.setPreReconciliationUnitPrice(null);
						goodsInfo.setPreReconciliationRemark(null);
						goodsInfo.setPreReconciliationSubtotal(null);
					}
				} else {
					for (GoodsInfo goodsInfo : list) {
						// 对账单价和备注清空
						goodsInfo.setReconciliationUnitPrice(null);
						goodsInfo.setReconciliationRemark(null);
						goodsInfo.setReconciliationSubtotal(null);
					}
				}
				Gson gson = getGson();
				deliverGoods.setGoodsInfo(gson.toJson(list));
			}
		}
		deliverGoodsService.batchUpdate(deliverGoodsList);
	}

	// 处理船运单信息
	private void handTransportOrderShips(
			List<TransportOrderShip> transportOrderShipList,
			Integer isConductReconciliation) {
		// 没有进入对账阶段时 就是处于预对账阶段 清空预对账的数据
		if (!CommonDef.Symbol.YES.match(isConductReconciliation)) {
			for (TransportOrderShip transportOrderShip : transportOrderShipList) {
				// 预对账单价和备注清空
				transportOrderShip.setPreReconciliationUnitPrice(null);
				transportOrderShip.setPreReconciliationRemark(null);
				transportOrderShip.setPreReconciliationSubtotal(null);
			}
		} else {
			for (TransportOrderShip transportOrderShip : transportOrderShipList) {
				// 对账单价和备注清空
				transportOrderShip.setReconciliationUnitPrice(null);
				transportOrderShip.setReconciliationRemark(null);
				transportOrderShip.setReconciliationSubtotal(null);
			}
		}

		transportOrderShipService.batchUpdate(transportOrderShipList);
	}

	// 处理汽运单单信息
	private void handTransportOrderVehicles(
			List<TransportOrderVehicle> transportOrderVehicleList,
			Integer isConductReconciliation) {
		// 没有进入对账阶段时 就是处于预对账阶段 清空预对账的数据
		if (!CommonDef.Symbol.YES.match(isConductReconciliation)) {
			for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicleList) {
				// 预对账单价和备注清空
				transportOrderVehicle.setPreReconciliationUnitPrice(null);
				transportOrderVehicle.setPreReconciliationRemark(null);
				transportOrderVehicle.setPreReconciliationSubtotal(null);
			}
		} else {
			for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicleList) {
				// 对账单价和备注清空
				transportOrderVehicle.setReconciliationUnitPrice(null);
				transportOrderVehicle.setReconciliationRemark(null);
				transportOrderVehicle.setReconciliationSubtotal(null);
			}
		}
		transportOrderVehicleService.batchUpdate(transportOrderVehicleList);
	}

	// 处理铁路单信息
	private void handTransportOrderRailways(
			List<TransportOrderRailway> transportOrderRailwayList,
			Integer isConductReconciliation) {
		// 没有进入对账阶段时 就是处于预对账阶段 清空预对账的数据
		if (!CommonDef.Symbol.YES.match(isConductReconciliation)) {
			for (TransportOrderRailway transportOrderRailway : transportOrderRailwayList) {
				// 预对账单价和备注清空
				transportOrderRailway.setPreReconciliationUnitPrice(null);
				transportOrderRailway.setPreReconciliationRemark(null);
				transportOrderRailway.setPreReconciliationSubtotal(null);
			}
		} else {
			for (TransportOrderRailway transportOrderRailway : transportOrderRailwayList) {
				// 对账单价和备注清空
				transportOrderRailway.setReconciliationUnitPrice(null);
				transportOrderRailway.setReconciliationRemark(null);
				transportOrderRailway.setReconciliationSubtotal(null);
			}
		}
		transportOrderRailwayService.batchUpdate(transportOrderRailwayList);
	}

	// 清空采购发货的预对账信息
	private void clearPreGoodsInfo(List<GoodsInfo> list) {
		for (GoodsInfo goodsInfo : list) {
			// 预对账单价和备注清空
			goodsInfo.setPreReconciliationUnitPrice(null);
			goodsInfo.setPreReconciliationRemark(null);
			goodsInfo.setPreReconciliationSubtotal(null);
		}
	}

	// 清空采购发货的对账信息
	private void clearGoodsInfo(List<GoodsInfo> list) {
		for (GoodsInfo goodsInfo : list) {
			// 对账单价和备注清空
			goodsInfo.setReconciliationUnitPrice(null);
			goodsInfo.setReconciliationRemark(null);
			goodsInfo.setReconciliationSubtotal(null);
		}
	}

	private List<DeliverGoods> getDeliverGoodsList(
			List<DeliverGoodsVo> deliverGoodsVoList) {
		List<DeliverGoods> goodsList = new ArrayList<>();
		List<TransportOrderShip> transportOrderShipList = new ArrayList<>();
		List<TransportOrderVehicle> transportOrderVehicleList = new ArrayList<>();
		List<TransportOrderRailway> transportOrderRailwayList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(deliverGoodsVoList)) {
			for (DeliverGoodsVo deliverGoodsVo : deliverGoodsVoList) {
				goodsList.add(deliverGoodsVo.getDeliverGoods());
				if (CollectionUtils
						.isNotEmpty(deliverGoodsVo.getTransportOrderShips())) {
					transportOrderShipList
							.addAll(deliverGoodsVo.getTransportOrderShips());
				}
				if (CollectionUtils.isNotEmpty(
						deliverGoodsVo.getTransportOrderVehicles())) {
					transportOrderVehicleList
							.addAll(deliverGoodsVo.getTransportOrderVehicles());
				}
				if (CollectionUtils.isNotEmpty(
						deliverGoodsVo.getTransportOrderRailways())) {
					transportOrderRailwayList
							.addAll(deliverGoodsVo.getTransportOrderRailways());
				}
			}
			if (CollectionUtils.isNotEmpty(goodsList)) {
				// 更新船运单信息
				deliverGoodsService.batchUpdate(goodsList);
			}
			if (CollectionUtils.isNotEmpty(transportOrderShipList)) {
				// 更新船运单信息
				transportOrderShipService.batchUpdate(transportOrderShipList);
			}
			if (CollectionUtils.isNotEmpty(transportOrderVehicleList)) {
				// 更新汽运单信息
				transportOrderVehicleService
						.batchUpdate(transportOrderVehicleList);
			}
			if (CollectionUtils.isNotEmpty(transportOrderRailwayList)) {
				// 更新铁路单信息
				transportOrderRailwayService
						.batchUpdate(transportOrderRailwayList);
			}
		}
		return goodsList;
	}

	// 获取新增对账时给 订单签收 设置的状态
	private Integer getStateCode(Reconciliation reconciliation) {
		// 设置状态的code
		Integer stateCode;
		// 如果对账状态是对账完成了 说明采购方是录入企业
		if (ReconciliationDef.State.FINISHED.match(reconciliation.getState())) {
			// 新增对账时 设置订单签收的对账状态为对账完成
			stateCode = OrderDef.BusinessStatus.COMPLETED.getCode();
		} else {
			// 如果采购方不是录入企业
			// 新增预对账时 设置订单签收的对账状态为预对账
			if (CommonDef.Symbol.YES
					.match(reconciliation.getIsPreReconciliation())) {
				stateCode = OrderDef.BusinessStatus.PRE_RECONCILIATION
						.getCode();
			} else {
				// 新增对账时 设置订单签收的对账状态为对账中
				stateCode = OrderDef.BusinessStatus.IN_PROGRESS.getCode();
			}
		}
		return stateCode;
	}

	/**
	 * 发送短信
	 *
	 * @param reconciliation
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(Reconciliation reconciliation, String templateCode,
			String title, Integer type) {
		Customer customer = null;
		if (ReconciliationDef.Type.SELL.match(type)) {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(reconciliation.getPurchaserBusinessId())
					.orElse(null);
			if (Objects.nonNull(dealingsEnterprise)) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		} else {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(reconciliation.getSellerBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("settle_id", reconciliation.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.ORDER.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.RECONCILIATION_DETAIL_PAGE)
						.detailId(String.valueOf(reconciliation.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}
}
