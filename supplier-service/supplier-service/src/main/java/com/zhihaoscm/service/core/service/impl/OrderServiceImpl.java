package com.zhihaoscm.service.core.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.api.util.multipart.file.CustomMultipartFile;
import com.zhihaoscm.common.api.util.multipart.file.MultipartFileUtils;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.bean.dto.GeneratPdfDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.GoodsInfo;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.ContractVo;
import com.zhihaoscm.domain.bean.vo.DeliverGoodsVo;
import com.zhihaoscm.domain.bean.vo.OrderCountVo;
import com.zhihaoscm.domain.bean.vo.OrderVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.PdfUtils;
import com.zhihaoscm.domain.utils.ThreadPoolUtil;
import com.zhihaoscm.service.config.LocalDateTimeAdapter;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.OrderMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.AdminSealService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单服务
 */
@Slf4j
@Service
public class OrderServiceImpl extends
		MpStringIdBaseServiceImpl<Order, OrderMapper> implements OrderService {

	@Autowired
	private ProjectService projectService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private ContractService contractService;
	@Autowired
	private DeliverGoodsService deliverGoodsService;
	@Autowired
	private ContractRecordService contractRecordService;
	@Autowired
	private FileService fileService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private SignReceiptService signReceiptService;
	@Autowired
	private PaymentService paymentService;
	@Autowired
	private ReconciliationService reconciliationService;
	@Autowired
	private AdminSealService adminSealService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private TransportOrderShipService transportOrderShipService;
	@Autowired
	private TransportOrderVehicleService transportOrderVehicleService;
	@Autowired
	private BillPaymentService billPaymentService;
	@Autowired
	private TransportOrderRailwayService transportOrderRailwayService;
	@Autowired
	private OutboundService outboundService;
	@Autowired
	private ProjectItemService projectItemService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	public OrderServiceImpl(OrderMapper repository) {
		super(repository);
	}

	@Override
	public Page<OrderVo> paging(Integer page, Integer size, String key,
			Integer type, String sellerName, String purchaserName,
			Integer projectType, String goodsName, Integer deliveryStatus,
			List<Integer> receiveStatus, List<Integer> reconciliationStatus,
			List<Integer> invoiceStatus, LocalDateTime startTime,
			LocalDateTime endTime, List<Integer> state, String sortKey,
			String sortOrder, String projectId, Boolean hasAll, Long userId,
			String param) {
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (Objects.nonNull(userId)) {
			if (!hasAll) {
				// 处理人是自己在的
				List<String> projectIdList = projectService.findByUserId(userId,
						null);
				if (org.apache.commons.collections4.CollectionUtils
						.isNotEmpty(projectIdList)) {
					wrapper.in(Order::getProjectId, projectIdList);
				} else {
					return Page.of(page, size, 0);
				}
			}
		}
		this.filterDeleted(wrapper);
		if (Objects.nonNull(key)) {
			List<String> projectIds = projectService.findByNameLike(key)
					.stream().map(Project::getId).distinct().toList();
			wrapper.and(x -> x.like(Order::getId, key).or().in(
					CollectionUtils.isNotEmpty(projectIds), Order::getProjectId,
					projectIds));
		}
		// 签收编号或者合同名称
		if (Objects.nonNull(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(x -> x.like(Order::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Order::getContractId, contractIds));
		}
		if (Objects.nonNull(type)) {
			wrapper.eq(Order::getType, type);
		}
		// 根据采购方名称及货物名称查询
		List<Contract> contracts;
		contracts = contractService.find(purchaserName, goodsName, null,
				projectType, null);
		if (CollectionUtils.isEmpty(contracts)) {
			return new Page<>();
		}

		if (CollectionUtils.isNotEmpty(contracts)) {
			wrapper.in(Order::getContractId,
					contracts.stream().map(Contract::getId).toList());
		}

		wrapper.eq(Objects.nonNull(projectId), Order::getProjectId, projectId);

		wrapper.ge(Objects.nonNull(startTime), Order::getApplyDate, startTime);
		wrapper.le(Objects.nonNull(endTime), Order::getApplyDate, endTime);

		if (CollectionUtils.isNotEmpty(state)) {
			boolean sign = state.contains(OrderDef.Status.SIGNING.getCode());
			boolean toSign = state
					.contains(OrderDef.Status.TO_BE_SIGNED.getCode());
			state.removeIf(e -> e.equals(OrderDef.Status.SIGNING.getCode())
					|| e.equals(OrderDef.Status.TO_BE_SIGNED.getCode()));
			if (CollectionUtils.isNotEmpty(state)) {
				String stateStr = state.stream().map(Object::toString)
						.collect(Collectors.joining(","));
				if (!sign && !toSign) {
					wrapper.apply(" (status >> 16) in (" + stateStr + ")");
				} else if (!sign) {
					wrapper.apply(" ((status >> 16) in (" + stateStr
							+ ") or (status in (196608,196609) and type = 1) )");
				} else if (!toSign) {
					wrapper.apply(" ((status >> 16) in (" + stateStr
							+ ") or (status = 196610 and type =1) )");
				} else {
					wrapper.apply(" ((status >> 16) in (" + stateStr
							+ ") or (status in (196608,196609,196610) and type = 1) )");
				}
			} else {
				if (!sign && toSign) {
					wrapper.apply(
							" ((status in (196608,196609) and type = 1)) ");
				} else if (sign && !toSign) {
					wrapper.apply(" ((status = 196610) and type = 1) ");
				} else {
					wrapper.apply(
							" (status in (196608,196609,196610) and type = 1) ");
				}
			}
		} else {
			List<Integer> list = new ArrayList<>();
			if (Objects.equals(projectType, 1)) {
				// 采购类型的数据不需要查询签署中类型数据
				// list.add(OrderDef.Status.REVERTED.getCode());
				list.add(OrderDef.Status.SIGNING.getCode());
			} else {
				// 销售类型数据排除掉草稿类型数据，已驳回，签署中数据
				list.add(OrderDef.Status.DRAFT.getCode());
				list.add(OrderDef.Status.REVERTED.getCode());
				list.add(OrderDef.Status.SIGNING.getCode());
			}

			String stateStr = list.stream().map(Object::toString)
					.collect(Collectors.joining(","));
			if (Objects.isNull(type)) {
				wrapper.apply(" (((status >> 16) not in (" + stateStr
						+ ") or status is null ) or ((status >> 16) = 3 and type = 1)) ");
			} else {
				wrapper.apply(" ((status >> 16) not in (" + stateStr
						+ ") or status is null ) ");
			}
		}
		if (Objects.nonNull(deliveryStatus)) {
			if (CommonDef.Symbol.YES.match(deliveryStatus)) {
				wrapper.eq(Order::getDeliveryStatus,
						OrderDef.BusinessStatus.COMPLETED.getCode());
			} else {
				wrapper.ne(Order::getDeliveryStatus,
						OrderDef.BusinessStatus.COMPLETED.getCode());
			}
		}
		if (CollectionUtils.isNotEmpty(receiveStatus)) {
			wrapper.in(Order::getReceiveStatus, receiveStatus).eq(
					Order::getDeliveryStatus,
					OrderDef.BusinessStatus.COMPLETED.getCode());
		}
		if (CollectionUtils.isNotEmpty(reconciliationStatus)) {
			wrapper.in(Order::getReconciliationStatus, reconciliationStatus).eq(
					Order::getReceiveStatus,
					OrderDef.BusinessStatus.COMPLETED.getCode());
		}
		if (CollectionUtils.isNotEmpty(invoiceStatus)) {
			wrapper.in(Order::getInvoiceStatus, invoiceStatus).eq(
					Order::getReconciliationStatus,
					OrderDef.BusinessStatus.COMPLETED.getCode());
		}

		if (CollectionUtils.isNotEmpty(receiveStatus)
				|| CollectionUtils.isNotEmpty(reconciliationStatus)
				|| CollectionUtils.isNotEmpty(invoiceStatus)) {
			wrapper.apply(
					" (status >> 16) = " + OrderDef.Status.FINISHED.getCode());
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照创建时间降序排列
			if (ContractDef.Type.BUY.match(projectType)) {
				wrapper.last(
						"order by " + " apply_date desc ,created_time desc ");
			} else {
				wrapper.last("order by updated_time desc ");
			}
		}

		Page<Order> paging = repository.selectPage(new Page<>(page, size),
				wrapper);

		List<OrderVo> vos = this.packVos(paging.getRecords(), contracts);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<OrderVo> customPaging(Integer page, Integer size, String key,
			Integer type, Integer projectType, String goodsName,
			Integer takeDeliveryStatus, List<Integer> receiveStatus,
			List<Integer> reconciliationStatus, List<Integer> invoiceStatus,
			LocalDateTime startTime, LocalDateTime endTime, List<Integer> state,
			Long customerId, String sortKey, String sortOrder, String projectId,
			String purchaserName) {
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		if (Objects.nonNull(key)) {
			wrapper.and(i -> i.like(Order::getId, key).or()
					.like(Order::getContractName, key));
		}
		if (Objects.nonNull(type)) {
			wrapper.eq(Order::getType, type);
		}
		// 根据采购方名称及货物名称查询
		List<Contract> contracts;
		if (projectType == 1) {
			contracts = contractService.findUp(null, goodsName, null,
					projectType, customerId);
			// 当查询的是客户端的销售订单时，只查询196608待签署，196609待确认，262145确认中，327683已完成，458752作废中，524288已作废的数据
			List<Integer> stateList = new ArrayList<>();
			stateList.add(OrderDef.Status.TO_BE_SIGNED.getCode());
			stateList.add(OrderDef.Status.SIGNING.getCode());
			stateList.add(OrderDef.Status.CONFIRMING.getCode());
			stateList.add(OrderDef.Status.FINISHED.getCode());
			stateList.add(OrderDef.Status.INVALIDING.getCode());
			stateList.add(OrderDef.Status.INVALID.getCode());

			String stateListStr = stateList.stream().map(Object::toString)
					.collect(Collectors.joining(","));
			wrapper.apply(" (status >> 16) in (" + stateListStr + ")");

		} else {
			contracts = contractService.find(null, goodsName, null, projectType,
					customerId);
		}
		if (CollectionUtils.isEmpty(contracts)) {
			return new Page<>();
		}

		if (CollectionUtils.isNotEmpty(contracts)) {
			wrapper.in(Order::getContractId,
					contracts.stream().map(Contract::getId).toList());
		}

		wrapper.eq(Objects.nonNull(projectId), Order::getProjectId, projectId);

		wrapper.ge(Objects.nonNull(startTime), Order::getApplyDate, startTime);
		wrapper.le(Objects.nonNull(endTime), Order::getApplyDate, endTime);

		if (CollectionUtils.isNotEmpty(state)) {
			boolean sign = state.contains(OrderDef.Status.SIGNING.getCode());
			boolean toSign = state
					.contains(OrderDef.Status.TO_BE_SIGNED.getCode());
			state.removeIf(e -> e.equals(OrderDef.Status.SIGNING.getCode())
					|| e.equals(OrderDef.Status.TO_BE_SIGNED.getCode()));
			if (CollectionUtils.isNotEmpty(state)) {
				String stateStr = state.stream().map(Object::toString)
						.collect(Collectors.joining(","));
				if (!sign && !toSign) {
					wrapper.apply(" (status >> 16) in (" + stateStr + ")");
				} else if (!sign) {
					wrapper.apply(" ((status >> 16) in (" + stateStr
							+ ") or (status in (196608,196609) ) )");
				} else if (!toSign) {
					wrapper.apply(" ((status >> 16) in (" + stateStr
							+ ") or (status = 196609) )");
				} else {
					wrapper.apply(" ((status >> 16) in (" + stateStr
							+ ") or (status in (196608,196609,196610)) )");
				}
			} else {
				if (!sign && toSign) {
					wrapper.apply(" (status in (196608,196610)) ");
				} else if (sign && !toSign) {
					wrapper.apply(" (status = 196609) ");
				} else {
					wrapper.apply(" (status in (196608,196609,196610)) ");
				}
			}
		}
		// 采购方名称
		if (StringUtils.isNotBlank(purchaserName)) {
			wrapper.apply(
					"JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%')",
					purchaserName);
		}
		if (Objects.nonNull(takeDeliveryStatus)) {
			if (CommonDef.Symbol.YES.match(takeDeliveryStatus)) {
				wrapper.eq(Order::getDeliveryStatus,
						OrderDef.BusinessStatus.COMPLETED.getCode());
			} else {
				wrapper.ne(Order::getDeliveryStatus,
						OrderDef.BusinessStatus.COMPLETED.getCode());
			}
		}
		if (CollectionUtils.isNotEmpty(receiveStatus)) {
			wrapper.in(Order::getReceiveStatus, receiveStatus).eq(
					Order::getDeliveryStatus,
					OrderDef.BusinessStatus.COMPLETED.getCode());
		}
		if (CollectionUtils.isNotEmpty(reconciliationStatus)) {
			wrapper.in(Order::getReconciliationStatus, reconciliationStatus).eq(
					Order::getReceiveStatus,
					OrderDef.BusinessStatus.COMPLETED.getCode());
		}
		if (CollectionUtils.isNotEmpty(invoiceStatus)) {
			wrapper.in(Order::getInvoiceStatus, invoiceStatus).eq(
					Order::getReconciliationStatus,
					OrderDef.BusinessStatus.COMPLETED.getCode());
		}
		if (CollectionUtils.isNotEmpty(receiveStatus)
				|| CollectionUtils.isNotEmpty(reconciliationStatus)
				|| CollectionUtils.isNotEmpty(invoiceStatus)) {
			wrapper.apply(
					" (status >> 16) = " + OrderDef.Status.FINISHED.getCode());
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照创建时间降序排列
			wrapper.last("order by updated_time desc ");
		}

		Page<Order> paging = repository.selectPage(new Page<>(page, size),
				wrapper);

		List<OrderVo> vos = this.packVos(paging.getRecords(), contracts);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<Order> selectorAssociated(Integer page, Integer size,
			String projectId, String contractId, String orderId) {
		if (StringUtils.isBlank(contractId)) {
			return new Page<>();
		}
		Contract contract = contractService.findOne(contractId).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30151001));
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(projectId), Order::getProjectId, projectId)
				.eq(StringUtils.isNotBlank(contractId), Order::getContractId,
						contractId)
				.like(StringUtils.isNotBlank(orderId), Order::getId, orderId);
		if (ContractDef.Type.SELL.match(contract.getContractType())) {
			// 已完成状态
			wrapper.apply(" (status = 327683) ");
		}
		// 默认按更新时间倒序
		wrapper.orderByDesc(Order::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	/**
	 * 根据ID查询订单
	 *
	 * @param id
	 *            订单ID
	 * @return 订单
	 */
	@Override
	public OrderVo findVoById(String id) {
		return this.findOne(id).stream().map(e -> {
			OrderVo orderVo = new OrderVo();
			// 设置已收到的订单保证金
			List<Payment> payments = paymentService.find(e.getId(),
					PaymentDef.CostType.ORDER_DEPOSIT.getCode(),
					PaymentDef.State.COMPLETED.getCode());
			if (CollectionUtils.isNotEmpty(payments)) {
				orderVo.setReceivedOrderDeposit(
						payments.stream().reduce(BigDecimal.ZERO,
								(sum, item) -> sum.add(item.getAmount()),
								BigDecimal::add));
			}
			orderVo.setOrder(e);
			orderVo.setProject(projectService.findOne(e.getProjectId())
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30152013)));
			orderVo.setContract(contractService.findOne(e.getContractId())
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30151001)));
			return orderVo;
		}).findFirst().orElseThrow(null);
	}

	@Override
	public List<Order> findByContractId(String contractId) {
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Order::getContractId, contractId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Order> findUnfinished(String projectId) {
		// 销售合同id
		List<Contract> contracts = contractService.find(null, null, null,
				ContractDef.Type.SELL.getCode(), null);
		List<String> contractIds = contracts.stream().map(Contract::getId)
				.toList();
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Order::getProjectId, projectId);
		if (CollectionUtils.isNotEmpty(contractIds)) {
			wrapper.in(Order::getContractId, contractIds);
			wrapper.ne(Order::getStatus, 327683);
			return repository.selectList(wrapper);
		} else {
			return Collections.emptyList();
		}
	}

	@Override
	public List<Order> findByPurchaserIdAndState(Long customId, Integer state) {
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(customId), Order::getPurchaserId, customId);
		wrapper.apply(" (status >> 16) in (" + state + ")");
		return repository.selectList(wrapper);
	}

	@Override
	public List<Order> findByContractIds(List<String> contractIds) {
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		wrapper.in(Order::getContractId, contractIds);
		return repository.selectList(wrapper);
	}

	/**
	 * 合同关联的订单下拉列表
	 *
	 * @return
	 */
	@Override
	public List<OrderVo> findByContractIdAndStatus(String reconciliationId,
			String contractId, List<Integer> receiveStatus,
			List<Integer> reconciliationStatus, List<String> orderIds) {
		List<Reconciliation> reconciliations = reconciliationService
				.findByContractId(contractId);
		// 合同关联的对账单里面找出订单id
		List<String> orderIdsList = reconciliations.stream().filter(
				e -> !ReconciliationDef.State.INVALID.match(e.getState()))
				.map(Reconciliation::getOrderIds).filter(Objects::nonNull)
				.flatMap(arrayString -> Arrays
						.stream(arrayString.toArray(new String[0])))
				.toList();
		// 根据合同id找出除了已完成和已作废的签收单
		List<SignReceipt> signReceipts = signReceiptService.findNotInStatus(
				contractId, List.of(SignReceiptDef.Status.FINISHED.getCode(),
						SignReceiptDef.Status.INVALID.getCode()));
		// 签收单对应的发货单
		List<String> signDeliverGoodsIdList = signReceipts.stream()
				.map(SignReceipt::getRelatedDeliverGoodsIds)
				.filter(Objects::nonNull).flatMap(arrayString -> Arrays
						.stream(arrayString.toArray(new String[0])))
				.toList();
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByIds(signDeliverGoodsIdList);
		// 发货单对应的订单
		List<String> deliverOrderIdsList = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).filter(Objects::nonNull)
				.toList();
		// 合同关联的订单列表（已经去除掉已经关联对账单的订单)
		LambdaQueryWrapper<Order> queryWrapper = Wrappers
				.lambdaQuery(Order.class);
		queryWrapper.eq(Order::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Order::getContractId, contractId);
		queryWrapper.in(CollectionUtils.isNotEmpty(receiveStatus),
				Order::getReceiveStatus, receiveStatus);
		queryWrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				Order::getReconciliationStatus, reconciliationStatus);
		queryWrapper.notIn(CollectionUtils.isNotEmpty(orderIdsList),
				Order::getId, orderIdsList);
		queryWrapper.notIn(CollectionUtils.isNotEmpty(deliverOrderIdsList),
				Order::getId, deliverOrderIdsList);
		// 合同关联的订单列表
		List<Order> orders = repository.selectList(queryWrapper);
		List<String> relatedOrderIds = new ArrayList<>();
		if (Objects.nonNull(reconciliationId)) {
			Reconciliation reconciliation = reconciliationService
					.findOne(reconciliationId)
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30154008));
			// 该对账单关联的订单id
			relatedOrderIds = reconciliation.getOrderIds().stream().toList();
		}
		List<String> combinedReceiptIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(orderIds)
				&& CollectionUtils.isNotEmpty(relatedOrderIds)) {
			// 将orderIds和relatedOrderIds 合并成一个列表
			combinedReceiptIds = Stream
					.concat(orderIds.stream(), relatedOrderIds.stream())
					.toList();
		}
		if (CollectionUtils.isNotEmpty(relatedOrderIds)) {
			combinedReceiptIds = relatedOrderIds;
		}
		if (CollectionUtils.isNotEmpty(orderIds)) {
			combinedReceiptIds = orderIds;
		}
		// 根据combinedReceiptIds查询的订单列表
		List<Order> orderList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(combinedReceiptIds)) {
			orderList = this.findByIds(combinedReceiptIds);
		}
		// orders和orderList合并为一个列表，并去重
		Map<String, Order> uniqueOrdersMap = Stream
				.concat(orders.stream(), orderList.stream())
				.collect(Collectors.toMap(Order::getId, sr -> sr,
						(existing, replacement) -> existing));
		// 将Map中的值转换回list
		List<Order> uniqueOrders = uniqueOrdersMap.values().stream().toList();
		return this.packOrders(uniqueOrders);

	}

	/**
	 * @description: 合同内全部有发货单(状态:已完成)且未签收的订单
	 * @author: 彭湃
	 * @date: 2025/1/18 16:34
	 * @param: []
	 * @return: java.util.List<com.zhihaoscm.domain.bean.entity.Order>
	 **/
	@Override
	public List<OrderVo> findUnReceiptOrders(String contractId) {
		List<Order> byContractId = this.findByContractId(contractId);
		if (CollectionUtils.isEmpty(byContractId)) {
			return List.of();
		}
		List<DeliverGoods> unReceipt = deliverGoodsService
				.findUnReceipt(contractId);
		if (CollectionUtils.isEmpty(unReceipt)) {
			return List.of();
		}
		List<String> list = unReceipt.stream().map(DeliverGoods::getOrderId)
				.distinct().toList();
		List<DeliverGoodsVo> deliverGoodsVos = this
				.packDeliverGoodsVos(unReceipt);
		List<Order> orderList = this.findByIds(list);
		orderList = orderList.stream()
				.filter(e -> Objects.equals(BusinessContractDef.SET_STATE.apply(
						OrderDef.Status.FINISHED.getCode(),
						BusinessContractDef.CommonSignState.COMPLETED
								.getCode()),
						e.getStatus()))
				.toList();
		Map<String, List<DeliverGoodsVo>> collect = deliverGoodsVos.stream()
				.collect(Collectors
						.groupingBy(e -> e.getDeliverGoods().getOrderId()));
		List<OrderVo> vos = orderList.stream().map(e -> {
			OrderVo orderVo = new OrderVo();
			orderVo.setOrder(e);
			orderVo.setDeliverGoodsVoList(collect.get(e.getId()));
			if (CollectionUtils.isNotEmpty(collect.get(e.getId()))) {
				BigDecimal sum = collect.get(e.getId()).stream().map(
						k -> k.getDeliverGoods().getActualGoodsTotalQuantity())
						.reduce(BigDecimal.ZERO, BigDecimal::add);
				orderVo.setUnReceiptWeight(sum);
			}
			return orderVo;
		}).toList();
		if (CollectionUtils.isEmpty(vos)) {
			return List.of();
		}
		// 过滤调未签收数量为空（没有发货单）的数据
		return vos.stream().filter(e -> Objects.nonNull(e.getUnReceiptWeight()))
				.distinct().toList();
	}

	/**
	 * @description: 采购管理中新增签收单查询未完成签收的订单
	 * @author: pp
	 * @date: 2025/2/6 10:38
	 * @param: [contractId]
	 * @return: java.util.List<com.zhihaoscm.domain.bean.entity.Order>
	 **/
	@Override
	public List<OrderVo> findUnReceiptBuyOrders(String contractId) {
		List<Order> orderList = this.findByContractId(contractId);
		if (CollectionUtils.isEmpty(orderList)) {
			return List.of();
		}
		List<String> orderIds = orderList.stream().map(Order::getId).distinct()
				.toList();
		List<DeliverGoods> deliverGoods = deliverGoodsService
				.findByOrderIds(orderIds);
		deliverGoods = deliverGoods.stream()
				.filter(e -> Objects.isNull(e.getSignReceiptId())).toList();
		if (CollectionUtils.isEmpty(deliverGoods)) {
			return List.of();
		}
		List<String> orderIdList = deliverGoods.stream()
				.map(DeliverGoods::getOrderId).distinct().toList();
		orderList = orderList.stream()
				.filter(e -> orderIdList.contains(e.getId())).toList();

		Map<String, List<DeliverGoods>> collect = deliverGoods.stream()
				.collect(Collectors.groupingBy(DeliverGoods::getOrderId));

		return orderList.stream().map(e -> {
			OrderVo orderVo = new OrderVo();
			orderVo.setOrder(e);
			orderVo.setDeliverGoodsList(collect.get(e.getId()));
			if (CollectionUtils.isNotEmpty(collect.get(e.getId()))) {
				BigDecimal sum = collect.get(e.getId()).stream()
						.map(DeliverGoods::getGoodsTotalQuantity)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
				orderVo.setUnReceiptWeight(sum);
			}
			return orderVo;
		}).toList();
	}

	/**
	 * @description: 采购管理中修改签收时查询未完成签收的订单
	 * @author: pp
	 * @date: 2025/2/6 10:38
	 * @param: [signReceiptId,
	 *             orderIds]
	 * @return: java.util.List<com.zhihaoscm.domain.bean.entity.Order>
	 **/
	@Override
	public List<OrderVo> findUnReceiptBuyOrdersUpdate(String signReceiptId,
			List<String> goodsIds) {
		SignReceipt signReceipt = signReceiptService.findOne(signReceiptId)
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30097001));
		ArrayString relatedDeliverGoodsIds;
		if (Objects.nonNull(signReceipt.getRelatedDeliverGoodsIds())) {
			relatedDeliverGoodsIds = signReceipt.getRelatedDeliverGoodsIds();
		} else {
			relatedDeliverGoodsIds = new ArrayString();
		}
		if (Objects.isNull(goodsIds)) {
			goodsIds = new ArrayList<>();
		}
		// 新增的发货单
		ArrayString finalRelatedDeliverGoodsIds = relatedDeliverGoodsIds;
		List<String> diff1 = goodsIds.stream()
				.filter(e -> !finalRelatedDeliverGoodsIds.contains(e)).toList();

		// 删除的发货单
		List<String> finalOrderIds = goodsIds;
		List<String> diff2 = relatedDeliverGoodsIds.stream()
				.filter(e -> !finalOrderIds.contains(e)).toList();

		List<DeliverGoods> deliverGoodsList = List.of();
		if (CollectionUtils.isNotEmpty(diff2)) {
			deliverGoodsList = deliverGoodsService.findByIds(diff2);
		}

		List<OrderVo> unReceiptBuyOrders = this
				.findUnReceiptBuyOrders(signReceipt.getContractId());
		if (CollectionUtils.isEmpty(unReceiptBuyOrders)) {
			if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
				return getOrderVos(deliverGoodsList);
			}
			return List.of();
		}

		List<DeliverGoods> deliverGoods = unReceiptBuyOrders.stream()
				.flatMap(e -> e.getDeliverGoodsList().stream()).toList();

		if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
			deliverGoods = new ArrayList<>(deliverGoods);

			deliverGoods.addAll(deliverGoodsList);
		}
		if (CollectionUtils.isNotEmpty(diff1)) {
			deliverGoods = deliverGoods.stream()
					.filter(e -> !diff1.contains(e.getId())).toList();
		}
		return getOrderVos(deliverGoods);
	}

	@Override
	public List<OrderVo> find(String signReceiptId,
			List<String> deliverGoodsIds) {
		SignReceipt signReceipt = signReceiptService.findOne(signReceiptId)
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30097001));
		ArrayString relatedDeliverGoodsIds = signReceipt
				.getRelatedDeliverGoodsIds();

		if (Objects.isNull(deliverGoodsIds)) {
			deliverGoodsIds = new ArrayList<>();
		}

		// 新增的发货单
		List<String> diff1 = deliverGoodsIds.stream()
				.filter(e -> !relatedDeliverGoodsIds.contains(e)).toList();

		// 删除的发货单
		List<String> finalDeliverGoodsIds = deliverGoodsIds;
		List<String> diff2 = relatedDeliverGoodsIds.stream()
				.filter(e -> !finalDeliverGoodsIds.contains(e)).toList();

		List<DeliverGoods> deliverGoodsList = null;
		if (CollectionUtils.isNotEmpty(diff2)) {
			deliverGoodsList = deliverGoodsService.findByIds(diff2);
		}

		List<DeliverGoods> list = new ArrayList<>();
		for (OrderVo unReceiptOrder : this
				.findUnReceiptOrders(signReceipt.getContractId())) {
			List<DeliverGoodsVo> deliverGoodsVoList = unReceiptOrder
					.getDeliverGoodsVoList();
			if (CollectionUtils.isNotEmpty(deliverGoodsVoList)) {
				List<DeliverGoods> deliverGoodsList1 = deliverGoodsVoList
						.stream().map(DeliverGoodsVo::getDeliverGoods).toList();
				list.addAll(deliverGoodsList1);
			}
		}

		list = list.stream().filter(e -> !diff1.contains(e.getId())).toList();

		ArrayList<DeliverGoods> list1 = new ArrayList<>(list);

		if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
			list1.addAll(deliverGoodsList);
		}

		List<DeliverGoodsVo> deliverGoodsVos = this.packDeliverGoodsVos(list1);

		List<String> orderIds = deliverGoodsVos.stream()
				.map(e -> e.getDeliverGoods().getOrderId()).toList();

		List<Order> orderList = this.findByIds(orderIds);

		orderList = orderList.stream()
				.filter(e -> Objects.equals(BusinessContractDef.SET_STATE.apply(
						OrderDef.Status.FINISHED.getCode(),
						BusinessContractDef.CommonSignState.COMPLETED
								.getCode()),
						e.getStatus()))
				.toList();

		Map<String, List<DeliverGoodsVo>> collect = deliverGoodsVos.stream()
				.collect(Collectors
						.groupingBy(e -> e.getDeliverGoods().getOrderId()));

		return orderList.stream().map(e -> {
			OrderVo orderVo = new OrderVo();
			orderVo.setOrder(e);
			orderVo.setDeliverGoodsVoList(collect.get(e.getId()));
			if (CollectionUtils.isNotEmpty(collect.get(e.getId()))) {
				BigDecimal sum = collect.get(e.getId()).stream().map(
						k -> k.getDeliverGoods().getActualGoodsTotalQuantity())
						.reduce(BigDecimal.ZERO, BigDecimal::add);
				orderVo.setUnReceiptWeight(sum);
			}
			return orderVo;
		}).toList();
	}

	/**
	 * @description: 展示全部有未完成发货的订单
	 * @author: 彭湃
	 * @date: 2025/1/22 10:40
	 * @param: [contractId]
	 * @return: java.util.List<com.zhihaoscm.domain.bean.vo.OrderVo>
	 **/
	@Override
	public List<OrderVo> findUnDeliverOrders(String contractId,
			String deliverGoodsId) {
		Contract contract = contractService.findOne(contractId).orElse(null);
		if (Objects.isNull(contract)) {
			return List.of();
		}
		List<Order> byContractId = this
				.findByContractIds(List.of(contract.getId()));
		Order order;
		DeliverGoods oldDeliverGoods;
		if (Objects.nonNull(deliverGoodsId)) {
			oldDeliverGoods = deliverGoodsService.findOne(deliverGoodsId)
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30001001));
			order = this.findOne(oldDeliverGoods.getOrderId()).orElseThrow(
					() -> new BadRequestException(ErrorCode.CODE_30009003));
			if (Objects.nonNull(byContractId)) {
				byContractId.add(order);
			} else {
				byContractId = List.of(order);
			}
		} else {
			oldDeliverGoods = null;
			order = null;
		}
		if (CollectionUtils.isEmpty(byContractId)) {
			return List.of();
		}
		// 所有未完成发货的订单
		List<Order> list = byContractId.stream().distinct().toList();
		list = list.stream().filter(e -> !OrderDef.BusinessStatus.COMPLETED
				.match(e.getDeliveryStatus())).toList();
		if (CollectionUtils.isEmpty(list)) {
			return List.of();
		}
		Map<String, Order> orderMap = list.stream()
				.collect(Collectors.toMap(Order::getId, i -> i));
		list = list.stream().distinct().toList();
		List<String> orderIds = list.stream().map(Order::getId).toList();
		List<DeliverGoods> deliverGoods = deliverGoodsService
				.findByOrderIds(orderIds);
		Map<String, List<DeliverGoods>> collect = deliverGoods.stream()
				.collect(Collectors.groupingBy(DeliverGoods::getOrderId));
		List<String> deliveryIds = new ArrayList<>();
		// 订单的剩余发货数量
		Map<String, BigDecimal> unDeliveryWeightMap = new HashMap<>();
		for (Map.Entry<String, List<DeliverGoods>> entry : collect.entrySet()) {
			List<DeliverGoods> value = entry.getValue();
			BigDecimal sum = BigDecimal.ZERO;
			for (DeliverGoods goods : value) {
				if (DeliverGoodsDef.Status.DELIVER_COMPLETE
						.match(goods.getStatus())) {
					sum = sum.add(goods.getActualGoodsTotalQuantity());
				} else if (DeliverGoodsDef.Status.DELIVERING
						.match(goods.getStatus())
						|| DeliverGoodsDef.Status.WAIT_DELIVER
								.match(goods.getStatus())) {
					sum = sum.add(goods.getGoodsTotalQuantity());
				}
			}
			if (sum.compareTo(
					orderMap.get(entry.getKey()).getGoodsTotalQuantity()) < 0) {
				// 发货重量小于订单总重量
				unDeliveryWeightMap.put(entry.getKey(),
						orderMap.get(entry.getKey()).getGoodsTotalQuantity()
								.subtract(sum));
			} else {
				deliveryIds.add(entry.getKey());

			}
		}

		if (CollectionUtils.isNotEmpty(deliveryIds)) {
			list = list.stream().filter(e -> !deliveryIds.contains(e.getId()))
					.toList();
		}

		list = list.stream()
				.filter(e -> Objects.equals(BusinessContractDef.SET_STATE.apply(
						OrderDef.Status.FINISHED.getCode(),
						BusinessContractDef.CommonSignState.COMPLETED
								.getCode()),
						e.getStatus()))
				.toList();

		if (Objects.nonNull(order)) {
			list = new ArrayList<>(list);
			list.add(order);
		}
		if (CollectionUtils.isEmpty(list)) {
			return List.of();
		}
		list = list.stream().distinct().toList();

		return list.stream().map(e -> {
			OrderVo orderVo = new OrderVo();
			orderVo.setOrder(e);
			orderVo.setDeliverGoodsList(collect.get(e.getId()));
			if (CollectionUtils.isNotEmpty(collect.get(e.getId()))) {
				BigDecimal sum = unDeliveryWeightMap.get(e.getId());
				if (Objects.isNull(sum)) {
					sum = BigDecimal.ZERO;
				}
				if (Objects.nonNull(order)
						&& Objects.equals(e.getId(), order.getId())) {
					sum = sum.add(oldDeliverGoods.getGoodsTotalQuantity());
				}
				orderVo.setUnDeliveryWeight(sum);
			} else {
				orderVo.setUnDeliveryWeight(e.getGoodsTotalQuantity());
			}
			return orderVo;
		}).toList();
	}

	/*
	 * @description: 展示未完成发货的采购订单
	 * 
	 * @author: pp
	 * 
	 * @date: 2025/4/23 11:14
	 * 
	 * @param: [contractId, deliverGoodsId]
	 * 
	 * @return: java.util.List<com.zhihaoscm.domain.bean.vo.OrderVo>
	 **/
	@Override
	public List<Order> findUnDeliverOrdersBuy(String contractId,
			String deliverGoodsId) {
		List<Order> orderList = this.findByContractId(contractId);
		Order order = null;
		DeliverGoods deliverGoods = null;
		if (Objects.nonNull(deliverGoodsId)) {
			deliverGoods = deliverGoodsService.findOne(deliverGoodsId)
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30001001));
			order = this.findOne(deliverGoods.getOrderId()).orElseThrow(
					() -> new BadRequestException(ErrorCode.CODE_30009003));
			order.setDeliveredWeight(order.getDeliveredWeight()
					.subtract(deliverGoods.getGoodsTotalQuantity()));
		}
		if (CollectionUtils.isEmpty(orderList)) {
			if (Objects.isNull(order)) {
				return List.of();
			} else {
				return List.of(order);
			}
		}
		List<SignReceipt> signReceipts = signReceiptService
				.findByOrderIds(orderList.stream().map(Order::getId).toList());
		List<String> orderIds = signReceipts.stream()
				.flatMap(e -> e.getRelatedOrderIds().stream()).toList();
		orderList = orderList.stream().filter(
				e -> !orderIds.contains(e.getId()) && e.getGoodsTotalQuantity()
						.compareTo(e.getDeliveredWeight()) > 0)
				.toList();
		if (CollectionUtils.isEmpty(orderList)) {
			if (Objects.nonNull(order)) {
				return List.of(order);
			}
			return List.of();
		}
		orderList = orderList.stream().distinct().toList();

		boolean flag = false;

		for (Order item : orderList) {
			if (Objects.nonNull(order)
					&& Objects.equals(item.getId(), order.getId())) {
				flag = true;
				item.setDeliveredWeight(item.getDeliveredWeight()
						.subtract(deliverGoods.getGoodsTotalQuantity()));
			}
		}
		if (!flag) {
			orderList = new ArrayList<>(orderList);
			if (Objects.nonNull(order)) {
				orderList.add(order);
			}
		}
		return orderList;
	}

	/**
	 * 根据合同id,订单状态 签收状态查询 总金额
	 *
	 * @Author:魏紫萱
	 * @return
	 */
	@Override
	public Optional<BigDecimal> findAmount(String contractId, Integer status,
			List<Integer> receiveStatus, Integer type) {
		// 没有关联签收单的订单 货物金额汇总
		LambdaQueryWrapper<Order> queryWrapper = Wrappers.query(Order.class)
				.select("SUM(goods_total_amount) goodsTotalAmount").lambda()
				.eq(Order::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Order::getContractId, contractId);
		queryWrapper.eq(Objects.nonNull(status), Order::getStatus, status);
		queryWrapper.in(CollectionUtils.isNotEmpty(receiveStatus),
				Order::getReceiveStatus, receiveStatus);
		// 采购项目要去掉已经关联了签收单的数据
		if (ContractDef.Type.BUY.match(type)) {
			// 合同关联的签收单
			List<SignReceipt> signReceipts = signReceiptService
					.findByContractId(contractId);
			List<String> orderIds = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(signReceipts)) {
				// 关联了签收单的订单id
				orderIds = signReceipts.stream()
						.map(SignReceipt::getRelatedOrderIds)
						.filter(Objects::nonNull)
						.flatMap(arrayString -> Arrays
								.stream(arrayString.toArray(new String[0])))
						.toList();
			}
			queryWrapper.notIn(CollectionUtils.isNotEmpty(orderIds),
					Order::getId, orderIds);
		}
		Order order = repository.selectOne(queryWrapper);
		return Objects.isNull(order) ? Optional.of(BigDecimal.ZERO)
				: Optional.of(order.getGoodsTotalAmount());
	}

	/**
	 * 计算注册企业合同的已提货未签收总金额
	 *
	 */
	@Override
	public Optional<BigDecimal> findAmount(String contractId) {
		Contract contract = contractService.findOne(contractId).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30151001));
		// 没有签收完成的签收单：指的是 订单没有签收单 或者 订单有签收单，但状态都不是签收完成
		// 存在签收完成状态的签收单：指的是 订单有签收单，并且至少有一个签收单是签收完成的
		BigDecimal orderAmount;
		// 订单状态已完成、作废中、签署中、待签署、待确认、草稿（客户端），是否完成发货为否，没有签收完成的签收单，等于订单合计金额
		BigDecimal orderAmount1 = BigDecimal.ZERO;
		// 订单状态已完成，是否完成发货为是，没有签收完成的签收单， 等于各个规格实际发货数量*对应规格的订单单价
		BigDecimal orderAmount2 = BigDecimal.ZERO;
		// 是否发货为否 有签收完成的签收单的数据 订单对应规格数量/重量-已签收规格数量/重量
		BigDecimal orderAmount3 = BigDecimal.ZERO;
		// 是否发货为是 有签收完成的签收单的数据 发货对应规格数量/重量-已签收规格数量/重量
		BigDecimal orderAmount4 = BigDecimal.ZERO;

		// 订单状态已完成、作废中、签署中、待签署、待确认、草稿（客户端），是否完成发货为否的订单
		List<Order> orders1 = this.findNotSignedOrders(contractId,
				List.of(OrderDef.Status.FINISHED.getCode(),
						OrderDef.Status.INVALIDING.getCode(),
						OrderDef.Status.DRAFT.getCode()),
				List.of(), CommonDef.Symbol.NO.getCode());
		if (CollectionUtils.isNotEmpty(orders1)) {
			// 没有签收单以及 没有签收完成的签收单 的订单
			List<Order> filterOrder1 = this
					.filterHasNoCompletedSignReceipt(orders1);
			// 订单数量*订单单价=订单合计金额
			orderAmount1 = filterOrder1.stream().map(Order::getGoodsTotalAmount)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}

		// 订单状态已完成，是否完成发货为是
		List<Order> orders2 = this.findByStatus(contractId,
				List.of(OrderDef.Status.FINISHED.getCode()),
				CommonDef.Symbol.YES.getCode());
		if (CollectionUtils.isNotEmpty(orders2)) {
			// 订单状态已完成，是否完成发货为是 没有签收单或者没有签收完成的签收单 的订单
			List<Order> filterOrder1 = this
					.filterHasNoCompletedSignReceipt(orders1);
			if (CollectionUtils.isNotEmpty(filterOrder1)) {
				List<String> orderIds = filterOrder1.stream().map(Order::getId)
						.toList();
				// 关联了这些订单的发货单
				List<DeliverGoods> deliverGoodsList = deliverGoodsService
						.findByOrderIds(orderIds);
				// 将发货单根据订单进行分组
				Map<String, List<DeliverGoods>> deliverGoodsMap = deliverGoodsList
						.stream().collect(Collectors
								.groupingBy(DeliverGoods::getOrderId));
				// 发货数量*订单单价
				for (Order order : filterOrder1) {
					// 订单里面的货物信息
					List<GoodsInfo> orderGoodsInfoList = this
							.convertGoodsInfo(order.getGoodsInfo());
					// 定义map用于存对应的规格和单价
					Map<String, BigDecimal> modelToPriceMap = new HashMap<>();
					if (CollectionUtils.isNotEmpty(orderGoodsInfoList)) {
						for (GoodsInfo orderGoodsInfo : orderGoodsInfoList) {
							String model = orderGoodsInfo.getModel();
							BigDecimal price = orderGoodsInfo.getUnitPrice();
							// 如果 map 中尚不存在该 model，则添加它
							if (!modelToPriceMap.containsKey(model)) {
								modelToPriceMap.put(model, price);
							}
						}
					}
					// 关联这个订单的发货信息列表
					List<DeliverGoods> deliverGoodsList1 = deliverGoodsMap
							.get(order.getId());
					// 已完成发货的金额
					if (CollectionUtils.isNotEmpty(deliverGoodsList1)) {
						orderAmount2 = this.getBigDecimal(deliverGoodsList1,
								modelToPriceMap, orderAmount2,
								contract.getContractType());
					} else {
						orderAmount2 = orderAmount2
								.add(order.getGoodsTotalAmount());
					}
				}
			}
			// 订单状态已完成，是否完成发货为是 存在签收完成状态的签收单
			List<Order> filterOrder = this
					.filterHasCompletedSignReceipt(orders1);
			// 并且过滤出签收数量小于实际发货数量的数据
			List<Order> filterOrder2 = filterOrder.stream()
					.filter(order -> order.getReceivedWeight()
							.compareTo(order.getActualDeliveredWeight()) < 0)
					.toList();
			if (CollectionUtils.isNotEmpty(filterOrder2)) {
				List<String> orderIds = filterOrder1.stream().map(Order::getId)
						.toList();
				// 关联了这些订单的发货单
				List<DeliverGoods> deliverGoodsList = deliverGoodsService
						.findByOrderIds(orderIds);
				// 将发货单根据订单进行分组
				Map<String, List<DeliverGoods>> deliverGoodsMap = deliverGoodsList
						.stream().collect(Collectors
								.groupingBy(DeliverGoods::getOrderId));
				// 发货数量*订单单价
				for (Order order : filterOrder2) {
					// 订单里面的货物信息
					List<GoodsInfo> orderGoodsInfoList = this
							.convertGoodsInfo(order.getGoodsInfo());
					// 定义map用于存对应的规格和单价
					Map<String, BigDecimal> modelToPriceMap = new HashMap<>();
					if (CollectionUtils.isNotEmpty(orderGoodsInfoList)) {
						for (GoodsInfo orderGoodsInfo : orderGoodsInfoList) {
							String model = orderGoodsInfo.getModel();
							BigDecimal price = orderGoodsInfo.getUnitPrice();
							// 如果 map 中尚不存在该 model，则添加它
							if (!modelToPriceMap.containsKey(model)) {
								modelToPriceMap.put(model, price);
							}
						}
					}
					// 关联这个订单的发货信息列表
					List<DeliverGoods> deliverGoodsList1 = deliverGoodsMap
							.get(order.getId());
					// 如果订单里面存在合并签收完成的签收单时 则剩余未签收部分为：（发货数量/重量-签收完成总数量/重量）*平均单价
					if (deliverGoodsList1.stream().anyMatch(
							deliverGoods -> DeliverGoodsDef.ReceiveWay.MERGE
									.match(deliverGoods.getReceiveWay()))) {
						BigDecimal filterOrderAmount;
						// 获取该订单的平均单价
						BigDecimal aveUnitPrice = this.getAveUnitPrice(order);
						// 订单剩余未签收数量 初始化
						BigDecimal orderWeight4;
						// 订单实际发货重量
						BigDecimal actualDeliveredWeight = BigDecimal.ZERO;
						if (Objects.nonNull(order.getActualDeliveredWeight())) {
							actualDeliveredWeight = order
									.getActualDeliveredWeight();
						}
						// 已签收重量
						BigDecimal receivedWeight = BigDecimal.ZERO;
						if (Objects.nonNull(order.getReceivedWeight())) {
							receivedWeight = order.getReceivedWeight();
						}
						// 订单剩余未签收数量=订单实际发货重量-已签收重量
						orderWeight4 = actualDeliveredWeight
								.subtract(receivedWeight);
						// 订单剩余未签收数量*订单平均单价
						filterOrderAmount = orderWeight4.multiply(aveUnitPrice);
						orderAmount4 = orderAmount4.add(filterOrderAmount);
					} else {
						// 不存在合并签收的
						// 等于（发货对应规格数量/重量-已签收规格数量/重量）得到的结果大于0的数值之和*订单对应规格的单价
						// 定义map用于存对应的规格和签收数量
						Map<String, BigDecimal> modelToReceiptWeightMap = new HashMap<>();
						// 定义map用于存对应的规格和发货数量
						Map<String, BigDecimal> modelToDeliveryWeightMap = new HashMap<>();
						// 所有订单的发货
						List<String> deliverGoodsIds = deliverGoodsList1
								.stream().map(DeliverGoods::getId).distinct()
								.toList();
						List<String> signReceiptIds = deliverGoodsList1.stream()
								.map(DeliverGoods::getSignReceiptId).distinct()
								.toList();
						// 过滤出签收完成的签收单
						List<SignReceipt> signReceipts = signReceiptService
								.findByIds(signReceiptIds).stream()
								.filter(signReceipt -> signReceipt != null
										&& SignReceiptDef.Status.FINISHED
												.match(signReceipt.getStatus()))
								.toList();
						// 已完成签收的发货信息
						List<DeliverGoods> filteredList = deliverGoodsList1
								.stream()
								.filter(deliverGoods -> signReceipts.stream()
										.anyMatch(signReceipt -> signReceipt
												.getId()
												.equals(deliverGoods
														.getSignReceiptId())))
								.toList();
						// 船运单信息
						Map<String, List<TransportOrderShip>> transportOrderShipMap = this
								.getTransportOrderShipMap(deliverGoodsIds);
						// 汽运运单信息
						Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = this
								.getTransportOrderVehicleMap(deliverGoodsIds);
						// 铁路单信息
						Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = this
								.getTransportOrderRailwayMap(deliverGoodsIds);
						// 采购的签收信息并且是录入企业都在货物信息里面
						if (ContractDef.ContractType.PURCHASE
								.match(contract.getContractType())
								&& Objects.isNull(
										contract.getUpstreamSuppliersId())) {
							// 计算发货里面对应的规格和发货数量
							for (DeliverGoods deliverGoods : deliverGoodsList1) {
								// 采购对账单关联的订单 里面的货物信息
								List<GoodsInfo> goodsInfos = this
										.convertGoodsInfo(
												deliverGoods.getGoodsInfo());
								for (GoodsInfo goodsInfo : goodsInfos) {
									String model = goodsInfo.getModel();
									BigDecimal deliverGoodsWeight = goodsInfo
											.getDeliveryQuantity();
									this.handleModelDeliveryWeight(
											modelToDeliveryWeightMap, model,
											deliverGoodsWeight);
								}
							}
							// 计算已完成签收里面对应的规格和签收数量
							this.handleBuyReceiptDeliverGoods(filteredList,
									modelToReceiptWeightMap);
						} else {
							for (DeliverGoods deliverGoods : deliverGoodsList1) {
								if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
										.match(deliverGoods.getDelivery())) {
									List<GoodsInfo> goodsInfos = this
											.convertGoodsInfo(deliverGoods
													.getGoodsInfo());
									for (GoodsInfo goodsInfo : goodsInfos) {
										String model = goodsInfo.getModel();
										BigDecimal deliverGoodsWeight = goodsInfo
												.getDeliveryQuantity();
										this.handleModelDeliveryWeight(
												modelToDeliveryWeightMap, model,
												deliverGoodsWeight);
									}
								} else if (DeliverGoodsDef.DeliverWay.SHIPPING
										.match(deliverGoods.getDelivery())) {
									List<TransportOrderShip> transportOrderShipList = transportOrderShipMap
											.get(deliverGoods.getId());
									if (org.apache.commons.collections4.CollectionUtils
											.isNotEmpty(
													transportOrderShipList)) {
										// 循环发货信息里面的货物信息
										for (TransportOrderShip transportOrderShip : transportOrderShipList) {
											String model = transportOrderShip
													.getModel();
											BigDecimal deliverGoodsWeight = BigDecimal
													.valueOf(transportOrderShip
															.getTon());
											this.handleModelDeliveryWeight(
													modelToDeliveryWeightMap,
													model, deliverGoodsWeight);
										}
									}
								} else if (DeliverGoodsDef.DeliverWay.CAR
										.match(deliverGoods.getDelivery())) {
									List<TransportOrderVehicle> transportOrderVehicleList = transportOrderVehicleMap
											.get(deliverGoods.getId());
									if (org.apache.commons.collections4.CollectionUtils
											.isNotEmpty(
													transportOrderVehicleList)) {
										// 循环发货信息里面的货物信息
										for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicleList) {
											String model = transportOrderVehicle
													.getGoodsType();
											BigDecimal deliverGoodsWeight = transportOrderVehicle
													.getTransportWeight();
											this.handleModelDeliveryWeight(
													modelToDeliveryWeightMap,
													model, deliverGoodsWeight);
										}
									}
								} else if (DeliverGoodsDef.DeliverWay.TRAIN
										.match(deliverGoods.getDelivery())) {
									List<TransportOrderRailway> transportOrderRailwayList = transportOrderRailwayMap
											.get(deliverGoods.getId());
									if (org.apache.commons.collections4.CollectionUtils
											.isNotEmpty(
													transportOrderRailwayList)) {
										// 循环发货信息里面的货物信息
										for (TransportOrderRailway transportOrderRailway : transportOrderRailwayList) {
											String model = transportOrderRailway
													.getModel();
											BigDecimal deliverGoodsWeight = transportOrderRailway
													.getTransportWeight();
											this.handleModelDeliveryWeight(
													modelToDeliveryWeightMap,
													model, deliverGoodsWeight);
										}
									}
								}
							}

							this.handleSaleReceiptDeliverGoods(filteredList,
									modelToReceiptWeightMap,
									transportOrderShipMap,
									transportOrderVehicleMap,
									transportOrderRailwayMap);
						}
						// 循环发货里面的规格
						for (Map.Entry<String, BigDecimal> entry : modelToDeliveryWeightMap
								.entrySet()) {
							String model = entry.getKey();
							BigDecimal deliveryWeight = entry.getValue();
							BigDecimal receiptWeight = BigDecimal.ZERO;
							BigDecimal unitPrice = BigDecimal.ZERO;
							BigDecimal amount = BigDecimal.ZERO;
							if (Objects.nonNull(
									modelToReceiptWeightMap.get(model))) {
								unitPrice = modelToPriceMap.get(model);
							}
							if (Objects.nonNull(
									modelToReceiptWeightMap.get(model))) {
								receiptWeight = modelToReceiptWeightMap
										.get(model);
							}
							// 发货数量大于签收数量时
							if (deliveryWeight.compareTo(receiptWeight) > 0) {
								// 金额=（发货数量-签收数量）*订单单价
								amount = (deliveryWeight
										.subtract(receiptWeight))
										.multiply(unitPrice);
							}
							orderAmount4 = orderAmount4.add(amount);
						}
					}
				}
			}

		}

		// 已提货 是否发货为否
		List<Order> orders3 = this.findByStatus(contractId,
				List.of(OrderDef.Status.FINISHED.getCode()),
				CommonDef.Symbol.NO.getCode());
		// 有存在完成签收的签收单的数据
		List<Order> ordersHasCompletedSignReceipt3 = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(orders3)) {
			// 至少有一个签收单是签收完成的订单列表 并且签收数量数量/重量小于订单数量重量
			ordersHasCompletedSignReceipt3 = this
					.filterHasCompletedSignReceipt(orders3).stream()
					.filter(order -> order.getReceivedWeight()
							.compareTo(order.getActualDeliveredWeight()) < 0)
					.toList();
		}
		// 用订单没有发货完的数量乘以订单的平均单价
		if (CollectionUtils.isNotEmpty(ordersHasCompletedSignReceipt3)) {
			List<String> orderIds = orders3.stream().map(Order::getId).toList();
			// 关联了这些订单的发货单
			List<DeliverGoods> deliverGoodsList = deliverGoodsService
					.findByOrderIds(orderIds);
			// 将发货单根据订单进行分组
			Map<String, List<DeliverGoods>> deliverGoodsMap = deliverGoodsList
					.stream()
					.collect(Collectors.groupingBy(DeliverGoods::getOrderId));
			for (Order order : orders3) {
				// 订单里面的货物信息
				List<GoodsInfo> orderGoodsInfoList = this
						.convertGoodsInfo(order.getGoodsInfo());
				// 定义map用于存对应的规格和单价
				Map<String, BigDecimal> modelToPriceMap = new HashMap<>();
				if (CollectionUtils.isNotEmpty(orderGoodsInfoList)) {
					for (GoodsInfo orderGoodsInfo : orderGoodsInfoList) {
						String model = orderGoodsInfo.getModel();
						BigDecimal price = orderGoodsInfo.getUnitPrice();
						// 如果 map 中尚不存在该 model，则添加它
						if (!modelToPriceMap.containsKey(model)) {
							modelToPriceMap.put(model, price);
						}
					}
				}
				// 关联这个订单的发货信息列表
				List<DeliverGoods> deliverGoodsList1 = deliverGoodsMap
						.get(order.getId());
				// 如果订单里面存在合并签收完成的签收单时 则剩余未签收部分为：（发货数量/重量-签收完成总数量/重量）*平均单价
				if (deliverGoodsList1.stream().anyMatch(
						deliverGoods -> DeliverGoodsDef.ReceiveWay.MERGE
								.match(deliverGoods.getReceiveWay()))) {
					BigDecimal filterOrderAmount;
					// 获取该订单的平均单价
					BigDecimal aveUnitPrice = this.getAveUnitPrice(order);
					// 订单剩余未签收数量 初始化
					BigDecimal orderWeight3;
					// 订单实际发货重量
					BigDecimal orderWeight = BigDecimal.ZERO;
					if (Objects.nonNull(order.getGoodsTotalQuantity())) {
						orderWeight = order.getGoodsTotalQuantity();
					}
					// 已签收重量
					BigDecimal receivedWeight = BigDecimal.ZERO;
					if (Objects.nonNull(order.getReceivedWeight())) {
						receivedWeight = order.getReceivedWeight();
					}
					// 订单剩余未签收数量=订单数量/重量-签收完成总数量/重量
					orderWeight3 = orderWeight.subtract(receivedWeight);
					// 订单剩余未签收数量*订单平均单价
					filterOrderAmount = orderWeight3.multiply(aveUnitPrice);
					orderAmount3 = orderAmount3.add(filterOrderAmount);
				} else {
					List<String> signReceiptIds = deliverGoodsList1.stream()
							.map(DeliverGoods::getSignReceiptId).distinct()
							.toList();
					// 过滤出签收完成的签收单
					List<SignReceipt> signReceipts = signReceiptService
							.findByIds(signReceiptIds).stream()
							.filter(signReceipt -> signReceipt != null
									&& SignReceiptDef.Status.FINISHED
											.match(signReceipt.getStatus()))
							.toList();
					// 已完成签收的发货信息
					List<DeliverGoods> filteredList = deliverGoodsList1.stream()
							.filter(deliverGoods -> signReceipts.stream()
									.anyMatch(signReceipt -> signReceipt.getId()
											.equals(deliverGoods
													.getSignReceiptId())))
							.toList();
					// 已完成签收的发货单id
					List<String> deliverGoodsIds = filteredList.stream()
							.map(DeliverGoods::getId).distinct().toList();

					// 船运单信息
					Map<String, List<TransportOrderShip>> transportOrderShipMap = this
							.getTransportOrderShipMap(deliverGoodsIds);
					// 汽运运单信息
					Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = this
							.getTransportOrderVehicleMap(deliverGoodsIds);
					// 铁路单信息
					Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = this
							.getTransportOrderRailwayMap(deliverGoodsIds);
					// 不存在合并签收的签收单时
					// 定义map用于存对应的规格和签收数量
					Map<String, BigDecimal> modelToReceiptWeightMap = new HashMap<>();
					// 定义map用于存对应的规格和订单数量
					Map<String, BigDecimal> modelToOrderWeightMap = new HashMap<>();
					// 订单 里面的货物信息
					List<GoodsInfo> goodsInfos = this
							.convertGoodsInfo(order.getGoodsInfo());
					for (GoodsInfo goodsInfo : goodsInfos) {
						String model = goodsInfo.getModel();
						BigDecimal orderWeight = goodsInfo.getAmount();
						this.handleModelOrderWeight(modelToOrderWeightMap,
								model, orderWeight);
					}
					// 采购的签收信息都在货物信息里面
					if (ContractDef.ContractType.PURCHASE
							.match(contract.getContractType())
							&& Objects.isNull(
									contract.getUpstreamSuppliersId())) {
						this.handleBuyReceiptDeliverGoods(filteredList,
								modelToReceiptWeightMap);

					} else {
						// 销售的签收信息在对应的单子里面
						this.handleSaleReceiptDeliverGoods(filteredList,
								modelToReceiptWeightMap, transportOrderShipMap,
								transportOrderVehicleMap,
								transportOrderRailwayMap);
					}
					// 循环订单里面的规格
					for (Map.Entry<String, BigDecimal> entry : modelToOrderWeightMap
							.entrySet()) {
						String model = entry.getKey();
						BigDecimal orderWeight = entry.getValue();
						BigDecimal receiptWeight = BigDecimal.ZERO;
						BigDecimal unitPrice = BigDecimal.ZERO;
						BigDecimal amount = BigDecimal.ZERO;
						if (Objects
								.nonNull(modelToReceiptWeightMap.get(model))) {
							// 获取订单里面对应规格的单价
							unitPrice = modelToPriceMap.get(model);
						}
						if (Objects
								.nonNull(modelToReceiptWeightMap.get(model))) {
							receiptWeight = modelToReceiptWeightMap.get(model);
						}
						// 订单数量大于签收数量时
						if (orderWeight.compareTo(receiptWeight) > 0) {
							// （订单数量-签收数量）*单价
							amount = (orderWeight.subtract(receiptWeight))
									.multiply(unitPrice);
						}
						orderAmount3 = orderAmount3.add(amount);
					}

				}

			}
		}
		orderAmount = orderAmount1.add(orderAmount2).add(orderAmount3)
				.add(orderAmount4);
		return Optional.of(orderAmount);
	}

	/**
	 * 计算录入企业合同的已提货未签收总金额
	 *
	 */
	@Override
	public Optional<BigDecimal> findRecordAmount(String contractId) {
		Contract contract = contractService.findOne(contractId).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30151001));
		BigDecimal orderAmount;
		// 订单状态已完成，没有签收完成的签收单，等于订单合计金额
		BigDecimal orderAmount1 = BigDecimal.ZERO;
		// 订单状态已完成，存在签收完成状态的签收单，但签收数量数量/重量小于订单数量重量，
		BigDecimal orderAmount2 = BigDecimal.ZERO;
		// 订单状态为已完成的数据
		List<Order> orders = this.findByStatus(contractId,
				List.of(OrderDef.Status.FINISHED.getCode()), null);
		// 没有签收完成的签收单的订单
		List<Order> filterOrder1;
		List<Order> filterOrder2 = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(orders)) {
			// 没有签收单以及 没有签收完成的签收单 的订单
			filterOrder1 = this.filterHasNoCompletedSignReceipt(orders);
			if (CollectionUtils.isNotEmpty(filterOrder1)) {
				// 订单数量*订单单价=订单合计金额
				orderAmount1 = filterOrder1.stream()
						.map(Order::getGoodsTotalAmount)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
				// 使用Set来存储filterOrder1中的订单ID
				Set<String> filterOrder1Ids = filterOrder1.stream()
						.map(Order::getId).collect(Collectors.toSet());
				// 从orders中过滤掉filterOrder1中的数据，得到filterOrder2
				filterOrder2 = orders.stream().filter(
						order -> !filterOrder1Ids.contains(order.getId()))
						.collect(Collectors.toList());

			} else {
				filterOrder2 = orders;
			}
		}
		// 存在签收完成状态的签收单的订单 签收数量数量/重量小于订单数量重量的订单
		if (CollectionUtils.isNotEmpty(filterOrder2)) {
			List<Order> orders3 = filterOrder2.stream()
					.filter(order -> order.getReceivedWeight()
							.compareTo(order.getActualDeliveredWeight()) < 0)
					.toList();
			if (CollectionUtils.isNotEmpty(orders3)) {
				List<String> orderIds = orders3.stream().map(Order::getId)
						.toList();
				// 关联了这些订单的发货单
				List<DeliverGoods> deliverGoodsList = deliverGoodsService
						.findByOrderIds(orderIds);
				// 将发货单根据订单进行分组
				Map<String, List<DeliverGoods>> deliverGoodsMap = deliverGoodsList
						.stream().collect(Collectors
								.groupingBy(DeliverGoods::getOrderId));
				for (Order order : orders3) {
					// 订单里面的货物信息
					List<GoodsInfo> orderGoodsInfoList = this
							.convertGoodsInfo(order.getGoodsInfo());
					// 定义map用于存对应的规格和单价
					Map<String, BigDecimal> modelToPriceMap = new HashMap<>();
					if (CollectionUtils.isNotEmpty(orderGoodsInfoList)) {
						for (GoodsInfo orderGoodsInfo : orderGoodsInfoList) {
							String model = orderGoodsInfo.getModel();
							BigDecimal price = orderGoodsInfo.getUnitPrice();
							// 如果 map 中尚不存在该 model，则添加它
							if (!modelToPriceMap.containsKey(model)) {
								modelToPriceMap.put(model, price);
							}
						}
					}
					// 关联这个订单的发货信息列表
					List<DeliverGoods> deliverGoodsList1 = deliverGoodsMap
							.get(order.getId());
					// 如果订单里面存在合并签收完成的签收单时 则剩余未签收部分为：（订单数量/重量-签收完成总数量/重量）*平均单价
					if (deliverGoodsList1.stream().anyMatch(
							deliverGoods -> DeliverGoodsDef.ReceiveWay.MERGE
									.match(deliverGoods.getReceiveWay()))) {
						BigDecimal filterOrderAmount;
						// 获取该订单的平均单价
						BigDecimal aveUnitPrice = this.getAveUnitPrice(order);
						// 订单剩余未签收数量 初始化
						BigDecimal orderWeight3;
						// 订单实际发货重量
						BigDecimal orderWeight = BigDecimal.ZERO;
						if (Objects.nonNull(order.getGoodsTotalQuantity())) {
							orderWeight = order.getGoodsTotalQuantity();
						}
						// 已签收重量
						BigDecimal receivedWeight = BigDecimal.ZERO;
						if (Objects.nonNull(order.getReceivedWeight())) {
							receivedWeight = order.getReceivedWeight();
						}
						// 订单剩余未签收数量=订单数量/重量-签收完成总数量/重量
						orderWeight3 = orderWeight.subtract(receivedWeight);
						// 订单剩余未签收数量*订单平均单价
						filterOrderAmount = orderWeight3.multiply(aveUnitPrice);
						orderAmount2 = orderAmount2.add(filterOrderAmount);
					} else {
						List<String> signReceiptIds = deliverGoodsList1.stream()
								.map(DeliverGoods::getSignReceiptId).distinct()
								.toList();
						// 过滤出签收完成的签收单
						List<SignReceipt> signReceipts = signReceiptService
								.findByIds(signReceiptIds).stream()
								.filter(signReceipt -> signReceipt != null
										&& SignReceiptDef.Status.FINISHED
												.match(signReceipt.getStatus()))
								.toList();
						// 已完成签收的发货信息
						List<DeliverGoods> filteredList = deliverGoodsList1
								.stream()
								.filter(deliverGoods -> signReceipts.stream()
										.anyMatch(signReceipt -> signReceipt
												.getId()
												.equals(deliverGoods
														.getSignReceiptId())))
								.toList();
						// 已完成签收的发货单id
						List<String> deliverGoodsIds = filteredList.stream()
								.map(DeliverGoods::getId).distinct().toList();

						// 船运单信息
						Map<String, List<TransportOrderShip>> transportOrderShipMap = this
								.getTransportOrderShipMap(deliverGoodsIds);
						// 汽运运单信息
						Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = this
								.getTransportOrderVehicleMap(deliverGoodsIds);
						// 铁路单信息
						Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = this
								.getTransportOrderRailwayMap(deliverGoodsIds);
						// 不存在合并签收的签收单时
						// 定义map用于存对应的规格和签收数量
						Map<String, BigDecimal> modelToReceiptWeightMap = new HashMap<>();
						// 定义map用于存对应的规格和订单数量
						Map<String, BigDecimal> modelToOrderWeightMap = new HashMap<>();
						// 订单 里面的货物信息
						List<GoodsInfo> goodsInfos = this
								.convertGoodsInfo(order.getGoodsInfo());
						for (GoodsInfo goodsInfo : goodsInfos) {
							String model = goodsInfo.getModel();
							BigDecimal orderWeight = goodsInfo.getAmount();
							this.handleModelOrderWeight(modelToOrderWeightMap,
									model, orderWeight);
						}
						// 采购的签收信息并且是录入企业的 都在货物信息里面
						if (ContractDef.ContractType.PURCHASE
								.match(contract.getContractType())
								&& Objects.isNull(
										contract.getUpstreamSuppliersId())) {
							this.handleBuyReceiptDeliverGoods(filteredList,
									modelToReceiptWeightMap);

						} else {
							// 销售的签收信息在对应的单子里面
							this.handleSaleReceiptDeliverGoods(filteredList,
									modelToReceiptWeightMap,
									transportOrderShipMap,
									transportOrderVehicleMap,
									transportOrderRailwayMap);
						}
						// 循环订单里面的规格
						for (Map.Entry<String, BigDecimal> entry : modelToOrderWeightMap
								.entrySet()) {
							String model = entry.getKey();
							BigDecimal orderWeight = entry.getValue();
							BigDecimal receiptWeight = BigDecimal.ZERO;
							BigDecimal unitPrice = BigDecimal.ZERO;
							BigDecimal amount = BigDecimal.ZERO;
							if (Objects.nonNull(
									modelToReceiptWeightMap.get(model))) {
								// 获取订单里面对应规格的单价
								unitPrice = modelToPriceMap.get(model);
							}
							if (Objects.nonNull(
									modelToReceiptWeightMap.get(model))) {
								receiptWeight = modelToReceiptWeightMap
										.get(model);
							}
							// 订单数量大于签收数量时
							if (orderWeight.compareTo(receiptWeight) > 0) {
								// （订单数量-签收数量）*单价
								amount = (orderWeight.subtract(receiptWeight))
										.multiply(unitPrice);
							}
							orderAmount2 = orderAmount2.add(amount);
						}
					}
				}
			}

		}
		orderAmount = orderAmount1.add(orderAmount2);
		return Optional.of(orderAmount);

	}

	/**
	 * 销售类型：根据合同id,订单状态 签收状态查询 已提货未签收的订单列表
	 *
	 * @return
	 */
	@Override
	public List<Order> findNotSignedOrders(String contractId,
			List<Integer> status, List<Integer> receiveStatus,
			Integer deliveryStatus) {
		// 没有关联签收单的订单
		LambdaQueryWrapper<Order> queryWrapper = Wrappers
				.lambdaQuery(Order.class);
		queryWrapper.eq(Order::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Order::getContractId, contractId);
		// 状态
		if (org.apache.commons.collections4.CollectionUtils
				.isNotEmpty(status)) {
			String stateStr = status.stream().map(Object::toString)
					.collect(Collectors.joining(","));
			// 再加上待确认和签署中,待签署数据
			queryWrapper.apply("((status >> 16) in (" + stateStr
					+ ") or (status in (262145,196608,196609,196610)))");
		}
		queryWrapper.in(CollectionUtils.isNotEmpty(receiveStatus),
				Order::getReceiveStatus, receiveStatus);
		// 是否完成发货字段
		if (Objects.nonNull(deliveryStatus)) {
			// 完成发货为是 则找出发货状态为已完成的数据
			if (CommonDef.Symbol.YES.match(deliveryStatus)) {
				queryWrapper.eq(Order::getDeliveryStatus,
						OrderDef.BusinessStatus.COMPLETED.getCode());
			} else {
				queryWrapper.ne(Order::getDeliveryStatus,
						OrderDef.BusinessStatus.COMPLETED.getCode());
			}
		}
		return repository.selectList(queryWrapper);
	}

	/**
	 * 销售类型：根据合同id,订单状态 签收状态查询 已提货未签收的订单列表
	 *
	 * @return
	 */
	@Override
	public List<Order> findByStatus(String contractId, List<Integer> status,
			Integer deliveryStatus) {
		// 没有关联签收单的订单
		LambdaQueryWrapper<Order> queryWrapper = Wrappers
				.lambdaQuery(Order.class);
		queryWrapper.eq(Order::getDel, CommonDef.Symbol.NO.getCode()).eq(
				StringUtils.isNotBlank(contractId), Order::getContractId,
				contractId);
		// 状态
		if (org.apache.commons.collections4.CollectionUtils
				.isNotEmpty(status)) {
			String stateStr = status.stream().map(Object::toString)
					.collect(Collectors.joining(","));
			// 再加上待确认和签署中,待签署数据
			queryWrapper.apply("((status >> 16) in (" + stateStr + "))");
		}
		// 是否完成发货字段
		if (Objects.nonNull(deliveryStatus)) {
			// 完成发货为是 则找出发货状态为已完成的数据
			if (CommonDef.Symbol.YES.match(deliveryStatus)) {
				queryWrapper.eq(Order::getDeliveryStatus,
						OrderDef.BusinessStatus.COMPLETED.getCode());
			} else {
				queryWrapper.ne(Order::getDeliveryStatus,
						OrderDef.BusinessStatus.COMPLETED.getCode());
			}
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<BigDecimal> findTotalQuantity(String projectId,
			Integer state, LocalDateTime startTime, LocalDateTime endTime,
			Integer type, String param, String purchaserName, Integer orderType,
			List<Integer> states) {
		BigDecimal goodsTotalQuantity = BigDecimal.ZERO;
		List<Contract> contracts;
		if (StringUtils.isNotBlank(purchaserName)) {
			contracts = contractService.find(purchaserName, null, null, type,
					null);
		} else {
			contracts = contractService.find(null, null, null, type, null);
		}
		if (CollectionUtils.isEmpty(contracts)) {
			return Optional.of(BigDecimal.ZERO);
		}
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		wrapper.eq(Order::getDel, CommonDef.Symbol.NO.getCode())
				.in(CollectionUtils.isNotEmpty(contracts), Order::getContractId,
						contracts.stream().map(Contract::getId).toList())
				.ge(Objects.nonNull(startTime), Order::getApplyDate, startTime)
				.le(Objects.nonNull(endTime), Order::getApplyDate, endTime)
				.eq(Order::getProjectId, projectId);
		// 订单类型
		if (Objects.nonNull(orderType)) {
			wrapper.eq(Order::getType, orderType);
		}
		if (CollectionUtils.isNotEmpty(states)) {
			boolean sign = states.contains(OrderDef.Status.SIGNING.getCode());
			boolean toSign = states
					.contains(OrderDef.Status.TO_BE_SIGNED.getCode());
			states.removeIf(e -> e.equals(OrderDef.Status.SIGNING.getCode())
					|| e.equals(OrderDef.Status.TO_BE_SIGNED.getCode()));
			if (CollectionUtils.isNotEmpty(states)) {
				String stateStr = states.stream().map(Object::toString)
						.collect(Collectors.joining(","));
				if (!sign && !toSign) {
					wrapper.apply(" (status >> 16) in (" + stateStr + ")");
				} else if (!sign) {
					wrapper.apply(" ((status >> 16) in (" + stateStr
							+ ") or (status in (196608,196609) and type = 1) )");
				} else if (!toSign) {
					wrapper.apply(" ((status >> 16) in (" + stateStr
							+ ") or (status = 196610 and type =1) )");
				} else {
					wrapper.apply(" ((status >> 16) in (" + stateStr
							+ ") or (status in (196608,196609,196610) and type = 1) )");
				}
			} else {
				if (!sign && toSign) {
					wrapper.apply(
							" ((status in (196608,196609) and type = 1)) ");
				} else if (sign && !toSign) {
					wrapper.apply(" ((status = 196610) and type = 1) ");
				} else {
					wrapper.apply(
							" (status in (196608,196609,196610) and type = 1) ");
				}
			}
		} else {
			List<Integer> list = new ArrayList<>();
			list.add(OrderDef.Status.DRAFT.getCode());
			list.add(OrderDef.Status.REVERTED.getCode());
			list.add(OrderDef.Status.SIGNING.getCode());
			String stateStr = list.stream().map(Object::toString)
					.collect(Collectors.joining(","));
			if (Objects.isNull(type)) {
				wrapper.apply(" (((status >> 16) not in (" + stateStr
						+ ") or status is null ) or ((status >> 16) = 3 and type = 1)) ");
			} else {
				wrapper.apply(" ((status >> 16) not in (" + stateStr
						+ ") or status is null ) ");
			}
		}

		// 订单编号或者合同名称
		if (Objects.nonNull(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(x -> x.like(Order::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Order::getContractId, contractIds));
		}
		List<Order> orders = repository.selectList(wrapper);
		if (CollectionUtils.isNotEmpty(orders)) {
			if (ContractDef.Type.SELL.match(type)) {
				// 销售类型的要过滤出已完成状态的
				List<Order> orders1 = orders.stream()
						.filter(i -> i.getStatus().equals(state)).toList();
				goodsTotalQuantity = orders1.stream()
						.map(Order::getGoodsTotalQuantity)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			} else {
				goodsTotalQuantity = orders.stream()
						.map(Order::getGoodsTotalQuantity)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);

			}
		}

		return Optional.of(goodsTotalQuantity);
	}

	@Override
	public Optional<BigDecimal> findSupplierEstimatedGoodsAmount(String id) {
		BigDecimal supplierEstimatedGoodsAmount = BigDecimal.ZERO;
		// 所有采购合同的上游可提货余额
		List<ContractVo> supplierContracts = contractService
				.findByProjectIdAndType(id,
						ContractDef.ContractType.PURCHASE.getCode());
		if (org.apache.commons.collections4.CollectionUtils
				.isNotEmpty(supplierContracts)) {
			supplierEstimatedGoodsAmount = supplierContracts.stream()
					.map(ContractVo::getEstimatedGoodsAmount)
					.filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		return Optional.of(supplierEstimatedGoodsAmount);
	}

	@Override
	public Optional<BigDecimal> findCustomerEstimatedGoodsAmount(String id) {
		BigDecimal customerEstimatedGoodsAmount = BigDecimal.ZERO;
		// 项目增减项增加的金额之和
		BigDecimal projectAddAmount = BigDecimal.ZERO;
		// 项目增减项减少的金额之和
		BigDecimal projectSubtractAmount = BigDecimal.ZERO;
		// 所有销售合同的下游可提货余额
		List<ContractVo> customerContracts = contractService
				.findByProjectIdAndType(id,
						ContractDef.ContractType.SALES.getCode());
		if (org.apache.commons.collections4.CollectionUtils
				.isNotEmpty(customerContracts)) {
			customerEstimatedGoodsAmount = customerContracts.stream()
					.map(ContractVo::getEstimatedGoodsAmount)
					.filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		// 根据项目id查询项目里面的增减项
		List<ProjectItem> projectItems = projectItemService
				.findByProjectIds(List.of(id));
		if (CollectionUtils.isNotEmpty(projectItems)) {
			// 加减方向为增加的数据
			List<ProjectItem> projectItemsAdd = projectItems.stream().filter(
					e -> ProjectDef.Direction.ADD.match(e.getDirection()))
					.toList();
			if (CollectionUtils.isNotEmpty(projectItemsAdd)) {
				projectAddAmount = projectItemsAdd.stream()
						.map(ProjectItem::getAmount).filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// 加减方向为减少的数据
			List<ProjectItem> projectItemsSubtract = projectItems.stream()
					.filter(e -> ProjectDef.Direction.SUBTRACT
							.match(e.getDirection()))
					.toList();
			if (CollectionUtils.isNotEmpty(projectItemsSubtract)) {
				projectSubtractAmount = projectItemsSubtract.stream()
						.map(ProjectItem::getAmount).filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
		}
		// 预估可提货额+项目里面为增加的数据-项目为减少的数据
		customerEstimatedGoodsAmount = customerEstimatedGoodsAmount
				.add(projectAddAmount).subtract(projectSubtractAmount);
		return Optional.of(customerEstimatedGoodsAmount);
	}

	/**
	 * @description: 根据客户id查询销售订单列表
	 * @author: pp
	 * @date: 2025/2/12 15:58
	 * @param: [customerId]
	 * @return: java.util.List<com.zhihaoscm.domain.bean.entity.Order>
	 **/
	@Override
	public List<Order> findByCustomer(Long customerId, String orderId,
			String projectId) {
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Order::getProjectId, projectId);
		wrapper.eq(Objects.nonNull(customerId), Order::getPurchaserId,
				customerId);
		wrapper.like(StringUtils.isNotBlank(orderId), Order::getId, orderId);
		wrapper.eq(Order::getStatus, BusinessContractDef.SET_STATE.apply(
				OrderDef.Status.FINISHED.getCode(),
				BusinessContractDef.CommonSignState.COMPLETED.getCode()));
		wrapper.isNull(Order::getRelatedBuyOrderId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Customer> find(String purchaserName) {
		List<Contract> projects = contractService.find(purchaserName, null,
				null, ContractDef.Type.SELL.getCode(), null);
		if (CollectionUtils.isEmpty(projects)) {
			return List.of();
		}
		List<Contract> list = projects.stream()
				.filter(e -> ProjectDef.State.PROCESSING.match(e.getState()))
				.toList();
		if (CollectionUtils.isEmpty(list)) {
			return List.of();
		}
		List<Long> ids = list.stream().map(Contract::getDownstreamPurchasersId)
				.distinct().toList();
		return customerService.findByIds(ids);
	}

	@Override
	public Optional<Boolean> checkOrderDeliveryStatus(String orderId) {
		Order order = super.findOne(orderId).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30009003));
		if (OrderDef.BusinessStatus.COMPLETED
				.match(order.getDeliveryStatus())) {
			return Optional.of(Boolean.FALSE);
		}
		List<DeliverGoods> byOrderIds = deliverGoodsService
				.findByOrderIds(List.of(orderId));
		if (CollectionUtils.isEmpty(byOrderIds)) {
			return Optional.of(Boolean.FALSE);
		}
		for (DeliverGoods deliverGoods : byOrderIds) {
			if (DeliverGoodsDef.Status.WAIT_DELIVER
					.match(deliverGoods.getStatus())
					|| DeliverGoodsDef.Status.DELIVERING
							.match(deliverGoods.getStatus())) {
				return Optional.of(Boolean.FALSE);
			}
		}
		return Optional.of(Boolean.TRUE);
	}

	/**
	 * 创建订单
	 *
	 * @param order
	 *            订单
	 * @return 订单
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> customCreateBuy(Order order, Integer saveType,
			Integer origin) {
		Project project = projectService.findOne(order.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService.findOne(order.getContractId())
				.orElse(new Contract());
		// 按规则设置签收单id
		order.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(), RedisKeys.Cache.ORDER_ID_GENERATOR,
				ContractDef.Type.from(contract.getContractType()).getStr()
						+ AutoCodeDef.BusinessRuleCode.ORDER_SUFFIX.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		order.setProjectName(project.getName());
		order.setPurchaserId(contract.getDownstreamPurchasersId());
		order.setPurchaserBusinessId(contract.getDownstreamId());
		order.setPurchaserEnterprise(
				contract.getDownstreamPurchasersEnterprise());

		order.setSellerId(contract.getSupplierChainId());
		order.setSellerEnterprise(contract.getSupplierChainEnterprise());

		order.setActualDeliveredWeight(BigDecimal.ZERO);
		order.setDeliveredWeight(BigDecimal.ZERO);
		order.setContractName(contract.getName());
		// 只有销售管理项目才需要设置状态
		if (ContractDef.Type.SELL.match(contract.getContractType())) {
			// 客户端走这个模块
			order.setDeliveryStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
			order.setTakeDeliveryStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
			order.setReceiveStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
			order.setReconciliationStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
			order.setInvoiceStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
			if (DeliverGoodsDef.SignMode.OFFLINE.match(order.getSignType())
					&& DeliverGoodsDef.SaveType.SAVE_AND_COMMIT
							.match(saveType)) {
				if (CommonDef.AccountSource.CUSTOM.match(origin)) {
					// custom端提交
					order.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.CONFIRMING.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode()));
					if (OrderDef.OrderType.PURCHASE.match(order.getType())) {
						ThreadPoolUtil.scheduleTask(
								() -> SpringUtil.getBean(OrderService.class)
										.notice(order, 2),
								3, TimeUnit.SECONDS,
								ThreadPoolUtil.getUserScheduledExecutor());
					}
				}
			} else {
				order.setStatus(BusinessContractDef.SET_STATE.apply(
						OrderDef.Status.DRAFT.getCode(),
						BusinessContractDef.CommonSignState.UNSIGNED
								.getCode()));
			}
		} else {
			// 供应链走这个模块
			// 采购订单如果有关联的销售订单，将所有被关联的销售订单的relatedBuyOrderId设置为当前订单id
			List<GoodsInfo> list = this.convertGoodsInfo(order.getGoodsInfo());
			List<String> orderIds = new ArrayList<>();
			for (GoodsInfo goodsInfo : list) {
				if (Objects.nonNull(goodsInfo.getRelatedOrderId())) {
					orderIds.add(goodsInfo.getRelatedOrderId());
				}
			}
			if (CollectionUtils.isNotEmpty(orderIds)) {
				List<Order> orders = this.findByIds(orderIds);
				if (CollectionUtils.isNotEmpty(orders)) {
					for (Order order1 : orders) {
						order1.setRelatedBuyOrderId(order.getId());
					}
				}
				super.batchUpdate(orders);
			}
		}

		return Optional.of(super.create(order));
	}

	@Override
	public Optional<Order> adminCreateSell(Order order) {
		Project project = projectService.findOne(order.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService.findOne(order.getContractId())
				.orElse(new Contract());
		// 按规则设置签收单id
		order.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(), RedisKeys.Cache.ORDER_ID_GENERATOR,
				ContractDef.Type.from(contract.getContractType()).getStr()
						+ AutoCodeDef.BusinessRuleCode.ORDER_SUFFIX.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		order.setProjectName(project.getName());

		order.setPurchaserId(contract.getDownstreamPurchasersId());
		order.setPurchaserBusinessId(contract.getDownstreamId());
		order.setPurchaserEnterprise(
				contract.getDownstreamPurchasersEnterprise());

		order.setSellerId(contract.getSupplierChainId());
		order.setSellerEnterprise(contract.getSupplierChainEnterprise());

		order.setActualDeliveredWeight(BigDecimal.ZERO);
		order.setDeliveredWeight(BigDecimal.ZERO);
		order.setContractName(contract.getName());

		// 采购订单如果有关联的销售订单，将所有被关联的销售订单的relatedBuyOrderId设置为当前订单id
		List<GoodsInfo> list = this.convertGoodsInfo(order.getGoodsInfo());
		if (CollectionUtils.isNotEmpty(list)) {
			List<String> orderIds = new ArrayList<>();
			for (GoodsInfo goodsInfo : list) {
				if (Objects.nonNull(goodsInfo.getRelatedOrderId())) {
					orderIds.add(goodsInfo.getRelatedOrderId());
				}
			}
			if (CollectionUtils.isNotEmpty(orderIds)) {
				List<Order> orders = this.findByIds(orderIds);
				if (CollectionUtils.isNotEmpty(orders)) {
					for (Order order1 : orders) {
						order1.setRelatedBuyOrderId(order.getId());
					}
				}
				super.batchUpdate(orders);
			}
		}
		// 状态设置为完成，签署方式为空-为录入企业数据
		order.setStatus(BusinessContractDef.SET_STATE.apply(
				OrderDef.Status.FINISHED.getCode(),
				BusinessContractDef.CommonSignState.COMPLETED.getCode()));
		order.setSignType(null);
		return Optional.of(super.create(order));
	}

	@Override
	public Optional<Order> adminUpdateSell(Order order) {
		return Optional.of(this.updateAllProperties(order));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> adminCreateBuy(Order order, Integer saveType) {
		Project project = projectService.findOne(order.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService.findOne(order.getContractId())
				.orElse(new Contract());
		// 按规则设置签收单id
		order.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(), RedisKeys.Cache.ORDER_ID_GENERATOR,
				ContractDef.Type.from(contract.getContractType()).getStr()
						+ AutoCodeDef.BusinessRuleCode.ORDER_SUFFIX.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		order.setProjectName(project.getName());

		order.setPurchaserId(contract.getSupplierChainId());
		order.setPurchaserEnterprise(contract.getSupplierChainEnterprise());

		order.setSellerId(contract.getUpstreamSuppliersId());
		order.setSellerBusinessId(contract.getUpstreamId());
		order.setSellerEnterprise(contract.getUpstreamSuppliersEnterprise());

		order.setActualDeliveredWeight(BigDecimal.ZERO);
		order.setDeliveredWeight(BigDecimal.ZERO);
		order.setContractName(contract.getName());

		// 供应链的采购订单都设置为未开始
		order.setDeliveryStatus(OrderDef.BusinessStatus.NOT_STARTED.getCode());
		order.setTakeDeliveryStatus(
				OrderDef.BusinessStatus.NOT_STARTED.getCode());
		order.setReceiveStatus(OrderDef.BusinessStatus.NOT_STARTED.getCode());
		order.setReconciliationStatus(
				OrderDef.BusinessStatus.NOT_STARTED.getCode());
		order.setInvoiceStatus(OrderDef.BusinessStatus.NOT_STARTED.getCode());

		// 判断合同是否为录入企业
		Boolean flag = contractService.validateIsRecorded(contract.getId(),
				QuotaChangeDef.Type.BUY.getCode()).orElse(null);

		if (flag) {
			// 状态设置为完成，签署方式为空-为录入企业数据
			order.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.FINISHED.getCode(),
					BusinessContractDef.CommonSignState.COMPLETED.getCode()));
			order.setSignType(null);
		} else {
			if (DeliverGoodsDef.SignMode.OFFLINE.match(order.getSignType())) {
				// 线下-保存并提交-待发起状态
				if (DeliverGoodsDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
					order.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.CONFIRMING.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode()));
					this.sendNotice(order,
							wxSubscriptionProperties.getOrderUnConfirmCode(),
							MessageFormat.format(
									UserMessageConstants.ORDER_UNCONFIRMAED_TEMPLATE,
									order.getId()),
							ContractDef.ContractType.PURCHASE.getCode());
				} else {
					// 线下-保存草稿-草稿状态
					order.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.DRAFT.getCode(),
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode()));
				}
			} else {
				if (DeliverGoodsDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
					// 线上-保存并提交-代发起状态
					order.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.TO_BE_INITIATED.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode()));
				} else {
					order.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.DRAFT.getCode(),
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode()));
				}
			}
		}
		return Optional.of(super.create(order));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> adminUpdateBuy(Order resource, Integer saveType) {

		// 判断合同是否为录入企业
		Boolean flag = contractService
				.validateIsRecorded(resource.getContractId(),
						QuotaChangeDef.Type.BUY.getCode())
				.orElse(null);

		if (flag) {
			// 状态设置为完成，签署方式为空-为录入企业数据
			resource.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.FINISHED.getCode(),
					BusinessContractDef.CommonSignState.COMPLETED.getCode()));
			resource.setSignType(null);
		} else {
			if (DeliverGoodsDef.SignMode.OFFLINE
					.match(resource.getSignType())) {
				// 线下-保存并提交-待发起状态
				if (DeliverGoodsDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
					resource.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.CONFIRMING.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode()));
				} else {
					// 线下-保存草稿-草稿状态
					resource.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.DRAFT.getCode(),
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode()));
				}
			} else {
				if (DeliverGoodsDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
					// 线上-保存并提交-代发起状态
					resource.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.TO_BE_INITIATED.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode()));
				} else {
					resource.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.DRAFT.getCode(),
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode()));
				}
			}
		}

		Contract contract = contractService.findOne(resource.getContractId())
				.orElse(new Contract());
		resource.setContractName(contract.getName());
		Order order = this.findOne(resource.getId()).orElse(new Order());

		// 将原来关联的所有销售订单的relatedBuyOrderId设置为null
		List<GoodsInfo> list = this.convertGoodsInfo(order.getGoodsInfo());
		List<String> orderIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(list)) {
			for (GoodsInfo goodsInfo : list) {
				if (Objects.nonNull(goodsInfo.getRelatedOrderId())) {
					orderIds.add(goodsInfo.getRelatedOrderId());
				}
			}
			if (CollectionUtils.isNotEmpty(orderIds)) {
				List<Order> orders = this.findByIds(orderIds);
				if (CollectionUtils.isNotEmpty(orders)) {
					for (Order order1 : orders) {
						order1.setRelatedBuyOrderId(null);
					}
					super.batchUpdate(orders);
				}
			}
		}

		// 采购订单如果有关联的销售订单，将所有被关联的销售订单的relatedBuyOrderId设置为当前订单id
		list = this.convertGoodsInfo(resource.getGoodsInfo());
		if (CollectionUtils.isNotEmpty(list)) {
			orderIds = new ArrayList<>();
			for (GoodsInfo goodsInfo : list) {
				if (Objects.nonNull(goodsInfo.getRelatedOrderId())) {
					orderIds.add(goodsInfo.getRelatedOrderId());
				}
			}
			if (CollectionUtils.isNotEmpty(orderIds)) {
				List<Order> orders = this.findByIds(orderIds);
				if (CollectionUtils.isNotEmpty(orders)) {
					for (Order order1 : orders) {
						order1.setRelatedBuyOrderId(resource.getId());
					}
					super.batchUpdate(orders);
				}
			}
		}

		return Optional.of(this.updateAllProperties(resource));
	}

	/**
	 * 更新订单
	 *
	 * @param resource
	 *            订单
	 * @return 订单
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> customUpdateBuy(Order resource, Integer saveType) {
		if (DeliverGoodsDef.SignMode.OFFLINE.match(resource.getSignType())
				&& DeliverGoodsDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
			// custom端提交
			resource.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.CONFIRMING.getCode(),
					BusinessContractDef.CommonSignState.BUYER_SIGNED
							.getCode()));
			if (OrderDef.OrderType.PURCHASE.match(resource.getType())) {
				SpringUtil.getBean(OrderService.class).notice(resource, 2);
			}
		} else {
			resource.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.DRAFT.getCode(),
					BusinessContractDef.CommonSignState.UNSIGNED.getCode()));
		}
		Contract contract = contractService.findOne(resource.getContractId())
				.orElse(new Contract());
		resource.setContractName(contract.getName());

		return Optional.of(this.updateAllProperties(resource));
	}

	/**
	 * @description: 删除采购订单，并将关联的销售订单的relatedBuyOrderId设置为null
	 * @author: pp
	 * @date: 2025/2/13 10:16
	 * @param: [id]
	 * @return: void
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteBuy(String id) {
		this.findOne(id).ifPresent(order -> {
			// 将原来关联的所有销售订单的relatedBuyOrderId设置为null
			List<GoodsInfo> list = this.convertGoodsInfo(order.getGoodsInfo());
			List<String> orderIds = new ArrayList<>();
			for (GoodsInfo goodsInfo : list) {
				if (Objects.nonNull(goodsInfo.getRelatedOrderId())) {
					orderIds.add(goodsInfo.getRelatedOrderId());
				}
			}
			if (CollectionUtils.isNotEmpty(orderIds)) {
				List<Order> orders = this.findByIds(orderIds);
				if (CollectionUtils.isNotEmpty(orders)) {
					for (Order order1 : orders) {
						order1.setRelatedBuyOrderId(null);
					}
					super.batchUpdate(orders);
				}
			}
			super.delete(id);
		});
	}

	/**
	 * @description: 确认操作
	 * @author: 彭湃
	 * @date: 2025/1/16 14:23
	 * @param: [resource]
	 * @return: com.zhihaoscm.domain.bean.entity.Order
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> confirm(Order resource, Integer origin) {
		resource.setStatus(BusinessContractDef.SET_STATE.apply(
				OrderDef.Status.FINISHED.getCode(),
				BusinessContractDef.CommonSignState.COMPLETED.getCode()));

		if (CommonDef.AccountSource.INNER.match(origin)) {
			if (OrderDef.SignType.OFFLINE.match(resource.getSignType())) {
				this.sendNotice(resource,
						wxSubscriptionProperties.getConfirmationOrderCode(),
						MessageFormat.format(
								UserMessageConstants.ORDER_CONFIRMA_TEMPLATE,
								resource.getId()),
						ContractDef.ContractType.SALES.getCode());
			}

			if (OrderDef.OrderType.DELIVER_APPLY.match(resource.getType())) {
				this.sendNotice(resource,
						wxSubscriptionProperties
								.getSupplierConfirmationOrderCode(),
						MessageFormat.format(
								UserMessageConstants.ORDER_CONFIRMAED_TEMPLATE,
								resource.getId()),
						ContractDef.ContractType.SALES.getCode());
			}
		}
		return Optional.of(this.updateAllProperties(resource));
	}

	/**
	 * @description: 驳回操作
	 * @author: 彭湃
	 * @date: 2025/1/16 14:58
	 * @param: [resource]
	 * @return: com.zhihaoscm.domain.bean.entity.Order
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> revert(Order resource, Boolean isRevoke,
			Integer origin) {
		resource.setStatus(BusinessContractDef.SET_STATE.apply(
				OrderDef.Status.REVERTED.getCode(),
				BusinessContractDef.CommonSignState.COMPLETED.getCode()));
		if (Objects.nonNull(resource.getVoucherFileId())
				&& OrderDef.SignType.ONLINE.match(resource.getSignType())) {
			fileService.batchUnActive(List.of(resource.getVoucherFileId()));
			resource.setVoucherFileId(null);
			if (isRevoke
					&& OrderDef.SignType.ONLINE.match(resource.getSignType())) {
				// 撤销合同
				contractRecordService.revoke(resource.getId(),
						PurchaseContractDef.CorrelationTable.ORDER);
			}
		}

		Contract contract = contractService.findOne(resource.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30151001));
		if (ContractDef.ContractType.SALES.match(contract.getContractType())) {
			if (OrderDef.SignType.OFFLINE.match(resource.getSignType())
					&& CommonDef.AccountSource.INNER.match(origin)) {
				this.sendNotice(resource,
						wxSubscriptionProperties.getDismissOrderCode(),
						MessageFormat.format(
								UserMessageConstants.ORDER_DISMISS_TEMPLATE,
								resource.getId()),
						ContractDef.ContractType.SALES.getCode());
			}
		} else {
			if (OrderDef.SignType.OFFLINE.match(resource.getSignType())
					&& CommonDef.AccountSource.CUSTOM.match(origin)) {
				SpringUtil.getBean(OrderService.class).notice(resource, 5);
			}
		}

		return Optional.of(this.update(resource));
	}

	/**
	 * @description: 提交操作
	 * @author: 彭湃
	 * @date: 2025/1/16 14:46
	 * @param: [resource]
	 * @return: com.zhihaoscm.domain.bean.entity.Order
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> submit(Order resource) {
		if (OrderDef.SignType.OFFLINE.match(resource.getSignType())) {
			resource.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.CONFIRMING.getCode(),
					BusinessContractDef.CommonSignState.BUYER_SIGNED
							.getCode()));
			if (OrderDef.OrderType.PURCHASE.match(resource.getType())) {
				SpringUtil.getBean(OrderService.class).notice(resource, 2);
			}
		} else {
			resource.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.SIGNING.getCode(),
					BusinessContractDef.CommonSignState.UNSIGNED.getCode()));
		}

		return Optional.of(this.updateAllProperties(resource));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> adminWithdraw(Order resource) {
		resource.setStatus(BusinessContractDef.SET_STATE.apply(
				OrderDef.Status.DRAFT.getCode(),
				BusinessContractDef.CommonSignState.UNSIGNED.getCode()));
		return Optional.of(super.updateAllProperties(resource));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> adminSubmit(Order resource) {
		if (OrderDef.SignType.OFFLINE.match(resource.getSignType())) {
			resource.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.CONFIRMING.getCode(),
					BusinessContractDef.CommonSignState.BUYER_SIGNED
							.getCode()));
			if (OrderDef.OrderType.PURCHASE.match(resource.getType())) {
				SpringUtil.getBean(OrderService.class).notice(resource, 2);
			}
		} else {
			resource.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.TO_BE_INITIATED.getCode(),
					BusinessContractDef.CommonSignState.BUYER_SIGNED
							.getCode()));
		}

		return Optional.of(this.updateAllProperties(resource));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> completeDelivery(Order order) {
		order.setDeliveryStatus(OrderDef.BusinessStatus.COMPLETED.getCode());
		Order order1 = this.updateAllProperties(order);
		signReceiptService.completedChangeOrderStatus(List.of(order.getId()));
		return Optional.of(order1);

	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = "{{#success}}", type = LogDef.ORDER_INFO, bizNo = "{{#resource.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#resource.getId()}}"),
			@LogRecord.KeyValuePair(key = "#projectId#", value = "{{#name}}") }, messageType = LogDef.MESSAGE_TYPE_ORDER, permission = LogDef.PROJECT_DEAL)
	public void notice(Order resource, Integer type) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElse(new Project());
		LogRecordContext.putVariable("name", project.getName());
		switch (type) {
			case 2 -> LogRecordContext.putVariable("success",
					LogDef.ADD_PURCHASE_ORDER);
			case 3 -> LogRecordContext.putVariable("success",
					LogDef.ADD_INVALID_ORDER);
			case 4 -> LogRecordContext.putVariable("success",
					LogDef.ORDER_INVALID_REJECTED);
			case 5 ->
				LogRecordContext.putVariable("success", LogDef.ORDER_REJECTED);

			default -> {
			}
		}
		log.info("订单创建成功，发送通知:{}", resource.getId());
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<ContractPageResponse> initiateSign(Order resource,
			Integer origin) {

		Long fileId = this.getFileId(resource);
		if (Objects.nonNull(fileId)) {
			resource.setVoucherFileId(fileId);
			this.updateAllProperties(resource);
		} else {
			log.info("订单生成pdf文件失败");
			return Optional.empty();
		}

		// 设置供应链签署人
		User user1 = adminSealService.findByType(SignerSettings.billType.order)
				.orElse(null);
		if (Objects.nonNull(user1)) {
			resource.setSupplierSigner(user1.getName());
			resource.setSupplierSignerId(user1.getId());
		}

		// 发起合同
		Map<Long, String> customerMap;
		if (OrderDef.OrderType.PURCHASE.getCode().equals(resource.getType())) {
			customerMap = contractRecordService.draft(
					resource.getPurchaserEnterprise().getName() + "订单",
					List.of(resource.getPurchaserId(), resource.getSellerId()),
					List.of(resource.getVoucherFileId()), resource.getId(),
					PurchaseContractDef.CorrelationTable.ORDER, null, null);
			resource.getPurchaserEnterprise()
					.setSignMobile(customerMap.get(resource.getPurchaserId()));
			resource.getSellerEnterprise()
					.setSignMobile(customerMap.get(resource.getSellerId()));
		} else {
			customerMap = contractRecordService.draft(
					resource.getPurchaserEnterprise().getName() + "订单",
					List.of(resource.getPurchaserId()),
					List.of(resource.getVoucherFileId()), resource.getId(),
					PurchaseContractDef.CorrelationTable.ORDER, null, null);
			resource.getPurchaserEnterprise()
					.setSignMobile(customerMap.get(resource.getPurchaserId()));
		}

		// 设置文件id
		resource.setVoucherFileId(contractRecordService.download(
				resource.getId(), PurchaseContractDef.CorrelationTable.ORDER));

		resource.setStatus(BusinessContractDef.SET_STATE.apply(
				OrderDef.Status.SIGNING.getCode(),
				BusinessContractDef.CommonSignState.UNSIGNED.getCode()));
		this.updateAllProperties(resource);

		// 校验子账号是否有签章权限
		if (OrderDef.OrderType.PURCHASE.getCode().equals(resource.getType())) {
			if (!contractRecordService.validateSign(
					resource.getPurchaserEnterprise().getSignMobile(),
					resource.getSellerEnterprise().getSignMobile())) {
				return Optional.empty();
			}
		} else {
			if (!contractRecordService.validateSign(
					resource.getPurchaserEnterprise().getSignMobile(), null)) {
				return Optional.empty();
			}
		}
		// 获取签署链接
		return contractRecordService.sign(resource.getId(),
				PurchaseContractDef.CorrelationTable.ORDER, origin);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ContractPageResponse> signing(Order order, Integer origin) {
		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				order.getPurchaserEnterprise().getSignMobile(),
				order.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}
		return contractRecordService.sign(order.getId(),
				PurchaseContractDef.CorrelationTable.ORDER, origin);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Boolean> verificationInvalid(String id, Integer type) {
		PurchaseContractDef.CorrelationTable from = PurchaseContractDef.CorrelationTable
				.from(type);
		switch (from) {
			// 发货完成的发货单满足以下任一条件“作废”按钮：①未被签收单关联②已被签收单关联，但关联的签收单都已作废。
			case GOODS -> {
				DeliverGoods deliverGoods = deliverGoodsService.findOne(id)
						.orElse(new DeliverGoods());
				if (Objects.isNull(deliverGoods.getSignReceiptId())) {
					return Optional.of(Boolean.TRUE);
				} else {
					SignReceipt signReceipt = signReceiptService
							.findOne(deliverGoods.getSignReceiptId())
							.orElse(new SignReceipt());
					if (SignReceiptDef.Status.INVALID
							.match(signReceipt.getStatus())) {
						return Optional.of(Boolean.TRUE);
					}
				}
			}
			// 已完成订单满足以下任一条件有“发起作废”按钮：①未被发货单关联②已被发货单关联，但关联的发货单都已作废、发货取消。
			case ORDER -> {
				List<DeliverGoods> deliverGoodsList = deliverGoodsService
						.findByOrderIds(List.of(id));
				if (CollectionUtils.isEmpty(deliverGoodsList)) {
					return Optional.of(Boolean.TRUE);
				} else {
					boolean flag = true;
					for (DeliverGoods deliverGoods : deliverGoodsList) {
						if (!(DeliverGoodsDef.Status.DELIVER_INVALID
								.match(deliverGoods.getStatus())
								|| DeliverGoodsDef.Status.DELIVER_CANCEL
										.match(deliverGoods.getStatus()))) {
							flag = false;
							break;
						}
					}
					if (flag) {
						return Optional.of(Boolean.TRUE);
					}
				}
			}
			// 签收完成的签收单满足以下任一条件有“发起作废”按钮：①未被对账单关联②已被对账单关联，但关联的对账单都已作废。
			case GOODS_RECEIPT -> {
				List<Reconciliation> reconciliations = reconciliationService
						.findBySignReceiptId(id);
				List<Outbound> outbounds = outboundService
						.findByReceiptIds(List.of(id));
				SignReceipt signReceipt = signReceiptService.findOne(id)
						.orElse(new SignReceipt());
				ArrayString deliverGoodsIds = signReceipt
						.getRelatedDeliverGoodsIds();
				List<DeliverGoods> deliverGoodsList = deliverGoodsService
						.findByIds(deliverGoodsIds);
				List<String> relatedOrderIds = deliverGoodsList.stream()
						.map(DeliverGoods::getOrderId).distinct().toList();
				List<Reconciliation> byOrderIds = reconciliationService
						.findByOrderIds(new ArrayList<>(relatedOrderIds),
								false);
				if (CollectionUtils.isEmpty(byOrderIds)
						&& CollectionUtils.isEmpty(reconciliations)
						&& CollectionUtils.isEmpty(outbounds)) {
					return Optional.of(Boolean.TRUE);
				} else {
					boolean flag = true;
					for (Reconciliation reconciliation : reconciliations) {
						if (!ReconciliationDef.State.INVALID
								.match(reconciliation.getState())) {
							flag = false;
							break;
						}
					}

					for (Reconciliation reconciliation : byOrderIds) {
						if (!ReconciliationDef.State.INVALID
								.match(reconciliation.getState())) {
							flag = false;
							break;
						}
					}

					for (Outbound outbound : outbounds) {
						if (!OutboundDef.Status.INVALIDED
								.match(outbound.getState())) {
							flag = false;
							break;
						}
					}
					if (flag) {
						return Optional.of(Boolean.TRUE);
					}
				}
			}
			// 对账完成的对账单满足以下任一条件有“发起作废”按钮：①未被开票关联②已被开票关联，但关联的开票都已作废。
			case RECONCILIATION -> {
				List<BillPayment> byReconciliationIds = billPaymentService
						.findByReconciliationIds(List.of(id));
				if (CollectionUtils.isEmpty(byReconciliationIds)) {
					return Optional.of(Boolean.TRUE);
				} else {
					boolean flag = true;
					for (BillPayment billPayment : byReconciliationIds) {
						if (!BillPaymentDef.State.INVALID
								.match(billPayment.getState())) {
							flag = false;
							break;
						}
					}
					if (flag) {
						return Optional.of(Boolean.TRUE);
					}
				}
			}
		}
		return Optional.of(Boolean.FALSE);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ContractPageResponse> invalid(Order order, Integer origin) {
		// 调用契约锁撤销合同接口
		contractRecordService.revoke(order.getId(),
				PurchaseContractDef.CorrelationTable.ORDER);
		// 作废后获取作废合同id
		Long fileId = contractRecordService.detail(order.getId(),
				PurchaseContractDef.CorrelationTable.ORDER);
		order.setInvalidFileId(fileId);
		order.setStatus(BusinessContractDef.SET_STATE.apply(
				OrderDef.Status.INVALIDING.getCode(),
				BusinessContractDef.CommonSignState.UNSIGNED.getCode()));
		order.setInvalidRevokeReason(null);
		order.setInvalidRevokeTime(null);
		order.setPurchaseInvalidTime(null);
		if (OrderDef.OrderType.DELIVER_APPLY.match(order.getType())
				&& CommonDef.UserType.INNER
						.match(order.getInvalidInitiator())) {
			order.setSellerInvalidTime(LocalDateTime.now());
		} else {
			order.setSellerInvalidTime(null);
		}
		super.updateAllProperties(order);

		Contract contract = contractService.findOne(order.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30001001));
		if (ContractDef.ContractType.SALES.match(contract.getContractType())) {
			if (CommonDef.UserType.INNER.match(origin)) {
				this.sendNotice(order,
						wxSubscriptionProperties.getOrderNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.ORDER_INVALID_TEMPLATE,
								order.getId()),
						ContractDef.ContractType.SALES.getCode());
			} else {
				SpringUtil.getBean(OrderService.class).notice(order, 3);
			}
		} else {
			if (CommonDef.UserType.INNER.match(origin)) {
				SpringUtil.getBean(OrderService.class).notice(order, 3);
			} else {
				this.sendNotice(order,
						wxSubscriptionProperties.getOrderNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.ORDER_INVALID_TEMPLATE,
								order.getId()),
						ContractDef.ContractType.PURCHASE.getCode());
			}
		}

		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				order.getPurchaserEnterprise().getSignMobile(),
				order.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}

		return contractRecordService.sign(order.getId(),
				PurchaseContractDef.CorrelationTable.ORDER,
				CertificationDef.Origin.PC.getCode());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> invalidOffLine(Order order, Integer initiator) {
		// 作废合同
		order.setInvalidRevokeReason(null);
		order.setInvalidRevokeTime(null);
		order.setPurchaseInvalidTime(null);
		order.setSellerInvalidTime(null);
		if (CommonDef.UserType.INNER.match(order.getInvalidInitiator())) {
			order.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.INVALIDING.getCode(),
					BusinessContractDef.CommonSignState.SUPPLY_SIGNED
							.getCode()));
			order.setSellerInvalidTime(LocalDateTime.now());
			this.sendNotice(order,
					wxSubscriptionProperties.getOrderNullifyConfirmCode(),
					MessageFormat.format(
							UserMessageConstants.ORDER_INVALID_TEMPLATE,
							order.getId()),
					ContractDef.ContractType.SALES.getCode());
		} else {
			order.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.INVALIDING.getCode(),
					BusinessContractDef.CommonSignState.BUYER_SIGNED
							.getCode()));
			order.setPurchaseInvalidTime(LocalDateTime.now());
			SpringUtil.getBean(OrderService.class).notice(order, 3);
		}
		return Optional.of(super.updateAllProperties(order));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> confirmInvalid(Order order) {
		// 确认作废合同
		order.setStatus(BusinessContractDef.SET_STATE.apply(
				OrderDef.Status.INVALID.getCode(),
				BusinessContractDef.CommonSignState.COMPLETED.getCode()));
		if (CommonDef.UserType.INNER.match(order.getInvalidInitiator())) {
			order.setPurchaseInvalidTime(LocalDateTime.now());
		} else {
			order.setSellerInvalidTime(LocalDateTime.now());
		}
		return Optional.of(super.updateAllProperties(order));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Order> revertInvalid(Order order) {
		// 撤销作废合同
		order.setStatus(BusinessContractDef.SET_STATE.apply(
				OrderDef.Status.FINISHED.getCode(),
				BusinessContractDef.CommonSignState.COMPLETED.getCode()));
		order.setInvalidRevokeTime(LocalDateTime.now());
		Contract contract = contractService.findOne(order.getContractId())
				.orElseThrow();
		if (ContractDef.ContractType.SALES.match(contract.getContractType())) {
			if (CommonDef.UserType.INNER.match(order.getInvalidInitiator())) {
				SpringUtil.getBean(OrderService.class).notice(order, 4);
			} else {
				this.sendNotice(order,
						wxSubscriptionProperties.getOrderNullifyDismissCode(),
						MessageFormat.format(
								UserMessageConstants.ORDER_INVALID_DISMISS_TEMPLATE,
								order.getId()),
						ContractDef.ContractType.SALES.getCode());
			}
		} else {
			if (CommonDef.UserType.INNER.match(order.getInvalidInitiator())) {
				this.sendNotice(order,
						wxSubscriptionProperties.getOrderNullifyDismissCode(),
						MessageFormat.format(
								UserMessageConstants.ORDER_INVALID_DISMISS_TEMPLATE,
								order.getId()),
						ContractDef.ContractType.PURCHASE.getCode());
			} else {
				SpringUtil.getBean(OrderService.class).notice(order, 4);
			}
		}
		return Optional.of(super.updateAllProperties(order));
	}

	@Override
	public List<OrderVo> findByOrderIds(List<String> orderIds,
			List<DeliverGoods> deliverGoodsList, Contract contract) {
		List<Order> orders = super.findByIds(orderIds);
		// 将发货单根据订单进行分组
		Map<String, List<DeliverGoods>> deliverGoodsMap = deliverGoodsList
				.stream()
				.collect(Collectors.groupingBy(DeliverGoods::getOrderId));
		// 查询订单所关联的费用类型为订单保证金的付款单 且没有被保证金转货款锁定的值
		List<Payment> payments = paymentService.findByOrders(orderIds,
				PaymentDef.State.COMPLETED.getCode(),
				PaymentDef.CostType.ORDER_DEPOSIT.getCode(), null);
		// 将支付记录根据订单id进行分组
		Map<String, List<Payment>> paymentMap = payments.stream()
				.collect(Collectors.groupingBy(Payment::getOrderId));
		List<OrderVo> orderVos = new ArrayList<>();
		for (Order order : orders) {
			OrderVo orderVo = new OrderVo();
			orderVo.setOrder(order);
			// 该订单本次关联的发货单
			List<DeliverGoods> deliverGoods = deliverGoodsMap
					.get(order.getId());
			BigDecimal orderReceiptWeight;
			// 采购类型并且是录入企业时
			if (ContractDef.ContractType.PURCHASE
					.match(contract.getContractType())
					&& Objects.isNull((contract.getUpstreamSuppliersId()))) {
				orderReceiptWeight = this
						.getBuyOrderReceiptWeight(deliverGoods);
			}
			// 销售类型
			else {
				orderReceiptWeight = this
						.getSellOrderReceiptWeight(deliverGoods);
			}
			// 设置本次订单里面的发货的签收总数量
			orderVo.setReceiptWeight(orderReceiptWeight);
			// 该订单所关联的费用类型为订单保证金的付款单 且没有被保证金转货款锁定的值
			List<Payment> paymentList = paymentMap.get(order.getId());
			// 设置已收到的订单保证金
			if (CollectionUtils.isNotEmpty(paymentList)) {
				orderVo.setReceivedOrderDeposit(
						paymentList.stream().reduce(BigDecimal.ZERO,
								(sum, item) -> sum.add(item.getAmount()),
								BigDecimal::add));
			}
			orderVos.add(orderVo);
		}
		return orderVos;
	}

	@Override
	public void downloadPdf(HttpServletResponse response, String id)
			throws IOException {
		setExportResponseFields(response, id);
		GeneratPdfDto pdf = generatPdfDto(id);
		Order resource = this.findOne(id).orElse(new Order());
		if (OrderDef.OrderType.PURCHASE.getCode().equals(resource.getType())) {
			PdfUtils.getPdf(TransactionDef.PdfType.ORDER,
					response.getOutputStream(), pdf);
		} else {
			PdfUtils.getPdf(TransactionDef.PdfType.RECEIPTAPPLY,
					response.getOutputStream(), pdf);
		}
	}

	@Override
	public Optional<OrderCountVo> staticsAdminOrder(boolean isManage,
			boolean isSeal) {
		OrderCountVo orderCountVo = new OrderCountVo();
		orderCountVo.setWaitConfirmed(0L);
		orderCountVo.setRejected(0L);
		orderCountVo.setToBeSigned(0L);
		orderCountVo.setInvaliding(0L);
		orderCountVo.setPurchaseToBeSigned(0L);
		orderCountVo.setPurchaseRejected(0L);
		orderCountVo.setSaleToBeConfirmed(0L);
		orderCountVo.setSaleRejected(0L);
		orderCountVo.setSaleInvaliding(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		wrapper.select(Order::getContractId);
		// 采购合同
		List<String> buyContractIds = new ArrayList<>();
		// 销售合同
		List<String> saleContractIds = new ArrayList<>();
		List<Order> orderList = repository.selectList(wrapper);
		if (CollectionUtils.isNotEmpty(orderList)) {
			List<Contract> contractList = contractService.findByIds(
					orderList.stream().map(Order::getContractId).toList());
			buyContractIds = contractList.stream()
					.filter(contract -> ContractDef.ContractType.PURCHASE
							.match(contract.getContractType()))
					.map(Contract::getId).distinct().toList();
			saleContractIds = contractList.stream()
					.filter(contract -> ContractDef.ContractType.SALES
							.match(contract.getContractType()))
					.map(Contract::getId).distinct().toList();
		}
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				if (CollectionUtils.isNotEmpty(saleContractIds)) {
					// 统计销售待确认
					wrapper.clear();
					this.filterDeleted(wrapper);
					wrapper.eq(Order::getStatus,
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.CONFIRMING.getCode(),
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode()));
					wrapper.in(Order::getProjectId, projectIds);
					wrapper.in(Order::getContractId, saleContractIds);
					orderCountVo.setSaleToBeConfirmed(
							repository.selectCount(wrapper));

					// 统计销售作废中
					wrapper.clear();
					this.filterDeleted(wrapper);
					wrapper.in(Order::getStatus, List.of(
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.INVALIDING.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()),
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.INVALIDING.getCode(),
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode()),
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.INVALIDING.getCode(),
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode())));
					wrapper.in(Order::getProjectId, projectIds);
					wrapper.in(Order::getContractId, saleContractIds);
					orderCountVo
							.setSaleInvaliding(repository.selectCount(wrapper));
				} else if (CollectionUtils.isNotEmpty(buyContractIds)) {
					// 统计采购已驳回
					wrapper.clear();
					this.filterDeleted(wrapper);
					wrapper.eq(Order::getStatus,
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.REVERTED.getCode(),
									BusinessContractDef.CommonSignState.COMPLETED
											.getCode()));
					wrapper.in(Order::getProjectId, projectIds);
					wrapper.in(Order::getContractId, buyContractIds);
					orderCountVo.setPurchaseRejected(
							repository.selectCount(wrapper));

					// 统计采购作废中
					wrapper.clear();
					this.filterDeleted(wrapper);
					wrapper.in(Order::getStatus, List.of(
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.INVALIDING.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()),
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.INVALIDING.getCode(),
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode()),
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.INVALIDING.getCode(),
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode())));
					wrapper.in(Order::getProjectId, projectIds);
					wrapper.in(Order::getContractId, buyContractIds);
					orderCountVo.setPurchaseInvaliding(
							repository.selectCount(wrapper));
				}
			}
		}

		if (isSeal) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				if (CollectionUtils.isNotEmpty(saleContractIds)) {
					// 统计销售待签署
					wrapper.clear();
					this.filterDeleted(wrapper);
					wrapper.and(x -> x.eq(Order::getStatus,
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.SIGNING.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()))
							.or().eq(Order::getStatus,
									BusinessContractDef.SET_STATE.apply(
											OrderDef.Status.SIGNING.getCode(),
											BusinessContractDef.CommonSignState.BUYER_SIGNED
													.getCode())));
					wrapper.in(Order::getProjectId, projectIds);
					wrapper.in(Order::getContractId, saleContractIds);
					orderCountVo
							.setSaleRejected(repository.selectCount(wrapper));
				} else if (CollectionUtils.isNotEmpty(buyContractIds)) {
					// 统计采购待签署
					wrapper.clear();
					this.filterDeleted(wrapper);
					wrapper.and(x -> x.eq(Order::getStatus,
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.SIGNING.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()))
							.or().eq(Order::getStatus,
									BusinessContractDef.SET_STATE.apply(
											OrderDef.Status.SIGNING.getCode(),
											BusinessContractDef.CommonSignState.SUPPLY_SIGNED
													.getCode())));
					wrapper.in(Order::getProjectId, projectIds);
					wrapper.in(Order::getContractId, buyContractIds);
					orderCountVo.setPurchaseToBeSigned(
							repository.selectCount(wrapper));
				}
			}
		}
		return Optional.of(orderCountVo);
	}

	@Override
	public Optional<OrderCountVo> staticsCustomerOrder(boolean isSeal,
			boolean isPermission) {
		OrderCountVo orderCountVo = new OrderCountVo();
		orderCountVo.setWaitConfirmed(0L);
		orderCountVo.setRejected(0L);
		orderCountVo.setToBeSigned(0L);
		orderCountVo.setInvaliding(0L);
		orderCountVo.setPurchaseToBeSigned(0L);
		orderCountVo.setPurchaseRejected(0L);
		orderCountVo.setSaleToBeConfirmed(0L);
		orderCountVo.setSaleRejected(0L);
		orderCountVo.setSaleInvaliding(0L);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
		this.filterDeleted(wrapper);
		if (isPermission) {
			// 统计采购已驳回
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Order::getPurchaserId, customerId);
			wrapper.eq(Order::getStatus, BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.REVERTED.getCode(),
					BusinessContractDef.CommonSignState.COMPLETED.getCode()));
			orderCountVo.setPurchaseRejected(repository.selectCount(wrapper));

			// 统计采购作废中
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Order::getPurchaserId, customerId);
			wrapper.in(Order::getStatus, List.of(
					BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.INVALIDING.getCode(),
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode()),
					BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.INVALIDING.getCode(),
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode()),
					BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.INVALIDING.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode())));
			orderCountVo.setPurchaseInvaliding(repository.selectCount(wrapper));

			// 统计销售待确认
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Order::getSellerId, customerId);
			wrapper.eq(Order::getStatus,
					BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.CONFIRMING.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode()));
			orderCountVo.setSaleToBeConfirmed(repository.selectCount(wrapper));

			// 统计销售作废中
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Order::getSellerId, customerId);
			wrapper.in(Order::getStatus, List.of(
					BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.INVALIDING.getCode(),
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode()),
					BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.INVALIDING.getCode(),
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode()),
					BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.INVALIDING.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode())));
			orderCountVo.setSaleInvaliding(repository.selectCount(wrapper));
		}

		if (isSeal) {
			// 统计采购待签署
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.and(x -> x
					.eq(Order::getStatus,
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.SIGNING.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()))
					.or()
					.eq(Order::getStatus, BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.SIGNING.getCode(),
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode())));
			wrapper.eq(Order::getPurchaserId, customerId);
			orderCountVo.setPurchaseToBeSigned(repository.selectCount(wrapper));

			// 统计销售待签署
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.and(x -> x
					.eq(Order::getStatus,
							BusinessContractDef.SET_STATE.apply(
									OrderDef.Status.SIGNING.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()))
					.or()
					.eq(Order::getStatus, BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.SIGNING.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode())));
			wrapper.eq(Order::getSellerId, customerId);
			orderCountVo.setSaleRejected(repository.selectCount(wrapper));
		}

		return Optional.of(orderCountVo);
	}

	// 处理采购模块的已完成签收的发货信息的规格对应的签收数量
	private void handleBuyReceiptDeliverGoods(List<DeliverGoods> filteredList,
			Map<String, BigDecimal> modelToReceiptWeightMap) {
		for (DeliverGoods deliverGoods : filteredList) {
			// 采购对账单关联的订单 里面的货物信息
			List<GoodsInfo> goodsInfos = this
					.convertGoodsInfo(deliverGoods.getGoodsInfo());
			for (GoodsInfo goodsInfo : goodsInfos) {
				String model = goodsInfo.getModel();
				BigDecimal receiptQuantity = goodsInfo.getReceiptQuantity();
				this.handleModelReceipt(modelToReceiptWeightMap, model,
						receiptQuantity);
			}
		}
	}

	// 处理销售模块 已完成签收的发货信息处理对应的规格和签收数量
	private void handleSaleReceiptDeliverGoods(List<DeliverGoods> filteredList,
			Map<String, BigDecimal> modelToReceiptWeightMap,
			Map<String, List<TransportOrderShip>> transportOrderShipMap,
			Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap,
			Map<String, List<TransportOrderRailway>> transportOrderRailwayMap) {
		for (DeliverGoods deliverGoods : filteredList) {
			if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
					.match(deliverGoods.getDelivery())) {
				List<GoodsInfo> goodsInfos = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				for (GoodsInfo goodsInfo : goodsInfos) {
					String model = goodsInfo.getModel();
					BigDecimal receiptQuantity = goodsInfo.getReceiptQuantity();
					this.handleModelReceipt(modelToReceiptWeightMap, model,
							receiptQuantity);
				}
			} else if (DeliverGoodsDef.DeliverWay.SHIPPING
					.match(deliverGoods.getDelivery())) {
				List<TransportOrderShip> transportOrderShipList = transportOrderShipMap
						.get(deliverGoods.getId());
				if (org.apache.commons.collections4.CollectionUtils
						.isNotEmpty(transportOrderShipList)) {
					// 循环发货信息里面的货物信息
					for (TransportOrderShip transportOrderShip : transportOrderShipList) {
						String model = transportOrderShip.getModel();
						BigDecimal receiptQuantity = transportOrderShip
								.getReceiptQuantity();
						this.handleModelReceipt(modelToReceiptWeightMap, model,
								receiptQuantity);
					}
				}
			} else if (DeliverGoodsDef.DeliverWay.CAR
					.match(deliverGoods.getDelivery())) {
				List<TransportOrderVehicle> transportOrderVehicleList = transportOrderVehicleMap
						.get(deliverGoods.getId());
				if (org.apache.commons.collections4.CollectionUtils
						.isNotEmpty(transportOrderVehicleList)) {
					// 循环发货信息里面的货物信息
					for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicleList) {
						String model = transportOrderVehicle.getGoodsType();
						BigDecimal receiptQuantity = transportOrderVehicle
								.getReceiptQuantity();
						this.handleModelReceipt(modelToReceiptWeightMap, model,
								receiptQuantity);
					}
				}
			} else if (DeliverGoodsDef.DeliverWay.TRAIN
					.match(deliverGoods.getDelivery())) {
				List<TransportOrderRailway> transportOrderRailwayList = transportOrderRailwayMap
						.get(deliverGoods.getId());
				if (org.apache.commons.collections4.CollectionUtils
						.isNotEmpty(transportOrderRailwayList)) {
					// 循环发货信息里面的货物信息
					for (TransportOrderRailway transportOrderRailway : transportOrderRailwayList) {
						String model = transportOrderRailway.getModel();
						BigDecimal receiptQuantity = transportOrderRailway
								.getReceiptQuantity();
						this.handleModelReceipt(modelToReceiptWeightMap, model,
								receiptQuantity);
					}
				}
			}
		}
	}

	// 处理规格对应的签收重量
	private void handleModelReceipt(
			Map<String, BigDecimal> modelToReceiptWeightMap, String model,
			BigDecimal receiptQuantity) {
		// 初始化签收重量
		if (Objects.isNull(receiptQuantity)) {
			receiptQuantity = BigDecimal.ZERO;
		}
		// 如果map中存在对应的规格
		if (modelToReceiptWeightMap.containsKey(model)) {
			// 取出规格对应的签收重量
			BigDecimal oldReceiptQuantity = modelToReceiptWeightMap.get(model);
			// 将本次的重量进行相加
			BigDecimal newReceiptQuantity = oldReceiptQuantity
					.add(receiptQuantity);
			// 更新对应规格的签收重量
			modelToReceiptWeightMap.put(model, newReceiptQuantity);

		} else {
			// 不存在对应的规格 则将本次的插入Map
			modelToReceiptWeightMap.put(model, receiptQuantity);
		}
	}

	// 处理规格对应的发货重量
	private void handleModelDeliveryWeight(
			Map<String, BigDecimal> modelToDeliveryWeightMap, String model,
			BigDecimal deliverGoodsWeight) {
		// 初始化发货重量
		if (Objects.isNull(deliverGoodsWeight)) {
			deliverGoodsWeight = BigDecimal.ZERO;
		}
		// 如果map中存在对应的规格
		if (modelToDeliveryWeightMap.containsKey(model)) {
			// 取出规格对应的发货重量
			BigDecimal oldReceiptQuantity = modelToDeliveryWeightMap.get(model);
			// 将本次的发货重量进行相加
			BigDecimal newReceiptQuantity = oldReceiptQuantity
					.add(deliverGoodsWeight);
			// 更新对应规格的发货重量
			modelToDeliveryWeightMap.put(model, newReceiptQuantity);

		} else {
			// 不存在对应的规格 则将本次的插入Map
			modelToDeliveryWeightMap.put(model, deliverGoodsWeight);
		}
	}

	// 处理规格对应的订单重量
	private void handleModelOrderWeight(
			Map<String, BigDecimal> modelToOrderWeightMap, String model,
			BigDecimal orderWeight) {
		// 初始化发货重量
		if (Objects.isNull(orderWeight)) {
			orderWeight = BigDecimal.ZERO;
		}
		// 如果map中存在对应的规格
		if (modelToOrderWeightMap.containsKey(model)) {
			// 取出规格对应的发货重量
			BigDecimal oldOrderWeight = modelToOrderWeightMap.get(model);
			// 将本次的发货重量进行相加
			BigDecimal newReceiptQuantity = oldOrderWeight.add(orderWeight);
			// 更新对应规格的发货重量
			modelToOrderWeightMap.put(model, newReceiptQuantity);

		} else {
			// 不存在对应的规格 则将本次的插入Map
			modelToOrderWeightMap.put(model, orderWeight);
		}
	}

	/**
	 * @description: 返回vos
	 * @author: 彭湃
	 * @date: 2025/1/18 11:52
	 * @param: [list,
	 *             projects]
	 * @return: java.util.List<com.zhihaoscm.domain.bean.vo.OrderVo>
	 **/
	private List<OrderVo> packVos(List<Order> list, List<Contract> contracts) {

		if (CollectionUtils.isEmpty(contracts)) {
			contracts = contractService.findByIds(list.stream()
					.map(Order::getContractId).distinct().toList());
		}
		// 查询合同信息
		List<String> projectIds = list.stream().map(Order::getProjectId)
				.distinct().toList();
		Map<String, Project> projectMap = projectService.findByIds(projectIds)
				.stream().collect(Collectors.toMap(Project::getId, e -> e));
		Map<String, Contract> contractMap = contracts.stream()
				.collect(Collectors.toMap(Contract::getId, e -> e));
		return list.stream().map(e -> {
			OrderVo orderVo = new OrderVo();
			orderVo.setOrder(e);
			orderVo.setProject(projectMap.get(e.getProjectId()));
			orderVo.setContract(contractMap.get(e.getContractId()));
			return orderVo;
		}).toList();
	}

	/**
	 * @description: 返回vos
	 * @author: 许晶
	 */
	private List<OrderVo> packOrders(List<Order> list) {
		if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
			return List.of();
		}
		List<OrderVo> orderVos = new ArrayList<>();
		// 查询发货信息
		List<String> orderIds = list.stream().map(Order::getId).distinct()
				.toList();
		// 根据订单id查询发货信息
		List<DeliverGoodsVo> deliverGoodsList = deliverGoodsService
				.findVosByOrderIds(orderIds);
		// 将发货信息根据订单id分组
		Map<String, List<DeliverGoodsVo>> deliverGoodsVoMap = deliverGoodsList
				.stream()
				.collect(Collectors.groupingBy(deliverGoodsVo -> deliverGoodsVo
						.getDeliverGoods().getOrderId()));
		// 查询订单所关联的费用类型为订单保证金的付款单
		List<Payment> payments = paymentService.findByOrders(orderIds,
				PaymentDef.State.COMPLETED.getCode(),
				PaymentDef.CostType.ORDER_DEPOSIT.getCode(), null);
		// 将支付记录根据订单id进行分组
		Map<String, List<Payment>> paymentMap = payments.stream()
				.collect(Collectors.groupingBy(Payment::getOrderId));
		for (Order e : list) {
			OrderVo orderVo = new OrderVo();
			// 设置订单信息
			orderVo.setOrder(e);
			// 设置发货信息
			orderVo.setDeliverGoodsVoList(deliverGoodsVoMap.get(e.getId()));
			orderVos.add(orderVo);
			List<Payment> paymentList = paymentMap.get(e.getId());
			// 设置已收到的订单保证金
			if (CollectionUtils.isNotEmpty(paymentList)) {
				orderVo.setReceivedOrderDeposit(
						paymentList.stream().reduce(BigDecimal.ZERO,
								(sum, item) -> sum.add(item.getAmount()),
								BigDecimal::add));
			}

		}
		return orderVos;
	}

	private List<DeliverGoodsVo> packDeliverGoodsVos(List<DeliverGoods> list) {
		// 签收信息
		List<String> signReceiptIds = list.stream()
				.map(DeliverGoods::getSignReceiptId).distinct().toList();
		List<SignReceipt> signReceipts = signReceiptService
				.findByIds(signReceiptIds);
		Map<String, SignReceipt> signReceiptMap = signReceipts.stream()
				.collect(Collectors.toMap(SignReceipt::getId, i -> i));
		// 发货单id
		List<String> deliverGoodsIds = list.stream().map(DeliverGoods::getId)
				.distinct().toList();
		// 船运单信息
		Map<String, List<TransportOrderShip>> transportOrderShipMap = this
				.getTransportOrderShipMap(deliverGoodsIds);
		// 汽运单信息
		Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = this
				.getTransportOrderVehicleMap(deliverGoodsIds);
		// 铁路单信息
		Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = this
				.getTransportOrderRailwayMap(deliverGoodsIds);
		return list.stream().map(e -> {
			DeliverGoodsVo deliverGoodsVo = new DeliverGoodsVo();
			deliverGoodsVo.setDeliverGoods(e);
			deliverGoodsVo
					.setSignReceipt(signReceiptMap.get(e.getSignReceiptId()));
			// 发货方式为船运时设置船运单信息
			if (DeliverGoodsDef.DeliverWay.SHIPPING.match(e.getDelivery())) {
				deliverGoodsVo.setTransportOrderShips(
						transportOrderShipMap.get(e.getId()));
			}
			// 发货方式为汽运时设置汽运单信息
			if (DeliverGoodsDef.DeliverWay.CAR.match(e.getDelivery())) {
				deliverGoodsVo.setTransportOrderVehicles(
						transportOrderVehicleMap.get(e.getId()));
			}
			// 发货方式为铁路时设置铁路单信息
			if (DeliverGoodsDef.DeliverWay.TRAIN.match(e.getDelivery())) {
				deliverGoodsVo.setTransportOrderRailways(
						transportOrderRailwayMap.get(e.getId()));
			}
			return deliverGoodsVo;
		}).toList();
	}

	/**
	 * @description: 获取文件id，生成pdf文件并上传文件服务器，返回文件id
	 * @author: 彭湃
	 * @date: 2025/1/23 15:26
	 * @param: [resource]
	 * @return: java.lang.Long
	 **/
	private Long getFileId(Order resource) {

		// 生成pdf
		OutputStream outputStream = new ByteArrayOutputStream();
		GeneratPdfDto pdf = generatPdfDto(resource.getId());
		if (OrderDef.OrderType.PURCHASE.getCode().equals(resource.getType())) {
			PdfUtils.getPdf(TransactionDef.PdfType.ORDER, outputStream, pdf);
		} else {
			PdfUtils.getPdf(TransactionDef.PdfType.RECEIPTAPPLY, outputStream,
					pdf);
		}
		try {
			CustomMultipartFile convert = MultipartFileUtils.convert(
					outputStream, "订单合同" + resource.getId(),
					"订单合同" + resource.getId() + ".pdf", "text/plain");
			File file = fileService
					.upload(convert, "订单合同" + resource.getId() + ".pdf")
					.orElse(null);
			if (Objects.nonNull(file)) {
				return file.getId();
			}
		} catch (Exception e) {
			log.error("生成pdf文件失败", e);
		}
		return null;
	}

	private List<GoodsInfo> convertGoodsInfo(String data) {
		Gson gson = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
		Type listType = new TypeToken<List<GoodsInfo>>() {
		}.getType();
		return gson.fromJson(data, listType);
	}

	/**
	 * 设置导出响应头
	 *
	 * @param response
	 */
	private void setExportResponseFields(HttpServletResponse response,
			String id) {
		response.setContentType("application/pdf");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(
				String.format("订单_%s_%s",
						DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
								.format(LocalDateTime.now()),
						id),
				StandardCharsets.UTF_8).replaceAll("\\+", "%20");
		response.setHeader("Content-disposition",
				"attachment;filename*=utf-8''" + fileName + ".pdf");
	}

	/**
	 * 生成pdf转化类方法
	 *
	 * @param orderId
	 * @return
	 */
	private GeneratPdfDto generatPdfDto(String orderId) {
		OrderVo order = findVoById(orderId);
		Order or = order.getOrder();
		Contract contract = order.getContract();
		GeneratPdfDto pdf = new GeneratPdfDto();
		pdf.setBillId(orderId);
		pdf.setBuyerName(or.getPurchaserEnterprise().getName());

		pdf.setSellerName(or.getSellerEnterprise().getName());
		pdf.setContractName(contract.getName());
		DateTimeFormatter formatter = DateTimeFormatter
				.ofPattern("【yyyy】年【MM】月【dd】日");
		pdf.setContractSignDate(formatter.format(contract.getSignDate()));
		pdf.setContractId(contract.getId());
		pdf.setDeliverWay(
				DeliverGoodsDef.DeliverWay.from(or.getLogisticsWay()));
		pdf.setOrderRemark(or.getRemark());
		pdf.setGoodsInfoList(or.getGoodsInfo());
		return pdf;
	}

	private List<OrderVo> getOrderVos(List<DeliverGoods> deliverGoodsList) {
		if (CollectionUtils.isEmpty(deliverGoodsList)) {
			return List.of();
		}
		Map<String, List<DeliverGoods>> collect = deliverGoodsList.stream()
				.collect(Collectors.groupingBy(DeliverGoods::getOrderId));
		List<String> list = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).toList();
		List<Order> orderList = this.findByIds(list);
		return orderList.stream().map(e -> {
			OrderVo orderVo = new OrderVo();
			orderVo.setOrder(e);
			orderVo.setDeliverGoodsList(collect.get(e.getId()));
			if (CollectionUtils.isNotEmpty(collect.get(e.getId()))) {
				BigDecimal sum = collect.get(e.getId()).stream()
						.map(DeliverGoods::getGoodsTotalQuantity)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
				orderVo.setUnReceiptWeight(sum);
			}
			return orderVo;
		}).toList();
	}

	// 计算已完成发货的金额
	private BigDecimal getBigDecimal(List<DeliverGoods> deliverGoodsList1,
			Map<String, BigDecimal> modelToPriceMap, BigDecimal orderAmount2,
			Integer type) {
		// 过滤出发货单的发货进度超过100%且发货状态为发货完成的发货信息列表
		List<DeliverGoods> deliverGoodsList2 = deliverGoodsList1.stream()
				.filter(deliverGoods -> DeliverGoodsDef.Status.DELIVER_COMPLETE
						.match(deliverGoods.getStatus()))
				.filter(deliverGoods -> deliverGoods
						.getActualGoodsTotalQuantity()
						.compareTo(deliverGoods.getGoodsTotalQuantity()) >= 0)
				.toList();
		// 如果不存在 发货单的发货进度超过100%且发货状态为发货完成的时候，仍然用订单数量也就是订单里的合计金额
		if (CollectionUtils.isNotEmpty(deliverGoodsList2)) {
			if (ContractDef.ContractType.SALES.match(type)) {
				for (DeliverGoods deliverGoods : deliverGoodsList2) {
					switch (DeliverGoodsDef.DeliverWay
							.from(deliverGoods.getDelivery())) {
						case SHIPPING -> {
							// 船运
							List<TransportOrderShip> transportOrderShips = transportOrderShipService
									.findByDeliverGoodsIds(
											List.of(deliverGoods.getId()));
							if (CollectionUtils
									.isNotEmpty(transportOrderShips)) {
								for (TransportOrderShip transportOrderShip : transportOrderShips) {
									// 发货信息里面的发货重量*订单单价
									BigDecimal price = modelToPriceMap
											.get(transportOrderShip.getModel());
									Integer ton = transportOrderShip.getTon();
									if (Objects.nonNull(price)
											&& Objects.nonNull(ton)) {
										orderAmount2 = orderAmount2
												.add(new BigDecimal(ton)
														.multiply(price));
									}
								}
							}
						}
						case CAR -> {
							// 汽运
							List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
									.findByDeliverGoodsIds(
											List.of(deliverGoods.getId()));
							if (CollectionUtils
									.isNotEmpty(transportOrderVehicles)) {
								for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicles) {
									// 发货信息里面的发货重量*订单单价
									BigDecimal price = modelToPriceMap
											.get(transportOrderVehicle
													.getGoodsType());
									BigDecimal quantity = transportOrderVehicle
											.getTransportWeight();
									if (Objects.nonNull(price)
											&& Objects.nonNull(quantity)) {
										orderAmount2 = orderAmount2
												.add(quantity.multiply(price));
									}
								}
							}
						}
						case TRAIN -> {
							// 铁路
							List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
									.findByDeliverGoodsIds(
											List.of(deliverGoods.getId()));
							if (CollectionUtils
									.isNotEmpty(transportOrderRailways)) {
								for (TransportOrderRailway transportOrderRailway : transportOrderRailways) {
									// 发货信息里面的发货重量*订单单价
									BigDecimal price = modelToPriceMap.get(
											transportOrderRailway.getModel());
									BigDecimal quantity = transportOrderRailway
											.getTransportWeight();
									if (Objects.nonNull(price)
											&& Objects.nonNull(quantity)) {
										orderAmount2 = orderAmount2
												.add(quantity.multiply(price));
									}
								}
							}
						}
						case SELF_PICKUP -> {
							// 自提-用发货信息里面的货物信息
							List<GoodsInfo> deliverGoodsInfoList = this
									.convertGoodsInfo(
											deliverGoods.getGoodsInfo());
							if (CollectionUtils
									.isNotEmpty(deliverGoodsInfoList)) {
								for (GoodsInfo goodsInfo : deliverGoodsInfoList) {
									// 发货信息里面的发货重量*订单单价
									BigDecimal price = modelToPriceMap
											.get(goodsInfo.getModel());
									BigDecimal quantity = goodsInfo
											.getDeliveryQuantity();
									if (Objects.nonNull(price)
											&& Objects.nonNull(quantity)) {
										orderAmount2 = orderAmount2
												.add(quantity.multiply(price));
									}
								}
							}
						}
					}
				}
			} else {
				for (DeliverGoods deliverGoods : deliverGoodsList2) {
					// 采购直接 用发货信息里面的货物信息
					List<GoodsInfo> deliverGoodsInfoList = this
							.convertGoodsInfo(deliverGoods.getGoodsInfo());
					if (CollectionUtils.isNotEmpty(deliverGoodsInfoList)) {
						for (GoodsInfo goodsInfo : deliverGoodsInfoList) {
							// 发货信息里面的发货重量*订单单价
							BigDecimal price = modelToPriceMap
									.get(goodsInfo.getModel());
							BigDecimal quantity = goodsInfo
									.getDeliveryQuantity();
							if (Objects.nonNull(price)
									&& Objects.nonNull(quantity)) {
								orderAmount2 = orderAmount2
										.add(quantity.multiply(price));
							}
						}
					}
				}
			}
		}
		return orderAmount2;
	}

	// 获取采购模块发货信息里面的签收总数量
	private BigDecimal getBuyOrderReceiptWeight(
			List<DeliverGoods> deliverGoodsList) {
		// 订单数量/重量
		BigDecimal orderReceiptWeight = BigDecimal.ZERO;
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			// 如果发货方式为合并发货，则订单数量/重量为发货信息里面的 签收数量/重量之和
			if (DeliverGoodsDef.ReceiveWay.MERGE
					.match(deliverGoods.getReceiveWay())) {
				orderReceiptWeight = orderReceiptWeight.add(Objects
						.requireNonNullElse(deliverGoods.getReceiptWeight(),
								BigDecimal.ZERO));
			} else {
				// 采购对账单关联的订单 里面的货物信息
				List<GoodsInfo> goodsInfos = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				// 订单数量/重量 为订单货物信息里面的 签收数量/重量之和
				BigDecimal orderReceiptWeight1 = goodsInfos.stream()
						.map(goodsInfo -> Objects.requireNonNullElse(
								goodsInfo.getReceiptQuantity(),
								BigDecimal.ZERO))
						.reduce(BigDecimal.ZERO, BigDecimal::add);
				orderReceiptWeight = orderReceiptWeight
						.add(orderReceiptWeight1);
			}
		}
		return orderReceiptWeight;
	}

	// 获取销售模块发货信息里面的签收总数量
	private BigDecimal getSellOrderReceiptWeight(
			List<DeliverGoods> deliverGoodsList) {
		// 订单已签收数量/重量
		BigDecimal orderReceiptWeight = BigDecimal.ZERO;
		List<String> deliverGoodsIds = deliverGoodsList.stream()
				.map(DeliverGoods::getId).distinct().toList();
		// 船运单信息
		Map<String, List<TransportOrderShip>> transportOrderShipMap = this
				.getTransportOrderShipMap(deliverGoodsIds);
		// 汽运运单信息
		Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = this
				.getTransportOrderVehicleMap(deliverGoodsIds);
		// 铁路单信息
		Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = this
				.getTransportOrderRailwayMap(deliverGoodsIds);

		for (DeliverGoods deliverGoods : deliverGoodsList) {
			// 如果发货方式为合并发货，则订单数量/重量为发货信息里面的 签收数量/重量之和
			if (DeliverGoodsDef.ReceiveWay.MERGE
					.match(deliverGoods.getReceiveWay())) {
				orderReceiptWeight = orderReceiptWeight.add(Objects
						.requireNonNullElse(deliverGoods.getReceiptWeight(),
								BigDecimal.ZERO));
			} else {
				if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
						.match(deliverGoods.getDelivery())) {
					List<GoodsInfo> goodsInfos = this
							.convertGoodsInfo(deliverGoods.getGoodsInfo());
					// 循环发货信息里面的货物信息
					for (GoodsInfo goodsInfo : goodsInfos) {
						// 将货物信息里面的 签收数量相加
						orderReceiptWeight = orderReceiptWeight
								.add(Objects.requireNonNullElse(
										goodsInfo.getReceiptQuantity(),
										BigDecimal.ZERO));
					}
				} else if (DeliverGoodsDef.DeliverWay.SHIPPING
						.match(deliverGoods.getDelivery())) {
					List<TransportOrderShip> transportOrderShipList = transportOrderShipMap
							.get(deliverGoods.getId());
					if (org.apache.commons.collections4.CollectionUtils
							.isNotEmpty(transportOrderShipList)) {
						// 循环发货信息里面的货物信息
						for (TransportOrderShip transportOrderShip : transportOrderShipList) {
							// 将货物信息里面的 签收数量相加
							orderReceiptWeight = orderReceiptWeight
									.add(Objects.requireNonNullElse(
											transportOrderShip
													.getReceiptQuantity(),
											BigDecimal.ZERO));
						}
					}
				} else if (DeliverGoodsDef.DeliverWay.CAR
						.match(deliverGoods.getDelivery())) {
					List<TransportOrderVehicle> transportOrderVehicleList = transportOrderVehicleMap
							.get(deliverGoods.getId());
					if (org.apache.commons.collections4.CollectionUtils
							.isNotEmpty(transportOrderVehicleList)) {
						// 循环发货信息里面的货物信息
						for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicleList) {
							// 将货物信息里面的 签收数量相加
							orderReceiptWeight = orderReceiptWeight
									.add(Objects.requireNonNullElse(
											transportOrderVehicle
													.getReceiptQuantity(),
											BigDecimal.ZERO));
						}
					}
				} else if (DeliverGoodsDef.DeliverWay.TRAIN
						.match(deliverGoods.getDelivery())) {
					List<TransportOrderRailway> transportOrderRailwayList = transportOrderRailwayMap
							.get(deliverGoods.getId());
					if (org.apache.commons.collections4.CollectionUtils
							.isNotEmpty(transportOrderRailwayList)) {
						// 循环发货信息里面的货物信息
						for (TransportOrderRailway transportOrderRailway : transportOrderRailwayList) {
							// 将货物信息里面的 签收数量相加
							orderReceiptWeight = orderReceiptWeight
									.add(Objects.requireNonNullElse(
											transportOrderRailway
													.getReceiptQuantity(),
											BigDecimal.ZERO));
						}
					}
				}
			}
		}
		return orderReceiptWeight;
	}

	/**
	 * 将有签收单的订单列表 返回 至少有一个签收单是签收完成的。
	 *
	 * @param orders
	 * @return
	 */
	private List<Order> filterHasCompletedSignReceipt(List<Order> orders) {
		if (CollectionUtils.isEmpty(orders)) {
			return List.of();
		}
		// 提取传入的订单ids，排除null
		List<String> orderIds = orders.stream().map(Order::getId)
				.filter(StringUtils::isNotBlank).toList();
		if (CollectionUtils.isEmpty(orderIds)) {
			return List.of();
		}
		// 关联这些订单的签收单
		List<SignReceipt> signReceiptList = signReceiptService
				.findByOrderIds(orderIds);
		if (CollectionUtils.isEmpty(signReceiptList)) {
			return List.of();
		}
		// 过滤出签收完成的签收单
		List<SignReceipt> completedSignReceiptList = signReceiptList.stream()
				.filter(signReceipt -> signReceipt != null
						&& SignReceiptDef.Status.FINISHED
								.match(signReceipt.getStatus()))
				.toList();
		if (CollectionUtils.isEmpty(completedSignReceiptList)) {
			return List.of();
		}
		// 提取所有已完成签收单中关联的订单ID（可能是多个ID存在一个 ArrayString 中）
		Set<String> hasCompletedSignReceiptOrderIds = completedSignReceiptList
				.stream().map(SignReceipt::getRelatedOrderIds)
				.filter(Objects::nonNull).flatMap(arrayString -> {
					try {
						return arrayString.stream()
								.filter(StringUtils::isNotBlank);
					} catch (Exception e) {
						// 防止 arrayString 不可遍历时报错
						return Stream.empty();
					}
				}).collect(Collectors.toSet());
		if (CollectionUtils.isEmpty(hasCompletedSignReceiptOrderIds)) {
			return List.of();
		}
		// 从传入 orders 中筛选出这些已完成签收的订单
		return orders.stream().filter(order -> order != null
				&& StringUtils.isNotBlank(order.getId())
				&& hasCompletedSignReceiptOrderIds.contains(order.getId()))
				.toList();
	}

	/**
	 * 没有签收完成的订单
	 *
	 * @param orders
	 * @return
	 */
	private List<Order> filterHasNoCompletedSignReceipt(List<Order> orders) {
		if (CollectionUtils.isEmpty(orders)) {
			return List.of();
		}
		// 提取传入的订单ids，排除null
		List<String> orderIds = orders.stream().map(Order::getId)
				.filter(StringUtils::isNotBlank).toList();
		if (CollectionUtils.isEmpty(orderIds)) {
			return List.of();
		}
		// 关联这些订单的签收单
		List<SignReceipt> signReceiptList = signReceiptService
				.findByOrderIds(orderIds);
		// 订单没有关联签收单 则返回所有订单
		if (CollectionUtils.isEmpty(signReceiptList)) {
			return orders;
		} else {
			// 过滤出签收完成的签收单
			List<SignReceipt> completedSignReceiptList = signReceiptList
					.stream()
					.filter(signReceipt -> signReceipt != null
							&& SignReceiptDef.Status.FINISHED
									.match(signReceipt.getStatus()))
					.toList();
			// 没有签收完成的签收单时 返回所有订单
			if (CollectionUtils.isEmpty(completedSignReceiptList)) {
				return orders;
			} else {
				// 提取所有完成签收单中关联的订单ID（可能是多个ID存在一个 ArrayString 中）
				Set<String> hasCompletedSignReceiptOrderIds = completedSignReceiptList
						.stream().map(SignReceipt::getRelatedOrderIds)
						.filter(Objects::nonNull).flatMap(arrayString -> {
							try {
								return arrayString.stream()
										.filter(StringUtils::isNotBlank);
							} catch (Exception e) {
								// 防止 arrayString 不可遍历时报错
								return Stream.empty();
							}
						}).collect(Collectors.toSet());
				// 去除掉已经关联完成签收单的数据 就是没有关联签收单或者没有存在完成状态的签收单的数据
				return orders.stream()
						.filter(order -> !hasCompletedSignReceiptOrderIds
								.contains(order.getId()))
						.toList();
			}
		}
	}

	// 获取订单平均单价接口
	private BigDecimal getAveUnitPrice(Order order) {
		// 订单里面的货物信息
		List<GoodsInfo> orderGoodsInfoList = this
				.convertGoodsInfo(order.getGoodsInfo());
		// 订单单价之和
		BigDecimal totalPrice = BigDecimal.ZERO;
		// 订单
		int num = 0;
		if (CollectionUtils.isNotEmpty(orderGoodsInfoList)) {
			for (GoodsInfo orderGoodsInfo : orderGoodsInfoList) {
				num = num + 1;
				if (Objects.nonNull(orderGoodsInfo.getUnitPrice())) {
					totalPrice = totalPrice.add(orderGoodsInfo.getUnitPrice());
				}
			}
		}
		// 订单平均单价
		BigDecimal aveUnitPrice = BigDecimal.ZERO;
		if (num > 0) {
			aveUnitPrice = totalPrice.divide(BigDecimal.valueOf(num), 2,
					RoundingMode.HALF_UP);
		}
		return aveUnitPrice;
	}

	// 根据发货单id查询船运单
	private Map<String, List<TransportOrderShip>> getTransportOrderShipMap(
			List<String> deliverGoodsIds) {
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		return transportOrderShips.stream()
				.collect(Collectors.groupingBy(TransportOrderShip::getGoodsId));
	}

	// 根据发货单id查询铁路单
	private Map<String, List<TransportOrderRailway>> getTransportOrderRailwayMap(
			List<String> deliverGoodsIds) {
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		return transportOrderRailways.stream().collect(Collectors
				.groupingBy(TransportOrderRailway::getDeliverGoodsId));
	}

	// 根据发货单id查询汽运单
	private Map<String, List<TransportOrderVehicle>> getTransportOrderVehicleMap(
			List<String> deliverGoodsIds) {
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		return transportOrderVehicles.stream().collect(Collectors
				.groupingBy(TransportOrderVehicle::getDeliverGoodsId));
	}

	/**
	 * 发送短信
	 *
	 * @param order
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(Order order, String templateCode, String title,
			Integer type) {
		Customer customer = null;
		if (ContractDef.ContractType.SALES.match(type)) {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(order.getPurchaserBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		} else {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(order.getSellerBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("order_id", order.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.ORDER.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.FIND_BOAT_DETAIL_PAGE)
						.detailId(String.valueOf(order.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}
}
