package com.zhihaoscm.service.resource.validator.payment;

import java.math.BigDecimal;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Refund;
import com.zhihaoscm.service.core.service.ContractService;
import com.zhihaoscm.service.core.service.RefundService;
import com.zhihaoscm.service.resource.form.payment.CustomPaymentForm;
import com.zhihaoscm.service.resource.form.payment.PaymentAccountsForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Contract;
import com.zhihaoscm.domain.bean.entity.Payment;
import com.zhihaoscm.domain.bean.json.PaymentInfo;
import com.zhihaoscm.domain.meta.biz.ContractDef;
import com.zhihaoscm.domain.meta.biz.PaymentDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.AccountsService;
import com.zhihaoscm.service.core.service.PaymentService;
import com.zhihaoscm.service.resource.form.payment.PaymentForm;
import com.zhihaoscm.service.resource.form.payment.PaymentRejectForm;
import com.zhihaoscm.service.resource.validator.contract.ContractValidator;
import com.zhihaoscm.service.resource.validator.project.ProjectValidator;

/**
 * 收付款校验器
 */
@Component
public class PaymentValidator {
	@Autowired
	private PaymentService service;
	@Autowired
	private ProjectValidator projectValidator;
	@Autowired
	private ContractValidator contractValidator;

    @Autowired
    private ContractService contractService;

    @Autowired
    private RefundService refundService;


	/**
	 * 校验付款信息是否存在
	 *
	 * @param id
	 * @return
	 */
	public Payment validateExist(String id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30153001));
	}

	/**
	 * 校验收款信息是否可以新增
	 *
	 * @param payment
	 * @return
	 */
	private void validatePaymentInfo(Payment payment) {

		Integer paymentWay = payment.getPaymentWay();
		PaymentInfo paymentInfo = payment.getPaymentInfo();
		// 根据付款方式校验paymentInfo对应的信息
		switch (PaymentDef.PaymentWay.from(paymentWay)) {
			case CASH -> payment.setPaymentInfo(new PaymentInfo());
			case BANK_TRANSFER -> {
				// （采购）（销售）付款方式为银行转账时，收付款的银行账户id、开户名称、银行账号、开户行必填，付款账户、收款账户自行拼接
				if (Objects.isNull(paymentInfo)
						|| Objects.isNull(paymentInfo.getPurchaserBankId())) {
					throw new BadRequestException(ErrorCode.CODE_30153023);
				}
				if (StringUtils.isBlank(paymentInfo.getPurchaserBankName())) {
					throw new BadRequestException(ErrorCode.CODE_30153024);
				}
				if (StringUtils
						.isBlank(paymentInfo.getPurchaserBankAccount())) {
					throw new BadRequestException(ErrorCode.CODE_30153025);
				}
				if (StringUtils
						.isBlank(paymentInfo.getPurchaserOpeningBank())) {
					throw new BadRequestException(ErrorCode.CODE_30153026);
				}
				if (Objects.isNull(paymentInfo.getSellerBankId())) {
					throw new BadRequestException(ErrorCode.CODE_30153027);
				}
				if (StringUtils.isBlank(paymentInfo.getSellerBankName())) {
					throw new BadRequestException(ErrorCode.CODE_30153028);
				}
				if (StringUtils.isBlank(paymentInfo.getSellerBankAccount())) {
					throw new BadRequestException(ErrorCode.CODE_30153029);
				}
				if (StringUtils.isBlank(paymentInfo.getSellerOpeningBank())) {
					throw new BadRequestException(ErrorCode.CODE_30153030);
				}

				PaymentInfo paymentInfo1 = new PaymentInfo();
				paymentInfo1
						.setPurchaserBankId(paymentInfo.getPurchaserBankId());
				paymentInfo1.setPurchaserBankName(
						paymentInfo.getPurchaserBankName());
				paymentInfo1.setPurchaserBankAccount(
						paymentInfo.getPurchaserBankAccount());
				paymentInfo1.setPurchaserOpeningBank(
						paymentInfo.getPurchaserOpeningBank());
				paymentInfo1.setPurchaserBank(paymentInfo.getPurchaserBank());
				paymentInfo1.setSellerBankId(paymentInfo.getSellerBankId());
				paymentInfo1.setSellerBankName(paymentInfo.getSellerBankName());
				paymentInfo1.setSellerBankAccount(
						paymentInfo.getSellerBankAccount());
				paymentInfo1.setSellerOpeningBank(
						paymentInfo.getSellerOpeningBank());
				paymentInfo1.setSellerBank(paymentInfo.getSellerBank());
				payment.setPaymentInfo(paymentInfo1);
			}
			case BANK_ACCEPTANCE, COMMERCIAL_ACCEPTANCE -> {
				PaymentInfo paymentInfo1 = new PaymentInfo();
				if (Objects.nonNull(paymentInfo)) {
					// 付款方式为银行承兑汇票和商业承兑汇票时，票据号码不超过30位数字，承兑人不超过25字符
					if (StringUtils.isNotBlank(paymentInfo.getTicketNumber())) {
						if (paymentInfo.getTicketNumber().length() > 30
								|| !paymentInfo.getTicketNumber()
										.matches("\\d{0,30}")) {
							throw new BadRequestException(
									ErrorCode.CODE_30153031);
						}
					}
					if (StringUtils
							.isNotBlank(paymentInfo.getTicketAcceptor())) {
						if (paymentInfo.getTicketAcceptor().length() > 25) {
							throw new BadRequestException(
									ErrorCode.CODE_30153032);
						}
					}
					paymentInfo1.setTicketNumber(paymentInfo.getTicketNumber());
					paymentInfo1
							.setTicketingDate(paymentInfo.getTicketingDate());
					paymentInfo1.setTicketExpireDate(
							paymentInfo.getTicketExpireDate());
					paymentInfo1
							.setTicketAcceptor(paymentInfo.getTicketAcceptor());
				}
				payment.setPaymentInfo(paymentInfo1);
			}
			case LETTER_OF_CREDIT -> {
				PaymentInfo paymentInfo1 = new PaymentInfo();
				if (Objects.nonNull(paymentInfo)) {
					// 付款方式为信用证时，信用证号码不超过16字符，开证行不超过25字符
					if (StringUtils.isNotBlank(paymentInfo.getCreditNumber())) {
						if (paymentInfo.getCreditNumber().length() > 16) {
							throw new BadRequestException(
									ErrorCode.CODE_30153033);
						}
					}
					if (StringUtils.isNotBlank(paymentInfo.getCreditBank())) {
						if (paymentInfo.getCreditBank().length() > 25) {
							throw new BadRequestException(
									ErrorCode.CODE_30153034);
						}
					}
					paymentInfo1.setCreditNumber(paymentInfo.getCreditNumber());
					paymentInfo1.setCreditDate(paymentInfo.getCreditDate());
					paymentInfo1.setCreditBank(paymentInfo.getCreditBank());
				}
				payment.setPaymentInfo(paymentInfo1);
			}
			case LIQUIDITY_LOAN -> {
				// （采购）（销售）付款方式为流动贷时，收款的银行账户id、开户名称、银行账号、开户行必填，收款账户自行拼接
				if (Objects.isNull(paymentInfo)
						|| Objects.isNull(paymentInfo.getSellerBankId())) {
					throw new BadRequestException(ErrorCode.CODE_30153027);
				}
				if (StringUtils.isBlank(paymentInfo.getSellerBankName())) {
					throw new BadRequestException(ErrorCode.CODE_30153028);
				}
				if (StringUtils.isBlank(paymentInfo.getSellerBankAccount())) {
					throw new BadRequestException(ErrorCode.CODE_30153029);
				}
				if (StringUtils.isBlank(paymentInfo.getSellerOpeningBank())) {
					throw new BadRequestException(ErrorCode.CODE_30153030);
				}
				PaymentInfo paymentInfo1 = new PaymentInfo();
				paymentInfo1.setSellerBankId(paymentInfo.getSellerBankId());
				paymentInfo1.setSellerBankName(paymentInfo.getSellerBankName());
				paymentInfo1.setSellerBankAccount(
						paymentInfo.getSellerBankAccount());
				paymentInfo1.setSellerOpeningBank(
						paymentInfo.getSellerOpeningBank());
				paymentInfo1.setSellerBank(paymentInfo.getSellerBank());
				payment.setPaymentInfo(paymentInfo1);
				payment.setPaymentInfo(paymentInfo1);
			}
		}
	}

	/**
	 * 校验费用类型
	 * 
	 * @param payment
	 */
	private void validateCostType(Payment payment) {
		Integer costType = payment.getCostType();
		if (PaymentDef.CostType.ORDER_DEPOSIT.match(costType)) {
			// 费用类型选择订单保证金,须关联订单
			if (Objects.isNull(payment.getOrderId())) {
				throw new BadRequestException(ErrorCode.CODE_30153038);
			}

		} else if (PaymentDef.CostType.DEPOSIT.match(costType)) {
			// 费用类型选择履约保证金,只须关联合同
			if (Objects.isNull(payment.getContractId())) {
				throw new BadRequestException(ErrorCode.CODE_30153004);
			}
			payment.setOrderId(null);

		}
	}


    /**
     * 校验费用类型
     *
     * @param payment
     */
    private void validateCustomCostType(Payment payment, CustomPaymentForm form) {
        Integer costType = payment.getCostType();
        if (PaymentDef.CostType.ORDER_DEPOSIT.match(costType)) {
            // 费用类型选择订单保证金,须关联订单
            if (Objects.isNull(payment.getOrderId())) {
                throw new BadRequestException(ErrorCode.CODE_30153038);
            }

        } else if (PaymentDef.CostType.DEPOSIT.match(costType)) {
            // 费用类型选择履约保证金,只须关联合同
            if (Objects.isNull(payment.getContractId())) {
                throw new BadRequestException(ErrorCode.CODE_30153004);
            }
            payment.setOrderId(null);


        } else if (PaymentDef.CostType.GOODS_PAYMENT.match(costType)) {
            // 费用类型为货款
            if (PaymentDef.Type.SELL.match(payment.getType())) {

                // 销售：需要关联合同
                if (Objects.isNull(payment.getContractId())) {
                    throw new BadRequestException(ErrorCode.CODE_30153004);
                }
                // 付款方式不是流动贷时需要校验是否关联了应付款单
                if (!PaymentDef.PaymentWay.LIQUIDITY_LOAN.match(payment.getPaymentWay())) {
                    // 关联的合同结算方式为先货后款，需要关联应付款单
                    Contract contract = contractValidator.validateExist(payment.getContractId());
                    if (ContractDef.SettleWay.GOODS_FIRST.match(contract.getSettleWay()) && CollectionUtils.isNotEmpty(form.getPaymentAccountsFormList())) {
                        // 计算所有应付款单的未付金额总和
                        BigDecimal totalUnpaid = form.getPaymentAccountsFormList().stream()
                                .map(PaymentAccountsForm::getUnReceiptAmount)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                            // 获取当前付款金额
                        BigDecimal paymentAmount = payment.getAmount();
                        // 自定义错误码：付款金额超过可付金额
                        if (paymentAmount.compareTo(totalUnpaid) > 0) {
                            throw new BadRequestException(ErrorCode.CODE_30153040);
                        }
                    }
                }
                // 付款方式为流动贷时 借据id不能为空
                else {
                    if (Objects.isNull(payment.getLoanReceiptId())) {
                        throw new BadRequestException(ErrorCode.CODE_30153043);
                    }
                    // 资金方信息不能为空
                    if (Objects.isNull(payment.getBankEnterprise())) {
                        throw new BadRequestException(ErrorCode.CODE_30153044);
                    }
                }
                payment.setOrderId(null);
            }
        }
    }


	/**
	 * 供应链端-校验付款信息是否可以新增
	 *
	 * @param form
	 */
	public Payment validateAdminCreate(PaymentForm form) {
		Payment payment = form.convertToEntity();
        payment.setType(PaymentDef.Type.BUY.getCode());
        payment.setState(PaymentDef.State.PENDING_CONFIRMATION.getCode());
		Contract contract = contractValidator
				.validateExist(payment.getContractId());
		// 付款方式不是流动贷时 凭证不能为空
		if (!PaymentDef.PaymentWay.LIQUIDITY_LOAN.match(form.getPaymentWay())) {
			if (Objects.isNull(form.getPaymentFileId())) {
				throw new BadRequestException(ErrorCode.CODE_30153012);
			}
		}
        // 管理后台新增的付款是采购的，需要关联项目
        if (Objects.isNull(payment.getProjectId())) {
            throw new BadRequestException(ErrorCode.CODE_30153003);
        }
        projectValidator.validateExistProject(form.getProjectId());
        // 采购:采购方是供应链，销售方是上游供应链
        payment.setPurchaserId(contract.getSupplierChainId());
        payment.setPurchaserEnterprise(contract.getSupplierChainEnterprise());

        payment.setSellerId(contract.getUpstreamSuppliersId());
        payment.setSellerBusinessId(contract.getUpstreamId());
        payment.setSellerEnterprise(contract.getUpstreamSuppliersEnterprise());

        this.validatePaymentInfo(payment);
		// 校验费用类型
		this.validateCostType(payment);
		return payment;

	}

    /**
     * 校验付款信息是否可以修改
     *
     * @param id
     * @param form
     */
    public Payment validateAdminUpdate(String id, PaymentForm form) {
        Payment payment = this.validateExist(id);
        // 供应链端修改-销售-校验项目权限
        projectValidator.validateProjectPeople(payment.getProjectId());
        payment = form.convertToEntity(payment);
        this.validatePaymentInfo(payment);
        // 校验费用类型
        this.validateCostType(payment);
        return payment;
    }

    /**
     * 客户端-校验付款信息是否可以新增
     *
     * @param form
     */
    public Payment validateCustomCreate(CustomPaymentForm form) {

        Payment payment = form.convertToEntity();
        payment.setType(PaymentDef.Type.SELL.getCode());
        Contract contract = contractValidator
                .validateExist(payment.getContractId());
        // 付款方式不是流动贷时 凭证不能为空
        if (!PaymentDef.PaymentWay.LIQUIDITY_LOAN.match(form.getPaymentWay())) {
            if (Objects.isNull(form.getPaymentFileId())) {
                throw new BadRequestException(ErrorCode.CODE_30153012);
            }
        }
        payment.setProjectId(contract.getProjectId());
            // 销售：采购方是下游采购商，销售方是供应链
        payment.setPurchaserId(contract.getDownstreamPurchasersId());
        payment.setPurchaserBusinessId(contract.getDownstreamId());
        payment.setPurchaserEnterprise(contract.getDownstreamPurchasersEnterprise());

        payment.setSellerId(contract.getSupplierChainId());
        payment.setSellerEnterprise(contract.getSupplierChainEnterprise());

        this.validatePaymentInfo(payment);
        // 校验费用类型
        this.validateCustomCostType(payment,  form);
        return payment;

    }




    public Payment validateCustomUpdate(String id, Integer type, CustomPaymentForm form) {
        Payment payment = this.validateExist(id);
        //
        if (PaymentDef.Type.SELL.match(type)
                && !PaymentDef.State.REJECTED.match(payment.getState())) {
            throw new BadRequestException(ErrorCode.CODE_30153015);
        }
        payment = form.convertToEntity(payment);
        this.validatePaymentInfo(payment);
        // 校验费用类型
        this.validateCustomCostType(payment, form);
        return payment;
    }

	/**
	 * 校验付款信息是否可以删除
	 *
	 * @param id
	 * @param type
	 */
	public void validateDelete(String id, Integer type) {
		Payment payment = this.validateExist(id);
		if (PaymentDef.Type.BUY.match(type)) {
			projectValidator.validateProjectPeople(payment.getProjectId());
            boolean exists = refundService.getRepository().exists(new LambdaQueryWrapper<>(Refund.class).eq(Refund::getPaymentId, id).eq(Refund::getDel, CommonDef.Symbol.NO.getCode()));
            if( exists) {
                throw new BadRequestException(ErrorCode.CODE_30153046);
            }
        }
		if (PaymentDef.Type.SELL.match(type)
				&& !PaymentDef.State.REJECTED.match(payment.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30153016);
		}
	}

	/**
	 * 校验收款信息是否存在
	 * 
	 * @param id
	 * @return
	 */
	public Payment validateSaleExist(String id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30153002));
	}

	/**
	 * 校验收款信息是否可以驳回
	 * 
	 * @param id
	 */
	public Payment validateReject(String id, PaymentRejectForm form) {
		Payment payment = this.validateSaleExist(id);
		projectValidator.validateProjectPeople(payment.getProjectId());
		if (!PaymentDef.State.PENDING_CONFIRMATION.match(payment.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30153018);
		}
		payment.setState(PaymentDef.State.REJECTED.getCode());
		payment.setRejectReason(form.getRejectReason());
		return payment;
	}

	/**
	 * 校验收款信息是否可以确认
	 * 
	 * @param id
	 */
	public Payment validateConfirm(String id) {
		Payment payment = this.validateSaleExist(id);
		projectValidator.validateProjectPeople(payment.getProjectId());
		if (!PaymentDef.State.PENDING_CONFIRMATION.match(payment.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30153017);
		}
		payment.setState(PaymentDef.State.COMPLETED.getCode());
		return payment;
	}

    public Integer adminGetState(String contractId, Integer contractType ) {
        //判断 收款单位-销售方id 是否为录入企业，传入合同id，类型为采购，返回收款单位是否为录入企业
        Boolean flag = contractService.validateIsRecorded(contractId, contractType).orElse(null);
        // 如果是录入企业状态改成完成，调用完成方法
        if( flag) {
            return PaymentDef.State.COMPLETED.getCode();
        } else {
            return PaymentDef.State.PENDING_CONFIRMATION.getCode();
        }
    }

}
