package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.PaymentCountVo;
import com.zhihaoscm.domain.bean.vo.PaymentVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.PaymentMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.resource.form.payment.PaymentAccountsForm;

import cn.hutool.core.bean.BeanUtil;

/**
 * <p>
 * 付款 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
public class PaymentServiceImpl
		extends MpStringIdBaseServiceImpl<Payment, PaymentMapper>
		implements PaymentService {

	@Autowired
	private ProjectService projectService;

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private ContractService contractService;

	@Autowired
	private RefundService refundService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Autowired
	private AccountsService accountsService;

	@Autowired
	private PaymentAccountsService paymentAccountsService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	public PaymentServiceImpl(PaymentMapper repository) {
		super(repository);
	}

	@Override
	public Page<PaymentVo> adminBuyPaging(Integer page, Integer size,
			String keyword, String sellerName, String sellerBank,
			LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> costType, List<Integer> paymentWays, String sortKey,
			String sortOrder, String projectId, Boolean hasAll, Long userId,
			String param, String purchaserBank, Integer state) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			// 处理人是自己在的
			List<String> projectIdList = projectService.findByUserId(userId,
					null);
			if (org.apache.commons.collections4.CollectionUtils
					.isNotEmpty(projectIdList)) {
				wrapper.in(Payment::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}
		wrapper.eq(Payment::getType, PaymentDef.Type.BUY.getCode());
		wrapper.eq(Objects.nonNull(projectId), Payment::getProjectId,
				projectId);
		if (StringUtils.isNotBlank(keyword)) {
			List<String> projectIds = projectService.findByNameLike(keyword)
					.stream().map(Project::getId).distinct().toList();
			List<String> contractIds = contractService.findByNameLike(keyword)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(i -> i.like(Payment::getId, keyword).or()
					.in(CollectionUtils.isNotEmpty(projectIds),
							Payment::getProjectId, projectIds)
					.or().in(CollectionUtils.isNotEmpty(contractIds),
							Payment::getContractId, contractIds));
		}
		if (StringUtils.isNotBlank(sellerName)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%') )",
					sellerName));
		}
		if (StringUtils.isNotBlank(sellerBank)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(payment_info, '$.sellerBank') LIKE CONCAT('%',{0},'%') )",
					sellerBank));
		}
		// 付款编号或 合同名称
		if (StringUtils.isNotBlank(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(i -> i.like(Payment::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Payment::getContractId, contractIds));
		}
		// 付款账户
		if (StringUtils.isNotBlank(purchaserBank)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(payment_info, '$.purchaserBank') LIKE CONCAT('%',{0},'%') )",
					purchaserBank));
		}
		wrapper.ge(Objects.nonNull(beginTime), Payment::getPaymentDate,
				beginTime)
				.le(Objects.nonNull(endTime), Payment::getPaymentDate, endTime)
				.in(CollectionUtils.isNotEmpty(costType), Payment::getCostType,
						costType)
				.in(CollectionUtils.isNotEmpty(paymentWays),
						Payment::getPaymentWay, paymentWays);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 默认按照付款日期倒序
			// 默认按付款日期倒序排序，付款日期相同按创建时间倒序排序
			wrapper.orderByDesc(Payment::getUpdatedTime)
					.orderByDesc(Payment::getCreatedTime)
					.orderByDesc(Payment::getId);
		}
		wrapper.eq(Objects.nonNull(state), Payment::getState, state);
		Page<Payment> paymentPage = repository
				.selectPage(new Page<>(page, size), wrapper);
		List<PaymentVo> paymentVos = this.packVo(paymentPage.getRecords());
		return PageUtil.getRecordsInfoPage(paymentPage, paymentVos);
	}

	@Override
	public Page<PaymentVo> customSellPaging(Integer page, Integer size,
			String keyword, String sellerBank, LocalDateTime beginTime,
			LocalDateTime endTime, List<Integer> costType,
			List<Integer> paymentWays, List<Integer> state, String sortKey,
			String sortOrder, Long customId, String projectId) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Payment::getType, PaymentDef.Type.SELL.getCode());
		wrapper.eq(Objects.nonNull(customId), Payment::getPurchaserId, customId)
				.eq(Objects.nonNull(projectId), Payment::getProjectId,
						projectId);
		if (StringUtils.isNotBlank(keyword)) {
			List<String> contractIds = contractService.findByNameLike(keyword)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(i -> i.like(Payment::getId, keyword).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Payment::getContractId, contractIds));
		}
		if (StringUtils.isNotBlank(sellerBank)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(payment_info, '$.sellerBank') LIKE CONCAT('%',{0},'%') )",
					sellerBank));
		}
		wrapper.ge(Objects.nonNull(beginTime), Payment::getPaymentDate,
				beginTime)
				.le(Objects.nonNull(endTime), Payment::getPaymentDate, endTime)
				.in(CollectionUtils.isNotEmpty(costType), Payment::getCostType,
						costType)
				.in(CollectionUtils.isNotEmpty(paymentWays),
						Payment::getPaymentWay, paymentWays)
				.in(CollectionUtils.isNotEmpty(state), Payment::getState,
						state);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 默认按已驳回-3、确认中-1、已完成-2 状态排序，状态相同按更新时间倒序排序
			wrapper.last(
					"ORDER BY FIELD(state, 3,1,2) ASC, updated_time DESC, id DESC");
		}
		Page<Payment> paymentPage = repository
				.selectPage(new Page<>(page, size), wrapper);
		List<PaymentVo> paymentVos = this.packVo(paymentPage.getRecords());
		return PageUtil.getRecordsInfoPage(paymentPage, paymentVos);
	}

	@Override
	public Page<PaymentVo> adminSellPaging(Integer page, Integer size,
			String keyword, String purchaserName, String purchaserBank,
			LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> costType, List<Integer> paymentWays, Integer state,
			String sortKey, String sortOrder, String projectId, Boolean hasAll,
			Long userId, String param) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			// 处理人是自己在的
			List<String> projectIdList = projectService.findByUserId(userId,
					null);
			if (org.apache.commons.collections4.CollectionUtils
					.isNotEmpty(projectIdList)) {
				wrapper.in(Payment::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}
		wrapper.eq(Payment::getType, PaymentDef.Type.SELL.getCode());
		wrapper.eq(Objects.nonNull(projectId), Payment::getProjectId,
				projectId);
		if (StringUtils.isNotBlank(keyword)) {
			List<String> projectIds = projectService.findByNameLike(keyword)
					.stream().map(Project::getId).distinct().toList();
			wrapper.and(i -> i.like(Payment::getId, keyword).or().in(
					CollectionUtils.isNotEmpty(projectIds),
					Payment::getProjectId, projectIds));
		}
		if (StringUtils.isNotBlank(purchaserName)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%') )",
					purchaserName));
		}
		if (StringUtils.isNotBlank(purchaserBank)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(payment_info, '$.purchaserBank') LIKE CONCAT('%',{0},'%') )",
					purchaserBank));
		}
		// 付款编号或 合同名称
		if (StringUtils.isNotBlank(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(i -> i.like(Payment::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Payment::getContractId, contractIds));
		}
		wrapper.ge(Objects.nonNull(beginTime), Payment::getPaymentDate,
				beginTime)
				.le(Objects.nonNull(endTime), Payment::getPaymentDate, endTime)
				.in(CollectionUtils.isNotEmpty(costType), Payment::getCostType,
						costType)
				.in(CollectionUtils.isNotEmpty(paymentWays),
						Payment::getPaymentWay, paymentWays);
		if (Objects.nonNull(state)) {
			wrapper.eq(Payment::getState, state);
		} else {
			wrapper.ne(Payment::getState, PaymentDef.State.REJECTED.getCode());
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 默认按待确认-1、已完成-2 状态排序，状态相同按更新时间倒序排序
			wrapper.orderByAsc(Payment::getState)
					.orderByDesc(Payment::getUpdatedTime)
					.orderByDesc(Payment::getId);
		}
		Page<Payment> paymentPage = repository
				.selectPage(new Page<>(page, size), wrapper);
		List<PaymentVo> paymentVos = this.packVo(paymentPage.getRecords());
		return PageUtil.getRecordsInfoPage(paymentPage, paymentVos);
	}

	@Override
	public Page<PaymentVo> customBuyPaging(Integer page, Integer size,
			String keyword, String purchaserName, String purchaserBank,
			LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> costType, List<Integer> paymentWays, Integer state,
			String sortKey, String sortOrder, String projectId, Long userId,
			String param) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);

		wrapper.eq(Payment::getType, PaymentDef.Type.BUY.getCode());
		wrapper.eq(Objects.nonNull(projectId), Payment::getProjectId,
				projectId);
		if (StringUtils.isNotBlank(keyword)) {
			List<String> contractIds = contractService.findByNameLike(keyword)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(i -> i.like(Payment::getId, keyword).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Payment::getContractId, contractIds));
		}
		if (StringUtils.isNotBlank(purchaserName)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%') )",
					purchaserName));
		}
		if (StringUtils.isNotBlank(purchaserBank)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(payment_info, '$.purchaserBank') LIKE CONCAT('%',{0},'%') )",
					purchaserBank));
		}
		// 付款编号或 合同名称
		if (StringUtils.isNotBlank(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(i -> i.like(Payment::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Payment::getContractId, contractIds));
		}
		wrapper.ge(Objects.nonNull(beginTime), Payment::getPaymentDate,
				beginTime)
				.le(Objects.nonNull(endTime), Payment::getPaymentDate, endTime)
				.in(CollectionUtils.isNotEmpty(costType), Payment::getCostType,
						costType)
				.in(CollectionUtils.isNotEmpty(paymentWays),
						Payment::getPaymentWay, paymentWays);
		wrapper.eq(Objects.nonNull(userId), Payment::getSellerId, userId);
		if (Objects.nonNull(state)) {
			wrapper.eq(Payment::getState, state);
		} else {
			wrapper.ne(Payment::getState, PaymentDef.State.REJECTED.getCode());
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 默认按待确认-1、已完成-2 状态排序，状态相同按更新时间倒序排序
			wrapper.orderByAsc(Payment::getState)
					.orderByDesc(Payment::getUpdatedTime)
					.orderByDesc(Payment::getId);
		}
		Page<Payment> paymentPage = repository
				.selectPage(new Page<>(page, size), wrapper);
		List<PaymentVo> paymentVos = this.packVo(paymentPage.getRecords());
		return PageUtil.getRecordsInfoPage(paymentPage, paymentVos);
	}

	@Override
	public Page<Payment> selector(Integer page, Integer size, String projectId,
			String contractId, Integer contractType, String paymentId) {
		if (StringUtils.isBlank(contractId)) {
			return new Page<>();
		}
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		// 待确认和被驳回的退款所关联的收款单
		List<String> refundingPaymentIds = refundService
				.findByStates(
						List.of(RefundDef.State.PENDING_CONFIRMATION.getCode(),
								RefundDef.State.REJECTED.getCode()))
				.stream().map(Refund::getPaymentId).toList();
		// 供应链端销售合同2：关联收款单2需要判断 没被退款关联+付款方式不是流动贷
		// 供应链端采购合同1（客户端销售合同）：关联付款单1（关联收款单）需要判断 没被退款关联
		if (ContractDef.ContractType.SALES.match(contractType)) {
			// 不显示付款方式为流动贷的收款单
			wrapper.ne(Payment::getPaymentWay,
					PaymentDef.PaymentWay.LIQUIDITY_LOAN.getCode());
		}
		// 状态为已完成的且未关联退款
		wrapper.eq(Payment::getProjectId, projectId)
				.eq(Payment::getContractId, contractId)
				.like(StringUtils.isNotBlank(paymentId), Payment::getId,
						paymentId)
				.eq(Payment::getState, PaymentDef.State.COMPLETED.getCode())
				.isNull(Payment::getRefundId).isNull(Payment::getChangeRemark)
				.notIn(CollectionUtils.isNotEmpty(refundingPaymentIds),
						Payment::getId, refundingPaymentIds)
				.orderByDesc(Payment::getUpdatedTime);
		// 按更新时间倒序
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Optional<PaymentVo> findVoById(String id) {
		return super.findOne(id).map(payment -> {
			PaymentVo paymentVo = new PaymentVo();
			paymentVo.setPayment(payment);
			paymentVo.setContract(contractService
					.findOne(payment.getContractId()).orElse(null));
			if (Objects.nonNull(payment.getProjectId())) {
				projectService.findOne(payment.getProjectId())
						.ifPresent(paymentVo::setProject);
			}
			List<PaymentAccounts> paymentAccounts = paymentAccountsService
					.getRepository()
					.selectList(new LambdaQueryWrapper<>(PaymentAccounts.class)
							.eq(PaymentAccounts::getPaymentId, payment.getId())
							.eq(PaymentAccounts::getDel,
									CommonDef.Symbol.NO.getCode()));
			// 详情补充应付款列表数据
			if (CollectionUtils.isNotEmpty(paymentAccounts)) {
				List<Accounts> list = accountsService.findByIds(paymentAccounts
						.stream().map(PaymentAccounts::getAccountsId).toList());
				paymentVo.setAccountsList(list);
				paymentVo.setPaymentAccountsList(paymentAccounts);
			}
			return paymentVo;
		});
	}

	@Override
	public List<Payment> findByContractId(String contractId) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Payment::getContractId, contractId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Payment> findByOrderIds(List<String> orderIds,
			List<Integer> states, Integer costType) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		wrapper.in(Payment::getOrderId, orderIds);
		wrapper.in(CollectionUtils.isNotEmpty(states), Payment::getState,
				states);
		wrapper.eq(Objects.nonNull(costType), Payment::getCostType, costType);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Payment> findByOrders(List<String> orderIds, Integer state,
			Integer costType, Integer isDepositTransfer) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		wrapper.in(Payment::getOrderId, orderIds);
		wrapper.in(Objects.nonNull(state), Payment::getState, state);
		wrapper.eq(Objects.nonNull(costType), Payment::getCostType, costType);
		wrapper.eq(Objects.nonNull(isDepositTransfer),
				Payment::getIsDepositTransfer, isDepositTransfer);
		wrapper.isNull(Payment::getRefundId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Payment> findByLoanReceiptId(String loanReceiptId) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Payment::getLoanReceiptId, loanReceiptId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Payment> findByRecIdAndAccId(String reconciliationId,
			Integer state, Integer costType, String accountsId) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);

		this.filterDeleted(wrapper);
		wrapper.eq(StringUtils.isNotBlank(reconciliationId),
				Payment::getReconciliationId, reconciliationId);
		wrapper.eq(Objects.nonNull(state), Payment::getState, state);
		wrapper.eq(Objects.nonNull(costType), Payment::getCostType, costType);
		// accountsId传入为空，对账id reconciliationId 不为空，要查询中间表查询数据
		if (StringUtils.isNotBlank(reconciliationId)) {
			List<PaymentAccounts> list = paymentAccountsService.getRepository()
					.selectList(new LambdaQueryWrapper<>(PaymentAccounts.class)
							.eq(PaymentAccounts::getReconciliationId,
									reconciliationId)
							.eq(PaymentAccounts::getDel,
									CommonDef.Symbol.NO.getCode()));
			List<String> payMentIdList = list.stream()
					.map(PaymentAccounts::getPaymentId).toList();
			wrapper.in(CollectionUtils.isNotEmpty(payMentIdList),
					Payment::getId, payMentIdList);
		}
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<BigDecimal> findAmount(String contractId, Integer state,
			Integer type, Integer costType) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers.query(Payment.class)
				.select("SUM(amount) amount").lambda()
				.eq(Payment::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Payment::getContractId, contractId)
				.eq(Objects.nonNull(type), Payment::getType, type)
				.eq(Objects.nonNull(costType), Payment::getCostType, costType)
				.eq(Objects.nonNull(state), Payment::getState, state);
		// 销售类型的合同时 付款方式是流动贷的数据要去除掉 已经关联了退款单的并且退款完成的数据要去掉
		if (Objects.nonNull(type)
				&& type.equals(PaymentDef.Type.SELL.getCode())) {
			wrapper.ne(Payment::getPaymentWay,
					PaymentDef.PaymentWay.LIQUIDITY_LOAN.getCode());
			wrapper.isNull(Payment::getRefundId);
		}
		// 采购类型的未关联退款完成的退款单的数据
		if (Objects.nonNull(type)
				&& type.equals(PaymentDef.Type.BUY.getCode())) {
			wrapper.isNull(Payment::getRefundId);
		}
		Payment payment = repository.selectOne(wrapper);
		return Objects.isNull(payment) ? Optional.of(BigDecimal.ZERO)
				: Optional.of(payment.getAmount());
	}

	@Override
	public List<Payment> findByTypeAndProjectId(Integer type, String projectId,
			Integer state, List<Integer> costType, Boolean isRefund,
			LocalDateTime beginTime, LocalDateTime endTime) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(type), Payment::getType, type);
		wrapper.eq(Objects.nonNull(projectId), Payment::getProjectId,
				projectId);
		wrapper.eq(Objects.nonNull(state), Payment::getState, state);
		if (Objects.nonNull(isRefund)) {
			wrapper.isNotNull(isRefund, Payment::getRefundId);
			wrapper.isNull(!isRefund, Payment::getRefundId);
		}
		wrapper.in(CollectionUtils.isNotEmpty(costType), Payment::getCostType,
				costType);
		wrapper.ge(Objects.nonNull(beginTime), Payment::getPaymentDate,
				beginTime);
		wrapper.le(Objects.nonNull(endTime), Payment::getPaymentDate, endTime);
		return repository.selectList(wrapper);
	}

	/**
	 * 根据订单id查询未退款的付款单
	 * 
	 * @author: pp 2025/2/12 15:37 [orderId, costType, state]
	 *          java.util.List<com.zhihaoscm.domain.bean.entity.Payment>
	 **/
	@Override
	public List<Payment> find(String orderId, Integer costType, Integer state) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(orderId), Payment::getOrderId, orderId);
		wrapper.eq(Objects.nonNull(costType), Payment::getCostType, costType);
		wrapper.eq(Objects.nonNull(state), Payment::getState, state);
		wrapper.isNull(Payment::getRefundId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Payment> findUnfinished(String projectId) {
		LambdaQueryWrapper<Payment> queryWrapper = Wrappers
				.lambdaQuery(Payment.class);
		queryWrapper.eq(Payment::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Payment::getProjectId, projectId);
		queryWrapper.ne(Payment::getState,
				PaymentDef.State.COMPLETED.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Payment> findByContractId(String contractId, Integer costType,
			Integer state) {
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(contractId), Payment::getContractId,
				contractId);
		wrapper.eq(Objects.nonNull(costType), Payment::getCostType, costType);
		wrapper.eq(Objects.nonNull(state), Payment::getState, state);
		wrapper.isNull(Payment::getRefundId);
		return repository.selectList(wrapper);
	}

	@Override
	@FileId
	@Transactional(rollbackFor = Exception.class)
	public Payment adminCreate(Payment resource) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		// 按规则设置付款id
		// 付款编号:所属项目编号（6位）+0（固定）+4（固定）+自增数（4位）
		resource.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(),
				RedisKeys.Cache.PURCHASE_PAYMENT_CODE_GENERATOR,
				0 + AutoCodeDef.BusinessRuleCode.PAYMENT_SUFFIX.getCode(), 4,
				AutoCodeDef.DATE_TYPE.yy));
		Payment payment = super.create(resource);

		if (Objects.equals(PaymentDef.State.COMPLETED.getCode(),
				payment.getState())) {
			this.confirm(payment);
		}

		this.sendNotice(payment,
				wxSubscriptionProperties.getPaymentUnConfirmCode(),
				MessageFormat.format(
						UserMessageConstants.PAYMENT_UNCONFIRMED_TEMPLATE,
						resource.getId()),
				PaymentDef.Type.BUY.getCode());
		return payment;
	}

	@Override
	public Payment adminUpdate(Payment resource,
			List<PaymentAccountsForm> paymentAccountsFormList) {
		super.updateAllProperties(resource);
		paymentAccountsService.getRepository()
				.delete(new LambdaQueryWrapper<>(PaymentAccounts.class)
						.eq(PaymentAccounts::getPaymentId, resource.getId()));

		if (CollectionUtils.isNotEmpty(paymentAccountsFormList)) {
			List<PaymentAccounts> list = BeanUtil
					.copyToList(paymentAccountsFormList, PaymentAccounts.class);
			list.forEach(paymentAccounts -> paymentAccounts
					.setPaymentId(resource.getId()));
			paymentAccountsService.batchCreate(list);
		}

		if (Objects.equals(PaymentDef.State.COMPLETED.getCode(),
				resource.getState())) {
			this.confirm(resource);
		}
		return resource;
	}

	@Override
	@FileId(type = 2)
	public Payment updateAllProperties(Payment resource) {
		// 只有收款单有状态，付款单无状态
		if (PaymentDef.Type.SELL.match(resource.getType())) {
			switch (PaymentDef.State.from(resource.getState())) {
				case COMPLETED -> this.sendNotice(resource,
						wxSubscriptionProperties.getConfirmPaymentCode(),
						MessageFormat.format(
								UserMessageConstants.PAYMENT_CONFIRMED_TEMPLATE,
								resource.getId()),
						PaymentDef.Type.SELL.getCode());

				case REJECTED -> this.sendNotice(resource,
						wxSubscriptionProperties.getDismissPaymentCode(),
						MessageFormat.format(
								UserMessageConstants.PAYMENT_DISMISS_TEMPLATE,
								resource.getId()),
						PaymentDef.Type.SELL.getCode());

			}
		}
		return super.updateAllProperties(resource);
	}

	@Override
	@FileId(type = 3)
	@Transactional(rollbackFor = Exception.class)
	public void delete(String id) {
		super.delete(id);
		List<PaymentAccounts> paymentAccounts = paymentAccountsService
				.getRepository()
				.selectList(new LambdaQueryWrapper<>(PaymentAccounts.class)
						.eq(PaymentAccounts::getPaymentId, id)
						.eq(PaymentAccounts::getDel,
								CommonDef.Symbol.NO.getCode()));
		List<Long> idList = paymentAccounts.stream().map(PaymentAccounts::getId)
				.collect(Collectors.toList());

		if (CollectionUtils.isNotEmpty(idList)) {
			paymentAccountsService.batchDelete(idList);
		}
	}

	@Override
	public void updateRefund(String id, String refundId,
			LocalDateTime refundDate) {
		LambdaUpdateWrapper<Payment> wrapper = Wrappers
				.lambdaUpdate(Payment.class);
		wrapper.eq(Payment::getId, id)
				.eq(Payment::getDel, CommonDef.Symbol.NO.getCode())
				.set(Payment::getRefundId, refundId)
				.set(Payment::getRefundDate, refundDate);
		repository.update(wrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Payment> confirm(Payment payment) {
		List<PaymentAccounts> list = paymentAccountsService.getRepository()
				.selectList(new LambdaQueryWrapper<>(PaymentAccounts.class)
						.eq(PaymentAccounts::getPaymentId, payment.getId())
						.eq(PaymentAccounts::getDel,
								CommonDef.Symbol.NO.getCode()));
		if (CollectionUtils.isNotEmpty(list)) {
			List<Accounts> accounts = accountsService
					.findByIds(list.stream().map(PaymentAccounts::getAccountsId)
							.collect(Collectors.toList()));
			if (CollectionUtils.isNotEmpty(accounts)) {
				for (Accounts accounts1 : accounts) {
					if (Objects.nonNull(payment.getAmount())) {
						// 更新应收款金额
						accounts1.setReceiptedAmount(accounts1
								.getReceiptedAmount().add(payment.getAmount()));
					}
					BigDecimal unReceiptAmount = accounts1.getAmount()
							.subtract(accounts1.getReceiptedAmount());
					if (unReceiptAmount.compareTo(BigDecimal.ZERO) < 0) {
						unReceiptAmount = BigDecimal.ZERO;
					}
					accounts1.setUnReceiptAmount(unReceiptAmount);
					// 未收款金额为0时 状态变为已结清
					if (accounts1.getUnReceiptAmount()
							.compareTo(BigDecimal.ZERO) == 0) {
						accounts1.setStatus(
								AccountsDef.Status.SETTLED.getCode());
					}
					// 清空逾期时长
					accounts1.setDelayDate(null);
				}
				// 批量更新应收/付款列表
				accountsService.batchUpdate(accounts);
			}
		}
		return Optional.of(super.updateAllProperties(payment));
	}

	@Override
	public Optional<BigDecimal> calcTotalAmount(String projectId, Integer type,
			LocalDateTime beginTime, LocalDateTime endTime, String param,
			String sellerName, List<Integer> costType,
			List<Integer> paymentWays, String purchaserBank,
			String purchaserName, Integer state) {
		BigDecimal amount = BigDecimal.ZERO;
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		wrapper.eq(Payment::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Payment::getProjectId, projectId).eq(Payment::getType, type)
				.ge(Objects.nonNull(beginTime), Payment::getPaymentDate,
						beginTime)
				.le(Objects.nonNull(endTime), Payment::getPaymentDate, endTime)
				.in(CollectionUtils.isNotEmpty(costType), Payment::getCostType,
						costType)
				.in(CollectionUtils.isNotEmpty(paymentWays),
						Payment::getPaymentWay, paymentWays);
		if (Objects.nonNull(state)) {
			wrapper.eq(Payment::getState, state);
		}

		// 付款编号或 合同名称
		if (StringUtils.isNotBlank(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(i -> i.like(Payment::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Payment::getContractId, contractIds));
		}
		// 收款单位名称
		if (StringUtils.isNotBlank(sellerName)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%') )",
					sellerName));
		}
		// 付款账户
		if (StringUtils.isNotBlank(purchaserBank)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(payment_info, '$.purchaserBank') LIKE CONCAT('%',{0},'%') )",
					purchaserBank));
		}
		// 付款单位名称
		if (StringUtils.isNotBlank(purchaserName)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%') )",
					purchaserName));
		}
		List<Payment> payments = repository.selectList(wrapper);
		if (CollectionUtils.isNotEmpty(payments)) {
			if (ContractDef.Type.SELL.match(type)) {
				// 销售：状态为已完成的且未关联退款金额之和
				List<Payment> payments1 = payments.stream()
						.filter(i -> i.getState()
								.equals(PaymentDef.State.COMPLETED.getCode())
								&& Objects.isNull(i.getRefundId()))
						.toList();
				amount = payments1.stream().map(Payment::getAmount)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			} else {
				amount = payments.stream().map(Payment::getAmount)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);

			}
		}

		return Optional.of(amount);
	}

	@Override
	public Optional<PaymentCountVo> staticsAdminPayment(boolean isManage) {
		PaymentCountVo paymentCountVo = new PaymentCountVo();
		paymentCountVo.setRejectPayment(0L);
		paymentCountVo.setWaitConfirmPayment(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计收款待确认
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Payment::getType, PaymentDef.Type.SELL.getCode());
				wrapper.eq(Payment::getState,
						PaymentDef.State.PENDING_CONFIRMATION.getCode());
				wrapper.in(Payment::getProjectId, projectIds);
				paymentCountVo
						.setWaitConfirmPayment(repository.selectCount(wrapper));

				// 统计付款已驳回
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Payment::getType, PaymentDef.Type.BUY.getCode());
				wrapper.eq(Payment::getState,
						PaymentDef.State.REJECTED.getCode());
				wrapper.in(Payment::getProjectId, projectIds);
				paymentCountVo
						.setRejectPayment(repository.selectCount(wrapper));
			}

		}
		return Optional.of(paymentCountVo);
	}

	@Override
	public Optional<PaymentCountVo> staticsCustomerPayment(
			boolean isPermission) {
		PaymentCountVo paymentCountVo = new PaymentCountVo();
		paymentCountVo.setRejectPayment(0L);
		paymentCountVo.setWaitConfirmPayment(0L);
		LambdaQueryWrapper<Payment> wrapper = Wrappers
				.lambdaQuery(Payment.class);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		if (isPermission) {
			// 统计收款待确认
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Payment::getType, PaymentDef.Type.BUY.getCode());
			wrapper.eq(Payment::getState,
					PaymentDef.State.PENDING_CONFIRMATION.getCode());
			wrapper.eq(Payment::getSellerId, customerId);
			paymentCountVo
					.setWaitConfirmPayment(repository.selectCount(wrapper));

			// 统计付款已驳回
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Payment::getType, PaymentDef.Type.SELL.getCode());
			wrapper.eq(Payment::getState, PaymentDef.State.REJECTED.getCode());
			wrapper.eq(Payment::getPurchaserId, customerId);
			paymentCountVo.setRejectPayment(repository.selectCount(wrapper));

		}
		return Optional.of(paymentCountVo);
	}

	/**
	 * 封装vo
	 *
	 * @param records
	 *            记录数据
	 */
	private List<PaymentVo> packVo(List<Payment> records) {
		List<PaymentVo> paymentVos = new ArrayList<>();
		// 合同信息
		List<String> contracts = records.stream().map(Payment::getContractId)
				.distinct().toList();
		Map<String, Contract> contractMap = contractService.findByIds(contracts)
				.stream().collect(Collectors.toMap(Contract::getId, e -> e));
		// 项目信息
		List<String> projects = records.stream().map(Payment::getProjectId)
				.distinct().toList();
		Map<String, Project> projectMap = projectService.findByIds(projects)
				.stream().collect(Collectors.toMap(Project::getId, e -> e));
		for (Payment payment : records) {
			PaymentVo vo = new PaymentVo();
			vo.setPayment(payment);
			vo.setContract(contractMap.get(payment.getContractId()));
			vo.setProject(projectMap.get(payment.getProjectId()));

			// 根据应收/付款id查询 已完成的支付记录
			List<PaymentAccounts> paymentAccountsList = paymentAccountsService
					.getRepository()
					.selectList(new LambdaQueryWrapper<>(PaymentAccounts.class)
							.eq(PaymentAccounts::getPaymentId, payment.getId())
							.eq(PaymentAccounts::getDel,
									CommonDef.Symbol.NO.getCode()));
			if (CollectionUtils.isNotEmpty(paymentAccountsList)) {
				List<String> accountsIdList = paymentAccountsList.stream()
						.map(PaymentAccounts::getAccountsId).toList();
				List<Accounts> list = accountsService.findByIds(accountsIdList);
				vo.setAccountsList(list);
			}

			paymentVos.add(vo);
		}
		return paymentVos;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Payment customCreate(Payment resource,
			List<PaymentAccountsForm> paymentAccountsFormList) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		// 用户新增付款
		// 收款编号:所属项目编号（6位）+0（固定）+3（固定）+自增数（4位）
		resource.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(),
				RedisKeys.Cache.RECEIVE_PAYMENT_CODE_GENERATOR,
				0 + AutoCodeDef.BusinessRuleCode.RECEIVE_PAYMENT_SUFFIX
						.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		super.create(resource);
		if (CollectionUtils.isNotEmpty(paymentAccountsFormList)) {
			List<PaymentAccounts> list = BeanUtil
					.copyToList(paymentAccountsFormList, PaymentAccounts.class);
			list.forEach(paymentAccounts -> {
				paymentAccounts.setPaymentId(resource.getId());
				paymentAccounts.setPaymentWay(resource.getPaymentWay());
				paymentAccounts.setPaymentDate(resource.getPaymentDate());
				paymentAccounts.setPaymentInfo(resource.getPaymentInfo());
			});
			paymentAccountsService.batchCreate(list);
		}
		if (Objects.equals(PaymentDef.State.COMPLETED.getCode(),
				resource.getState())) {
			this.confirm(resource);
		}
		return resource;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Payment customUpdate(Payment resource,
			List<PaymentAccountsForm> paymentAccountsFormList) {
		super.updateAllProperties(resource);
		paymentAccountsService.getRepository()
				.delete(new LambdaQueryWrapper<>(PaymentAccounts.class)
						.eq(PaymentAccounts::getPaymentId, resource.getId()));

		if (CollectionUtils.isNotEmpty(paymentAccountsFormList)) {
			List<PaymentAccounts> list = BeanUtil
					.copyToList(paymentAccountsFormList, PaymentAccounts.class);
			list.forEach(paymentAccounts -> {
				paymentAccounts.setPaymentId(resource.getId());
				paymentAccounts.setPaymentWay(resource.getPaymentWay());
				paymentAccounts.setPaymentDate(resource.getPaymentDate());
				paymentAccounts.setPaymentInfo(resource.getPaymentInfo());
			});

			paymentAccountsService.batchCreate(list);
		}
		return resource;
	}

	/**
	 * 发送短信
	 *
	 * @param payment
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(Payment payment, String templateCode, String title,
			Integer type) {
		Customer customer = null;
		if (PaymentDef.Type.SELL.match(type)) {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(payment.getPurchaserBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		} else {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(payment.getSellerBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("order_id", payment.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.FUNDING.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.PAYMENT_DETAIL_PAGE)
						.detailId(String.valueOf(payment.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}

}
