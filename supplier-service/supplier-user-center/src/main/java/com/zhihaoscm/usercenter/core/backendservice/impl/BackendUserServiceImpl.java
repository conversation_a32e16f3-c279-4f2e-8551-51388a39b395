package com.zhihaoscm.usercenter.core.backendservice.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.util.utils.DxyzmUtils;
import com.zhihaoscm.common.util.utils.JsonUtils;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Person;
import com.zhihaoscm.domain.bean.entity.Role;
import com.zhihaoscm.domain.bean.entity.User;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.vo.UserVo;
import com.zhihaoscm.usercenter.config.properties.SMSProperties;
import com.zhihaoscm.usercenter.core.backendservice.BackendRoleService;
import com.zhihaoscm.usercenter.core.backendservice.BackendUserChangeService;
import com.zhihaoscm.usercenter.core.backendservice.BackendUserService;
import com.zhihaoscm.usercenter.core.mapper.UserMapper;
import com.zhihaoscm.usercenter.core.service.MessageService;
import com.zhihaoscm.usercenter.core.service.UserChangeService;
import com.zhihaoscm.usercenter.utils.ThreadPoolUtil;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Service
public class BackendUserServiceImpl
		extends MpLongIdBaseServiceImpl<User, UserMapper>
		implements BackendUserService {

	public BackendUserServiceImpl(UserMapper repository) {
		super(repository);
	}

	@Autowired
	private BackendRoleService roleService;

	@Autowired
	private PasswordEncoder passwordEncoder;

	@Autowired
	private MessageService messageService;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Override
	public Page<UserVo> paging(Integer page, Integer size, String param,
			Integer state, Long tenantId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());

		if (StringUtils.isNotBlank(param)) {
			queryWrapper.and(x -> x.eq(User::getMobile, param).or()
					.like(User::getName, param).or()
					.eq(User::getEmployeeId, param));
		}

		queryWrapper.eq(Objects.nonNull(state), User::getState, state);

		if (Objects.isNull(tenantId)) {
			queryWrapper.isNull(User::getTenantId);
		} else {
			queryWrapper.eq(User::getTenantId, tenantId);
		}

		queryWrapper.orderByDesc(User::getId);
		Page<User> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		List<UserVo> userVos = this.packVo(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, userVos);
	}

	@Override
	public List<User> findByIdsNoDeleted(Collection<Long> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return List.of();
		}
		String fieldOrderSql = "ORDER BY FIELD(id," + ids.stream()
				.map(String::valueOf).collect(Collectors.joining(",")) + ")";

		LambdaQueryWrapper<User> wrapper = Wrappers.lambdaQuery(User.class)
				.in(User::getId, ids).last(fieldOrderSql);
		return repository.selectList(wrapper);
	}

	@Override
	public List<User> selector(String name, Integer origin) {
		LambdaQueryWrapper<User> wrapper = Wrappers.lambdaQuery(User.class)
				.eq(User::getDel, CommonDef.Symbol.NO.getCode())
				.like(StringUtils.isNotBlank(name), User::getName, name)
				.isNull(User::getTenantId)
				.eq(Objects.nonNull(origin), User::getOrigin, origin);
		return repository.selectList(wrapper);
	}

	@Override
	public List<User> selectorUser(String name, Integer state) {
		LambdaQueryWrapper<User> wrapper = Wrappers.lambdaQuery(User.class)
				.eq(User::getDel, CommonDef.Symbol.NO.getCode())
				.like(StringUtils.isNotBlank(name), User::getName, name)
				.isNull(User::getTenantId)
				.eq(Objects.nonNull(state), User::getState, state);
		return repository.selectList(wrapper);
	}

	@Override
	public List<User> selectorUserSeal(String searchParam, Integer state) {
		LambdaQueryWrapper<User> wrapper = Wrappers.lambdaQuery(User.class)
				.eq(User::getDel, CommonDef.Symbol.NO.getCode())
				.isNull(User::getTenantId)
				.eq(Objects.nonNull(state), User::getState, state);
		if (StringUtils.isNotBlank(searchParam)) {
			wrapper.and(x -> x.like(User::getName, searchParam).or()
					.like(User::getMobile, searchParam));
		}
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<UserVo> findVoById(Long id) {
		return super.findOne(id).map(this::packVo);
	}

	@Override
	public Optional<UserVo> findByMobile(String mobile) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(User::getMobile, mobile);
		queryWrapper.isNull(User::getTenantId);
		return Optional.ofNullable(repository.selectOne(queryWrapper))
				.map(this::packVo);
	}

	@Override
	public Optional<UserVo> findByAccountAndTenantId(String mobile,
			Long tenantId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(User::getMobile, mobile);
		queryWrapper.eq(User::getTenantId, tenantId);
		return Optional.ofNullable(repository.selectOne(queryWrapper))
				.map(this::packVo);
	}

	@Override
	public Optional<UserVo> findByMobileAndOrigin(String mobile,
			Integer origin) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(User::getMobile, mobile);
		queryWrapper.eq(User::getOrigin, origin);
		queryWrapper.isNull(User::getTenantId);
		return Optional.ofNullable(repository.selectOne(queryWrapper))
				.map(this::packVo);
	}

	@Override
	public Optional<UserVo> findByWxwUserId(String wxwUserId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(User::getWxwUserId, wxwUserId);
		queryWrapper.isNull(User::getTenantId);
		return Optional.ofNullable(repository.selectOne(queryWrapper))
				.map(this::packVo);
	}

	@Override
	public List<User> findUsersByPermission(String permission, String name) {
		List<Role> roles = roleService.findRolesByPermission(permission);
		if (CollectionUtils.isNotEmpty(roles)) {
			List<Long> roleIds = roles.stream().map(Role::getId).toList();
			LambdaQueryWrapper<User> queryWrapper = Wrappers
					.lambdaQuery(User.class);
			queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
			queryWrapper.like(StringUtils.isNotBlank(name), User::getName,
					name);
			queryWrapper.eq(User::getOrigin,
					CommonDef.UserType.INNER.getCode());
			queryWrapper.apply("JSON_OVERLAPS(role_ids, {0})",
					JsonUtils.objectToJson(roleIds));
			queryWrapper.isNull(User::getTenantId);
			return repository.selectList(queryWrapper);
		}
		return List.of();
	}

	@Override
	public List<User> findUsersByRole(Long roleId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.apply("JSON_CONTAINS(role_ids, {0})", roleId.toString());
		queryWrapper.isNull(User::getTenantId);
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<User> findByNameAndEmployeeId(String name,
			String employeeId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(User::getState, CommonDef.Symbol.YES.getCode());
		queryWrapper.eq(User::getEmployeeId, employeeId);
		queryWrapper.eq(User::getName, name);
		queryWrapper.isNull(User::getTenantId);
		return Optional.ofNullable(repository.selectOne(queryWrapper));
	}

	@Override
	public Optional<User> findByEmployeeId(String employeeId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(User::getEmployeeId, employeeId);
		queryWrapper.isNull(User::getTenantId);
		return Optional.ofNullable(repository.selectOne(queryWrapper));
	}

	@Override
	public Optional<User> findByEmployeeIdAndState(String employeeId,
			Integer state) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(User::getState, state);
		queryWrapper.eq(User::getEmployeeId, employeeId);
		queryWrapper.isNull(User::getTenantId);
		return Optional.ofNullable(repository.selectOne(queryWrapper));
	}

	@Override
	public List<User> findByTenantId(Long tenantId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(User::getState, CommonDef.Symbol.YES.getCode());
		queryWrapper.eq(User::getTenantId, tenantId);
		return repository.selectList(queryWrapper);
	}

	@Override
	public User create(User resource) {
		String password = DxyzmUtils.getNonceStr(6);
		resource.setPassword(passwordEncoder.encode(password));
		resource.setIsSelfSetPassword(CommonDef.Symbol.NO.getCode());
		if (StringUtils.isNotBlank(resource.getMobile())) {
			messageService.sendNotice(AliMessage.builder()
					.templateCode(wxSubscriptionProperties.getAddAdminUserCode())
					.params(Map.of("phone", resource.getMobile(), "random_password",
							password))
					.receiptors(List.of()).mobile(resource.getMobile()).build());
		}
		return super.create(resource);
	}

	@Override
	public User updateAllProperties(User resource) {
		// 校验用户角色是否发生变化
		super.findOne(resource.getId()).ifPresent(user -> {
			if (!Objects.equals(new HashSet<>(user.getRoleIds()),
					new HashSet<>(resource.getRoleIds()))) {
				ThreadPoolUtil.scheduleTask(
						() -> SpringUtil.getBean(BackendUserChangeService.class)
								.userRoleChange(resource.getId()),
						3, TimeUnit.SECONDS, ThreadPoolUtil.getUserExecutor());
			}
		});
		// 用户禁用
		if (resource.getState().equals(CommonDef.Symbol.NO.getCode())) {
			SpringUtil.getBean(BackendUserChangeService.class)
					.userForbidden(resource.getId());
		}
		return super.updateAllProperties(resource);
	}

	@Override
	public void delete(Long id) {
		super.delete(id);
		SpringUtil.getBean(UserChangeService.class).userForbidden(id);
	}

	@Override
	public boolean verifyWhetherRoleUsed(Long roleId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers
				.lambdaQuery(User.class);
		queryWrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.apply("JSON_CONTAINS(role_ids, {0})", roleId.toString());
		Long count = repository.selectCount(queryWrapper);
		return count > 0;
	}

	@Override
	public void resetPassword(User user) {
		String password = DxyzmUtils.getNonceStr(6);
		user.setPassword(passwordEncoder.encode(password));
		user.setIsSelfSetPassword(CommonDef.Symbol.NO.getCode());
		if (StringUtils.isNotBlank(user.getMobile())) {
			messageService.sendNotice(AliMessage.builder()
					.templateCode(wxSubscriptionProperties.getRestPasswordCode())
					.params(Map.of("random_password", password))
					.receiptors(List.of()).mobile(user.getMobile()).build());
		}
		super.updateAllProperties(user);
		SpringUtil.getBean(UserChangeService.class).userForbidden(user.getId());
	}

	@Override
	public void updateState(Long id, Integer state) {
		LambdaUpdateWrapper<User> wrapper = Wrappers.lambdaUpdate(User.class);
		wrapper.eq(User::getId, id);
		wrapper.eq(User::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.set(User::getState, state);
		repository.update(wrapper);
		if (CommonDef.Symbol.NO.match(state)) {
			SpringUtil.getBean(UserChangeService.class).userForbidden(id);
		}
	}

	@Override
	public void updatePassword(User user) {
		super.updateAllProperties(user);
		SpringUtil.getBean(UserChangeService.class).userForbidden(user.getId());
	}

	@Override
	public User createUser(User user) {
		Long id = Calendar.getInstance().getTimeInMillis();
		user.setId(id);
		user.setPassword(passwordEncoder.encode(user.getPassword()));
		user.setIsSelfSetPassword(CommonDef.Symbol.NO.getCode());
		return super.create(user);
	}

	@Override
	public User updateUser(User user) {
		user.setPassword(passwordEncoder.encode(user.getPassword()));
		return super.updateAllProperties(user);
	}

	@Override
	public void update(Person person) {
		super.findOne(person.getId()).ifPresent(user -> {
			user.setName(person.getName());
			user.setMobile(person.getMobile());
			user.setMail(person.getMail());
			super.updateAllProperties(user);
		});
	}

	/**
	 * 组装账号相关数据
	 *
	 * @param users
	 * @return
	 */
	private List<UserVo> packVo(List<User> users) {
		if (CollectionUtils.isEmpty(users)) {
			return List.of();
		}

		List<Long> roleIds = users.stream()
				.filter(user -> CollectionUtils.isNotEmpty(user.getRoleIds()))
				.flatMap(user -> user.getRoleIds().stream().filter(Objects::nonNull))
				.distinct()
				.toList();
		List<Role> roles = roleService.findByIdsNoDeleted(roleIds);
		Map<Long, Role> roleMap = roles.stream()
				.collect(Collectors.toMap(Role::getId, Function.identity()));
		return users.stream().map(user -> {
			UserVo userVo = new UserVo();
			userVo.setUser(user);
			List<Role> roleList = user.getRoleIds().stream().map(roleMap::get)
					.filter(Objects::nonNull).toList();
			userVo.setRoles(roleList);
			return userVo;
		}).toList();
	}

	/**
	 * 组装账号相关数据
	 *
	 * @param user
	 * @return
	 */
	private UserVo packVo(User user) {
		UserVo userVo = new UserVo();
		userVo.setUser(user);
		List<Role> roles = roleService.findByIdsNoDeleted(user.getRoleIds());
		userVo.setRoles(roles);
		List<String> permissions = roles.stream()
				.flatMap(role -> role.getPermissions().stream()).distinct()
				.toList();
		userVo.setPermissions(permissions);
		return userVo;
	}
}
