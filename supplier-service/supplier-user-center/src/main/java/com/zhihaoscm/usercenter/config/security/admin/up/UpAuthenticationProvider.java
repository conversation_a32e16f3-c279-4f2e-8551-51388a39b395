package com.zhihaoscm.usercenter.config.security.admin.up;

import java.time.LocalDateTime;
import java.util.Objects;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.security.BaseUserDetailsService;
import com.zhihaoscm.common.security.LoginUser;
import com.zhihaoscm.common.security.authentication.AbstractUserDetailAuthenticationProvider;
import com.zhihaoscm.domain.bean.entity.AdminSecuritySettingDevice;
import com.zhihaoscm.domain.bean.entity.User;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.meta.error.ErrorCodeDef;
import com.zhihaoscm.usercenter.config.properties.SecuritySettingProperties;
import com.zhihaoscm.usercenter.config.security.custom.wx.exception.CustomerDisableException;
import com.zhihaoscm.usercenter.core.service.AdminSecuritySettingDeviceService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UpAuthenticationProvider
		extends AbstractUserDetailAuthenticationProvider {

	private final PasswordEncoder passwordEncoder;
	private final SecuritySettingProperties securitySettingProperties;
	private final AdminSecuritySettingDeviceService adminSecuritySettingDeviceService;

	public UpAuthenticationProvider(BaseUserDetailsService userDetailsService,
			PasswordEncoder passwordEncoder,
			AdminSecuritySettingDeviceService adminSecuritySettingDeviceService,
			SecuritySettingProperties securitySettingProperties) {
		super(userDetailsService);
		super.setHideUserNotFoundExceptions(false);
		this.passwordEncoder = passwordEncoder;
		this.adminSecuritySettingDeviceService = adminSecuritySettingDeviceService;
		this.securitySettingProperties = securitySettingProperties;
	}

	@Override
	public Authentication authenticate(Authentication authentication)
			throws AuthenticationException {

		UpUniqueKey uniqueKey = (UpUniqueKey) authentication.getPrincipal();
		UserDetails user;
		try {
			user = this.retrieveUser(uniqueKey);
		} catch (UsernameNotFoundException ex) {

			if (!this.hideUserNotFoundExceptions) {
				throw ex;
			}
			throw new BadCredentialsException("Bad credentials");
		}
		// 校验密码
		if (!passwordEncoder.matches(uniqueKey.getPassword(),
				user.getPassword())) {
			throw new UsernameNotFoundException(
					ErrorCodeDef.CODE_30059017.getMessage());
		}
		LoginUser<User> loginUser;
		if (user instanceof LoginUser) {
			loginUser = (LoginUser<User>) user;
		} else {
			// 如果user不是LoginUser类型，可以抛出异常或者执行其他逻辑
			throw new IllegalArgumentException(
					"User is not of type LoginUser<User>");
		}

		// 校验是否重置密码
		if (CommonDef.Symbol.NO
				.match(loginUser.getUser().getIsSelfSetPassword())) {
			// 校验是否绑定该设备
			AdminSecuritySettingDevice adminSecuritySettingDevice = adminSecuritySettingDeviceService
					.findByUserIdAndDeviceCode(loginUser.getUser().getId(),
							uniqueKey.getDeviceCode())
					.orElse(null);
			if (Objects.nonNull(adminSecuritySettingDevice)) {
				adminSecuritySettingDevice.setLoginTime(LocalDateTime.now());
				adminSecuritySettingDeviceService
						.updateAllProperties(adminSecuritySettingDevice);
			} else {
				if (securitySettingProperties.getEnabled()) {
					throw new CustomerDisableException(ErrorCode.CODE_30011002,
							null);
				}
			}
		} else {
			AdminSecuritySettingDevice adminSecuritySettingDevice = new AdminSecuritySettingDevice();
			adminSecuritySettingDevice.setDeviceCode(uniqueKey.getDeviceCode());
			adminSecuritySettingDevice.setUserId(loginUser.getUser().getId());
			adminSecuritySettingDevice.setLoginTime(LocalDateTime.now());
			adminSecuritySettingDeviceService
					.create(adminSecuritySettingDevice);
		}

		return new UpAuthentication(user, user.getAuthorities());
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return (UpAuthentication.class.isAssignableFrom(authentication));
	}
}
