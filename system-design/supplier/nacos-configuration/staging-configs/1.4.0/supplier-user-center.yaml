# Application Configuration
application:
  config:
    # 系统登录token过期时间, 按秒计算 12 * 12 * 60
    sys-access-token-expire: 43200
    # jwt密钥
    jwt-secret: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9"
    admin:
      login-success-page: "https://supplier.staging.saas-admin.zhihaoscm.cn/mytask/index"
      forbidden-page: "https://supplier.staging.saas-admin.zhihaoscm.cn/403"
    supplier-service: ""

# 安全设置
security:
  max-count: 10
  enabled: true

# spring相关配置
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 15MB
      max-request-size: 15MB

mybatis-plus:
  # 别名 bean包
  typeAliasesPackage: com.lianxin.tffm.core.bean.entity
  mapperLocations: classpath:mybatis/mapper/**/*.xml
  global-config:
    banner: false
    db-config:
      update-strategy: ignored
  configuration:
    mapUnderscoreToCamelCase: true
    lazyLoadingEnabled: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-handlers-package: com.zhihaoscm.common.mybatis.plus.json.handler,com.zhihaoscm.domain.bean.json.handler

# spring doc config
springdoc:
  swagger-ui:
    enabled: true
  # 指定包下面的接口生成文档
  packages-to-scan: com.zhihaoscm.usercenter.resource.custom,com.zhihaoscm.usercenter.resource.admin
  api-docs:
    path: /v4/api-docs
  group-configs:
    -   group: 'admin'
        paths-to-match: '/admin/**'
        packages-to-scan:
          # 配置接口文档扫描包路径
          - com.zhihaoscm.usercenter.resource.admin
    -   group: 'custom'
        paths-to-match: '/custom/**'
        packages-to-scan:
          # 配置接口文档扫描包路径
          - com.zhihaoscm.usercenter.resource.custom
    -   group: 'bank'
        paths-to-match: '/bank/**'
        packages-to-scan:
          # 配置接口文档扫描包路径
          - com.zhihaoscm.usercenter.resource.bank

# 后台印章管理
admin:
  seal:
    app-token: 0E6Lu0UmfH
    app-secret: 7c3OggzvvmL49yj7niILHwg0C2tP6T
    mobile: ***********