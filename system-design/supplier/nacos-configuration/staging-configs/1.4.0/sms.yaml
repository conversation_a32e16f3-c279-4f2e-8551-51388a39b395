aliyun:
  sms:
    access-key: LTAI5tNn8xdUUdqidPD69UWL
    secret-key: ******************************
    endpoint: dysmsapi.aliyuncs.com
    connect-timeout: 5
    read-timeout: 10
    # 登录验证码
    loginVerifyCode: SMS_477895023
    # 我有意向验证码
    intentionVerifyCode: SMS_465926750
    # 管理后台审核通过了机构认证
    auditInstitutionCode: SMS_477865055
    # 管理后台审核驳回了机构认证
    dismissInstitutionCode: SMS_477950034
    # 主账号成功邀请子账号
    invitationSubCode: SMS_477970040
    # 子账号状态变更为失效
    failureSubCode: SMS_477800036
    # 子账号状态变更为激活
    activationSubCode: SMS_477900032
    # 新增后台用户后发送随机密码
    addAdminUserCode: SMS_478165005
    # 后台用户重置密码后发送随机密码
    restPasswordCode: SMS_478205004
    # 被添加子账号提醒
    subAccountConfirmCode: SMS_474090121
    # 管理后台新增项目并到了交付中状态
    createProjectCode: SMS_477955023
    # 采购项目到了已完成状态
    finishProjectCode: SMS_477930026
    # 管理后台确认了订单（签署方式：线下；类型：采购订单）
    confirmationOrderCode: SMS_477780027
    # 管理后台驳回了订单（签署方式：线下；类型：采购订单）
    dismissOrderCode: SMS_477805032
    # 订单（签署方式：线上；类型：采购订单）状态流转到已完成
    finishOrderCode: SMS_477835020
    # 订单的是否完成发货变为是
    finishOrderGoodCode: SMS_477945029
    # 供应链端确认了订单（提货申请）
    supplierConfirmationOrderCode: SMS_480205172
    # 供应链端新增发货，且发货状态为发货中
    createGoodsCode: SMS_480225442
    # 供应链端完成了发货，发货状态变更为发货完成
    confirmGoodsCode: SMS_480160421
    # 供应链端取消了发货，发货状态变更为发货取消
    withdrawGoodsCode: SMS_480195167
    # 签收进入待确认状态
    unConfirmReceiptCode: SMS_477875036
    # 签收（签署方式：线上）状态流转到已完成
    finishReceiptCode: SMS_477950026
    # 对账进入待确认状态
    unConfirmReconciliationCode: SMS_477985033
    # 对账（签署方式：线上）状态流转到已完成
    finishReconciliationCode: SMS_477865039
    # 开票进入待确认状态
    unConfirmBillCode: SMS_477845031
    # 额度变更进入待确认状态
    unConfirmQuotaCode: SMS_477975027
    # 合同进入待确认状态
    unConfirmContractCode: SMS_477845032
    # 管理后台确认了合同（签署方式：线下）
    confirmContractCode: SMS_477925037
    # 管理后台确认了签收（签署方式：线下）
    confirmReceiptCode: SMS_477840036
    # 管理后台驳回了签收（签署方式：线下）
    dismissReceiptCode: SMS_477855025
    # 管理后台确认了对账（签署方式：线下）
    confirmReconciliationCode: SMS_477815038
    # 管理后台驳回了对账（签署方式：线下）
    dismissReconciliationCode: SMS_477920026
    # 管理后台确认了开票
    confirmBillCode: SMS_477865052
    # 管理后台驳回了开票
    dismissBillCode: SMS_477915033
    # 管理后台确认了付款
    confirmPaymentCode: SMS_477965037
    # 管理后台驳回了付款
    dismissPaymentCode: SMS_477975032
    # 管理后台确认了额度变更
    confirmQuotaCode: SMS_477890033
    # 管理后台驳回了额度变更
    dismissQuotaCode: SMS_477780038
    # 管理后台驳回了合同（签署方式：线下）
    dismissContractCode: SMS_477795021
    # 合同快到期提醒
    contractExpirationReminder: SMS_479115195
    # 订单账期快到期提醒
    orderAccountPeriodReminder: SMS_479120224
    # 供应链新增一条退款
    createRefundCode: SMS_480265158
    # 供应链端确认了服务费
    confirmServiceFeeCode: SMS_480275167
    # 供应链端驳回了服务费
    dismissServiceFeeCode: SMS_480295152
    # 合同过期提醒
    contractExpirationCode: SMS_481225102
    # 订单账期到期提醒
    orderExpirationCode: SMS_481350023
    # 供应端释放提货额度
    releaseDeliveryQuotaCode: SMS_480975050
    # 银行端驳回
    releaseDeniedCode: SMS_480910114
    # 银行端确认扣款
    releaseConfirmCode: SMS_480830124
    # 借据逾期提醒
    loanReceiptExpectationCode: SMS_480765120
    # 银行端确认了融资
    financingConfirmCode: SMS_480935103
    # 开票作废供应链端发起作废(被驳回)
    billPaymentNullifyDismissCode: SMS_485160059
    # 开票作废供应链端发起作废(待确认)
    billPaymentNullifyConfirmCode: SMS_484990024
    # 签收作废供应链端发起作废(被驳回)
    receiptNullifyDismissCode: SMS_485005022
    # 签收作废供应链端发起作废(待确认)
    receiptNullifyConfirmCode: SMS_485160029
    # 提货作废供应链端作废了发货，发货状态变更为已作废
    deliverNullifyCode: SMS_485170030
    # 订单作废供应链端发起作废(被驳回)
    orderNullifyDismissCode: SMS_484985030
    # 订单作废供应链端发起作废(待确认)
    orderNullifyConfirmCode: SMS_485050030
    # 对账作废供应链端发起作废(被驳回)
    reconciliationNullifyDismissCode: SMS_485290135
    # 对账作废供应链端发起作废(待确认)
    reconciliationNullifyConfirmCode: SMS_485395141
    # 1.20.0新增
    # 新增一条入库单
    createInboundCode: SMS_485830007
    # 出库单进入待确认状态
    unConfirmOutboundCode: SMS_485690017
    # 出库单（签署方式：线上）状态流转到已完成
    finishOutboundCode: SMS_485770020
    # 出库单供应链端发起作废
    outboundNullifyConfirmCode: SMS_485665026
    # 出库单供应链端驳回作废
    outboundNullifyDismissCode: SMS_485685042
    # 盘点进入待确认状态
    unConfirmStocktakingCode: SMS_485720041
    # 盘点（签署方式：线上）状态流转到已完成
    finishStocktakingCode: SMS_485810042
    # 盘点供应链端发起作废
    stocktakingNullifyConfirmCode: SMS_485880025
    # 盘点供应链端驳回作废
    stocktakingNullifyDismissCode: SMS_485900034
    # 库存抽检进入待确认状态
    unConfirmStockCheckCode: SMS_485740032
    # 库存抽检（签署方式：线上）状态流转到已完成
    finishStockCheckCode: SMS_485875025
    # 库存抽检供应链端发起作废
    stockCheckNullifyConfirmCode: SMS_485735043
    # 库存抽检供应链端驳回作废
    stockCheckNullifyDismissCode: SMS_485775038
    # 新增一条质押
    createPledgeCode: SMS_485680040
    # 变更一条质押
    changePledgeCode: SMS_485880026
    # 主账号认证失效
    rootAccountCertificationLapses: SMS_485675169
    # 企业变更导致子账号失效
    subAccountCertificationLapses: SMS_485675168
    # 企业重新认证子账号恢复使用
    subAccountRestore: SMS_485840204
    # 1.3.0新增
    # 入库被驳回
    inboundDismissCode: SMS_488020048
    # 入库单供应链端发起作废
    inboundNullifyConfirmCode: SMS_488070041
    # 入库单供应链端驳回作废
    inboundNullifyDismissCode: SMS_488235035
    # 出库被驳回
    outboundDismissCode: SMS_488115045
    # 过户进入待核实状态
    transferBeVerifiedCode: SMS_488195045
    # 待核实用户点击确认、重新发送验证码
    transferVerificationCode: SMS_488065037
    # 供应链端审核驳回了机构认证
    rejectInstitutionApply: SMS_488210026
    # 供应链端审核通过了机构认证-两个身份
    passInstitutionApplyAll: SMS_488395002
    # 供应链端审核通过了机构认证-采购
    passInstitutionApplyPurchase: SMS_488210027
    # 供应链端审核通过了机构认证-仓储
    passInstitutionApplyWarehouse: SMS_487990038
    # 1.3.2新增
    # 对账单状态流转到预对账完成状态
    reconciliationPreFinishedCode: SMS_490765133
    # 1.4.0新增
    # 签收进入待确认状态
    unConfirmSignReceiptCode: SMS_490965210
    # 供应链端确认了退款
    refundConfirmCode: SMS_491205228
    # 供应链端驳回了退款
    refundNullifyCode: SMS_491205229
    # 收款进入待确认状态
    paymentUnConfirmCode: SMS_491020252
    # 订单进入待确认状态
    orderUnConfirmCode: SMS_491195240
    # 结算进入待确认状态
    reconciliationUnConfirmCode: SMS_491485387
    # 供应链端确认了结算（签署方式：线下）
    reconciliationConfirmCode: SMS_491475330
    # 供应链端驳回了结算（签署方式：线下）
    reconciliationDismissCode: SMS_491495351
    # 结算（签署方式：线上）状态流转到结算完成
    reconciliationFinishCode: SMS_491330366
    # 供应链端发起作废
    nullifyConfirmReconciliationCode: SMS_491315376
    # 供应链端驳回作废
    nullifyDismissReconciliationCode: SMS_491405365
    # 结算（签署方式：线上）状态流转到预结算完成
    preFinishedReconciliationCode: SMS_491530391
    # 短信链接小程序 URL Link 失效类型，失效时间：0，失效间隔天数：1
    expireType: 1
    # 短信链接失效时间，默认最长时限30天
    expireNum: 30