server:
  port: 8012

#logback.mxl文件配置
logging:
  config: classpath:config/staging/logback.xml

# actuator配置, 先要暴露端点，然后才能启用
management:
  endpoints:
    # 默认"启用"端点的开关
    enabled-by-default: false
    # http端点配置
    web:
      # 自定义根路径
      # base-path: /manager
      # 自定义端点路径
      path-mapping:
        health: healthcheck
        info: appinfo
      # "暴露"端点的相关配置
      exposure:
        # 加入端点， *代表所有端点
        include: "*"
        # 排除端点
        exclude: beans
  # 启用端点的开关
  endpoint:
    gateway:
      enabled: true
    info:
      enabled: true
    health:
      enabled: true
    env:
      enabled: true
    # post端点
    refresh:
      enabled: true

# gateway 路由配置
spring:
  cloud:
    gateway:
      httpclient:
        connect-timeout: 1000
        response-timeout: 3s
      # x-forwarded:
      #     # 总开关，启用X-Forwarded系列头处理
      #     enabled: true
      #     # 是否启用X-Forwarded-For头处理
      #     for-enabled: true
      discovery:
        locator:
          lower-case-service-id: true
      globalcors:
        cors-configurations:
          "[/**]":
            allowedHeaders: "*"
            allowedOrigins:
              - "https://www.zhihaoscm.cn"
              - "https://supplier.staging.zhihaoscm.cn"
              - "https://supplier.staging.admin.zhihaoscm.cn"
              - "https://supplier.staging.api.zhihaoscm.cn"
              - "https://supplier.staging.map.zhihaoscm.cn"
              - "http://localhost:8848"
              - "http://localhost:5173"
              - "http://localhost:3000"
              - "https://supplier.staging.saas-admin.zhihaoscm.cn"
            allowedMethods: "*"
            #                  - POST
            #                    GET
            #                    DELETE
            #                    PUT
            #                    OPTION
            allowCredentials: true

      default-filters:
        - StripPrefix=0
      routes:
        - id: supplier-ws
          uri: lb:ws://supplier-ws
          predicates:
            - Path=/ws/**
          filters:
            - StripPrefix=1
        - id: supplier-user-center-swagger
          uri: lb://supplier-user-center
          predicates:
            - Path=/admin/v4/api-docs/**,/custom/v4/api-docs/**,/bank/v4/api-docs/**,/backend/v4/api-docs/**
          filters:
            - StripPrefix=1
        - id: supplier-service-backend
          uri: lb://supplier-service
          predicates:
            - Path=/backend/backend-supplier/change-role/**,/backend/backend-customer/change-role/**
          filters:
            - StripPrefix=0
        - id: supplier-user-center
          uri: lb://supplier-user-center
          predicates:
            - Path=/admin/activation/code/**,/admin/captcha/**,/admin/operationLog/paging,/admin/business-config/**,/admin/customer/**,/admin/dept/**,/admin/login/**,/admin/membership-level/**,/admin/member-open-record/**,/admin/msg/send,/admin/person/**,/admin/promotion/**,/admin/role/**,/admin/user/**,/custom/activation/code/**,/custom/callback/personal/**,/custom/callback/enterprise/**,/custom/captcha/**,/custom/business-config/**,/custom/membership-level/**,/custom/login,/custom/login/**,/custom/msg/**,/admin/institution-apply/**,/custom/institution-apply/**,/custom/create,/custom/logout,/admin/logout,/admin/callback/wxw/change,/bank/login/**,/bank/captcha/**,/bank/msg/**,/bank/role/**,/bank/user/**,/bank/bank/user/**,/custom/regular-customers/**,/custom/customer/receiving-address,/custom/customer/receiving-address/**,/custom/customer/bank,/custom/customer/bank/**,/custom/customer/certification,/custom/customer/certification/**,/custom/customer/invoice/header,/custom/customer/invoice/header/**,/custom/customer/sub,/custom/customer/sub/**,/custom/customer/real-name/*,/custom/customer/mobile/*,/custom/customer/customer-sub,/custom/customer/mobile,/custom/customer/head-img,/custom/customer/password/**,/custom/customer/registration,/custom/customer/selector-regular,/backend/customer/**,/backend/dealings-enterprise/**,/backend/dept/**,/backend/file/**,/backend/institution-apply/**,/backend/login/**,/backend/person/**,/backend/role/**,/backend/supplier/**,/backend/user/**,/admin/callback/personal/**,/admin/callback/enterprise/**,/custom/supplier/**
          filters:
            - StripPrefix=0
        - id: supplier-service-specific-route
          uri: lb://supplier-service
          predicates:
            - Path=/admin/contract/initiate-sign/**,/admin/order/initiate-sign/**,/admin/reconciliation/sale/initiate-signing/**,/admin/sign/receipt/buy/initiate-sign/**,/admin/sign/receipt/sale/initiate-sign/**,/admin/deliver/goods/initiate/initiate-sign/**,/custom/reconciliation/buy/initiate-signing/**,/custom/contract/initiate-sign/**,/custom/sign/receipt/buy/initiate-sign/**,/custom/order/initiate-sign/**,/admin/ship/**,/admin/car/**,/custom/ship/**,/custom/car/**,/admin/admin-seal/**,/admin/stock-check/initiate-signing/**,/admin/stocktaking/initiate-signing/**,/admin/outbound/initiate-sign/**,/admin/inventory-verify-report/**,/admin/inventory-details/export/**,/admin/inventory-details/export-buy,/custom/inventory-details/export/**,/bank/inventory-verify-report/**,/bank/inventory-details/export/**,/bank/car/**,/admin/camera/capture,/custom/inbound/initiate-sign/**,/custom/outbound/initiate-sign/**,/admin/stock-contract/initiate-sign/**,/custom/customer/seal/**,/admin/admin-seal/**,/custom/reconciliation/buy/update/reconciliation/**
          metadata:
            response-timeout: 1000000
          filters:
            - StripPrefix=0
        - id: supplier-swagger
          uri: lb://supplier-service
          predicates:
            - Path=/admin/v3/api-docs/**,/custom/v3/api-docs/**,/bank/v3/api-docs/**,/backend/v3/api-docs/**
          filters:
            - StripPrefix=1
        - id: supplier-service
          uri: lb://supplier-service
          predicates:
            - Path=/admin/**,/custom/**,/bank/**,/backend/**
          filters:
            - StripPrefix=0

    sentinel:
      datasource:
        ds1:
          nacos:
            server-addr: ${spring.cloud.nacos.config.server-addr}
            namespace: ${spring.cloud.nacos.config.namespace}
            username: ${spring.cloud.nacos.config.username}
            password: ${spring.cloud.nacos.config.password}
            dataId: sentinel.json
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
        ds2:
          nacos:
            server-addr: ${spring.cloud.nacos.config.server-addr}
            namespace: ${spring.cloud.nacos.config.namespace}
            username: ${spring.cloud.nacos.config.username}
            password: ${spring.cloud.nacos.config.password}
            data-id: api-group.json
            group-id: DEFAULT_GROUP
            data-type: json
            rule-type: gw-api-group
application:
  config:
    ignoreUrls:
      - /**/login/**
      - /dev/**
      - /test/**
      - /ws/**
      - /api/sso-passport/login
      - /**/file/**
      - /**/admin/**
      - /**/admin/role/**
      - /**/custom/**
      - /**/bank/**
      - /**/userCenter/**
      - /**/transport/**
      - /**/backend/**
knife4j:
  gateway:
    enabled: true
    # 指定服务发现的模式聚合微服务文档，并且是默认 default 分组
    strategy: manual
    routes:
      - name: admin-api
        # 文档地址，查看单体服务中的文档地址，并且加上前缀，并且注意分组
        url: /admin/v3/api-docs/admin
        # 上下文前缀，看 spring.cloud.gateway.routes 中每个服务的 predicates.Path 里写的前缀
        context-path: /
        # 服务名
        service-name: supplier-service
        # 排序
        order: 1
      - name: custom-api
        url: /custom/v3/api-docs/custom
        context-path: /
        service-name: supplier-service
        order: 2
      - name: user-center-admin
        url: /admin/v4/api-docs/admin
        context-path: /
        service-name: supplier-user-center
        order: 3
      - name: user-center-custom
        url: /custom/v4/api-docs/custom
        context-path: /
        service-name: supplier-user-center
        order: 4
      - name: bank-api
        url: /bank/v3/api-docs/bank
        context-path: /
        service-name: supplier-service
        order: 5
      - name: user-center-bank
        url: /bank/v4/api-docs/bank
        context-path: /
        service-name: supplier-user-center
        order: 6
