use
zhihaoscm_supplier;

create table t_person
(
    id           bigint unsigned auto_increment
        primary key,
    employee_id  varchar(10) null comment '工号',
    tenant_id    bigint unsigned                      null comment '企业定制id',
    code         varchar(64) not null comment '员工编号',
    name         varchar(64) null comment '人员姓名',
    mobile       varchar(64) null comment '手机号',
    mail         varchar(64) null comment '邮箱账号',
    position     varchar(128) null comment '职位描述',
    del          tinyint(1) default 0                 null,
    origin       tinyint(1)                           null comment '来源',
    created_by   bigint unsigned                      null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by   bigint unsigned                      null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    constraint code_UNIQUE
        unique (code)
) comment '员工';

create table t_dept
(
    id           bigint unsigned auto_increment
        primary key,
    tenant_id    bigint unsigned                      null comment '企业定制id',
    parent_id    bigint                             not null comment '上级部门id',
    code         varchar(64) null comment '部门编码',
    name         varchar(64)                        not null comment '部门名称',
    sort         bigint                             not null comment '排序',
    del          tinyint(1) default 0                 not null comment '删除标志 0存在 1删除',
    origin       tinyint(1)                           null comment '来源',
    created_by   bigint unsigned                      null,
    created_time datetime default CURRENT_TIMESTAMP not null,
    updated_by   bigint unsigned                      null,
    updated_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    constraint code_UNIQUE
        unique (code)
);

create table t_person_dept
(
    id           bigint unsigned auto_increment
        primary key,
    tenant_id    bigint unsigned                      null comment '企业定制id',
    person_id    bigint unsigned                      not null comment '用户id',
    dept_id      bigint unsigned                      not null comment '部门id',
    main         tinyint(1) default 0                 null comment '是否是主部门',
    del          tinyint(1) default 0                 null,
    origin       tinyint(1)                           null comment '来源',
    created_by   bigint unsigned                      null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by   bigint unsigned                      null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
);

-- 组织机构认证增加字段
alter table t_institution_apply
    add source tinyint(1) default 1 null comment '来源：1：客户端  2：供应链' after roles;

alter table t_institution_apply
    add power_attorney_file_id bigint null comment '委托授权书文件id' after business_license_file_id;

-- 项目增加字段
ALTER TABLE t_project
    ADD COLUMN `supplier_is_recorded` tinyint      NULL COMMENT '上游供应商是否自录数据' AFTER `introduction`,
    ADD COLUMN `customer_is_recorded` tinyint      NULL COMMENT '下游采购方是否自录数据' AFTER `supplier_settle_period`,
    ADD COLUMN `supervising_unit`     varchar(255) NULL COMMENT '监管单位' AFTER `is_exist_storage`,
    ADD COLUMN `goods_unit` varchar(32) NULL COMMENT '货物单位' AFTER `goods_name`;
-- 合同增加字段
ALTER TABLE t_contract
    ADD COLUMN `upstream_id`  bigint NULL COMMENT '上游供应商往来企业id' AFTER `supplier_chain_enterprise`,
    ADD COLUMN `downstream_id` bigint NULL COMMENT '下游采购方往来企业id' AFTER `upstream_suppliers_enterprise`;



alter table t_contract_lock_record
    add source tinyint(1) default 1 null comment '数据来源 1：客户端 2：供应链' after state;

create table t_user_personal_certification
(
    id           bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id    bigint unsigned                    null comment '企业定制id',
    user_id      bigint unsigned                    not null comment '用户id',
    name         varchar(55)        not null comment '真实姓名',
    id_no        varchar(18)        not null comment '身份证号',
    status       tinyint null comment '认证状态',
    message      varchar(255) null comment '认证返回信息',
    state        tinyint null comment '个人认证记录状态 0：换绑手机号 1：有效 2：无效',
    del          tinyint  default 0 not null comment '是否删除',
    origin       tinyint(1)                         null comment '来源',
    created_by   bigint null comment '创建人',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by   bigint null comment '更新人',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '用户个人认证记录表' row_format = DYNAMIC;

create table t_user_enterprise_certification
(
    id                         bigint unsigned auto_increment comment '主键'
        primary key,
    user_id                    bigint null comment '用户id',
    tenant_id                  bigint unsigned                    null comment '企业定制id',
    company_id                 bigint null comment '契约锁返回的企业id',
    name                       varchar(255) null comment '企业名称',
    unified_social_credit_code varchar(25) null comment '统一社会信用代码',
    legal_representative       varchar(25) null comment '法定代表人',
    message                    varchar(255) null comment '认证返回信息',
    status                     tinyint null comment '认证状态',
    auth_end_time              varchar(20) null comment '授权有效期',
    state                      tinyint null comment '企业认证记录状态 0：换绑手机号 1：有效 2：无效',
    del                        tinyint  default 0 not null comment '是否删除',
    origin                     tinyint(1)                         null comment '来源',
    created_by                 bigint null comment '创建人',
    created_time               datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by                 bigint null comment '更新人',
    updated_time               datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '用户企业认证记录表' row_format = DYNAMIC;

-- 供应链端修改字段
alter table t_supplier
    add account varchar(20) null comment '账号' after tenant_id;

alter table t_supplier
    add password varchar(50) null comment '密码' after account;

alter table t_supplier
    add email varchar(20) null comment '邮箱' after mobile;

alter table t_supplier
    add code varchar(5) null comment '编号' after tenant_id;


alter table t_supplier
    add real_name varchar(25) null comment '真实姓名' after email;

alter table t_supplier
    add real_name_pinyin varchar(128) null comment '真实姓名拼音' after real_name;

alter table t_supplier
    add id_no varchar(18) null comment '身份证号' after real_name_pinyin;

alter table t_supplier
    add app_token varchar(64) null comment '企业认证令牌' after id_no;

alter table t_supplier
    add app_secret varchar(64) null comment '企业认证秘钥' after app_token;

alter table t_supplier
    add seal_admin tinyint(1) null comment '是否是印章管理员' after app_secret;

alter table t_supplier
    add apply_state tinyint(1) default 0 null comment '机构认证状态' after seal_admin;

alter table t_supplier
    add has_expired tinyint null comment '企业认证是否已过期' after apply_state;

alter table t_supplier
    add roles json null comment '企业身份' after has_expired;

alter table t_supplier
    add system_name varchar(20) null comment '系统名称' after email;

alter table t_supplier
    add system_logo bigint null comment '系统logo' after system_name;


-- 对账新增字段
ALTER TABLE t_reconciliation
    ADD COLUMN `purchaser_business_id` bigint         NULL COMMENT '采购方往来企业id' AFTER `type`,
    ADD COLUMN `seller_business_id`    bigint         NULL COMMENT '销售方往来企业id' AFTER `purchaser_enterprise`,
    ADD COLUMN `settle_period`         int            NULL COMMENT '账期' AFTER `state`,
    ADD COLUMN `pre_settle_way`        tinyint        NULL COMMENT '预结算方式' AFTER `settle_period`,
    ADD COLUMN `pre_payment_value`     decimal(16, 2) NULL COMMENT '预付比例/预付金额' AFTER `pre_settle_way`,
    ADD COLUMN `settlement_payment`    decimal(16, 2) NULL COMMENT '结算预付款' AFTER `pre_paymenrt_value`,
    ADD COLUMN `is_record`             tinyint        NULL COMMENT '销售对账的采购方/采购对账的销售方 是否录入企业' AFTER `pre_delivered_info`;


create table t_dealings_enterprise
(
    id                                        bigint unsigned auto_increment
        primary key,
    tenant_id                                 bigint unsigned                      null comment '企业定制id',
    enterprise_type                           tinyint(1)                           null comment '企业类型(1：注册企业,2：录入企业)',
    initiator                                 tinyint(1)                           null comment '类型(1：客户，2：供应链)',
    customer_id                               bigint unsigned                      null comment '客户id',
    institution_name                          varchar(255) null comment '客户企业名称',
    unified_social_credit_code                varchar(25) null comment '客户统一社会信用代码',
    legal_representative                      varchar(25) null comment '客户法定代表人',
    supplier_chain_id                         bigint unsigned                      null comment '供应链id',
    supplier_chain_institution_name           varchar(255) null comment '供应链企业名称',
    supplier_chain_unified_social_credit_code varchar(25) null comment '供应链统一社会信用代码',
    supplier_chain_legal_representative       varchar(25) null comment '供应链法定代表人',
    name                                      varchar(64) null comment '联系人',
    mobile                                    varchar(64) null comment '手机号码',
    state                                     tinyint(1)                           not null comment '状态(1：待确认,2：确认中,3：合作中)',
    remark                                    varchar(300) null comment '驳回原因',
    del                                       tinyint(1) default 0                 not null comment '删除标志 0存在 1删除',
    origin                                    tinyint(1)                           null comment '来源',
    created_by                                bigint unsigned                      null,
    created_time                              datetime default CURRENT_TIMESTAMP null,
    updated_by                                bigint unsigned                      null,
    updated_time                              datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
) comment '往来企业';



alter table t_customer_bank
    add biz_type tinyint(1) default 1 not null comment '业务类型' after type;

alter table t_supplier
    auto_increment = ************;

alter table t_sign_receipt
    add type tinyint(1) null comment '单据类型 1：采购 2销售' after status;

alter table t_user
    add is_main tinyint default 0 null comment '是否是创建供应链下的主账号' after state;

ALTER TABLE t_payment
    ADD COLUMN `is_deposit_transfer` tinyint NULL DEFAULT 0 COMMENT '是否处于保证金转货款流程中 1是,0否' AFTER `state`;


alter table t_customer
    add system_name varchar(50) null comment '系统名称' after state;

alter table t_customer
    add system_logo bigint null comment '系统logo' after system_name;

-- 开票新增字段
ALTER TABLE t_bill_payment
    ADD COLUMN `purchaser_input_id` bigint unsigned        NULL COMMENT '录入采购方id' AFTER `bill_type`,
    ADD COLUMN `seller_input_id`    bigint unsigned        NULL COMMENT '录入销售方id' AFTER `purchaser_enterprise`;

-- 签收新增字段
ALTER TABLE t_sign_receipt
    ADD COLUMN `purchaser_input_id` bigint unsigned        NULL COMMENT '录入采购方id' AFTER `contract_name`,
    ADD COLUMN `seller_input_id`    bigint unsigned        NULL COMMENT '录入销售方id' AFTER `purchaser_enterprise`;

-- 出库新增字段
alter table t_outbound
    add purchaser_business_id bigint null comment '采购方往来企业id' after outbound_type;

alter table t_outbound
    add seller_business_id bigint null comment '销售方往来企业id' after purchaser_enterprise;




