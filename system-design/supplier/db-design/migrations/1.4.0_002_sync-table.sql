-- 同步到zhihaoscm_supplier
-- t_dept
-- ① INSERT 触发器：源表新增数据时同步
DELIMITER $$
CREATE TRIGGER t_dept_sync_after_insert
AFTER INSERT ON zhihaoscm.t_dept
FOR EACH ROW
BEGIN
  INSERT INTO zhihaoscm_supplier.t_dept 
  VALUES (new.id,
new.tenant_id,
new.parent_id,
new.code,
new.name,
new.sort,
new.del,
new.origin,
new.created_by,
new.created_time,
new.updated_by,
new.updated_time);
END$$
DELIMITER ;

-- ② UPDATE 触发器：源表修改数据时同步
DELIMITER $$
CREATE TRIGGER t_dept_sync_after_update
AFTER UPDATE ON zhihaoscm.t_dept
FOR EACH ROW
BEGIN
  UPDATE zhihaoscm_supplier.t_dept
  SET 
    id=new.id,
tenant_id=new.tenant_id,
parent_id=new.parent_id,
code=new.code,
name=new.name,
sort=new.sort,
del=new.del,
origin=new.origin,
created_by=new.created_by,
created_time=new.created_time,
updated_by=new.updated_by,
updated_time=new.updated_time
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;

-- ③ DELETE 触发器：源表删除数据时同步
DELIMITER $$
CREATE TRIGGER t_dept_sync_after_delete
AFTER DELETE ON zhihaoscm.t_dept
FOR EACH ROW
BEGIN
  DELETE FROM zhihaoscm_supplier.t_dept
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;


-- t_person
-- ① INSERT 触发器：源表新增数据时同步
DELIMITER $$
CREATE TRIGGER t_person_sync_after_insert
AFTER INSERT ON zhihaoscm.t_person
FOR EACH ROW
BEGIN
  INSERT INTO zhihaoscm_supplier.t_person 
  VALUES (new.id,
new.employee_id,
new.tenant_id,
new.code,
new.name,
new.mobile,
new.mail,
new.position,
new.del,
new.origin,
new.created_by,
new.created_time,
new.updated_by,
new.updated_time);
END$$
DELIMITER ;

-- ② UPDATE 触发器：源表修改数据时同步
DELIMITER $$
CREATE TRIGGER t_person_sync_after_update
AFTER UPDATE ON zhihaoscm.t_person
FOR EACH ROW
BEGIN
  UPDATE zhihaoscm_supplier.t_person
  SET 
    id=new.id,
employee_id=new.employee_id,
tenant_id=new.tenant_id,
code=new.code,
name=new.name,
mobile=new.mobile,
mail=new.mail,
position=new.position,
del=new.del,
origin=new.origin,
created_by=new.created_by,
created_time=new.created_time,
updated_by=new.updated_by,
updated_time=new.updated_time
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;

-- ③ DELETE 触发器：源表删除数据时同步
DELIMITER $$
CREATE TRIGGER t_person_sync_after_delete
AFTER DELETE ON zhihaoscm.t_person
FOR EACH ROW
BEGIN
  DELETE FROM zhihaoscm_supplier.t_person
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;


-- t_person_dept
-- ① INSERT 触发器：源表新增数据时同步
DELIMITER $$
CREATE TRIGGER t_person_dept_sync_after_insert
AFTER INSERT ON zhihaoscm.t_person_dept
FOR EACH ROW
BEGIN
  INSERT INTO zhihaoscm_supplier.t_person_dept
  VALUES (
   new.id,
   new.tenant_id,
   new.person_id,
   new.dept_id,
   new.main,
   new.del,
   new.origin,
   new.created_by,
   new.created_time,
   new.updated_by,
   new.updated_time
  );
END$$
DELIMITER ;

-- ② UPDATE 触发器：源表修改数据时同步
DELIMITER $$
CREATE TRIGGER t_person_dept_sync_after_update
AFTER UPDATE ON zhihaoscm.t_person_dept
FOR EACH ROW
BEGIN
  UPDATE zhihaoscm_supplier.t_person_dept
  SET 
   id=new.id,
   tenant_id=new.tenant_id,
   person_id=new.person_id,
   dept_id=new.dept_id,
   main=new.main,
   del=new.del,
   origin=new.origin,
   created_by=new.created_by,
   created_time=new.created_time,
   updated_by=new.updated_by,
   updated_time=new.updated_time
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;

-- ③ DELETE 触发器：源表删除数据时同步
DELIMITER $$
CREATE TRIGGER t_person_dept_sync_after_delete
AFTER DELETE ON zhihaoscm.t_person_dept
FOR EACH ROW
BEGIN
  DELETE FROM zhihaoscm_supplier.t_person_dept
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;


-- 同步到zhihaoscm_supplier_saas
-- t_dept
-- ① INSERT 触发器：源表新增数据时同步
DELIMITER $$
CREATE TRIGGER saas_t_dept_sync_after_insert
AFTER INSERT ON zhihaoscm.t_dept
FOR EACH ROW
BEGIN
  INSERT INTO zhihaoscm_supplier_saas.t_dept 
  VALUES (new.id,
new.tenant_id,
new.parent_id,
new.code,
new.name,
new.sort,
new.del,
new.origin,
new.created_by,
new.created_time,
new.updated_by,
new.updated_time);
END$$
DELIMITER ;

-- ② UPDATE 触发器：源表修改数据时同步
DELIMITER $$
CREATE TRIGGER saas_t_dept_sync_after_update
AFTER UPDATE ON zhihaoscm.t_dept
FOR EACH ROW
BEGIN
  UPDATE zhihaoscm_supplier_saas.t_dept
  SET 
    id=new.id,
tenant_id=new.tenant_id,
parent_id=new.parent_id,
code=new.code,
name=new.name,
sort=new.sort,
del=new.del,
origin=new.origin,
created_by=new.created_by,
created_time=new.created_time,
updated_by=new.updated_by,
updated_time=new.updated_time
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;

-- ③ DELETE 触发器：源表删除数据时同步
DELIMITER $$
CREATE TRIGGER saas_t_dept_sync_after_delete
AFTER DELETE ON zhihaoscm.t_dept
FOR EACH ROW
BEGIN
  DELETE FROM zhihaoscm_supplier_saas.t_dept
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;


-- t_person
-- ① INSERT 触发器：源表新增数据时同步
DELIMITER $$
CREATE TRIGGER saas_t_person_sync_after_insert
AFTER INSERT ON zhihaoscm.t_person
FOR EACH ROW
BEGIN
  INSERT INTO zhihaoscm_supplier_saas.t_person 
  VALUES (new.id,
new.employee_id,
new.tenant_id,
new.code,
new.name,
new.mobile,
new.mail,
new.position,
new.del,
new.origin,
new.created_by,
new.created_time,
new.updated_by,
new.updated_time);
END$$
DELIMITER ;

-- ② UPDATE 触发器：源表修改数据时同步
DELIMITER $$
CREATE TRIGGER saas_t_person_sync_after_update
AFTER UPDATE ON zhihaoscm.t_person
FOR EACH ROW
BEGIN
  UPDATE zhihaoscm_supplier_saas.t_person
  SET 
    id=new.id,
employee_id=new.employee_id,
tenant_id=new.tenant_id,
code=new.code,
name=new.name,
mobile=new.mobile,
mail=new.mail,
position=new.position,
del=new.del,
origin=new.origin,
created_by=new.created_by,
created_time=new.created_time,
updated_by=new.updated_by,
updated_time=new.updated_time
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;

-- ③ DELETE 触发器：源表删除数据时同步
DELIMITER $$
CREATE TRIGGER saas_t_person_sync_after_delete
AFTER DELETE ON zhihaoscm.t_person
FOR EACH ROW
BEGIN
  DELETE FROM zhihaoscm_supplier_saas.t_person
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;


-- t_person_dept
-- ① INSERT 触发器：源表新增数据时同步
DELIMITER $$
CREATE TRIGGER saas_t_person_dept_sync_after_insert
AFTER INSERT ON zhihaoscm.t_person_dept
FOR EACH ROW
BEGIN
  INSERT INTO zhihaoscm_supplier_saas.t_person_dept
  VALUES (
   new.id,
   new.tenant_id,
   new.person_id,
   new.dept_id,
   new.main,
   new.del,
   new.origin,
   new.created_by,
   new.created_time,
   new.updated_by,
   new.updated_time
  );
END$$
DELIMITER ;

-- ② UPDATE 触发器：源表修改数据时同步
DELIMITER $$
CREATE TRIGGER saas_t_person_dept_sync_after_update
AFTER UPDATE ON zhihaoscm.t_person_dept
FOR EACH ROW
BEGIN
  UPDATE zhihaoscm_supplier_saas.t_person_dept
  SET 
   id=new.id,
   tenant_id=new.tenant_id,
   person_id=new.person_id,
   dept_id=new.dept_id,
   main=new.main,
   del=new.del,
   origin=new.origin,
   created_by=new.created_by,
   created_time=new.created_time,
   updated_by=new.updated_by,
   updated_time=new.updated_time
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;

-- ③ DELETE 触发器：源表删除数据时同步
DELIMITER $$
CREATE TRIGGER saas_t_person_dept_sync_after_delete
AFTER DELETE ON zhihaoscm.t_person_dept
FOR EACH ROW
BEGIN
  DELETE FROM zhihaoscm_supplier_saas.t_person_dept
  WHERE id = OLD.id;  -- 用 OLD.id 定位目标记录
END$$
DELIMITER ;


-- 插入数据
insert into zhihaoscm_supplier.t_person_dept select * from  zhihaoscm.t_person_dept;
insert into zhihaoscm_supplier_saas.t_person_dept select * from  zhihaoscm.t_person_dept;

insert into zhihaoscm_supplier.t_dept select * from  zhihaoscm.t_dept;
insert into zhihaoscm_supplier_saas.t_dept select * from  zhihaoscm.t_dept;

insert into zhihaoscm_supplier.t_person select * from  zhihaoscm.t_person;
insert into zhihaoscm_supplier_saas.t_person select * from  zhihaoscm.t_person;