create schema zhihaoscm_supplier_saas collate utf8mb4_0900_ai_ci;

use zhihaoscm_supplier_saas;

create table t_accounts
(
    id varchar(64) not null comment '主键,编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    project_name varchar(512) null comment '项目名称',
    contract_id varchar(64) null comment '合同id',
    contract_name varchar(64) null comment '合同名称',
    order_id varchar(64) null comment '订单id',
    reconciliation_id varchar(64) null comment '对账单id',
    payment_id bigint null comment '付款单位id',
    payment_business_id bigint null comment '付款单位往来企业id',
    payment_enterprise json null comment '付款单位信息',
    receipt_id bigint null comment '收款单位id',
    receipt_business_id bigint null comment '收款单位往来企业id',
    receipt_enterprise json null comment '收款单位信息',
    amount decimal(16,2) null comment '应收/应付金额',
    receipted_amount decimal(16,2) null comment '已收金额',
    un_receipt_amount decimal(16,2) null comment '未收金额',
    settle_end_date datetime null comment '账期截止日期',
    delay_date int null comment '逾期时长',
    status tinyint(1) null comment '状态;1.unsettled:未结清,2.delay:已逾期,3.settled:已结清',
    purchase_date datetime null comment '采购日期',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    type tinyint null comment '类型 1.采购 2.销售',
    project_long_id bigint null comment '项目长ID'
)
    comment '应收/应付账款表' row_format = DYNAMIC;

create table t_admin_seal
(
    id bigint unsigned auto_increment comment '主键id'
        primary key,
    tenant_id bigint unsigned null comment '租户id',
    third_seal_id varchar(64) null comment '第三方印章id',
    name varchar(50) not null comment '印章名称',
    style tinyint(1) not null comment '印章样式',
    foot varchar(50) null comment '下方横排文字',
    info_code varchar(50) null comment '信息编码,即印章上的数字',
    surround_text varchar(255) null comment '环绕文字',
    correlation_business json null comment '关联业务',
    file_id bigint unsigned null comment '印章图片文件id',
    state tinyint(1) default 1 not null comment '状态',
    module tinyint default 1 null comment '模块',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '供应链印章管理';

create table t_admin_security_setting_device
(
    id bigint unsigned auto_increment comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    user_id bigint null comment '用户id',
    device_code varchar(64) null comment '设备编号',
    login_time datetime null comment '登录时间',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '供应链端安全设置设备表';

create table t_article
(
    id bigint unsigned auto_increment comment '编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    type tinyint(1) null comment '文章类型',
    content text not null comment '内容',
    remark varchar(200) null,
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '文章';

create table t_bank_role
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    name varchar(64) not null comment '角色名称',
    remark varchar(128) null comment '角色描述',
    permissions json not null comment '角色拥有权限列表',
    del tinyint(1) default 0 not null comment '删除标志 0存在 1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by bigint unsigned null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
);

create table t_bank_security_setting_device
(
    id bigint unsigned auto_increment comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    bank_user_id bigint null comment '用户id',
    device_code varchar(64) null comment '设备编号',
    login_time datetime null comment '登录时间',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '银行端安全设置设备表';

create table t_bank_user
(
    id bigint unsigned auto_increment
        primary key,
    employee_id varchar(10) null comment '工号',
    tenant_id bigint unsigned null comment '企业定制id',
    enterprise_custom_id bigint unsigned null comment '定制平台id',
    wxw_user_id varchar(64) null comment '企业微信userId',
    name varchar(64) not null comment '用户名称',
    password varchar(64) null comment '登录密码',
    mobile varchar(64) null comment '手机号',
    mail varchar(64) null comment '邮箱账号',
    role_ids json null comment '授权角色列表',
    is_self_set_password tinyint default 0 not null comment '是否自己设置密码',
    state tinyint(1) default 1 not null comment '状态 1 启用  0 禁用',
    del tinyint(1) default 0 not null comment '删除标志 0存在 1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by bigint unsigned null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
);

create table t_bill_payment
(
    id varchar(64) not null comment '主键'
        primary key,
    tenant_id bigint null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) not null comment '合同id',
    contract_name varchar(100) null comment '合同名称',
    goods_name varchar(64) null comment '货物名称',
    project_name varchar(64) null comment '项目名称',
    reconciliation_ids json null comment '关联的对账单ids',
    type tinyint(1) null comment '类型 1.采购 2.销售',
    bill_type tinyint(1) null comment '开票类型 1：采购方发起 2:销售方发起',
    purchaser_input_id bigint unsigned null comment '录入采购方id',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_enterprise json null comment '采购方信息',
    seller_input_id bigint unsigned null comment '录入销售方id',
    seller_id bigint unsigned null comment '销售方id',
    seller_enterprise json null comment '销售方信息',
    bill_file_id bigint null comment '开票单据文件id',
    state tinyint(1) null comment '开票状态:1.草稿 2.待确认 3.确认中 4.已驳回 5.已完成',
    bill_date datetime null comment '开票日期',
    bill_amount decimal(16,2) null comment '开票总金额',
    tax_rate int null comment '税率',
    reasons varchar(200) null comment '驳回原因',
    remark varchar(255) null comment '备注',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    rec_amount decimal(16,2) null comment '对账金额',
    invalid_file_id bigint null comment '作废合同文件id',
    invalid_initiator tinyint null comment '作废发起方',
    purchase_invalid_time datetime null comment '采购方作废时间',
    seller_invalid_time datetime null comment '销售方作废时间',
    invalid_revoke_time datetime null comment '作废驳回时间',
    invalid_sign_state int null comment '作废签署状态',
    invalid_reason varchar(200) null comment '作废原因',
    invalid_revoke_reason varchar(200) null comment '驳回原因',
    project_long_id bigint null comment '项目长ID'
)
    comment '开票' row_format = DYNAMIC;

create table t_camera
(
    id bigint auto_increment comment '摄像头编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    camera_name varchar(64) null comment '摄像头名称',
    camera_type tinyint(1) default 1 null comment '设备类型 1：摄像头  2：硬盘录像机',
    serial_no varchar(64) null comment '序列号',
    regis_no varchar(64) null comment '注册号',
    warehouse_id varchar(32) null comment '仓库id',
    warehouse_name varchar(64) null comment '仓库名称',
    storage_id bigint null comment '库位id',
    storage_name varchar(64) null comment '库位名称',
    install_location varchar(64) null comment '安装位置',
    installer_id bigint null comment '安装人id',
    installer_name varchar(64) null comment '安装人名称',
    install_time datetime null comment '安装日期',
    state tinyint(1) default 0 null comment '当前状态;1:在线,0离线',
    channel_id varchar(64) null comment '通道id',
    channel_no tinyint null comment '通道号',
    channel_status tinyint null comment '通道状态，0：离线，1：在线，-1：设备未上报。',
    channel_type varchar(64) null comment '通道类型，10300:视频通道，10302：报警输入',
    is_use tinyint null comment '通道启用状态：0-禁用；1-启用',
    ipc_serial varchar(64) null comment 'NVR通道关联的IPC序列号',
    child_device_model varchar(64) null comment 'NVR通道关联的IPC型号',
    camera_id bigint null comment '关联设备id',
    relevance_camera_name varchar(64) null comment '关联设备名称',
    is_bind tinyint default 0 null comment '是否绑定设备 0：未绑定 1：已绑定',
    validate_code varchar(20) null comment '验证码/设备令牌',
    data_card varchar(20) null comment '数据卡号/ICCID',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '摄像头';

create table t_contract
(
    id varchar(64) not null comment '主键,合同编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    name varchar(64) not null comment '合同名称',
    project_id varchar(64) null comment '项目id',
    sign_mode tinyint null comment '签署方式: 1.线上/2.线下',
    sign_date datetime null comment '签订日期',
    start_date datetime null comment '起始日期',
    finish_date datetime null comment '截止日期',
    external_contract_number varchar(64) null comment '自定义合同编号',
    deposit decimal(15,2) null comment '履约保证金',
    file_id bigint null comment '合同文件id',
    goods_name varchar(255) null comment '货物名称',
    goods_info json null comment '货物信息',
    financing_product tinyint null comment '融资产品',
    borrowing_amount decimal(15,2) null comment '借款额度',
    validity_start_date datetime null comment '额度有效期起始日期',
    validity_end_date datetime null comment '额度有效期结束日期',
    borrowing_interest_rate_type tinyint null comment '借款利率方式：1.固定/2.浮动',
    borrowing_interest_rate_spread decimal(5,2) null comment '借款利率点差',
    interest_rate_adjustment_period int null comment '利率调整周期',
    interest_settlement_cycle_type tinyint null comment '结息周期方式：1.按月/2.按季/3.按年',
    interest_settlement_day int null comment '结息日',
    bank_id bigint null comment '资金方id',
    bank_enterprise json null comment '资金方信息',
    supplier_chain_id bigint null comment '供应链id',
    supplier_chain_enterprise json null comment '供应链信息',
    upstream_id bigint null comment '上游供应商往来企业id',
    upstream_suppliers_id bigint null comment '上游供应商id',
    upstream_suppliers_enterprise json null comment '上游供应商信息',
    downstream_id bigint null comment '下游采购方往来企业id',
    downstream_purchasers_id bigint null comment '下游采购方id',
    downstream_purchasers_enterprise json null comment '下游采购方信息',
    purchaser_banks json null comment '采购方银行账户',
    purchaser_pay_type tinyint null comment '付款方式：1.现金/2.银行转账/3.信用证/4.银行承兑汇票/5.商业承兑汇票',
    seller_banks json null comment '销售方银行账户',
    remark varchar(200) null comment '备注',
    reject_reason varchar(200) null comment '驳回原因',
    contract_type tinyint null comment '合同类型',
    settle_way tinyint null comment '结算方式',
    settle_period int null comment '账期',
    settle_start_with tinyint null comment '账期起始方式',
    pre_receipt_amount decimal(10,2) null comment '预收货款',
    rolling_payment decimal(15,2) null comment '滚动货款',
    rolling_payment_supplement_amount decimal(15,2) null comment '滚动货款补足金额',
    supplier_signer varchar(100) null comment '供应链签署人',
    supplier_signer_id bigint null comment '签署人id',
    creator_name varchar(100) null comment '创建者名字',
    relate_contract_ids json null comment '关联合同ids',
    loan_amount decimal(16,2) null comment '货款金额',
    refund_loan_amount decimal(16,2) null comment '已完成的货款退款金额',
    state int null comment '合同状态',
    sign_status tinyint null comment '签署状态',
    invalid_file_id bigint null comment '作废合同文件id',
    invalid_initiator tinyint null comment '作废发起方',
    purchase_invalid_time datetime null comment '采购方作废时间',
    seller_invalid_time datetime null comment '销售方作废时间',
    invalid_revoke_time datetime null comment '作废驳回时间',
    invalid_sign_state int null comment '作废签署状态',
    invalid_reason varchar(200) null comment '作废原因',
    invalid_revoke_reason varchar(200) null comment '驳回原因',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '合同';

create table t_contract_lock_record
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    type tinyint not null comment '认证类型',
    customer_id bigint unsigned not null comment '客户id',
    params varchar(2000) null comment '请求认证接口参数',
    auth_id varchar(55) null comment '契约锁认证请求id',
    auth_url varchar(512) null comment '契约锁认证链接',
    result varchar(600) null comment '契约锁认证返回结果',
    callback_params text null comment '契约锁认证回调参数',
    state tinyint null comment '契约锁认证记录状态 0：换绑手机号 1：有效 2：无效',
    source tinyint(1) default 1 null comment '数据来源 1：客户端 2：供应链',
    del tinyint default 0 not null comment '是否已删除',
    origin tinyint(1) null comment '来源',
    created_by bigint null comment '创建人',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint null comment '更新人',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '契约锁调用记录表';

create index idx_customer
    on t_contract_lock_record (customer_id);

create table t_contract_record
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    contract_id bigint null comment '契约锁合同id',
    correlation_id varchar(64) null comment '关联表id',
    type tinyint(1) null comment '业务类型',
    document_ids json null comment '合同文档id列表',
    message varchar(255) null comment '返回信息',
    del tinyint not null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '契约锁合同记录';

create table t_custom_security_setting_device
(
    id bigint unsigned auto_increment comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    customer_id bigint null comment '用户id',
    device_code varchar(64) null comment '设备编号',
    login_time datetime null comment '登录时间',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '客户端安全设置设备表';

create table t_customer
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    code varchar(10) not null comment '用户id',
    wx_open_id varchar(64) null comment '微信openId',
    union_id varchar(64) null comment '微信Unionid',
    nick_name varchar(64) not null comment '用户昵称',
    real_name varchar(25) null comment '真实姓名',
    real_name_pinyin varchar(128) null comment '真实姓名拼音',
    id_no varchar(18) null comment '身份证号',
    institution_name varchar(255) null comment '组织机构名称',
    unified_social_credit_code varchar(25) null comment '统一社会信用代码',
    legal_representative varchar(25) null comment '法定代表人',
    has_expired tinyint(1) null comment '企业认证是否已过期',
    mobile varchar(64) null comment '手机号码',
    password varchar(64) null comment '密码',
    avatar_file_id bigint null comment '关联文件表id',
    app_token varchar(64) null comment '企业认证令牌',
    app_secret varchar(64) null comment '企业认证秘钥',
    seal_admin tinyint(1) null comment '是否是印章管理员',
    apply_state tinyint(1) default 0 not null comment '机构认证状态',
    state tinyint(1) default 1 null comment '状态',
    system_name varchar(50) null comment '系统名称',
    system_logo bigint null comment '系统logo',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    created_type tinyint null comment '创建途径',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    updated_type tinyint null comment '修改途径',
    password_update_time datetime null comment '密码修改时间',
    receipt_sms tinyint(1) default 1 null comment '是否接收短信',
    roles json null comment '企业身份'
)
    comment '客户' row_format = DYNAMIC;

create table t_customer_bank
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    customer_id bigint unsigned not null comment '客户id',
    name varchar(255) not null comment '开户名称/开户人',
    account varchar(30) not null comment '银行账户/银行卡号',
    bank varchar(128) null comment '开户行',
    type tinyint not null comment '账户类型',
    biz_type tinyint(1) default 1 not null comment '业务类型',
    is_default tinyint default 0 not null comment '是否默认',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '客户-银行账户' row_format = DYNAMIC;

create index idx_customer
    on t_customer_bank (customer_id);

create table t_customer_enterprise
(
    id bigint unsigned auto_increment comment '主键id'
        primary key,
    tenant_id bigint unsigned null comment '租户id',
    main_account_id bigint unsigned not null comment '主账号id',
    sub_account_id bigint unsigned not null comment '子账号id',
    third_sub_account_id bigint null comment '子账号在第三方平台入驻企业的员工id',
    permissions json null comment '拥有权限',
    state tinyint(1) null comment '状态',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    account_state tinyint(1) default 1 null comment '账号状态',
    receipt_sms tinyint(1) default 1 null comment '是否接收短信'
)
    comment '子账号中间表';

create table t_customer_enterprise_certification
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    customer_id bigint null comment '用户id',
    tenant_id bigint unsigned null comment '企业定制id',
    company_id bigint null comment '契约锁返回的企业id',
    name varchar(255) null comment '企业名称',
    unified_social_credit_code varchar(25) null comment '统一社会信用代码',
    legal_representative varchar(25) null comment '法定代表人',
    message varchar(255) null comment '认证返回信息',
    status tinyint null comment '认证状态',
    auth_end_time varchar(20) null comment '授权有效期',
    state tinyint null comment '企业认证记录状态 0：换绑手机号 1：有效 2：无效',
    del tinyint default 0 not null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint null comment '创建人',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint null comment '更新人',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '客户企业认证记录表' row_format = DYNAMIC;

create table t_customer_invoice_header
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    customer_id bigint not null comment '客户id',
    header_type tinyint not null comment '抬头类型',
    invoice_type tinyint null comment '发票类型',
    name varchar(255) not null comment '发票抬头',
    taxpayer_no varchar(25) null comment '纳税人识别号',
    address varchar(255) null comment '地址',
    bank varchar(128) null comment '开户行',
    mobile varchar(64) null comment '电话',
    account varchar(20) null comment '账号',
    is_default tinyint not null comment '是否默认抬头',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '客户-发票抬头' row_format = DYNAMIC;

create index idx_customer
    on t_customer_invoice_header (customer_id);

create table t_customer_personal_certification
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    customer_id bigint unsigned not null comment '客户id',
    name varchar(55) not null comment '真实姓名',
    id_no varchar(18) not null comment '身份证号',
    status tinyint null comment '认证状态',
    message varchar(255) null comment '认证返回信息',
    state tinyint null comment '个人认证记录状态 0：换绑手机号 1：有效 2：无效',
    del tinyint default 0 not null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint null comment '创建人',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint null comment '更新人',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '个人认证记录表' row_format = DYNAMIC;

create table t_customer_receiving_address
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    customer_id bigint unsigned not null comment '客户id',
    institution_name varchar(55) null comment '机构名称',
    contacts varchar(35) not null comment '联系人',
    cellphone varchar(11) not null comment '手机号',
    area_code varchar(4) null comment '区号',
    landline varchar(8) null comment '座机号码',
    sub_landline varchar(4) null comment '座机分机号码',
    province varchar(10) not null comment '省份',
    city varchar(25) not null comment '市',
    district varchar(25) not null comment '区',
    detail_address varchar(255) not null comment '详细地址',
    zip_code varchar(15) null comment '邮政编码',
    is_default tinyint not null comment '是否默认',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '客户-我的地址' row_format = DYNAMIC;

create index idx_customer
    on t_customer_receiving_address (customer_id);

create table t_dealings_enterprise
(
    id                                        bigint unsigned auto_increment
        primary key,
    tenant_id                                 bigint unsigned                      null comment '企业定制id',
    enterprise_type                           tinyint(1)                           null comment '企业类型(1：注册企业,2：录入企业)',
    initiator                                 tinyint(1)                           null comment '类型(1：客户，2：供应链)',
    customer_id                               bigint unsigned                      null comment '客户id',
    institution_name                          varchar(255) null comment '客户企业名称',
    unified_social_credit_code                varchar(25) null comment '客户统一社会信用代码',
    legal_representative                      varchar(25) null comment '客户法定代表人',
    supplier_chain_id                         bigint unsigned                      null comment '供应链id',
    supplier_chain_institution_name           varchar(255) null comment '供应链企业名称',
    supplier_chain_unified_social_credit_code varchar(25) null comment '供应链统一社会信用代码',
    supplier_chain_legal_representative       varchar(25) null comment '供应链法定代表人',
    name                                      varchar(64) null comment '联系人',
    mobile                                    varchar(64) null comment '手机号码',
    state                                     tinyint(1)                           not null comment '状态(1：待确认,2：确认中,3：合作中)',
    remark                                    varchar(300) null comment '驳回原因',
    del                                       tinyint(1) default 0                 not null comment '删除标志 0存在 1删除',
    origin                                    tinyint(1)                           null comment '来源',
    created_by                                bigint unsigned                      null,
    created_time                              datetime default CURRENT_TIMESTAMP null,
    updated_by                                bigint unsigned                      null,
    updated_time                              datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
) comment '往来企业';

create table t_deliver_goods
(
    id varchar(20) not null comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(20) null comment '合同id',
    order_id varchar(20) null comment '订单id',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_business_id bigint null comment '采购方往来企业id',
    purchaser_enterprise json null comment '采购方信息',
    seller_id bigint unsigned null comment '销售方id',
    seller_business_id bigint null comment '销售方往来企业id',
    seller_enterprise json null comment '销售方信息',
    sign_type tinyint null comment '签署方式',
    delivery tinyint null comment '发货方式',
    goods_info json null comment '货物信息',
    goods_total_quantity decimal(32,2) null comment '货物合计数量',
    actual_goods_total_quantity decimal(32,2) null comment '实际发货总数量',
    delivery_date datetime null comment '发货日期',
    delivery_file_id bigint unsigned null comment '发货单据文件id',
    remark varchar(200) null comment '备注',
    status int null comment '状态',
    receive_way tinyint null comment '签收方式',
    receipt_weight decimal(15,2) null comment '签收重量',
    sign_receipt_id varchar(20) null comment '签收单id',
    invalid_file_id bigint null comment '作废合同文件id',
    invalid_time datetime null comment '作废时间',
    invalid_reason varchar(200) null comment '作废原因',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    supplier_signer varchar(100) null comment '供应链签署人',
    supplier_signer_id bigint null comment '签署人id',
    project_long_id bigint null comment '项目长ID'
)
    comment '发货单';

create table t_dept
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    parent_id bigint not null comment '上级部门id',
    code varchar(64) null comment '部门编码',
    name varchar(64) not null comment '部门名称',
    sort bigint not null comment '排序',
    del tinyint(1) default 0 not null comment '删除标志 0存在 1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null,
    created_time datetime default CURRENT_TIMESTAMP not null,
    updated_by bigint unsigned null,
    updated_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    constraint code_UNIQUE
        unique (code)
);

create table t_file
(
    id bigint unsigned auto_increment comment '编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    name varchar(1024) not null comment '文件名',
    path text not null comment '保存附件路径',
    type varchar(32) not null comment '附件后缀名',
    state tinyint(1) default 1 not null comment '状态',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '附件';

create table t_financing
(
    id varchar(20) not null comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    project_name varchar(50) null comment '项目名称',
    loan_contract_id varchar(20) null comment '借款合同',
    sale_contract_id varchar(20) null comment '销售合同',
    relate_contract_ids json null comment '关联合同ids',
    borrower_id bigint null comment '借款方id',
    borrower_enterprise json null comment '借款方信息',
    bank_enterprise json null comment '资金方信息',
    bank_user_id bigint null comment '银行端id',
    financing_product tinyint null comment '融资产品',
    loan_amount decimal(32,2) null comment '借款额度',
    loan_rate decimal(15,2) null comment '借款利率',
    loan_type tinyint null comment '借款类型',
    lpr_id bigint null comment 'lpr利率id',
    interest_rate_adjust_cycle int null comment '利率调整周期',
    interest_settlement_cycle_type tinyint null comment '结息周期类型',
    interest_settlement_date int null comment '结息日',
    limit_start_date datetime null comment '额度有效期开始日期',
    limit_end_date datetime null comment '额度有效期结束日期',
    lending_date datetime null comment '放款日期',
    entrusted_collection_account bigint null comment '受托收款账户',
    guarantee_information json null comment '担保信息',
    compound_interest tinyint null comment '是否存在复利',
    default_interest_info json null comment '罚息信息',
    remark varchar(500) null comment '备注',
    attachment json null comment '附件',
    supply_chain_date datetime null comment '供应链完成录入时间',
    funding_state int null comment '资金方状态',
    funding_confirm_date datetime null comment '资金方确认时间',
    borrower_state int null comment '借款方状态',
    borrower_confirm_date datetime null comment '借款方确认日期',
    released_amount decimal(32,2) null comment '已释放额度',
    loan_receipt_id varchar(64) null comment '生成的借据id',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '融资表';

create table t_goods
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    goods_name varchar(10) null comment '货物名称',
    unit_classification tinyint(1) null comment '单位分类',
    unit varchar(32) null comment '单位',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '货物名称表';

create table t_inbound
(
    id varchar(20) not null comment 'id'
        primary key,
    tenant_id bigint null comment '企业定制id',
    type tinyint null comment '归属类型：1 进销存  2 仓储',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(20) null comment '合同id',
    goods_name_id bigint null comment '货物名称id',
    sign_mode tinyint null comment '签署类型(线上/线下)',
    inbound_type int null comment '入库类型',
    receipt_id varchar(20) null comment '签收单id',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_enterprise json null comment '采购方信息',
    seller_id bigint unsigned null comment '销售方id',
    seller_enterprise json null comment '销售方信息',
    outbound_id varchar(20) null comment '出库单id',
    warehouse_id varchar(20) null comment '仓库id',
    storage_id bigint null comment '库位id',
    calc_inbound_amount tinyint null comment '是否计算入库金额',
    goods_info json null comment '货物信息',
    inbound_weight decimal(32,2) null comment '入库总重量',
    inbound_amount decimal(32,2) null comment '入库总金额',
    inbound_person bigint null comment '入库人',
    inbound_person_name varchar(32) null comment '入库人名称',
    inbound_file_id bigint null comment '入库单据id',
    detection tinyint null comment '是否有检测',
    detection_info json null comment '检测信息',
    inbound_detection_file_id bigint null comment '入库检测表',
    detector varchar(20) null comment '检测人',
    detection_date datetime null comment '检测日期',
    remark varchar(200) null comment '备注',
    inbound_pic json null comment '入库照片',
    state int null comment '入库状态',
    inbound_time datetime null comment '入库时间',
    sign_state int null comment '签署状态',
    reject_time datetime null comment '驳回时间',
    reject_reason varchar(200) null comment '驳回原因',
    sign_file_id bigint null comment '签署文件id',
    invalid_reason varchar(200) null comment '作废原因',
    invalid_revoke_reason varchar(200) null comment '作废驳回原因',
    invalid_file_id bigint null comment '作废文件',
    invalid_initiator tinyint null comment '作废发起方',
    purchase_invalid_time datetime null comment '采购方作废时间',
    seller_invalid_time datetime null comment '销售方作废时间',
    invalid_revoke_time datetime null comment '作废驳回时间',
    invalid_sign_state int null comment '作废签署状态',
    fee_standards decimal(16,2) null comment '收费标准',
    inbound_fee decimal(32,2) null comment '入库费',
    reviewer bigint null comment '审核人',
    review_time datetime null comment '审核时间',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '入库单';

create table t_inbound_detection
(
    id bigint auto_increment comment 'id'
        primary key,
    tenant_id bigint null,
    inbound_id varchar(20) null comment '入库id',
    type tinyint null comment '单据类型',
    project_id varchar(64) null comment '项目id',
    goods_id bigint null comment '货物id',
    contract_id varchar(20) null comment '合同id',
    purchaser_id bigint null comment '采购方id',
    purchaser_enterprise json null comment '采购方信息',
    seller_id bigint null comment '销售方id',
    seller_enterprise json null comment '销售方信息',
    warehouse_id varchar(20) null comment '仓库id',
    storage_id bigint null comment '库位id',
    inbound_weight decimal(32,2) null comment '入库总数量',
    inbound_time datetime null comment '入库时间',
    model varchar(20) null comment '规格型号',
    unit varchar(20) null comment '单位',
    detection_result varchar(50) null comment '检测结果',
    sampling_counter varchar(50) null comment '取样柜台/车间',
    remark varchar(200) null comment '备注',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '入库检测表';

create table t_institution_apply
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    customer_id bigint unsigned not null comment '用户id',
    institution_name varchar(255) null comment '组织机构名称',
    unified_social_credit_code varchar(25) not null comment '统一社会信用代码',
    business_license_file_id bigint unsigned null comment '营业执照文件id',
    power_attorney_file_id bigint null comment '委托授权书文件id',
    legal_representative varchar(25) null comment '法定代表人',
    approve_by bigint unsigned null comment '审核人id',
    approve_name varchar(64) null comment '审核人名称',
    approve_time datetime null comment '审核时间',
    remark varchar(200) null comment '备注',
    is_cancel tinyint(1) default 0 not null comment '是否取消',
    state tinyint(1) default 1 not null comment '状态 1：待审核，2：已驳回，3已通过',
    roles json null comment '企业身份',
    source tinyint(1) default 1 null comment '来源：1：客户端  2：供应链',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '组织机构认证';

create table t_inventory_details
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint null comment '企业定制id',
    type tinyint null comment '归属类型：1 进销存 2 仓储',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    bound_id varchar(20) null comment '出入库编号',
    bound_type tinyint null comment '出入库类型',
    bound_time datetime null comment '出入库时间',
    warehouse_id varchar(64) null comment '仓库id',
    warehouse_name varchar(64) null comment '仓库名称',
    storage_id bigint null comment '库位id',
    storage_name varchar(64) null comment '库位名称',
    warehouse_type tinyint(1) null comment '仓库类型',
    goods_name_id bigint null comment '货物名称id',
    goods_name varchar(64) null comment '货物名称',
    model varchar(64) null comment '规格/型号',
    unit_price decimal(32,2) null comment '单价',
    quantity decimal(32,2) null comment '数量/重量',
    amount decimal(32,2) null comment '金额',
    creator_id bigint null comment '制单人id',
    creator_name varchar(255) null comment '制单人名称',
    state tinyint(1) null comment '数据状态 0无效 1有效',
    business_type tinyint(1) null comment '业务类型',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '库存明细';

create table t_inventory_verify_report
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_enterprise json null comment '采购方信息',
    seller_id bigint unsigned null comment '销售方id',
    seller_enterprise json null comment '销售方信息',
    goods_name_id bigint null comment '货物名称id',
    goods_name varchar(64) null comment '货物名称',
    prior_day_average_price decimal(15,2) null comment '上日加权平均价',
    pledge_num decimal(15,2) null comment '质押财产最低数量/重量',
    shippable_quantity decimal(65,2) null comment '可出货数量/重量',
    total_prior_day_verify_quantity decimal(65,2) null comment '上日核库数量/重量',
    total_normal_inbound_quantity decimal(65,2) null comment '正常入库数量/重量',
    total_transfer_in_quantity decimal(65,2) null comment '调拨入库数量/重量',
    total_sale_outbound_quantity decimal(65,2) null comment '销售出库数量/重量',
    total_transfer_out_quantity decimal(65,2) null comment '调拨出库数量/重量',
    total_today_verify_quantity decimal(65,2) null comment '今日核库数量/重量',
    today_verify_value decimal(65,2) null comment '今日核库价值',
    total_inventory_quantity decimal(65,2) null comment '库存总数量',
    inventory_verify_date datetime null comment '核库日期',
    report_file_id bigint null comment '核库报表文件id',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '核库报表';

create table t_inventory_verify_report_detail
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id bigint null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    inventory_verify_report_id bigint null comment '核库报表id',
    warehouse_id varchar(64) null comment '仓库id',
    warehouse_name varchar(64) null comment '仓库名称',
    storage_id bigint null comment '库位id',
    storage_name varchar(64) null comment '库位名称',
    goods_name_id bigint null comment '货物名称id',
    goods_name varchar(64) null comment '货物名称',
    model varchar(64) null comment '规格/型号',
    prior_day_verify_quantity decimal(65,2) null comment '上日核库数量/重量',
    normal_inbound_quantity decimal(65,2) null comment '正常入库数量/重量',
    transfer_in_quantity decimal(65,2) null comment '调拨入库数量/重量',
    sale_outbound_quantity decimal(65,2) null comment '销售出库数量/重量',
    transfer_out_quantity decimal(65,2) null comment '调拨出库数量/重量',
    today_verify_quantity decimal(65,2) null comment '今日核库数量/重量',
    inventory_verify_date datetime null comment '核库日期',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '核库报表明细';

create table t_loan_receipt
(
    id varchar(64) not null comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    financing_id varchar(20) null comment '关联融资id',
    relate_contract_id varchar(64) null comment '关联合同id',
    relate_contract_name varchar(64) null comment '关联合同名称',
    borrower_contract_id varchar(64) null comment '借款合同id',
    borrower_contract_name varchar(64) null comment '借款合同名称',
    financing_product tinyint null comment '融资产品',
    borrower_id bigint null comment '借款方id',
    borrower_enterprise json null comment '借款方信息',
    bank_id bigint null comment '资金方id',
    bank_enterprise json null comment '资金方信息',
    limit_start_date datetime null comment '额度有效期开始日期',
    limit_end_date datetime null comment '额度有效期结束日期',
    loan_principal decimal(16,2) null comment '贷款本金',
    repaid_principal decimal(16,2) null comment '已还本金',
    remaining_principal decimal(16,2) null comment '剩余本金',
    repaid_interest decimal(16,2) null comment '已还利息',
    repaid_count int null comment '已还次数',
    state tinyint null comment '状态1.逾期、2.正常、3.结清',
    deduced_principal decimal(16,2) null comment '银行已扣款的本金',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '借据';

create table t_lpr_rate_config
(
    id bigint unsigned auto_increment comment '主键,编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    publish_date datetime not null comment '发布日期',
    rate_type tinyint(1) not null comment '利率类型 1:一年期',
    rate_value decimal(16,2) not null comment '利率值',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment 'LPR利率配置表';

create table t_message_settings
(
    id bigint unsigned auto_increment comment '主键,编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    open tinyint(1) null comment '是否开启;0:不开启,1开启',
    remind_times tinyint(1) null comment '提醒次数;1:提醒一次,2:提醒二次',
    first_time_type tinyint(1) null comment '第一次提醒类型;1:提前3天,2:提前7天,3:提前15天,4:自定义',
    first_time_date tinyint(1) null comment '第一次提醒输入日期',
    second_time_type tinyint(1) null comment '第二次提醒类型;1:提前3天,2:提前7天,3:提前15天,4:自定义',
    second_time_date tinyint(1) null comment '第二次提醒输入日期',
    remind_way tinyint(1) null comment '提醒方式;1:短信,2:站内消息',
    category tinyint(1) null comment '类别;1:合同到期,2:账期到期'
)
    comment '消息提醒设置表' row_format = DYNAMIC;

create table t_message_usermsg
(
    id bigint unsigned auto_increment comment '主键,编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    url varchar(64) null comment '跳转页面地址',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    title varchar(512) null comment '标题',
    content varchar(256) null comment '消息内容',
    customer_id bigint null comment '接收人id',
    send_time datetime default CURRENT_TIMESTAMP null comment '发送时间',
    message_state tinyint(1) null comment '消息阅读状态',
    type tinyint(1) null comment '消息类型',
    role tinyint null comment '接收人角色',
    detail_id varchar(64) null comment '详情界面跳转id',
    initiator tinyint null comment '是否业务发起方'
)
    comment '用户消息表' row_format = DYNAMIC;

create table t_operation_history
(
    id bigint auto_increment comment '主键ID'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    operator_id bigint null comment '操作人ID',
    biz_no varchar(512) null comment '业务ID',
    operation_module int null comment '操作模块',
    operation_action varchar(255) null comment '操作动作',
    operation_content json null comment '操作详细内容',
    disposes varchar(500) null comment '需处理的文本',
    del tinyint not null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '操作历史记录';

create table t_operation_log
(
    id bigint auto_increment comment '主键ID'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    operator_id bigint null comment '操作人ID',
    operator_type int null comment '操作人类型',
    biz_no varchar(512) null comment '业务ID',
    clazz varchar(255) null comment '所属类',
    method varchar(255) null comment '所属方法',
    operation_module int null comment '操作模块',
    operation_type int unsigned null comment '操作类型',
    content text null comment '内容',
    message_type tinyint null comment '消息的类型 1： 交易  2：用户',
    assignment_id json null comment '指派人员',
    type tinyint null comment '类型 1：银行端',
    permission varchar(50) null comment '权限',
    del tinyint not null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    high_light varchar(500) null comment '高亮文本'
)
    comment '操作日志表' row_format = DYNAMIC;

create table t_order
(
    id varchar(20) not null comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    project_name varchar(100) null comment '项目名称',
    contract_id varchar(20) null comment '合同',
    contract_name varchar(50) null comment '合同名称',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_business_id bigint null comment '采购方往来企业id',
    purchaser_enterprise json null comment '采购方信息',
    seller_id bigint unsigned null comment '销售方id',
    seller_business_id bigint null comment '销售方往来企业id',
    seller_enterprise json null comment '销售方信息',
    type tinyint(1) null comment '类型',
    sign_type tinyint(1) null comment '签署方式',
    logistics_way tinyint(1) null comment '物流方式',
    goods_info json null comment '货物信息',
    goods_name varchar(50) null comment '货物名称',
    unit varchar(10) null comment '单位',
    goods_total_quantity decimal(15,2) null comment '货物合计数量',
    goods_total_amount decimal(15,2) null comment '货物合计金额',
    apply_date datetime null comment '申请日期',
    voucher_file_id bigint unsigned null comment '凭证文件id',
    remark varchar(200) null comment '备注',
    status int null comment '状态',
    delivered_weight decimal(15,2) null comment '已发货重量',
    actual_delivered_weight decimal(15,2) null comment '实际发货重量',
    received_weight decimal(15,2) null comment '已签收重量',
    delivery_status int null comment '发货状态',
    take_delivery_status int null comment '提货状态',
    receive_status int null comment '签收状态',
    reconciliation_status int null comment '对账状态',
    invoice_status int null comment '开票状态',
    revoke_reason varchar(200) null comment '驳回原因',
    order_deposit decimal(32,2) null comment '订单保证金',
    advance_payment decimal(32,2) null comment '预付货款',
    settle_period int null comment '账期',
    settle_way tinyint(1) null comment '结算方式：1.先货后款/2.先款后货',
    related_buy_order_id varchar(20) null comment '关联的采购订单id',
    invalid_file_id bigint null comment '作废合同文件id',
    invalid_initiator tinyint null comment '作废发起方',
    purchase_invalid_time datetime null comment '采购方作废时间',
    seller_invalid_time datetime null comment '销售方作废时间',
    invalid_reason varchar(200) null comment '作废原因',
    invalid_revoke_reason varchar(200) null comment '驳回原因',
    invalid_revoke_time datetime null comment '作废驳回时间',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    supplier_signer varchar(100) null comment '供应链签署人',
    supplier_signer_id bigint null comment '签署人id',
    project_long_id bigint null comment '项目长ID'
)
    comment '订单';

create table t_outbound
(
    id varchar(20) not null comment 'id'
        primary key,
    tenant_id bigint null comment '企业定制id',
    type tinyint null comment '类型（进销存/仓储管理）',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(20) null comment '合同id',
    goods_name_id bigint null comment '货物名称id',
    receipt_id varchar(20) null comment '签收单id',
    outbound_type tinyint null comment '出库类型',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_enterprise json null comment '采购方信息',
    seller_id bigint unsigned null comment '销售方id',
    seller_enterprise json null comment '销售方信息',
    sign_type tinyint null comment '签署类型（线上/线下）',
    warehouse_id varchar(20) null comment '仓库id',
    storage_id bigint null comment '库位id',
    pledge_weight decimal(32,2) null comment '质押数量',
    can_sale_weight decimal(32,2) null comment '可销售数量',
    calc_outbound_amount tinyint null comment '是否计算出库金额',
    goods_info json null comment '货物信息',
    outbound_weight decimal(32,2) null comment '出库总重量',
    outbound_amount decimal(32,2) null comment '出库总金额',
    detection tinyint null comment '是否有检测',
    detection_info json null comment '检测信息',
    outbound_detection_file_id bigint null comment '出库检测表',
    outbound_file_id bigint null comment '出库单据id（线下）',
    detector varchar(20) null comment '检测人',
    detection_date datetime null comment '检测日期',
    remark varchar(200) null comment '备注',
    outbound_pic json null comment '出库照片',
    state int null comment '出库状态',
    outbound_person bigint null comment '出库人',
    outbound_person_name varchar(32) null comment '出库人名称',
    outbound_time datetime null comment '出库时间',
    sign_state int null comment '签署状态',
    reject_time datetime null comment '驳回时间',
    reject_reason varchar(200) null comment '驳回原因',
    sign_file_id bigint null comment '签署文件id',
    invalid_file_id bigint null comment '作废合同文件id',
    invalid_initiator tinyint null comment '作废发起方',
    purchase_invalid_time datetime null comment '采购方作废时间',
    seller_invalid_time datetime null comment '销售方作废时间',
    invalid_revoke_time datetime null comment '作废驳回时间',
    invalid_sign_state int null comment '作废签署状态',
    invalid_reason varchar(200) null comment '作废原因',
    invalid_revoke_reason varchar(200) null comment '驳回原因',
    fee_standards decimal(16,2) null comment '收费标准',
    outbound_fee decimal(32,2) null comment '出库费',
    reviewer bigint null comment '审核人',
    review_time datetime null comment '审核时间',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '出库单';

create table t_payment
(
    id varchar(64) not null comment '付款编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    type tinyint null comment '类型：1.采购/2.销售',
    project_id varchar(64) null comment '项目id',
    purchaser_id bigint null comment '采购方id',
    purchaser_business_id bigint null comment '采购方往来企业id',
    purchaser_enterprise json null comment '采购方信息',
    seller_id bigint null comment '销售方id',
    seller_business_id bigint null comment '销售方往来企业id',
    seller_enterprise json null comment '销售方信息',
    bank_id bigint null comment '资金方id',
    bank_enterprise json null comment '资金方信息',
    contract_id varchar(64) null comment '合同id',
    order_id varchar(64) null comment '订单id',
    loan_receipt_id varchar(64) null comment '借据id',
    accounts_id varchar(64) null comment '应收/应付id',
    reconciliation_id varchar(64) null comment '对账单id',
    change_remark varchar(32) null comment '变更事由',
    refund_id varchar(64) null comment '退款单id',
    refund_date datetime null comment '退款日期',
    cost_type tinyint null comment '费用类型：1.保证金/2.货款/3.服务费',
    amount decimal(15,2) null comment '金额',
    payment_date datetime null comment '付款日期',
    payment_way tinyint null comment '付款方式',
    payment_info json null comment '付款方式对应的详细信息',
    remark varchar(200) null comment '备注',
    reject_reason varchar(200) null comment '驳回原因',
    state tinyint null comment '状态：1.确认中 2.已完成 3.已驳回',
    is_deposit_transfer tinyint null comment '是否处于保证金转货款流程中 1是,0否',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default (now()) null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    payment_file_id json null,
    project_long_id bigint null comment '项目长ID'
)
    comment '付款';

create table t_payment_accounts
(
    id int auto_increment comment '主键id'
        primary key,
    payment_id varchar(64) not null comment '付款表id',
    accounts_id varchar(64) not null comment '应付款id',
    payment_info json null comment '付款方式对应的详细信息',
    reconciliation_id varchar(64) null comment '对账单id',
    amount decimal(15,2) not null comment '付款金额(元)',
    payment_way int null comment '付款方式',
    payment_date datetime null comment '付款日期',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    created_by bigint null comment '创建人',
    updated_by bigint null comment '修改人',
    updated_time datetime default CURRENT_TIMESTAMP null comment '修改时间',
    del int default 0 null comment '是否删除0否 1是',
    tenant_id bigint null,
    origin tinyint(1) null comment '客户来源'
)
    comment '付款表和应付款表中间关联表';

create index t_payment_accounts_payment_id_accounts_id_index
    on t_payment_accounts (payment_id, accounts_id);

create table t_payment_backup
(
    id varchar(64) null,
    tenant_id bigint unsigned null,
    type tinyint null,
    project_id bigint unsigned null,
    purchaser_id bigint null,
    purchaser_enterprise json null,
    seller_id bigint null,
    seller_enterprise json null,
    bank_id bigint null,
    bank_enterprise json null,
    contract_id varchar(64) null,
    order_id varchar(64) null,
    loan_receipt_id varchar(64) null,
    accounts_id varchar(500) null,
    reconciliation_id varchar(500) null,
    change_remark varchar(32) null,
    refund_id varchar(64) null,
    refund_date datetime null,
    cost_type tinyint null,
    amount decimal(15,2) null,
    payment_date datetime null,
    payment_way tinyint null,
    payment_info json null,
    payment_file_id varchar(500) null,
    remark varchar(200) null,
    reject_reason varchar(200) null,
    state tinyint null,
    del tinyint null,
    origin tinyint null,
    created_by bigint unsigned null,
    created_time datetime null,
    updated_by bigint unsigned null,
    updated_time datetime null
);

create table t_person
(
    id bigint unsigned auto_increment
        primary key,
    employee_id varchar(10) null comment '工号',
    tenant_id bigint unsigned null comment '企业定制id',
    code varchar(64) not null comment '员工编号',
    name varchar(64) null comment '人员姓名',
    mobile varchar(64) null comment '手机号',
    mail varchar(64) null comment '邮箱账号',
    position varchar(128) null comment '职位描述',
    del tinyint(1) default 0 null,
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by bigint unsigned null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    constraint code_UNIQUE
        unique (code)
)
    comment '员工';

create table t_person_dept
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    person_id bigint unsigned not null comment '用户id',
    dept_id bigint unsigned not null comment '部门id',
    main tinyint(1) default 0 null comment '是否是主部门',
    del tinyint(1) default 0 null,
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by bigint unsigned null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
);

create table t_platform_bank_account
(
    id bigint auto_increment
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    supplier_id bigint null comment '供应商id',
    account_id bigint unsigned null comment '账户id',
    name varchar(32) not null comment '开户名称',
    account varchar(32) not null comment '银行账户',
    opening_bank varchar(128) not null comment '开户行',
    is_default tinyint(1) default 0 not null comment '是否默认',
    state tinyint(1) default 1 not null comment '状态 1 启用 0 禁用',
    type tinyint(1) default 1 not null comment '类型 1:平台银行账户 2:供应商银行账户',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '平台银行账户' row_format = DYNAMIC;

create table t_pledge
(
    id varchar(64) not null comment '质押id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    project_name varchar(64) null comment '关联项目名称',
    contract_id varchar(64) null comment '关联合同',
    contract_name varchar(64) null comment '关联合同名称',
    goods_name varchar(32) null comment '货物名称',
    pledgor_id bigint null comment '出质人id',
    pledgor_enterprise json null comment '出质人信息',
    pledgee_id bigint null comment '质权人id',
    pledgee_enterprise json null comment '质权人信息',
    pledge_info json null comment '质押信息',
    effective_date datetime null comment '最新生效日期',
    file_id bigint null comment '附件',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '质押表';

create table t_project
(
    id varchar(64) not null
        primary key,
    tenant_id bigint null comment '企业定制id',
    code varchar(64) not null comment '年(2位)+月2位+自增数（3位）',
    name varchar(32) not null comment '项目名称',
    introduction varchar(200) null comment '项目介绍',
    supplier_is_recorded tinyint null comment '上游供应商是否自录数据',
    supplier_ids json null comment '上游供应商id',
    supplier_names json null comment '上游供应商名称',
    supplier_settle_way tinyint null comment '上游供应商结算方式',
    supplier_pre_payment decimal(15,2) null comment '上游供应商预付货款',
    supplier_pre_payment_ratio decimal(15,2) null comment '上游供应商预付货款比例',
    supplier_settle_period int null comment '上游供应商账期',
    customer_is_recorded tinyint null comment '下游采购方是否自录数据',
    customer_ids json null comment '下游采购方id',
    customer_names json null comment '下游采购方名称',
    customer_settle_way tinyint null comment '下游采购方结算方式',
    customer_pre_payment decimal(15,2) null comment '下游采购方预付货款',
    customer_pre_payment_ratio decimal(15,2) null comment '下游采购方预付货款比例',
    customer_settle_period int null comment '下游采购方账期',
    is_exist_storage tinyint null comment '是否存在仓储 1.是 0.否',
    supervising_unit varchar(255) null comment '监管单位',
    warehouse_ids json null comment '仓库ids',
    storage_ids json null comment '库位ids',
    out_condition tinyint null comment '出库条件',
    payment_ratio decimal(15,2) null comment '付款比例',
    remain_payment_period int null comment '余款账期',
    goods_id bigint null comment '货物id',
    goods_name varchar(255) null comment '货物名称',
    goods_unit varchar(32) null comment '货物单位',
    supervisor_id bigint null comment '项目负责人id',
    supervisor_name varchar(255) null comment '项目负责人名称',
    project_manager_ids json null comment '项目经理ids',
    project_manager_names json null comment '项目经理名称',
    is_apply_finish tinyint(1) null comment '是否申请了完结 1.是 0.否',
    state tinyint(1) default 1 not null comment '项目状态 1. 合同待签约 2.履约中 3.已结清',
    valid_begin_time datetime null comment '有效期开始日期',
    valid_end_time datetime null comment '有效期结束日期',
    project_max_limit decimal(15,2) null comment '项目最高限额',
    project_return_rate decimal(15,2) null comment '项目收益率',
    control_ratio_way tinyint null comment '控货比要求方式',
    inventory_control_ratio decimal(15,2) null comment '库存控货比',
    inventory_amount decimal(15,2) null comment '库存金额',
    formula varchar(255) null comment '下游可提货余额公式',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '项目';

create table t_project_inception
(
    id varchar(64) not null comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    goods_id bigint null comment '货物id',
    goods_name varchar(255) null comment '货物名称',
    total_receipt_amount decimal(65,2) null comment '货款收入总金额',
    total_reconciled_sales_amount decimal(65,2) null comment '销售对账总金额',
    total_reconciled_sales_quantity decimal(65,2) null comment '销售对账数量/重量',
    total_invoiced_sales_amount decimal(65,2) null comment '销售开票总金额',
    total_payment_amount decimal(65,2) null comment '货款支出总金额',
    total_reconciled_purchase_amount decimal(65,2) null comment '采购对账总金额',
    total_reconciled_purchase_quantity decimal(65,2) null comment '采购对账数量/重量',
    total_invoiced_purchase_amount decimal(65,2) null comment '采购开票总金额',
    operator_id bigint null comment '录入人id',
    operator_name varchar(255) null comment '录入人名称',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '项目期初';

create table t_project_inception_detail
(
    id bigint unsigned auto_increment comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    project_inception_id varchar(64) null comment '项目期初id',
    type int null comment '期初明细类型1.采购期初、2.销售期初',
    contract_id varchar(64) null comment '合同id',
    purchaser_id bigint null comment '采购方id',
    seller_id bigint null comment '销售方id',
    payment_amount decimal(15,2) null comment '货款收入/支出总金额',
    reconciled_amount decimal(15,2) null comment '对账总金额',
    reconciled_quantity decimal(15,2) null comment '对账总数量/重量',
    invoiced_amount decimal(15,2) null comment '开票总金额',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '项目期初明细';

create table t_project_item
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    item tinyint null comment '增减项',
    direction tinyint null comment '加减方向',
    amount decimal(10,2) null comment '金额',
    remark varchar(32) null comment '备注',
    file_id bigint null comment '附件id',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '项目可提货额公式项';

create table t_quota_change
(
    id varchar(64) not null comment '付款编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    initiator tinyint not null comment '发起类型',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_business_id bigint null comment '采购方往来企业id',
    purchaser_enterprise json not null comment '采购方信息',
    seller_id bigint unsigned null comment '销售方id',
    seller_business_id bigint null comment '销售方往来企业id',
    seller_enterprise json not null comment '销售方信息',
    contract_id varchar(64) not null comment '合同id',
    contract_name varchar(64) not null comment '合同名称',
    change_type tinyint not null comment '变更方向: 1.增加/2.减少',
    type tinyint default 2 null comment '变更类型：1.采购变更2.销售变更',
    cost_type tinyint not null comment '费用类型：1.贷款/2.保证金',
    apply_amount decimal(15,2) not null comment '申请金额',
    apply_date datetime not null comment '申请日期',
    apply_remark varchar(255) not null comment '申请事由',
    apply_file_id bigint null comment '凭证文件id',
    reject_reason varchar(255) null comment '驳回原因',
    state tinyint default 1 not null comment '状态：1.确认中 2.已完成 3.已驳回',
    del tinyint(1) default 0 not null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '额度变更';

create table t_reconciliation
(
    id varchar(64) not null comment '主键'
        primary key,
    tenant_id bigint null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) not null comment '合同id',
    contract_name varchar(64) null comment '合同名称',
    goods_name varchar(64) null comment '货物名称',
    receipt_ids json null comment '关联的签收单ids',
    project_name varchar(64) null comment '项目名称',
    type tinyint null comment '类型 1.采购 2.销售',
    purchaser_business_id bigint null comment '采购方往来企业id',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_enterprise json null comment '采购方信息',
    seller_business_id bigint null comment '销售方往来企业id',
    seller_id bigint unsigned null comment '销售方id',
    seller_enterprise json null comment '销售方信息',
    reconciliation_file_id bigint null comment '对账单据文件id',
    sign_type tinyint null comment '签署方式 1线上 2.线下',
    sign_status tinyint null comment '签署状态',
    invoice_state tinyint null comment '开票状态： 0.未开票 1.开票中 2.已开票',
    state tinyint null comment '对账状态:1草稿 2.待确认 3.确认中 4.签署中 5.已完成 6.已驳回',
    settle_period int null comment '账期',
    pre_settle_way tinyint null comment '预结算方式',
    pre_payment_value decimal(16,2) null comment '预付比例/预付金额',
    settlement_payment decimal(16,2) null comment '结算预付款',
    reconciliation_date datetime null comment '对账日期',
    reconciliation_weight decimal(16,2) null comment '对账重量/数量',
    reconciliation_amount decimal(16,2) null comment '对账金额/实际对账金额',
    actual_amount decimal(16,2) null comment '实际应收/应付金额',
    unbilled_amount decimal(16,2) null comment '未开票金额',
    billed_amount decimal(16,2) null comment '已完成开票金额',
    remark varchar(255) null comment '备注',
    reject_reason varchar(255) null comment '驳回原因',
    initiator int null comment '发起方',
    invalid_file_id bigint null comment '作废合同文件id',
    invalid_initiator tinyint null comment '作废发起方',
    purchase_invalid_time datetime null comment '采购方作废时间',
    seller_invalid_time datetime null comment '销售方作废时间',
    invalid_revoke_time datetime null comment '作废驳回时间',
    invalid_sign_state int null comment '作废签署状态',
    invalid_reason varchar(200) null comment '作废原因',
    invalid_revoke_reason varchar(200) null comment '驳回原因',
    delivered_info json null comment '发货快照数据',
    pre_reconciliation_file_id bigint null comment '预对账单据文件id',
    pre_sign_type tinyint null comment '预对账签署方式 1线上 2.线下',
    pre_sign_status tinyint null comment '预对账签署状态',
    pre_invalid_file_id bigint null comment '预对账作废合同文件id',
    pre_invalid_initiator tinyint null comment '预对账作废发起方',
    pre_purchase_invalid_time datetime null comment '预对账采购方作废时间',
    pre_seller_invalid_time datetime null comment '预对账销售方作废时间',
    pre_invalid_revoke_time datetime null comment '预对账作废驳回时间',
    pre_invalid_sign_state int null comment '预对账作废签署状态',
    pre_invalid_reason varchar(200) null comment '预对账作废原因',
    pre_invalid_revoke_reason varchar(200) null comment '预对账驳回原因',
    pre_reconciliation_date datetime null comment '预对账日期',
    pre_reconciliation_weight decimal(16,2) null comment '预对账重量/数量',
    pre_reconciliation_amount decimal(16,2) null comment '预对账金额',
    is_pre_reconciliation tinyint default 0 null comment '是否存在预对账0.否 1.是',
    is_conduct_reconciliation tinyint null comment '是否进入对账阶段 0.否 1.是',
    pre_supplier_signer varchar(100) null comment '预对账供应链签署人',
    pre_supplier_signer_id bigint null comment '预对账签署人id',
    pre_initiator int null comment '预对账发起方',
    pre_delivered_info json null comment '预对账发货快照数据',
    is_record tinyint null comment '销售对账的采购方/采购对账的销售方 是否录入企业',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    order_ids json null comment '关联的订单ids',
    order_deposit decimal(16,2) null comment '订单保证金',
    deposit_transfer_amount tinyint null comment '保证金是否转货款  0.不转 1.转',
    supplier_signer varchar(100) null comment '供应链签署人',
    supplier_signer_id bigint null comment '签署人id',
    project_long_id bigint null comment '项目长ID'
)
    comment '对账' row_format = DYNAMIC;

create table t_refund
(
    id varchar(64) not null comment '退款编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    initiator tinyint(1) default 1 null comment '发起方',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    purchaser_business_id bigint null comment '收款方往来企业id',
    purchaser_id bigint null comment '收款方id',
    purchaser_enterprise json null comment '收款方信息',
    seller_business_id bigint null comment '退款方往来企业id',
    seller_id bigint null comment '退款方id',
    seller_enterprise json null comment '退款方信息',
    payment_id varchar(64) null comment '收款id',
    cost_type tinyint null comment '费用类型',
    amount decimal(15,2) null comment '金额',
    payment_date datetime null comment '收款日期',
    refund_date datetime null comment '退款日期',
    refund_way tinyint null comment '退款方式',
    refund_info json null comment '退款方式对应的详细信息',
    refund_file_id bigint null comment '退款凭证文件id',
    refund_reason varchar(200) null comment '退款原因',
    reject_reason varchar(200) null comment '驳回原因',
    state tinyint null comment '状态：1.确认中 2.已完成 3.已驳回',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '退款';

create table t_regular_customers
(
    id bigint unsigned auto_increment comment 'id'
        primary key,
    tenant_id varchar(64) null comment '企业定制id',
    institution_name varchar(255) null comment '企业名称',
    legal_representative varchar(25) null comment '法定代表人',
    unified_social_credit_code varchar(25) null comment '社会统一信用代码',
    state tinyint(1) default 0 null comment '状态 0：启用 1:禁用',
    customer_id mediumtext null comment '关联客户id',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '往来客户';

create table t_repayment
(
    id varchar(64) not null comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    relate_contract_id varchar(64) null comment '关联合同id',
    financing_products json null comment '融资产品枚举ids',
    borrower_id bigint null comment '借款方id',
    borrower_enterprise json null comment '借款方信息',
    repayment_amount decimal(16,2) null comment '还款金额',
    repayment_principal decimal(16,2) null comment '还款本金',
    repayment_interest decimal(16,2) null comment '还款利息',
    expected_released_amount decimal(16,2) null comment '预计释放额度',
    actual_released_amount decimal(16,2) null comment '实际释放额度',
    deduction_date datetime null comment '扣款日期',
    state tinyint null comment '状态1.待扣款、2.扣款完成、3.额度已释放',
    remark varchar(200) null comment '备注',
    reject_reason varchar(200) null comment '驳回原因',
    apply_time datetime null comment '还款放货申请时间',
    reject_time datetime null comment '资金方驳回时间',
    confirm_deduction_time datetime null comment '资金方确认扣款时间',
    release_time datetime null comment '供应链释放可提货额度时间',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '还款';

create table t_repayment_detail
(
    id bigint unsigned auto_increment comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    repayment_id varchar(64) null comment '还款id',
    loan_receipt_id varchar(64) null comment '单据id',
    relate_contract_id varchar(64) null comment '关联合同id',
    relate_contract_name varchar(64) null comment '关联合同名称',
    borrower_contract_id varchar(64) null comment '借款合同id',
    borrower_contract_name varchar(64) null comment '借款合同名称',
    financing_product tinyint null comment '融资产品',
    borrower_id bigint null comment '借款方id',
    borrower_enterprise json null comment '借款方信息',
    bank_id bigint null comment '资金方id',
    bank_enterprise json null comment '资金方信息',
    limit_start_date datetime null comment '额度有效期开始日期',
    limit_end_date datetime null comment '额度有效期结束日期',
    remain_loan_principal decimal(16,2) null comment '贷款剩余本金',
    repayment_method tinyint null comment '还款方式',
    expected_repayment_principal decimal(16,2) null comment '预计还款本金',
    expected_repayment_interest decimal(16,2) null comment '预计还款利息',
    this_repayment_principal decimal(16,2) null comment '本次还款本金',
    this_repayment_interest decimal(16,2) null comment '本次还款利息',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '还款明细表';

create table t_report_zip_record
(
    id bigint auto_increment comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    warehouse_id varchar(20) null comment '仓库id',
    storage_id bigint null comment '库位名称',
    goods_id bigint null comment '货物id',
    zip_types json null comment '打包类型',
    zip_data_volume int null comment '打包数据量',
    zip_date datetime null comment '打包日期',
    downloading tinyint null comment '是否正在下载',
    file_id bigint null comment '文件id',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '报表打包表';

create table t_role
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    name varchar(64) not null comment '角色名称',
    remark varchar(128) null comment '角色描述',
    permissions json not null comment '角色拥有权限列表',
    del tinyint(1) default 0 not null comment '删除标志 0存在 1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by bigint unsigned null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
);

create table t_seal
(
    id bigint unsigned auto_increment comment '主键id'
        primary key,
    tenant_id bigint unsigned null comment '租户id',
    main_account_id bigint not null comment '主账号id',
    third_seal_id varchar(64) null comment '第三方印章id',
    name varchar(50) not null comment '印章名称',
    style tinyint(1) not null comment '印章样式',
    foot varchar(50) null comment '下方横排文字',
    info_code varchar(50) null comment '信息编码,即印章上的数字',
    surround_text varchar(255) null comment '环绕文字',
    correlation_business json null comment '关联业务',
    file_id bigint unsigned null comment '印章图片文件id',
    state tinyint default 1 not null comment '状态',
    module tinyint default 1 null comment '模块',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '印章管理';

create table t_service_fee
(
    id varchar(64) not null comment '服务费编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    purchaser_id bigint null comment '采购方id',
    purchaser_enterprise json null comment '采购方信息',
    seller_id bigint null comment '销售方id',
    seller_enterprise json null comment '销售方信息',
    cost_type tinyint default 1 null comment '费用类型：1.服务费',
    amount decimal(15,2) null comment '金额',
    service_month datetime null comment '服务月份',
    payment_date datetime null comment '付款日期',
    payment_way tinyint default 2 null comment '付款方式：2.银行转账',
    payment_info json null comment '付款方式对应的详细信息',
    payment_file_id bigint null comment '付款凭证文件id',
    remark varchar(200) null comment '备注',
    reject_reason varchar(200) null comment '驳回原因',
    state tinyint null comment '状态：1.确认中 2.已完成 3.已驳回',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '服务费' row_format = DYNAMIC;

create table t_sign_receipt
(
    id varchar(20) not null comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    project_name varchar(100) null comment '项目名称',
    contract_id varchar(20) null comment '合同',
    contract_name varchar(50) null comment '合同名称',
    purchaser_input_id bigint unsigned null comment '录入采购方id',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_enterprise json null comment '采购方信息',
    seller_input_id bigint unsigned null comment '录入销售方id',
    seller_id bigint unsigned null comment '销售方id',
    seller_enterprise json null comment '销售方信息',
    related_deliver_goods_ids json null comment '关联的发货单id',
    related_order_ids json null comment '关联的订单id',
    receipt_weight decimal(15,2) null comment '签收重量',
    sign_confirm_date datetime null comment '签收确认日期',
    sign_receipt_file_id bigint unsigned null comment '签收单据文件id',
    remark varchar(200) null comment '备注',
    status int null comment '状态',
    type tinyint(1) null comment '单据类型 1：采购 2销售',
    sign_status int null comment '签署状态',
    sign_type tinyint null comment '签署方式',
    reconciliation_status int null comment '对账状态',
    invoice_state tinyint default 1 not null comment '开票状态: 1.未开票 2.开票中 3.已开票',
    initiator int null comment '发起方',
    revoke_reason varchar(200) null comment '驳回原因',
    invalid_file_id bigint null comment '作废合同文件id',
    invalid_initiator tinyint null comment '作废发起方',
    purchase_invalid_time datetime null comment '采购方作废时间',
    seller_invalid_time datetime null comment '销售方作废时间',
    invalid_revoke_time datetime null comment '作废驳回时间',
    invalid_sign_state int null comment '作废签署状态',
    invalid_reason varchar(200) null comment '作废原因',
    invalid_revoke_reason varchar(200) null comment '驳回原因',
    delivered_info json null comment '发货快照数据',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    supplier_signer varchar(100) null comment '供应链签署人',
    supplier_signer_id bigint null comment '签署人id',
    project_long_id bigint null comment '项目长ID'
);

create table t_signer_settings
(
    id bigint unsigned auto_increment comment '主键,编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    bill_type varchar(100) null comment '单据类型',
    signer json null comment '签署人id数组'
)
    comment '签署配置表' row_format = DYNAMIC;

create table t_stock_check
(
    id varchar(64) not null comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    module tinyint null comment '所属模块 1 进销存  2 仓储监管',
    purchaser_business_id bigint null comment '采购方往来企业id',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_enterprise json null comment '采购方信息',
    seller_business_id bigint null comment '销售方往来企业id',
    seller_id bigint unsigned null comment '销售方id',
    seller_enterprise json null comment '销售方信息',
    warehouse_id varchar(64) null comment '仓库id',
    warehouse_name varchar(64) null comment '仓库名称',
    storage_id bigint null comment '库位id',
    storage_name varchar(64) null comment '库位名称',
    type tinyint null comment '抽检类型1.每日抽检、2.定期抽检、3.临时抽检、4.国检',
    check_date datetime null comment '抽检时间',
    check_user_id bigint null comment '抽检人id',
    check_user_name varchar(255) null comment '抽检人名称',
    retrieve_date datetime null comment '取回日期',
    goods_info json null comment '货物信息',
    sign_type tinyint null comment '签署方式 1线上 2.线下',
    sign_status tinyint null comment '签署状态',
    state tinyint null comment '状态',
    total_check_quantity decimal(65,2) null comment '抽检总数量',
    tester varchar(64) null comment '检测人',
    check_result tinyint null comment '抽检结果',
    check_file_id bigint null comment '检测单据文件id',
    check_photos_ids json null comment '抽检照片ids',
    remark varchar(255) null comment '备注',
    reject_reason varchar(255) null comment '驳回原因',
    initiator int null comment '发起方',
    invalid_file_id bigint null comment '作废合同文件id',
    invalid_initiator tinyint null comment '作废发起方',
    purchase_invalid_time datetime null comment '采购方作废时间',
    seller_invalid_time datetime null comment '销售方作废时间',
    invalid_revoke_time datetime null comment '作废驳回时间',
    invalid_sign_state int null comment '作废签署状态',
    invalid_reason varchar(200) null comment '作废原因',
    invalid_revoke_reason varchar(200) null comment '驳回原因',
    supplier_signer varchar(100) null comment '供应链签署人',
    supplier_signer_id bigint null comment '签署人id',
    checker_id bigint null comment '抽检员id',
    checker_name varchar(255) null comment '抽检员名称',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '库存抽检';

create table t_stock_check_detail
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id bigint null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    stock_check_id varchar(64) null comment '库存抽检id',
    goods_name varchar(64) null comment '货物名称',
    model varchar(64) null comment '规格/型号',
    unit varchar(64) null comment '单位',
    check_location varchar(64) null comment '取样地点',
    check_quantity decimal(15,2) null comment '抽检数量/重量',
    result varchar(32) null comment '检测结果',
    remark varchar(64) null comment '备注',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '库存抽检货物信息明细';

create table t_stock_contract
(
    id varchar(64) not null comment '主键,合同编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    name varchar(64) not null comment '合同名称',
    project_id varchar(64) null comment '项目id',
    sign_mode tinyint null comment '签署方式: 1.线上/2.线下',
    contract_type tinyint null comment '合同类型',
    warehouse_ids json null comment '仓库ids',
    storage_ids json null comment '库位ids',
    sign_date datetime null comment '签订日期',
    start_date datetime null comment '起始日期',
    finish_date datetime null comment '截止日期',
    external_contract_number varchar(64) null comment '自定义合同编号',
    file_id bigint null comment '合同文件id',
    goods_id bigint null comment '货物id',
    goods_name varchar(255) null comment '货物名称',
    goods_unit varchar(32) null comment '货物单位',
    stock_id bigint null comment '仓储方id',
    stock_enterprise json null comment '仓储方信息',
    customer_id bigint null comment '存货方id',
    customer_enterprise json null comment '存货方信息',
    remark varchar(200) null comment '备注',
    free_storage_days int null comment '免费存储天数',
    reject_reason varchar(200) null comment '驳回原因',
    supplier_signer varchar(100) null comment '供应链签署人',
    supplier_signer_id bigint null comment '签署人id',
    creator_name varchar(100) null comment '创建者名字',
    state int null comment '合同状态',
    sign_status tinyint null comment '签署状态',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '仓储监管合同';

create table t_stock_contract_fees
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    fee_item tinyint null comment '收费项',
    unit varchar(64) null comment '单位',
    rates decimal(10,2) null comment '收费标准',
    remark varchar(32) null comment '备注',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '仓储监管合同收费标准';

create table t_stock_project
(
    id varchar(64) not null
        primary key,
    tenant_id bigint null comment '企业定制id',
    code varchar(64) not null comment '年(2位)+月2位+自增数（3位）',
    name varchar(32) not null comment '项目名称',
    introduction varchar(200) null comment '项目介绍',
    customer_ids json null comment '存货方id',
    customer_names json null comment '存货方名称',
    goods_id bigint null comment '货物id',
    goods_name varchar(255) null comment '货物名称',
    goods_unit varchar(32) null comment '货物单位',
    supervisor_id bigint null comment '项目负责人id',
    supervisor_name varchar(255) null comment '项目负责人名称',
    project_manager_ids json null comment '项目经理ids',
    project_manager_names json null comment '项目经理名称',
    is_apply_finish tinyint(1) null comment '是否申请了完结 1.是 0.否',
    state tinyint(1) default 1 not null comment '项目状态 1. 合同待签约 2.履约中 3.已结清',
    valid_begin_time datetime null comment '有效期开始日期',
    valid_end_time datetime null comment '有效期结束日期',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '仓储监管项目';

create table t_stocktaking
(
    id varchar(64) not null comment 'id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    module tinyint null comment '所属模块 1 进销存  2 仓储监管',
    purchaser_business_id bigint null comment '采购方往来企业id',
    purchaser_id bigint unsigned null comment '采购方id',
    purchaser_enterprise json null comment '采购方信息',
    seller_business_id bigint null comment '销售方往来企业id',
    seller_id bigint unsigned null comment '销售方id',
    seller_enterprise json null comment '销售方信息',
    goods_info json null comment '盘点货物信息',
    warehouse_id varchar(64) null comment '仓库id',
    warehouse_name varchar(64) null comment '仓库名称',
    warehouse_subject varchar(64) null comment '仓库所属主体',
    storage_id bigint null comment '库位id',
    storage_name varchar(64) null comment '库位名称',
    type tinyint null comment '盘点类型1.每日盘点、2.定期盘点、3.临时盘点、4.抽查盘点',
    stocktaking_date datetime null comment '盘点时间',
    stocktaking_user_id bigint null comment '盘点人id',
    stocktaking_user_name varchar(255) null comment '盘点人名称',
    sign_type tinyint null comment '签署方式 1线上 2.线下',
    sign_status tinyint null comment '签署状态',
    state tinyint null comment '状态',
    is_display tinyint null comment '是否显示盈亏',
    min_pledged_assets_quantity decimal(65,2) null comment '质押财产最低数量',
    total_stocktaking_quantity decimal(65,2) null comment '盘点总数量/重量',
    total_profit_loss_quantity decimal(65,2) null comment '盈亏数量/重量',
    total_net_weight decimal(65,2) null comment '净重合计',
    stocktaking_file_id bigint null comment '盘点单据文件id',
    stocktaking_photos_ids json null comment '盘点照片ids',
    remark varchar(255) null comment '备注',
    reject_reason varchar(255) null comment '驳回原因',
    initiator int null comment '发起方',
    invalid_file_id bigint null comment '作废合同文件id',
    invalid_initiator tinyint null comment '作废发起方',
    purchase_invalid_time datetime null comment '采购方作废时间',
    seller_invalid_time datetime null comment '销售方作废时间',
    invalid_revoke_time datetime null comment '作废驳回时间',
    invalid_sign_state int null comment '作废签署状态',
    invalid_reason varchar(200) null comment '作废原因',
    invalid_revoke_reason varchar(200) null comment '驳回原因',
    supplier_signer varchar(100) null comment '供应链签署人',
    supplier_signer_id bigint null comment '签署人id',
    creator_id bigint null comment '制单人id',
    creator_name varchar(255) null comment '制单人名称',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '盘点';

create table t_stocktaking_detail
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id bigint null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    stocktaking_id varchar(64) null comment '盘点id',
    goods_name varchar(64) null comment '货物名称',
    model varchar(64) null comment '规格/型号',
    unit varchar(64) null comment '单位',
    book_quantity decimal(65,2) null comment '账面数量/重量',
    stocktaking_quantity decimal(65,2) null comment '盘点数量/重量',
    profit_loss_quantity decimal(15,2) null comment '盈亏数量/重量',
    disc_weight decimal(15,2) null comment '盘重',
    gross_weight decimal(15,2) null comment '毛重',
    net_weight decimal(15,2) null comment '净重',
    remark varchar(64) null comment '备注',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '盘点货物信息明细';

create table t_storage
(
    id bigint unsigned auto_increment comment '库位id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    warehouse_id varchar(10) null comment '仓库id',
    name varchar(64) null comment '库位名称',
    address varchar(64) null comment '库位地址',
    supervisor varchar(64) null comment '负责人',
    mobile varchar(64) null comment '联系电话',
    is_used tinyint(1) default 0 null comment '是否被使用 0：未使用 1：已使用',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '库位表';

create table t_storage_fee
(
    id bigint auto_increment comment 'id'
        primary key,
    project_id varchar(64) null comment '项目id',
    contract_id varchar(20) null comment '合同id',
    business_date datetime null comment '业务日期',
    goods_id bigint null comment '货物id',
    weight decimal(32,2) null comment '重量',
    fee_type tinyint null comment '费用类型',
    fee_standards decimal(32,2) null comment '收费标准',
    unit varchar(20) null comment '单位',
    amount decimal(32,2) null comment '金额',
    warehouse_id varchar(20) null comment '仓库id',
    storage_id bigint null comment '库位id',
    relate_id varchar(20) null comment '关联id',
    del tinyint(1) default 0 not null comment '删除标志0 存在，1删除',
    tenant_id bigint null comment '企业定制id',
    origin tinyint(1) null comment '客户来源',
    created_by bigint null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '仓储费用表';

create table t_storage_inception
(
    id varchar(64) not null comment '仓储初期id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    project_name varchar(64) null comment '关联项目名称',
    goods_name varchar(32) null comment '货物名称',
    inputer_id bigint null comment '录入人id',
    inputer_name varchar(64) null comment '录入人名称',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '仓储初期';

create table t_storage_inception_inbound_detail
(
    id bigint unsigned auto_increment comment 'id'
        primary key,
    tenant_id varchar(64) null comment '企业定制id',
    storage_inception_id varchar(32) null comment '仓储期初id',
    project_id varchar(64) null comment '项目id',
    project_name varchar(64) null comment '关联项目名称',
    contract_id varchar(64) null comment '关联合同',
    contract_name varchar(64) null comment '关联合同名称',
    goods_name varchar(32) null comment '货物名称',
    warehouse_id varchar(32) null comment '仓库id',
    warehouse_name varchar(64) null comment '仓库名称',
    storage_id bigint null comment '库位id',
    storage_name varchar(64) null comment '库位名称',
    model varchar(64) null comment '规格型号',
    quantity decimal(32,2) null comment '入库数量/重量',
    unit varchar(64) null comment '单位',
    amount decimal(32,2) null comment '入库金额',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '仓储初期入库明细';

create table t_storage_inception_outbound_detail
(
    id bigint unsigned auto_increment comment 'id'
        primary key,
    tenant_id varchar(64) null comment '企业定制id',
    storage_inception_id varchar(32) null comment '仓储期初id',
    project_id varchar(64) null comment '项目id',
    project_name varchar(64) null comment '关联项目名称',
    contract_id varchar(64) null comment '关联合同',
    contract_name varchar(64) null comment '关联合同名称',
    goods_name varchar(32) null comment '货物名称',
    warehouse_id varchar(32) null comment '仓库id',
    warehouse_name varchar(64) null comment '仓库名称',
    storage_id bigint null comment '库位id',
    storage_name varchar(64) null comment '库位名称',
    model varchar(64) null comment '规格型号',
    quantity decimal(32,2) null comment '出库数量/重量',
    unit varchar(64) null comment '单位',
    amount decimal(32,2) null comment '出库金额',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '仓储初期出库明细';

create table t_supervision_storage_inception
(
    id varchar(64) not null comment '仓储初期id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    project_name varchar(64) null comment '关联项目名称',
    goods_name varchar(32) null comment '货物名称',
    unit varchar(64) null comment '单位',
    inputer_id bigint null comment '录入人id',
    inputer_name varchar(64) null comment '录入人名称',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '仓储监管-仓储初期';

create table t_supervision_storage_inception_detail
(
    id bigint unsigned auto_increment comment 'id'
        primary key,
    tenant_id varchar(64) null comment '企业定制id',
    supervision_storage_inception_id varchar(32) null comment '仓储期初id',
    project_id varchar(64) null comment '项目id',
    project_name varchar(64) null comment '关联项目名称',
    contract_id varchar(64) null comment '关联合同',
    contract_name varchar(64) null comment '关联合同名称',
    goods_name varchar(32) null comment '货物名称',
    warehouse_id varchar(32) null comment '仓库id',
    warehouse_name varchar(64) null comment '仓库名称',
    storage_id bigint null comment '库位id',
    storage_name varchar(64) null comment '库位名称',
    model varchar(64) null comment '规格型号',
    inbound_quantity decimal(32,2) null comment '入库数量/重量',
    outbound_quantity decimal(32,2) null comment '出库数量/重量',
    inventory_quantity decimal(32,2) null comment '库存数量/重量',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '仓储监管-仓储明细';

create table t_supplier
(
    id bigint unsigned auto_increment
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    code varchar(5) null comment '编号',
    account varchar(20) null comment '账号',
    password varchar(50) null comment '密码',
    institution_name varchar(255) null comment '企业名称',
    unified_social_credit_code varchar(25) null comment '统一社会信用代码',
    legal_representative varchar(25) null comment '法定代表人',
    name varchar(64) null comment '联系人',
    mobile varchar(64) null comment '手机号码',
    email varchar(20) null comment '邮箱',
    system_name varchar(20) null comment '系统名称',
    system_logo bigint null comment '系统logo',
    real_name varchar(25) null comment '真实姓名',
    real_name_pinyin varchar(128) null comment '真实姓名拼音',
    id_no varchar(18) null comment '身份证号',
    app_token varchar(64) null comment '企业认证令牌',
    app_secret varchar(64) null comment '企业认证秘钥',
    seal_admin tinyint(1) null comment '是否是印章管理员',
    apply_state tinyint(1) default 0 null comment '机构认证状态',
    has_expired tinyint null comment '企业认证是否已过期',
    roles json null comment '企业身份',
    del tinyint(1) default 0 not null comment '删除标志 0存在 1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by bigint unsigned null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
)
    comment '供应商' row_format = DYNAMIC;

create table t_trajectory
(
    id bigint unsigned auto_increment comment '主键,编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    business_id varchar(64) not null comment '业务ID',
    type tinyint(1) not null comment '类型 1船运 2汽运',
    trajectory_info json null comment '轨迹信息',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '历史轨迹';

create table t_transfer
(
    id varchar(20) not null comment 'id'
        primary key,
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) not null comment '关联合同id',
    contract_name varchar(64) null comment '关联合同名称',
    goods_name varchar(255) null comment '货物名称',
    goods_info json null comment '货物json数据',
    buyer_id bigint null comment '买方id',
    buyer_name varchar(64) null comment '买方名称',
    seller_id bigint null comment '卖方id',
    seller_name varchar(64) null comment '卖方名称',
    warehouse_id varchar(64) not null comment '所属仓库id',
    warehouse_name varchar(64) null comment '所属仓库名称',
    storage_id bigint null comment '库位id',
    storage_name varchar(64) null comment '库位名称',
    transfer_total decimal(16,2) not null comment '过户总数量/重量',
    new_weight decimal(16,2) null comment '净重',
    rough_weight decimal(16,2) null comment '毛重',
    state tinyint(1) default 0 not null comment '状态',
    submit_time datetime null comment '卖方提交时间',
    warehouse_audits_time datetime null comment '仓库审核时间',
    seller_verify_time datetime null comment '卖家核实时间',
    buyer_verify_time datetime null comment '买方核实时间',
    warehouse_sign_time datetime null comment '仓库签署时间',
    reject_reason varchar(255) null comment '驳回原因',
    reject_user_id bigint null comment '驳回人id',
    seller_operator_name varchar(64) null comment '卖方操作员（卖方审核人员）',
    buyer_operator_name varchar(64) null comment '买方操作员（买方审核人员）',
    warehouse_operator_name varchar(64) null comment '仓储方审核员',
    change_standard decimal(16,2) null comment '收费标准',
    transfer_fee decimal(16,2) null comment '过户费',
    sign_type tinyint null comment '签署类型0 线上，1线下',
    sign_file_id bigint null comment '签署文件id（线上和线下）',
    sign_user_id bigint null comment '签署人id',
    file_id bigint null comment '附件id',
    transfer_date datetime not null comment '过户日期',
    remark varchar(200) null comment '备注',
    del tinyint(1) default 0 not null comment '删除标志0 存在，1删除',
    tenant_id bigint null comment '企业定制id',
    origin tinyint(1) null comment '客户来源',
    created_by bigint null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '过户记录表';

create index t_transfer__index2
    on t_transfer (contract_id, project_id);

create table t_transport_order_details_ship
(
    id varchar(64) not null comment '主键,跟船运单同一个id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    delivery_info json null comment '发货信息',
    jd_info json null comment '接档信息',
    lazb_info json null comment '离岸准备信息',
    drainage tinyint null comment '是否排水 0否 1是',
    drainage_time datetime null comment '排水确认时间',
    drainage_video_id bigint null comment '排水视频ID',
    owner_set_sail_time datetime null comment '货主确认发航时间',
    set_sail_info json null comment '发航信息',
    captain_arrival_time datetime null comment '船主确认到港时间',
    arrival_video_id bigint null comment '到港视频ID',
    owner_unloading_time datetime null comment '货主确认卸货时间',
    unloading_info json null comment '卸货信息',
    freight_settlement_tonnage decimal(18,2) null comment '运费结算吨位',
    del tinyint not null,
    origin tinyint(1) null comment '来源',
    created_by bigint null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by bigint null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
)
    comment '船运单明细';

create table t_transport_order_railway
(
    id varchar(64) not null comment '铁路单编号'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '关联合同',
    deliver_goods_id varchar(64) null comment '关联发货',
    order_id varchar(64) null comment '关联订单',
    car_type_number varchar(32) null comment '车种车号',
    transport_weight decimal(32,2) null comment '运输重量',
    departure_address varchar(32) null comment '发站',
    unload_address varchar(32) null comment '到站',
    departure_time datetime null comment '发车时间',
    unload_time datetime null comment '到达时间',
    state tinyint(1) null comment '状态 1:已卸货 2:已取消',
    goods_name varchar(32) null comment '货物名称',
    model varchar(32) null comment '规格/型号',
    attachments json null comment '附件',
    data_sources tinyint(1) default 1 null comment '数据来源',
    receipt_quantity decimal(16,2) null comment '签收数量/重量',
    receipt_date datetime null comment '签收日期',
    receipt_remark varchar(255) null comment '签收备注',
    reconciliation_unit_price decimal(16,2) null comment '对账单价',
    reconciliation_subtotal decimal(16,2) null comment '对账小计',
    reconciliation_remark varchar(255) null comment '对账备注',
    pre_reconciliation_unit_price decimal(16,2) null comment '预对账单价',
    pre_reconciliation_subtotal decimal(16,2) null comment '预对账小计',
    pre_reconciliation_remark varchar(255) null comment '预对账备注',
    owner_id bigint null comment '货主id',
    owner_name varchar(32) null comment '货主名称',
    del tinyint(1) default 0 null comment '逻辑删除标志，0表示未删除，1表示已删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    create_type tinyint(1) default 0 null comment '创建类型 0 供应链创建 1 客户端创建',
    project_long_id bigint null comment '项目长ID'
)
    comment '铁路单';

create table t_transport_order_ship
(
    id varchar(64) not null comment '主键'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    shipping_order_id varchar(64) null comment '链云运单ID',
    goods_type varchar(64) not null comment '货品类型',
    model varchar(64) not null comment '规格、型号',
    source tinyint not null comment '数据来源 1自建录入 2志豪链云',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    goods_id varchar(64) not null comment '发货单id',
    order_id varchar(64) not null comment '订单id',
    owner_id bigint null comment '货主id',
    owner_enterprise json null comment '货主公司信息',
    owner_name varchar(64) null comment '货主信息联系人',
    owner_mobile varchar(20) null comment '货主信息手机号',
    captain_id bigint null comment '船主账号id',
    captain_enterprise json null comment '船主公司信息',
    captain_name varchar(64) null comment '船主信息联系人',
    captain_mobile varchar(20) null comment '船主信息手机号',
    captain_bank_id bigint null comment '船主收款银行账户id',
    captain_bank_info json null comment '船主收款银行账户信息',
    ship_id varchar(64) null comment '船舶ID',
    ship_type tinyint(1) null comment '船舶类型',
    ship_name varchar(64) null comment '船舶名称',
    source_port_id bigint not null comment '始发地码头id',
    source_port_name varchar(64) not null comment '始发地码头名称',
    destination_port_id bigint not null comment '目的地码头id',
    destination_port_name varchar(64) not null comment '目的地码头名称',
    unit_price decimal(6,2) null comment '意向单价',
    ton int not null comment '吨数',
    actual_freight_cost decimal(16,2) null comment '实际运费',
    load_date date null comment '装载日期',
    load_days tinyint null comment '装载日期宽限的天数',
    load_unload_days tinyint null comment '装卸天数',
    demurrage_fee decimal(6,2) null comment '滞期费',
    maritime_affairs_fee tinyint(1) null comment '海事费用',
    deposit decimal(18,2) null comment '定金',
    advance_deposit json null comment '垫付定金',
    pay_type tinyint(1) null comment '支付类型',
    state tinyint(1) null comment '状态',
    owner_ship_info_service_fee decimal(18,2) null comment '货主船务信息服务费',
    owner_ship_info_service_fee_state tinyint(1) null comment '货主船务信息服务费支付状态',
    captain_ship_info_service_fee decimal(18,2) null comment '船主船务信息服务费',
    captain_ship_info_service_fee_state tinyint(1) null comment '船主船务信息服务费支付状态',
    owner_pay_confirm_state tinyint(1) null comment '货主付款确认状态',
    captain_pay_confirm_state tinyint(1) null comment '船主收款确认状态',
    owner_pay_file_id bigint unsigned null comment '货主支付凭证',
    owner_pay_freight_file_id bigint unsigned null comment '货主支付运费凭证',
    advance_pay_file_id json null comment '垫付支付凭证',
    owner_ship_info_service_fee_pay_type tinyint(1) null comment '货主服务费支付类型',
    captain_ship_info_service_fee_pay_type tinyint(1) null comment '船主服务费支付类型',
    is_finish_unload tinyint(1) default 0 not null comment '是否完成清仓',
    third_state tinyint null comment '链云运单状态',
    before_cancel_state tinyint null comment '取消之前的运单状态',
    before_cancel_third_state tinyint null comment '取消之前的第三方运单状态',
    is_start_unloading tinyint default 0 not null comment '是否开始卸货',
    start_loading_time datetime null comment '链云开始装货时间',
    remark varchar(240) null comment '补充说明',
    receipt_quantity decimal(16,2) null comment '签收数量/重量',
    receipt_date datetime null comment '签收日期',
    receipt_remark varchar(255) null comment '签收备注',
    reconciliation_unit_price decimal(16,2) null comment '对账单价',
    reconciliation_subtotal decimal(16,2) null comment '对账小计',
    reconciliation_remark varchar(255) null comment '对账备注',
    pre_reconciliation_unit_price decimal(16,2) null comment '预对账单价',
    pre_reconciliation_subtotal decimal(16,2) null comment '预对账小计',
    pre_reconciliation_remark varchar(255) null comment '预对账备注',
    loading_time datetime null comment '装货时间',
    unload_time datetime null comment '卸货时间',
    attachments json null comment '附件',
    is_agree_unloading tinyint null comment '是否已经同意卸货',
    is_departure tinyint null comment '是否已经确认发航',
    del tinyint not null,
    origin tinyint(1) null comment '来源',
    created_by bigint null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by bigint null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    freight_settle_ton_confirm_node tinyint null,
    owner_confirm_freight_settle_ton_state tinyint null,
    owner_confirm_freight_settle_ton_time datetime null,
    captain_confirm_freight_settle_ton_state tinyint null,
    captain_confirm_freight_settle_ton_time datetime null,
    create_type tinyint(1) default 0 null comment '创建类型 0 供应链创建 1 客户端创建',
    project_long_id bigint null comment '项目长ID'
)
    comment '船运单';

create table t_transport_order_vehicle
(
    id varchar(64) not null comment '主键id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    address json null comment '地址',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(64) null comment '合同id',
    deliver_goods_id varchar(64) null comment '关联发货',
    order_id varchar(64) null comment '关联订单',
    driver_name varchar(64) null comment '司机名称',
    driver_mobile varchar(64) null comment '司机手机号',
    driver_license_plate varchar(64) null comment '司机车牌号',
    color tinyint(1) null comment '车牌类型',
    vehicle_type tinyint null comment '车型',
    vehicle_length tinyint null comment '车长',
    departure_time datetime null comment '发车时间',
    estimated_weight decimal(32,2) null comment '预估重量',
    transport_weight decimal(32,2) null comment '运输重量',
    freight_type tinyint null comment '运费类型',
    mileage decimal(16,2) null comment '里程',
    freight_unit_price decimal(10,2) null comment '运费单价',
    freight_unit_price_type tinyint(1) null comment '运费单价类型',
    departure_pic_file_id bigint null comment '发车照片文件id',
    other_departure_pic_file_ids json null comment '其他发车照片',
    unloaded_time datetime null comment '卸货时间',
    cancel_time datetime null comment '取消时间',
    unloaded_weight decimal(32,2) null comment '已卸货重量',
    actual_costs decimal(10,2) null comment '实际运费',
    settlement_documents_file_id bigint null comment '结算单据照片文件id',
    unloaded_pic_file_ids json null comment '卸货照片文件id',
    exception_infos json null comment '异常信息及照片',
    locus_file_id bigint null comment '轨迹文件id',
    departure_address json null comment '发车地址',
    unload_address json null comment '卸货地址',
    state tinyint(1) null comment '状态',
    expected_unloaded_time datetime null comment '期望卸货时间',
    expected_loading_time datetime null comment '期望装货时间',
    goods_name varchar(256) null comment '货物名称',
    goods_type varchar(128) null comment '规格/型号',
    unit varchar(64) null comment '单位',
    transport_volume decimal(32,2) null comment '运输体积',
    transport_items int null comment '运输件数',
    contacts varchar(64) null comment '联系人',
    mobile varchar(20) null comment '手机号',
    remark varchar(200) null comment '备注',
    owner_id bigint null comment '货主id',
    owner_name varchar(20) null comment '货主名称',
    estimate_freight_revenue decimal(32,2) null comment '预估运费支出',
    delivery_quantity decimal(16,2) null comment '发货数量/重量',
    receipt_quantity decimal(16,2) null comment '签收数量/重量',
    receipt_date datetime null comment '签收日期',
    receipt_remark varchar(255) null comment '签收备注',
    reconciliation_unit_price decimal(16,2) null comment '对账单价',
    reconciliation_subtotal decimal(16,2) null comment '对账小计',
    reconciliation_remark varchar(255) null comment '对账备注',
    pre_reconciliation_unit_price decimal(16,2) null comment '预对账单价',
    pre_reconciliation_subtotal decimal(16,2) null comment '预对账小计',
    pre_reconciliation_remark varchar(255) null comment '预对账备注',
    before_cancel_state tinyint null comment '取消之前的运单状态',
    departure_call_time datetime null comment '发车调用时间',
    unload_call_time datetime null comment '卸货调用时间',
    source tinyint(1) default 1 null comment '数据来源 1自建录入 2线下单据',
    attachments json null comment '附件',
    del tinyint(1) default 0 null comment '逻辑删除标志，0表示未删除，1表示已删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    create_type tinyint(1) default 0 null comment '创建类型 0 供应链创建 1 客户端创建',
    project_long_id bigint null comment '项目长ID'
)
    comment '汽运单';

create table t_user
(
    id bigint unsigned auto_increment
        primary key,
    employee_id varchar(10) null comment '工号',
    tenant_id bigint unsigned null comment '企业定制id',
    enterprise_custom_id bigint unsigned null comment '定制平台id',
    wxw_user_id varchar(64) null comment '企业微信userId',
    name varchar(64) not null comment '用户名称',
    password varchar(64) null comment '登录密码',
    mobile varchar(64) null comment '手机号',
    mail varchar(64) null comment '邮箱账号',
    role_ids json null comment '授权角色列表',
    is_self_set_password tinyint default 0 not null comment '是否自己设置密码',
    state tinyint(1) default 1 not null comment '状态 1 启用  0 禁用',
    is_main tinyint default 0 null comment '是否是创建供应链下的主账号',
    del tinyint(1) default 0 not null comment '删除标志 0存在 1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null,
    created_time datetime default CURRENT_TIMESTAMP null,
    updated_by bigint unsigned null,
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
);

create table t_user_enterprise_certification
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    user_id bigint null comment '用户id',
    tenant_id bigint unsigned null comment '企业定制id',
    company_id bigint null comment '契约锁返回的企业id',
    name varchar(255) null comment '企业名称',
    unified_social_credit_code varchar(25) null comment '统一社会信用代码',
    legal_representative varchar(25) null comment '法定代表人',
    message varchar(255) null comment '认证返回信息',
    status tinyint null comment '认证状态',
    auth_end_time varchar(20) null comment '授权有效期',
    state tinyint null comment '企业认证记录状态 0：换绑手机号 1：有效 2：无效',
    del tinyint default 0 not null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint null comment '创建人',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint null comment '更新人',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户企业认证记录表' row_format = DYNAMIC;

create table t_user_personal_certification
(
    id bigint unsigned auto_increment comment '主键'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    user_id bigint unsigned not null comment '用户id',
    name varchar(55) not null comment '真实姓名',
    id_no varchar(18) not null comment '身份证号',
    status tinyint null comment '认证状态',
    message varchar(255) null comment '认证返回信息',
    state tinyint null comment '个人认证记录状态 0：换绑手机号 1：有效 2：无效',
    del tinyint default 0 not null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint null comment '创建人',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint null comment '更新人',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户个人认证记录表' row_format = DYNAMIC;

create table t_warehouse
(
    id varchar(10) not null comment '仓库id'
        primary key,
    tenant_id bigint unsigned null comment '企业定制id',
    name varchar(64) null comment '仓库名称',
    subject varchar(64) null comment '所属主体',
    type tinyint(1) null comment '仓库类型',
    area_code json null comment '省市区编码',
    address varchar(64) null comment '仓库地址',
    supervisor varchar(64) null comment '负责人',
    mobile varchar(64) null comment '联系电话',
    storage_quantity bigint null comment '库位数量',
    state tinyint(1) default 0 null comment '是否删除;0:启用,1禁用',
    is_used tinyint(1) default 0 null comment '是否被使用 0:未使用 1:已使用',
    is_bind_facility tinyint(1) default 0 null comment '是否绑定设备 0：未绑定  1：已绑定',
    del tinyint(1) default 0 null comment '是否删除;0:存在,1删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建人编号',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新人编号',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '仓库表';

create table t_warehouse_goods_info
(
    id bigint auto_increment comment 'id'
        primary key,
    tenant_id bigint null comment '企业定制id',
    attribution_type tinyint null comment '归属类型：1 进销存 2 仓储',
    relate_id varchar(20) null comment '关联出库/入库单id',
    project_id varchar(64) null comment '项目id',
    contract_id varchar(20) null comment '合同id',
    warehouse_id varchar(20) null comment '仓库id',
    warehouse_name varchar(50) null comment '仓库名称',
    storage_id bigint null comment '库位id',
    storage_name varchar(50) null comment '库位名称',
    type tinyint(1) null comment '单据类型',
    goods_id bigint null comment '货物名称id',
    model varchar(50) null comment '规格型号',
    unit_price decimal(32,2) null comment '单价',
    unit varchar(20) null comment '单位',
    inbound_weight decimal(32,2) null comment '入库数量',
    inbound_amount decimal(32,2) null comment '入库金额',
    remark varchar(200) null comment '备注',
    inbound_time datetime null comment '出入库时间',
    customer_id bigint null comment '用户id（过户单使用）',
    state tinyint null comment '状态（过户单使用  0：未完成 1：已完成 ）',
    del tinyint(1) default 0 null comment '是否删除',
    origin tinyint(1) null comment '来源',
    created_by bigint unsigned null comment '创建者id',
    created_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by bigint unsigned null comment '更新者id',
    updated_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    project_long_id bigint null comment '项目长ID'
)
    comment '入库/出库单关联货物信息表';


alter table t_supplier
    auto_increment = 100000000000;

-- 出库新增字段
alter table t_outbound
    add purchaser_business_id bigint null comment '采购方往来企业id' after outbound_type;

alter table t_outbound
    add seller_business_id bigint null comment '销售方往来企业id' after purchaser_enterprise;    

-- 授权
grant all on zhihaoscm_supplier_saas.* to supplier@'%' ;