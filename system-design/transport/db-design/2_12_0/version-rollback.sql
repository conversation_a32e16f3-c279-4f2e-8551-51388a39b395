-- ----------------------------------------------------------------------------------------------------------
-- -----------user-center 2.12.0 sql
-- ----------------------------------------------------------------------------------------------------------

UPDATE `user_center`.`t_function_module`
SET `name` = '货主对账',
    `full_path` = '财务管理-上游对账开票-货主对账'
    WHERE
        `id` = 45;

UPDATE `user_center`.`t_function_module`
SET `name` = '货主开票',
    `full_path` = '财务管理-上游对账开票-货主开票'
    WHERE
        `id` = 46;

UPDATE `user_center`.`t_function_module`
SET `name` = '承运商对账',
    `full_path` = '财务管理-下游对账开票-承运商对账'
    WHERE
        `id` = 48;

UPDATE `user_center`.`t_function_module`
SET `name` = '承运商开票',
    `full_path` = '财务管理-下游对账开票-承运商开票'
    WHERE
        `id` = 49;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_UPCO_V')), '"', '')
        ),
        '$',
        'ROLE_OWNRECO_V'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_UPCO_V') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_UPCO_M')), '"', '')
        ),
        '$',
        'ROLE_OWNRECO_M'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_UPCO_M') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_UPLNV_V')), '"', '')
        ),
        '$',
        'ROLE_OWNBILL_V'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_UPLNV_V') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_UPLNV_M')), '"', '')
        ),
        '$',
        'ROLE_OWNBILL_M'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_UPLNV_M') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_DOWNCO_V')), '"', '')
        ),
        '$',
        'ROLE_CARRRECO_V'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_DOWNCO_V') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_DOWNCO_M')), '"', '')
        ),
        '$',
        'ROLE_CARRRECO_M'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_DOWNCO_M') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_DOWNLNV_V')), '"', '')
        ),
        '$',
        'ROLE_CARRBILL_V'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_DOWNLNV_V') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_DOWNLNV_M')), '"', '')
        ),
        '$',
        'ROLE_CARRBILL_M'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_DOWNLNV_M') IS NOT NULL
      AND del = 0;

-- ----------------------------------------------------------------------------------------------------------
-- -----------transport 2.12.0 sql
-- ---------------------------------------------------------------------------------------------------------
ALTER TABLE `zhihaoscm_transport`.`t_vehicle_order_qrcode`
    CHANGE COLUMN `expenses_price` `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '运费单价' AFTER `goods_name`,
    CHANGE COLUMN `expenses_price_unit` `price_unit` tinyint(1) NULL DEFAULT NULL COMMENT '价格单位' AFTER `price`,
    CHANGE COLUMN `expenses_price_rule_snapshot_id` `price_rule_snapshot_id` bigint NULL DEFAULT NULL COMMENT '报价id' AFTER `expected_unloading_time`,
    CHANGE COLUMN `expenses_price_rule_formula_id` `price_rule_formula_id` bigint NULL DEFAULT NULL COMMENT '报价公式id' AFTER `price_rule_snapshot_id`,
    DROP COLUMN `expenses_price_total`,
    DROP COLUMN `income_price`,
    DROP COLUMN `income_price_unit`,
    DROP COLUMN `income_price_rule_snapshot_id`,
    DROP COLUMN `income_price_rule_formula_id`,
    DROP COLUMN `contact`,
    DROP COLUMN `mobile`,
    DROP COLUMN `receive_contact`,
    DROP COLUMN `receive_mobile`,
    DROP COLUMN `receive_id_card`;


ALTER TABLE `zhihaoscm_transport`.`t_router`
    DROP COLUMN `transport_duration_hour`,
    DROP COLUMN `transport_duration_minute`;

ALTER TABLE `zhihaoscm_transport`.`t_transport_freight_calculate`
    DROP COLUMN `rel_type`,
    MODIFY COLUMN `transport_order_vehicle_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '运单id' AFTER `price_rule_formula_id`,
    DROP COLUMN `router_id`;



ALTER TABLE `zhihaoscm_transport`.`t_grab_requirement`
    MODIFY COLUMN `grab_price` decimal(10, 2) NOT NULL COMMENT '抢单单价' AFTER `order_id`;

ALTER TABLE `zhihaoscm_transport`.`t_grab_requirement`
    DROP COLUMN `telephone_consult_method`;

ALTER TABLE `zhihaoscm_transport`.`t_order`
    DROP COLUMN `price_method`;

ALTER TABLE `zhihaoscm_transport`.`t_grab_requirement`
    DROP COLUMN `grab_unit`;

ALTER TABLE `zhihaoscm_transport`.`t_invoice`
    DROP COLUMN `invoice_time`;

ALTER TABLE `zhihaoscm_transport`.`t_reconciliation_order_vehicle`
    DROP COLUMN `freight_unit_price_type`;