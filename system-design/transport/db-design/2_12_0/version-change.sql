-- ----------------------------------------------------------------------------------------------------------
-- -----------user-center 2.12.0 sql
-- ----------------------------------------------------------------------------------------------------------

UPDATE `user_center`.`t_function_module`
SET `name` = '对账管理',
    `full_path` = '财务管理-上游对账开票-对账管理'
    WHERE
        `id` = 45;

UPDATE `user_center`.`t_function_module`
SET `name` = '开票管理',
    `full_path` = '财务管理-上游对账开票-开票管理'
    WHERE
        `id` = 46;

UPDATE `user_center`.`t_function_module`
SET `name` = '对账管理',
    `full_path` = '财务管理-下游对账开票-对账管理'
    WHERE
        `id` = 48;

UPDATE `user_center`.`t_function_module`
SET `name` = '开票管理',
    `full_path` = '财务管理-下游对账开票-开票管理'
    WHERE
        `id` = 49;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_OWNRECO_V')), '"', '')
        ),
        '$',
        'ROLE_UPCO_V'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_OWNRECO_V') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_OWNRECO_M')), '"', '')
        ),
        '$',
        'ROLE_UPCO_M'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_OWNRECO_M') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_OWNBILL_V')), '"', '')
        ),
        '$',
        'ROLE_UPLNV_V'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_OWNBILL_V') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_OWNBILL_M')), '"', '')
        ),
        '$',
        'ROLE_UPLNV_M'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_OWNBILL_M') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_CARRRECO_V')), '"', '')
        ),
        '$',
        'ROLE_DOWNCO_V'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_CARRRECO_V') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_CARRRECO_M')), '"', '')
        ),
        '$',
        'ROLE_DOWNCO_M'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_CARRRECO_M') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_CARRBILL_V')), '"', '')
        ),
        '$',
        'ROLE_DOWNLNV_V'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_CARRBILL_V') IS NOT NULL
      AND del = 0;

UPDATE t_role
SET permissions = JSON_ARRAY_APPEND(
        JSON_REMOVE(permissions,
                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(permissions, 'one', 'ROLE_CARRBILL_M')), '"', '')
        ),
        '$',
        'ROLE_DOWNLNV_M'
                  )
    WHERE JSON_SEARCH(permissions, 'one', 'ROLE_CARRBILL_M') IS NOT NULL
      AND del = 0;

-- ----------------------------------------------------------------------------------------------------------
-- -----------transport 2.12.0 sql
-- ---------------------------------------------------------------------------------------------------------
ALTER TABLE `zhihaoscm_transport`.`t_vehicle_order_qrcode`
    CHANGE COLUMN `price` `expenses_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '支出运费单价' AFTER `goods_name`,
    CHANGE COLUMN `price_unit` `expenses_price_unit` tinyint(1) NULL DEFAULT NULL COMMENT '支出价格单位' AFTER `expenses_price`,
    CHANGE COLUMN `price_rule_snapshot_id` `expenses_price_rule_snapshot_id` bigint NULL DEFAULT NULL COMMENT '支出报价id' AFTER `expected_unloading_time`,
    CHANGE COLUMN `price_rule_formula_id` `expenses_price_rule_formula_id` bigint NULL DEFAULT NULL COMMENT '支出报价公式id' AFTER `expenses_price_rule_snapshot_id`,
    ADD COLUMN `expenses_price_total` decimal(12, 2) NULL COMMENT '支出金额' AFTER `expenses_price_rule_formula_id`,
    ADD COLUMN `income_price` decimal(10, 2) NULL COMMENT '收入运费单价' AFTER `has_check_in_fence`,
    ADD COLUMN `income_price_unit` tinyint(1) NULL COMMENT '收入价格单位' AFTER `income_price`,
    ADD COLUMN `income_price_rule_snapshot_id` bigint NULL COMMENT '收入报价id' AFTER `income_price_unit`,
    ADD COLUMN `income_price_rule_formula_id` bigint NULL COMMENT '收入报价公式id' AFTER `income_price_rule_snapshot_id`,
    ADD COLUMN `contact` varchar(32) NULL COMMENT '发货人' AFTER `income_price_rule_formula_id`,
    ADD COLUMN `mobile` varchar(11) NULL COMMENT '发货人手机号' AFTER `contact`,
    ADD COLUMN `receive_contact` varchar(32) NULL COMMENT '收货人' AFTER `mobile`,
    ADD COLUMN `receive_mobile` varchar(11) NULL COMMENT '收货人手机号' AFTER `receive_contact`,
    ADD COLUMN `receive_id_card` varchar(18) NULL COMMENT '收货人身份证号' AFTER `receive_mobile`;

ALTER TABLE `zhihaoscm_transport`.`t_router`
    ADD COLUMN `transport_duration_hour` int NULL COMMENT '运输时长小时' AFTER `mileage`,
    ADD COLUMN `transport_duration_minute` int NULL COMMENT '运输时长分钟' AFTER `transport_duration_hour`;

ALTER TABLE `zhihaoscm_transport`.`t_transport_freight_calculate`
    ADD COLUMN `rel_type` int NULL COMMENT '关联类型：1 运单，2 路线' AFTER `price_rule_formula_id`,
    MODIFY COLUMN `transport_order_vehicle_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '运单id' AFTER `price_rule_formula_id`,
    ADD COLUMN `router_id` bigint NULL COMMENT '路线id' AFTER `transport_order_vehicle_id`;

update t_transport_freight_calculate set rel_type = 1 WHERE transport_order_vehicle_id is not null;

ALTER TABLE `zhihaoscm_transport`.`t_vehicle_order_qrcode`
    ADD COLUMN `expenses_price_total` decimal(12, 2) NULL COMMENT '支出金额' AFTER `expenses_price_rule_formula_id`;


ALTER TABLE `zhihaoscm_transport`.`t_grab_requirement`
    MODIFY COLUMN `grab_price` decimal(10, 2) NULL COMMENT '抢单单价' AFTER `order_id`;

ALTER TABLE `zhihaoscm_transport`.`t_grab_requirement`
    ADD COLUMN `telephone_consult_method` tinyint(1) NULL COMMENT '电议计价方式（1，一口价；2，以货主报价/合同约定为准）' AFTER `grab_price`;

ALTER TABLE `zhihaoscm_transport`.`t_order`
    ADD COLUMN `price_method` tinyint(1) NULL COMMENT '计价方式(1，一口价；2，以货主报价/合同约定为准)' AFTER `has_self_operated`;

ALTER TABLE `zhihaoscm_transport`.`t_grab_requirement`
    ADD COLUMN `grab_unit` tinyint(1) NULL COMMENT '抢单单价单位' AFTER `telephone_consult_method`;

ALTER TABLE `zhihaoscm_transport`.`t_invoice`
    ADD COLUMN `invoice_time` datetime NULL COMMENT '开票时间' AFTER `reason`;

ALTER TABLE `zhihaoscm_transport`.`t_reconciliation_order_vehicle`
    ADD COLUMN `freight_unit_price_type` tinyint(1) NULL COMMENT '运费单价类型' AFTER `freight_unit_price`;
